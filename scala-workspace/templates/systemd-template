[Unit]
Description=${{descr}}
Requires=${{start_facilities}}

[Service]
Type=simple
WorkingDirectory=${{chdir}}
EnvironmentFile=/etc/environment
EnvironmentFile=${{env_config}}
ExecStart=${{chdir}}/bin/${{exec}}
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=${{retryTimeout}}
SuccessExitStatus=${{SuccessExitStatus}}
TimeoutStopSec=${{TimeoutStopSec}}
User=${{daemon_user}}
ExecStartPre=/bin/mkdir -p /run/${{app_name}}
ExecStartPre=/bin/chown ${{daemon_user}}:${{daemon_group}} /run/${{app_name}}
ExecStartPre=/bin/chmod 755 /run/${{app_name}}
PermissionsStartOnly=true
LimitNOFILE=${{file_descriptor_limit}}

[Install]
WantedBy=multi-user.target
