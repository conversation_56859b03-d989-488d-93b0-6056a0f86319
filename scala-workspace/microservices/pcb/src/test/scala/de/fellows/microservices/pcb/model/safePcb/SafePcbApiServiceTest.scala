package de.fellows.microservices.pcb.model.safePcb

import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.model.lq.{Credentials, ExistingOffer}
import de.fellows.microservices.pcb.model.panel.{NumberOfPanels, PanelSample}
import de.fellows.luminovo.panel.Depanelization
import de.fellows.microservices.pcb.model.pcb.ManufacturerApi
import de.fellows.microservices.pcb.model.pcb.props.{
  BlindVias,
  BoardHeight,
  BoardWidth,
  BuriedVias,
  EdgeMetalization,
  ImpedanceTested,
  PressFit
}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import sttp.client3.testing.SttpBackendStub

import java.util.UUID

class SafePcbApiServiceTest extends AnyFlatSpec with should.Matchers {

  private val stubAsyncBackend = SttpBackendStub.asynchronousFuture
  private val apiService       = new SafePcbApiService(stubAsyncBackend)(<PERSON><PERSON>)

  val safePcbRequest = SafePcbRequest(
    name = Some("test-name"),
    width = BoardWidth(25),
    height = BoardHeight(10),
    ipcAClass = SafePcbIPCAClass2,
    numberOfLayers = FourLayers,
    material = SafePcbFR4,
    finalThickness = Mm030,
    outerCopperThickness = SafePcbOuterThicknessMcr35,
    innerCopperThickness = SafePcbInnerThicknessMcr35,
    surfaceFinish = SafePcbChemicalTin,
    solderMaskSide = SafePcbSolderMaskSidesTopBottom,
    solderMaskColor = SafePcbSolderMaskColorGreen,
    silkscreenSides = SafePcbSilkscreenSidesTopBottom,
    silkscreenColor = SafePcbSilkscreenColorBlack,
    peelableMask = SafePcbPeelableMaskTopBottom,
    impedanceTested = ImpedanceTested(false),
    edgeMetalization = EdgeMetalization(false),
    pressFit = PressFit(false),
    blindVias = BlindVias(false),
    buriedVias = BuriedVias(false),
    calculatedPanelInfo = PanelSample.calculatedPanelDetailsInfo,
    depanelization = SafePcbDepanelization(Depanelization.VCut)
  )

  private val api = ManufacturerApi.SafePcb

  "fullRequest" should "include PCB name" in {
    val xml = apiService.fullRequest(
      pcb = safePcbRequest,
      numberOfPanels = Seq(NumberOfPanels(1)),
      existingOffer = None,
      c = Credentials(api, "user", "pass", region = None)
    )
    (xml \ "REFERENCE" \ "PCB_Name").text should be("test-name")
  }

  it should "add multiple quantities to the same request" in {
    val xml = apiService.fullRequest(
      pcb = safePcbRequest,
      numberOfPanels = Seq(1, 2, 3).map(NumberOfPanels),
      existingOffer = None,
      c = Credentials(api, "user", "pass", region = None)
    )
    (xml \ "QUANTITIES" \ "Panel").text should be("1;2;3")
  }

  it should "not add offer number if not present" in {
    val xml = apiService.fullRequest(
      pcb = safePcbRequest,
      numberOfPanels = Seq(1, 2, 3).map(NumberOfPanels),
      existingOffer = None,
      c = Credentials(api, "user", "pass", region = None)
    )
    (xml \ "QUOTE" \ "Number").text should be("")
  }

  it should "add offer number if present" in {
    val xml = apiService.fullRequest(
      pcb = safePcbRequest,
      numberOfPanels = Seq(1, 2, 3).map(NumberOfPanels),
      existingOffer = Some(
        ExistingOffer(
          offerId = UUID.randomUUID(),
          supplierAndStockLocation = UUID.randomUUID(),
          offerNumber = "offer-12345",
          manufacturer = api
        )
      ),
      c = Credentials(api, "user", "pass", region = None)
    )
    (xml \ "QUOTE" \ "Number").text should be("offer-12345")
  }

}
