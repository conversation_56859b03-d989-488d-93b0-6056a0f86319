package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model._
import de.fellows.microservices.pcb.model.pcb.props._

private[pcb] object helper {

  def rigidPCBConfiguration: wurth.WurthPcbRequest =
    wurth.WurthPcbRequest(
      pcbName = "test",
      orderNumber = Some("order 1"),
      quantity = 10,
      quantityMax = None,
      layerstackType = wurth.WurthLayerstackType.Rigid,
      numberOfLayers = wurth.FourLayers,
      material = wurth.Tg150,
      width = 150f,
      height = 200f,
      thickness = wurth.Mm100(),
      laserDrill = wurth.TopDrill,
      buriedVia = BuriedVias.no,
      blindVia = BlindVias.no,
      outerLayerStructure = wurth.OuterMcr125(),
      outerCopperThickness = wurth.OuterThicknessMcr70(),
      innerLayerStructure = wurth.WurthInnerMcr250(),
      innerCopperThickness = wurth.InnerThicknessMcr105(),
      smallestVia = wurth.Mm10,
      smallestRoutTool = wurth.Between100mmAnd050mm,
      shape = wurth.OtherShape,
      viaFilling = wurth.ViaFillingNo,
      surfaceFinish = wurth.ChemicalTin,
      solderMaskSides = wurth.SolderMaskSidesBottom,
      solderMaskColor = wurth.SolderMaskColorGreen,
      silkscreenColor = wurth.WurthSilkscreenColor.White,
      silkscreenSides = wurth.SilkscreenSidesTopBottom,
      fullyCoatedSilkscreen = wurth.FullyCoatedSilkscreenBottom,
      eTest = WurthETestYes,
      chamfer = wurth.Chamfer20PCI,
      edgeMetalization = WurthEdgeMetalizationYes,
      hardGold = WurthHardGoldNo,
      numberOfPCBs = 1,
      depanelization = WurthDepanelization.Milling,
      orderingFiles = WurthOrderingFiles.WurthNoOrdering,
      additionalDocumentation = false,
      ulMarkingType = NoUlMarking,
      deliveryTime = None
    )

}
