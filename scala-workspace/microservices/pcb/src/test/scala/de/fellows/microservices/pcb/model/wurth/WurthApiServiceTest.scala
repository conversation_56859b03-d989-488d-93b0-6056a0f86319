package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb
import de.fellows.microservices.pcb.PcbServer.SyncBackend
import de.fellows.microservices.pcb.model.lq.Credentials
import de.fellows.microservices.pcb.{ExceptionError, ServiceError, UnauthorizedError}
import de.fellows.microservices.pcb.model.lq.PriceType.ListPrice
import de.fellows.microservices.pcb.model.panel.RequestedPcbs
import de.fellows.microservices.pcb.model.wurth.WurthApiService.WurthLocation
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import play.api.libs.json.{JsError, JsPath, JsonValidationError}
import sttp.client3.playJson.SttpPlayJsonApi
import sttp.client3.{DeserializationException, HttpURLConnectionBackend, Response}
import sttp.model.StatusCode

class WurthApiServiceTest extends AnyFlatSpec with should.Matchers with SttpPlayJsonApi {
  class TestableApiService(
      username: String,
      password: String,
      backend: SyncBackend
  ) extends WurthApiService(
        "https://stackrate.de",
        backend
      ) {
    def authorize: Either[ServiceError, String] =
      super.authorize(Credentials(api, username, password, region = None))
  }

  "Authentication" should "fail if username or password is wrong" in {
    val backend = HttpURLConnectionBackend.stub
      .whenRequestMatches(_.uri.toString().endsWith("/gateways/api/auth"))
      .thenRespond(Response("No client found", StatusCode.Unauthorized))

    val apiService = new TestableApiService("test", "pwd", backend)
    apiService.authorize shouldBe Left(
      UnauthorizedError("Unauthorized error in Wuerth. URI: GET http://example.com: No client found")
    )
  }
  it should "fail if access_token not found" in {
    val backend = HttpURLConnectionBackend.stub
      .whenRequestMatches(_.uri.toString().endsWith("/gateways/api/auth"))
      .thenRespond(Response.ok("{\"no_access_token\": \"\"}"))

    val apiService = new TestableApiService("test", "pwd", backend)
    apiService.authorize shouldBe Left(ExceptionError(
      "Could not deserialize response \"{\"no_access_token\": \"\"}\" from Wuerth",
      DeserializationException(
        "{\"no_access_token\": \"\"}",
        JsError(List(
          (
            JsPath \ "access_token",
            List(JsonValidationError(
              List(
                "error.path.missing"
              )
            ))
          )
        ))
      )
    ))
  }
  it should "return access_token=test" in {
    val backend = HttpURLConnectionBackend.stub
      .whenRequestMatches(_.uri.toString().endsWith("/gateways/api/auth"))
      .thenRespond(Response.ok("{\"access_token\": \"test\"}"))
    val apiService = new TestableApiService("test", "pwd", backend)
    apiService.authorize shouldBe Right("test")
  }

  val configuration    = helper.rigidPCBConfiguration
  val mockManufacturer = pcb.helper.mockManufacturer()
  val location         = mockManufacturer.locations.head
  val wurthLocation    = WurthLocation.Germany

  "Offer" should "fail if token is not valid" in {
    val backend = HttpURLConnectionBackend.stub
      .whenRequestMatches(_.uri.toString().contains("/gateways/api/configuration"))
      .thenRespond(Response("Token expired", StatusCode.Unauthorized))

    val apiService = new TestableApiService("test", "pwd", backend)
    apiService.addConfiguration(
      configuration,
      pcb.helper.mockManufacturer(),
      location,
      wurthLocation,
      "test",
      ListPrice,
      RequestedPcbs(10)
    ) shouldBe Left(
      UnauthorizedError("Unauthorized error in Wuerth. URI: GET http://example.com: Token expired")
    )
  }

  it should "fail if the response misses some properties" in {
    val rawResponse =
      scala.io.Source.fromInputStream(getClass.getResourceAsStream("/badOfferResponse.json")).getLines().mkString("\n")
    val backend = HttpURLConnectionBackend.stub
      .whenRequestMatches(_.uri.toString().contains("/gateways/api/configuration"))
      .thenRespond(Response.ok(rawResponse))
    val apiService = new TestableApiService("test", "pwd", backend)
    apiService.addConfiguration(
      configuration,
      pcb.helper.mockManufacturer(),
      location,
      wurthLocation,
      "test",
      ListPrice,
      RequestedPcbs(10)
    ) match {
      case Left(error) => error.isInstanceOf[ServiceError] shouldBe true
      case Right(_)    => fail("Response should fail")
    }
  }

  it should "succeed and produce a set of offers" in {
    val rawResponse =
      scala.io.Source.fromInputStream(getClass.getResourceAsStream("/offerResponse.json")).getLines().mkString("\n")
    val backend = HttpURLConnectionBackend.stub
      .whenRequestMatches(_.uri.toString().contains("/gateways/api/configuration"))
      .thenRespond(Response.ok(rawResponse))
    val apiService = new TestableApiService("test", "pwd", backend)
    apiService.addConfiguration(
      configuration,
      pcb.helper.mockManufacturer(),
      location,
      wurthLocation,
      "test",
      ListPrice,
      RequestedPcbs(10)
    ) match {
      case Left(error) => fail(s"Should not fail: $error")
      case Right(offerResponse) =>
        val firstOffer = offerResponse.offers.head
        firstOffer.price should be(375.0f)
        firstOffer.productionDays should be(3)
    }
  }

  it should "fail extracting offer number" in {
    val errorResponse = """<p style="margin-left:10px">
                          |			File: /var/www/beta.wedirekt.com/htdocs/public/index.php<br />
                          |			Line: 462<br />
                          |			Function: require_once			</p>
                          |
                          |
                          |
                          |
                          |</div>
                          |""".stripMargin
    WurthApiService.extractOfferNumber(errorResponse) shouldBe None
  }
  it should "succeed extracting offer number" in {
    val errorResponse = """<p style="margin-left:10px">
                          |			File: /var/www/beta.wedirekt.com/htdocs/public/index.php<br />
                          |			Line: 462<br />
                          |			Function: require_once			</p>
                          |
                          |
                          |
                          |
                          |</div>{"pcb_offer_number":"WE24451020-78 PCB","delivery_date_estimated":"2022-05-12","link":"https:\/\/fr.beta.wedirekt.com\/fr\/offer\/download\/WE24451020-78+PCB"}
                          |""".stripMargin
    WurthApiService.extractOfferNumber(errorResponse) shouldBe Some("WE24451020-78 PCB")
  }
}
