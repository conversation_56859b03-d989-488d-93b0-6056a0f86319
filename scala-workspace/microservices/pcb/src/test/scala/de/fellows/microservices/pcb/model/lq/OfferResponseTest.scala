package de.fellows.microservices.pcb.model.lq

import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.Offer
import de.fellows.microservices.pcb.model.lq.PriceType.ListPrice
import de.fellows.microservices.pcb.model.pcb.ManufacturerApi
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID

class OfferResponseTest extends AnyFlatSpec with should.Matchers {

  val offers = Seq(
    Offer(70f, 3, None, None),
    Offer(60f, 4, None, None),
    Offer(50f, 5, None, None),
    Offer(40f, 6, None, None),
    Offer(30f, 7, None, None),
    Offer(20f, 8, None, None),
    Offer(10f, 10, None, None)
  )
  val offerResponse = OfferResponse(
    api = ManufacturerApiWithInformation(ManufacturerApi.Wuerth),
    manufacturer = helper.mockManufacturer(),
    location = helper.mockManufacturerLocation(),
    quantity = 5,
    offers = offers,
    priceType = ListPrice
  )

  "Fastest offer" should "be the offer with minimum delivery time" in {
    offerResponse.fastest.price should be(70f)
    offerResponse.fastest.productionDays should be(3)
  }
  "Best price offer" should "be the offer with the lowest price" in {
    offerResponse.bestPrice.price should be(10f)
    offerResponse.bestPrice.productionDays should be(10)
  }
  it should "be with the earliest delivery date if there are several identical prices" in {
    val identicalOffers = offers :+ Offer(10f, 9, None, None)
    val offer           = offerResponse.copy(offers = identicalOffers).bestPrice
    offer.price should be(10f)
    offer.productionDays should be(9)
  }

  val format = DateTimeFormatter.ISO_DATE
  val today  = LocalDate.now()
  "Best price by date" should "be the earliest offer if the date is before the earliest offer" in {
    val offer = offerResponse.bestPriceByDate(today.format(format))
    offer.price should be(70f)
    offer.productionDays should be(3)
    offer.productionDate should be(today.plusDays(3).format(format))
  }
  it should "be the latest offer if the date is after the latest offer" in {
    val offer = offerResponse.bestPriceByDate(today.plusMonths(1).format(format))
    offer.price should be(10f)
    offer.productionDays should be(10)
    offer.productionDate should be(today.plusDays(10).format(format))
  }
  it should "be the offer for 2 working days" in {
    val identicalOffers = offers :+ Offer(10f, 2, None, None)
    val offer = offerResponse.copy(quantity = 5, offers = identicalOffers, priceType = ListPrice).bestPriceByDate(
      today.plusDays(6).format(format)
    )
    offer.price should be(10f)
    offer.productionDays should be(2)
    offer.productionDate should be(today.plusDays(2).format(format))
  }
  it should "be the offer for 8 WD (the date before the desired date)" in {
    val offer = offerResponse.bestPriceByDate(today.plusDays(9).format(format))
    offer.price should be(20f)
    offer.productionDays should be(8)
    offer.productionDate should be(today.plusDays(8).format(format))
  }

  "Response" should "return no price points if no lead time preferences given" in {
    offerResponse.toPricePoints(Seq(), sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())) should be(Seq())
  }
  it should "return empty price points if no offers" in {
    val newOfferResponse = offerResponse.copy(quantity = 5, offers = Seq(), priceType = ListPrice)
    newOfferResponse.toPricePoints(
      Seq(LeadTime(LeadTimePreference.Fastest)),
      sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())
    ) should be(Seq())
  }
  it should "return fastest price point for the fastest time preference" in {
    val points = offerResponse.toPricePoints(
      Seq(LeadTime(LeadTimePreference.Fastest)),
      sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())
    )
    points.size should be(1)
    points.head.point.quantity should be(5)
    points.head.point.amount should be(14f)
    points.head.point.leadTimeDays should be(Some(3))
  }
  it should "return best price point for the best price preference" in {
    val points = offerResponse.toPricePoints(
      Seq(LeadTime(LeadTimePreference.BestPrice)),
      sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())
    )
    points.size should be(1)
    points.head.point.quantity should be(5)
    points.head.point.amount should be(2f)
    points.head.point.leadTimeDays should be(Some(10))
  }
  it should "return the price point for 6 WD" in {
    val points =
      offerResponse.toPricePoints(
        Seq(LeadTime(LeadTimePreference.BestPriceByDate, Some(today.plusDays(6).format(format)))),
        sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())
      )
    points.size should be(1)
    points.head.point.quantity should be(5)
    points.head.point.amount should be(8f)
    points.head.point.leadTimeDays should be(Some(6))
  }
  it should "return best price and fastest price points for the best price preference" in {
    val points = offerResponse.toPricePoints(
      Seq(LeadTime(LeadTimePreference.BestPrice), LeadTime(LeadTimePreference.Fastest)),
      sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())
    )
    points.size should be(2)
    points.head.point.quantity should be(5)
    points.head.point.amount should be(2f)
    points.head.point.leadTimeDays should be(Some(10))
    points.last.point.quantity should be(5)
    points.last.point.amount should be(14f)
    points.last.point.leadTimeDays should be(Some(3))
  }
  it should "return all points from Wuerth API if ignoreLeadTimes = true" in {
    val points = offerResponse.toPricePoints(
      Seq(),
      ignoreLeadTimes = true,
      sourcingScenarioId = SourcingScenarioId(UUID.randomUUID())
    )
    points.size should be(7)
    points.head.point.quantity should be(5)
    points.head.point.amount should be(14f)
    points.head.point.leadTimeDays should be(Some(3))
    points.last.point.quantity should be(5)
    points.last.point.amount should be(2f)
    points.last.point.leadTimeDays should be(Some(10))
  }

}
