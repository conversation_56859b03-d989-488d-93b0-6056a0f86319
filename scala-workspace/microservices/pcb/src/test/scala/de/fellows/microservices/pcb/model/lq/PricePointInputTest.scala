package de.fellows.microservices.pcb.model.lq

import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec
import play.api.libs.json.Json

class PricePointInputTest extends AnyWordSpec with should.Matchers {

  val pricePointInput = PricePointInput(5, 13.4f, None, None)

  "JSON for PricePointInput" should {
    "have quantity = 5" in {
      (Json.toJson(pricePointInput) \ "quantity").as[Int] should be(5)
    }
    "have amount = 13.4" in {
      (Json.toJson(pricePointInput) \ "amount").as[Float] should be(13.4f)
    }
    "have lead_time_days = None" in {
      (Json.toJson(pricePointInput) \ "lead_time_days").asOpt[Int] shouldBe None
    }
    "have lead_time_days = 10" in {
      (Json.toJson(pricePointInput.copy(leadTimeDays = Some(10))) \ "lead_time_days").asOpt[Int] shouldBe Some(10)
    }
  }
}
