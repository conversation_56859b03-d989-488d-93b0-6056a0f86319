package de.fellows.microservices.pcb.model.lq

import de.fellows.microservices.pcb.{PropertyErrors, ThirdPartyError}
import de.fellows.microservices.pcb.model.pcb.props.{BoardWidth, NumberOfLayers}
import de.fellows.microservices.pcb.model.pcb.{PropertyError, PropertyErrorKind}
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec
import play.api.libs.json.{JsValue, Json}
import sttp.model.StatusCode

import java.util.UUID
import play.api.libs.json.JsObject

class RequestOfferErrorTest extends AnyWordSpec with should.Matchers {
  val boardWidth     = BoardWidth(12.13)
  val numberOfLayers = NumberOfLayers(2)

  "JSON for parameter error" should {
    val error           = PropertyError(boardWidth, PropertyErrorKind.Plain("must be red"))
    "have name = 'color'" in {
      (Json.toJson(error) \ "name").as[String] shouldBe "boardWidth"
    }
    "have error = 'must be red'" in {
      (Json.toJson(error) \ "kind").as[JsObject] shouldBe Json.obj("Plain" -> Json.obj("message" -> "must be red"))
    }
  }

  "JSON for offer error" should {
    val pcbId     = UUID.fromString("cdc0dbf4-49a1-4c11-b7a3-0ee60e5edba5")
    val requestId = UUID.fromString("cdc0dbf4-1111-4c11-b7a3-0ee60e5edba5")
    val offerError =
      RequestOfferError(pcbId, requestId, ThirdPartyError("request failed", StatusCode.InternalServerError))
    "have pcb_id = cdc0dbf4-49a1-4c11-b7a3-0ee60e5edba5" in {
      (Json.toJson(offerError) \ "pcb_id").as[String] shouldBe "cdc0dbf4-49a1-4c11-b7a3-0ee60e5edba5"
    }
    "have request_id = cdc0dbf4-1111-4c11-b7a3-0ee60e5edba5" in {
      (Json.toJson(offerError) \ "request_id").as[String] shouldBe "cdc0dbf4-1111-4c11-b7a3-0ee60e5edba5"
    }
    "have error = 'request failed'" in {
      (Json.toJson(offerError) \ "error").as[String] shouldBe "request failed"
    }
    "have error has length = 2" in {
      val offerError = RequestOfferError(
        pcbId,
        requestId,
        PropertyErrors(Seq(
          PropertyError(boardWidth, PropertyErrorKind.Plain("must be red")),
          PropertyError(numberOfLayers, PropertyErrorKind.Plain("must be large"))
        ))
      )
      (Json.toJson(offerError) \ "error").as[Seq[JsValue]].length shouldBe 2
    }
  }
}
