package de.fellows.microservices.pcb.model.lq

import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.luminovo.panel._
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.lq.PriceType.{ContractPrice, ListPrice}
import de.fellows.microservices.pcb.model.pcb.ManufacturerApi
import de.fellows.utils.CurrencyCode
import de.fellows.utils.model.PCBId
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec
import play.api.libs.json.{JsArray, JsString, Json}

import java.util.UUID

class PcbOfferInputTest extends AnyWordSpec with should.Matchers {

  private val pcbId       = "cdc0dbf4-49a1-4c11-b7a3-0ee60e5edba5"
  private val pricePoints = Seq(PricePointInput(5, 13.4f, Some(5), None), PricePointInput(10, 16.6f, Some(6), None))

  private val manufacturerOffers = mkOffer(ManufacturerApi.Wuerth, ContractPrice, pricePoints)

  private val pcb = PcbOfferInput(
    pcbId = PCBId(UUID.fromString(pcbId)),
    pcbHash = Some("some hash"),
    responses = Seq(manufacturerOffers)
  )

  "JSON for PcbOfferInput" should {
    "have UUID = cdc0dbf4-49a1-4c11-b7a3-0ee60e5edba5" in {
      (Json.toJson(pcb) \ "pcb_id").as[UUID] should be(UUID.fromString(pcbId))
    }
    "have a single manufacturer entry" in {
      (Json.toJson(pcb) \ "responses").as[JsArray].value.toSeq.length shouldBe 1
    }
    "have a single offer entry" in {
      val json = Json.toJson(pcb)
      (json \ "responses" \ 0 \ "status" \ "type").as[JsString].value shouldBe "Success"
      (json \ "responses" \ 0 \ "status" \ "offers").as[JsArray].value.toSeq.length shouldBe 1
    }
    "have two price points" in {
      val json = Json.toJson(pcb)
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "price_points").as[JsArray].value.toSeq.length shouldBe 2
    }
    "have valid_until = null" in {
      (Json.toJson(pcb) \ "valid_until").asOpt[String] shouldBe None
    }
    "have offer_number = offer_number" in {
      val json = Json.toJson(pcb)
      (json \ "responses" \ 0 \ "status" \ "offers").as[JsArray].value.toSeq.length shouldBe 1
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "offer_number").as[String] shouldBe "offer_number"
    }
    "have offer_url = https://www.wedirekt.de/de/pcb/load_offer/offer_number" in {
      val json = Json.toJson(pcb)
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "offer_url").as[String] shouldBe "some_url"
    }
    "have offer_url = https://de.beta-layout.com/?apiLoadConfig=offer_number" in {
      val input = Json.toJson(pcb.copy(responses =
        Seq(manufacturerOffers.copy(api = ManufacturerApiWithInformation(ManufacturerApi.BetaLayout)))
      ))
      (input \ "responses" \ 0 \ "api" \ "api").as[String] shouldBe "BetaLayout"
    }
    "have offer_validity = long_hash" in {
      val json = Json.toJson(pcb)
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "offer_validity").as[String] shouldBe "long_hash"
    }
    "have price_type = ContractPrice" in {
      val json = Json.toJson(pcb)
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "price_type").as[String] shouldBe "ContractPrice"
    }
    "have price_type = ListPrice" in {
      val json = Json.toJson(pcb.copy(responses = Seq(mkOffer(ManufacturerApi.Wuerth, ListPrice, pricePoints))))
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "price_type").as[String] shouldBe "ListPrice"
    }
    "have pieces_per_unit = 4" in {
      val json = Json.toJson(pcb)
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "pieces_per_unit").as[Int] shouldBe 4
    }
    "have notes = Delivery panel: 3 rows, 2 columns (256mm x 56mm)" in {
      val notes = "Delivery panel: 3 rows, 2 columns (256mm x 56mm)"
      val json  = Json.toJson(pcb.copy(responses = Seq(mkOffer(ManufacturerApi.Wuerth, ListPrice, pricePoints, notes))))
      (json \ "responses" \ 0 \ "status" \ "offers" \ 0 \ "notes").as[String] shouldBe notes
    }
    "have manufacturer = Wuerth" in {
      (Json.toJson(pcb) \ "responses" \ 0 \ "api" \ "api").as[String] shouldBe "Wuerth"
    }
  }

  private def mkOffer(
      manufacturer: ManufacturerApi,
      priceType: PriceType,
      pricePoints: Seq[PricePointInput],
      note: String = "some note"
  ): ManufacturerStatus =
    ManufacturerStatus(
      manufacturer = helper.mockManufacturer(),
      api = ManufacturerApiWithInformation(manufacturer),
      location = helper.mockManufacturerLocation(),
      status = PcbOfferStatus.Success(
        Seq(
          PcbOffer(
            pricePoints = pricePoints,
            currency = CurrencyCode.EUR,
            validUntil = None,
            offerNumber = "offer_number",
            offerValidity = "long_hash",
            priceType = priceType,
            piecesPerUnit = 4,
            notes = note,
            offerUrl = Some("some_url"),
            oneTimeCosts = None,
            sourcingScenarioId = SourcingScenarioId(UUID.randomUUID()),
            sharedPcbId = None,
            panel = None,
            panelSpecification = Some(PanelSpecification.PanelDetailsSpecification(
              PanelDetails(
                id = None,
                rowCount = 1,
                columnCount = 1,
                horizontalSpacingInMm = 2,
                verticalSpacingInMm = 2,
                minMillingDistanceInMm = 8,
                padding = LuminovoPadding(1, 1, 1, 1),
                depanelization = Depanelization.Milling,
                pcbIsRotated = false
              )
            ))
          )
        )
      )
    )
}
