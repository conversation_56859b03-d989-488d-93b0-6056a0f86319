package de.fellows.microservices.pcb.model.pcb

import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.model.pcb.props.NumberOfLayers.NumberOfLayersCapability
import de.fellows.microservices.pcb.model.pcb.props.{<PERSON><PERSON><PERSON><PERSON>, OneLayer, <PERSON>L<PERSON>ers, SixteenLayers, TwoLayers}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import zio.NonEmptyChunk

class SetCapabilityTest extends AnyFlatSpec with should.Matchers {
  val capability          = new NumberOfLayersCapability(OneLayer, TwoLayers, FourLayers)
  implicit val lang: Lang = Lang("en")

  "Validation" should "fail with message `The value for number of layers is not supported" in {
    capability.validate(SixteenLayers).toEither shouldBe Left(
      NonEmptyChunk(PropertyError(
        SixteenLayers,
        "The value for number of layers is not supported",
        PropertyErrorKind.NotInSet
      ))
    )
  }

  it should "fail with messages `The value for number of layers is not supported" in {
    capability.validate(SixLayers).toEither shouldBe Left(
      NonEmptyChunk(PropertyError(
        SixLayers,
        "The value for number of layers is not supported",
        PropertyErrorKind.NotInSet
      ))
    )
  }

  it should "succeed" in {
    capability.validate(FourLayers).toEither shouldBe Right(FourLayers)
  }
}
