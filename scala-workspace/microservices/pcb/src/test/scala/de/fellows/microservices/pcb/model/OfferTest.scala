package de.fellows.microservices.pcb.model

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import play.api.libs.json.Json

class OfferTest extends AnyFlatSpec with should.Matchers {
  val offerJson = "{" +
    "\"basic_price_net\": 375.0," +
    "\"deliverytime\": 3" +
    "}"

  "Offer" should "be created from json" in {
    implicit val reads = Offer.reads
    val offer          = Json.parse(offerJson).as[Offer]
    offer.price should be(375.0)
    offer.productionDays should be(3)
  }
}
