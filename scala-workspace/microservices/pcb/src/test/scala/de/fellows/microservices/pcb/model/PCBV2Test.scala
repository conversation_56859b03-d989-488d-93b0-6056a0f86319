package de.fellows.microservices.pcb.model

import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.PCBV2BasicBoardProperties
import de.fellows.ems.pcb.api.specification.{ApplicationType, Side, SilkscreenColor, SoldermaskColor, SurfaceFinish}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import play.api.libs.json.{JsUndefined, Json}
import de.fellows.ems.pcb.api.specification.units.UnitConversions._
import de.fellows.ems.pcb.api.specification.units.UnitConversions.implicits._

class PCBV2Test extends AnyFlatSpec with should.Matchers {

  private val basicBoardProperties = PCBV2BasicBoardProperties(
    boardHeight = Some(150.55 millimeters),
    boardWidth = Some(300.34 millimeters),
    silkscreenColor = Some(SilkscreenColor.Blue),
    silkscreenSide = Some(Side.Both),
    surfaceFinish = Some(SurfaceFinish.HalPbFree),
    enigThickness = Some(0.1 microinch),
    applicationType = Some(ApplicationType.Medical),
    notes = Some("notes"),
    hardGold = Some(true),
    hardGoldArea = Some(1.0 squareMillimeters),
    hardGoldThickness = Some(0.2 millimeters),
    exposedCopperArea = Some(2.0 squareMillimeters),
    exposedCopperAreaTop = Some(3.0 squareMillimeters),
    exposedCopperAreaBottom = Some(4.0 squareMillimeters),
    traceWidth = Some(0.1 millimeters),
    outerTraceWidth = Some(0.2 millimeters),
    innerTraceWidth = Some(0.3 millimeters),
    copperClearance = Some(0.4 millimeters),
    outerCopperClearance = Some(0.5 millimeters),
    innerCopperClearance = Some(0.6 millimeters),
    soldermaskColor = Some(SoldermaskColor.Red),
    soldermaskSide = Some(Side.Top),
    pasteSide = None,
    soldermaskDam = Some(0.7 millimeters),
    soldermaskClearance = Some(0.8 millimeters)
  )

  "PCBV2BasicBoardProperties" should "be serialized and deserialized successfully" in {
    val json   = Json.toJson(basicBoardProperties)
    val string = json.toString

    basicBoardProperties shouldBe Json.parse(string.getBytes).as[PCBV2BasicBoardProperties]
  }

  it should "not serialize missing fields as null" in {
    val json = Json.toJson(basicBoardProperties.copy(boardHeight = None))
    (json \ "boardHeight") shouldBe a[JsUndefined]
  }
}
