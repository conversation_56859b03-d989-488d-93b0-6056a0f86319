package de.fellows.microservices.pcb.model.pcb

import de.fellows.microservices.pcb.model.pcb.props.BoardWidth.BoardWidthCapability
import de.fellows.microservices.pcb.model.pcb.props.BoardWidth
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import zio.NonEmptyChunk

class MinMaxRequiredCapabilityTest extends AnyFlatSpec with should.Matchers {

  val capability          = new BoardWidthCapability(1.0, 4.0)
  "Validation" should "fail with message `The value for board width is below the supported minimum of 1mm" in {
    val width = BoardWidth(0.5)
    capability.validate(width).toEither shouldBe Left(
      NonEmptyChunk(PropertyError(
        width,
        PropertyErrorKind.BelowMin(value = 0.5, min = 1, unit = "mm")
      ))
    )
  }

  it should "fail with message `The value for board width is above the supported maximum of 4mm" in {
    val width = BoardWidth(13)
    capability.validate(width).toEither shouldBe Left(
      NonEmptyChunk(PropertyError(
        width,
        PropertyErrorKind.AboveMax(value = 13, max = 4, unit = "mm")
      ))
    )
  }

  it should "fail with message `The value for board width is above the supported maximum of 3mm" in {
    val width = BoardWidth(13)
    new BoardWidthCapability(1.0, 3.0).validate(width).toEither shouldBe Left(
      NonEmptyChunk(PropertyError(
        width,
        PropertyErrorKind.AboveMax(value = 13, max = 3, unit = "mm")
      ))
    )
  }
}
