package de.fellows.microservices.pcb.model.pcb.capability

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.IPC600Class.IPC3
import de.fellows.ems.pcb.api.specification.Side
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.{No, Yes}
import de.fellows.microservices.pcb.model.pcb.props.ETest.ETestCapability
import de.fellows.microservices.pcb.model.pcb.props.EdgeMetalization.EdgeMetalizationCapability
import de.fellows.microservices.pcb.model.pcb.props.IPCA600Class.IPCA600ClassCapability
import de.fellows.microservices.pcb.model.pcb.props.ImpedanceTested.ImpedanceTestedCapability
import de.fellows.microservices.pcb.model.pcb.props.PeelableMask.PeelableMaskCapability
import de.fellows.microservices.pcb.model.pcb.props.PressFit.PressFitCapability
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec

class AdvancedBoardCapabilityTest extends AnyWordSpec with should.Matchers {
  implicit val lang: Lang = Lang("en")

  val props = helper.emptyProperties

  "After applying set of capabilities, result advanced capability" should {
    val boardCapability = AdvancedBoardCapability()
    "have a new IPC-A-600 class capability" in {
      val capability = new IPCA600ClassCapability(IPC3)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.ipcA600Class should be(capability)
    }
    "have a new E-test capability" in {
      val capability = new ETestCapability(Yes, No)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.eTest should be(capability)
    }
    "have a new press fit capability" in {
      val capability = new PressFitCapability(Yes, No)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.pressFit should be(capability)
    }
    "have a new impedance tested capability" in {
      val capability = new ImpedanceTestedCapability(Yes, No)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.impedanceTested should be(capability)
    }
    "have a new edge metalization capability" in {
      val capability = new EdgeMetalizationCapability(Yes, No)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.edgeMetalization should be(capability)
    }
    "have a new peelable mask side capability" in {
      val capability = new PeelableMaskCapability(Side.Both)
      val changed = boardCapability.applyCapabilities(Seq(capability))
      changed.peelableMask should be(capability)
    }
  }
}
