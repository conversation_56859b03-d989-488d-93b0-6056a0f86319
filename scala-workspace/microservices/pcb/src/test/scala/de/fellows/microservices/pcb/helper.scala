package de.fellows.microservices.pcb

import de.fellows.app.assembly.commons.ProjectType
import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.ems.pcb.api.specification.BaseMaterial.FR4
import de.fellows.ems.pcb.api.specification.IPC600Class.IPC2
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi._
import de.fellows.ems.pcb.api.specification.Side.Bottom
import de.fellows.ems.pcb.api.specification.SurfaceFinish.HalPbFree
import de.fellows.ems.pcb.api.specification.ULMarkingTypeEnum.NoMarking
import de.fellows.ems.pcb.api.specification.units.UnitConversions._
import de.fellows.ems.pcb.api.specification.ViaFillingType.PluggedSingleSided
import de.fellows.ems.pcb.api.specification.{DateCodeType, LayerstackType => SpecLayerstackType}
import de.fellows.microservices.pcb.model.panel.{<PERSON>R<PERSON>tangle, PanelRenderer}
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.props._
import de.fellows.utils.Region
import de.fellows.utils.model.PCBId

import java.io.FileWriter
import java.util.UUID

private[pcb] object helper {

  val uuid = PCBId(UUID.fromString("cdc0dbf4-49a1-4c11-b7a3-0ee60e5edba5"))
  // The properties which are not set explicitly are not supported by Wurth
  val basicProperties = PCBV2BasicBoardProperties.empty.copy(
    boardHeight = Some(150.55 millimeters),
    boardWidth = Some(300.34 millimeters),
    silkscreenSide = Some(Bottom),
    surfaceFinish = Some(HalPbFree),
    notes = None,
    hardGold = Some(false),
    hardGoldArea = Some(10.1 squareMillimeters),
    soldermaskColor = Some(de.fellows.ems.pcb.api.specification.SoldermaskColor.Green),
    soldermaskSide = Some(Bottom),
    exposedCopperAreaTop = Some(3.2 squareMillimeters),
    exposedCopperAreaBottom = Some(4.2 squareMillimeters),
    copperClearance = Some(0.1 millimeters),
    traceWidth = Some(0.01 millimeters),
    soldermaskDam = Some(2.0 millimeters),
    soldermaskClearance = Some(1.56 millimeters)
  )
  val advancedProperties = PCBV2AdvancedBoardProperties.empty.copy(
    ipc600Class = Some(IPC2),
    eTest = Some(true),
    edgeMetalization = Some(true),
    ctiClass = CtiClass.none.value
  )
  val board = PCBV2BoardProperties(basicProperties, advancedProperties)
  val layerStack = PCBV2LayerStackProperties.empty.copy(
    layerstackType = Some(SpecLayerstackType.Rigid),
    layercount = Some(4),
    ulLayerStack = Some(false),
    ulMarkingType = Some(NoMarking),
    baseMaterial = Some(FR4),
    finalThickness = Some(1.8 millimeters),
    outerCopperThickness = Some(85.0 micrometers),
    minOuterLayerStructure = Some(0.16 millimeters),
    innerCopperThickness = Some(35.0 micrometers),
    minInnerLayerStructure = Some(0.1 millimeters)
  )
  val mechanical = PCBV2MechanicalProperties.empty.copy(
    minViaDiameter = Some(0.3 millimeters),
    buriedVias = Some(false),
    chamfering = None,
    viaFilling = Some(PluggedSingleSided)
  )
  val reports: PCBV2ReportsProperties = PCBV2ReportsProperties(
    crossSection = Some(false),
    xRayMeasurement = Some(false),
    xRayMeasurementPoints = None,
    firstArticleInspection = Some(false),
    certificateOfConformance = Some(false)
  )

  val markings: PCBV2MarkingsProperties = PCBV2MarkingsProperties(
    dateCode = Some(DateCodeType.NoDateCode),
    fabricatorLogo = Some(false),
    datamatrixCode = Some(false)
  )

  val misc: PCBV2MiscellaneousProperties = PCBV2MiscellaneousProperties(
    manufacturingInformation = None
  )
  val properties = PCBV2Properties(board, layerStack, mechanical, reports, markings, misc)

  val stackRatePcb = PCBV2(
    id = uuid.value,
    name = Some("PCB name"),
    assembly = UUID.randomUUID(),
    description = None,
    created = java.time.Instant.now(),
    files = None,
    filesLocked = false,
    lifecycles = Seq(),
    orderId = Some("order id"),
    outline = None,
    specifications = Seq(),
    properties = properties,
    customer = None,
    projectType = ProjectType.NoFiles
  )
  val defaultPcb = PCB(stackRatePcb)

  val emptyBasicBoardProps = BasicBoardProperties(
    boardWidth = BoardWidth.zero,
    boardHeight = BoardHeight.zero,
    silkscreenSide = SilkscreenSide.default,
    silkscreenColor = SilkscreenColor.default,
    hardGold = HardGold(false),
    hardGoldArea = HardGoldArea.default,
    surfaceFinish = SurfaceFinish.default,
    enigThickness = EnigThickness(None),
    soldermaskSide = SoldermaskSide.default,
    soldermaskColor = SoldermaskColor.default,
    minTraceWidth = MinTraceWidth.empty,
    outerTraceWidth = OuterTraceWidth.empty,
    innerTraceWidth = InnerTraceWidth.empty,
    exposedCopperAreaTop = ExposedCopperAreaTop.empty,
    exposedCopperAreaBottom = ExposedCopperAreaBottom.empty,
    copperClearance = CopperClearance.empty,
    outerCopperClearance = OuterCopperClearance.empty,
    innerCopperClearance = InnerCopperClearance.empty,
    soldermaskDam = SoldermaskDam.empty,
    soldermaskClearance = SoldermaskClearance.empty
  )
  val emptyAdvancedBoardProps = AdvancedBoardProperties(
    ipcA600Class = IPCA600Class.default,
    eTest = ETest.no,
    pressFit = PressFit.no,
    impedanceTested = ImpedanceTested.no,
    impedanceTolerance = ImpedanceTolerance.none,
    peelableMask = PeelableMask.default,
    edgeMetalization = EdgeMetalization.no,
    itarCompliance = ItarCompliance.no,
    maxXOutsAllowed = MaxXOutsAllowed.empty,
    carbonPrint = CarbonPrint.default,
    halogenFree = HalogenFree.no,
    ctiClass = CtiClass.none,
    captonTape = CaptonTape.default,
    halfCutPlatedVias = HalfCutPlatedVias.no,
    ecobond = Ecobond.no,
    numberOfLines = NumberOfLines.empty
  )

  val emptyLayerStackProps = LayerStackProperties(
    layerstackType = LayerstackType.rigid,
    ulLayerStack = ULLayerStack.no,
    uLMarkingType = ULMarkingType.noMarking,
    numberOfLayers = OneLayer,
    finalThickness = FinalThickness.default,
    baseMaterial = BaseMaterial.default,
    outerCopperThickness = Outer70mcr,
    innerCopperThickness = InnerCopperThickness.empty,
    minOuterLayerStructure = MinOuterLayerStructure.empty,
    minInnerLayerStructure = MinInnerLayerStructure.empty,
    tgValue = TGValue.empty,
    customStackUp = CustomStackUp.no,
    numberOfPrepregs = NumberOfPrepregs.empty,
    numberOfLaminationCycles = NumberOfLaminationCycles.empty
  )

  val emptyMechanicalProps = MechanicalProperties(
    minViaDiameter = MinViaDiameter.empty,
    viaFillingType = ViaFillingType.default,
    blindVias = BlindVias.no,
    buriedVias = BuriedVias.no,
    blindViaCount = BlindViaCount.empty,
    buriedViaCount = BuriedViaCount.empty,
    chamfering = Chamfering.default,
    outlineLength = OutlineLength.empty,
    aspectRatio = AspectRatio.empty,
    phCount = PhCount.empty,
    phToolCount = PhToolCount.empty,
    phMinDiameter = PhMinDiameter.empty,
    phMaxDiameter = PhMaxDiameter.empty,
    nphCount = NphCount.empty,
    nphToolCount = NphToolCount.empty,
    nphMinDiameter = NphMinDiameter.empty,
    nphMaxDiameter = NphMaxDiameter.empty,
    phAnnularRing = PhAnnularRing.empty
  )

  val emptyMarkingProps = MarkingProperties(
    dateCodeMarking = DateCodeMarking.noMarking
  )

  val emptyProperties = PCBProperties(
    basic = emptyBasicBoardProps,
    advanced = emptyAdvancedBoardProps,
    layer = emptyLayerStackProps,
    mechanical = emptyMechanicalProps,
    markings = emptyMarkingProps
  )

  def saveTestRender(panel: Seq[DistributionRectangle], name: String = "test.svg"): Unit = {
    val svg    = PanelRenderer.render(panel)
    val writer = new FileWriter(name)
    svg.stream(writer, true)
    writer.close()
  }

  def mockManufacturer() =
    Manufacturer(
      supplier = UUID.randomUUID(),
      locations = Seq(mockManufacturerLocation()),
      name = "Würth Elektronik"
    )

  def mockManufacturerLocation() =
    ManufacturerLocation(
      stockLocation = UUID.randomUUID(),
      region = Region.Germany
    )

}
