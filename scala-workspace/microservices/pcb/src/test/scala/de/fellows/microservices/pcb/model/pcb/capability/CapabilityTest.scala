package de.fellows.microservices.pcb.model.pcb.capability

import de.fellows.microservices.pcb.model.pcb.props.BoardHeight.BoardHeightCapability
import de.fellows.microservices.pcb.model.pcb.props.BoardWidth.BoardWidthCapability
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class CapabilityTest extends AnyFlatSpec with should.Matchers {

  "Merging" should "add a capability on the left side to the result" in {
    val boardWidth = new BoardWidthCapability(10, 20)
    val left = Seq(boardWidth)
    val right = Seq()
    val result = Capability.merge(left, right)
    result should contain (boardWidth)
  }
  it should "add a capability on the right side to the result" in {
    val boardWidth = new BoardWidthCapability(10, 20)
    val left = Seq()
    val right = Seq(boardWidth)
    val result = Capability.merge(left, right)
    result should contain (boardWidth)
  }
  it should "add capabilities from the left and right to the result" in {
    val boardWidth = new BoardWidthCapability(10, 20)
    val boardHeight = new BoardHeightCapability(30, 40)
    val left = Seq(boardWidth)
    val right = Seq(boardHeight)
    val result = Capability.merge(left, right)
    result should contain (boardWidth)
    result should contain (boardHeight)
  }
  it should "replace a capability on the left side with a capability on the right side" in {
    val boardWidth = new BoardWidthCapability(10, 20)
    val boardWidth2 = new BoardWidthCapability(15, 25)
    val left = Seq(boardWidth)
    val right = Seq(boardWidth2)
    val result = Capability.merge(left, right)
    result should contain (boardWidth2)
    result should not contain (boardWidth)
  }
}
