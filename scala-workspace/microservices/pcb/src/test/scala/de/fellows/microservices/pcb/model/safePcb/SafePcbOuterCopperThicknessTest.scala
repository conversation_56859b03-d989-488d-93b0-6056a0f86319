package de.fellows.microservices.pcb.model.safePcb

import de.fellows.microservices.pcb.model.pcb.props
import de.fellows.microservices.pcb.model.pcb.props.OuterCopperThickness
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SafePcbOuterCopperThicknessTest extends AnyFlatSpec with should.Matchers {
  val outerCopperThickness = props.OuterCopperThickness(None)
  "Outer copper thickness" should "be None if PCB value is empty" in {
    SafePcbOuterCopperThickness.fromPcb(outerCopperThickness) shouldBe None
  }
  it should "be Mcr16 if PCB is less than 16" in {
    val origin = props.OuterCopperThickness(17)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr17)
  }
  it should "be Mcr17 if PCB is  17" in {
    val origin = props.OuterCopperThickness(17)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr17)
  }
  it should "be Mcr35 if PCB is less than 35" in {
    val origin = props.OuterCopperThickness(34)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr35)
  }
  it should "be Mcr35 if PCB is 35" in {
    val origin = props.OuterCopperThickness(35)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr35)
  }
  it should "be Mcr70 if PCB is less than 70" in {
    val origin = props.OuterCopperThickness(69)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr70)
  }
  it should "be Mcr70 if PCB is 70" in {
    val origin = props.OuterCopperThickness(70)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr70)
  }
  it should "be Mcr105 if PCB is less than 105" in {
    val origin = props.OuterCopperThickness(104)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr105)
  }
  it should "be Mcr105 if PCB is 105" in {
    val origin = props.OuterCopperThickness(105)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr105)
  }
  it should "be Mcr140 if PCB is less than 140" in {
    val origin = props.OuterCopperThickness(120)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr140)
  }
  it should "be Mcr140 if PCB is 140" in {
    val origin = props.OuterCopperThickness(140)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr140)
  }
  it should "be Mcr175 if PCB is less than 175" in {
    val origin = props.OuterCopperThickness(160)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr175)
  }
  it should "be Mcr175 if PCB is 175" in {
    val origin = props.OuterCopperThickness(175)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr175)
  }
  it should "be Mcr210 if PCB is less than 210" in {
    val origin = props.OuterCopperThickness(180)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr210)
  }
  it should "be Mcr210 if PCB is 210" in {
    val origin = props.OuterCopperThickness(210)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr210)
  }
  it should "be Mcr400 if PCB is less than 400" in {
    val origin = props.OuterCopperThickness(390)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr400)
  }
  it should "be Mcr400 if PCB is 400" in {
    val origin = props.OuterCopperThickness(400)
    SafePcbOuterCopperThickness.fromPcb(origin) shouldBe Some(SafePcbOuterThicknessMcr400)
  }
  it should "be `not supported` if PCB is more than 400" in {
    SafePcbOuterCopperThickness.fromPcb(OuterCopperThickness(500)) shouldBe None
  }

}
