package de.fellows.microservices.pcb.utils.json

import de.fellows.ems.layerstack.api.LayerDefinition
import de.fellows.ems.pcb.model.LayerFile
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.{
  HoleList<PERSON>son,
  OutlineJson,
  PCBHoleListGraphic,
  PCBLayer,
  PCBLayerGraphic,
  PCBOutlineGraphic,
  RendererGraphicJson
}
import de.fellows.utils.meta.MetaInfo
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.util.UUID

class JacksonUtilsTest extends AnyFlatSpec with should.Matchers {

  "JacksonUtils" should "serialize correctly" in {
    val uuid = UUID.fromString("76e4bdc0-**************-f27b3b2e7e7d")

    val layer = PCBLayerGraphic(
      graphic = Some(RendererGraphicJson("{\"A\": \"B\"}")),
      data = PCBLayer(
        definition = Some(
          LayerDefinition(
            id = Some(uuid),
            layerType = None,
            meta = None,
            materialRef = None,
            material = None
          )
        ),
        file = Some(
          LayerFile(
            id = Some(uuid),
            None,
            None,
            None,
            None,
            metaInfo = MetaInfo.empty,
            inverted = true
          )
        )
      )
    )

    val str = JacksonJsonUtils.writeValueAsString(layer)

    str shouldBe "{\"type\":\"layer\",\"graphic\":{\"A\": \"B\"},\"data\":{\"definition\":{\"id\":\"76e4bdc0-**************-f27b3b2e7e7d\"},\"file\":{\"id\":\"76e4bdc0-**************-f27b3b2e7e7d\",\"metaInfo\":{},\"inverted\":true}}}"
  }

  it should "serialize outline correctly" in {
    val graphics = PCBOutlineGraphic(OutlineJson("{\"name\":\"outline\"}"))
    val str      = JacksonJsonUtils.writeValueAsString(graphics)

    str shouldBe "{\"type\":\"outline\",\"data\":{\"name\":\"outline\"}}"
  }

  it should "serialize holelist correctly" in {
    val graphics = PCBHoleListGraphic(HoleListJson(("{\"name\":\"outline\"}")))
    val str      = JacksonJsonUtils.writeValueAsString(graphics)

    str shouldBe "{\"type\":\"drills\",\"data\":{\"name\":\"outline\"}}"
  }
}
