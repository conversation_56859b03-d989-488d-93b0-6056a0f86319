package de.fellows.microservices.pcb.model.safePcb

import de.fellows.microservices.pcb.model.panel.PanelSample
import de.fellows.luminovo.panel.Depanelization
import de.fellows.microservices.pcb.model.pcb.props.{
  BlindVias,
  BoardHeight,
  BoardWidth,
  BuriedVias,
  EdgeMetalization,
  ImpedanceTested,
  PressFit
}
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec

class XmlSafePcbRequestTest extends AnyWordSpec with should.Matchers {

  private val safePcbRequest = SafePcbRequest(
    name = Some("test-name"),
    width = BoardWidth(25),
    height = BoardHeight(10),
    ipcAClass = SafePcbIPCAClass2,
    numberOfLayers = FourLayers,
    material = SafePcbFR4,
    finalThickness = Mm030,
    outerCopperThickness = SafePcbOuterThicknessMcr35,
    innerCopperThickness = SafePcbInnerThicknessMcr35,
    surfaceFinish = SafePcbChemicalTin,
    solderMaskSide = SafePcbSolderMaskSidesTopBottom,
    solderMaskColor = SafePcbSolderMaskColorGreen,
    silkscreenSides = SafePcbSilkscreenSidesTopBottom,
    silkscreenColor = SafePcbSilkscreenColorBlack,
    peelableMask = SafePcbPeelableMaskTopBottom,
    impedanceTested = ImpedanceTested(false),
    edgeMetalization = EdgeMetalization(false),
    pressFit = PressFit(false),
    blindVias = BlindVias(false),
    buriedVias = BuriedVias(false),
    calculatedPanelInfo = PanelSample.calculatedPanelDetailsInfo,
    depanelization = SafePcbDepanelization(Depanelization.VCut)
  )
  "XML" should {
    "have family=RIGID" in {
      val xml = safePcbRequest.toXML
      (xml \ "BASE" \ "Family").text should be("RIGID")
    }
    "have Class_IPC=CLASS_2" in {
      val xml = safePcbRequest.toXML
      (xml \ "BASE" \ "Class_IPC").text should be("CLASS_2")
    }
    "have Norms=WITHOUT" in {
      val xml = safePcbRequest.toXML
      (xml \ "BASE" \ "Norms").text should be("WITHOUT")
    }
    "have Panel_Mode=21" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Panel_Mode").text should be("21")
    }
    "have Circuit_X=25" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Circuit_X").text should be("25")
    }
    "have Circuit_Y=10" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Circuit_Y").text should be("10")
    }
    "have Repeat_X=2" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Repeat_X").text should be("2")
    }
    "have Repeat_Y=3" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Repeat_Y").text should be("2")
    }
    "have Space_X=6" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Space_X").text should be("2")
    }
    "have Space_Y=5" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Space_Y").text should be("2")
    }
    "have Border_L=8" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Border_L").text should be("17")
    }
    "have Border_R=8" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Border_R").text should be("17")
    }
    "have Border_T=9" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Border_T").text should be("17")
    }
    "have Border_B=9" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Border_B").text should be("17")
    }
    "have Panel_X=100" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Panel_X").text should be("100")
    }
    "have Panel_Y=50" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Panel_Y").text should be("100")
    }
    "have Separation_XY=VCUT" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Separation_XY").text should be("VCUT")
    }
    "have Quantity_Circuit_Different=1" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Quantity_Circuit_Different").text should be("1")
    }
    "have Quantity_Circuit_per_Panel=6" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Quantity_Circuit_per_Panel").text should be("4")
    }
    "have Panel_Profil=CUSTOMER" in {
      val xml = safePcbRequest.toXML
      (xml \ "PANEL" \ "Panel_Profil").text should be("CUSTOMER")
    }
    "have Material=S1000H" in {
      val xml = safePcbRequest.toXML
      (xml \ "RIGID" \ "Material").text should be("S1000H")
    }
    "have Material=POLYIMIDE" in {
      val xml = safePcbRequest.copy(material = SafePcbPolyimide).toXML
      (xml \ "RIGID" \ "Material").text should be("SH260")
      (xml \ "RIGID" \ "Thickness").text should be("SH260_0.3mm")
    }
    "have Layers=R_4" in {
      val xml = safePcbRequest.toXML
      (xml \ "RIGID" \ "Layers").text should be("R_4")
    }
    "have Layers=R_6" in {
      val xml = safePcbRequest.copy(numberOfLayers = SixLayers).toXML
      (xml \ "RIGID" \ "Layers").text should be("R_6")
    }
    "have Thickness=S1000H_0.3mm" in {
      val xml = safePcbRequest.toXML
      (xml \ "RIGID" \ "Thickness").text should be("S1000H_0.3mm")
    }
    "have Thickness=S1000H_0.8mm" in {
      val xml = safePcbRequest.copy(finalThickness = Mm080).toXML
      (xml \ "RIGID" \ "Thickness").text should be("S1000H_0.8mm")
    }
    "have Thickness=S1000H_2.0mm" in {
      val xml = safePcbRequest.copy(finalThickness = Mm200).toXML
      (xml \ "RIGID" \ "Thickness").text should be("S1000H_2.0mm")
    }
    "have Surface_Finish=CHEM.TIN" in {
      val xml = safePcbRequest.toXML
      (xml \ "RIGID" \ "Surface_Finish").text should be("CHEM.TIN")
    }
    "have Surface_Finish=HAL" in {
      val xml = safePcbRequest.copy(surfaceFinish = SafePcbHal).toXML
      (xml \ "RIGID" \ "Surface_Finish").text should be("HAL")
    }
    "have Copper_Internal=WITHOUT" in {
      val xml = safePcbRequest.copy(numberOfLayers = TwoLayers).toXML
      (xml \ "RIGID" \ "Copper_Internal").text should be("WITHOUT")
    }
    "have Copper_Internal=35um" in {
      val xml = safePcbRequest.copy(innerCopperThickness = SafePcbInnerThicknessMcr35).toXML
      (xml \ "RIGID" \ "Copper_Internal").text should be("35um")
    }
    "have Copper_External=35um" in {
      val xml = safePcbRequest.copy(numberOfLayers = TwoLayers).toXML
      (xml \ "RIGID" \ "Copper_External").text should be("35um")
    }
    "have Copper_External=MUL_35um" in {
      val xml = safePcbRequest.copy(numberOfLayers = FourLayers).toXML
      (xml \ "RIGID" \ "Copper_External").text should be("MUL_35um")
    }
    "have Method=GERBER" in {
      val xml = safePcbRequest.toXML
      (xml \ "STACKUP" \ "Method").text should be("GERBER")
    }
    "have Method=WITHOUT" in {
      val xml = safePcbRequest.copy(numberOfLayers = TwoLayers).toXML
      (xml \ "STACKUP" \ "Method").text should be("WITHOUT")
    }
    "have soldermask Position=TOP.BOT" in {
      val xml = safePcbRequest.toXML
      (xml \ "SOLDERMASK" \ "Position").text should be("TOP.BOT")
    }
    "have soldermask Color_TOP=GREEN" in {
      val xml = safePcbRequest.toXML
      (xml \ "SOLDERMASK" \ "Color_TOP").text should be("GREEN")
    }
    "have soldermask Color_BOT=GREEN" in {
      val xml = safePcbRequest.toXML
      (xml \ "SOLDERMASK" \ "Color_BOT").text should be("GREEN")
    }
    "have soldermask Color_TOP=RED" in {
      val xml = safePcbRequest.copy(solderMaskColor = SafePcbSolderMaskColorRed).toXML
      (xml \ "SOLDERMASK" \ "Color_TOP").text should be("RED")
    }
    "have soldermask Color_BOT=WITHOUT" in {
      val xml = safePcbRequest.copy(solderMaskSide = SafePcbSolderMaskSidesTop).toXML
      (xml \ "SOLDERMASK" \ "Color_BOT").text should be("WITHOUT")
    }
    "have soldermask Color_TOP=WITHOUT" in {
      val xml = safePcbRequest.copy(solderMaskSide = SafePcbSolderMaskSidesNone).toXML
      (xml \ "SOLDERMASK" \ "Color_TOP").text should be("WITHOUT")
    }
    "have peelable mask Position=TOP.BOT" in {
      val xml = safePcbRequest.toXML
      (xml \ "PEELABLE" \ "Position").text should be("TOP.BOT")
    }
    "have peelable mask Type=MASK" in {
      val xml = safePcbRequest.toXML
      (xml \ "PEELABLE" \ "Type").text should be("MASK")
    }
    "have peelable mask Type=WITHOUT" in {
      val xml = safePcbRequest.copy(peelableMask = SafePcbPeelableMaskNone).toXML
      (xml \ "PEELABLE" \ "Type").text should be("WITHOUT")
    }
    "have Impedance_Control=NO" in {
      val xml = safePcbRequest.toXML
      (xml \ "OPTIONS" \ "Impedance_Control").text should be("NO")
    }
    "have Impedance_Control=YES" in {
      val xml = safePcbRequest.copy(impedanceTested = ImpedanceTested(true)).toXML
      (xml \ "OPTIONS" \ "Impedance_Control").text should be("YES")
    }
    "have Edge_Plating=NO" in {
      val xml = safePcbRequest.toXML
      (xml \ "OPTIONS" \ "Edge_Plating").text should be("NO")
    }
    "have Edge_Plating=YES" in {
      val xml = safePcbRequest.copy(edgeMetalization = EdgeMetalization(true)).toXML
      (xml \ "OPTIONS" \ "Edge_Plating").text should be("YES")
    }
    "have Press_Fit=NO" in {
      val xml = safePcbRequest.toXML
      (xml \ "OPTIONS" \ "Press_Fit").text should be("NO")
    }
    "have Press_Fit=YES" in {
      val xml = safePcbRequest.copy(pressFit = PressFit(true)).toXML
      (xml \ "OPTIONS" \ "Press_Fit").text should be("YES")
    }
    "have Blind_Holes=WITHOUT" in {
      val xml = safePcbRequest.toXML
      (xml \ "OPTIONS" \ "Blind_Holes").text should be("WITHOUT")
    }
    "have Blind_Holes=BLIND" in {
      val xml = safePcbRequest.copy(blindVias = BlindVias(true)).toXML
      (xml \ "OPTIONS" \ "Blind_Holes").text should be("BLIND")
    }
    "have Blind_Holes=BURIED" in {
      val xml = safePcbRequest.copy(buriedVias = BuriedVias(true)).toXML
      (xml \ "OPTIONS" \ "Blind_Holes").text should be("BURIED")
    }
    "have Blind_Holes=BLIND.BURIED " in {
      val xml = safePcbRequest.copy(buriedVias = BuriedVias(true), blindVias = BlindVias(true)).toXML
      (xml \ "OPTIONS" \ "Blind_Holes").text should be("BLIND.BURIED")
    }
    "have Color=BLACK for silkscreen color" in {
      val xml = safePcbRequest.toXML
      (xml \ "MARKING" \ "Color").text should be("BLACK")
    }
    "have Legend=TOP.BOT for silkscreen sides" in {
      val xml = safePcbRequest.toXML
      (xml \ "MARKING" \ "Legend").text should be("TOP.BOT")
    }
    "have Color=WITHOUT for silkscreen color" in {
      val xml = safePcbRequest.copy(silkscreenSides = SafePcbSilkscreenSidesNone).toXML
      (xml \ "MARKING" \ "Color").text should be("WITHOUT")
    }
    "have Panel_Mode=22" in {
      val xml = safePcbRequest.copy(calculatedPanelInfo = PanelSample.calculatedExistingPanelInfo).toXML
      (xml \ "PANEL" \ "Panel_Mode").text should be("22")
      (xml \ "PANEL" \ "Circuit_X").text should be("25")
      (xml \ "PANEL" \ "Circuit_Y").text should be("10")
      (xml \ "PANEL" \ "Repeat_X").text should be("")
      (xml \ "PANEL" \ "Repeat_Y").text should be("")
      (xml \ "PANEL" \ "Space_X").text should be("")
      (xml \ "PANEL" \ "Space_Y").text should be("")
      (xml \ "PANEL" \ "Border_L").text should be("")
      (xml \ "PANEL" \ "Border_R").text should be("")
      (xml \ "PANEL" \ "Border_T").text should be("")
      (xml \ "PANEL" \ "Border_B").text should be("")
      (xml \ "PANEL" \ "Panel_X").text should be("150")
      (xml \ "PANEL" \ "Panel_Y").text should be("200")
      (xml \ "PANEL" \ "Separation_XY").text should be("VCUT")
      (xml \ "PANEL" \ "Quantity_Circuit_Different").text should be("1")
      (xml \ "PANEL" \ "Quantity_Circuit_per_Panel").text should be("5")
      (xml \ "PANEL" \ "Panel_Profil").text should be("CUSTOMER")
    }
  }
}
