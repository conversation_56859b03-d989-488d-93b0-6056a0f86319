package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.MinOuterLayerStructure
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WurthOuterLayerStructureTest extends AnyFlatSpec with should.Matchers {

  val minOuterLayerStructure = MinOuterLayerStructure(None)
  "Outer layer structure thickness" should "be not supported if PCB outer layer structure is empty" in {
    WurthOuterLayerStructure.converter(minOuterLayerStructure) shouldBe None
  }
  it should "be Mcr85 if PCB thickness is 85" in {
    val origin = MinOuterLayerStructure(0.085)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr85(origin))
  }
  it should "be `not supported` if PCB thickness is less than 085" in {
    WurthOuterLayerStructure.converter(minOuterLayerStructure.copy(value = Some(0.045))) shouldBe None
  }
  it should "be Mcr085 if PCB thickness is more than 85 but less than 100" in {
    val origin = MinOuterLayerStructure(0.09)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr85(origin))
  }
  it should "be Mcr100 if PCB thickness is 100" in {
    val origin = MinOuterLayerStructure(0.1)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr100(origin))
  }
  it should "be Mcr100 if PCB thickness is more than 100" in {
    val origin = MinOuterLayerStructure(0.124)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr100(origin))
  }
  it should "be Mcr125 if PCB thickness is 125" in {
    val origin = MinOuterLayerStructure(0.125)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr125(origin))
  }
  it should "be Mcr125 if PCB thickness is more than 125 but less than 150" in {
    val origin = MinOuterLayerStructure(0.149)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr125(origin))
  }
  it should "be Mcr150 if PCB thickness is 150" in {
    val origin = MinOuterLayerStructure(0.150)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr150(origin))
  }
  it should "be Mcr150 if PCB thickness is more than 150 but less than 192" in {
    val origin = MinOuterLayerStructure(0.191)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr150(origin))
  }
  it should "be Mcr192 if PCB thickness is 192" in {
    val origin = MinOuterLayerStructure(0.192)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr192(origin))
  }
  it should "be Mcr192 if PCB thickness is less than 192 but less then 250" in {
    val origin = MinOuterLayerStructure(0.249)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr192(origin))
  }
  it should "be Mcr250 if PCB thickness is 250" in {
    val origin = MinOuterLayerStructure(0.250)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr250(origin))
  }
  it should "be Mcr250 if PCB thickness is more than 250" in {
    val origin = MinOuterLayerStructure(0.392)
    WurthOuterLayerStructure.converter(origin) shouldBe Some(OuterMcr250(origin))
  }

}
