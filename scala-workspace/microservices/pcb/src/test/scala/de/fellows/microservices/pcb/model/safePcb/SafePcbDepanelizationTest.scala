package de.fellows.microservices.pcb.model.safePcb

import de.fellows.luminovo.panel.Depanelization
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class SafePcbDepanelizationTest extends AnyFlatSpec with should.Matchers {
  "SafePcbDepanelization" should "have value VCut" in {
    SafePcbDepanelization(Depanelization.VCut).value should be("VCUT")
  }
  it should "have value Milling" in {
    SafePcbDepanelization(Depanelization.Milling).value should be("MIL")
  }
}
