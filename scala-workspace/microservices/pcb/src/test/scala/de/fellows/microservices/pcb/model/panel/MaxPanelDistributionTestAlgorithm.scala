package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.BigPoint
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class MaxPanelDistributionTestAlgorithm extends AnyFlatSpec with should.Matchers with EitherValues {

  "6 PCBs" should "be positioned on the max sized panel, less than allowed maximum PCBs per panel" in {
    val constraints = PanelPreferences.Empty.copy(
      minWidth = 100,
      minHeight = 100,
      maxWidth = 170,
      maxHeight = 145,
      maxPCBs = None,
      padding = new PanelPadding(0, 0),
      spacing = new PanelGap(1.0, 2.0)
    )
    val distribution = DistributionAlgorithm.calculate(6, new PcbSize(40, 70), constraints)
    val rects = List(
      DistributionRectangle(BigPoint(0.0, 0.0), BigPoint(70.0, 40.0), Pcb),
      DistributionRectangle(BigPoint(71.0, 0.0), BigPoint(141.0, 40.0), Pcb),
      DistributionRectangle(BigPoint(0.0, 42.0), BigPoint(70.0, 82.0), Pcb),
      DistributionRectangle(BigPoint(71.0, 42.0), BigPoint(141.0, 82.0), Pcb),
      DistributionRectangle(BigPoint(0.0, 84.0), BigPoint(70.0, 124.0), Pcb),
      DistributionRectangle(BigPoint(71.0, 84.0), BigPoint(141.0, 124.0), Pcb)
    )
    distribution.value.items should be(rects)
  }

}
