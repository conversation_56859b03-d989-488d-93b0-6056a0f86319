package de.fellows.microservices.pcb

import de.fellows.luminovo.panel._
import de.fellows.luminovo.sourcing._
import de.fellows.microservices.pcb.offers.OfferManager.createScenarioRequestsWithPanel
import de.fellows.microservices.pcb.model.lq.{ChangeStatus, LeadTime, LeadTimePreference, ScenarioRequest}
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerLocation}
import de.fellows.utils.Region
import de.fellows.utils.model.PCBId
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

import java.util.UUID

class MatchScenariosWithPanelTest extends AnyFlatSpec with should.Matchers {

  "createScenarioRequestsWithPanel" should "match sourcing scenario request with RfQ panel" in {
    val scenarios = Seq(
      mkScenarioRequest,
      mkScenarioRequest
    )

    val pcbId = PCBId(UUID.randomUUID())
    val pcbPanel = PerPcb(
      pcb = pcbId,
      panelDetails = mkPanelDetails(columns = 2)
    )

    // TODO stackrate panel matching
    val scenarioRequestWithPanels =
      createScenarioRequestsWithPanel(scenarios, pcbId, Seq(pcbPanel), Seq.empty, Map.empty)
    val expected = scenarios.map { s =>
      ScenarioRequestWithPanel(
        scenarioRequest = s,
        panelInfo = PcbPanelInfo.Details(pcbPanel.panelDetails),
        stackratePanel = None,
        manufacturers = Seq.empty
      )
    }

    scenarioRequestWithPanels should equal(expected)
  }

  it should "ignore non-matching sourcing scenarios" in {
    val scenarios = Seq(
      mkScenarioRequest,
      mkScenarioRequest
    )

    val pcbId = PCBId(UUID.randomUUID())
    val pcbPanel = PerPcb(
      pcb = pcbId,
      panelDetails = mkPanelDetails(columns = 2)
    )

    val sourcingScenarioPanel = PerSourcingScenario(
      sourcingScenario = SourcingScenarioId(UUID.randomUUID()),
      pcb = pcbId,
      panelDetails = mkPanelDetails(columns = 2)
    )

    val scenarioRequestWithPanels =
      createScenarioRequestsWithPanel(scenarios, pcbId, Seq(pcbPanel, sourcingScenarioPanel), Seq(), Map.empty)

    val expected = scenarios.map { s =>
      ScenarioRequestWithPanel(
        scenarioRequest = s,
        panelInfo = PcbPanelInfo.Details(pcbPanel.panelDetails),
        stackratePanel = None,
        manufacturers = Seq.empty
      )
    }

    scenarioRequestWithPanels should equal(expected)
  }

  it should "prioritize sourcing scenario panel over pcbPanel" in {
    val scenarioRequestWithPanel    = mkScenarioRequest
    val scenarioRequestWithoutPanel = mkScenarioRequest
    val scenarios = Seq(
      scenarioRequestWithPanel,
      scenarioRequestWithoutPanel
    )

    val pcbId = PCBId(UUID.randomUUID())
    val pcbPanel = PerPcb(
      pcb = pcbId,
      panelDetails = mkPanelDetails(columns = 2)
    )

    val sourcingScenarioPanel = PerSourcingScenario(
      sourcingScenario = scenarioRequestWithPanel.sourcingScenarioId,
      pcb = pcbId,
      panelDetails = mkPanelDetails(rows = 2)
    )

    val scenarioRequestWithPanels =
      createScenarioRequestsWithPanel(scenarios, pcbId, Seq(pcbPanel, sourcingScenarioPanel), Seq.empty, Map.empty)

    val expected = Seq(
      ScenarioRequestWithPanel(
        scenarioRequest = scenarioRequestWithPanel,
        panelInfo = PcbPanelInfo.Details(sourcingScenarioPanel.panelDetails),
        stackratePanel = None,
        manufacturers = Seq.empty
      ),
      ScenarioRequestWithPanel(
        scenarioRequest = scenarioRequestWithoutPanel,
        panelInfo = PcbPanelInfo.Details(pcbPanel.panelDetails),
        stackratePanel = None,
        manufacturers = Seq.empty
      )
    )

    scenarioRequestWithPanels should equal(expected)
  }

  it should "not match if nothing matches" in {
    val scenarioRequestWithPanel    = mkScenarioRequest
    val scenarioRequestWithoutPanel = mkScenarioRequest
    val scenarios = Seq(
      scenarioRequestWithPanel,
      scenarioRequestWithoutPanel
    )

    val pcbId = PCBId(UUID.randomUUID())
    val rfqPanel = PerPcb(
      pcb = pcbId,
      panelDetails = mkPanelDetails(columns = 2)
    )

    val sourcingScenarioPanel = PerSourcingScenario(
      sourcingScenario = scenarioRequestWithPanel.sourcingScenarioId,
      pcb = pcbId,
      panelDetails = mkPanelDetails(rows = 2)
    )

    val scenarioRequestWithPanels =
      createScenarioRequestsWithPanel(
        scenarios,
        PCBId(UUID.randomUUID()),
        Seq(rfqPanel, sourcingScenarioPanel),
        Seq.empty,
        Map.empty
      )

    val expected = Seq(
      ScenarioRequestWithPanel(
        scenarioRequest = scenarioRequestWithPanel,
        panelInfo = PcbPanelInfo.Details(sourcingScenarioPanel.panelDetails),
        stackratePanel = None,
        manufacturers = Seq.empty
      ),
      ScenarioRequestWithPanel(
        scenarioRequest = scenarioRequestWithoutPanel,
        panelInfo = PcbPanelInfo.NoPanel,
        stackratePanel = None,
        manufacturers = Seq.empty
      )
    )

    scenarioRequestWithPanels should equal(expected)
  }

  it should "add suppliers" in {
    val scenarioRequestWithPanel    = mkScenarioRequest
    val scenarioRequestWithoutPanel = mkScenarioRequest
    val scenarios = Seq(
      scenarioRequestWithPanel,
      scenarioRequestWithoutPanel
    )
    val manufacturer = Manufacturer(
      supplier = UUID.randomUUID(),
      locations = Seq(ManufacturerLocation(UUID.randomUUID(), Region.Europe)),
      name = "test"
    )
    val supplierMap = Map(
      scenarioRequestWithPanel.sourcingScenarioId -> Seq(manufacturer)
    )

    val pcbId = PCBId(UUID.randomUUID())
    val rfqPanel = PerPcb(
      pcb = pcbId,
      panelDetails = mkPanelDetails(columns = 2)
    )

    val sourcingScenarioPanel = PerSourcingScenario(
      sourcingScenario = scenarioRequestWithPanel.sourcingScenarioId,
      pcb = pcbId,
      panelDetails = mkPanelDetails(rows = 2)
    )

    val scenarioRequestWithPanels = createScenarioRequestsWithPanel(
      scenarios,
      PCBId(UUID.randomUUID()),
      Seq(rfqPanel, sourcingScenarioPanel),
      Seq.empty,
      supplierMap
    )

    val expected = Seq(
      ScenarioRequestWithPanel(
        scenarioRequest = scenarioRequestWithPanel,
        panelInfo = PcbPanelInfo.Details(sourcingScenarioPanel.panelDetails),
        stackratePanel = None,
        manufacturers = Seq(manufacturer)
      ),
      ScenarioRequestWithPanel(
        scenarioRequest = scenarioRequestWithoutPanel,
        panelInfo = PcbPanelInfo.NoPanel,
        stackratePanel = None,
        manufacturers = Seq.empty
      )
    )

    scenarioRequestWithPanels should equal(expected)
  }

  private def mkScenarioRequest: ScenarioRequest =
    ScenarioRequest(
      sourcingScenarioId = SourcingScenarioId(UUID.randomUUID()),
      leadTimes = Seq(LeadTime(LeadTimePreference.Fastest)),
      quantity = 10,
      change = ChangeStatus.New,
      supplierAndStockLocations = Seq.empty
    )

  private def mkPanelDetails(
      rows: Int = 1,
      columns: Int = 1
  ): PanelDetails =
    PanelDetails(
      id = Some(PanelId.random),
      rowCount = rows,
      columnCount = columns,
      horizontalSpacingInMm = 1,
      verticalSpacingInMm = 1,
      minMillingDistanceInMm = 1,
      padding = LuminovoPadding(1, 1, 1, 1),
      depanelization = Depanelization.VCut,
      pcbIsRotated = false
    )

}
