package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import de.fellows.luminovo.panel.{Depanelization, ExistingPanel}
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.utils.model.PCBId

object PanelSample {
  val panel: PanelDistribution = PanelDistribution.withoutPrecomputedGap(
    panel = Rectangle(100, 100),
    waste = WastedArea(2044, 0.0, 33),
    pcbPerPanel = 4,
    mesh = PcbMesh(2, 2),
    items = List(
      DistributionRectangle(Dimension(BigPoint(0, 0), BigPoint(100, 10)), Padding),
      DistributionRectangle(Dimension(BigPoint(0, 10), BigPoint(10, 100)), Padding),
      DistributionRectangle(Dimension(BigPoint(10, 90), BigPoint(100, 100)), Padding),
      DistributionRectangle(Dimension(BigPoint(90, 10), BigPoint(100, 90)), Padding),
      DistributionRectangle(Dimension(BigPoint(17, 17), BigPoint(49, 49)), Pcb),
      DistributionRectangle(Dimension(BigPoint(51, 17), BigPoint(83, 49)), Pcb),
      DistributionRectangle(Dimension(BigPoint(17, 51), BigPoint(49, 83)), Pcb),
      DistributionRectangle(Dimension(BigPoint(51, 51), BigPoint(83, 83)), Pcb)
    ),
    depanelization = Depanelization.Milling,
    minMillingDistanceInMm = 8,
    pcbIsRotated = false
  )

  val existingPanel: ExistingPanel = ExistingPanel(
    id = None,
    panelWidth = 150,
    panelHeight = 200,
    numberOfPcbs = 5,
    pcb = PCBId.random,
    depanelization = Depanelization.VCut,
    pcbIsRotated = false
  )

  val calculatedPanelDetailsInfo: CalculatedPanelInfo.FromPanelDetails = CalculatedPanelInfo.FromPanelDetails(
    panelDistribution = panel,
    requestedPcbs = RequestedPcbs(16),
    numberOfPanels = NumberOfPanels(4),
    depanelization = Depanelization.Milling
  )

  val calculatedExistingPanelInfo: CalculatedPanelInfo.FromExisting = CalculatedPanelInfo.FromExisting(
    existing = existingPanel,
    requestedPcbs = RequestedPcbs(16),
    numberOfPanels = NumberOfPanels(4)
  )

}
