package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.microservices.pcb.model.betaLayout.BetaLayoutLeadTime.OneDay
import de.fellows.microservices.pcb.model.panel.PanelSample
import de.fellows.microservices.pcb.model.pcb.Quantity
import de.fellows.microservices.pcb.model.pcb.props.{BoardHeight, BoardWidth, EdgeMetalization, SoldermaskSide}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class JsonBetaLayoutPcbRequestTest extends AnyFlatSpec with Matchers {
  private val rigidPcbRequest = BetaLayoutPcbRequest(
    quantity = Quantity(10),
    numberOfLayers = FourLayers,
    width = BoardWidth(150.55),
    height = BoardHeight(300.34),
    panelWidth = PanelSample.calculatedPanelDetailsInfo.panelDistribution.panel.widthInMm,
    panelHeight = PanelSample.calculatedPanelDetailsInfo.panelDistribution.panel.heightInMm,
    pcbsPerPanel = PanelSample.calculatedPanelDetailsInfo.panelDistribution.mesh.size,
    depanelization = BetaLayoutDepanelization.Milling,
    finalThickness = Thickness1mm(),
    surfaceFinish = HAL,
    silkscreenSides = SilkscreenSidesTop,
    minStructure = MinStructure125mcr(),
    drillDiameter = DrillDiameter0_15mm,
    edgeMetalization = EdgeMetalization.yes,
    soldermask = SoldermaskSide.default,
    tg = TG135,
    leadTime = None,
    orderId = None,
    calculatedPanelInfo = PanelSample.calculatedPanelDetailsInfo
  )
  private val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest)

  "Quantity" should "be serialized" in {
    (json \ "product" \ "quantity").as[Int] shouldBe 10
  }
  "Number of layers" should "be serialized" in {
    (json \ "product" \ "layer_count").as[Int] shouldBe 4
  }
  "Panel" should "be serialized" in {
    (json \ "product" \ "multipanel").as[Int] shouldBe 3
    (json \ "product" \ "multipanel_processing").as[Int] shouldBe 4
    ((json \ "product" \ "projects")(0) \ "multipanel_length").as[Double] shouldBe 100
    ((json \ "product" \ "projects")(0) \ "multipanel_width").as[Double] shouldBe 100
    ((json \ "product" \ "projects")(0) \ "multipanel_quantity_x").as[Double] shouldBe 4
  }
  "Surface" should "be serialized" in {
    (json \ "product" \ "surface").as[Int] shouldBe 2
  }
  "Edge metalization" should "be serialized as 1" in {
    (json \ "product" \ "edge_metallisation").as[Int] shouldBe 1
  }
  it should "be serialized as 0" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(edgeMetalization = EdgeMetalization.no))
    (json \ "product" \ "edge_metallisation").as[Int] shouldBe 0
  }
  "Thickness" should "be serialized as 2" in {
    (json \ "product" \ "material_thickness").as[Int] shouldBe 2
  }
  it should "be serialized as 1" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(finalThickness = Thickness1_6mm()))
    (json \ "product" \ "material_thickness").as[Int] shouldBe 1
  }
  "Min structure" should "be serialized as 1" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(minStructure = MinStructure150mcr()))
    (json \ "product" \ "track_gap_size").as[Int] shouldBe 1
  }
  it should "be serialized as 2" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(minStructure = MinStructure125mcr()))
    (json \ "product" \ "track_gap_size").as[Int] shouldBe 2
  }
  it should "be serialized as 3" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(minStructure = MinStructure100mcr()))
    (json \ "product" \ "track_gap_size").as[Int] shouldBe 3
  }
  "Drill diameter" should "be serialized as 1" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(drillDiameter = DrillDiameter0_3mm))
    (json \ "product" \ "drill_diameter").as[Int] shouldBe 1
  }
  it should "be serialized as 2" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(drillDiameter = DrillDiameter0_2mm))
    (json \ "product" \ "drill_diameter").as[Int] shouldBe 2
  }
  it should "be serialized as 3" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(drillDiameter = DrillDiameter0_15mm))
    (json \ "product" \ "drill_diameter").as[Int] shouldBe 3
  }
  it should "be serialized as 4" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(drillDiameter = DrillDiameter0_1mm))
    (json \ "product" \ "drill_diameter").as[Int] shouldBe 4
  }
  "Lead time" should "be serialized as 1" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(leadTime = Some(OneDay)))
    (json \ "product" \ "lead_time").as[Int] shouldBe 1
  }
  "Order id" should "be serialized as pcb-beta-layout-id" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(orderId = Some("pcb-beta-layout-id")))
    (json \ "order_id").as[String] shouldBe "pcb-beta-layout-id"
  }
  "Soldermask" should "be serialized as 0 for empty soldermask" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(soldermask = SoldermaskSide(Side.None)))
    (json \ "product" \ "soldermask").as[Int] shouldBe 0
  }
  it should "be serialized as 0 for soldermask on top" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(soldermask = SoldermaskSide(Side.Top)))
    (json \ "product" \ "soldermask").as[Int] shouldBe 0
  }
  it should "be serialized as 0 for soldermask on bottom" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(soldermask = SoldermaskSide(Side.Bottom)))
    (json \ "product" \ "soldermask").as[Int] shouldBe 0
  }
  it should "be serialized as 1 for soldermask on both sides" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(soldermask = SoldermaskSide(Side.Both)))
    (json \ "product" \ "soldermask").as[Int] shouldBe 1
  }
  "Silkscreen" should "be serialized as 0 for no silkscreen" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(silkscreenSides = SilkscreenSidesNone))
    (json \ "product" \ "silkscreen").as[Int] shouldBe 0
  }
  "Silkscreen" should "be serialized as 1 for silkscreen on top" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(silkscreenSides = SilkscreenSidesTop))
    (json \ "product" \ "silkscreen").as[Int] shouldBe 1
  }
  "Silkscreen" should "be serialized as 2 for silkscreen on bottom" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(silkscreenSides = SilkscreenSidesBottom))
    (json \ "product" \ "silkscreen").as[Int] shouldBe 2
  }
  "Silkscreen" should "be serialized as 3 for silkscreen on both sides" in {
    val json = BetaLayoutPcbRequest.writes.writes(rigidPcbRequest.copy(silkscreenSides = SilkscreenSidesTopBottom))
    (json \ "product" \ "silkscreen").as[Int] shouldBe 3
  }
  "Updated Panel" should "be serialized" in {
    val depanelization = BetaLayoutDepanelization.Milling
    val json = BetaLayoutPcbRequest.writes.writes(
      rigidPcbRequest.copy(
        calculatedPanelInfo = PanelSample.calculatedExistingPanelInfo,
        depanelization = depanelization,
        panelWidth = 200,
        panelHeight = 200,
        pcbsPerPanel = 6
      )
    )

    (json \ "product" \ "multipanel").as[Int] shouldBe 3
    (json \ "product" \ "multipanel_processing").as[Int] shouldBe 4
    ((json \ "product" \ "projects")(0) \ "multipanel_length").as[Double] shouldBe 200
    ((json \ "product" \ "projects")(0) \ "multipanel_width").as[Double] shouldBe 200
    ((json \ "product" \ "projects")(0) \ "multipanel_quantity_x").as[Double] shouldBe 6
  }
}
