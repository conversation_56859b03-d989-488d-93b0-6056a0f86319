package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.MinViaDiameter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WurthSmallestViaTest extends AnyFlatSpec with should.Matchers {

  val minViaDiameter = MinViaDiameter(None)
  "Smallest via" should "be `not supported, empty` if PCB min via diameter is empty" in {
    WurthSmallestVia.converter(minViaDiameter) shouldBe None
  }
  it should "be `empty` if PCB min via diameter is 0.05 (less than 0.10mm)" in {
    WurthSmallestVia.converter(minViaDiameter.copy(value = Some(0.05f))) shouldBe None
  }
  it should "be Mm10 if PCB min via diameter is 0.15 (less than 0.25mm)" in {
    WurthSmallestVia.converter(minViaDiameter.copy(value = Some(0.15f))) shouldBe Some(Mm10)
  }
  it should "be Mm10 if PCB min via diameter is 0.2499 (less than 0.25mm)" in {
    WurthSmallestVia.converter(minViaDiameter.copy(value = Some(0.2499f))) shouldBe Some(Mm10)
  }
  it should "be `not supported` if PCB min via diameter is 0.099 (less than 0.10mm)" in {
    WurthSmallestVia.converter(minViaDiameter.copy(value = Some(0.099f))) shouldBe None
  }
  it should "be Mm25 if PCB min via diameter is 0.25 (more or equal to 0.25mm)" in {
    WurthSmallestVia.converter(minViaDiameter.copy(value = Some(0.25f))) shouldBe Some(Mm25)
  }
  it should "be Mm25 if PCB min via diameter is 0.29 (more or equal to 0.25mm)" in {
    WurthSmallestVia.converter(minViaDiameter.copy(value = Some(0.29f))) shouldBe Some(Mm25)
  }

}
