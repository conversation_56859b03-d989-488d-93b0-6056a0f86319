package de.fellows.microservices.pcb.model.panel

import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec

class PanelBoundTest extends AnyWordSpec with should.Matchers {

  "Crop" should {
    "return a panel without paddings" in {
      val panel = PanelBound(
        w = 100,
        h = 100,
        padding = DistributionPanelPadding.fromPanelPadding(new PanelPadding(10, 5)),
        gap = Rectangle(2, 1)
      )
      panel.crop should be(PanelBound(80, 90, DistributionPanelPadding.zero, Rectangle(2, 1)))
    }
  }

  "Number of columns" should {
    "be 5" when {
      "panel width = 100, padding = 0, space = 0, pcb width = 20" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.zero,
          gap = Rectangle(0, 0)
        )
        panel.cols(20) should be(5)
      }
    }
    "be 4" when {
      "panel width = 100, padding = 0, space = 0, pcb width = 21" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.zero,
          gap = Rectangle(0, 0)
        )
        panel.cols(21) should be(4)
      }
      "panel width = 100, padding = 10, space = 0, pcb width = 20" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.fromPanelPadding(new PanelPadding(10, 0)),
          gap = Rectangle(0, 0)
        )
        panel.cols(20) should be(4)
      }
    }
    "be 3" when {
      "panel width = 100, padding = 0, space = 0, pcb width = 30" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.zero,
          gap = Rectangle(0, 0)
        )
        panel.cols(30) should be(3)
      }
      "panel width = 100, padding = 10, space = 10, pcb width = 20" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.fromPanelPadding(new PanelPadding(10, 0)),
          gap = Rectangle(10, 0)
        )
        panel.cols(20) should be(3)
      }
    }
  }

  "Number of rows" should {
    "be 5" when {
      "panel height = 100, padding = 0, space = 0, pcb height = 20" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.zero,
          gap = Rectangle(0, 0)
        )
        panel.rows(20) should be(5)
      }
    }
    "be 4" when {
      "panel height = 100, padding = 0, space = 0, pcb height = 21" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.zero,
          gap = Rectangle(0, 0)
        )
        panel.rows(21) should be(4)
      }
      "panel height = 100, padding = 10, space = 0, pcb height = 20" in {
        val panel = PanelBound(
          w = 100,
          h = 100,
          padding = DistributionPanelPadding.fromPanelPadding(new PanelPadding(0, 10)),
          gap = Rectangle(0, 0)
        )
        panel.rows(20) should be(4)
      }
    }
    "be 3" when {
      "panel height = 100, padding = 0, space = 0, pcb height = 30" in {
        val panel = PanelBound(100, 100, DistributionPanelPadding.zero, Rectangle(0, 0))
        panel.rows(30) should be(3)
      }
      "panel height = 100, padding = 10, space = 10, pcb height = 20" in {
        val panel =
          PanelBound(
            w = 100,
            h = 100,
            padding = DistributionPanelPadding.fromPanelPadding(new PanelPadding(0, 10)),
            gap = Rectangle(0, 10)
          )
        panel.rows(20) should be(3)
      }
    }
  }
}
