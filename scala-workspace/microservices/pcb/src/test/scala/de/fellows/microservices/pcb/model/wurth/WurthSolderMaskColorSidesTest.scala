package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.SoldermaskColor.White
import de.fellows.microservices.pcb.model.pcb.props.{SoldermaskColor => PcbSoldermaskColor}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WurthSolderMaskColorSidesTest extends AnyFlatSpec with should.Matchers {

  val color = PcbSoldermaskColor(de.fellows.ems.pcb.api.specification.SoldermaskColor.Green)
  "SoldermaskColor" should "be green" in {
    WurthSolderMaskColor.converter(color) shouldBe Some(SolderMaskColorGreen)
  }
  it should "be error if the color is not supported" in {
    WurthSolderMaskColor.converter(color.copy(value = White)) shouldBe None
  }
}
