package de.fellows.microservices.pcb

import de.fellows.ems.pcb.api.PCBV2Layer
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi
import de.fellows.utils.UUIDUtils
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec

import java.nio.charset.StandardCharsets

// This test is in the wrong package - yes. And we will need to change it often
// But the pcb-server contains the only tests that run part of CI and we want to make sure this doesn't break
class PCBV2LayerTest extends AnyWordSpec with should.Matchers {

  val properties: PCBV2SpecificationApi.PCBV2Properties = helper.properties

  "hashString" should {
    "work" in {
      val encoded = PCBV2Layer.hashString(properties)

      encoded should be(
        "NDpGUi00OjMwMC4zNDoxNTAuNTU6MS44OmdyZWVuOjpmYWxzZTo6MC4xNjo4NS4wOjAuMzpoYWwtcGItZnJlZTpCb3R0b206dHJ1ZTo6dHJ1ZTpmYWxzZTpmYWxzZTo6Ojo6cmlnaWQ"
      )

      val bytes   = UUIDUtils.decoder.decode(encoded)
      val decoded = new String(bytes, StandardCharsets.UTF_8)

      decoded should be(
        "4:FR-4:300.34:150.55:1.8:green::false::0.16:85.0:0.3:hal-pb-free:Bottom:true::true:false:false:::::rigid"
      )
    }
  }

  "converting" should {
    "work" in {
      val converted = PCBV2Layer.from(properties, Some(4))
      val restored = PCBV2Layer.to(converted)


      restored should be(properties)

    }
  }
}
