package de.fellows.microservices.pcb.model.stackrate

import de.fellows.ems.layerstack.api.LayerStacksAPI
import de.fellows.microservices.pcb.utils.StubLoader
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import org.scalatest.{EitherValues, OptionValues}

class PCBLayerSupportTest extends AnyFlatSpec with should.Matchers with EitherValues with OptionValues {
  "PCBLayerSupport" should "filter out deeper layers" in {
    val layerStacks = StubLoader.readJsonStubFile[LayerStacksAPI]("/stubs/layerstack.json").value

    val stack = layerStacks.selected.value.stacks.headOption.value
    stack.layers should have size 7

    val result = PCBLayerSupport.visibleLayers(layerStacks)
    val ordered = Seq(
      (None, Some("SilkscreenTop")),
      (Some("soldermask"), Some("SoldermaskTop")),
      (Some("foil"), Some("CopperTop")),
      (Some("prepreg"), None),
      (Some("prepreg"), None),
      (Some("foil"), Some("CopperBottom")),
      (Some("soldermask"), Some("SoldermaskBottom")),
      (None, Some("SilkscreenBottom"))
    )
    result.value should have size 8

    val layers = result.value.map { layer =>
      val mbLayerType = layer.definition.flatMap(_.layerType)
      val mbFileType  = layer.file.flatMap(_.fileType.map(_.fileType))

      (mbLayerType, mbFileType)
    }

    layers should contain theSameElementsInOrderAs ordered
  }

  it should "handle core layers correctly" in {
    val layerStacks = StubLoader.readJsonStubFile[LayerStacksAPI]("/layerstack-core.json").value

    val result = PCBLayerSupport.visibleLayers(layerStacks)
    val ordered = Seq(
      (None, Some("SilkscreenTop")),
      (Some("soldermask"), Some("SoldermaskTop")),
      (Some("core"), Some("CopperTop")),
      (Some("core"), None),
      (Some("core"), Some("CopperBottom")),
      (Some("soldermask"), Some("SoldermaskBottom")),
      (None, Some("SilkscreenBottom"))
    )
    result.value should have size 7

    val layers = result.value.map { layer =>
      val mbLayerType = layer.definition.flatMap(_.layerType)
      val mbFileType  = layer.file.flatMap(_.fileType.map(_.fileType))

      (mbLayerType, mbFileType)
    }

    layers should contain theSameElementsInOrderAs ordered
  }

  it should "split core layers" in {
    val layerStacks = StubLoader.readJsonStubFile[LayerStacksAPI]("/layerstack-core.json").value
    val stack       = layerStacks.selected.value.stacks.headOption.value

    val layers = PCBLayerSupport.getPCBLayerList(stack)

    val pcbLayers = layers.map { layer =>
      val mbLayerType = layer.definition.flatMap(_.layerType)
      val mbFileType  = layer.file.flatMap(_.fileType.map(_.fileType))

      (mbLayerType, mbFileType)
    }

    val ordered = Seq(
      (Some("soldermask"), Some("SoldermaskTop")),
      (Some("core"), Some("CopperTop")),
      (Some("core"), None),
      (Some("core"), Some("CopperBottom")),
      (Some("soldermask"), Some("SoldermaskBottom"))
    )

    pcbLayers should contain theSameElementsInOrderAs ordered
  }
}
