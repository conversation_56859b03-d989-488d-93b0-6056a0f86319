package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.Side.{Both, Bottom, Top}
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskSide
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class WurthSolderMaskSideTest extends AnyFlatSpec with should.Matchers {

  val solderMaskSide = SoldermaskSide(None)
  "Soldermask side" should "be top+bottom if no side is given" in {
    WurthSolderMaskSide(solderMaskSide) shouldBe SolderMaskSidesTopBottom
  }
  it should "be bottom" in {
    WurthSolderMaskSide(solderMaskSide.copy(value = Bottom)) shouldBe SolderMaskSidesBottom
  }
  it should "be top" in {
    WurthSolderMaskSide(solderMaskSide.copy(value = Top)) shouldBe SolderMaskSidesTop
  }
  it should "be TopBottom" in {
    WurthSolderMaskSide(solderMaskSide.copy(value = Both)) shouldBe SolderMaskSidesTopBottom
  }
  it should "be None" in {
    WurthSolderMaskSide(
      solderMaskSide.copy(value = de.fellows.ems.pcb.api.specification.Side.None)
    ) shouldBe SolderMaskSidesNone
  }
}
