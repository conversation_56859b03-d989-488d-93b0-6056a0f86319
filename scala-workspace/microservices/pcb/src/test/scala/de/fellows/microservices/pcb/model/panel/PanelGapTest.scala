package de.fellows.microservices.pcb.model.panel

import org.scalatest.EitherValues
import org.scalatest.matchers.should
import org.scalatest.wordspec.AnyWordSpec
import de.fellows.luminovo.panel.Depanelization

class PanelGapTest extends AnyWordSpec with should.Matchers with EitherValues {
  private val panelPreferences = PanelPreferences(
    minWidth = 30,
    minHeight = 15,
    maxWidth = 260,
    maxHeight = 245,
    maxPCBs = Some(10),
    padding = new PanelPadding(5, 5),
    spacing = new PanelGap(5, 5),
    depanelization = Depanelization.VCut
  )

  "Panel Distribution" should {
    "use correct gaps (same)" in {
      val pcbSize      = new PcbSize(68.80, 19.80)
      val distribution = DistributionAlgorithm.calculate(10, pcbSize, panelPreferences).value

      distribution.gap.x should be(5)
      distribution.gap.y should be(5)

      distribution.padding.topInMm should be(5)
      distribution.padding.rightInMm should be(5)
      distribution.padding.bottomInMm should be(5)
      distribution.padding.leftInMm should be(5)
    }

    "use correct spacing (different)" in {
      val settings = panelPreferences.copy(
        padding = new PanelPadding(8, 11),
        spacing = new PanelGap(3, 9)
      )
      val pcbSize      = new PcbSize(68.80, 19.80)
      val distribution = DistributionAlgorithm.calculate(10, pcbSize, settings).value

      distribution.gap.x should be(3)
      distribution.gap.y should be(9)

      distribution.padding.topInMm should be(11)
      distribution.padding.rightInMm should be(8)
      distribution.padding.bottomInMm should be(11)
      distribution.padding.leftInMm should be(8)
    }
  }
}
