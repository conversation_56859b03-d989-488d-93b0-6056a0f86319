package de.fellows.microservices.pcb.model.stackrate

import com.typesafe.config.{ConfigFactory, ConfigValueFactory}
import de.fellows.microservices.pcb.helper
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.{
  PCBHoleListGraphic,
  PCBLayerGraphic,
  PCBOutlineGraphic
}
import org.scalatest.flatspec.AsyncFlatSpec
import org.scalatest.matchers.should
import org.scalatest.{EitherValues, OptionValues}

import java.nio.file.Path
import scala.jdk.CollectionConverters._

class StackRateAPITest extends AsyncFlatSpec with should.Matchers with EitherValues with OptionValues {

  "GetZip" should "return a stubbed zip" in {
    val config        = ConfigFactory.empty().withValue("stackrate.use_zip_stub", ConfigValueFactory.fromAnyRef(true))
    val api           = StubStackRateAPI(config)
    val forComparison = getClass.getResource("/stubs/gerberFiles.zip").toURI

    api.getZip(helper.uuid, "token").map { zip =>
      zip.toOption match {
        case None       => fail("Could not get zip")
        case Some(file) => filesCompareByByte(file.toPath, Path.of(forComparison)) should be(true)
      }
    }
  }

  "GetPCBGraphics" should "return a stubbed list of SVG graphics" in {
    val config = ConfigFactory.parseMap(
      Map(
        "stackrate.use_pcb_stub" -> true,
        "stackrate.use_svg_stub" -> true
      ).asJava
    )
    val api = StubStackRateAPI(config)

    api.getPCBGraphics(helper.uuid, "token").map { resp =>
      val graphics   = resp.value
      val outlines   = graphics.filter(_.isInstanceOf[PCBOutlineGraphic])
      val layers     = graphics.filter(_.isInstanceOf[PCBLayerGraphic])
      val drillHoles = graphics.filter(_.isInstanceOf[PCBHoleListGraphic])

      outlines should have size 1
      layers should have size 8
      drillHoles should have size 1
    }
  }

  import java.io.{BufferedInputStream, FileInputStream}

  def filesCompareByByte(path1: Path, path2: Path): Boolean = {
    val fis1           = new BufferedInputStream(new FileInputStream(path1.toFile))
    val fis2           = new BufferedInputStream(new FileInputStream(path2.toFile))
    var value1, value2 = 0;
    do { // since we're buffered, read() isn't expensive
      value1 = fis1.read
      value2 = fis2.read
      if (value1 != value2) return false
    } while (
      value1 >= 0
    )
    true
  }
}
