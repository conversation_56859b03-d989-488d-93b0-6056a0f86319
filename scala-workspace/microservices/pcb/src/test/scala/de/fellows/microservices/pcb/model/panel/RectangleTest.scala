package de.fellows.microservices.pcb.model.panel

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class RectangleTest extends AnyFlatSpec with should.Matchers {

  private val rectangle = Rectangle(10, 10)
  "Rectangle (10,10)" should "fit a rectangle (5,5)" in {
    val rectangleToFit = Rectangle(5, 5)
    rectangle.fit(rectangleToFit) shouldBe true
  }
  it should "not fit a rectangle (15,5)" in {
    val rectangleToFit = Rectangle(15, 5)
    rectangle.fit(rectangleToFit) shouldBe false
  }
  it should "not fit a rectangle (5,15)" in {
    val rectangleToFit = Rectangle(5, 15)
    rectangle.fit(rectangleToFit) shouldBe false
  }

  "After adjusting up, rectangle (10,10)" should "be (10,10) with lower bound (5,5)" in {
    val lowerBound = Rectangle(5, 5)
    rectangle.adjustUp(lowerBound) shouldBe rectangle
  }
  it should "be (20,10) for lower bound (20,5)" in {
    val lowerBound = Rectangle(20, 5)
    rectangle.adjustUp(lowerBound) shouldBe Rectangle(20, 10)
  }
  it should "be (10,20) for lower bound (5,20)" in {
    val lowerBound = Rectangle(5, 20)
    rectangle.adjustUp(lowerBound) shouldBe Rectangle(10, 20)
  }
}
