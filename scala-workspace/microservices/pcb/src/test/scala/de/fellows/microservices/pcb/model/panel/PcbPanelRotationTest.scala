package de.fellows.microservices.pcb.model.panel

import de.fellows.luminovo.panel.{LuminovoPadding, PanelDetails}
import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should

class PcbPanelRotationTest extends AnyFlatSpec with should.Matchers with EitherValues {

  "Pcb" should "be rotated to fit into panel preferences" in {
    val amount  = 100
    val pcbSize = new PcbSize(300, 80)

    val constraints = PanelPreferences.Empty.copy(
      minWidth = 50,
      minHeight = 50,
      maxWidth = 265,
      maxHeight = 465,
      maxPCBs = None,
      padding = new PanelPadding(2, 2),
      spacing = new PanelGap(2, 2)
    )
    val distribution = DistributionAlgorithm.calculate(
      amount = amount,
      pcbSize = pcbSize,
      preferences = constraints
    ).value

    distribution.mesh should be(PcbMesh(1, 2))
    distribution.panel should be(new PanelDimensions(166, 304))
    distribution.pcbIsRotated should be(true)
    distribution.padding should be(DistributionPanelPadding(2, 2, 2, 2))

    val panelDetails = PanelDetails(
      id = None,
      rowCount = distribution.mesh.rows,
      columnCount = distribution.mesh.columns,
      horizontalSpacingInMm = distribution.gap.x,
      verticalSpacingInMm = distribution.gap.y,
      minMillingDistanceInMm = distribution.minMillingDistanceInMm,
      padding = LuminovoPadding(
        topInMm = distribution.padding.topInMm,
        rightInMm = distribution.padding.rightInMm,
        bottomInMm = distribution.padding.bottomInMm,
        leftInMm = distribution.padding.leftInMm
      ),
      depanelization = distribution.depanelization,
      pcbIsRotated = distribution.pcbIsRotated
    )

    val distributionFromPanelDetails = DistributionAlgorithm.calculateDistributionFromDetails(
      amount = amount,
      pcbSize = pcbSize,
      panelDetails = panelDetails,
      panelConstraints = None
    ).value

    distributionFromPanelDetails.mesh should be(PcbMesh(1, 2))
    distributionFromPanelDetails.panel should be(new PanelDimensions(166, 304))
    distributionFromPanelDetails.pcbIsRotated should be(true)
    distributionFromPanelDetails.padding should be(DistributionPanelPadding(2, 2, 2, 2))
  }

}
