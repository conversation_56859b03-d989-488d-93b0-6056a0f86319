package de.fellows.microservices.pcb.model.lq

import de.fellows.microservices.pcb.PcbServerError
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerLocation}
import de.fellows.utils.model.ShareId

import java.util.UUID

final case class ManufacturerApiResponse(
    api: ManufacturerApiWithInformation,
    manufacturer: Manufacturer,
    location: ManufacturerLocation,
    response: Either[PcbServerError, ManufacturerQuote]
)

final case class ManufacturerQuote(
    quoteResponses: Seq[QuoteResponse]
)

/** Response for a quote request
  */
final case class QuoteResponse(
    offerResponse: OfferResponse,
    quoteId: String,
    url: Option[String],
    calculatedPanelInfo: CalculatedPanelInfo,
    sourcingScenarioId: SourcingScenarioId,
    existingOfferId: Option[UUID],
    sharedPcbId: Option[ShareId]
)
