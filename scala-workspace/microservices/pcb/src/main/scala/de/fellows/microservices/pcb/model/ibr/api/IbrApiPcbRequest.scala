package de.fellows.microservices.pcb.model.ibr.api

import com.osinka.i18n.Lang
import de.fellows.ems.pcb.api.specification.IPC600Class
import de.fellows.microservices.pcb.PropertyErrors
import de.fellows.microservices.pcb.model.ibr.IbrCapability
import de.fellows.microservices.pcb.model.ibr.props.{
  IBRSides,
  IbrBoolean,
  IbrEtest,
  IbrMaterial,
  IbrNumberOfLayers,
  IbrSilkscreenColor,
  IbrSoldermaskColor,
  IbrSurfaceFinish
}
import de.fellows.microservices.pcb.model.lq.LeadTime
import de.fellows.microservices.pcb.model.pcb.props.{IPCA600Class, InnerNone, OuterNone}
import de.fellows.microservices.pcb.model.pcb.{Convert, PCB, PCBProperties}
import de.fellows.utils.logging.StackrateLogging
import play.api.libs.json.{Format, <PERSON>son}
import zio.prelude.Validation

import java.time.LocalDate
import java.time.format.DateTimeFormatter

case class IbrApiPcbRequest(
    api_key: String,
    data: IbrApiBasicRequestData
)

object IbrApiPcbRequest extends StackrateLogging {

  implicit val format: Format[IbrApiPcbRequest] = Json.format[IbrApiPcbRequest]

  def validateAndConvert(
      pcb: PCB,
      quantity: Int,
      leadTimes: Seq[LeadTime],
      credentials: IbrApiCredentialsCheck
  )(implicit lang: Lang): Either[PropertyErrors, IbrApiRequestData] =
    IbrCapability.capability.validate(pcb.properties) match {
      case Nil =>
        convert(pcb, quantity, leadTimes, credentials)
      case errors =>
        Left(PropertyErrors(errors))
    }

  protected def convert(
      pcb: PCB,
      quantity: Int,
      leadTimes: Seq[LeadTime],
      credentials: IbrApiCredentialsCheck
  )(implicit lang: Lang): Either[PropertyErrors, IbrApiRequestData] = {
    val props = pcb.properties
    Validation.validateWith(
      Validation.succeed(credentials),
      Validation.succeed(pcb),
      Validation.succeed(quantity),
      Validation.succeed(leadTimes),
      Validation.succeed(props),
      Convert.convertRequired(props.layer.numberOfLayers, IbrNumberOfLayers.fromPcb),
      Convert.convertRequired(props.basic.surfaceFinish, IbrSurfaceFinish.converter),
      Convert.convertRequired(props.advanced.eTest, IbrEtest.convert),
      Validation.succeed(IBRSides.convertSides(props.basic.soldermaskSide.value)),
      Validation.succeed(IBRSides.convertSides(props.basic.silkscreenSide.value)),
      Convert.convertRequired(props.basic.silkscreenColor, IbrSilkscreenColor.converter),
      Validation.succeed(IbrBoolean.converter(props.advanced.edgeMetalization.value)),
      Validation.succeed(IbrMaterial.converter(props.layer.baseMaterial, props.layer.tgValue))
    )(createRequest).fold(
      errors => Left(PropertyErrors(errors)),
      Right.apply
    )
  }

  def buildThicknessString(props: PCBProperties): Option[String] = {
    val layers         = props.layer.numberOfLayers.value
    val outerThickness = props.layer.outerCopperThickness
    val innerThickness = props.layer.innerCopperThickness

    if (layers <= 2 && outerThickness != OuterNone) {
      Some(s"${outerThickness.value}")
    } else if (innerThickness != InnerNone) {
      Some(s"${outerThickness.value},${innerThickness.value}")
    } else {
      None
    }

  }

  def convertIPCClass(ipcA600Class: IPCA600Class): Option[Int] =
    ipcA600Class.value match {
      case IPC600Class.IPCNone  => None
      case IPC600Class.IPC1     => Some(1)
      case IPC600Class.IPC2     => Some(2)
      case IPC600Class.IPC2Plus => None
      case IPC600Class.IPC3     => Some(3)
      case IPC600Class.IPC3a    => None
    }

  private def createRequest(
      credentials: IbrApiCredentialsCheck,
      pcb: PCB,
      quantity: Int,
      leadTimes: Seq[LeadTime],
      props: PCBProperties,
      layers: IbrNumberOfLayers,
      finish: IbrSurfaceFinish,
      etest: Int,
      ls: (Int, Int),
      silkscreen: (Int, Int),
      silkscreenColor: IbrSilkscreenColor,
      edgePlating: Int,
      material: Option[IbrMaterial]
  ): IbrApiRequestData = {
    val expressDeliveryTime = IbrExpress.deliveryTime(leadTimes)

    IbrApiRequestData(
      expressQuantities = Some(Seq(quantity)),
      quantities = Seq(quantity),
      revision = None,
      customer = credentials.customer,
      customer_api_key = credentials.customer_api_key,
      projectName = pcb.id.toString,
      partid = Some(pcb.id.toString),
      applicationType = None,
      source = Some("luminovo"),
      origin = None,
      creatorName = None,
      creatorEmail = None,
      creationDate = Some(LocalDate.now().format(DateTimeFormatter.ISO_DATE)),
      expressdeliverytime = expressDeliveryTime,
      partName = None,
      lp = IbrApiPCB(
        Laenge = props.basic.boardHeight.value.doubleValue,
        Breite = props.basic.boardWidth.value.doubleValue,
        Lagen = layers.value,
        ULKanada = None,
        Stiffener = None,
        Fraesen = None,    // will be set later
        Nutzenzahl = None, // will be set later
        GoldArea = None,   // TODO
        AnzahlBohrungen = props.mechanical.phCount.value.map(_.intValue),
        PDTop = Some(silkscreen._1),
        PDBot = Some(silkscreen._2),
        BlindVias = props.mechanical.blindViaCount.value.map(_.intValue),
        Nutzentyp = None, // will be set later
        SprungRitzen = None,
        DickeEnddickeMax = None, // only relevant when we provide special stackups
        Bohrkonfiguration = None,
        Hartgold = pcb.properties.basic.hardGoldArea.value.map(_.doubleValue),
        ETestTestPunkte = None,
        ViaTyp = None, // TODO not sure how to map this.
        BuriedVias = props.mechanical.buriedViaCount.value.map(_.intValue),
        ZAchsenFraesen = None,
        Finish = Some(finish.value),
        BreiteEinzel = None, // will be set later
        RoHSsign = None,
        LSTop = Some(ls._1),
        LSBot = Some(ls._2),
        LPTyp = None, // TODO not sure how to map this.
        DateCode = None,
        minTrack = None, // Do not fill this. According to IBR, only minAbstaende is needed
        SenkungenBot = None,
        ULsign = None,
        ZAchsenFraesenBot = None,
        ULType = None,
        Ritzen = None,
        minAbstaende =
          Seq(
            props.layer.minOuterLayerStructure.value,
            props.layer.minInnerLayerStructure.value
          ).flatten.minOption.map(
            _.doubleValue
          ), // acccording to IBR, we should use the min structure for this field instead of the actual clearance
        Kantenverzinnung = Some(edgePlating),
        Material = material.map(_.value),
        CarbonTop = None,
        CarbonBot = None,
        minRestring = None,
        Spulentechnik = None,
        ZAchsenFraesenTiefe = None,
        Seriennummern = None,
        Nutzenaufbau = None,
        ViaTypFüllung = None,
        IPCKlasse = convertIPCClass(props.advanced.ipcA600Class),
        Pressfit = None,
        Bemerkung = None,
        DickeEnddicke = None, // only relevant when we provide special stackups
        AnzahlBohrDurchmesser = Some(
          pcb.properties.mechanical.phToolCount.value.getOrElse(0) +
            pcb.properties.mechanical.nphToolCount.value.getOrElse(0)
        ),
        MaterialHalogenfrei = None,
        PeelingTop = None,
        PeelingBot = None,
        PeelingArt = None,
        PadsTop = None,
        PadsBot = None,
        Etest = Some(etest),
        Fasung = None,
        Komplexitaet = None,
        Senkungen = None,
        LSdoppelt = None,
        Dicke = Some(pcb.properties.layer.finalThickness.value.doubleValue),
        Name = pcb.name,
        DKSchlitze = None,
        MaterialCTIWert = None,
        Kupfer = buildThicknessString(props),
        Nutzenkeinexout = None,
        BemerkungPruefbericht = None,
        PDFarbe = Some(silkscreenColor.value),
        NutzenaufbauText = None,
        LSFarbe = IbrSoldermaskColor.converter(pcb.properties.basic.soldermaskColor).map(_.value),
        FraesDurchmesser = None,
        Lagenaufbau = None,
        minBohrung = Seq(
          props.mechanical.phMinDiameter.value.map(_.doubleValue),
          props.mechanical.nphMinDiameter.value.map(_.doubleValue)
        ).flatten.minOption,
        LaengeEinzel = None
      )
    )
  }
}
