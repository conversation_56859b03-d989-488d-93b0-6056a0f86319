package de.fellows.microservices.pcb.model.pcb.capability

import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.model.pcb.PropertyError
import de.fellows.microservices.pcb.model.pcb.props.{PCBOptionalProperty, PCBRequiredProperty}
import zio.prelude.Validation

trait BoardCapability {

  protected def validate[K <: PCBOptionalProperty[_]](
      t: NonEmptyCapability[K, _],
      property: K
  )(implicit lang: Lang): Validation[PropertyError, K] =
    t.validate(property)

  protected def validate[K <: PCBRequiredProperty[_]](
      t: NonEmptyRequiredCapability[K, _],
      property: K
  )(implicit lang: Lang): Validation[PropertyError, K] =
    t.validate(property)
}

object BoardCapability {
  implicit class AlternativeCapability(val alternatives: Seq[Capability[_]]) extends AnyVal {
    def getAlternative[T <: Capability[_]](capability: T): T =
      alternatives.find(_.typeTag == capability.typeTag).map(_.asInstanceOf[T]).getOrElse(capability)
  }
}
