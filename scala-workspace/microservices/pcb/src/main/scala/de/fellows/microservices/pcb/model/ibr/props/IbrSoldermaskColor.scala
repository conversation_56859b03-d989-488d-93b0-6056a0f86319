package de.fellows.microservices.pcb.model.ibr.props

import de.fellows.ems.pcb.api.specification
import de.fellows.microservices.pcb.model.pcb.props

sealed trait IbrSoldermaskColor {
  val value: String
  val toPCB: props.SoldermaskColor
}

object IbrSoldermaskColor {
  val ALL = Set(
    weiss,
    gruen,
    rot,
    schwarz,
    gelb,
    blau,
    lila,
    mattgruen,
    glanzgruen
  )

  def converter(value: props.SoldermaskColor): Option[IbrSoldermaskColor] =
    ALL.find(_.toPCB == value)

  private case object gruen extends IbrSoldermaskColor {
    override val value: String = "grün"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.Green)
  }

  private case object weiss extends IbrSoldermaskColor {
    override val value: String = "weiss"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.White)
  }

  private case object rot extends IbrSoldermaskColor {
    override val value: String = "rot"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.Red)
  }

  private case object schwarz extends IbrSoldermaskColor {
    override val value: String = "schwarz"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.Black)
  }

  private case object gelb extends IbrSoldermaskColor {
    override val value: String = "gelb"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.Yellow)
  }

  private case object blau extends IbrSoldermaskColor {
    override val value: String = "blau"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.Blue)
  }

  //private case object transparent extends IbrSoldermaskColor {
  //  override val value: String                = "transparent"
  //  override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.)
  //}
  //private case object orange extends IbrSoldermaskColor {
  //  override val value: String                = "orange"
  //  override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor)
  //}
  private case object lila extends IbrSoldermaskColor {
    override val value: String = "lila"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.Purple)
  }

  private case object mattgruen extends IbrSoldermaskColor {
    override val value: String = "mattgrün"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.GreenMatt)
  }

  //private case object mattweiss extends IbrSoldermaskColor {
  //  override val value: String                = "mattweiss"
  //  override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor)
  //}
  //private case object mattblau extends IbrSoldermaskColor {
  //  override val value: String                = "mattblau"
  //  override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor)
  //}
  //private case object mattschwarz extends IbrSoldermaskColor {
  //  override val value: String                = "mattschwarz"
  //  override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor)
  //}
  //private case object mattrot extends IbrSoldermaskColor {
  //  override val value: String                = "mattrot"
  //  override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor)
  //}
  private case object glanzgruen extends IbrSoldermaskColor {
    override val value: String = "glanzgrün"
    override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor.GreenGlossy)
  }
  //private case object flex-coverlay extends IbrSoldermaskColor {
  //  override val value: String                = "flex-coverlay"
  //  override val toPCB: props.SoldermaskColor = props.SoldermaskColor(specification.SoldermaskColor)
  //}

}
