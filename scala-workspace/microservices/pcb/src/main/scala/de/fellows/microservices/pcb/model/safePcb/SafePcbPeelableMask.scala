package de.fellows.microservices.pcb.model.safePcb

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.Side.{ Bottom, Top }
import de.fellows.microservices.pcb.model.pcb.props.PeelableMask

trait SafePcbPeelableMask {
  val value: String

}
private case object SafePcbPeelableMaskTopBottom extends SafePcbPeelableMask {
  override val value: String = "TOP.BOT"
}
private case object SafePcbPeelableMaskTop extends SafePcbPeelableMask {
  override val value: String = "TOP"
}
private case object SafePcbPeelableMaskBottom extends SafePcbPeelableMask {
  override val value: String = "BOT"
}
private case object SafePcbPeelableMaskNone extends SafePcbPeelableMask {
  override val value: String = "WITHOUT"
}

private object SafePcbPeelableMask {

  def apply(value: PeelableMask): SafePcbPeelableMask =
    value.value match {
      case Side.None => SafePcbPeelableMaskNone
      case Top       => SafePcbPeelableMaskTop
      case Bottom    => SafePcbPeelableMaskBottom
      case _         => SafePcbPeelableMaskTopBottom
    }
}
