package de.fellows.microservices.pcb.model.lq

import enumeratum.{Enum, EnumEntry, PlayJsonEnum}

sealed trait SupplierPartType extends EnumEntry {}

object SupplierPartType extends Enum[SupplierPartType] with PlayJsonEnum[SupplierPartType] {
  val values = findValues

  case object OffTheShelf extends SupplierPartType
  case object Pcb         extends SupplierPartType
  case object Custom      extends SupplierPartType
  case object Assembly    extends SupplierPartType
}
