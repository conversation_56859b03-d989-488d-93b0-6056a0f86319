package de.fellows.microservices.pcb.model.safePcb

import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.luminovo.panel.Depanelization
import de.fellows.microservices.pcb.model.panel.{
  NumberOfPanels,
  PanelDimensions,
  PanelDistribution,
  PcbMesh,
  RequestedPcbs,
  WastedArea
}
import de.fellows.microservices.pcb.model.pcb.{Convert, PCB, PropertyError}
import de.fellows.microservices.pcb.model.pcb.props.{
  BlindVias,
  BoardHeight,
  BoardWidth,
  BuriedVias,
  EdgeMetalization,
  ImpedanceTested,
  PressFit
}
import zio.prelude.Validation

/** Represents a PCB request for Safe-PCB API
  */
final case class SafePcbRequest(
    name: Option[String],
    width: BoardWidth,
    height: BoardHeight,
    ipcAClass: SafePcbIPCAClass,
    numberOfLayers: SafePcbNumberOfLayers,
    material: SafePcbMaterial,
    finalThickness: SafePcbFinalThickness,
    outerCopperThickness: SafePcbOuterCopperThickness,
    innerCopperThickness: SafePcbInnerCopperThickness,
    surfaceFinish: SafePcbSurfaceFinish,
    solderMaskSide: SafePcbSolderMaskSide,
    solderMaskColor: SafePcbSolderMaskColor,
    silkscreenSides: SafePcbSilkscreenSides,
    silkscreenColor: SafePcbSilkscreenColor,
    peelableMask: SafePcbPeelableMask,
    impedanceTested: ImpedanceTested,
    edgeMetalization: EdgeMetalization,
    pressFit: PressFit,
    blindVias: BlindVias,
    buriedVias: BuriedVias,
    calculatedPanelInfo: CalculatedPanelInfo,
    depanelization: SafePcbDepanelization
) {

  def withPanel(calculatedPanelInfo: CalculatedPanelInfo): SafePcbRequest =
    copy(
      calculatedPanelInfo = calculatedPanelInfo,
      depanelization = SafePcbDepanelization(calculatedPanelInfo.depanelization)
    )

  def toXML: scala.xml.Elem = {
    val copperExternal = numberOfLayers match {
      case OneLayer | TwoLayers => s"${outerCopperThickness.value}m"
      case _                    => s"MUL_${outerCopperThickness.value}m"
    }
    val copperInternal = numberOfLayers match {
      case OneLayer | TwoLayers => "WITHOUT"
      case _                    => s"${innerCopperThickness.value}m"
    }
    val method = numberOfLayers match {
      case OneLayer | TwoLayers => "WITHOUT"
      case _                    => s"GERBER"
    }
    val soldermaskColorTop = solderMaskSide match {
      case SafePcbSolderMaskSidesTopBottom | SafePcbSolderMaskSidesTop => solderMaskColor.value
      case _                                                           => SafePcbSolderMaskColorNone.value
    }
    val soldermaskColorBottom = solderMaskSide match {
      case SafePcbSolderMaskSidesTopBottom | SafePcbSolderMaskSidesBottom => solderMaskColor.value
      case _                                                              => SafePcbSolderMaskColorNone.value
    }
    val silkscreenColorCorrected = silkscreenSides match {
      case SafePcbSilkscreenSidesNone => SafePcbSilkscreenColorNone.value
      case _                          => silkscreenColor.value
    }
    val peelableMaskType = if (peelableMask == SafePcbPeelableMaskNone) "WITHOUT" else "MASK"
    val blindHoles = (blindVias.value, buriedVias.value) match {
      case (false, false) => "WITHOUT"
      case (true, false)  => "BLIND"
      case (false, true)  => "BURIED"
      case (true, true)   => "BLIND.BURIED"
    }

    val finalThicknessValue = s"${material.value}_${finalThickness.value}"

    <PCB>
        <BASE>
          <Family>RIGID</Family>
          <Class_IPC>{ipcAClass.value}</Class_IPC>
          <!-- Other values are EN45545_2 and EN9100. -->
          <!-- Please to contact Safe-PCB to get more information about the "Norms" -->
          <Norms>WITHOUT</Norms>
        </BASE>
        {panelXML}
        <RIGID>
          <Material>{material.value}</Material>
          <Layers>{numberOfLayers.value}</Layers>
          <Thickness>{finalThicknessValue}</Thickness>
          <Copper_External>{copperExternal}</Copper_External>
          <Copper_Internal>{copperInternal}</Copper_Internal>
          <Surface_Finish>{surfaceFinish.value}</Surface_Finish>
        </RIGID>
        <MARKING>
          <Color>{silkscreenColorCorrected}</Color>
          <Legend>{silkscreenSides.value}</Legend>
          <Rohs>GERBER</Rohs>
          <UL>WITHOUT</UL>
          <Supplier>WITHOUT</Supplier>
          <Etest>WITHOUT</Etest>
          <DATE_Position>GERBER</DATE_Position>
          <DATE_Format>GERBER</DATE_Format>
          <FREETEXT_Position>WITHOUT</FREETEXT_Position>
          <INCREMENTAL_Format>WITHOUT</INCREMENTAL_Format>
          <INCREMENTAL_Position>WITHOUT</INCREMENTAL_Position>
        </MARKING>
        <STACKUP>
          <!-- Define how Safe-PCB engineers will create the Stackup -->
          <Method>{method}</Method>
        </STACKUP>
        <SOLDERMASK>
          <Position>{solderMaskSide.value}</Position>
          <Color_TOP>{soldermaskColorTop}</Color_TOP>
          <Color_BOT>{soldermaskColorBottom}</Color_BOT>
        </SOLDERMASK>
        <PEELABLE>
          <Position>{peelableMask.value}</Position>
          <Type>{peelableMaskType}</Type>
        </PEELABLE>
        <OPTIONS>
          <Impedance_Control>{if (impedanceTested.value) "YES" else "NO"}</Impedance_Control>
          <Edge_Plating>{if (edgeMetalization.value) "YES" else "NO"}</Edge_Plating>
          <Press_Fit>{if (pressFit.value) "YES" else "NO"}</Press_Fit>
          <Blind_Holes>{blindHoles}</Blind_Holes>
        </OPTIONS>
      </PCB>
  }

  private def panelXML: scala.xml.Elem =
    calculatedPanelInfo match {
      case p: CalculatedPanelInfo.FromPanelDetails => fromPanelDetailsXML(p)
      case p: CalculatedPanelInfo.FromExisting     => fromExistingPanelXML(p)
    }

  private def fromPanelDetailsXML(panelInfo: CalculatedPanelInfo.FromPanelDetails): scala.xml.Elem = {
    val panel = panelInfo.panelDistribution
    <PANEL>
      <Panel_Mode>{SafePcbPanelMode.SafePcbPanelMode.value}</Panel_Mode>
      <Circuit_X>{width.value}</Circuit_X>
      <Circuit_Y>{height.value}</Circuit_Y>
      <Repeat_X>{panel.mesh.columns}</Repeat_X>
      <Repeat_Y>{panel.mesh.rows}</Repeat_Y>
      <Space_X>{panel.gap.x}</Space_X>
      <Space_Y>{panel.gap.y}</Space_Y>
      <Border_L>{panel.padding.leftInMm}</Border_L>
      <Border_R>{panel.padding.rightInMm}</Border_R>
      <Border_T>{panel.padding.topInMm}</Border_T>
      <Border_B>{panel.padding.bottomInMm}</Border_B>
      <Panel_X>{panel.panel.widthInMm}</Panel_X>
      <Panel_Y>{panel.panel.heightInMm}</Panel_Y>
      <Separation_XY>{depanelization.value}</Separation_XY>
      <!-- This is the number of different PCBs in gerber -->
      <Quantity_Circuit_Different>1</Quantity_Circuit_Different>
      <Quantity_Circuit_per_Panel>{panel.mesh.size}</Quantity_Circuit_per_Panel>
      <!-- Safe-PCB engineer check the customer panel profil to create panel -->
      <Panel_Profil>CUSTOMER</Panel_Profil>
    </PANEL>
  }

  private def fromExistingPanelXML(panelInfo: CalculatedPanelInfo.FromExisting): scala.xml.Elem =
    <PANEL>
      <Panel_Mode>{SafePcbPanelMode.SafePcbGerberPanelMode.value}</Panel_Mode>
      <Circuit_X>{width.value}</Circuit_X>
      <Circuit_Y>{height.value}</Circuit_Y>
      <Panel_X>{panelInfo.existing.panelWidth}</Panel_X>
      <Panel_Y>{panelInfo.existing.panelHeight}</Panel_Y>
      <Separation_XY>{depanelization.value}</Separation_XY>
      <!-- This is the number of different PCBs in gerber -->
      <Quantity_Circuit_Different>1</Quantity_Circuit_Different>
      <Quantity_Circuit_per_Panel>{panelInfo.existing.numberOfPcbs}</Quantity_Circuit_per_Panel>
      <!-- Safe-PCB engineer check the customer panel profil to create panel -->
      <Panel_Profil>CUSTOMER</Panel_Profil>
    </PANEL>
}

object SafePcbRequest {

  /** Converts a [[PCB]] to a [[SafePcbRequest]]. If the conversion fails, it returns a message, containing all errors,
    * separated by comma
    *
    * @param pcb         PCB
    * @param preferences EMS panel preferences
    */
  def validateAndConvert(pcb: PCB)(implicit
      lang: Lang
  ): Either[Seq[PropertyError], SafePcbRequest] =
    SafePcbCapability.capability.validate(pcb.properties) match {
      case Nil    => convert(pcb)
      case errors => Left(errors)
    }

  protected def convert(pcb: PCB)(implicit
      lang: Lang
  ): Either[Seq[PropertyError], SafePcbRequest] = {
    val props = pcb.properties
    Validation.validateWith(
      Validation.succeed(pcb),
      Convert.convertRequired(props.layer.numberOfLayers, SafePcbNumberOfLayers.fromPcb),
      Convert.convertRequired(props.layer.baseMaterial, SafePcbMaterial.fromPcb),
      Convert.convertRequired(props.layer.finalThickness, SafePcbFinalThickness.fromPcb),
      Convert.convertRequired(props.layer.outerCopperThickness, SafePcbOuterCopperThickness.fromPcb),
      Convert.convertRequired(props.layer.innerCopperThickness, SafePcbInnerCopperThickness.fromPcb),
      Convert.convertRequired(props.basic.surfaceFinish, SafePcbSurfaceFinish.fromPcb)
    )(createRequest).fold(
      errors => Left(errors),
      Right.apply
    )
  }

  private def createRequest(
      pcb: PCB,
      numberOfLayers: SafePcbNumberOfLayers,
      baseMaterial: SafePcbMaterial,
      finalThickness: SafePcbFinalThickness,
      outerCopperThickness: SafePcbOuterCopperThickness,
      innerCopperThickness: SafePcbInnerCopperThickness,
      surfaceFinish: SafePcbSurfaceFinish
  ) = {
    val props = pcb.properties
    val emptyDistribution = PanelDistribution.withoutPrecomputedGap(
      panel = new PanelDimensions(0, 0),
      waste = WastedArea.zero,
      pcbPerPanel = 0,
      mesh = PcbMesh(0, 0),
      items = Seq.empty,
      depanelization = Depanelization.Milling,
      minMillingDistanceInMm = 8,
      pcbIsRotated = false
    )

    SafePcbRequest(
      name = pcb.name,
      width = props.basic.boardWidth,
      height = props.basic.boardHeight,
      ipcAClass = SafePcbIPCAClass(props.advanced.ipcA600Class),
      numberOfLayers = numberOfLayers,
      material = baseMaterial,
      finalThickness = finalThickness,
      outerCopperThickness = outerCopperThickness,
      innerCopperThickness = innerCopperThickness,
      surfaceFinish = surfaceFinish,
      solderMaskSide = SafePcbSolderMaskSide(props.basic.soldermaskSide),
      solderMaskColor = SafePcbSolderMaskColor(props.basic.soldermaskColor),
      silkscreenSides = SafePcbSilkscreenSides(props.basic.silkscreenSide),
      silkscreenColor = SafePcbSilkscreenColor(props.basic.silkscreenColor),
      peelableMask = SafePcbPeelableMask(props.advanced.peelableMask),
      impedanceTested = props.advanced.impedanceTested,
      edgeMetalization = props.advanced.edgeMetalization,
      pressFit = props.advanced.pressFit,
      blindVias = props.mechanical.blindVias,
      buriedVias = props.mechanical.buriedVias,
      calculatedPanelInfo = CalculatedPanelInfo.FromPanelDetails(
        panelDistribution = emptyDistribution,
        requestedPcbs = RequestedPcbs(0),
        numberOfPanels = NumberOfPanels(0),
        depanelization = emptyDistribution.depanelization
      ),
      depanelization = SafePcbVCut
    )
  }
}
