package de.fellows.microservices.pcb.model.wurth

/** Name of param in Wurth API: screen_printing
  */
sealed trait WurthFullyCoatedSilkscreen {
  val value: Int
}
private case object FullyCoatedSilkscreenNone extends WurthFullyCoatedSilkscreen {
  override val value: Int = 0
}
private case object FullyCoatedSilkscreenTop extends WurthFullyCoatedSilkscreen {
  override val value: Int = 1
}
private case object FullyCoatedSilkscreenBottom extends WurthFullyCoatedSilkscreen {
  override val value: Int = 2
}
private case object FullyCoatedSilkscreenTopBottom extends WurthFullyCoatedSilkscreen {
  override val value: Int = 3
}

private object WurthFullyCoatedSilkscreen {

  def fromWurth(value: Int): WurthFullyCoatedSilkscreen = value match {
    case 0 => FullyCoatedSilkscreenNone
    case 1 => FullyCoatedSilkscreenTop
    case 2 => FullyCoatedSilkscreenBottom
    case 3 => FullyCoatedSilkscreenTopBottom
    case _ => throw new IllegalArgumentException(s"Unknown value for fully coated silkscreen: $value")
  }
}
