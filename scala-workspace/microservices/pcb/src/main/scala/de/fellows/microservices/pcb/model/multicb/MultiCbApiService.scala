package de.fellows.microservices.pcb.model.multicb

import cats.data.EitherT
import cats.syntax.traverse._
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import de.fellows.microservices.pcb._
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.panel.EmsPreferences
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerApi, PCB}
import de.fellows.microservices.pcb.model.{ApiService, Offer, RequestValidation, SingleManufacturerApiService}
import de.fellows.utils.CurrencyCode
import io.opentelemetry.extension.annotations.WithSpan
import play.api.Logging
import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, <PERSON>son}
import sttp.client3.playJson.SttpPlayJsonApi
import sttp.client3.{basicRequest, multipart, UriContext}

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class MultiCbApiService(
    host: String,
    asyncBackend: AsyncBackend
) extends SingleManufacturerApiService
    with Logging with SttpHelper with SttpPlayJsonApi {

  override val api: ManufacturerApi = ManufacturerApi.MultiCB

  override def checkCredentials(
      credentials: Credentials,
      tenant: String
  )(implicit ec: ExecutionContext): Future[Either[String, Boolean]] = {
    logger.info(s"Checking MultiCB credentials")
    basicRequest
      .post(uri"$host/api/price")
      .auth.basic(credentials.username, credentials.password)
      .response(asJson[JsValue])
      .body(Json.toJson(MultiCbPcbRequest.CredentialCheckRequest))
      .send(asyncBackend)
      .map { response =>
        logger.info(s"MultiCB credential check response: ${response}")
        response.body match {
          case Left(error) => Left("Invalid credentials.")
          case Right(_)    => Right(true)
        }
      }
  }

  @WithSpan
  override def doMakeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      scenarios: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Option[Credentials],
      requestValidations: Map[UUID, Seq[model.RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    (for {
      credentials <- EitherT.fromOption[Future](
        credentials,
        CredentialsMissingError("Credentials missing for MultiCB API")
      )

      _ <- EitherT.fromEither[Future](
        ApiService.getSupplierCapabilityErrors(manufacturer, requestValidations)
      )

      manufacturerLocation <- EitherT.fromOption[Future](
        manufacturer.locations.headOption,
        ServerError(s"No location found for manufacturer ${manufacturer.name}")
      )

      requests <- EitherT.fromEither[Future](
        fromPcbToMultiCB(
          tenant = tenant,
          pcb = pcb,
          scenarioRequestsWithPanels = scenarios,
          emsPreferences = emsPreferences
        )
      )

      quotes <- EitherT {
        ApiService.makeMultipleSingleQuotes(manufacturer, requests) {
          case (pcbRequest) =>
            val quantity = pcbRequest.calculatedPanelInfo.numberOfPanels.value
            (for {
              priceResponses <-
                EitherT(makeAllPriceRequestsForPanel(pcbRequest, credentials, quantity))

              quote <- EitherT(
                makeQuoteRequestForPanel(
                  request = pcbRequest,
                  priceResponses = priceResponses,
                  credentials = credentials,
                  quantity = quantity
                )
              )
            } yield {

              val offerResponse = OfferResponse(
                api = ManufacturerApiWithInformation(api),
                manufacturer = manufacturer,
                location = manufacturerLocation,
                quantity = quantity,
                offers = priceResponses.map { resp =>
                  Offer(
                    price = resp.totalPrice,
                    productionDays = resp.productionTime,
                    currency = Some(CurrencyCode.EUR),
                    oneTimeCosts = None
                  )
                },
                priceType = PriceType.ContractPrice
              )

              val resp = QuoteResponse(
                offerResponse = offerResponse,
                quoteId = quote.orderId,
                url = Some(s"$host${quote.id}"),
                calculatedPanelInfo = pcbRequest.calculatedPanelInfo,
                existingOfferId = None,
                sourcingScenarioId = pcbRequest.sourcingScenarioId,
                sharedPcbId = None
              )

              Seq(resp)
            }).value
        }
      }
    } yield quotes).value

  private def makeAllPriceRequestsForPanel(
      request: MultiCbRequest,
      credentials: Credentials,
      quantity: Int
  )(implicit
      ec: ExecutionContext
  ): Future[Either[PcbServerError, Vector[MultiCbPriceResponse]]] =
    makeAllPriceRequest(
      request = request,
      credentials = credentials,
      quantity = quantity
    )

  private def makeAllPriceRequest(
      request: MultiCbRequest,
      credentials: Credentials,
      quantity: Int
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Vector[MultiCbPriceResponse]]] = {

    val pcbRequestWithLeadTime = request
      .pcb
      .withLeadTime(MultiCbApiService.leadTimeLimit)
      .withQuantity(quantity)

    val requestBody = Json.toJson(pcbRequestWithLeadTime)

    logger.info(s"Sending MultiCB price request ${requestBody}")
    basicRequest
      .post(uri"$host/api/price/all")
      .auth.basic(credentials.username, credentials.password)
      .response(asJson[JsValue])
      .body(requestBody)
      .send(asyncBackend)
      .map { response =>
        val responseStatusCode = response.code
        handleJsonResponse(response, "MultiCB") { response =>
          logger.info(s"Received MultiCB price response ${response}")
          Try(response.as[Vector[MultiCbPriceResponse]]) match {
            case Success(value) =>
              Right(value)
            case Failure(exception) =>
              Left(ThirdPartyError("Response is not deserializable", responseStatusCode))
          }
        }
      }
  }

  private def makeQuoteRequestForPanel(
      request: MultiCbRequest,
      priceResponses: Vector[MultiCbPriceResponse],
      credentials: Credentials,
      quantity: Int
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, MultiCbQuoteResponse]] = {

    val leadTimes     = priceResponses.map(_.productionTime)
    val leadTimesTail = leadTimes.tail
    val restLeadTimes = Option.when(leadTimesTail.nonEmpty)(leadTimesTail)
    val quoteRequest = MultiCbQuoteRequest(
      Seq(
        MultiCbPcbQuote(
          pcb = request.pcb.copy(
            productionTime = leadTimes.head,
            quantity = quantity
          ),
          additionalWorkdays = restLeadTimes,
          additionalQuantities = None
        )
      )
    )

    logger.info(s"Sending MultiCB quote request ${Json.toJson(quoteRequest)}")
    // We need to send the request as multipart because it was copied from the order endpoint, which required file uploads
    // We don't need to send any files for the quote endpoint, though
    // The sendConfirmationEmail parameter just controls  whether MultiCB sends a confirmation email to the user or not
    basicRequest
      .post(uri"$host/api/quote?sendConfirmationEmail=${request.sendConfirmationEmail}")
      .auth.basic(credentials.username, credentials.password)
      .response(asJson[JsValue])
      .multipartBody(
        multipart("order", Json.toJson(quoteRequest))
      )
      .send(asyncBackend)
      .map { response =>
        val responseStatusCode = response.code
        handleJsonResponse(response, "MultiCB") { response =>
          logger.info(s"Received MultiCB quote response $response")
          Try(response.as[MultiCbQuoteResponse]) match {
            case Success(value) => Right(value)
            case Failure(exception) =>
              Try(response.as[MultiCbErrorResponse]) match {
                case Success(error) =>
                  logger.error(s"MultiCB returned an error response: ${error}")
                  Left(ThirdPartyError(s"StatusCode: '$responseStatusCode' Response: '$response'", responseStatusCode))
                case Failure(_) =>
                  Tracing.exceptionError(exception)
                  logger.error(
                    s"Couldn't deserialize a JSON response from MultiCB pcb response: ${exception.getMessage}"
                  )
                  Left(ThirdPartyError("Response is not deserializable", responseStatusCode))
              }
          }
        }
      }
  }

  override def doValidateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]] =
    Future.successful(Seq.empty)

  private def fromPcbToMultiCB(
      tenant: String,
      pcb: PCB,
      scenarioRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      emsPreferences: EmsPreferences
  ): Either[PcbServerError, Vector[MultiCbRequest]] =
    scenarioRequestsWithPanels
      .toVector
      .traverse { scenarioRequestWithPanel =>
        ApiService
          .calculateWorkingPanelInfo(
            pcb = pcb,
            quantity = scenarioRequestWithPanel.quantity,
            panelInfo = scenarioRequestWithPanel.panelInfo,
            panelPreferences = emsPreferences.panelPreferencesOrDefault,
            panelConstraints = None
          )
          .flatMap { calculatedPanelInfo =>
            MultiCbPcbRequest.convert(pcb, calculatedPanelInfo)
              .map { pcbRequest =>
                val request = MultiCbRequest(
                  pcb = pcbRequest,
                  calculatedPanelInfo = calculatedPanelInfo,
                  sourcingScenarioId = scenarioRequestWithPanel.sourcingScenarioId,
                  sendConfirmationEmail = false
                )

                request
              }
          }
      }

}

private case class MultiCbRequest(
    pcb: MultiCbPcbRequest,
    calculatedPanelInfo: CalculatedPanelInfo,
    sourcingScenarioId: SourcingScenarioId,
    sendConfirmationEmail: Boolean
)

object MultiCbApiService {
  val leadTimeLimit: Int = 80
}
