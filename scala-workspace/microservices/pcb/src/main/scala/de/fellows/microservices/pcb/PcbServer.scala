package de.fellows.microservices.pcb

import cats.syntax.either._
import akka.actor.ActorSystem
import akka.dispatch.ExecutionContexts
import akka.http.scaladsl.Http
import akka.http.scaladsl.common.{EntityStreamingSupport, JsonEntityStreamingSupport}
import akka.http.scaladsl.model.HttpHeader.ParsingResult
import akka.http.scaladsl.model.{Http<PERSON><PERSON><PERSON>, HttpHeader, MediaTypes, StatusCodes}
import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server._
import akka.http.scaladsl.server.directives.FileInfo
import akka.http.scaladsl.server.directives.MarshallingDirectives.{as, entity}
import akka.http.scaladsl.server.directives.RouteDirectives.complete
import akka.http.scaladsl.settings.RoutingSettings
import akka.http.scaladsl.unmarshalling.FromRequestUnmarshaller
import akka.stream.scaladsl.Source
import ch.megard.akka.http.cors.scaladsl.CorsDirectives._
import com.osinka.i18n.Lang
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.price.api.PriceService
import de.fellows.app.quotation
import de.fellows.app.quotation.{QuotationItemInfo, QuotationService}
import de.fellows.app.supplier.SupplierService
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.{CustomerPanelAPI, LuminovoCustomerPanel, PanelService}
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.luminovo.panel._
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.PanelErrorKind.errorMessage
import de.fellows.microservices.pcb.api.{PcbServerErrorResponse, UploadFileResponse}
import de.fellows.microservices.pcb.client.LagomServiceClient
import de.fellows.microservices.pcb.client.LagomServiceClient._
import de.fellows.microservices.pcb.client.luminovo.LuminovoClientImpl
import de.fellows.microservices.pcb.model.FileName
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.panel.{DistributionAlgorithm, PanelRenderer, PcbSize}
import de.fellows.microservices.pcb.model.pcb.{ManufacturerApi, PropertyErrorResponse}
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.GraphicError
import de.fellows.microservices.pcb.offers.OfferManager
import de.fellows.microservices.pcb.utils.PlayJsonSupport._
import de.fellows.microservices.pcb.utils.TracingExecutionContext
import de.fellows.utils.model.{PCBId, ShareId}
import de.fellows.utils.telemetry.Telemetry
import de.fellows.utils.{CurrencyCode, UUIDUtils}
import io.opentelemetry.api.trace.{Span, StatusCode}
import play.api.Logging
import play.api.libs.json.Json
import sttp.client3.logging.slf4j.Slf4jLoggingBackend
import sttp.client3.{HttpClientFutureBackend, HttpClientSyncBackend, Identity, SttpBackend}

import java.io.File
import java.net.http.HttpClient
import java.time.{Duration => JDuration}
import java.util.concurrent.ForkJoinPool
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.io.StdIn
import scala.util.control.NonFatal
import scala.util.{Failure, Success}

object PcbServer extends Logging {
  private var _healthy = false
  val AUTH_HEADER_NAME = "validated-token"

  implicit val executionContext: ExecutionContext =
    new TracingExecutionContext(ExecutionContexts.fromExecutor(new ForkJoinPool()))
  implicit val conf: Config = ConfigFactory.load()

  type AsyncBackend = SttpBackend[Future, Any]
  type SyncBackend  = SttpBackend[Identity, Any]

  def main(args: Array[String]): Unit = {

    implicit val system: ActorSystem = akka.actor.ActorSystem(
      name = "PcbServer",
      defaultExecutionContext = Some(new TracingExecutionContext(ExecutionContexts.fromExecutor(new ForkJoinPool())))
    )

    val httpClient = HttpClient
      .newBuilder()
      .connectTimeout(JDuration.ofMillis(30.seconds.toMillis))
      .build()
    val tracingHttpClient = Tracing.createTracingHttpClient(httpClient)

    implicit val asyncBackend: AsyncBackend =
      Slf4jLoggingBackend(HttpClientFutureBackend.usingClient(tracingHttpClient))
    implicit val syncBackend: SyncBackend =
      Slf4jLoggingBackend(HttpClientSyncBackend.usingClient(tracingHttpClient))

    val lagomServiceClient = new LagomServiceClient(
      HttpClientFutureBackend.usingClient(tracingHttpClient)
    )

    implicit val pricingService: PriceService         = lagomServiceClient.implement[PriceService]
    implicit val quotationService: QuotationService   = lagomServiceClient.implement[QuotationService]
    implicit val assemblyService: AssemblyService     = lagomServiceClient.implement[AssemblyService]
    implicit val panelService: PanelService           = lagomServiceClient.implement[PanelService]
    implicit val customerService: CustomerService     = lagomServiceClient.implement[CustomerService]
    implicit val userService: UserService             = lagomServiceClient.implement[UserService]
    implicit val supplierService: SupplierService     = lagomServiceClient.implement[SupplierService]
    implicit val pcbService: PCBService               = lagomServiceClient.implement[PCBService]
    implicit val layerstackService: LayerstackService = lagomServiceClient.implement[LayerstackService]

    implicit val stackRateApi: StackRateAPI = new StackRateAPI(
      config = system.settings.config,
      asyncBackend = asyncBackend,
      assemblyService = assemblyService,
      pcbService = pcbService,
      layerstackService = layerstackService
    )
    implicit val pcbServerConfig: PcbServerConfig   = PcbServerConfig(system.settings.config)
    implicit val luminovoClient: LuminovoClientImpl = new LuminovoClientImpl(pcbServerConfig)
    implicit val pcbManager: PcbManager             = new PcbManager(stackRateApi)

    implicit val offerManager: OfferManager = {
      // To be removed later - translations are being moved to the frontend
      // we don't do any translations based on param language anymore, it's always in english because the offer request comes
      // from the sourcing-worker, which does not know which language the customer wants.
      implicit val lang: Lang = Lang("en")
      new OfferManager(
        config = pcbServerConfig,
        stackrateApi = stackRateApi,
        syncBackend = syncBackend,
        asyncBackend = asyncBackend
      )
    }

    // Custom exception handler to save exceptions in traces, while keeping original behaviour
    val routingSettings = RoutingSettings(system)
    val defaultHandler  = ExceptionHandler.default(routingSettings)
    val exceptionHandler: ExceptionHandler =
      ExceptionHandler {
        case NonFatal(e) =>
          logger.error(s"Exception while processing request: ${e.getMessage}")
          val span = Span.current()
          span.setStatus(StatusCode.ERROR, e.getMessage)
          span.recordException(e)
          defaultHandler.apply(e)
      }

    val defaultRejectionHandler = RejectionHandler.default
    val rejectionHandler = RejectionHandler
      .newBuilder()
      .handle { rejection =>
        Tracing.exceptionError(RejectionError(rejection), s"Rejection while processing request: ${rejection.toString}")
        defaultRejectionHandler(Seq(rejection)).getOrElse {
          logger.error(s"Unhandled rejection: ${rejection.toString}")
          complete(StatusCodes.InternalServerError)
        }
      }
      .result()
      .withFallback(defaultRejectionHandler)

    val route: Route =
      concat(
        path("_health")(get {
          if (_healthy) {
            complete(StatusCodes.OK)
          } else {
            complete(StatusCodes.InternalServerError, "not healthy")
          }
        }),
        Route.seal(
          pathPrefix("api")(
            concat(
              path(Segment / "pcb" / PcbUUID / "batch-offers")(makeBatchOffers),
              path("pcb" / "manufacturers" / "check-auth")(checkAuth),
              path("pcb" / "panel" / "generate")(generatePanel),
              path("pcb" / "panel" / "render")(generatePanelImage),
              path("pcb" / "panel" / "positions")(generatePanelPositions),
              path("pcb" / PcbUUID / "download-pdf")(downloadPdf),
              path("pcb" / PcbUUID / "panel" / "download-pdf")(downloadPanelPdf),
              path("pcb" / PcbUUID / "download-zip")(downloadZip),
              path("pcb" / PcbUUID / "graphics")(getPCBGraphics),
              path("pcb" / PcbUUID / "upload-files")(uploadPcbFiles),
              path("pcb" / PcbUUID / "files")(updatePcbFileTypes),
              path("pcb" / ShareUUID / "offers" / Segment)(getPCBOffer)
            )
          )
        )(
          exceptionHandler = exceptionHandler,
          rejectionHandler = rejectionHandler
        ),
        cors() {
          complete(StatusCodes.OK)
        }
      )

    val future = Http()
      .newServerAt("0.0.0.0", system.settings.config.getInt("pcb.port"))
      .bind(route)

    future.onComplete {
      case Failure(exception) =>
        Tracing.exceptionError(exception, "Failed to bind server")
        PcbServer._healthy = false
      case Success(_) =>
        logger.info("HTTP Server started.")
        PcbServer._healthy = true
    }

    // When running with Mirrord, block the main thread (otherwise it exists right away)
    if (sys.env.get("MIRRORD_BLOCK").exists(_.toBoolean)) {
      StdIn.readLine()
    }
  }

  private def generatePanel = post {
    entity(as[PanelGenerationInput]) { input =>
      val distributionResult = DistributionAlgorithm.calculate(
        input.amount,
        input.pcbSize,
        input.preferences
      )
      distributionResult match {
        case Right(panelDistribution) =>
          val result = PanelGenerationResult(
            panel = panelDistribution.panel,
            waste = panelDistribution.waste,
            pcbPerPanel = panelDistribution.pcbPerPanel,
            mesh = panelDistribution.mesh,
            padding = panelDistribution.padding,
            gap = panelDistribution.gap,
            pcbIsRotated = panelDistribution.pcbIsRotated
          )

          complete(StatusCodes.OK, result)
        case Left(error) =>
          complete(StatusCodes.BadRequest, error.error)
      }
    }
  }

  private def generatePanelPositions =
    post {
      parameters("pcbWidth".as[Double], "pcbHeight".as[Double]) {
        case (pcbWidth, pcbHeight) =>
          entity(as[RenderPanelRequest]) { renderRequest =>
            val panelDetails = renderRequest.panel match {
              case p: PerSourcingScenario => Some(p.panelDetails)
              case p: PerPcb              => Some(p.panelDetails)
              case _: ExistingWrapper     => None
            }

            val result = panelDetails
              .toRight("Cannot render panel positions without panel details")
              .flatMap { panelDetails =>
                val pcbSize = new PcbSize(pcbWidth, pcbHeight)

                DistributionAlgorithm
                  .calculateDistributionFromDetails(
                    amount = panelDetails.quantity,
                    pcbSize = pcbSize,
                    panelDetails = panelDetails,
                    panelConstraints = None
                  )
                  .leftMap(e => PanelErrorKind.errorMessage(e.error))
              }

            result match {
              case Right(panel) => complete(StatusCodes.OK, panel)
              case Left(error)  => complete(StatusCodes.BadRequest, error)
            }
          }
      }
    }

  private def generatePanelImage =
    post {
      parameters("pcbWidth".as[Double], "pcbHeight".as[Double]) {
        case (pcbWidth, pcbHeight) =>
          entity(as[RenderPanelRequest]) { renderRequest =>
            val panelDetails = renderRequest.panel match {
              case p: PerSourcingScenario => Some(p.panelDetails)
              case p: PerPcb              => Some(p.panelDetails)
              case _: ExistingWrapper     => None
            }

            val result = panelDetails
              .toRight("Cannot render panel positions without panel details")
              .flatMap { panelDetails =>
                val pcbSize = new PcbSize(pcbWidth, pcbHeight)

                DistributionAlgorithm
                  .calculateDistributionFromDetails(
                    amount = panelDetails.quantity,
                    pcbSize = pcbSize,
                    panelDetails = panelDetails,
                    panelConstraints = None
                  )
                  .leftMap(e => PanelErrorKind.errorMessage(e.error))
              }

            result match {
              case Right(panel) =>
                val result = PanelRenderer.renderToFile(panel.items)

                withFileResponse(Some("panel"), "svg") { headers =>
                  complete(StatusCodes.OK, headers, HttpEntity.fromFile(MediaTypes.`image/svg+xml`, result))
                }

              case Left(error) => complete(StatusCodes.BadRequest, error)
            }
          }
      }
    }

  /** Returns `is_valid: true` if credentials are valid. Otherwise returns `{is_valid: false, error: "error message"}`
    */
  private def checkAuth(implicit offerManager: OfferManager) =
    postOfferHandleWithToken[Option[Credentials]] { (credentials, token, offerManager) =>
      credentials match {
        case Some(creds) =>
          val currentSpan = Span.current()
          currentSpan.updateName(s"HTTP POST Check Credentials ${creds.manufacturer}")

          onComplete(offerManager.checkCredentials(creds, token)) { result =>
            val response = result match {
              case Success(Right(_))    => Json.obj("is_valid" -> true)
              case Success(Left(error)) => Json.obj("is_valid" -> false, "error" -> error)
              case Failure(exception) =>
                val msg = s"Error while checking credentials for ${creds.manufacturer}"
                Tracing.exceptionError(exception, msg)
                Json.obj("is_valid" -> false, "error" -> msg)
            }

            complete(StatusCodes.OK, response)
          }
        case None => complete(Json.obj("is_valid" -> false, "error" -> "No credentials provided"))
      }
    }

  private def makeBatchOffers(tenant: String, pcbId: PCBId)(implicit offerManager: OfferManager) =
    withRequestTimeout(5.minutes) {
      postOfferHandle[BatchOfferRequest] { (request, manager) =>
        val currentSpan = Span.current()
        currentSpan.updateName(s"HTTP POST Make Batch Offers")
        currentSpan.setAttribute("pcb", pcbId.toString)
        currentSpan.setAttribute("api", request.api.api)
        currentSpan.setAttribute("tenant", request.tenant)

         onComplete(
          manager.makeBatchOffers(tenant, request)
        ) {
          case Success(Right(offers)) =>
            logger.info(s"Successfully created offers for ${request.api}: ${offers}")
            complete(StatusCodes.OK, offers)
          case Success(Left(error)) =>
            logger.error(s"Error while creating offers for ${request.api}: ${error}")
            processError(error)
          case Failure(exception) =>
            logger.error(s"Exception while creating offers for ${request.api}: ${exception}")
            processError(ExceptionError(s"Error getting PCB offers for pcb id=${request.pcbId}", exception))
        }
      }
    }

  private def downloadZip(pcbId: PCBId)(implicit pcbManager: PcbManager) =
    get {
      headerValueByName(AUTH_HEADER_NAME) { token =>
        // Everything needs to be strings because using akka unmarshalling doesn't seem to
        // return an exception if the values are not valid... just an OK
        parameters(
          "projectName".optional,
          "offerSize".optional,
          "manufacturerId".optional,
          "sourcing_scenario_id".optional,
          "includeSpecification".optional,
          "includePanel".optional,
          "isShare".optional
        ) {
          case (
                projectName,
                offerSizesParam,
                manufacturerId,
                sourcingScenarioIdsParam,
                includeSpecification,
                includePanel,
                isShare
              ) =>
            val currentSpan = Span.current()
            val share       = isShare.exists(_.toBoolean) // default to false
            currentSpan.updateName(s"HTTP GET PCB Zip")
            currentSpan.setAttribute("pcb", pcbId.toString)
            currentSpan.setAttribute("manufacturerId", manufacturerId.getOrElse("null"))
            currentSpan.setAttribute("sourcing_scenario_id", sourcingScenarioIdsParam.getOrElse("null"))
            currentSpan.setAttribute("offerSize", offerSizesParam.getOrElse("null"))
            currentSpan.setAttribute("includeSpecification", includeSpecification.getOrElse("null"))
            currentSpan.setAttribute("includePanel", includePanel.getOrElse("null"))
            currentSpan.setAttribute("isShare", isShare.getOrElse("null"))

            val params = Map(
              "projectName"          -> projectName,
              "offerSize"            -> offerSizesParam,
              "manufacturerId"       -> manufacturerId,
              "sourcing_scenario_id" -> sourcingScenarioIdsParam,
              "includeSpecification" -> includeSpecification,
              "includePanel"         -> includePanel,
              "isShare"              -> isShare
            )
            logger.info(s"Generating panel for pcb ${pcbId} with parameters ${params}")

            val offerSizeStrings          = offerSizesParam.fold(Seq.empty[String])(_.split(",").toSeq)
            val sourcingScenarioIdStrings = sourcingScenarioIdsParam.fold(Seq.empty[String])(_.split(",").toSeq)

            val offerSizes          = offerSizeStrings.flatMap(_.toIntOption)
            val sourcingScenarioIds = sourcingScenarioIdStrings.flatMap(SourcingScenarioId.fromStringOption)

            val manufacturerApi = manufacturerId.flatMap(ManufacturerApi.withNameInsensitiveOption)

            val includePanelPDF = includePanel.forall(_.toBoolean)
            if (offerSizes.size != sourcingScenarioIds.size) {
              complete(StatusCodes.BadRequest, "Mismatch between offer sizes and sourcing scenarios")
            } else {
              onComplete(
                if (share) {
                  pcbManager.getZipForShare(
                    shareId = ShareId(pcbId.value),
                    token = token,
                    sourcingScenarios = sourcingScenarioIds.zip(offerSizes),
                    manufacturerApi = manufacturerApi,
                    includeSpecificationPDF = includeSpecification.forall(_.toBoolean),
                    includePanelPDF = includePanelPDF
                  )
                } else {
                  pcbManager.getZip(
                    pcbId = pcbId,
                    token = token,
                    sourcingScenarios = sourcingScenarioIds.zip(offerSizes),
                    manufacturerApi = manufacturerApi,
                    includeSpecificationPDF = includeSpecification.forall(_.toBoolean),
                    includePanelPDF = includePanelPDF
                  )
                }
              ) {
                case Success(Right((zip, pcbName))) =>
                  val responseFileName = projectName.orElse(pcbName)
                  withFileResponse(responseFileName, "zip") { headers =>
                    complete(StatusCodes.OK, headers, HttpEntity.fromFile(MediaTypes.`application/zip`, zip))
                  }
                case Success(Left(error)) => processError(error)
                case Failure(exception) =>
                  processError(ExceptionError(s"Error getting PCB zip for pcb id=${pcbId} ${exception}", exception))
              }
            }
        }
      }
    }

  private def downloadPdf(pcbId: PCBId)(implicit pcbManager: PcbManager) =
    headerValueByName(AUTH_HEADER_NAME) { token =>
      get {
        parameter("projectName".optional) { projectName =>
          withFileResponse(projectName, "pdf") { headers =>
            val currentSpan = Span.current()
            currentSpan.updateName(s"HTTP GET PCB PDF")
            currentSpan.setAttribute("pcb", pcbId.toString)

            onComplete(
              pcbManager.getPdf(pcbId, token)
            ) {
              case Success(Right(pdf)) =>
                complete(StatusCodes.OK, headers, HttpEntity.fromFile(MediaTypes.`application/pdf`, pdf))
              case Success(Left(error)) => processError(error)
              case Failure(exception) =>
                processError(ExceptionError(s"Error creating PCB PDF file for pcb id=${pcbId}", exception))
            }
          }
        }
      }
    }

  private def downloadPanelPdf(pcbId: PCBId)(implicit pcbManager: PcbManager) =
    headerValueByName(AUTH_HEADER_NAME) { token =>
      get {
        parameters(
          "projectName".optional,
          "panelId".optional
        ) {
          case (
                projectName,
                panelIdParam
              ) =>
            withFileResponse(projectName, "pdf") { headers =>
              val currentSpan = Span.current()
              currentSpan.updateName(s"HTTP GET PCB PDF")
              currentSpan.setAttribute("pcb", pcbId.toString)

              panelIdParam.flatMap(PanelId.fromStringOption) match {
                case Some(panelId) =>
                  onComplete(
                    pcbManager.getPanelPdf(
                      token,
                      pcbId,
                      panelId
                    )
                  ) {
                    case Success(Right(pdf)) =>
                      complete(StatusCodes.OK, headers, HttpEntity.fromFile(MediaTypes.`application/pdf`, pdf))
                    case Success(Left(error)) => processError(error)
                    case Failure(exception) =>
                      processError(
                        ExceptionError(s"Error creating PCB PDF file for pcb id=${pcbId}", exception)
                      )
                  }

                case _ =>
                  complete(StatusCodes.BadRequest, "No valid panel id provided")
              }
            }
        }
      }
    }

  private def getPCBGraphics(pcbId: PCBId)(implicit api: StackRateAPI) =
    headerValueByName(AUTH_HEADER_NAME) { token =>
      get {
        implicit val jsonStreamingSupport: JsonEntityStreamingSupport = EntityStreamingSupport.json()

        val currentSpan = Span.current()
        currentSpan.updateName(s"HTTP GET PCB Graphics")
        currentSpan.setAttribute("pcb", pcbId.toString)

        onComplete(
          api.getPCBGraphics(pcbId, token)
        ) {
          case Success(Right(layers)) =>
            complete(Source.apply(layers))
          case Success(Left(GraphicError.MissingLayerstack)) =>
            complete(StatusCodes.NotFound, PcbServerErrorResponse.MissingLayerstack: PcbServerErrorResponse)
          case Success(Left(GraphicError.MissingOutline)) =>
            complete(StatusCodes.NotFound, PcbServerErrorResponse.MissingOutline: PcbServerErrorResponse)
          case Success(Left(GraphicError.ServiceErrorWrapper(error))) =>
            logger.error(s"Error getting PCB graphics for pcb id=${pcbId} ${error}")
            processError(error)
          case Failure(exception) =>
            processError(ExceptionError(s"Error getting PCB graphics for pcb id=${pcbId}", exception))
        }
      }
    }

  private def uploadPcbFiles(pcbId: PCBId)(implicit pcbManager: PcbManager) =
    headerValueByName(AUTH_HEADER_NAME) { token =>
      post {
        withSizeLimit(maxBytes = 100L * 1024 * 1024) { // 100MB should be enough
          val currentSpan = Span.current()
          currentSpan.updateName(s"HTTP POST PCB Files")
          currentSpan.setAttribute("pcb", pcbId.toString)

          def tempDest(fileInfo: FileInfo): File = {
            val dest = File.createTempFile("akka-http-upload", ".tmp")
            dest.deleteOnExit()
            dest
          }

          storeUploadedFiles("", tempDest) { files =>
            if (files.nonEmpty) {
              val filesToUpload = files.map {
                case (fileInfo, file) =>
                  (FileName(fileInfo.fileName), file)
              }

              onComplete(
                pcbManager.uploadPcbFiles(pcbId, filesToUpload, token)
              ) { result =>
                files.foreach(_._2.delete())
                result match {
                  case Success(Right(r)) =>
                    Telemetry.track("upload_pcb_files", Map("pcb" -> pcbId.toString), token)
                    complete(StatusCodes.OK, UploadFileResponse.fromResult(r)(Lang("en")))
                  case Success(Left(error)) =>
                    processError(error)
                  case Failure(exception) =>
                    processError(ExceptionError(s"Error uploading PCB files for pcb id=${pcbId}", exception))
                }
              }
            } else {
              complete(StatusCodes.OK, UploadFileResponse.Empty)
            }
          }
        }
      }
    }

  private def updatePcbFileTypes(pcbId: PCBId)(implicit pcbManager: PcbManager) =
    headerValueByName(AUTH_HEADER_NAME) { token =>
      concat(
        put {
          entity(as[FileTypeUpdateRequest]) { updateRequest =>
            val currentSpan = Span.current()
            currentSpan.updateName(s"HTTP PUT Update PCB file types")
            currentSpan.setAttribute("pcb", pcbId.toString)

            onComplete(
              pcbManager.updatePcbFileTypes(pcbId, token, updateRequest.updates)
            ) {
              case Success(Right(_))    => complete(StatusCodes.OK)
              case Success(Left(error)) => processError(error)
              case Failure(exception)   => processError(ExceptionError("Error updating PCB file types", exception))
            }
          }
        },
        delete {
          entity(as[DeleteFileRequest]) { request =>
            val currentSpan = Span.current()
            currentSpan.updateName(s"HTTP DELETE Delete PCB files")
            currentSpan.setAttribute("pcb", pcbId.toString)

            onComplete(
              pcbManager.deletePcbFiles(token, pcbId, request.files)
            ) {
              case Success(Right(_))    => complete(StatusCodes.OK)
              case Success(Left(error)) => processError(error)
              case Failure(e) => processError(ExceptionError(s"Error deleting PCB files for pcb id=${pcbId}", e))
            }
          }
        }
      )
    }

  private def getPCBOffer(share: ShareId, offer: String)(implicit
      pcbService: PCBService,
      quotationService: QuotationService,
      panelService: PanelService
  ) =
    headerValueByName(AUTH_HEADER_NAME) { token =>
      get {
        val result: Future[Either[PcbServerError, (quotation.Quotation, Seq[CustomerPanelAPI], PCBV2)]] =
          for {
            pcbv2 <- pcbService.getSharedPCBV2(share.value).withToken(token).invoke()
            quote <- quotationService.getQuotation(offer, Some(true)).withToken(token).invoke()
            panels <- Future.sequence(
              quote.items.toSeq
                .flatMap(z =>
                  z
                    .flatMap(z =>
                      z.resolved.flatMap(_.info)
                    )
                )
                .flatMap(_.panel)
                .map {
                  id =>
                    panelService.getCustomerPanel(pcbv2.assembly, pcbv2.id, id.toString).withToken(token).invoke()
                }.toSeq
            )
          } yield
            if (
              quote.assembly.getReference().id == pcbv2.assembly && quote.assembly.getReference().version == pcbv2.id
            ) {
              Right((quote, panels, pcbv2))
            } else {
              Left(InvalidParams("Quote does not match PCB"))
            }

        onComplete(result) {
          case Failure(exception) =>
            processError(ExceptionError(s"error getting the quotes", exception))
          case Success(Right(value)) =>
            convertQuotes(value._1, value._2, value._3) match {
              case Left(value) =>
                processError(value)
              case Right(offer) =>
                complete(StatusCodes.OK, offer)
            }
          case Success(Left(value)) =>
            processError(value)
        }
      }

    }

  private def convertQuotes(
      value: quotation.Quotation,
      panels: Seq[CustomerPanelAPI],
      pcb: PCBV2
  ): Either[PcbServerError, PcbOffer] = {
    val infoes = value.items.getOrElse(Seq()).flatMap(_.resolved.flatMap(_.info))

    val convertedPanels = panels.map(p => LuminovoCustomerPanel.convert(p, 0, Depanelization.VCut)).toMap

    val groups = infoes.groupBy(x => (x.currency, x.panel))
    val filteredGroup =
      if (groups.size > 1) {
        Left(ServerError("quote is not uniform"))
      } else {
        Right(groups.head)
      }

    filteredGroup.flatMap { x =>
      val (currency, panelId) = x._1
      val stackratePanel      = panels.find(papi => papi.id == panelId)
      val info                = x._2

      for {
        x <-
          (panelId.flatMap(
            convertedPanels.get
          ) match {
            case Some(value) => Right(value)
            case None        => Left(NotFoundError("panel not found"))
          })
        pricePoints <- either(info.map(convertPricePoints))
      } yield {
        val (convertedPanel, multiplier) = x
        val otc = info.flatMap(x => x.nre.map(nre => Cost(nre.toFloat))).filter(_.amount > 0) match {
          case Nil => None
          case x   => Some(x)
        }
        PcbOffer(
          pricePoints = pricePoints,
          currency = currency.getOrElse(CurrencyCode.EUR),
          validUntil = None,
          offerNumber = value.name,
          offerValidity = pcb.specifications.head.hash,
          priceType = PriceType.ListPrice,
          piecesPerUnit = multiplier,
          notes = value.description.getOrElse(""),
          offerUrl = None,
          oneTimeCosts = otc,
          sourcingScenarioId =
            SourcingScenarioId(stackratePanel.flatMap(_.sourcingScenario).getOrElse(UUIDUtils.nil)),
          panel = None,
          panelSpecification = Some(PanelSpecification.PanelDetailsSpecification(convertedPanel)),
          sharedPcbId = None
        )
      }
    }
  }

  private def either[X, Y](a: Iterable[Either[X, Y]]): Either[X, Seq[Y]] =
    // convert a sequence of either to an either of a sequence
    a.foldLeft[Either[X, Seq[Y]]](Right(Seq())) {
      case (Right(acc), Right(value)) => Right(acc :+ value)
      case (Right(_), Left(value))    => Left(value)
      case (Left(value), _)           => Left(value)
    }

  private def either[X, Y](a: Option[X], b: Y) =
    a match {
      case Some(value) => Right(value)
      case None        => Left(b)
    }

  private def convertPricePoints(item: QuotationItemInfo): Either[PcbServerError, PricePointInput] =
    for {
      panelQuantity <- either(item.panelQuantity, NotFoundError("quantity not found"))
      unitPrice     <- either(item.unitPrice, NotFoundError("unit price not found"))
    } yield PricePointInput(
      quantity = item.panelQuantity.get.toLong,
      amount = item.unitPrice.get.floatValue,
      leadTimeDays = item.delivery.map(_.intValue),
      currency = item.currency
    )

  private def withFileResponse(name: Option[String], extension: String)(f: Seq[HttpHeader] => Route) = {
    def clean(filename: String): String = filename.replaceAll("[^a-zA-Z0-9.-]", "")

    val fileName = name.map(x => s"$x-specification").map(clean).getOrElse("pcb-specification") + s".$extension"
    val exposeHeader = HttpHeader.parse("Access-Control-Expose-Headers", "Content-Disposition") match {
      case ParsingResult.Ok(header, errors) => Some(header)
      case ParsingResult.Error(error)       => None
    }
    HttpHeader.parse("Content-Disposition", s"inline; filename=\"$fileName\"") match {
      case HttpHeader.ParsingResult.Ok(header, _) => f(Seq(exposeHeader, Some(header)).flatten)
      case _                                      => complete(StatusCodes.InternalServerError)
    }
  }

  private def processError(requestError: PcbServerError): Route = {
    implicit val ilang: Lang = Lang("en")

    val (status, response) = requestError match {
      case e: NotFoundError =>
        Tracing.error(e)
        (StatusCodes.NotFound, PcbServerErrorResponse.TechnicalError("Requested PCB not found"))

      case e: InvalidParams =>
        Tracing.error(e)
        (StatusCodes.BadRequest, PcbServerErrorResponse.BadRequest(e.error))

      case e: ThirdPartyError =>
        Tracing.error(e)
        (
          StatusCodes.ServiceUnavailable,
          PcbServerErrorResponse.TechnicalError("There was an error communicating with an external service")
        )

      case _: CredentialsMissingError =>
        (StatusCodes.Unauthorized, PcbServerErrorResponse.TechnicalError("Credentials missing or invalid"))

      case e: UnauthorizedError =>
        Tracing.error(e)
        (StatusCodes.Unauthorized, PcbServerErrorResponse.TechnicalError("Credentials missing or invalid"))

      case e: ApiNotSetUpError =>
        (StatusCodes.BadRequest, PcbServerErrorResponse.TechnicalError(s"API is not properly setup: ${e.kind}"))

      case e: UnknownError =>
        Tracing.error(e)
        (StatusCodes.InternalServerError, PcbServerErrorResponse.TechnicalError("An unknown error occurred"))

      case e: ServerError =>
        Tracing.error(e)
        (StatusCodes.InternalServerError, PcbServerErrorResponse.TechnicalError("An unknown error occurred"))

      case e: ExceptionError =>
        Tracing.exceptionError(e.ex, e.error)
        (StatusCodes.InternalServerError, PcbServerErrorResponse.TechnicalError("An unknown error occurred"))

      case e: PropertyErrors =>
        (
          StatusCodes.OK,
          PcbServerErrorResponse.PropertyErrors(e.errors.map(PropertyErrorResponse.fromPropertyError))
        )

      case _: StackratePricingErrors | _: StackratePricingBreaks =>
        (StatusCodes.OK, PcbServerErrorResponse.StackratePricingIssue)

      case CustomStackupError =>
        (StatusCodes.OK, PcbServerErrorResponse.CustomStackup)

      case e: PanelError =>
        (
          StatusCodes.OK,
          PcbServerErrorResponse.PanelError(errorMessage(e.error))
        )
    }

    complete(status, response)
  }

  private def postOfferHandleWithToken[R](
      f: (R, AuthToken, OfferManager) => Route
  )(
      implicit
      offerManager: OfferManager,
      unmarshaller: FromRequestUnmarshaller[R]
  ): Route =
    headerValueByName(AUTH_HEADER_NAME) { token =>
      postOfferHandle[R] { (offer, manager) =>
        f(offer, token, manager)
      }
    }

  private def postOfferHandle[R](
      f: (R, OfferManager) => Route
  )(
      implicit
      offerManager: OfferManager,
      unmarshaller: FromRequestUnmarshaller[R]
  ): Route =
    post {
      entity(as[R]) { offer =>
        parameterMap { params =>
          f(offer, offerManager)
        }
      }
    }
}
