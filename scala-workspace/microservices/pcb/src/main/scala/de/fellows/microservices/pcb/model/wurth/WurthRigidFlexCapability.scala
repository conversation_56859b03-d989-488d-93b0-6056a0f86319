package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.ViaFillingType.PluggedSingleSided
import de.fellows.ems.pcb.api.specification.{
  BaseMaterial => PCBBaseMaterial,
  Chamfering,
  LayerstackType => SpecLayerstackType,
  ViaFillingType
}
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.No
import de.fellows.microservices.pcb.model.pcb.capability._
import de.fellows.microservices.pcb.model.pcb.props.BaseMaterial.BaseMaterialCapability
import de.fellows.microservices.pcb.model.pcb.props.BlindVias.BlindViasCapability
import de.fellows.microservices.pcb.model.pcb.props.BuriedVias.BuriedViasCapability
import de.fellows.microservices.pcb.model.pcb.props.Chamfering.ChamferingCapability
import de.fellows.microservices.pcb.model.pcb.props.FinalThickness.FinalThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.HardGold.HardGoldCapability
import de.fellows.microservices.pcb.model.pcb.props.InnerCopperThickness.InnerCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.LayerstackType.LayerstackTypeCapability
import de.fellows.microservices.pcb.model.pcb.props.MinInnerLayerStructure.MinInnerLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinOuterLayerStructure.MinOuterLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinViaDiameter.MinViaDiameterCapability
import de.fellows.microservices.pcb.model.pcb.props.NumberOfLayers.NumberOfLayersCapability
import de.fellows.microservices.pcb.model.pcb.props.OuterCopperThickness.OuterCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.PCBPropertyExtractor._
import de.fellows.microservices.pcb.model.pcb.props.ULLayerStack.ULLayerStackCapability
import de.fellows.microservices.pcb.model.pcb.props.ViaFillingType.ViaFillingTypeCapability
import de.fellows.microservices.pcb.model.pcb.props._

object WurthRigidFlexCapability {

  private val layerConditions =
    Condition(
      Rule.Gt(props.NumberOfLayers(2)),
      Seq(
        new InnerCopperThicknessCapability(Inner18mcr),
        MinInnerLayerStructureCapability(0.100)
      )
    )

  private val conditionalCapabilities = Seq(layerConditions)

  val layerStackCapability = LayerStackCapability(
    new LayerstackTypeCapability(SpecLayerstackType.RigidFlex),
    new ULLayerStackCapability(No),
    new NumberOfLayersCapability(props.TwoLayers, props.FourLayers, props.SixLayers),
    new FinalThicknessCapability(FinalThickness.Thickness1mm, FinalThickness.Thickness155mm),
    new BaseMaterialCapability(PCBBaseMaterial.FR4),
    new OuterCopperThicknessCapability(Outer35mcr),
    new InnerCopperThicknessCapability(),
    MinOuterLayerStructureCapability(0.100),
    MinInnerLayerStructureCapability(0.250)
  )

  val mechanicalCapability = MechanicalCapability(
    new MinViaDiameterCapability(0.25, Int.MaxValue, required = true),
    new ViaFillingTypeCapability(ViaFillingType.None, PluggedSingleSided),
    new BlindViasCapability(No),
    new BuriedViasCapability(No),
    new ChamferingCapability(Chamfering.None),
  )

  val capability = ManufacturerCapability(
    boardBasic = WurthCapability.basicBoardCapability.copy(
      hardGold = new HardGoldCapability(No)
    ),
    boardAdvanced = WurthCapability.advancedBoardCapability,
    layerStack = layerStackCapability,
    mechanical = mechanicalCapability,
    alternativeConditions = conditionalCapabilities
  )
}
