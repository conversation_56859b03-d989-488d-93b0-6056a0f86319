package de.fellows.microservices.pcb.model

import de.fellows.microservices.pcb.model.wurth.WurthApiService.WurthLocation

package object wurth {
  private[wurth] val panelConstraints = Wurth.panelConstraints

  private[wurth] def offerUrl(offerNumber: String, location: WurthLocation): String = {
    val (locationDomain, locationPath) = location match {
      case WurthLocation.Germany                         => ("de", "de")
      case WurthLocation.France                          => ("fr", "fr")
      case WurthLocation.SpainAndPortugal                => ("es", "es")
      case WurthLocation.UnitedKingdomAndNorthernIreland => ("com", "en-gb")
      case WurthLocation.Switzerland                     => ("com", "de-ch")
      case WurthLocation.RestOfWorld                     => ("com", "en")
    }
    s"https://www.wedirekt.$locationDomain/$locationPath/pcb/load_offer/$offerNumber"
  }
}
