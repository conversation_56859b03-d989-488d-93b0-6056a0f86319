package de.fellows.microservices.pcb.model.pcb.props

/** The amount of plated holes (vias)
  */
object PhCount {
  val name: String  = "phCount"
  val label: String = "pcb.mechanical.phCount"

  def empty: PhCount                             = PhCount(Option.empty[Int])
  def apply(value: Int): PhCount                 = PhCount(Some(value))
  def fromV2(value: Option[BigDecimal]): PhCount = PhCount(value.map(_.toInt))
}

final case class PhCount(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = PhCount.name
  val label: String     = PhCount.label
  val unit: String      = "holes"

  override val legacyNames: Option[Seq[String]] = Some(Seq("ph_count"))
}
