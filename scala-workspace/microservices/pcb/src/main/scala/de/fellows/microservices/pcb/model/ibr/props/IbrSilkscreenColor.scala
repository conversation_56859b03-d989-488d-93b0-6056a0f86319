package de.fellows.microservices.pcb.model.ibr.props

import de.fellows.ems.pcb.api.specification
import de.fellows.microservices.pcb.model.pcb.props
sealed trait IbrSilkscreenColor {
  val value: String
  val toPCB: props.SilkscreenColor
}

object IbrSilkscreenColor {
  val ALL = Set(
    weiss,
    schwarz,
    gelb
  )

  def converter(value: props.SilkscreenColor): Option[IbrSilkscreenColor] =
    ALL.find(_.toPCB == value)

  private case object weiss extends IbrSilkscreenColor {
    override val value: String                = "weiss"
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(specification.SilkscreenColor.White)
  }
  private case object schwarz extends IbrSilkscreenColor {
    override val value: String                = "schwarz"
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(specification.SilkscreenColor.Black)

  }
  private case object gelb extends IbrSilkscreenColor {
    override val value: String                = "gelb"
    override val toPCB: props.SilkscreenColor = props.SilkscreenColor(specification.SilkscreenColor.Yellow)

  }
}
