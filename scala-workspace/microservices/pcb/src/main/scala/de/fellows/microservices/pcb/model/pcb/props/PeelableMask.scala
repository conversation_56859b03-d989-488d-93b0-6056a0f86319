package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object PeelableMask {
  val name: String  = "peelableMask"
  val label: String = "pcb.board.advanced.peelableMask"

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for peelableMask is None
    */
  def default: PeelableMask                    = PeelableMask(Side.None)
  def apply(value: Option[Side]): PeelableMask = value.map(PeelableMask(_)).getOrElse(default)
  type PeelableMaskCapability = SetCapability[PeelableMask, Side]
}

final case class PeelableMask(override val value: Side) extends PCBRequiredProperty[Side] {
  val fieldName: String = PeelableMask.name
  val label: String     = PeelableMask.label
  val unit: String      = ""
}
