package de.fellows.microservices.pcb.model.safePcb

import de.fellows.microservices.pcb.model.panel.PanelConstraints

object SafePcb {

  /** Preferences are taken from here: https://www.notion.so/luminovo/Safe-PCB-API-8eaeac7653a040d9bee44df0ac0cfdb0
    * File: PCB Compatibilities, Circuit and Panel sections
    */
  val panelConstraints: PanelConstraints = PanelConstraints(
    minWidth = 3,
    minHeight = 3,
    maxWidth = 1020,
    maxHeight = 520
  )
}
