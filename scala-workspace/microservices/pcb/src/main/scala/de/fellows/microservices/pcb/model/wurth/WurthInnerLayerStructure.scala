package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.{LayerstackType => SpecLayerstackType}
import de.fellows.microservices.pcb.model.pcb.props.{LayerstackType, MinInnerLayerStructure}

/** Name of param in Wurth API: pattern_structures_inward
  */
sealed trait WurthInnerLayerStructure {
  val value: Int
  val initialValue: MinInnerLayerStructure

  override def equals(obj: Any): Boolean = obj match {
    case that: WurthInnerLayerStructure => that.value == value
    case _                              => false
  }
}

private case class InnerLayerEmpty(override val initialValue: MinInnerLayerStructure = MinInnerLayerStructure.empty)
    extends WurthInnerLayerStructure {
  override val value = -1
}
private case class WurthInnerMcr192(override val initialValue: MinInnerLayerStructure = MinInnerLayerStructure(0.192))
    extends WurthInnerLayerStructure {
  override val value = 1
}

private case class WurthInnerMcr150(override val initialValue: MinInnerLayerStructure = MinInnerLayerStructure(0.150))
    extends WurthInnerLayerStructure {
  override val value = 2
}

private case class WurthInnerMcr125(override val initialValue: MinInnerLayerStructure = MinInnerLayerStructure(0.125))
    extends WurthInnerLayerStructure {
  override val value = 3
}

private case class WurthInnerMcr100(override val initialValue: MinInnerLayerStructure = MinInnerLayerStructure(0.100))
    extends WurthInnerLayerStructure {
  override val value = 4
}

private case class WurthInnerMcr85(override val initialValue: MinInnerLayerStructure = MinInnerLayerStructure(0.085))
    extends WurthInnerLayerStructure {
  override val value = 5
}

private case class WurthInnerMcr250(override val initialValue: MinInnerLayerStructure = MinInnerLayerStructure(0.250))
    extends WurthInnerLayerStructure {
  override val value = 10
}

private object WurthInnerLayerStructure {

  def converter(layerstackType: LayerstackType)(value: MinInnerLayerStructure): Option[WurthInnerLayerStructure] =
    layerstackType.value match {
      case SpecLayerstackType.Rigid     => rigidConverter(value)
      case SpecLayerstackType.RigidFlex => rigidFlexConverter(value)
      case SpecLayerstackType.Flex      => Some(InnerLayerEmpty())
      case SpecLayerstackType.Ims       => Some(InnerLayerEmpty())
    }

  private def rigidConverter(value: MinInnerLayerStructure): Option[WurthInnerLayerStructure] =
    value.value match {
      case Some(t) if t >= 0.250 => Some(WurthInnerMcr250(value))
      case Some(t) if t >= 0.192 => Some(WurthInnerMcr192(value))
      case Some(t) if t >= 0.150 => Some(WurthInnerMcr150(value))
      case Some(t) if t >= 0.125 => Some(WurthInnerMcr125(value))
      case Some(t) if t >= 0.1   => Some(WurthInnerMcr100(value))
      case Some(t) if t >= 0.085 => Some(WurthInnerMcr85(value))
      case Some(_)               => None
      case _                     => Some(InnerLayerEmpty(value))
    }

  private def rigidFlexConverter(value: MinInnerLayerStructure): Option[WurthInnerLayerStructure] =
    value.value match {
      case Some(t) if t >= 0.150 => Some(WurthInnerMcr150(value))
      case Some(t) if t >= 0.125 => Some(WurthInnerMcr125(value))
      case Some(t) if t >= 0.1   => Some(WurthInnerMcr100(value))
      case Some(_)               => None
      case _                     => Some(InnerLayerEmpty(value))
    }

  def fromWurth(value: Int): WurthInnerLayerStructure = value match {
    case 1  => WurthInnerMcr192()
    case 2  => WurthInnerMcr150()
    case 3  => WurthInnerMcr125()
    case 4  => WurthInnerMcr100()
    case 5  => WurthInnerMcr85()
    case 10 => WurthInnerMcr250()
    case -1 => InnerLayerEmpty()
    case _  => throw new IllegalArgumentException(s"Unknown value for inner layer structure: $value")
  }
}
