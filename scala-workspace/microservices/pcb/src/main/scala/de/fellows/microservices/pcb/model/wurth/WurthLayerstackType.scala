package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.LayerstackType
import de.fellows.microservices.pcb.model.pcb.props

/** Number of layers
  *
  * Name of param in Wurth API: layers
  */
sealed trait WurthLayerstackType {
  val value: Int
}

private object Wurth<PERSON>ayerstackType {

  case object Rigid extends WurthLayerstackType {
    override val value: Int = 1
  }

  case object Flex1Layer extends WurthLayerstackType {
    override val value: Int = 10
  }

  case object Flex2Layer extends WurthLayerstackType {
    override val value: Int = 11
  }

  case object RigidFlex2Layers extends WurthLayerstackType {
    override val value: Int = 14
  }

  case object RigidFlex4Layers extends WurthLayerstackType {
    override val value: Int = 15
  }

  case object RigidFlex6Layers extends WurthLayerstackType {
    override val value: Int = 16
  }

  /** Converts from StackRate layers to Wurth layers
    */
  def converter(layers: props.NumberOfLayers)(value: props.LayerstackType): Option[WurthLayerstackType] =
    value.value match {
      case LayerstackType.Rigid => Some(Rigid)
      case LayerstackType.Flex =>
        layers match {
          case props.OneLayer  => Some(Flex1Layer)
          case props.TwoLayers => Some(Flex2Layer)
          case _               => None
        }
      case LayerstackType.RigidFlex =>
        layers match {
          case props.TwoLayers  => Some(RigidFlex2Layers)
          case props.FourLayers => Some(RigidFlex4Layers)
          case props.SixLayers  => Some(RigidFlex6Layers)
          case _                => None
        }
      case _ => None
    }

  def fromWurth(value: Int): WurthLayerstackType = value match {
    case Rigid.value            => Rigid
    case Flex1Layer.value       => Flex1Layer
    case Flex2Layer.value       => Flex2Layer
    case RigidFlex2Layers.value => RigidFlex2Layers
    case RigidFlex4Layers.value => RigidFlex4Layers
    case RigidFlex6Layers.value => RigidFlex6Layers
    case _                      => Rigid
  }
}
