package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.Chamfering
import de.fellows.ems.pcb.api.specification.Chamfering.{Isa, Pci}
import de.fellows.microservices.pcb.model.pcb.props

/** Name of param in Wurth API: beveling
  */
sealed trait WurthChamfer {
  val value: Int
  val toPCB: props.Chamfering
}
private case object ChamferNone extends WurthChamfer {
  override val value: Int              = 0
  override val toPCB: props.Chamfering = props.Chamfering(Chamfering.None)
}
private case object Chamfer20PCI extends <PERSON>rth<PERSON>ham<PERSON> {
  override val value: Int              = 1
  override val toPCB: props.Chamfering = props.Chamfering(Pci)
}
private case object Chamfer45ISA extends WurthChamfer {
  override val value: Int              = 3
  override val toPCB: props.Chamfering = props.Chamfering(Isa)
}

private object Wu<PERSON><PERSON><PERSON>fer {

  def fromStackRateChamfer(chamfer: Chamfering): WurthChamfer =
    chamfer match {
      case Pci => Chamfer20PCI
      case Isa => Chamfer45ISA
      case _   => ChamferNone
    }

  def fromWurth(value: Int): WurthChamfer = value match {
    case 0 => ChamferNone
    case 1 => Chamfer20PCI
    case 3 => Chamfer45ISA
    case _ => throw new IllegalArgumentException(s"Unknown chamfer value: $value")
  }
}
