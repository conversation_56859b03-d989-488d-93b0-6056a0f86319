package de.fellows.microservices.pcb

import play.api.Logging
import play.api.libs.json.JsError
import sttp.client3.{DeserializationException, HttpError, Response, ResponseException}
import sttp.model.StatusCode

trait SttpHelper extends Logging {

  /** This method helps to log any error in case with incorrect reply from third-party service and handle this error
    * graciously
    */
  protected def handleJsonResponse[E <: PcbServerError, T, A](
      response: Response[Either[ResponseException[String, JsError], T]],
      service: String
  )(
      f: T => Either[E, A]
  ): Either[E, A] =
    handleSimpleJsonResponse(service)(response).flatMap(f)

  protected def handleSimpleJsonResponse[E <: PcbServerError, T](
      service: String
  )(
      response: Response[Either[ResponseException[String, JsError], T]]
  ): Either[E, T] =
    response.body match {
      case Left(error) =>
        error match {
          case HttpError(_, StatusCode.NotFound) =>
            val message =
              s"Not found error in $service. URI: ${response.request.method.method} ${response.request.uri}"
            logger.error(message)
            Left(NotFoundError(message).asInstanceOf[E])

          case HttpError(msg, StatusCode.Unauthorized) =>
            val message =
              s"Unauthorized error in $service. URI: ${response.request.method.method} ${response.request.uri}: $msg"
            logger.error(message)
            Left(UnauthorizedError(message).asInstanceOf[E])

          case HttpError(msg, code) =>
            val message = s"A problem in communication with $service. Http code: $code, Error: $msg"
            logger.error(message)
            Left(ThirdPartyError(message, code).asInstanceOf[E])

          case e @ DeserializationException(body, _) =>
            val message = s"Could not deserialize response \"$body\" from $service"
            logger.error(message)
            Left(ExceptionError(message, e).asInstanceOf[E])
        }
      case Right(value) => Right(value)
    }

}
