package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.microservices.pcb.model.pcb.LayerStackProperties

/** Name of param in BetaLayout API: track_gap_size
  */
sealed trait BetaLayoutMinStructure {
  val value: Int

  override def equals(obj: Any): Boolean = obj match {
    case that: BetaLayoutMinStructure => that.value == value
    case _                            => false
  }
}

private final case class MinStructure150mcr() extends BetaLayoutMinStructure {
  override val value = 1
}

private final case class MinStructure125mcr() extends BetaLayoutMinStructure {
  override val value = 2
}

private case class MinStructure100mcr() extends BetaLayoutMinStructure {
  override val value = 3
}

private object BetaLayoutMinStructure {

  def converter(value: LayerStackProperties): Option[BetaLayoutMinStructure] = {
    val min = (value.minInnerLayerStructure.value, value.minOuterLayerStructure.value) match {
      case (None, None)       => None
      case (Some(x), None)    => Some(x)
      case (None, Some(x))    => Some(x)
      case (Some(x), Some(y)) => Some(x min y)
    }
    min match {
      case Some(t) if t >= 0.150 => Some(MinStructure150mcr())
      case Some(t) if t >= 0.125 => Some(MinStructure125mcr())
      case Some(t) if t >= 0.1   => Some(MinStructure100mcr())
      case _                     => None
    }
  }

}
