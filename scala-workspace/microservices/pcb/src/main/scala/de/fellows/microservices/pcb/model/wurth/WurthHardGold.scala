package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.HardGold

sealed trait WurthHardGold {
  val value: Int
  val toPCB: HardGold
}

private case object <PERSON><PERSON><PERSON>ardGoldNo extends Wurth<PERSON>ardGold {
  override val value: Int      = 0
  override val toPCB: HardGold = HardGold.no
}
private case object <PERSON><PERSON><PERSON><PERSON><PERSON>oldY<PERSON> extends WurthHardGold {
  override val value: Int      = 1
  override val toPCB: HardGold = HardGold.yes
}

private object WurthHardGold {

  def apply(value: HardGold): WurthHardGold =
    if (value.value)
      WurthHardGoldYes
    else
      WurthHardGoldNo

  def fromWurth(value: Option[Int]): WurthHardGold =
    value match {
      case Some(value) => fromWurth(value)
      case None        => apply(HardGold.default)
    }

  def fromWurth(value: Int): WurthHardGold = value match {
    case 1 => WurthHardGoldYes
    case _ => WurthHardGoldNo
  }
}
