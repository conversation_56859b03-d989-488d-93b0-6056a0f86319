package de.fellows.microservices.pcb.model.ibr.api

import de.fellows.luminovo.panel.Depanelization
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.lq.Credentials
import play.api.libs.functional.syntax.toFunctionalBuilderOps
import play.api.libs.json.{__, Format, JsNull, JsNumber, JsObject, JsString, <PERSON>son, Reads, Writes}

sealed trait IbrApiBasicRequestData {
  val customer: Int
  val customer_api_key: String
}

case class IbrApiRequestData(
    override val customer: Int,
    override val customer_api_key: String,
    quantities: Seq[Int],
    expressQuantities: Option[Seq[Int]],
    revision: Option[Int],
    partid: Option[String],
    applicationType: Option[String],
    source: Option[String],
    origin: Option[String],
    creatorName: Option[String],
    creatorEmail: Option[String],
    creationDate: Option[String],
    projectName: String,
    expressdeliverytime: Option[Int],
    partName: Option[String],
    lp: IbrApiPCB
) extends IbrApiBasicRequestData {
  def withPanel(calculatedPanelInfo: CalculatedPanelInfo): IbrApiRequestData = {
    val (
      panelWidth,
      panelHeight,
      pcbsPerPanel,
      depanelization
    ) =
      calculatedPanelInfo match {
        case p: CalculatedPanelInfo.FromPanelDetails =>
          (
            p.panelDistribution.panel.widthInMm,
            p.panelDistribution.panel.heightInMm,
            p.panelDistribution.mesh.size,
            p.depanelization
          )

        case p: CalculatedPanelInfo.FromExisting =>
          (
            p.existing.panelWidth,
            p.existing.panelHeight,
            p.existing.numberOfPcbs,
            p.existing.depanelization
          )
      }

    val singlePCB = pcbsPerPanel == 1 && panelWidth == this.lp.Breite && panelHeight == this.lp.Laenge

    if (singlePCB) {
      copy(
        lp = this.lp.copy(
          Nutzentyp = Some(0),
          Breite = this.lp.Breite,
          Laenge = this.lp.Laenge,
          BreiteEinzel = Some(this.lp.Breite),
          LaengeEinzel = Some(this.lp.Laenge),
          Ritzen = None,
          Fraesen = None
        )
      )
    } else {
      copy(
        lp = this.lp.copy(
          Nutzentyp = Some(1),
          Nutzenzahl = Some(pcbsPerPanel),
          Breite = panelWidth.doubleValue,
          Laenge = panelHeight.doubleValue,
          BreiteEinzel = Some(this.lp.Breite),
          LaengeEinzel = Some(this.lp.Laenge),
          Nutzenaufbau = Some(""),
          NutzenaufbauText = Some("lt. Vorgabe"),
          Ritzen = depanelization match {
            case Depanelization.VCut           => Some(1)
            case Depanelization.Milling        => None
            case Depanelization.MillingAndVCut => None
          },
          Fraesen = depanelization match {
            case Depanelization.VCut           => None
            case Depanelization.Milling        => Some(1)
            case Depanelization.MillingAndVCut => Some(1)
          }
        ),
        quantities = Seq(calculatedPanelInfo.numberOfPanels.value),
        expressQuantities = Some(Seq(calculatedPanelInfo.numberOfPanels.value))
      )
    }
  }
}

object IbrApiRequestData {
  val baseformat = Json.format[IbrApiRequestData]
  implicit val format: Format[IbrApiRequestData] = Format(
    Reads { r =>
      baseformat.reads(r)
    },
    Writes { r =>
      val express = r.expressQuantities.map(q => JsString(q.mkString(",")))
      baseformat.writes(r) +
        ("quantities"        -> JsString(r.quantities.mkString(","))) +
        ("expressQuantities" -> express.getOrElse(JsNull))
    }
  )
}

case class IbrApiCredentialsCheck(
    override val customer: Int,
    override val customer_api_key: String
) extends IbrApiBasicRequestData

object IbrApiCredentialsCheck {

  val reads: Reads[IbrApiCredentialsCheck] =
    ((__ \ "customer").read[Int] and
      (__ \ "customer_api_key").read[String])((customer, key) =>
      IbrApiCredentialsCheck(customer, key)
    )

  implicit val format: Format[IbrApiCredentialsCheck] = Format(
    reads,
    Writes { v =>
      JsObject(Seq(
        "customer"         -> JsNumber(v.customer),
        "customer_api_key" -> JsString(v.customer_api_key),
        "projectName"      -> JsString("Credentials Check"),
        "lp" -> JsObject(Seq(
          "Laenge" -> JsNumber(1),
          "Breite" -> JsNumber(1)
        ))
      ))
    }
  )

  def fromCredentials(credentials: Credentials): Either[String, IbrApiCredentialsCheck] =
    credentials.username.trim.toIntOption match {
      case Some(value) => Right(IbrApiCredentialsCheck(value, credentials.password))
      case None        => Left("Invalid customer id.")
    }

}
object IbrApiBasicRequestData {
  implicit val format: Format[IbrApiBasicRequestData] = Json.format[IbrApiBasicRequestData]
}
