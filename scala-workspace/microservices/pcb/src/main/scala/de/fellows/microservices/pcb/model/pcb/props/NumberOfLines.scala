package de.fellows.microservices.pcb.model.pcb.props

object NumberOfLines {
  val name: String  = "numberOfLines"
  val label: String = "pcb.board.advanced.numberOfLines"

  def empty: NumberOfLines                             = NumberOfLines(Option.empty[Int])
  def apply(value: Int): NumberOfLines                 = NumberOfLines(Some(value))
  def fromV2(value: Option[BigDecimal]): NumberOfLines = NumberOfLines(value.map(_.toInt))
}

final case class NumberOfLines(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = NumberOfLines.name
  val label: String     = NumberOfLines.label
  val unit: String      = "lines"
} 