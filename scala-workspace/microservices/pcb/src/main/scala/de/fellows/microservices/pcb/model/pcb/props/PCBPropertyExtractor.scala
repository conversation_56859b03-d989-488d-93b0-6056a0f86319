package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.pcb.PCBProperties

/** Represents a PCB property extractor
  *
  * This can be used to extract a property from a PCBProperties object.
  *
  * This way we can create [[de.fellows.microservices.pcb.model.pcb.capability.Rule]]s and
  * [[de.fellows.microservices.pcb.model.pcb.capability.Condition]]s without caring
  * how to extract the property from the PCBProperties object.
  */
trait PCBPropertyExtractor[T] {
  def extractPcbProperty(properties: PCBProperties): T
}

object PCBPropertyExtractor {
  implicit val boardHeightExtractor: PCBPropertyExtractor[BoardHeight]     = _.basic.boardHeight
  implicit val boardWidthExtractor: PCBPropertyExtractor[BoardWidth]       = _.basic.boardWidth
  implicit val layersExtractor: PCBPropertyExtractor[NumberOfLayers]       = _.layer.numberOfLayers
  implicit val thicknessExtractor: PCBPropertyExtractor[FinalThickness]    = _.layer.finalThickness
  implicit val baseMaterialExtractor: PCBPropertyExtractor[BaseMaterial]   = _.layer.baseMaterial
  implicit val buriedViasExtractor: PCBPropertyExtractor[BuriedVias]       = _.mechanical.buriedVias
  implicit val blindViasExtractor: PCBPropertyExtractor[BlindVias]         = _.mechanical.blindVias
  implicit val surfaceFinishExtractor: PCBPropertyExtractor[SurfaceFinish] = _.basic.surfaceFinish

  implicit val outerCopperThicknessExtractor: PCBPropertyExtractor[OuterCopperThickness] =
    _.layer.outerCopperThickness
  implicit val innerCopperThicknessExtractor: PCBPropertyExtractor[InnerCopperThickness] =
    _.layer.innerCopperThickness

  implicit val boardHeightOrdering: Ordering[BoardHeight]                   = Ordering.by(_.value)
  implicit val boardWidthOrdering: Ordering[BoardWidth]                     = Ordering.by(_.value)
  implicit val layerOrdering: Ordering[NumberOfLayers]                      = Ordering.by(_.value)
  implicit val thickessOrdering: Ordering[FinalThickness]                   = Ordering.by(_.value)
  implicit val outerCopperThicknessOrdering: Ordering[OuterCopperThickness] = Ordering.by(_.value)
  implicit val innerCopperThicknessOrdering: Ordering[InnerCopperThickness] = Ordering.by(_.value)

  // Even though it's not used, Eq condition still requires an ordering so we need to define one
  implicit val buriedViasOrdering: Ordering[BuriedVias] = Ordering.by(_.value)
  implicit val blindViasOrdering: Ordering[BlindVias]   = Ordering.by(_.value)

}
