package de.fellows.microservices.pcb.model.pcb.props

/** The amount of different tools used for non-plated holes
  */
object NphToolCount {
  val name: String  = "nphToolCount"
  val label: String = "pcb.mechanical.nphToolCount"

  def empty: NphToolCount                             = NphToolCount(Option.empty[Int])
  def apply(value: Int): NphToolCount                 = NphToolCount(Some(value))
  def fromV2(value: Option[BigDecimal]): NphToolCount = NphToolCount(value.map(_.toInt))
}

final case class NphToolCount(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = PhToolCount.name
  val label: String     = PhToolCount.label
  val unit: String      = ""

  override val legacyNames: Option[Seq[String]] = Some(Seq("nph_tool_count"))
}
