package de.fellows.microservices.pcb.model.panel

import de.fellows.microservices.pcb.model.{Millimeters, SquareMm}

/** Represents a PCB panel
  *
  * @param w Width of the panel.
  * @param h Height of the panel.
  * @param paddingX Padding in x axis.
  * @param paddingY Padding in y axis.
  */
private[panel] final case class PanelBound(
    w: Millimeters,
    h: Millimeters,
    padding: DistributionPanelPadding,
    gap: PanelGap
) {
  def croppedW: Millimeters = w - padding.leftInMm - padding.rightInMm
  def croppedH: Millimeters = h - padding.topInMm - padding.bottomInMm

  def cols(pcbW: Millimeters): Int = ((croppedW + gap.widthInMm) / (pcbW + gap.widthInMm)).toInt
  def rows(pcbH: Millimeters): Int = ((croppedH + gap.heightInMm) / (pcbH + gap.heightInMm)).toInt

  def mesh(pcb: PcbSize): PcbMesh = PcbMesh(rows(pcb.heightInMm), cols(pcb.widthInMm))

  def area: SquareMm = w * h

  def dimensions: PanelDimensions = new PanelDimensions(w, h)

  /** Returns the panel without paddings
    */
  def crop: PanelBound = copy(
    w = croppedW,
    h = croppedH,
    padding = DistributionPanelPadding.zero
  )
}
