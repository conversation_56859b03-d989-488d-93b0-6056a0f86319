package de.fellows.microservices.pcb.model

import cats.syntax.eq._
import de.fellows.app.price.api.CapabilitiesApi.{
  CapabilityCheckFailure,
  CapabilityCheckRequest,
  CapabilityCheckResult,
  StackratePricingFailedField
}
import de.fellows.app.price.api.PriceService
import de.fellows.app.supplier
import de.fellows.app.supplier.SupplierService
import de.fellows.luminovo.panel.{Depanelization, ExistingPanel, PanelDetails}
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.panel._
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.props.PCBProperty
import de.fellows.microservices.pcb.{
  CustomStackupError,
  PcbPanelInfo,
  PcbServerError,
  PropertyErrors,
  ScenarioRequestWithPanel,
  UnknownError
}
import de.fellows.utils.FutureUtils
import io.opentelemetry.api.common.{AttributeKey, Attributes}
import io.opentelemetry.api.trace.{Span, StatusCode}
import play.api.Logging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

trait ApiService {

  def manufacturer: ManufacturerApi

  /** Verifies if the customer credentials are correct
    * @return
    */
  def checkCredentials(credentials: Credentials, tenant: String)(implicit
      ec: ExecutionContext
  ): Future[Either[String, Boolean]]

  /** Create quotes for the given PCB and the list of scenarios/quantities
    *
    * @param pcb            PCB
    * @param scenarios      List of scenarios/quantities of PCBs to order
    * @param emsPreferences Preferences of the EMS customer
    * @param credentials    EMS credentials for the PCB supplier API
    */
  def makeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturers: Seq[Manufacturer],
      scenarios: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Seq[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Seq[ManufacturerApiResponse]]

  /** Validate request for API
    *
    * @param pcb            PCB
    * @param quantity       Number of PCB to order
    * @param emsPreferences Preferences of the EMS customer
    */
  def validateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Seq[Manufacturer],
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]]
}

object ApiService extends Logging {
  def calculateWorkingPanelInfo(
      pcb: PCB,
      quantity: Int,
      panelInfo: PcbPanelInfo,
      panelPreferences: PanelPreferences,
      panelConstraints: Option[PanelConstraints]
  ): Either[PcbServerError, CalculatedPanelInfo] =
    calculateWorkingPanelInfo(
      pcbWidth = pcb.properties.basic.boardWidth.value,
      pcbHeight = pcb.properties.basic.boardHeight.value,
      quantity = quantity,
      panelInfo = panelInfo,
      panelPreferences = panelPreferences,
      panelConstraints = panelConstraints
    )

  def calculateWorkingPanelInfo(
      pcbWidth: Millimeters,
      pcbHeight: Millimeters,
      quantity: Int,
      panelInfo: PcbPanelInfo,
      panelPreferences: PanelPreferences,
      panelConstraints: Option[PanelConstraints]
  ): Either[PcbServerError, CalculatedPanelInfo] =
    panelInfo match {
      case PcbPanelInfo.Existing(existing) =>
        Right(
          CalculatedPanelInfo.FromExisting(
            existing = existing,
            requestedPcbs = RequestedPcbs(quantity),
            numberOfPanels = calculateNumberOfPanels(quantity, existing.numberOfPcbs)
          )
        )

      case PcbPanelInfo.NoPanel =>
        generateNewPanelFromPreferences(
          quantity = quantity,
          pcbWidth = pcbWidth,
          pcbHeight = pcbHeight,
          panelPreferences = panelPreferences,
          panelConstraints = panelConstraints
        )

      case PcbPanelInfo.Details(panelDetails) =>
        calculatePanelDetailDistribution(
          quantity = quantity,
          panelDetails = panelDetails,
          pcbWidth = pcbWidth,
          pcbHeight = pcbHeight,
          panelConstraints = panelConstraints
        )
    }

  def calculatePanelDetailDistribution(
      pcb: PCB,
      panelDetails: PanelDetails
  ): Either[PcbServerError, CalculatedPanelInfo] =
    calculatePanelDetailDistribution(
      quantity = panelDetails.quantity,
      panelDetails = panelDetails,
      pcbWidth = pcb.properties.basic.boardWidth.value,
      pcbHeight = pcb.properties.basic.boardHeight.value,
      panelConstraints = None
    )

  def calculatePanelDetailDistribution(
      quantity: Int,
      panelDetails: PanelDetails,
      pcbWidth: Millimeters,
      pcbHeight: Millimeters,
      panelConstraints: Option[PanelConstraints]
  ): Either[PcbServerError, CalculatedPanelInfo] =
    DistributionAlgorithm
      .calculateDistributionFromDetails(
        amount = quantity,
        pcbSize = new PcbSize(pcbWidth, pcbHeight),
        panelDetails = panelDetails,
        panelConstraints = panelConstraints
      ).flatMap { panelDistribution =>
        getCalculatedDistributionInfo(
          quantity,
          panelDistribution,
          panelDetails.depanelization
        )
      }

  def generateNewPanelFromPreferences(
      quantity: Int,
      pcbWidth: Millimeters,
      pcbHeight: Millimeters,
      panelPreferences: PanelPreferences,
      panelConstraints: Option[PanelConstraints]
  ): Either[PcbServerError, CalculatedPanelInfo] = {
    val limited = panelConstraints.fold(
      panelPreferences
    ) { panelConstraints =>
      panelPreferences.limit(panelConstraints)
    }
    DistributionAlgorithm
      .calculate(
        amount = quantity,
        pcbSize = new PcbSize(pcbWidth, pcbHeight),
        preferences = limited
      )
      .flatMap { panelDistribution =>
        getCalculatedDistributionInfo(
          quantity,
          panelDistribution,
          limited.depanelization
        )
      }
  }

  private def getCalculatedDistributionInfo(
      quantity: Int,
      distribution: PanelDistribution,
      depanelization: Depanelization
  ): Either[PcbServerError, CalculatedPanelInfo] =
    if (distribution.mesh.size === 0) {
      Left(UnknownError("Error generating panel distribution"))
    } else {
      Right(
        CalculatedPanelInfo.FromPanelDetails(
          panelDistribution = distribution,
          requestedPcbs = RequestedPcbs(quantity),
          numberOfPanels = calculateNumberOfPanels(quantity, distribution.mesh.size),
          depanelization = depanelization
        )
      )
    }

  private def calculateNumberOfPanels(quantity: Int, pcbsPerPanel: Int): NumberOfPanels = {
    val additionalPanel = if (quantity % pcbsPerPanel =!= 0) 1 else 0
    NumberOfPanels(quantity / pcbsPerPanel + additionalPanel)
  }

  sealed trait CalculatedPanelInfo {
    def requestedPcbs: RequestedPcbs
    def numberOfPanels: NumberOfPanels
    def depanelization: Depanelization
    def totalPcbs: TotalPcbs
  }

  object CalculatedPanelInfo {
    final case class FromPanelDetails(
        panelDistribution: PanelDistribution,
        requestedPcbs: RequestedPcbs,
        numberOfPanels: NumberOfPanels,
        depanelization: Depanelization
    ) extends CalculatedPanelInfo {
      val totalPcbs: TotalPcbs = TotalPcbs(panelDistribution.pcbPerPanel * numberOfPanels.value)
    }

    final case class FromExisting(
        existing: ExistingPanel,
        requestedPcbs: RequestedPcbs,
        numberOfPanels: NumberOfPanels
    ) extends CalculatedPanelInfo {
      override def depanelization: Depanelization = existing.depanelization
      override val totalPcbs: TotalPcbs           = TotalPcbs(existing.numberOfPcbs * numberOfPanels.value)
    }
  }

  /** Make multiple quotes for the given scenarios.
    * Since not all APIs support multiple scenarios/quantities, we need to make a single quote for each scenario.
    */
  def makeMultipleSingleQuotes[R](
      manufacturer: Manufacturer,
      iterable: Seq[R]
  )(
      fn: R => Future[Either[PcbServerError, Seq[QuoteResponse]]]
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    FutureUtils
      .sequentialTraverse(iterable) { it =>
        fn(it).recover {
          case NonFatal(e) =>
            val span = Span.current()
            span.setStatus(StatusCode.ERROR, e.getMessage)
            span.recordException(e)
            e.printStackTrace()
            logger.error(s"Error while making quote ${e.getMessage}")
            Left(UnknownError(s"Error while getting ${manufacturer.name} quote for $it"))
        }
      }
      .map { quoteResponses =>
        val (errors, quotes) = quoteResponses.partitionMap(identity)

        errors.foreach { err =>
          logger.error("Error when making quote: " + err)
        }

        if (quotes.nonEmpty) {
          Right(quotes.flatten)
        } else {
          errors.headOption match {
            case Some(error) => Left(error)
            case None        => Right(Seq.empty)
          }
        }
      }

  private def findProperty(propertyName: String, properties: PCBProperties): Option[PCBProperty] = {
    val propertiesList = properties.toSeq()

    val result =
      propertiesList
        .find(_.fieldName.toLowerCase === propertyName.toLowerCase)
        .orElse {
          propertiesList
            .find(_.legacyNames.exists(_.map(_.toLowerCase).contains(propertyName.toLowerCase)))
        }

    if (result.isEmpty) {
      logger.error(s"property $propertyName not found")
    }

    result
  }

  def createErrorMessage(
      p: PropertyDescriptor,
      fail: CapabilityCheckFailure,
      failedProperty: StackratePricingFailedField,
      conditions: Seq[StackratePricingFailedField]
  ): PropertyError = {
    val filteredConditions = conditions.filter(_.expression.isDefined)
    fail.hint match {
      case Some(value) =>
        PropertyError(
          property = p,
          error = value,
          PropertyErrorKind.Plain(value)
        )

      case None => constructPropertyErrorWithConditions(p, failedProperty, filteredConditions)
    }
  }

  private def constructPropertyError(failedProperty: StackratePricingFailedField): String =
    failedProperty.expression match {
      case Some(value) => s"${failedProperty.value} is not supported because it is ${value}"
      case None        => s"${failedProperty.value} is not supported"
    }

  def constructPropertyErrorWithConditions(
      p: PropertyDescriptor,
      failedProperty: StackratePricingFailedField,
      conditions: Seq[StackratePricingFailedField]
  ): PropertyError = {
    val mainError = constructPropertyError(failedProperty)
    val errorMessage =
      if (conditions.isEmpty) {
        mainError
      } else {
        val conditionErrors = conditions.flatMap { f =>
          f.expression match {
            case Some(value) => Some(s"${f.variable} is ${value}")
            case None        => None
          }
        }
        s"$mainError while ${conditionErrors.mkString(" and ")}"
      }

    PropertyError(
      property = p,
      error = errorMessage,
      PropertyErrorKind.Plain(errorMessage)
    )
  }

  private def convertCapabilityCheck(check: CapabilityCheckResult, properties: PCBProperties): Seq[PropertyError] = {
    val propErrors = check.failures.flatMap { fail =>
      val filteredFailures = fail.variableFailures.dropWhile(_.expression.isEmpty)
      // the first variable failure is the one that failed. We throw away empty leading columns to make the usage of the tables more convenient

      if (filteredFailures.nonEmpty) {
        val failedProperty = filteredFailures.head
        val conditions     = filteredFailures.tail

        findProperty(failedProperty.variableName, properties) match {
          case Some(p) =>
            Some(createErrorMessage(PropertyDescriptor(p), fail, failedProperty, conditions))

          case None =>
            Span.current().addEvent(
              "failed to match property",
              Attributes.of(
                AttributeKey.stringKey("pcb_property"),
                failedProperty.variableName
              )
            )
            logger.error(s"failed to match property ${failedProperty.variableName}")

            val p = GenericPropertyDescriptor(failedProperty.variable, failedProperty.variable)
            Some(createErrorMessage(p, fail, failedProperty, conditions))
        }
      } else {
        None
      }
    }

    propErrors
  }

  def filterApprovedSuppliersWithPricing(
      manufacturers: Seq[Manufacturer],
      suppliers: Seq[supplier.PCBSupplierDescription]
  ): Seq[supplier.PCBSupplierDescription] = {
    val manufacturerIds = manufacturers.flatMap(m => m.locations.map(_.stockLocation) :+ m.supplier).toSet

    suppliers.filter { supplier =>
      supplier.lqReference.exists(lqReference => manufacturerIds.contains(lqReference))
    }
  }

  // We assume that this list of suppliers is already filtered.
  // Each supplier has a valid LQ reference and pricing table defined
  private def runStackrateCapabilityCheck(
      connectedTeam: String,
      pcbOwningTeam: String,
      suppliers: Seq[supplier.PCBSupplierDescription],
      pcb: PCB
  )(
      implicit
      ec: ExecutionContext,
      priceService: PriceService
  ): Future[Seq[((UUID, String), Option[PropertyErrors])]] = {
    val supplierIds = suppliers.flatMap(_.id).toSet
    // 1 - We only need to run the capability checks if there's no custom stackup
    // 2 - We pass along the list of enabled suppliers. No need to run capability checks for suppliers whose
    // results will not be used
    priceService
      ._checkCapabilitiesForSupplier(connectedTeam)
      .invoke(CapabilityCheckRequest(pcb.original, pcbOwningTeam, Some(supplierIds)))
      .map { capabilityCheck =>
        capabilityCheck.flatMap { res =>
          suppliers.find(_.id.contains(res.capability.supplier)).map { supplier =>
            val errors = convertCapabilityCheck(res, pcb.properties)

            (res.capability.supplier, supplier.name) -> (errors match {
              case x if x.nonEmpty => Some(PropertyErrors(x))
              case _               => None
            })
          }
        }
      }
  }

  def stackrateCapabilityCheck(
      connectedTeam: String,
      pcbOwningTeam: String,
      manufacturers: Seq[Manufacturer],
      pcb: PCB
  )(
      implicit
      ec: ExecutionContext,
      priceService: PriceService,
      supplierService: SupplierService
  ): Future[Seq[RequestValidation]] =
    supplierService
      ._getSuppliers(connectedTeam)
      .invoke()
      .flatMap { suppliers =>
        val suppliersWithPricingOrLqReference = filterApprovedSuppliersWithPricing(manufacturers, suppliers)

        if (pcb.properties.layer.customStackUp.value) {
          Future.successful {
            suppliersWithPricingOrLqReference.flatMap { supplier =>
              // We need to find the Manufacturer for the supplier
              val manufacturerLocations: Seq[(Manufacturer, ManufacturerLocation)] =
                manufacturers.flatMap(m =>
                  m.locations.find(ml => supplier.lqReference.contains(ml.stockLocation)).map(m -> _)
                )

              manufacturerLocations.map {
                case (manufacturer, location) =>
                  RequestValidation(
                    api = ManufacturerApi.Stackrate,
                    manufacturer = manufacturer,
                    location = location,
                    validation = Some(CustomStackupError)
                  )
              }
            }
          }
        } else {
          runStackrateCapabilityCheck(connectedTeam, pcbOwningTeam, suppliersWithPricingOrLqReference, pcb).map { res =>
            val referencedManufacturers = res.flatMap { x =>
              val ((supplierId, _), errors) = x
              val supplier                  = suppliers.find(_.id.exists(_ === supplierId))

              // We need to find the Manufacturer for the supplier
              val manufacturerLocations: Seq[(Manufacturer, ManufacturerLocation)] =
                supplier.toSeq.flatMap { supplier =>
                  manufacturers.flatMap(m =>
                    m.locations.find(ml => supplier.lqReference.contains(ml.stockLocation)).map(m -> _)
                  )
                }

              // If there are multiple regions this should work. We'd have multiple suppliers with the same lq reference and different regions.
              // => there would be multiple capability checks in the `res` list.

              val res = manufacturerLocations.map { manufacturerAndlocation =>
                val (manufacturer, location) = manufacturerAndlocation
                RequestValidation(
                  api = ManufacturerApi.Stackrate,
                  manufacturer = manufacturer,
                  location = location,
                  validation = errors
                )
              }

              if (manufacturerLocations.nonEmpty) {
                logger.info(s"cap check for locations: ${manufacturerLocations.map(_._1.name)}: ${res}")
              }

              res
            }

            referencedManufacturers
          }
        }
      }

  def getSupplierCapabilityErrors(
      manufacturer: Manufacturer,
      requestValidations: Map[UUID, Seq[RequestValidation]]
  ): Either[PcbServerError, Unit] = {
    val validationResults = requestValidations.getOrElse(manufacturer.supplier, Seq.empty)
    val errors            = validationResults.flatMap(_.validation).flatMap(PcbServerError.propertyErrors).distinct

    if (errors.nonEmpty) {
      Left(PropertyErrors(errors))
    } else {
      Right(())
    }
  }

  def quoteResponse(
      api: ManufacturerApi,
      manufacturer: Manufacturer,
      quotes: Seq[QuoteResponse]
  ): Seq[ManufacturerApiResponse] = {
    val byLocation = quotes.groupBy(_.offerResponse.location)

    manufacturer.locations.map { location =>
      val quoteResponse = byLocation.getOrElse(location, Seq.empty)
      ManufacturerApiResponse(
        api = ManufacturerApiWithInformation(api),
        manufacturer = manufacturer,
        location = location,
        response = Right(ManufacturerQuote(quoteResponse))
      )
    }
  }

  def generalSupplierError(
      api: ManufacturerApi,
      manufacturer: Manufacturer,
      error: PcbServerError
  ): Seq[ManufacturerApiResponse] =
    manufacturer.locations.map { location =>
      ManufacturerApiResponse(
        api = ManufacturerApiWithInformation(api),
        manufacturer = manufacturer,
        location = location,
        response = Left(error)
      )
    }

  def validateCustomStackup(properties: PCBProperties): Option[CustomStackupError.type] =
    Option.when(properties.layer.customStackUp.value)(
      CustomStackupError
    )
}

abstract class SingleManufacturerApiService extends ApiService with Logging {

  def api: ManufacturerApi

  def doMakeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      scenarios: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Option[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]]

  def doValidateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]]

  override final def manufacturer: ManufacturerApi = api

  override final def makeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturers: Seq[Manufacturer],
      scenarios: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Seq[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Seq[ManufacturerApiResponse]] =
    manufacturers.find(m => m.name === api.name).map { manufacturer =>
      doMakeQuotes(
        tenant = tenant,
        pcb = pcb,
        manufacturer = manufacturer,
        scenarios = scenarios,
        existingOffers = existingOffers,
        emsPreferences = emsPreferences,
        credentials = credentials.find(c => c.manufacturer === api),
        requestValidations = requestValidations
      )
        .map {
          case Left(error)   => ApiService.generalSupplierError(api, manufacturer, error)
          case Right(quotes) => ApiService.quoteResponse(api, manufacturer, quotes)
        }
    }.getOrElse(Future.successful(Seq()))

  override final def validateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Seq[Manufacturer],
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]] =
    manufacturer
      .find(m => m.name === api.name)
      .map { manufacturer =>
        if (pcb.properties.layer.customStackUp.value) {
          Future.successful(
            manufacturer.locations.map { location =>
              RequestValidation(
                api = api,
                manufacturer = manufacturer,
                location = location,
                validation = Some(CustomStackupError)
              )
            }
          )
        } else {
          doValidateRequest(
            team,
            pcb,
            manufacturer,
            quantity,
            emsPreferences
          )
        }
      }.getOrElse(Future.successful(Seq()))

}

case class ApiServiceWithManufacturers(
    service: ApiService,
    api: ManufacturerApi,
    manufacturers: Seq[Manufacturer]
)
