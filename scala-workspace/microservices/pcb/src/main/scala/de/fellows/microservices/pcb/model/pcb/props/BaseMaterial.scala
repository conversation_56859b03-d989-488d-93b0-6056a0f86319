package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.BaseMaterial.FR4
import de.fellows.ems.pcb.api.specification.{BaseMaterial => SpecBaseMaterial}
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object BaseMaterial {
  val name: String  = "baseMaterial"
  val label: String = "pcb.layer.baseMaterial"

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for baseMaterial is FR4
    */
  def default: BaseMaterial                                = BaseMaterial(FR4)
  def apply(value: Option[SpecBaseMaterial]): BaseMaterial = value.map(BaseMaterial(_)).getOrElse(default)
  type BaseMaterialCapability = SetCapability[BaseMaterial, SpecBaseMaterial]
}

final case class BaseMaterial(override val value: SpecBaseMaterial)
    extends PCBRequiredProperty[SpecBaseMaterial] {
  val fieldName: String = BaseMaterial.name
  val label: String     = BaseMaterial.label
  val unit: String      = ""
}
