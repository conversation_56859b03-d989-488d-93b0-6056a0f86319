package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props

/** Number of layers
  *
  * Name of param in Wurth API: layers
  */
sealed trait WurthNumberOfLayers {
  val value: Int
}

private case object <PERSON><PERSON><PERSON><PERSON> extends WurthNumberOfLayers {
  override val value: Int = 1
}

private case object Two<PERSON><PERSON><PERSON> extends WurthNumberOfLayers {
  override val value: Int = 2
}

private case object <PERSON><PERSON><PERSON><PERSON> extends WurthNumberOfLayers {
  override val value: Int = 4
}

private case object <PERSON><PERSON><PERSON><PERSON> extends WurthNumberOfLayers {
  override val value: Int = 6
}

private case object <PERSON><PERSON><PERSON><PERSON> extends WurthNumberOfLayers {
  override val value: Int = 8
}

private case object TenLay<PERSON> extends WurthNumberOfLayers {
  override val value: Int = 10
}

private case object Twelve<PERSON>ay<PERSON> extends WurthNumberOfLayers {
  override val value: Int = 12
}

private case object FourteenLay<PERSON> extends WurthNumberOfLayers {
  override val value: Int = 14
}

private object <PERSON>rth<PERSON>umberOfLayers {

  /** Converts from StackRate layers to Wurth layers
    */
  def converter(value: props.NumberOfLayers): Option[WurthNumberOfLayers] =
    value.value match {
      case 1  => Some(OneLayer)
      case 2  => Some(TwoLayers)
      case 4  => Some(FourLayers)
      case 6  => Some(SixLayers)
      case 8  => Some(EightLayers)
      case 10 => Some(TenLayers)
      case 12 => Some(TwelveLayers)
      case 14 => Some(FourteenLayers)
      case _  => None
    }

  def fromWurth(value: Int): WurthNumberOfLayers = value match {
    case 1  => OneLayer
    case 2  => TwoLayers
    case 4  => FourLayers
    case 6  => SixLayers
    case 8  => EightLayers
    case 10 => TenLayers
    case 12 => TwelveLayers
    case 14 => FourteenLayers
    case _  => TwoLayers
  }
}
