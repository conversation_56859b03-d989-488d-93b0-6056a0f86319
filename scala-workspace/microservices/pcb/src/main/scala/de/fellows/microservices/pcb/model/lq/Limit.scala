package de.fellows.microservices.pcb.model.lq

import play.api.libs.json._
import play.api.libs.json.Reads._
import play.api.libs.functional.syntax._

sealed trait Limit {
  val `type`: String
}

object Limit {
  implicit val limitReads: Reads[Limit] = (
    (JsPath \ "type").read[String] and
      (JsPath \ "number").readNullable[Int]
  )((`type`, number) =>
    `type` match {
      case "Never" => Never
      case "Days"  => Days(number.getOrElse(0))
    }
  )

  implicit val limitWrites: Writes[Limit] = {
    case Never      => Json.obj("type" -> "Never")
    case Days(days) => Json.obj("type" -> "Days", "number" -> days)
  }

  implicit val limitFormat: Format[Limit] = Format(limitReads, limitWrites)

  case object Never extends Limit {
    override val `type`: String = "Never"
  }

  case class Days(number: Int) extends Limit {
    override val `type`: String = "Days"
  }
}
