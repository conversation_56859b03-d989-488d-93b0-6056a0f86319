package de.fellows.microservices.pcb.utils

import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.{PCBPreviews, PCBV2Properties, PCBV2Specification}
import de.fellows.app.assembly.commons.ProjectType
import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.Side.Both
import de.fellows.ems.pcb.api.specification.SoldermaskColor.Green
import de.fellows.ems.pcb.api.specification.SurfaceFinish.Enig
import de.fellows.microservices.pcb.ServiceError
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.props._
import de.fellows.microservices.pcb.model.stackrate.StackRateAPI.AssemblyId
import de.fellows.utils.model.PCBId

import java.time.Instant
import java.util.UUID

object StubPCB {

  def get(pcbId: PCBId, token: String): Either[ServiceError, PCB] =
    Right(getPcb(pcbId))

  def withRandomId: PCB = getPcb(PCBId(UUID.randomUUID()))

  def getPcb(pcbId: PCBId): PCB = {
    val properties = PCBProperties(
      basic = BasicBoardProperties(
        boardWidth = BoardWidth(149.27f),
        boardHeight = BoardHeight(151.41f),
        silkscreenSide = SilkscreenSide(Both),
        silkscreenColor = SilkscreenColor.default,
        hardGold = HardGold.no,
        hardGoldArea = HardGoldArea.default,
        surfaceFinish = SurfaceFinish(Enig),
        enigThickness = EnigThickness(Some(2)),
        soldermaskSide = SoldermaskSide(Side.None),
        soldermaskColor = SoldermaskColor(Green),
        minTraceWidth = MinTraceWidth.empty,
        outerTraceWidth = OuterTraceWidth.empty,
        innerTraceWidth = InnerTraceWidth.empty,
        exposedCopperAreaTop = ExposedCopperAreaTop.empty,
        exposedCopperAreaBottom = ExposedCopperAreaBottom.empty,
        copperClearance = CopperClearance.empty,
        outerCopperClearance = OuterCopperClearance.empty,
        innerCopperClearance = InnerCopperClearance.empty,
        soldermaskDam = SoldermaskDam.empty,
        soldermaskClearance = SoldermaskClearance.empty
      ),
      advanced = AdvancedBoardProperties(
        ipcA600Class = IPCA600Class.default,
        eTest = ETest.yes,
        pressFit = PressFit.no,
        impedanceTested = ImpedanceTested.no,
        impedanceTolerance = ImpedanceTolerance.none,
        peelableMask = PeelableMask.default,
        edgeMetalization = EdgeMetalization.no,
        itarCompliance = ItarCompliance.no,
        maxXOutsAllowed = MaxXOutsAllowed.empty,
        carbonPrint = CarbonPrint.default,
        halogenFree = HalogenFree.no,
        ctiClass = CtiClass.none,
        captonTape = CaptonTape.default,
        halfCutPlatedVias = HalfCutPlatedVias.no,
        ecobond = Ecobond.no,
        numberOfLines = NumberOfLines.empty
      ),
      layer = LayerStackProperties(
        layerstackType = LayerstackType.rigid,
        ulLayerStack = ULLayerStack.no,
        uLMarkingType = ULMarkingType.noMarking,
        numberOfLayers = NumberOfLayers(4),
        finalThickness = FinalThickness(1.55f),
        baseMaterial = BaseMaterial.default,
        outerCopperThickness = OuterCopperThickness(35),
        innerCopperThickness = InnerCopperThickness(35),
        minOuterLayerStructure = MinOuterLayerStructure(0.1),
        minInnerLayerStructure = MinInnerLayerStructure(0.1),
        tgValue = TGValue(135),
        customStackUp = CustomStackUp.no,
        numberOfPrepregs = NumberOfPrepregs.empty,
        numberOfLaminationCycles = NumberOfLaminationCycles.empty
      ),
      mechanical = MechanicalProperties(
        minViaDiameter = MinViaDiameter(0.4),
        viaFillingType = ViaFillingType(None),
        blindVias = BlindVias.no,
        buriedVias = BuriedVias.no,
        blindViaCount = BlindViaCount.empty,
        buriedViaCount = BuriedViaCount.empty,
        chamfering = Chamfering(None),
        outlineLength = OutlineLength.empty,
        aspectRatio = AspectRatio.empty,
        phCount = PhCount.empty,
        phToolCount = PhToolCount.empty,
        phMinDiameter = PhMinDiameter.empty,
        phMaxDiameter = PhMaxDiameter.empty,
        nphCount = NphCount.empty,
        nphToolCount = NphToolCount.empty,
        nphMinDiameter = NphMinDiameter.empty,
        nphMaxDiameter = NphMaxDiameter.empty,
        phAnnularRing = PhAnnularRing.empty
      ),
      markings = MarkingProperties(
        dateCodeMarking = DateCodeMarking.noMarking
      )
    )

    val id = UUID.randomUUID()
    PCB(
      id = pcbId,
      assemblyId = AssemblyId(id),
      name = Some("test"),
      properties = properties,
      orderId = Some("order 1"),
      previews = None,
      hash = Some("test-hash"),
      files = Seq.empty,
      projectType = ProjectType.WithFiles,
      original = PCBV2(
        id = pcbId.value,
        name = Some("test"),
        assembly = id,
        description = None,
        created = Instant.now,
        files = None,
        filesLocked = false,
        lifecycles = Seq(),
        orderId = None,
        outline = None,
        specifications = Seq(
          PCBV2Specification(
            id = UUID.randomUUID(),
            name = "specification",
            previews = PCBPreviews(None, None),
            settings = PCBV2Properties.EMPTY,
            hash = "test-hash",
            status = None,
            changes = None
          )
        ),
        properties = PCBV2Properties.EMPTY,
        customer = None,
        projectType = ProjectType.WithFiles
      )
    )
  }
}
