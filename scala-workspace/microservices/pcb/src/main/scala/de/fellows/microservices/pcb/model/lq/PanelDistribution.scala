package de.fellows.microservices.pcb.model.lq

import de.fellows.luminovo.LuminovoJson
import de.fellows.microservices.pcb.model.panel.{
  DistributionPanelPadding,
  PanelDimensions,
  PanelGap,
  PanelPreferences,
  PcbMesh,
  PcbSize,
  Rectangle,
  WastedArea
}
import play.api.libs.json._

final case class PanelGenerationInput(
    amount: Int,
    pcbSize: PcbSize,
    preferences: PanelPreferences
)

object PanelGenerationInput {

  implicit val format: Format[PanelGenerationInput] = {
    implicit val config: JsonConfiguration          = LuminovoJson.configuration
    implicit val formatRectangle: Format[Rectangle] = Json.format[Rectangle]

    Json.format[PanelGenerationInput]
  }
}

final case class PanelGenerationResult(
    panel: PanelDimensions,
    waste: WastedArea,
    pcbPerPanel: Int,
    mesh: PcbMesh,
    padding: DistributionPanelPadding,
    gap: PanelGap,
    pcbIsRotated: Boolean
)

object PanelGenerationResult {
  implicit val writes: Writes[PanelGenerationResult] = Json.writes
}
