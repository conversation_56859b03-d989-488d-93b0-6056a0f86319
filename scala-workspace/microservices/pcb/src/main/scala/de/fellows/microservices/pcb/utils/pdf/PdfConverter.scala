package de.fellows.microservices.pcb.utils.pdf

import cats.syntax.eq._
import de.fellows.ems.pcb.api.PCBV2Api.PCBV2File
import de.fellows.ems.pcb.api.specification.LayerstackType
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.{Micrometers, Millimeters}
import de.fellows.microservices.pcb.model.panel.{PanelDistribution, PanelRenderer}
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.props.OutlineLength
import de.fellows.utils.templating.PDFGenerator
import org.apache.batik.svggen.SVGGraphics2D
import org.apache.batik.transcoder.image.{ImageTranscoder, PNGTranscoder}
import org.apache.batik.transcoder.{SVGAbstractTranscoder, TranscoderInput, TranscoderOutput}
import play.api.Logging

import java.awt.Color
import java.io.{File, FileOutputStream, StringReader, StringWriter}
import java.nio.file.{Files, Path}
import scala.util.Using
import de.fellows.ems.pcb.api.specification.CtiClass
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.PCBV2LayerStackProperties
import de.fellows.ems.pcb.api.specification.units.LengthUnit.Millimeter
import de.fellows.microservices.pcb.model.stackrate.StackRatePCB

object PdfConverter extends Logging {

  def generateSpecification(
      pcb: PCB,
      pcbPreviews: PcbTempPreviews
  ): Option[File] =
    generate(
      templatePath = Path.of(getClass.getResource("/pcb-specification.jinja2").toURI),
      pcb = pcb,
      pcbPreviews = pcbPreviews,
      panel = None
    )

  def generatePanel(
      pcb: PCB,
      panel: CalculatedPanelInfo,
      pcbPreviews: PcbTempPreviews
  ): Option[File] =
    generate(
      templatePath = Path.of(getClass.getResource("/pcb-panel.jinja2").toURI),
      pcb = pcb,
      pcbPreviews = pcbPreviews,
      panel = Some(panel)
    )

  private def generate(
      templatePath: Path,
      pcb: PCB,
      pcbPreviews: PcbTempPreviews,
      panel: Option[CalculatedPanelInfo]
  ): Option[File] = {
    val generator     = new PDFGenerator()
    val stylesBaseUri = getClass.getResource("/templates/").toURI.toURL

    val sources = Map(
      "logo_png"            -> getClass.getResource("/luminovo_logo.png"),
      "noto_sans_ttf"       -> getClass.getResource("/fonts/noto_sans.ttf"),
      "poppins_regular_ttf" -> getClass.getResource("/fonts/poppins_regular.ttf"),
      "poppins_bold_ttf"    -> getClass.getResource("/fonts/poppins_bold.ttf")
    ).map {
      case (key, value) =>
        (key, Path.of(value.toURI))
    }

    val (panelPicture, panelLegend) = panel match {
      case Some(p: CalculatedPanelInfo.FromPanelDetails) =>
        val panelPicture   = createPanelPicture(p.panelDistribution).getAbsolutePath
        val legendResource = getClass.getResource("/pcb-specification-legend.png").toURI
        val panelLegend    = Path.of(legendResource).toFile.getAbsolutePath

        (Some(panelPicture), Some(panelLegend))

      case Some(_: CalculatedPanelInfo.FromExisting) =>
        // There's not enough information to generate a panel from an ExistingPanel
        // instead, we show a picture of the PCB (if one exists)
        (pcbPreviews.front.map(_.getAbsolutePath), None)

      case None => (None, None)
    }

    val pcbVariables = flatten(pcbToMap(pcb, pcbPreviews, panel, panelPicture, panelLegend))
    val variables    = pcbVariables ++ sources
    val pdf = generator
      .withTemplate(templatePath)
      .withVariables(variables)
      .withWorkingDirectory(Files.createTempDirectory("templating"))
      .simpleBuild(stylesBaseUri.toString)

    pdf
  }

  private def createPanelPicture(panel: PanelDistribution): File = {
    val file = Files.createTempFile("panel", ".jpg").toFile
    val svg  = PanelRenderer.render(panel.items)
    writePNG(svg, file)
    file
  }

  private def writePNG(svg: SVGGraphics2D, path: File): Unit = {
    val t = new PNGTranscoder()
    t.addTranscodingHint(SVGAbstractTranscoder.KEY_HEIGHT, 300f)
    t.addTranscodingHint(ImageTranscoder.KEY_BACKGROUND_COLOR, Color.WHITE)
    t.addTranscodingHint(ImageTranscoder.KEY_FORCE_TRANSPARENT_WHITE, true)

    Using.resource(new StringWriter()) { s =>
      svg.stream(s)
      Using.resource(new StringReader(s.toString)) { r =>
        val input: TranscoderInput = new TranscoderInput(r)
        Using.resource(new FileOutputStream(path)) { ostream =>
          val output = new TranscoderOutput(ostream)
          t.transcode(input, output)
        }
      }
    }
  }

  protected def flatten(properties: Map[String, Any]): Map[String, Any] =
    properties.flatMap {
      case (key, value: Map[String, Any] @unchecked)    => flatten(value).map { case (k, v) => s"${key}_$k" -> v }
      case (key, value: Option[Any]) if value.isDefined => Map(key -> value.get)
      case (key, value: Option[Any]) if value.isEmpty   => Map(key -> null)
      case (key, value)                                 => Map(key -> value)
    }

  private def pcbToMap(
      pcb: PCB,
      pcbPreviews: PcbTempPreviews,
      panel: Option[CalculatedPanelInfo],
      panelPicture: Option[String],
      panelLegend: Option[String]
  ): Map[String, Any] = {
    val customStackupFile = pcb.original.files.flatMap(_.find(_.fileType.fileType === LayerConstants.STACK_UP))
    val mergedV2 = pcb.original.specifications.headOption match {
      case Some(specification) =>
        StackRatePCB.merge(specification.settings, pcb.original.properties)
      case None =>
        pcb.original.properties
    }

    Map(
      "basic"      -> basicBoardPropertiesToMap(pcb.properties.basic),
      "advanced"   -> advancedBoardPropertiesToMap(pcb.properties.advanced),
      "layer"      -> layerStackPropertiesToMap(pcb.properties.layer, mergedV2.layerStack, customStackupFile),
      "mechanical" -> mechanicalPropertiesToMap(pcb.properties.mechanical),
      "panel"      -> panelPropertiesToMap(panel, panelPicture, panelLegend),
      "previews" -> Map(
        "front" -> pcbPreviews.front.map(_.getAbsolutePath),
        "rear"  -> pcbPreviews.rear.map(_.getAbsolutePath)
      )
    )
  }

  private def panelPropertiesToMap(
      panel: Option[CalculatedPanelInfo],
      preview: Option[String],
      panelLegend: Option[String]
  ): Map[String, Any] =
    Map(
      "preview" -> preview,
      "legend"  -> panelLegend
    ) ++ (panel match {
      case Some(p: CalculatedPanelInfo.FromExisting) => Map(
          "width"          -> p.existing.panelWidth,
          "height"         -> p.existing.panelHeight,
          "number_of_pcbs" -> p.existing.numberOfPcbs
        )

      case Some(p: CalculatedPanelInfo.FromPanelDetails) => Map(
          "width"              -> p.panelDistribution.panel.widthInMm,
          "height"             -> p.panelDistribution.panel.heightInMm,
          "area"               -> p.panelDistribution.panel.area,
          "number_of_pcbs"     -> p.panelDistribution.pcbPerPanel,
          "number_of_pcb_cols" -> p.panelDistribution.mesh.columns,
          "number_of_pcb_rows" -> p.panelDistribution.mesh.rows,
          "padding_top"        -> p.panelDistribution.padding.topInMm,
          "padding_bottom"     -> p.panelDistribution.padding.bottomInMm,
          "padding_left"       -> p.panelDistribution.padding.leftInMm,
          "padding_right"      -> p.panelDistribution.padding.rightInMm,
          "gap_x"              -> p.panelDistribution.gap.x,
          "gap_y"              -> p.panelDistribution.gap.y
        )

      case None => Map.empty
    })

  private def basicBoardPropertiesToMap(pcb: BasicBoardProperties): Map[String, Any] =
    Map(
      "width"                   -> pcb.boardWidth.value,
      "height"                  -> pcb.boardHeight.value,
      "area"                    -> pcb.area,
      "silkscreenSide"          -> pcb.silkscreenSide.value.toString,
      "silkscreenColor"         -> pcb.silkscreenColor.value.value,
      "hardGold"                -> pcb.hardGold.value,
      "hardGoldArea"            -> pcb.hardGoldArea.value,
      "surfaceFinish"           -> pcb.surfaceFinish.value.value,
      "enigThickness"           -> pcb.enigThickness.value,
      "soldermaskSide"          -> pcb.soldermaskSide.value.toString,
      "soldermaskColor"         -> pcb.soldermaskColor.value.value,
      "traceWidth"              -> pcb.minTraceWidth.value,
      "exposedCopperAreaTop"    -> pcb.exposedCopperAreaTop.value,
      "exposedCopperAreaBottom" -> pcb.exposedCopperAreaBottom.value,
      "copperClearance"         -> pcb.copperClearance.value,
      "soldermaskDam"           -> pcb.soldermaskDam.value,
      "soldermaskClearance"     -> pcb.soldermaskClearance.value
    )

  private def advancedBoardPropertiesToMap(pcb: AdvancedBoardProperties): Map[String, Any] =
    Map(
      "ipcA600Class"      -> pcb.ipcA600Class.value.entryName,
      "eTest"             -> pcb.eTest.value,
      "pressFit"          -> pcb.pressFit.value,
      "impedanceTested"   -> pcb.impedanceTested.value,
      "peelableMask"      -> pcb.peelableMask.value.toString,
      "edgeMetalization"  -> pcb.edgeMetalization.value,
      "maxXOutsAllowed"   -> pcb.maxXOutsAllowed.value,
      "halogenFree"       -> pcb.halogenFree.value,
      "carbonPrint"       -> pcb.carbonPrint.value,
      "ctiClass"          -> pcb.ctiClass.value.map(CtiClass.label),
      "captonTape"        -> pcb.captonTape.value.toString,
      "ecobond"           -> pcb.ecobond.value,
      "numberOfLines"     -> pcb.numberOfLines.value,
      "halfCutPlatedVias" -> pcb.halfCutPlatedVias.value
    )

  private def layerStackPropertiesToMap(
      properties: LayerStackProperties,
      v2Properties: PCBV2LayerStackProperties,
      customStackupFile: Option[PCBV2File]
  ): Map[String, Any] = {
    val (minInnerLayerStructure, innerCopperThickness): (Option[Millimeters], Option[Micrometers]) =
      if (properties.numberOfLayers.value >= 4) {
        (properties.minInnerLayerStructure.value, Some(properties.innerCopperThickness.value))
      } else {
        (None, None)
      }

    Map(
      "pcbType"        -> stringifyPcbType(properties.layerstackType.value),
      "ulLayerStack"   -> properties.ulLayerStack.value,
      "numberOfLayers" -> properties.numberOfLayers.value,
      "finalThickness" -> v2Properties.finalThickness.map(
        _.to(Millimeter)
      ), // use continuous value instead of enum in PCB model
      "baseMaterial"             -> properties.baseMaterial.value.value,
      "outerCopperThickness"     -> properties.outerCopperThickness.value,
      "innerCopperThickness"     -> innerCopperThickness,
      "minOuterLayerStructure"   -> properties.minOuterLayerStructure.value,
      "minInnerLayerStructure"   -> minInnerLayerStructure,
      "tgValue"                  -> properties.tgValue.value,
      "uLMarkingType"            -> properties.uLMarkingType.value.entryName,
      "customStackUpFileName"    -> customStackupFile.map(_.name),
      "numberOfPrepregs"         -> properties.numberOfPrepregs.value,
      "numberOfLaminationCycles" -> properties.numberOfLaminationCycles.value
    )
  }

  private def mechanicalPropertiesToMap(pcb: MechanicalProperties): Map[String, Any] =
    Map(
      "minViaDiameter"   -> pcb.minViaDiameter.value,
      "viaFillingType"   -> pcb.viaFillingType.value.value,
      "blindVias"        -> pcb.blindVias.value,
      "buriedVias"       -> pcb.buriedVias.value,
      "chamfering"       -> pcb.chamfering.value.value,
      OutlineLength.name -> pcb.outlineLength.value,
      "aspectRation"     -> pcb.aspectRatio.value,
      "phCount"          -> pcb.phCount.value,
      "phToolCount"      -> pcb.phToolCount.value,
      "phMinDiameter"    -> pcb.minViaDiameter.value.orElse(pcb.phMinDiameter.value),
      "phMaxDiameter"    -> pcb.phMaxDiameter.value,
      "nphCount"         -> pcb.nphCount.value,
      "nphToolCount"     -> pcb.nphToolCount.value,
      "nphMinDiameter"   -> pcb.nphMinDiameter.value,
      "nphMaxDiameter"   -> pcb.nphMaxDiameter.value,
      "totalHoleCount"   -> pcb.totalHoleCount,
      "minHoleDiameter"  -> pcb.minHoleDiameter,
      "maxHoleDiameter"  -> pcb.maxHoleDiameter
    )

  private def stringifyPcbType(value: LayerstackType): String =
    value match {
      case LayerstackType.Rigid     => "Rigid"
      case LayerstackType.Flex      => "Flex"
      case LayerstackType.Ims       => "IMS"
      case LayerstackType.RigidFlex => "Rigid-Flex"
    }
}
