package de.fellows.microservices.pcb.model.ibr.props

import de.fellows.microservices.pcb.model.Micrometers

trait IbrCopperThickness {
  val initialValue: Micrometers
  val value: String
}

case class Mm0(override val initialValue: Micrometers = 0) extends IbrCopperThickness {
  override val value: String = "0"
}
case class Mm18(override val initialValue: Micrometers = 18) extends IbrCopperThickness {
  override val value: String = "18"
}
case class Mm35(override val initialValue: Micrometers = 35) extends IbrCopperThickness {
  override val value: String = "35"
}
case class Mm50(override val initialValue: Micrometers = 50) extends IbrCopperThickness {
  override val value: String = "50"
}
case class Mm70(override val initialValue: Micrometers = 70) extends IbrCopperThickness {
  override val value: String = "70"
}
case class Mm105(override val initialValue: Micrometers = 105) extends IbrCopperThickness {
  override val value: String = "105"
}
case class Mm140(override val initialValue: Micrometers = 140) extends IbrCopperThickness {
  override val value: String = "140"
}
case class Mm210(override val initialValue: Micrometers = 210) extends IbrCopperThickness {
  override val value: String = "210"
}
case class Mm240(override val initialValue: Micrometers = 240) extends IbrCopperThickness {
  override val value: String = "240"
}
case class Mm400(override val initialValue: Micrometers = 400) extends IbrCopperThickness {
  override val value: String = "400"
}
case class Mm800(override val initialValue: Micrometers = 800) extends IbrCopperThickness {
  override val value: String = "800"
}
case class Mm999(override val initialValue: Micrometers = 999) extends IbrCopperThickness {
  override val value: String = "999"
}
