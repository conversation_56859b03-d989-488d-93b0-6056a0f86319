package de.fellows.microservices.pcb.model.lq

import de.fellows.luminovo.LuminovoJson
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerLocation, PropertyError}
import de.fellows.microservices.pcb.{ApiNotSetUpErrorKind, PanelErrorKind}
import de.fellows.utils.CurrencyCode
import de.fellows.utils.model.{PCBId, ShareId}
import play.api.libs.json.{Format, Json, JsonConfiguration, Writes}
import sttp.model.StatusCode
import de.fellows.luminovo.panel.{ExistingPanel, PanelDetails}
import de.fellows.luminovo.sourcing.SourcingScenarioId

/** PCB offer for Luminovo
  * @param pcbId ID of the PCB
  * @param responses Responses from the manufacturers
  */
final case class PcbOfferInput(
    pcbId: PCBId,
    pcbHash: Option[String],
    responses: Seq[ManufacturerStatus]
)

object PcbOfferInput {
  implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
  implicit val writes: Writes[PcbOfferInput]        = Json.writes[PcbOfferInput]
}

/** PCB offer for Luminovo
  *
  * @param api Manufacturer api information
  * @param manufacturer Manufacturer
  * @param location  Manufacturer location
  * @param status The manufacturer offer response. see [[PcbOfferStatus]]
  */
final case class ManufacturerStatus(
    api: ManufacturerApiWithInformation,
    manufacturer: Manufacturer,
    location: ManufacturerLocation,
    status: PcbOfferStatus
)

object ManufacturerStatus {
  implicit val writes: Writes[ManufacturerStatus] = s =>
    Json.obj(
      "status"                      -> s.status,
      "supplier_and_stock_location" -> s.location.stockLocation,
      "api"                         -> Json.toJson(s.api)
    )
}

/** The possible responses from the manufacturer
  */
sealed trait PcbOfferStatus

object PcbOfferStatus {

  /** The offer request was successful
    * @param offers The offers
    */
  case class Success(offers: Seq[PcbOffer]) extends PcbOfferStatus

  /** The offer request failed because either the capability check failed
    * or the manufacturer api returned an error
    */
  case class SpecificationUnsupported(errors: Seq[PropertyError]) extends PcbOfferStatus
  case class PanelError(error: PanelErrorKind)                    extends PcbOfferStatus

  case object MissingCredentials                      extends PcbOfferStatus
  case class ApiNotSetUp(error: ApiNotSetUpErrorKind) extends PcbOfferStatus
  case object InvalidCredentials                      extends PcbOfferStatus

  case object CustomStackup extends PcbOfferStatus

  final case class StackratePricingFailedField(
      field: String,
      value: String,
  )

  object StackratePricingFailedField {
    implicit val writes: Writes[StackratePricingFailedField] = {
      implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
      Json.writes[StackratePricingFailedField]
    }

    def from(variableFailure: de.fellows.app.price.api.CapabilitiesApi.StackratePricingFailedField): StackratePricingFailedField =
      StackratePricingFailedField(
        variableFailure.variable,
        variableFailure.value,
      )
  }

  final case class StackratePricingMessage(
      name: String,
      message: String,
      fields: Seq[StackratePricingFailedField],
      hints: Seq[String]
  )

  object StackratePricingMessage {
    implicit val writes: Writes[StackratePricingMessage] = {
      implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
      Json.writes[StackratePricingMessage]
    }

    def from(variableFailure: de.fellows.microservices.pcb.StackratePricingMessage): StackratePricingMessage =
      StackratePricingMessage(
        variableFailure.name,
        variableFailure.message,
        variableFailure.fields.map(StackratePricingFailedField.from),
        variableFailure.hints
      )

  }

  final case class StackratePricingErrors(errors: Seq[StackratePricingMessage])        extends PcbOfferStatus
  object StackratePricingErrors {
    implicit val writes: Writes[StackratePricingErrors] = {
      implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
      Json.writes[StackratePricingErrors]
    }

    def from(errors: de.fellows.microservices.pcb.StackratePricingErrors): StackratePricingErrors =
      StackratePricingErrors(errors.errors.map(StackratePricingMessage.from))
  }
  final case class StackratePricingBreaks(pricingBreaks: Seq[StackratePricingMessage]) extends PcbOfferStatus

  object StackratePricingBreaks {
    implicit val writes: Writes[StackratePricingBreaks] = {
      implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
      Json.writes[StackratePricingBreaks]
    }

    def from(pricingBreaks: de.fellows.microservices.pcb.StackratePricingBreaks): StackratePricingBreaks =
      StackratePricingBreaks(pricingBreaks.pricingBreaks.map(StackratePricingMessage.from))
  }

  /** The offer request failed because we could not read the response or reach the manufacturer api
    */
  case class ApiError(statusCode: StatusCode, errorMessage: String) extends PcbOfferStatus

  private val typeFieldName = "type"
  implicit val writes: Writes[PcbOfferStatus] = {
    case s: Success =>
      Json.obj(
        "offers"      -> Json.toJson(s.offers),
        typeFieldName -> "Success"
      )

    case e: SpecificationUnsupported =>
      Json.obj(
        "errors"      -> Json.toJson(e.errors),
        typeFieldName -> "SpecificationUnsupported"
      )

    case e: PanelError =>
      Json.obj(
        "error"       -> e.error,
        typeFieldName -> "PanelError"
      )

    case MissingCredentials =>
      Json.obj(typeFieldName -> "MissingCredentials")

    case InvalidCredentials =>
      Json.obj(typeFieldName -> "InvalidCredentials")

    case e: ApiNotSetUp =>
      Json.obj(
        "error"       -> e.error,
        typeFieldName -> "ApiNotSetUp"
      )

    case StackratePricingBreaks(pricingBreaks) =>
      Json.obj(
        "pricing_breaks" -> Json.toJson(pricingBreaks),
        typeFieldName -> "StackratePricingBreaks"
      )

    case StackratePricingErrors(errors) =>
      Json.obj(
        "errors" -> Json.toJson(errors),
        typeFieldName -> "StackratePricingErrors"
      )

    case ApiError(statusCode, errorMessage) =>
      Json.obj(
        "status_code" -> statusCode.code,
        "error"       -> errorMessage,
        typeFieldName -> "ApiError"
      )

    case CustomStackup =>
      Json.obj(typeFieldName -> "CustomStackup")
  }
}

final case class Cost(
    amount: BigDecimal,
    description: Option[String] = None
)

final case class PcbOffer(
    pricePoints: Seq[PricePointInput],
    currency: CurrencyCode,
    validUntil: Option[String],
    offerNumber: String,
    offerValidity: String,
    priceType: PriceType,
    piecesPerUnit: Int,
    notes: String,
    offerUrl: Option[String],
    oneTimeCosts: Option[Seq[Cost]],
    sourcingScenarioId: SourcingScenarioId,
    panel: Option[PanelDetails],
    panelSpecification: Option[PanelSpecification],
    sharedPcbId: Option[ShareId]
)

object PcbOffer {
  implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
  implicit val writes: Writes[PcbOffer]             = Json.writes[PcbOffer]
}

sealed trait PanelSpecification
object PanelSpecification {
  implicit val writes: Writes[PanelSpecification] = Writes {
    case p: PanelDetailsSpecification =>
      Json.obj(
        "type" -> "PanelDetails",
        "data" -> p
      )
    case p: ExistingPanelSpecification =>
      Json.obj(
        "type" -> "Existing",
        "data" -> p
      )
  }

  final case class PanelDetailsSpecification(panelDetails: PanelDetails) extends PanelSpecification

  object PanelDetailsSpecification {
    implicit val writes: Writes[PanelDetailsSpecification] = Writes { specification =>
      implicitly[Writes[PanelDetails]].writes(specification.panelDetails)
    }
  }

  final case class ExistingPanelSpecification(existing: ExistingPanel) extends PanelSpecification

  object ExistingPanelSpecification {
    implicit val writes: Writes[ExistingPanelSpecification] =
      Writes { specification =>
        implicitly[Writes[ExistingPanel]].writes(specification.existing)
      }
  }
}

object Cost {
  implicit val f: Format[Cost] = Json.format[Cost]
}
