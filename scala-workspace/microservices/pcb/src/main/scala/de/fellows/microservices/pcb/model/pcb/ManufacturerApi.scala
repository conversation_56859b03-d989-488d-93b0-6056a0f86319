package de.fellows.microservices.pcb.model.pcb

import cats.Eq
import enumeratum._

/** The name of manufacturer is used extensively while exchanging data with Luminovo. It should be unique and
  * should not be changed. It's also case-insensitive.
  */
sealed trait ManufacturerApi extends EnumEntry {
  val name: String
  val api: String = entryName
}

object Manufacturer<PERSON><PERSON> extends Enum[ManufacturerApi] with PlayInsensitiveJsonEnum[ManufacturerApi] {
  override val values: IndexedSeq[ManufacturerApi] = findValues

  case object Wuerth extends ManufacturerApi {
    override val name: String = "Würth Elektronik"
  }

  case object BetaLayout extends ManufacturerA<PERSON> {
    override val name: String = "Beta Layout"
  }

  case object SafePcb extends ManufacturerA<PERSON> {
    override val name: String = "Safe-PCB"
  }

  case object IBRRingler extends ManufacturerApi {
    override val name: String = "IBR Leiterplatten"
  }

  case object APCT extends ManufacturerApi {
    override val name: String = "APCT"
  }

  case object Gatema extends ManufacturerApi {
    override val name: String = "Gatema"
  }

  case object Alba extends ManufacturerApi {
    override val name: String = "Alba"
  }

  case object MultiCB extends ManufacturerApi {
    override val name: String = "Multi-CB"
  }

  case object Stackrate extends ManufacturerApi {
    override val name: String = "Stackrate"
  }

  def byManufacturer(manufacturer: Manufacturer): Option[ManufacturerApi] =
    byName(manufacturer.name)

  def byName(name: String): Option[ManufacturerApi] =
    name match {
      case Wuerth.name     => Some(Wuerth)
      case BetaLayout.name => Some(BetaLayout)
      case SafePcb.name    => Some(SafePcb)
      case IBRRingler.name => Some(IBRRingler)
      case APCT.name       => Some(APCT)
      case Gatema.name     => Some(Gatema)
      case Alba.name       => Some(Alba)
      case _               => None
    }

  implicit val eq: Eq[ManufacturerApi] = Eq.fromUniversalEquals
}
