package de.fellows.microservices.pcb.model.pcb

import de.fellows.utils.Region
import play.api.libs.json.{<PERSON><PERSON>, Writes}

import java.util.UUID

case class Manufacturer(
    supplier: UUID,
    locations: Seq[ManufacturerLocation],
    name: String
)

object Manufacturer {
  implicit val writes: Writes[Manufacturer] = Json.writes[Manufacturer]
}

case class ManufacturerLocation(
    stockLocation: UUID,
    region: Region
)

object ManufacturerLocation {
  implicit val writes: Writes[ManufacturerLocation] = Json.writes[ManufacturerLocation]
}
