package de.fellows.microservices.pcb.model.apct

import cats.data.EitherT
import cats.syntax.traverse._
import cats.instances.future._
import de.fellows.generated.integrations.apct.{QuoteOrder_DataResponse, WebServiceSoap12Bindings}
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.apct.ApctApiError.{DeserializationError, InvalidResponse}
import de.fellows.microservices.pcb.model.apct.ApctApiService.parseResponse
import de.fellows.microservices.pcb.{
  CredentialsMissingError,
  PanelError,
  PanelErrorKind,
  PcbPanelInfo,
  PcbServerError,
  ScenarioRequestWithPanel,
  Server<PERSON><PERSON><PERSON>,
  Third<PERSON>arty<PERSON><PERSON><PERSON>,
  Unknown<PERSON><PERSON><PERSON>
}
import de.fellows.microservices.pcb.model.{ApiService, Offer, RequestValidation, SingleManufacturerApiService}
import de.fellows.microservices.pcb.model.lq.{
  ChangeStatus,
  Credentials,
  ExistingOffer,
  ManufacturerApiWithInformation,
  OfferResponse,
  PriceType,
  QuoteResponse
}
import de.fellows.microservices.pcb.model.panel.EmsPreferences
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerApi, PCB}
import de.fellows.utils.CurrencyCode
import io.opentelemetry.api.common.{AttributeKey, Attributes}
import io.opentelemetry.api.trace.{Span, StatusCode => TraceStatusCode}
import play.api.Logging
import play.api.libs.json.Json
import scalaxb.{DataRecord, HttpClientsAsync, SoapClientsAsync}
import sttp.client3.{asString, basicRequest}
import sttp.model.{StatusCode, Uri}

import java.net.URI
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try
import scala.util.control.NonFatal
import scala.xml.Node

class ApctApiService(host: String, asyncBackend: AsyncBackend)
    extends SingleManufacturerApiService with Logging {

  override def api: ManufacturerApi = ManufacturerApi.APCT

  lazy val baseUri = new URI(host)

  val service: WebServiceSoap12Bindings =
    new WebServiceSoap12Bindings with SoapClientsAsync with HttpClientsAsync {
      override def httpClient: HttpClient = new HttpClient {
        override def request(
            requestBody: String,
            address: URI,
            headers: Map[String, String]
        )(implicit ec: ExecutionContext): Future[String] =
          basicRequest
            .post(Uri(baseUri))
            .headers(headers)
            .body(requestBody)
            .response(asString)
            .send(asyncBackend)
            .flatMap {
              _.body match {
                case Right(value) => Future.successful(value)
                case Left(value)  => Future.failed(new Exception(value))
              }
            }
      }
    }

  override def doMakeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      scenarios: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Option[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    (for {
      credentials <- EitherT.fromOption[Future](
        credentials,
        CredentialsMissingError("Credentials missing for APCT API")
      )

      _ <- EitherT.fromEither[Future](
        ApiService.getSupplierCapabilityErrors(manufacturer, requestValidations)
      )

      quotes <- EitherT {
        ApiService.makeMultipleSingleQuotes(
          manufacturer,
          scenarios.filterNot(_.changeStatus == ChangeStatus.Unchanged)
        ) { scenario =>
          makeSingleQuote(pcb, manufacturer, scenario, emsPreferences, credentials)
        }
      }

    } yield quotes).value

  private def makeSingleQuote(
      pcb: PCB,
      manufacturer: Manufacturer,
      request: ScenarioRequestWithPanel,
      preferences: EmsPreferences,
      credentials: Credentials
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    (for {
      request <- EitherT.fromEither {
        toApctPcbRequest(
          pcb = pcb,
          quantity = request.quantity,
          panelInfo = request.panelInfo,
          sourcingScenarioId = request.sourcingScenarioId,
          preferences = preferences
        )
      }
      response <- EitherT(makeRequest(manufacturer, request, credentials))
    } yield response).value

  def makeRequest(
      manufacturer: Manufacturer,
      request: ApctRequest,
      credentials: Credentials
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] = {
    val orderData = request.toAPCTformat

    logger.info(s"APCT request data1=${orderData.Data1} data4=${orderData.Data4}")

    service.service.quoteOrder_Data(
      sPwd = Some(credentials.password),
      data1 = orderData.Data1,
      data2 = orderData.Data2,
      data3 = orderData.Data3,
      data4 = orderData.Data4
    )
      .map { response =>
        parseResponse(response) match {
          case Left(error) =>
            val span = Span.current()
            span.addEvent(
              "failed to read apct api response",
              Attributes.of(
                AttributeKey.stringKey("error.message"),
                error.message
              )
            )
            Left(ThirdPartyError("Could not read APCT API response", StatusCode.InternalServerError))
          case Right(value) =>
            manufacturer
              .locations
              .headOption
              .map { location =>
                val (errors, responses) = value.partitionMap { quote =>
                  val numberOfPanels = request.calculatedPanelInfo.numberOfPanels
                  val price = quote.unitOfMeasurement match {
                    case ApctUnitOfMeasurement.Each  => Right((quote.price * quote.quantity).toFloat)
                    case ApctUnitOfMeasurement.Array => Right((quote.price * numberOfPanels.value).toFloat)
                    case ApctUnitOfMeasurement.Panel | ApctUnitOfMeasurement.Lot =>
                      val msg = s"Unable to handle ${quote.unitOfMeasurement} unit returned by APCT API"
                      logger.error(msg)
                      Left(UnknownError(msg))
                  }

                  price.map { price =>
                    val response = OfferResponse(
                      api = ManufacturerApiWithInformation(api),
                      manufacturer = manufacturer,
                      location = location,
                      quantity = numberOfPanels.value, // the quote quantity is the total number of PCBs
                      offers = Seq(
                        Offer(
                          price = price,
                          productionDays = quote.days,
                          currency = Some(CurrencyCode.USD),
                          oneTimeCosts = quote.otherCharges.collect { case x if x > 0 => x.toFloat }
                        )
                      ),
                      priceType = PriceType.ContractPrice
                    )

                    QuoteResponse(
                      offerResponse = response,
                      quoteId = quote.rfq,
                      url = None,
                      calculatedPanelInfo = request.calculatedPanelInfo,
                      existingOfferId = None,
                      sourcingScenarioId = request.sourcingScenarioId,
                      sharedPcbId = None
                    )
                  }
                }

                val grouped = responses
                  .groupBy(q => (q.quoteId, q.offerResponse.quantity))
                  .values.map { quotes =>
                    quotes.head.copy(
                      offerResponse = quotes.head.offerResponse.copy(
                        offers = quotes.flatMap(_.offerResponse.offers)
                      )
                    )
                  }.toSeq

                if (errors.nonEmpty) {
                  logger.error(s"Errors while processing APCT quotes: ${errors}")
                }

                val dbg = grouped.map { r =>
                  Json.obj(
                    "quote"    -> r.quoteId,
                    "quantity" -> r.offerResponse.quantity,
                    "offers" -> (r.offerResponse.offers.map { rr =>
                      Json.obj(
                        "price"          -> rr.price,
                        "productionDays" -> rr.productionDays,
                        "currency"       -> rr.currency
                      )
                    })
                  )
                }

                logger.info(s"APCT API response: ${Json.toJson(dbg)}")

                (errors.headOption, grouped) match {
                  case (Some(error), grouped) if grouped.isEmpty => Left(error)
                  case (_, grouped)                              => Right(grouped)
                }
              }
              .getOrElse {
                Left(ServerError("Couldn't convert offer response"))
              }
        }
      }
      .recover { e =>
        val span = Span.current()
        span.recordException(e)
        span.setStatus(TraceStatusCode.ERROR, e.getMessage)
        logger.error(s"Error in APCT API: ${e.getMessage}")
        e.printStackTrace()
        Left(ThirdPartyError("Could not read APCT API response", StatusCode.ServiceUnavailable))
      }
  }

  private def toApctPcbRequest(
      pcb: PCB,
      quantity: Int,
      panelInfo: PcbPanelInfo,
      sourcingScenarioId: SourcingScenarioId,
      preferences: EmsPreferences
  ): Either[PcbServerError, ApctRequest] =
    for {
      calculatedPanelInfo <- ApiService.calculateWorkingPanelInfo(
        pcb = pcb,
        quantity = quantity,
        panelInfo = panelInfo,
        panelPreferences = preferences.panelPreferencesOrDefault,
        panelConstraints = Some(panelConstraints)
      )

      fromPanelDetails <- calculatedPanelInfo match {
        case p: CalculatedPanelInfo.FromPanelDetails => Right(p)
        case _: CalculatedPanelInfo.FromExisting     => Left(PanelError(PanelErrorKind.ExistingPanelNotSupported))
      }

      pcbRequest <- ApctPcbRequest.validateAndConvert(pcb, fromPanelDetails.panelDistribution)
    } yield {
      // APCT's Array is our Panel
      val unitOfMeasurement = ApctUnitOfMeasurement.Array

      ApctRequest(
        pcbRequest,
        unitOfMeasurement,
        calculatedPanelInfo,
        sourcingScenarioId
      )
    }

  /** Verifies if the customer credentials are correct
    *
    * @return
    */
  override def checkCredentials(
      credentials: Credentials,
      tenant: String
  )(implicit ec: ExecutionContext): Future[Either[String, Boolean]] =
    // TODO: Right now there is no way to check the credentials without "creating" new quotes
    // Since Zollner will be the only customer to use this API, we can just return true and hope for the best
    Future.successful(Right(true))

  /** Validate request for API
    *
    * @param pcb            PCB
    * @param quantity       Number of PCB to order
    * @param emsPreferences Preferences of the EMS customer
    */
  override def doValidateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]] =
    // TODO: implement a proper capability check once we have the values from APCT
    // for now, a very bare bones validation is done
    Future.successful {
      manufacturer.locations.map { location =>
        RequestValidation(
          api = api,
          manufacturer = manufacturer,
          location = location,
          validation =
            toApctPcbRequest(
              pcb,
              quantity,
              panelInfo = PcbPanelInfo.NoPanel,
              SourcingScenarioId(UUID.randomUUID()), // dummy value for sourcing scenario id
              emsPreferences
            ).left.toOption
        )
      }
    }
}

object ApctApiService extends Logging {
  def parseResponse(response: QuoteOrder_DataResponse): Either[ApctApiError, Seq[ApctQuote]] = {
    logger.info(s"Parsing APCT response ${response}")
    (for {
      result <- response.QuoteOrder_DataResult
      dataNode <- result.any match {
        case DataRecord(_, _, elem: xml.Elem) => (elem \ "NewDataSet").headOption
        case _                                => None
      }
    } yield parseDataSet(dataNode))
      .toRight(InvalidResponse("NewDataSet not present in APCT response")).flatten
  }

  private[apct] def parseDataSet(dataSet: Node): Either[ApctApiError, Seq[ApctQuote]] = {
    logger.info(s"Parsing APCT NewDataSet response ${dataSet}")

    (dataSet \ "Table").toVector.traverse { entry =>
      try {
        val rfq                   = (entry \ "RFQ").text.trim
        val quantity              = (entry \ "Quantity").text.trim
        val price                 = (entry \ "Price").text.trim
        val days                  = (entry \ "Days").text.trim.toInt
        val unitOfMeasurementText = (entry \ "Unit").text
        val otherCharges          = Try(BigDecimal((entry \ "OtherChg").text.trim)).toOption

        val unitOfMeasurement = ApctUnitOfMeasurement.withName(unitOfMeasurementText)

        Right(
          ApctQuote(
            rfq = rfq,
            quantity = BigDecimal(quantity),
            price = BigDecimal(price),
            unitOfMeasurement = unitOfMeasurement,
            days = days,
            otherCharges = otherCharges
          )
        )
      } catch {
        case NonFatal(e) =>
          val msg = s"Failed to parse APCT response: ${e.getMessage}"
          logger.error(msg)

          Left(DeserializationError(msg))
      }
    }
      .flatMap { quotes =>
        if (quotes.isEmpty) {
          Left(InvalidResponse("No offers returned"))
        } else {
          Right(quotes)
        }
      }
  }
}

sealed trait ApctApiError {
  val message: String
}

object ApctApiError {
  case class InvalidResponse(message: String)      extends ApctApiError
  case class DeserializationError(message: String) extends ApctApiError
}
