package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

object OuterTraceWidth {
  val name: String  = "outerTraceWidth"
  val label: String = "pcb.basic.outerTraceWidth"

  def empty: OuterTraceWidth                     = OuterTraceWidth(None)
  def apply(value: Millimeters): OuterTraceWidth = OuterTraceWidth(Some(value))
}

final case class OuterTraceWidth(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = OuterTraceWidth.name
  val label: String     = OuterTraceWidth.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("outer_trace_width"))
}
