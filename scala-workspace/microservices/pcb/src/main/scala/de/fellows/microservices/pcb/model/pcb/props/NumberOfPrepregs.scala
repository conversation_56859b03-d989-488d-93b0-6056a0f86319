package de.fellows.microservices.pcb.model.pcb.props

object NumberOfPrepregs {
  val name: String  = "numberOfPrepregs"
  val label: String = "pcb.board.advanced.numberOfPrepregs"

  def empty: NumberOfPrepregs                             = NumberOfPrepregs(Option.empty[Int])
  def apply(value: Int): NumberOfPrepregs                 = NumberOfPrepregs(Some(value))
  def fromV2(value: Option[BigDecimal]): NumberOfPrepregs = NumberOfPrepregs(value.map(_.toInt))
}

final case class NumberOfPrepregs(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = NumberOfPrepregs.name
  val label: String     = NumberOfPrepregs.label
  val unit: String      = ""
} 