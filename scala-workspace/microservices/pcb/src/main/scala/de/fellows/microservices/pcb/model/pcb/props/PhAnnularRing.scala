package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

object PhAnnularRing {
  val name: String  = "phAnnularRing"
  val label: String = "pcb.basic.phAnnularRing"

  def empty: PhAnnularRing = PhAnnularRing(None)

  def apply(value: Millimeters): PhAnnularRing = PhAnnularRing(Some(value))
}

final case class PhAnnularRing(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = PhAnnularRing.name
  val label: String     = PhAnnularRing.label
}
