package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

/** The largest non-plated hole diameter in mm
  */
object NphMaxDiameter {
  val name: String  = "nphMaxDiameter"
  val label: String = "pcb.mechanical.nphMaxDiameter"

  def empty: NphMaxDiameter                     = NphMaxDiameter(None)
  def apply(value: Millimeters): NphMaxDiameter = NphMaxDiameter(Some(value))
}

final case class NphMaxDiameter(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = NphMaxDiameter.name
  val label: String     = NphMaxDiameter.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("nph_max_size"))
}
