package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

object InnerCopperClearance {
  val name: String  = "innerCopperClearance"
  val label: String = "pcb.basic.innerCopperClearance"

  def empty: InnerCopperClearance                     = InnerCopperClearance(None)
  def apply(value: Millimeters): InnerCopperClearance = InnerCopperClearance(Some(value))
}

final case class InnerCopperClearance(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = InnerCopperClearance.name
  val label: String     = InnerCopperClearance.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("inner_copper_clearance"))
}
