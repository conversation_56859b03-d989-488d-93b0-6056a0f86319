package de.fellows.microservices.pcb.model.pcb

import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.ems.pcb.api.specification.units.AreaUnit.SquareMillimeter
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.PCBV2Properties
import de.fellows.ems.pcb.api.specification.SilkscreenColor.White
import de.fellows.ems.pcb.api.specification.{SurfaceFinish => PcbSurfaceFinish}
import de.fellows.ems.pcb.api.specification.{
  Side,
  SilkscreenColor => PCBSilkscreenColor,
  SoldermaskColor => PCBSdColor,
  SurfaceFinish => PCBSurfaceFinish
}
import de.fellows.microservices.pcb.model.Millimeters
import de.fellows.microservices.pcb.model.pcb.props._
import de.fellows.microservices.pcb.model.stackrate.StackRatePCB
import de.fellows.ems.pcb.api.specification.units.LengthUnit.{Micrometer, Millimeter}
import de.fellows.ems.pcb.api.specification.units.TemperatureUnit.Celsius

final case class BasicBoardProperties(
    boardWidth: BoardWidth,
    boardHeight: BoardHeight,
    silkscreenSide: SilkscreenSide,
    silkscreenColor: SilkscreenColor,
    hardGold: HardGold,
    hardGoldArea: HardGoldArea,
    surfaceFinish: SurfaceFinish,
    enigThickness: EnigThickness,
    soldermaskSide: SoldermaskSide,
    soldermaskColor: SoldermaskColor,
    minTraceWidth: MinTraceWidth,
    outerTraceWidth: OuterTraceWidth,
    innerTraceWidth: InnerTraceWidth,
    exposedCopperAreaTop: ExposedCopperAreaTop,
    exposedCopperAreaBottom: ExposedCopperAreaBottom,
    copperClearance: CopperClearance,
    outerCopperClearance: OuterCopperClearance,
    innerCopperClearance: InnerCopperClearance,
    soldermaskDam: SoldermaskDam,
    soldermaskClearance: SoldermaskClearance
) {
  def area: Millimeters = boardWidth.value * boardHeight.value

  def toSeq: Seq[PCBProperty] =
    this.productIterator.toSeq.filter(_.isInstanceOf[PCBProperty]).map(_.asInstanceOf[PCBProperty])
}

final case class AdvancedBoardProperties(
    ipcA600Class: IPCA600Class,
    eTest: ETest,
    pressFit: PressFit,
    impedanceTested: ImpedanceTested,
    impedanceTolerance: ImpedanceTolerance,
    peelableMask: PeelableMask,
    edgeMetalization: EdgeMetalization,
    itarCompliance: ItarCompliance,
    maxXOutsAllowed: MaxXOutsAllowed,
    carbonPrint: CarbonPrint,
    halogenFree: HalogenFree,
    ctiClass: CtiClass,
    captonTape: CaptonTape,
    halfCutPlatedVias: HalfCutPlatedVias,
    ecobond: Ecobond,
    numberOfLines: NumberOfLines
) {
  def toSeq: Seq[PCBProperty] =
    this.productIterator.toSeq.filter(_.isInstanceOf[PCBProperty]).map(_.asInstanceOf[PCBProperty])
}

final case class LayerStackProperties(
    layerstackType: LayerstackType,
    ulLayerStack: ULLayerStack,
    uLMarkingType: ULMarkingType,
    numberOfLayers: NumberOfLayers,
    finalThickness: FinalThickness,
    baseMaterial: BaseMaterial,
    outerCopperThickness: OuterCopperThickness,
    innerCopperThickness: InnerCopperThickness,
    minOuterLayerStructure: MinOuterLayerStructure,
    minInnerLayerStructure: MinInnerLayerStructure,
    tgValue: TGValue,
    numberOfPrepregs: NumberOfPrepregs,
    numberOfLaminationCycles: NumberOfLaminationCycles,
    customStackUp: CustomStackUp
) {
  def toSeq: Seq[PCBProperty] =
    this.productIterator.toSeq.filter(_.isInstanceOf[PCBProperty]).map(_.asInstanceOf[PCBProperty])
}

final case class MechanicalProperties(
    minViaDiameter: MinViaDiameter,
    viaFillingType: ViaFillingType,
    blindVias: BlindVias,
    buriedVias: BuriedVias,
    blindViaCount: BlindViaCount,
    buriedViaCount: BuriedViaCount,
    chamfering: Chamfering,
    outlineLength: OutlineLength,
    aspectRatio: AspectRatio,
    phCount: PhCount,
    phToolCount: PhToolCount,
    phMinDiameter: PhMinDiameter,
    phMaxDiameter: PhMaxDiameter,
    nphCount: NphCount,
    nphToolCount: NphToolCount,
    nphMinDiameter: NphMinDiameter,
    nphMaxDiameter: NphMaxDiameter,
    phAnnularRing: PhAnnularRing
) {
  def toSeq: Seq[PCBProperty] =
    this.productIterator.toSeq.filter(_.isInstanceOf[PCBProperty]).map(_.asInstanceOf[PCBProperty])

  def totalHoleCount: Option[Int] = (phCount.value, nphCount.value) match {
    case (Some(ph), Some(nph)) => Some(ph + nph)
    case (Some(ph), None)      => Some(ph)
    case (None, Some(nph))     => Some(nph)
    case _                     => None
  }

  def minHoleDiameter: Option[Millimeters] =
    (minViaDiameter.value, phMinDiameter.value, nphMinDiameter.value) match {
      case (Some(min), _, _)           => Some(min)
      case (None, Some(ph), Some(nph)) => Some(ph min nph)
      case (None, Some(ph), None)      => Some(ph)
      case (None, None, Some(nph))     => Some(nph)
      case _                           => None
    }

  def maxHoleDiameter: Option[Millimeters] = (phMaxDiameter.value, nphMaxDiameter.value) match {
    case (Some(ph), Some(nph)) => Some(ph max nph)
    case (Some(ph), None)      => Some(ph)
    case (None, Some(nph))     => Some(nph)
    case _                     => None
  }
}

final case class MarkingProperties(
    dateCodeMarking: DateCodeMarking
)

/** An intermediate PCB representation with new encoded properties
  */
final case class PCBProperties(
    basic: BasicBoardProperties,
    advanced: AdvancedBoardProperties,
    layer: LayerStackProperties,
    mechanical: MechanicalProperties,
    markings: MarkingProperties
) {

  def boardWidth(value: Millimeters): PCBProperties  = copy(basic = basic.copy(boardWidth = BoardWidth(value)))
  def boardHeight(value: Millimeters): PCBProperties = copy(basic = basic.copy(boardHeight = BoardHeight(value)))
  def surfaceFinish(value: PCBSurfaceFinish): PCBProperties =
    copy(basic = basic.copy(surfaceFinish = SurfaceFinish(value)))
  def silkscreenSide(value: Side): PCBProperties = copy(basic = basic.copy(silkscreenSide = SilkscreenSide(value)))
  def silkscreenColor(value: PCBSilkscreenColor): PCBProperties =
    copy(basic = basic.copy(silkscreenColor = SilkscreenColor(value)))

  def hardGold(value: Boolean): PCBProperties = copy(basic = basic.copy(hardGold = HardGold(value)))

  def soldermaskSide(value: Side): PCBProperties = copy(basic = basic.copy(soldermaskSide = SoldermaskSide(value)))
  def soldermaskColor(value: PCBSdColor): PCBProperties =
    copy(basic = basic.copy(soldermaskColor = SoldermaskColor(value)))

  def numberOfLayers(value: NumberOfLayers): PCBProperties = copy(layer = layer.copy(numberOfLayers = value))
  def withCustomStackUp: PCBProperties                     = copy(layer = layer.copy(customStackUp = CustomStackUp.yes))

  def toSeq(): Seq[PCBProperty] =
    basic.toSeq ++ advanced.toSeq ++ layer.toSeq ++ mechanical.toSeq
}

object PCBProperties {

  def apply(pcb: PCBV2): PCBProperties = {
    val right            = pcb.properties
    val left             = pcb.specifications.headOption.map(_.settings).getOrElse(right)
    val mergedProperties = StackRatePCB.merge(left, right)
    val customStackUp    = CustomStackUp(StackRatePCB.hasCustomStackUpFile(pcb.files))

    apply(mergedProperties, customStackUp)
  }

  def apply(pcb: PCBV2Properties, customStackUp: CustomStackUp): PCBProperties = {
    // this particular bit of code must be removed as soon as we implement a proper way to keep the PCB model
    // completely in line with user settings (e.g. by removing properties that should not be there)
    val numberOfLayers = NumberOfLayers(pcb.layerStack.layercount.map(_.toInt))

    val (
      modifiedInnerLayerStructure,
      modifiedInnerCopperThickness,
      modifiedInnerTraceWidth,
      modifiedInnerCopperClearance
    ) =
      if (numberOfLayers.value >= 4) {
        val innerCopperThickness =
          InnerCopperThickness(pcb.layerStack.innerCopperThickness.map(_.to(Micrometer).intValue))
        val innerLayoutStructure = MinInnerLayerStructure(pcb.layerStack.minInnerLayerStructure.map(_.to(Millimeter)))
        val innerTraceWidth      = InnerTraceWidth(pcb.board.basic.innerTraceWidth.map(_.to(Millimeter)))
        val innerCopperClearance = InnerCopperClearance(pcb.board.basic.innerCopperClearance.map(_.to(Millimeter)))

        (
          innerLayoutStructure,
          innerCopperThickness,
          innerTraceWidth,
          innerCopperClearance
        )
      } else {
        (
          MinInnerLayerStructure.empty,
          InnerCopperThickness.empty,
          InnerTraceWidth.empty,
          InnerCopperClearance.empty
        )
      }

    val modifiedEnigThickness = pcb.board.basic.surfaceFinish match {
      case Some(PcbSurfaceFinish.Enig) => EnigThickness(pcb.board.basic.enigThickness.map(_.to(Micrometer).intValue))
      case _                           => EnigThickness.empty
    }

    val modifiedCarbonPrint = (pcb.board.advanced.carbonPrintSide, pcb.board.advanced.carbonPrint) match {
      case (None, Some(print)) => CarbonPrint.fromBoolean(print)
      case (side, _)          => CarbonPrint(side)
    }

    val ecobond = Ecobond(pcb.board.advanced.ecobond)
    val modifiedNumberOfLines = if (ecobond.value) {
      NumberOfLines.empty
    } else {
      NumberOfLines.fromV2(pcb.board.advanced.numberOfLines)
    }

    val basic = BasicBoardProperties(
      boardWidth = BoardWidth(pcb.board.basic.boardWidth.map(_.to(Millimeter))),
      boardHeight = BoardHeight(pcb.board.basic.boardHeight.map(_.to(Millimeter))),
      silkscreenSide = SilkscreenSide(pcb.board.basic.silkscreenSide),
      silkscreenColor = SilkscreenColor(pcb.board.basic.silkscreenColor.getOrElse(White)),
      hardGold = HardGold(pcb.board.basic.hardGold.getOrElse(false)),
      hardGoldArea = HardGoldArea(pcb.board.basic.hardGoldArea.map(_.to(SquareMillimeter))),
      surfaceFinish = SurfaceFinish(pcb.board.basic.surfaceFinish),
      enigThickness = modifiedEnigThickness,
      soldermaskSide = SoldermaskSide(pcb.board.basic.soldermaskSide),
      soldermaskColor = SoldermaskColor(pcb.board.basic.soldermaskColor),
      minTraceWidth = MinTraceWidth(pcb.board.basic.traceWidth.map(_.to(Millimeter))),
      outerTraceWidth = OuterTraceWidth(pcb.board.basic.outerTraceWidth.map(_.to(Millimeter))),
      innerTraceWidth = modifiedInnerTraceWidth,
      exposedCopperAreaTop = ExposedCopperAreaTop(pcb.board.basic.exposedCopperAreaTop.map(_.to(SquareMillimeter))),
      exposedCopperAreaBottom =
        ExposedCopperAreaBottom(pcb.board.basic.exposedCopperAreaBottom.map(_.to(SquareMillimeter))),
      copperClearance = CopperClearance(pcb.board.basic.copperClearance.map(_.to(Millimeter))),
      outerCopperClearance = OuterCopperClearance(pcb.board.basic.outerCopperClearance.map(_.to(Millimeter))),
      innerCopperClearance = modifiedInnerCopperClearance,
      soldermaskDam = SoldermaskDam(pcb.board.basic.soldermaskDam.map(_.to(Millimeter))),
      soldermaskClearance = SoldermaskClearance(pcb.board.basic.soldermaskClearance.map(_.to(Millimeter)))
    )
    val advanced = AdvancedBoardProperties(
      ipcA600Class = IPCA600Class(pcb.board.advanced.ipc600Class),
      eTest = ETest(pcb.board.advanced.eTest),
      pressFit = PressFit(pcb.board.advanced.pressFit),
      impedanceTested = ImpedanceTested(pcb.board.advanced.impedanceTested),
      impedanceTolerance = ImpedanceTolerance(pcb.board.advanced.impedanceTolerance),
      peelableMask = PeelableMask(pcb.board.advanced.peelableMask),
      edgeMetalization = EdgeMetalization(pcb.board.advanced.edgeMetalization),
      itarCompliance = ItarCompliance(pcb.board.advanced.itar),
      maxXOutsAllowed = MaxXOutsAllowed.fromV2(pcb.board.advanced.maxXOutsAllowed),
      carbonPrint = modifiedCarbonPrint,
      halogenFree = HalogenFree(pcb.board.advanced.halogenFree),
      ctiClass = CtiClass(pcb.board.advanced.ctiClass),
      captonTape = CaptonTape(pcb.board.advanced.captonTape),
      halfCutPlatedVias = HalfCutPlatedVias(pcb.board.advanced.halfCutPlatedVias),
      ecobond = Ecobond(pcb.board.advanced.ecobond),
      numberOfLines = modifiedNumberOfLines
    )
    val layer = LayerStackProperties(
      layerstackType = LayerstackType(pcb.layerStack.layerstackType),
      ulLayerStack = ULLayerStack(pcb.layerStack.ulLayerStack),
      uLMarkingType = ULMarkingType(pcb.layerStack.ulMarkingType),
      numberOfLayers = numberOfLayers,
      finalThickness = FinalThickness(pcb.layerStack.finalThickness.map(_.to(Millimeter))),
      baseMaterial = BaseMaterial(pcb.layerStack.baseMaterial),
      outerCopperThickness = OuterCopperThickness(pcb.layerStack.outerCopperThickness.map(_.to(Micrometer).intValue)),
      innerCopperThickness = modifiedInnerCopperThickness,
      minOuterLayerStructure = MinOuterLayerStructure(pcb.layerStack.minOuterLayerStructure.map(_.to(Millimeter))),
      minInnerLayerStructure = modifiedInnerLayerStructure,
      tgValue = TGValue(pcb.layerStack.tgValue.map(_.to(Celsius))),
      numberOfPrepregs = NumberOfPrepregs.fromV2(pcb.layerStack.numberOfPrepregs),
      numberOfLaminationCycles = NumberOfLaminationCycles.fromV2(pcb.layerStack.numberOfLaminationCycles),
      customStackUp = customStackUp
    )
    val mechanical = MechanicalProperties(
      minViaDiameter = MinViaDiameter(pcb.mechanical.minViaDiameter.map(_.to(Millimeter))),
      viaFillingType = ViaFillingType(pcb.mechanical.viaFilling),
      blindVias = BlindVias(pcb.mechanical.blindVias),
      buriedVias = BuriedVias(pcb.mechanical.buriedVias),
      blindViaCount = BlindViaCount(pcb.mechanical.blindViaCount),
      buriedViaCount = BuriedViaCount(pcb.mechanical.buriedViaCount),
      chamfering = Chamfering(pcb.mechanical.chamfering),
      outlineLength = OutlineLength(pcb.mechanical.outlineLength.map(_.to(Millimeter))),
      aspectRatio = AspectRatio(pcb.mechanical.aspectRatio),
      phCount = PhCount.fromV2(pcb.mechanical.phCount),
      phToolCount = PhToolCount.fromV2(pcb.mechanical.phToolCount),
      phMinDiameter = PhMinDiameter(pcb.mechanical.phMinSize.map(_.to(Millimeter))),
      phMaxDiameter = PhMaxDiameter(pcb.mechanical.phMaxSize.map(_.to(Millimeter))),
      nphCount = NphCount.fromV2(pcb.mechanical.nphCount),
      nphToolCount = NphToolCount.fromV2(pcb.mechanical.nphToolCount),
      nphMinDiameter = NphMinDiameter(pcb.mechanical.nphMinSize.map(_.to(Millimeter))),
      nphMaxDiameter = NphMaxDiameter(pcb.mechanical.nphMaxSize.map(_.to(Millimeter))),
      phAnnularRing = PhAnnularRing(pcb.mechanical.phAnnularRing.map(_.to(Millimeter)))
    )

    val markings = MarkingProperties(
      dateCodeMarking = DateCodeMarking(pcb.markings.dateCode)
    )

    PCBProperties(
      basic,
      advanced,
      layer,
      mechanical,
      markings
    )
  }
}
