package de.fellows.microservices.pcb.model.gatema

import cats.data.EitherT
import cats.syntax.traverse._
import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import de.fellows.microservices.pcb.model.gatema.Gatema.panelConstraints
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.panel.{EmsPreferences, PanelInfo, Tenants}
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerApi, ManufacturerLocation, PCB, Quantity}
import de.fellows.microservices.pcb.model.{ApiService, Offer, RequestValidation, SingleManufacturerApiService}
import de.fellows.microservices.pcb.{
  ApiNotSetUpError,
  ApiNotSetUpE<PERSON><PERSON><PERSON><PERSON>,
  PcbServer<PERSON>onfig,
  P<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ScenarioRequestWithPanel,
  Server<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ThirdPartyError
}
import de.fellows.utils.{CurrencyCode, FutureUtils}
import play.api.Logging
import play.api.libs.json.{JsValue, Json}
import sttp.client3.playJson.SttpPlayJsonApi
import sttp.client3.{basicRequest, UriContext}
import sttp.model.StatusCode

import java.util.UUID
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class GatemaApiService(
    host: String,
    username: String,
    password: String,
    gatemaDbProfile: String,
    gatemaServerURL: String,
    asyncBackend: AsyncBackend
)(implicit lang: Lang) extends SingleManufacturerApiService
    with Logging with SttpHelper with SttpPlayJsonApi {

  override def api: ManufacturerApi = ManufacturerApi.Gatema

  private val globalCredentials: Credentials =
    Credentials(
      ManufacturerApi.Gatema,
      username = username,
      password = password,
      region = None
    )

  override def checkCredentials(
      credentials: Credentials,
      tenant: String
  )(implicit ec: ExecutionContext): Future[Either[String, Boolean]] =
    // Gatema doesn't have a way to validate customer ids... so we just create a very simple check according to
    // what we know:
    //  - it must be at least 5 chars (actually, it seems like it will change to 8, but bmk has one with 5 so...)
    //  - Luminovo can use "0" for now, so we can just allow anything
    if (tenant == Tenants.Luminovo) {
      Future.successful(Right(true))
    } else if (credentials.username.length < 5) {
      Future.successful(Left("Invalid Customer ID"))
    } else {
      Future.successful(Right(true))
    }

  private def getToken(
      credentials: Credentials
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, GatemaToken]] =
    basicRequest
      .post(uri"$host/heliosapi/api/Connect/Login")
      .body(
        AuthenticationRequest(
          UserName = credentials.username,
          password = credentials.password,
          LanguageId = "CZ",
          DbProfile = gatemaDbProfile,
          UseWindowsAuthentication = false,
          UseCurrentUserCredentials = false,
          ServerURL = gatemaServerURL
        )
      )
      .response(asJson[JsValue])
      .send(asyncBackend)
      .map { response =>
        val responseStatusCode = response.code
        handleJsonResponse(response, "Gatema") { response =>
          logger.info(s"Received Gatema authentication response $response")
          Try(response.as[AuthenticationResponse]) match {
            case Success(authResponse) if authResponse.success =>
              Right(GatemaToken(authResponse.userName, authResponse.userId))

            case Success(authResponse) =>
              logger.error(s"Error while getting token from Gatema: ${authResponse.errorMessage}")
              val statusCode = Try(authResponse.statusCode.toInt)
                .toEither
                .flatMap(StatusCode.safeApply)
                .getOrElse(StatusCode.InternalServerError)
              Left(ThirdPartyError(s"Error while getting token: ${authResponse.errorMessage}", statusCode))

            case Failure(exception) =>
              logger.error(s"Couldn't deserialize a JSON response from Gatema auth response: ${exception.getMessage}")
              Left(ThirdPartyError("Response is not deserializable", responseStatusCode))
          }
        }
      }

  private def fromPcbToGatema(
      pcb: PCB,
      scenarioRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      emsPreferences: EmsPreferences,
      existingOffer: Option[ExistingOffer],
      customerId: String
  ): Either[PcbServerError, Seq[(GatemaPcbRequest, (CalculatedPanelInfo, Seq[PanelInfo]))]] = {
    val quantities = scenarioRequestsWithPanels.map(s => Quantity(s.quantity))
    GatemaPcbRequest.validateAndConvert(
      pcb = pcb,
      quantities = quantities,
      existingOffer = existingOffer,
      customerId = customerId
    ).flatMap { request =>
      scenarioRequestsWithPanels
        .traverse { scenarioRequestWithPanel =>
          ApiService.calculateWorkingPanelInfo(
            pcb = pcb,
            quantity = scenarioRequestWithPanel.quantity,
            panelInfo = scenarioRequestWithPanel.panelInfo,
            panelPreferences = emsPreferences.panelPreferencesOrDefault,
            panelConstraints = Some(panelConstraints)
          ).map { calculatedPanelInfo =>
            val panelInfo =
              PanelInfo(
                numberOfPanels = calculatedPanelInfo.numberOfPanels,
                requestedPcbs = calculatedPanelInfo.requestedPcbs,
                totalPcbs = calculatedPanelInfo.totalPcbs,
                sourcingScenarioId = scenarioRequestWithPanel.sourcingScenarioId
              )
            (calculatedPanelInfo, panelInfo)
          }
        }
        .map { panelDistributions =>
          distributeRequests(request, panelDistributions)
        }
    }
  }

  private def distributeRequests(
      request: GatemaPcbRequest,
      panelDistributions: Seq[(CalculatedPanelInfo, PanelInfo)]
  ): Seq[(GatemaPcbRequest, (CalculatedPanelInfo, Seq[PanelInfo]))] =
    panelDistributions
      .groupBy {
        case (p: CalculatedPanelInfo.FromPanelDetails, _) => p.panelDistribution.mesh.size
        case (p: CalculatedPanelInfo.FromExisting, _)     => p.existing.numberOfPcbs
      }
      .map(x => x._2.head._1 -> x._2.map(_._2))
      .toSeq
      .map {
        case (calculatedPanelInfo, panelInfo) =>
          val updatedRequest = request.withPanel(calculatedPanelInfo).withQuantities(panelInfo)
          (updatedRequest, (calculatedPanelInfo, panelInfo))
      }

  override def doMakeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      scenarioRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Option[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    (for {
      credentials <- EitherT.fromOption[Future](
        credentials,
        ApiNotSetUpError(ApiNotSetUpErrorKind.MissingGatemaCustomerId, s"missing.customerId.gatema")
      )

      customerId = credentials.username

      location <- EitherT.fromOption[Future](
        manufacturer.locations.headOption,
        ServerError(s"No location found for manufacturer ${manufacturer.name}")
      )

      _ <- EitherT.fromEither[Future](
        ApiService.getSupplierCapabilityErrors(manufacturer, requestValidations)
      )

      token <- EitherT(getToken(globalCredentials))

      responses <- EitherT {
        makeQuote(
          token = token,
          pcb = pcb,
          manufacturer = manufacturer,
          location = location,
          scenarioRequestsWithPanels = scenarioRequestsWithPanels,
          emsPreferences = emsPreferences,
          existingOffer = existingOffers.headOption,
          customerId = customerId
        )
      }
    } yield responses).value

  private def makeQuote(
      token: GatemaToken,
      pcb: PCB,
      manufacturer: Manufacturer,
      location: ManufacturerLocation,
      scenarioRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      emsPreferences: EmsPreferences,
      existingOffer: Option[ExistingOffer],
      customerId: String
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    fromPcbToGatema(
      pcb = pcb,
      scenarioRequestsWithPanels = scenarioRequestsWithPanels,
      emsPreferences = emsPreferences,
      existingOffer = existingOffer,
      customerId = customerId
    ) match {
      case Right(requests) =>
        FutureUtils.sequentialTraverse(
          requests.zipWithIndex
        ) {
          case ((request, (calculatedPanelInfo, panelInfos)), idx) =>
            val futRequest = () =>
              makeRequest(
                token = token,
                request = request,
                calculatedPanelInfo = calculatedPanelInfo,
                panelInfos = panelInfos,
                manufacturer = manufacturer,
                location = location
              )

            if (idx == 0) {
              futRequest()
            } else {
              FutureUtils.delay(3.5d.seconds) {
                futRequest()
              }
            }
        }
          .map(responses => responses.sequence.map(_.flatten))

      case Left(error) => Future.successful(Left(error))
    }

  private def makeRequest(
      token: GatemaToken,
      request: GatemaPcbRequest,
      calculatedPanelInfo: CalculatedPanelInfo,
      panelInfos: Seq[PanelInfo],
      manufacturer: Manufacturer,
      location: ManufacturerLocation
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    makeQuoteRequestCall(token, request).map {
      case Left(error) => Left(error)

      case Right(response) if response.leadTime.size != response.unitPrice.size =>
        logger.error("Lead time and price size mismatch, cannot create offers")
        Left(ServerError("Lead time and price size mismatch"))

      case Right(response) =>
        val offers = response
          .leadTime
          .lazyZip(response.unitPrice)
          .lazyZip(response.quantity)
          .lazyZip(response.tooling)
          .map {
            case (leadTime, unitPrice, quantity, tooling) =>
              quantity -> Offer(
                productionDays = leadTime,
                price = unitPrice * quantity,
                currency = Some(CurrencyCode.EUR),
                oneTimeCosts = Some(tooling)
              )
          }
          .groupMap(_._1)(_._2)

        val quoteResponses = offers
          .flatMap {
            case (quantity, offers) =>
              panelInfos.flatMap(panelInfo =>
                if (quantity == panelInfo.totalPcbs.value) {
                  Some(
                    QuoteResponse(
                      offerResponse = OfferResponse(
                        api = ManufacturerApiWithInformation(api),
                        manufacturer = manufacturer,
                        location = location,
                        quantity = panelInfo.numberOfPanels.value,
                        offers = offers,
                        priceType = PriceType.ContractPrice
                      ),
                      quoteId = response.priceOfferReference,
                      url = None,
                      calculatedPanelInfo = calculatedPanelInfo,
                      existingOfferId = None,
                      sourcingScenarioId = panelInfo.sourcingScenarioId,
                      sharedPcbId = None
                    )
                  )
                } else {
                  None
                }
              )
          }
          .toSeq

        Right(quoteResponses)
    }

  private def makeQuoteRequestCall(
      token: GatemaToken,
      pcb: GatemaPcbRequest
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, GatemaPcbResponse]] = {
    logger.info(s"Sending Gatema quote request ${Json.toJson(pcb)}")
    basicRequest
      .auth.basic(token.username, token.userId)
      .post(uri"$host/heliosapi/api/v1/partner/Gatema/PCBPlugin")
      .body(pcb)
      .response(asJson[JsValue])
      .send(asyncBackend)
      .map { response =>
        val responseStatusCode = response.code
        handleJsonResponse(response, "Gatema") { response =>
          logger.info(s"Received Gatema quote response $response")
          Try(response.as[GatemaPcbResponse]) match {
            case Success(value) => Right(value)
            case Failure(exception) =>
              logger.error(s"Couldn't deserialize a JSON response from Gatema pcb response: ${exception.getMessage}")
              Left(ThirdPartyError("Response is not deserializable", responseStatusCode))
          }
        }
      }
  }

  /** Validate request for Gatema API
    */
  def doValidateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]] =
    Future.successful(Seq.empty)
}

object GatemaApiService {
  def fromConfig(
      config: PcbServerConfig,
      asyncBackend: AsyncBackend
  )(implicit lang: Lang): GatemaApiService =
    new GatemaApiService(
      host = config.getString("gatemaApi.host"),
      username = config.getString("gatemaApi.username"),
      password = config.getString("gatemaApi.password"),
      gatemaDbProfile = config.getString("gatemaApi.dbProfile"),
      gatemaServerURL = config.getString("gatemaApi.serverUrl"),
      asyncBackend = asyncBackend
    )
}
