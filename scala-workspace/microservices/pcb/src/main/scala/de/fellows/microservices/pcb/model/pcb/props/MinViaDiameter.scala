package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters
import de.fellows.microservices.pcb.model.pcb.capability.MinMaxCapability

object MinViaDiameter {
  val name: String  = "minViaDiameter"
  val label: String = "pcb.mechanical.minViaDiameter"

  def empty: MinViaDiameter                     = MinViaDiameter(None)
  def apply(value: Millimeters): MinViaDiameter = MinViaDiameter(Some(value))
  type MinViaDiameterCapability = MinMaxCapability[MinViaDiameter, Millimeters]
}

final case class MinViaDiameter(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = MinViaDiameter.name
  val label: String     = MinViaDiameter.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("ph_min_size"))
}
