package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters
import de.fellows.microservices.pcb.model.pcb.capability.MinMaxRequiredCapability

object BoardHeight {
  val name: String  = "boardHeight"
  val label: String = "pcb.board.basic.height"

  def zero: BoardHeight                              = BoardHeight(0.0f)
  def apply(value: Option[Millimeters]): BoardHeight = value.map(BoardHeight(_)).getOrElse(zero)

  type BoardHeightCapability = MinMaxRequiredCapability[BoardHeight, Millimeters]
}

final case class BoardHeight(override val value: Millimeters) extends PCBRequiredProperty[Millimeters] {
  val fieldName: String                         = BoardHeight.name
  val label: String                             = BoardHeight.label
  override val legacyNames: Option[Seq[String]] = Some(Seq("height"))
  override val unit: String                     = "mm"
}
