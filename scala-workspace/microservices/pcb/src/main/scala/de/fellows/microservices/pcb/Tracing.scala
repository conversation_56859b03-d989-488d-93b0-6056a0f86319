package de.fellows.microservices.pcb

import de.fellows.utils.logging.StackrateLogging
import io.opentelemetry.api.trace.{Span, StatusCode, Tracer}
import io.opentelemetry.api.{GlobalOpenTelemetry, OpenTelemetry}
import io.opentelemetry.instrumentation.httpclient.JavaHttpClientTelemetry

import java.net.http.HttpClient
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try, Using}

object Tracing extends StackrateLogging {

  val openTelemetry: OpenTelemetry = GlobalOpenTelemetry.get()

  private def getTracer(): Tracer =
    openTelemetry.getTracer("pcb-server", "1.0.0")

  def createTracingHttpClient(httpClient: HttpClient): HttpClient =
    JavaHttpClientTelemetry.create(openTelemetry).newHttpClient(httpClient)

  def withTrace[X](name: String)(op: Span => X): X = {
    val span: Span = getTracer().spanBuilder(name).startSpan()
    Using.resource(span.makeCurrent()) { s =>
      val tried = Try(op(span)) match {
        case x: Failure[X] =>
          span.setStatus(StatusCode.ERROR, x.exception.getMessage)
          span.recordException(x.exception)
          x
        case x => x
      }

      span.end()
      tried.get
    }
  }

  def withAsyncTrace[X](name: String)(op: Span => Future[X])(implicit
      ec: ExecutionContext
  ): Future[X] = {
    val span = getTracer().spanBuilder(name).startSpan()
    val ctx  = span.makeCurrent()
    val f    = Try(op(span))

    f match {
      case Failure(exception) =>
        span.setStatus(StatusCode.ERROR, exception.getMessage)
        span.recordException(exception)
        span.end()

      case Success(value) =>
        value.onComplete {
          case Failure(exception) =>
            span.setStatus(StatusCode.ERROR, exception.getMessage)
            span.recordException(exception)
            span.end()
          case Success(_) =>
            span.end()
        }
    }

    ctx.close()

    f.get
  }

  def exceptionError(ex: Throwable): Unit =
    exceptionError(ex, "Found an error")

  def exceptionError(ex: Throwable, msg: String): Unit = {
    val span = Span.current()
    span.setStatus(StatusCode.ERROR, ex.getMessage)
    span.recordException(ex)
    logger.error(s"$msg: ${ex.getMessage}")
  }

  def error(serviceError: ServiceError): Unit =
    error(s"${serviceError.getClass.getSimpleName}: ${serviceError.error}")

  def error(msg: String): Unit = {
    val span = Span.current()
    span.setStatus(StatusCode.ERROR, msg)
    logger.error(msg)
  }

}
