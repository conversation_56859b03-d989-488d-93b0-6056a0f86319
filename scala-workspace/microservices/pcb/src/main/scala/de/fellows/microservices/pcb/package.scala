package de.fellows.microservices

import de.fellows.ems.panel.api.CustomerPanel
import de.fellows.luminovo.panel.{
  ExistingPanel,
  ExistingWrapper,
  GenericPanel,
  PanelDetails,
  PerPcb,
  PerSourcingScenario
}
import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.model.lq.{ChangeStatus, LeadTime, ScenarioRequest}
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, PropertyError}
import de.fellows.utils.JsonFormats
import de.fellows.utils.security.{AuthenticationServiceComposition, GenericTokenContent}
import enumeratum._
import play.api.libs.json.{JsString, Json, JsonConfiguration, Writes}
import sttp.model.StatusCode

import scala.util.{Failure, Success}
import de.fellows.app.price.api.CapabilitiesApi.StackratePricingFailedField
import de.fellows.app.price.api.PricingError
import de.fellows.app.price.api.PricingBreak
import de.fellows.luminovo.LuminovoJson

package object pcb {
  type AuthToken = String

  sealed trait PcbServerError

  object PcbServerError {
    implicit val serverErrorWrites: Writes[PcbServerError] = {
      case e: ServiceError           => JsString(e.error)
      case e: PanelError             => Json.toJson(s"Error generating PCB panel: ${e.error}")
      case e: PropertyErrors         => Json.toJson(e.errors)
      case e: StackratePricingBreaks => Json.obj("pricingBreaks" -> e.pricingBreaks, "type" -> "StackratePricingBreaks")
      case e: StackratePricingErrors => Json.obj("errors" -> e.errors, "type" -> "StackratePricingErrors")
      case CustomStackupError        => Json.obj("type" -> "CustomStackupError")
    }

    def propertyErrors(error: PcbServerError): Seq[PropertyError] =
      error match {
        case PropertyErrors(errors) => errors
        case _                      => Seq()
      }
  }

  sealed trait ServiceError extends PcbServerError {
    val error: String
  }

  final case class InvalidParams(error: String)                           extends ServiceError
  final case class NotFoundError(error: String)                           extends ServiceError
  final case class ThirdPartyError(error: String, statusCode: StatusCode) extends ServiceError

  final case class UnauthorizedError(error: String)                            extends ServiceError
  final case class CredentialsMissingError(error: String)                      extends ServiceError
  final case class ApiNotSetUpError(kind: ApiNotSetUpErrorKind, error: String) extends ServiceError

  final case class ServerError(error: String)                   extends ServiceError
  final case class UnknownError(error: String)                  extends ServiceError
  final case class ExceptionError(error: String, ex: Throwable) extends ServiceError

  sealed trait OfferFetchError extends PcbServerError
  sealed trait PanelErrorKind  extends EnumEntry
  object PanelErrorKind extends Enum[PanelErrorKind] {
    val values: IndexedSeq[PanelErrorKind] = findValues

    case object PcbWidthBelowDeliveryPanelMinimum  extends PanelErrorKind
    case object PcbWidthExceedsDeliveryPanel       extends PanelErrorKind
    case object PcbHeightBelowDeliveryPanelMinimum extends PanelErrorKind
    case object PcbHeightExceedsDeliveryPanel      extends PanelErrorKind
    case object ExistingPanelNotSupported          extends PanelErrorKind
    case class UnknownError(error: Throwable)      extends PanelErrorKind

    def errorMessage(error: PanelErrorKind): String = error match {
      case PcbWidthBelowDeliveryPanelMinimum  => "Pcb width is below delivery panel minimum"
      case PcbWidthExceedsDeliveryPanel       => "Pcb width exceeds delivery panel"
      case PcbHeightBelowDeliveryPanelMinimum => "Pcb height is below delivery panel minimum"
      case PcbHeightExceedsDeliveryPanel      => "Pcb height exceeds delivery panel"
      case ExistingPanelNotSupported          => "ExistingPanels are not supported"
      case UnknownError(e)                    => s"Unknown error: ${e.getMessage}"
    }

    private val typeField = "type"
    implicit val writes: Writes[PanelErrorKind] = Writes {
      case UnknownError(e) => Json.obj(typeField -> "UnknownError", "message" -> e.getMessage)
      case e               => Json.obj(typeField -> e.entryName)
    }
  }

  final case class PanelError(error: PanelErrorKind)          extends OfferFetchError
  final case class PropertyErrors(errors: Seq[PropertyError]) extends OfferFetchError
  final case object CustomStackupError                        extends OfferFetchError

  final case class StackratePricingErrorMessage(
      name: String,
      message: String
  )

  object StackratePricingErrorMessage {
    implicit val format: Writes[StackratePricingErrorMessage] = Json.writes[StackratePricingErrorMessage]

  }

  final case class StackratePricingMessage(
      name: String,
      message: String,
      fields: Seq[StackratePricingFailedField],
      hints: Seq[String]
  )

  object StackratePricingMessage {
    implicit val format: Writes[StackratePricingMessage] = {
      implicit val jsonConfiguration: JsonConfiguration = LuminovoJson.configuration
      Json.writes[StackratePricingMessage]
    }

    def from(error: PricingError): StackratePricingMessage =
      StackratePricingMessage(error.name, error.message, Seq.empty, Seq.empty)

    def from(error: PricingBreak): StackratePricingMessage =
      StackratePricingMessage(error.name, error.message, error.variableFailures, error.hint)
  }

  final case class StackratePricingErrors(errors: Seq[StackratePricingMessage])        extends OfferFetchError
  final case class StackratePricingBreaks(pricingBreaks: Seq[StackratePricingMessage]) extends OfferFetchError

  sealed trait ApiNotSetUpErrorKind extends EnumEntry
  object ApiNotSetUpErrorKind extends Enum[ApiNotSetUpErrorKind] {
    val values: IndexedSeq[ApiNotSetUpErrorKind] = findValues

    case object MissingGatemaCustomerId extends ApiNotSetUpErrorKind

    implicit val writes: Writes[ApiNotSetUpErrorKind] = Writes(e => Json.obj("type" -> e.entryName))
  }

  def decodeToken(token: AuthToken): Either[UnauthorizedError, GenericTokenContent] =
    AuthenticationServiceComposition.decodeAnyToken(token) match {
      case Failure(exception) => Left(UnauthorizedError(s"Could not decode token: ${exception.getMessage}"))
      case Success(token)     => Right(token)
    }

  val JsonSealedTraitConfig: JsonConfiguration =
    JsonConfiguration(
      discriminator = "type",
      typeNaming = JsonFormats.JsonSealedTraitNaming
    )

  sealed trait PcbPanelInfo
  object PcbPanelInfo {
    case object NoPanel                            extends PcbPanelInfo
    case class Existing(panel: ExistingPanel)      extends PcbPanelInfo
    case class Details(panelDetails: PanelDetails) extends PcbPanelInfo

    def fromMbGenericPanel(panel: Option[GenericPanel]): PcbPanelInfo =
      panel.fold(NoPanel: PcbPanelInfo)(fromGenericPanel)

    def fromGenericPanel(panel: GenericPanel): PcbPanelInfo =
      panel match {
        case p: PerSourcingScenario => Details(p.panelDetails)
        case p: PerPcb              => Details(p.panelDetails)
        case p: ExistingWrapper     => Existing(p.panel)
      }
  }

  final case class ScenarioRequestWithPanel(
      sourcingScenarioId: SourcingScenarioId,
      leadTimes: Seq[LeadTime],
      quantity: Int,
      changeStatus: ChangeStatus,
      panelInfo: PcbPanelInfo,
      stackratePanel: Option[CustomerPanel],
      manufacturers: Seq[Manufacturer]
  )

  object ScenarioRequestWithPanel {
    def apply(
        scenarioRequest: ScenarioRequest,
        panelInfo: PcbPanelInfo,
        stackratePanel: Option[CustomerPanel],
        manufacturers: Seq[Manufacturer]
    ): ScenarioRequestWithPanel =
      ScenarioRequestWithPanel(
        sourcingScenarioId = scenarioRequest.sourcingScenarioId,
        leadTimes = scenarioRequest.leadTimes,
        quantity = scenarioRequest.quantity,
        changeStatus = scenarioRequest.change,
        panelInfo = panelInfo,
        stackratePanel = stackratePanel,
        manufacturers = manufacturers
      )
  }
}
