package de.fellows.microservices.pcb.model.wurth

/** Name of param in Wurth API: microvia_side
  */
sealed trait WurthLaserDrill {
  val value: Int
}

private case object NoDrill extends <PERSON><PERSON>LaserDrill {
  override val value: Int = 0
}

private case object TopDrill extends <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {
  override val value: Int = 1
}

private case object Bottom<PERSON>rill extends <PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON> {
  override val value: Int = 2
}

private case object <PERSON>BottomDrill extends Wu<PERSON><PERSON><PERSON>r<PERSON>rill {
  override val value: Int = 3
}

private object WurthLaserDrill {

  def fromWurth(value: Int): WurthLaserDrill = value match {
    case 0 => NoDrill
    case 1 => TopDrill
    case 2 => BottomDrill
    case 3 => TopBottomDrill
    case _ => throw new IllegalArgumentException(s"Unknown laser drill value: $value")
  }
}
