package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.pcb.capability.SetValueCapability

/** Number of layers
  */
sealed trait NumberOfLayers extends PCBRequiredProperty[Int] {
  val value: Int
  val fieldName: String = NumberOfLayers.name
  val label: String     = NumberOfLayers.label
  val unit: String      = ""
}

object NumberOfLayers {
  val name: String  = "layerCount"
  val label: String = "pcb.layer.numberOfLayers"

  def apply(value: Int): NumberOfLayers         = apply(Some(value))
  def apply(value: Option[Int]): NumberOfLayers = convert(value.getOrElse(1))

  private def convert(value: Int): NumberOfLayers = value match {
    case 0      => ZeroLayers
    case 1      => OneLayer
    case 2      => TwoLayers
    case 3      => ThreeLayers
    case 4      => FourLayers
    case 5      => FiveLayers
    case 6      => SixLayers
    case 8      => EightLayers
    case 10     => TenLayers
    case 12     => TwelveLayers
    case 14     => FourteenLayers
    case 16     => SixteenLayers
    case 18     => EighteenLayers
    case 20     => TwentyLayers
    case 22     => TwentyTwoLayers
    case 24     => TwentyFourLayers
    case 26     => TwentySixLayers
    case 28     => TwentyEightLayers
    case layers => InvalidNumberOfLayers(layers)
  }

  type NumberOfLayersCapability = SetValueCapability[NumberOfLayers]

  def capability(r: Range): NumberOfLayersCapability = {
    val range = r.map(convert).filterNot(_.isInstanceOf[InvalidNumberOfLayers])
    SetValueCapability[NumberOfLayers](range: _*)
  }

}

case object ZeroLayers extends NumberOfLayers {
  override val value: Int = 0
}

case object OneLayer extends NumberOfLayers {
  override val value: Int = 1
}

case object TwoLayers extends NumberOfLayers {
  override val value: Int = 2
}
case object ThreeLayers extends NumberOfLayers {
  override val value: Int = 3
}

case object FourLayers extends NumberOfLayers {
  override val value: Int = 4
}

case object FiveLayers extends NumberOfLayers {
  override val value: Int = 5
}

case object SixLayers extends NumberOfLayers {
  override val value: Int = 6
}

case object EightLayers extends NumberOfLayers {
  override val value: Int = 8
}

case object TenLayers extends NumberOfLayers {
  override val value: Int = 10
}

case object TwelveLayers extends NumberOfLayers {
  override val value: Int = 12
}

case object FourteenLayers extends NumberOfLayers {
  override val value: Int = 14
}
case object SixteenLayers extends NumberOfLayers {
  override val value: Int = 16
}
case object EighteenLayers extends NumberOfLayers {
  override val value: Int = 18
}
case object TwentyLayers extends NumberOfLayers {
  override val value: Int = 20
}
case object TwentyTwoLayers extends NumberOfLayers {
  override val value: Int = 22
}
case object TwentyFourLayers extends NumberOfLayers {
  override val value: Int = 24
}
case object TwentySixLayers extends NumberOfLayers {
  override val value: Int = 26
}
case object TwentyEightLayers extends NumberOfLayers {
  override val value: Int = 28
}

case class InvalidNumberOfLayers(value: Int) extends NumberOfLayers
