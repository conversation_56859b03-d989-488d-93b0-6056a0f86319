package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.{Chamfering => PCBChamfering}
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object Chamfering {
  val name: String  = "chamfering"
  val label: String = "pcb.mechanical.chamfering"

  /** According to this specification, https://www.notion.so/luminovo/PCB-Specification-4493fa95f17e4bbf81fe2a6c59259e82
    * the default value for Chamfering is None
    */
  def default: Chamfering                             = Chamfering(PCBChamfering.None)
  def apply(value: Option[PCBChamfering]): Chamfering = value.map(Chamfering(_)).getOrElse(default)

  type ChamferingCapability = SetCapability[Chamfering, PCBChamfering]
}

final case class Chamfering(override val value: PCBChamfering) extends PCBRequiredProperty[PCBChamfering] {
  val fieldName: String = Chamfering.name
  val label: String     = Chamfering.label
  val unit: String      = ""
}
