package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.Millimeters

/** The smallest non-plated hole diameter in mm
  */
object NphMinDiameter {
  val name: String  = "nphMinDiameter"
  val label: String = "pcb.mechanical.nphMinDiameter"

  def empty: NphMinDiameter                     = NphMinDiameter(None)
  def apply(value: Millimeters): NphMinDiameter = NphMinDiameter(Some(value))
}

final case class NphMinDiameter(override val value: Option[Millimeters]) extends MillimeterPCBProperty {
  val fieldName: String = NphMinDiameter.name
  val label: String     = NphMinDiameter.label

  override val legacyNames: Option[Seq[String]] = Some(Seq("nph_min_size"))
}
