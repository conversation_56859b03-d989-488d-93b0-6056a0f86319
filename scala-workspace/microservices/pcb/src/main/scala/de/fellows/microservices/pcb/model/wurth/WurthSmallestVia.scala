package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.MinViaDiameter

/** Name of param in Wurth API: pattern_drill_diameter
  */
sealed trait WurthSmallestVia {
  val value: Int
  def toPCB: MinViaDiameter
}
private case object Mm25 extends WurthSmallestVia {
  override val value: Int            = 1
  override def toPCB: MinViaDiameter = MinViaDiameter(0.25)
}
private case object Mm10 extends WurthSmallestVia {
  override val value: Int            = 2
  override def toPCB: MinViaDiameter = MinViaDiameter(0.10)
}

private object WurthSmallestVia {

  /** everything between 0.25 and 0.1 -> use 0.1
    * everything above 0.25 -> use 0.25
    */
  def converter(value: MinViaDiameter): Option[WurthSmallestVia] = value.value match {
    case Some(v) if v < 0.1   => None
    case Some(v) if v < 0.25  => Some(Mm10)
    case Some(v) if v >= 0.25 => Some(Mm25)
    case _                    => None
  }

  def fromWurth(value: Int): WurthSmallestVia = value match {
    case 1 => Mm25
    case 2 => Mm10
    case _ => throw new IllegalArgumentException(s"Unknown value for smallest via: $value")
  }
}
