package de.fellows.microservices.pcb.model.panel

import de.fellows.microservices.pcb.model.Millimeters

trait RectangleMesh {

  def netWidth(pcb: PcbSize, gap: PanelGap, columns: Int): Millimeters =
    columns * (pcb.widthInMm + gap.x) - gap.x

  def netHeight(pcb: PcbSize, gap: PanelGap, rows: Int): Millimeters =
    rows * (pcb.heightInMm + gap.y) - gap.y

  def netArea(pcb: PcbSize, gap: PanelGap, rows: Int, columns: Int): NetArea =
    new NetArea(netWidth(pcb, gap, columns), netHeight(pcb, gap, rows))
}
