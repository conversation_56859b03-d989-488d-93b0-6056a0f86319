package de.fellows.microservices.pcb.model.safePcb

import cats.data.EitherT
import cats.syntax.traverse._
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import de.fellows.microservices.pcb.model.ApiService.CalculatedPanelInfo
import de.fellows.microservices.pcb.model.lq.PriceType.ContractPrice
import de.fellows.microservices.pcb.model.lq.{
  Credentials,
  ExistingOffer,
  ManufacturerApiWithInformation,
  OfferResponse,
  QuoteResponse
}
import de.fellows.microservices.pcb.model.panel.{EmsPreferences, NumberOfPanels, PanelInfo}
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerApi, PCB}
import de.fellows.microservices.pcb.model.{ApiService, Offer, RequestValidation, SingleManufacturerApiService}
import de.fellows.microservices.pcb.{
  CredentialsMissingError,
  NotFoundError,
  PcbServer<PERSON>rror,
  Property<PERSON><PERSON>rs,
  ScenarioRequestWithPanel,
  ServiceError,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ThirdPartyError
}
import de.fellows.utils.ListUtils.SeqExtensions
import de.fellows.utils.{CurrencyCode, Region}
import org.apache.commons.codec.digest.{HmacAlgorithms, HmacUtils}
import play.api.Logging
import sttp.client3.playJson.SttpPlayJsonApi
import sttp.client3.{asString, basicRequest, ResponseAs}
import sttp.model.{MediaType, StatusCode, Uri}

import java.time.format.DateTimeFormatter
import java.time.{ZoneId, ZonedDateTime}
import java.util.{Base64, UUID}
import scala.concurrent.{ExecutionContext, Future}
import scala.xml.{Elem, NodeSeq}

sealed case class SafePcbResponse(
    offers: Seq[SafePcbOffer],
    quoteNumber: Option[String],
    currency: Option[CurrencyCode]
)

final case class SafePcbOffer(
    quantity: Int,
    price: BigDecimal,
    productionDays: Int
) {
  def toOffer(currency: Option[CurrencyCode]): Offer = Offer(
    price = price,
    productionDays = productionDays,
    currency = currency,
    oneTimeCosts = None // TODO: One Time Costs for SafePCB
  )
}

/** This service is responsible for making API requests to Safe-PCB API and handle responses graciously
  *
  * Specification: https://www.notion.so/luminovo/Beta-layout-API-technical-documentation-65cb45d8dcc54aae88b130f83aaa5109
  */
class SafePcbApiService(asyncBackend: AsyncBackend)
    extends SingleManufacturerApiService
    with Logging with SttpHelper with SttpPlayJsonApi {
  protected val host    = "HTTPS://sales.safe-pcb.com/SAFE_PCB"
  protected val version = "V3.01"

  override def api: ManufacturerApi = ManufacturerApi.SafePcb

  override def checkCredentials(
      credentials: Credentials,
      tenant: String
  )(implicit ec: ExecutionContext): Future[Either[String, Boolean]] = {
    // we use CREATE_QUOTE method because the API can have limits on creating quotes as well
    val uri = Uri.unsafeParse(getUrl("CREATE_QUOTE"))
    val requestBody = <XML>
      {head}
      {customer(credentials)}
    </XML>

    basicRequest
      .post(uri)
      .contentType(MediaType.ApplicationXml)
      .body(requestBody.toString())
      .response(asSafePcbResponse)
      .send(asyncBackend)
      .map { response =>
        response.body match {
          case Left(error) =>
            error match {
              case s if s.contains("Customer ID") => Left("Invalid customer ID")
              case s if s.contains("API not enable for this customer ID") =>
                Left(" Please call Safe-PCB to activate API on your account")
              case s if s.contains("API not enable for CREATE_QUOTE request") =>
                Left("Please call Safe-PCB to activate the CREATE_QUOTE request on your API account")
              case _ => Right(true)
            }
          case Right(_) => Right(true)
        }
      }
  }

  override def doMakeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      scenarioRequestsWithPanel: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Option[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[QuoteResponse]]] =
    (for {
      credentials <- EitherT.fromOption[Future](
        credentials,
        CredentialsMissingError(s"Credentials missing for \"${api.name}\""): PcbServerError
      )

      _ <- EitherT.fromEither[Future](
        ApiService.getSupplierCapabilityErrors(manufacturer, requestValidations)
      )

      safePcbRequests <- EitherT.fromEither[Future](
        fromPcbToSafePcb(pcb, scenarioRequestsWithPanel, emsPreferences)
      )

      quotes <- EitherT {
        Future
          .traverse(safePcbRequests.zipLeft(existingOffers)) {
            case ((request, quantities), mbOffer) =>
              doRequest(request, manufacturer, quantities, mbOffer, credentials)
          }.map { responses =>
            val (failed, successful) = responses.partitionMap(identity)

            failed.foreach { err =>
              logger.error("Error when making quote: " + err)
            }

            val quotes = successful.flatten

            (quotes, failed) match {
              case (quotes, _) if quotes.nonEmpty => Right(quotes)
              case (_, first +: _)                => Left(first: PcbServerError)
              case _                              => Right(Seq.empty)
            }
          }
      }
    } yield quotes).value

  private def doRequest(
      request: SafePcbRequest,
      manufacturer: Manufacturer,
      panelInfo: Seq[PanelInfo],
      offer: Option[ExistingOffer],
      credentials: Credentials
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, Seq[QuoteResponse]]] =
    (for {
      priceResponse <- EitherT(
        makeRequest(
          request,
          manufacturer,
          panelInfo,
          offer,
          "GET_PRICE",
          credentials
        )
      )

      quoteResponse <- EitherT(
        makeRequest(
          request,
          manufacturer,
          panelInfo,
          offer,
          "CREATE_QUOTE",
          credentials
        )
      )

      quoteNumber <- EitherT.fromOption[Future](
        quoteResponse._2,
        ThirdPartyError("Quote number is not returned for SafePcb", StatusCode.Ok): ServiceError
      )
    } yield priceResponse._1.flatMap { pr =>
      panelInfo.flatMap(panelInfo =>
        if (panelInfo.numberOfPanels.value == pr.quantity) {

          Some(QuoteResponse(
            offerResponse = pr,
            quoteId = quoteNumber,
            url = Some(offerUrl(quoteNumber)),
            calculatedPanelInfo = request.calculatedPanelInfo,
            existingOfferId = offer.map(_.offerId),
            sourcingScenarioId = panelInfo.sourcingScenarioId,
            sharedPcbId = None
          ))
        } else {
          None
        }
      )
    }).value

  /** Validate request for API
    *
    * @param pcb            PCB
    * @param quantity       Number of PCB to order
    * @param emsPreferences Preferences of the EMS customer
    */
  override def doValidateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Manufacturer,
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]] = {
    val location = getLocation(manufacturer)
    Future.successful(
      location.toSeq.map { location =>
        RequestValidation(
          api = api,
          manufacturer = manufacturer,
          location = location,
          validation = validatePcbRequest(pcb)
        )
      }
    )
  }

  private def validatePcbRequest(
      pcb: PCB
  ): Option[PcbServerError] =
    SafePcbCapability.capability.validate(pcb.properties) match {
      case Nil    => None
      case errors => Some(PropertyErrors(errors))
    }

  private def getLocation(manufacturer: Manufacturer) =
    manufacturer.locations.find(l => l.region == Region.Unknown || l.region == Region.Germany)

  private[safePcb] def fromPcbToSafePcb(
      pcb: PCB,
      scenarioRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      emsPreferences: EmsPreferences
  ): Either[PcbServerError, Seq[(SafePcbRequest, Seq[PanelInfo])]] =
    SafePcbRequest.validateAndConvert(pcb) match {
      case Left(error) => Left(PropertyErrors(error))
      case Right(request) =>
        scenarioRequestsWithPanels
          .traverse { scenario =>
            ApiService
              .calculateWorkingPanelInfo(
                pcb = pcb,
                quantity = scenario.quantity,
                panelInfo = scenario.panelInfo,
                panelPreferences = emsPreferences.panelPreferencesOrDefault,
                panelConstraints = Some(panelConstraints)
              )
              .map { calculatedPanelInfo =>
                (
                  request,
                  calculatedPanelInfo,
                  PanelInfo(
                    numberOfPanels = calculatedPanelInfo.numberOfPanels,
                    requestedPcbs = calculatedPanelInfo.requestedPcbs,
                    totalPcbs = calculatedPanelInfo.totalPcbs,
                    sourcingScenarioId = scenario.sourcingScenarioId
                  )
                )
              }
          }
          .map { requestsWithPanelDistributions =>
            SafePcbApiService.distributeRequests(requestsWithPanelDistributions)
          }
    }

  /** Adds a configuration to the session and returns the list of offers
    */
  protected def makeRequest(
      pcb: SafePcbRequest,
      manufacturer: Manufacturer,
      panelInfo: Seq[PanelInfo],
      existingOffer: Option[ExistingOffer],
      method: String,
      c: Credentials
  )(implicit ec: ExecutionContext): Future[Either[ServiceError, (Seq[OfferResponse], Option[String])]] = {
    val uri         = Uri.unsafeParse(getUrl(method))
    val requestBody = fullRequest(pcb, panelInfo.map(_.numberOfPanels), existingOffer, c).toString()
    val location    = getLocation(manufacturer)

    location.map { location =>
      val response = basicRequest
        .post(uri)
        .contentType(MediaType.ApplicationXml)
        .body(requestBody)
        .response(asSafePcbResponse)
        .send(asyncBackend)

      logger.info("Request body: " + requestBody)
      response.map { response =>
        response.body match {
          case Left(error) =>
            logger.error(s"Error while making request to SafePcb: $error")
            Left(ThirdPartyError(error, response.code))
          case Right(response) =>
            val offerResponses = response.offers.groupBy(_.quantity).map {
              case (quantity, offers) =>
                val priceType = ContractPrice // SafePCB only supports contract prices
                OfferResponse(
                  api = ManufacturerApiWithInformation(api),
                  manufacturer = manufacturer,
                  location = location,
                  quantity = quantity,
                  offers = offers.map(_.toOffer(response.currency)),
                  priceType = priceType
                )
            }.toSeq
            Right(offerResponses, response.quoteNumber)
        }
      }
    }.getOrElse(
      Future.successful(Left(NotFoundError("No location found")))
    )
  }

  private val asSafePcbResponse: ResponseAs[Either[String, SafePcbResponse], Any] =
    asString.map {
      case Left(error) => Left(error)
      case Right(body) =>
        logger.info(s"response: ${body}")
        try {
          val xml = scala.xml.XML.loadString(body)
          (xml \\ "ERR").map(_.text) match {
            case Nil    => Right(extractSafePcbResponse(xml))
            case errors => Left(errors.mkString(", "))
          }
        } catch {
          case e: Exception =>
            val error = s"Couldn't parse a response from Safe-PCB API: ${e.getMessage}"
            logger.error(error)
            Left(error)
        }
    }

  private def extractSafePcbResponse(xml: Elem): SafePcbResponse = {
    val priceElem = xml \ "MATRIX_PRICE" \ "QUANTITY_ITEM" \ "DELIVERY_ITEM"
    val offers = priceElem.map { elem =>
      val quantityPanels = (elem \ "QUANTITY" \ "Panel").text.toInt
      val days           = (elem \ "PRODUCTION" \ "Days").text.toInt
      val price          = BigDecimal((elem \ "PRICE" \ "Total").text)
      SafePcbOffer(quantityPanels, price, days)
    }
    val currency    = CurrencyCode.ALL.find(_.code == (xml \ "MATRIX_PRICE" \ "Currency").text)
    val quoteNumber = (xml \ "QUOTE" \ "Number").headOption.map(_.text)
    SafePcbResponse(offers, quoteNumber, currency)
  }

  private def getUrl(method: String): String = s"$host?REQUEST=${method.toUpperCase}&version=$version"

  private[safePcb] def fullRequest(
      pcb: SafePcbRequest,
      numberOfPanels: Seq[NumberOfPanels],
      existingOffer: Option[ExistingOffer],
      c: Credentials
  ) = {
    // SafePcb supports only up to 5 quantity values
    assert(numberOfPanels.nonEmpty && numberOfPanels.size <= 5)
    <XML>
      {head}
      {customer(c)}
      {reference(pcb)}
      {quote(existingOffer)}
      <QUANTITIES>
        <Panel>{numberOfPanels.map(_.value).mkString(";")}</Panel>
      </QUANTITIES>
      {pcb.toXML}
      <STENCIL>
        <Position>WITHOUT</Position>
        <Format>WITHOUT</Format>
        <Thickness>WITHOUT</Thickness>
        <PAD_Reduction>WITHOUT</PAD_Reduction>
      </STENCIL>
    </XML>
  }

  private def head =
    <HEAD>
      <!-- This flag tells the API to display all the details of the parameter scan. -->
      <Detail>0</Detail>
      <!-- This flag tells the API to display to you all the parameters sent and recognized by the API as well as the submitted values. -->
      <Post>0</Post>
      <!-- The API will add the tax and the total amount with tax into the PRICE block -->
      <Export_Format_Price>TAXE</Export_Format_Price>
      <!-- You can tell the API which fields of the DELIVERY block you want to display in the response -->
      <Export_Format_Delivery>PRODUCTION</Export_Format_Delivery>
      <Valid_Quote_Creation>Create_Quote</Valid_Quote_Creation>
      <Software>LUMINOVO</Software>
    </HEAD>

  private def customer(c: Credentials) = {
    val time = ZonedDateTime.now(ZoneId.of("UTC")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"))
    <CUSTOMER>
      <Customer_ID>{c.username}</Customer_ID>
      <Time>{time}</Time>
      <HASH_Base64>{calculateHash(time, c)}</HASH_Base64>
    </CUSTOMER>
  }

  private def reference(request: SafePcbRequest) =
    <REFERENCE>
      <Project_Name>-</Project_Name>
      <PCB_Name>{request.name.getOrElse("-")}</PCB_Name>
    </REFERENCE>

  private def quote(offer: Option[ExistingOffer]) =
    offer.map(offer => quoteNumber(offer)).getOrElse(NodeSeq.Empty)

  private def quoteNumber(offer: ExistingOffer) =
    <QUOTE>
      <Number>{offer.offerNumber}</Number>
    </QUOTE>

  private def calculateHash(time: String, c: Credentials): String = {
    val hash = new HmacUtils(HmacAlgorithms.HMAC_SHA_1, c.password.getBytes).hmac(s"Safe-PCB API $time")
    new String(Base64.getEncoder.encode(hash))
  }
}

object SafePcbApiService {

  // SafePCB allows up to 5 quantity values per request
  private val MaxQuantitiesPerRequest = 5

  private[safePcb] def distributeRequests(
      requestsWithPanelDistributions: Seq[(SafePcbRequest, CalculatedPanelInfo, PanelInfo)]
  ): Seq[(SafePcbRequest, Seq[PanelInfo])] =
    requestsWithPanelDistributions
      .groupMap {
        case (request, _, _) => request
      } {
        case (_, calculatedPanelInfo, panelQuantity) => (calculatedPanelInfo, panelQuantity)
      }
      .toSeq
      .flatMap {
        case (request, panelDistributions) =>
          trim(panelDistributions)
            .flatMap {
              case (_, (calculatedPanelInfo, panelQuantities)) =>
                // we can only send 5 quantity values at a time to SafePCB, so we make groups of 5
                panelQuantities
                  .grouped(MaxQuantitiesPerRequest)
                  .map { quantities =>
                    (request.withPanel(calculatedPanelInfo), quantities)
                  }
            }
      }

  private[safePcb] def trim(
      panelDistributions: Seq[(CalculatedPanelInfo, PanelInfo)]
  ): Seq[(Int, (CalculatedPanelInfo, Seq[PanelInfo]))] =
    panelDistributions
      .groupMapReduce {
        case (c: CalculatedPanelInfo.FromPanelDetails, _) => c.panelDistribution.mesh.size
        case (c: CalculatedPanelInfo.FromExisting, _)     => c.existing.numberOfPcbs
      } {
        case (calculatedPanelInfo, panelQuantity) => (calculatedPanelInfo, Seq(panelQuantity))
      } {
        case ((calculatedPanelInfo, panelQuantitiesLeft), (_, panelQuantitiesRight)) =>
          (calculatedPanelInfo, panelQuantitiesLeft ++ panelQuantitiesRight)
      }
      .toSeq
}
