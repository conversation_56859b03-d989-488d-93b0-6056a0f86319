package de.fellows.microservices.pcb.model.wurth

import de.fellows.ems.pcb.api.specification.IPC600Class.{IPC1, IPC2}
import de.fellows.ems.pcb.api.specification.{
  LayerstackType => SpecLayerstackType,
  Side,
  SilkscreenColor,
  SoldermaskColor => SpecSoldermaskColor,
  SurfaceFinish => PCBSurfaceFinish
}
import de.fellows.microservices.pcb.model.pcb.{ManufacturerCapability, PCB, PropertyError, PropertyErrorKind}
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.{No, Yes}
import de.fellows.microservices.pcb.model.pcb.capability._
import de.fellows.microservices.pcb.model.pcb.props.BoardHeight.BoardHeightCapability
import de.fellows.microservices.pcb.model.pcb.props.BoardWidth.BoardWidthCapability
import de.fellows.microservices.pcb.model.pcb.props.ETest.ETestCapability
import de.fellows.microservices.pcb.model.pcb.props.EdgeMetalization.EdgeMetalizationCapability
import de.fellows.microservices.pcb.model.pcb.props.HardGold.HardGoldCapability
import de.fellows.microservices.pcb.model.pcb.props.IPCA600Class.IPCA600ClassCapability
import de.fellows.microservices.pcb.model.pcb.props.ImpedanceTested.ImpedanceTestedCapability
import de.fellows.microservices.pcb.model.pcb.props.PeelableMask.PeelableMaskCapability
import de.fellows.microservices.pcb.model.pcb.props.PressFit.PressFitCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenColor.SilkscreenColorCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenSide.SilkscreenSideCapability
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskColor.SoldermaskColorCapability
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskSide.SoldermaskSideCapability
import de.fellows.microservices.pcb.model.pcb.props.SurfaceFinish.SurfaceFinishCapability
import de.fellows.microservices.pcb.model.pcb.props._
import zio.prelude.Validation
import de.fellows.microservices.pcb.model.pcb.CustomLabelKind

object WurthCapability {

  def validate(pcb: PCB): Seq[PropertyError] =
    forLayerstackType(pcb.properties.layer.layerstackType)
      .validate(pcb.properties)

  def forLayerstackType(layerstackType: LayerstackType): ManufacturerCapability =
    layerstackType.value match {
      case SpecLayerstackType.Flex      => WurthFlexCapability.capability
      case SpecLayerstackType.RigidFlex => WurthRigidFlexCapability.capability
      case _                            => WurthRigidCapability.capability
    }

  val basicBoardCapability = BoardBasicCapability(
    new BoardWidthCapability(7.00, 426.00),
    new BoardHeightCapability(7.00, 271.00),
    new SilkscreenSideCapability(Side.None, Side.Top, Side.Bottom, Side.Both),
    new SilkscreenColorCapability(SilkscreenColor.White),
    new HardGoldCapability(Yes, No),
    new SurfaceFinishCapability(PCBSurfaceFinish.Enig, PCBSurfaceFinish.It) {
      override def validate(property: SurfaceFinish): Validation[PropertyError, SurfaceFinish] =
        super.validate(property).mapError { err =>
          if (property.value == PCBSurfaceFinish.HalPbFree) {
            PropertyError(
              property,
              PropertyErrorKind.CustomLabel(CustomLabelKind.WurthSurfaceFinishHalPbFree)
            )
          } else {
            err
          }
        }
    },
    new SoldermaskSideCapability(Side.None, Side.Top, Side.Bottom, Side.Both),
    new SoldermaskColorCapability(SpecSoldermaskColor.Green)
  )

  val advancedBoardCapability = AdvancedBoardCapability(
    new IPCA600ClassCapability(IPC1, IPC2),
    new ETestCapability(Yes),
    new PressFitCapability(No),
    new ImpedanceTestedCapability(No),
    new PeelableMaskCapability(Side.None),
    new EdgeMetalizationCapability(Yes, No)
  )

}
