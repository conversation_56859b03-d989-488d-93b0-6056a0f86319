package de.fellows.microservices.pcb.model.panel

import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import org.apache.batik.anim.dom.SVGDOMImplementation
import org.apache.batik.svggen.{SVGGeneratorContext, SVGGraphics2D}

import java.awt.Color
import java.io.{File, StringWriter}

object PanelRenderer {

  val PcbColor     = new Color(25, 77, 27)
  val PaddingColor = new Color(245, 246, 250)
  val Grey         = new Color(133, 140, 160);

  def renderToFile(panel: Seq[DistributionRectangle]): File = {
    val file = File.createTempFile("panel", ".svg")
    val g    = render(panel)
    g.stream(file.getAbsolutePath)
    file
  }

  def renderToString(panel: Seq[DistributionRectangle]): String = {
    val svg    = PanelRenderer.render(panel)
    val writer = new StringWriter()
    svg.stream(writer, true)
    writer.close()
    writer.toString
  }

  def render(panel: Seq[DistributionRectangle]): SVGGraphics2D = {
    val g   = createGraphics
    val max = panel.map(_.rect.max).reduce(_.max(_))

    // scale everything to get rid of possible rounding errors
    // anything after the third decimal place we discard
    val scale = 1000

    // Border around PCBs + padding, it can be seen as the panel delimiter
    val borderSize = 4

    // Increase the total size by two times the border size (so we can have one border each side)
    val width  = (max.x + (borderSize * 2)) * scale
    val height = (max.y + (borderSize * 2)) * scale

    val dimension  = new java.awt.Dimension(width.toInt, height.toInt)
    val background = Dimension(BigPoint(0, 0), BigPoint(width, height))

    // The actual border definitions
    // We subtract -1 (and in the last case a +1) to one of the
    // coordinates to avoid a disconnect in the final border
    // With those adjustments, the final rectangles overlap, so we don't have that issue
    val scaledBorderSize = borderSize * scale
    val borders = Seq(
      Dimension(BigPoint(0, 0), BigPoint(width, scaledBorderSize)),
      Dimension(BigPoint(0, 0), BigPoint(scaledBorderSize, height)),
      Dimension(BigPoint(0, height - scaledBorderSize), BigPoint(width, height)),
      Dimension(
        BigPoint(width - scaledBorderSize, 0),
        BigPoint(width, height)
      )
    )

    g.setSVGCanvasSize(dimension)

    g.setPaint(PaddingColor)
    g.fill(background.rectangle)

    g.setPaint(Grey)
    borders.foreach { b =>
      g.fill(b.rectangle)
    }

    panel.foreach { rect =>
      rect.kind match {
        // Draw both the padding and the gap between PCBs as white
        case Pcb => g.setPaint(PcbColor)
        case _   => g.setPaint(PaddingColor)
      }

      val shifted = rect.rect.shiftedBy(borderSize) * scale

      g.fill(shifted.rectangle)
    }

    g
  }

  protected def createGraphics: SVGGraphics2D = {
    val svgNS    = "http://www.w3.org/2000/svg"
    val domImpl  = SVGDOMImplementation.getDOMImplementation
    val document = domImpl.createDocument(svgNS, "svg", null)
    val ctx      = SVGGeneratorContext.createDefault(document)
    new SVGGraphics2D(ctx, false)
  }
}
