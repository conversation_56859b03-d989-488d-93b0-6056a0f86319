package de.fellows.microservices.pcb.model.lq

import enumeratum._

sealed trait LeadTimePreference extends EnumEntry

object LeadTimePreference extends Enum[LeadTimePreference] {
  override val values: IndexedSeq[LeadTimePreference] = findValues

  case object Fastest         extends LeadTimePreference
  case object BestPrice       extends LeadTimePreference
  case object BestPriceByDate extends LeadTimePreference

  def fromId(id: String): LeadTimePreference = id match {
    case "best_price"    => BestPrice
    case "best_price_by" => BestPriceByDate
    case _               => Fastest
  }
}
