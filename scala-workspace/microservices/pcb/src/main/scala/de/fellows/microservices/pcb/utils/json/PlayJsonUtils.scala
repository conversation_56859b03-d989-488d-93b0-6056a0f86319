package de.fellows.microservices.pcb.utils.json

import play.api.libs.json.{JsNull, JsObject, JsValue}

object PlayJsonUtils {

  // When we use Json.obj, Option serialization will result in null values for None
  // Which is different from the behavior of macro-derived Json.writes/formats
  // This just removes nulls from the JSON, sometimes we want undefined instead of explicit null
  def withoutNull(json: JsValue): JsValue = json match {
    case obj: JsObject => JsObject(obj.fields.filter(filterNullEntry))
    case other         => other
  }

  private def filterNullEntry(entry: (String, JsValue)): Boolean = entry match {
    case (_, JsNull) => false
    case _           => true
  }

}
