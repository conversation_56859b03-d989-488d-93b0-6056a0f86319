package de.fellows.microservices.pcb.model

import de.fellows.utils.CurrencyCode
import play.api.libs.functional.syntax.toFunctionalBuilderOps
import play.api.libs.json.{__, Reads, Writes}

import java.time.LocalDate

/** Price offer from manufacturer
  *
  * In the ideal world, we'd like to return delivery date, not the production days. However, there are several issues
  *  with it:
  *    - not all APIs return delivery date, only number of working days to produce the PCB (for example, BetaLayout API)
  *    - in some cases we don't support customer credentials so we cannot be sure that the delivery date is correct as
  *      they can be based in different location compared to our default credentials
  *
  * @param price Price of the item
  * @param productionDays Number of days to produce the item
  */
final case class Offer(
    price: BigDecimal,
    productionDays: Int,
    currency: Option[CurrencyCode],
    oneTimeCosts: Option[BigDecimal]
) {

  /** The way we calculate the production date here is incorrect as it doesn't take into account weekends and holidays.
    * However, it's not possible to calculate the exact date as we don't know about weekends and holidays on
    * the manufacturer's side. So we use the simplest logic here.
    */
  def productionDate: String = LocalDate.now().plusDays(productionDays).toString
}

object Offer {
  implicit val reads: Reads[Offer] =
    ((__ \ "basic_price_net").read[java.math.BigDecimal] and
      (__ \ "deliverytime").read[Int] and
      (__ \ "currency").readNullable[CurrencyCode] and
      (__ \ "one_time_costs").readNullable[java.math.BigDecimal])((price, deliveryTime, currency, cost) =>
      Offer(price, deliveryTime, currency, cost.map(BigDecimal.apply))
    )

  implicit val writes: Writes[Offer] =
    ((__ \ "basic_price_net").write[BigDecimal] and
      (__ \ "deliverytime").write[Int] and
      (__ \ "currency").writeNullable[CurrencyCode] and
      (__ \ "one_time_costs").writeNullable[BigDecimal])((offer: Offer) =>
      (offer.price, offer.productionDays, offer.currency, offer.oneTimeCosts)
    )
}
