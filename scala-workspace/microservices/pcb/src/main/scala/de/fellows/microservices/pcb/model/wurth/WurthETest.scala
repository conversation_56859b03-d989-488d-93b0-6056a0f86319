package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props.ETest

sealed trait WurthETest {
  val value: Int
  val toPCB: ETest
}

private case object WurthETestNo extends WurthETest {
  override val value: Int   = 0
  override val toPCB: ETest = ETest.no
}
private case object WurthETestYes extends WurthETest {
  override val value: Int   = 1
  override val toPCB: ETest = ETest.yes
}

private object WurthETest {

  def apply(value: ETest): WurthETest =
    if (value.value)
      WurthETestYes
    else
      WurthETestNo

  def fromWurth(value: Option[Int]): WurthETest =
    value match {
      case Some(value) => fromWurth(value)
      case None        => apply(ETest.default)
    }

  def fromWurth(value: Int): WurthETest = value match {
    case 1 => WurthETestYes
    case _ => WurthETestNo
  }
}
