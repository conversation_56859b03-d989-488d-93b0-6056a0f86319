package de.fellows.microservices.pcb.model.lq

import de.fellows.luminovo.sourcing.SourcingScenarioId
import de.fellows.microservices.pcb.model.Offer
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerApi, ManufacturerLocation}
import play.api.libs.json.{<PERSON>son, Writes}

case class ManufacturerApiWithInformation(
    api: ManufacturerApi,
    information: Option[String] = None
)

object ManufacturerApiWithInformation {

  implicit val writes: Writes[ManufacturerApiWithInformation] = s =>
    Json.obj(
      "information" -> s.information,
      "api"         -> Json.toJson(s.api.api)
    )
}

/** A set of offers for the given configuration
  */
final case class OfferResponse(
    api: ManufacturerApiWithInformation,
    manufacturer: Manufacturer,
    location: ManufacturerLocation,
    quantity: Int,
    offers: Seq[Offer],
    priceType: PriceType
) {

  /** Returns the fastest delivery
    */
  def fastest: Offer = offers.minBy(_.productionDays)

  /** Returns the best price possible
    */
  def bestPrice: Offer = {
    val order: Ordering[Offer] = (x: Offer, y: Offer) =>
      (x.price).compare(y.price) match {
        case 0      => x.productionDays.compare(y.productionDays)
        case result => result
      }
    offers.min(order)
  }

  /** Returns the best price If a user selects "best price by date", we have to check how it fits in the 3 -20 days
    * range
    *   - if <= 3 days, take the 3 days
    *   - if > 3 days, take the best price which is available for the given date
    */
  def bestPriceByDate(date: String): Offer = {
    val suitableOffers = offers.filter(_.productionDate <= date)
    if (suitableOffers.isEmpty) {
      fastest
    } else {
      copy(offers = offers.filter(_.productionDate <= date)).bestPrice
    }
  }

  /** Returns the price points for Luminovo
    */
  def toPricePoints(
      leadTimes: Seq[LeadTime],
      ignoreLeadTimes: Boolean = false,
      sourcingScenarioId: SourcingScenarioId
  ): Seq[PricePointWithOneTimeCosts] =
    offers match {
      case Nil => Seq()
      case _ =>
        if (ignoreLeadTimes)
          offers.map(x =>
            PricePointWithOneTimeCosts(
              point = PricePointInput(
                quantity = quantity,
                amount = x.price / quantity,
                leadTimeDays = Some(x.productionDays),
                currency = x.currency
              ),
              oneTimeCosts = x.oneTimeCosts.map(x => Seq(Cost(x))),
              priceType = priceType,
              sourcingScenarioId = sourcingScenarioId
            )
          )
        else
          leadTimes.map(leadTime => toPricePoint(leadTime = leadTime, sourcingScenarioId = sourcingScenarioId))
    }

  private def toPricePoint(leadTime: LeadTime, sourcingScenarioId: SourcingScenarioId): PricePointWithOneTimeCosts = {
    val offer = leadTime.preference match {
      case LeadTimePreference.Fastest   => fastest
      case LeadTimePreference.BestPrice => bestPrice
      case LeadTimePreference.BestPriceByDate =>
        leadTime.date.map(date => bestPriceByDate(date)).getOrElse(fastest)
    }
    PricePointWithOneTimeCosts(
      PricePointInput(quantity, offer.price / quantity, Some(offer.productionDays), offer.currency),
      offer.oneTimeCosts.map(x => Seq(Cost(x))),
      priceType,
      sourcingScenarioId
    )
  }
}

object OfferResponse {
  implicit val writes: Writes[OfferResponse] = Json.writes[OfferResponse]
}
