package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.YesNoCapability

object EdgeMetalization {
  val name: String  = "edgeMetalization"
  val label: String = "pcb.board.advanced.edgeMetalization"

  val yes: EdgeMetalization = EdgeMetalization(true)
  val no: EdgeMetalization  = EdgeMetalization(false)

  val default: EdgeMetalization = no

  def apply(value: Option[Boolean]): EdgeMetalization =
    value.fold(default)(EdgeMetalization(_))

  type EdgeMetalizationCapability = YesNoCapability[EdgeMetalization]
}

final case class EdgeMetalization(override val value: <PERSON>olean) extends YesNoPCBProperty {
  val fieldName: String                         = EdgeMetalization.name
  override val legacyNames: Option[Seq[String]] = Some(Seq("edge_plating"))
  val label: String                             = EdgeMetalization.label
}
