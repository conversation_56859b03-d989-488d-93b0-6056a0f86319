package de.fellows.microservices.pcb.model.pcb.capability

import de.fellows.ems.pcb.api.specification.{Side, SilkscreenColor, SoldermaskColor}
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.No
import de.fellows.microservices.pcb.model.pcb.props.BoardHeight.BoardHeightCapability
import de.fellows.microservices.pcb.model.pcb.props.{BoardHeight, BoardWidth}
import de.fellows.microservices.pcb.model.pcb.props.BoardWidth.BoardWidthCapability
import de.fellows.microservices.pcb.model.pcb.props.HardGold.HardGoldCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenColor.SilkscreenColorCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenSide.SilkscreenSideCapability
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskColor.SoldermaskColorCapability
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskSide.SoldermaskSideCapability
import de.fellows.microservices.pcb.model.pcb.props.SurfaceFinish.SurfaceFinishCapability
import de.fellows.microservices.pcb.model.pcb.{BasicBoardProperties, PropertyError}
import zio.prelude.Validation

final case class BoardBasicCapability(
    boardWidth: BoardWidthCapability = new BoardWidthCapability(0, 0),
    boardHeight: BoardHeightCapability = new BoardHeightCapability(0, 0),
    silkscreenSide: SilkscreenSideCapability = new SilkscreenSideCapability(Side.None),
    silkscreenColor: SilkscreenColorCapability = new SilkscreenColorCapability(SilkscreenColor.White),
    hardGold: HardGoldCapability = new HardGoldCapability(No),
    surfaceFinish: SurfaceFinishCapability = new SurfaceFinishCapability(),
    soldermaskSide: SoldermaskSideCapability = new SoldermaskSideCapability(Side.None),
    soldermaskColor: SoldermaskColorCapability = new SoldermaskColorCapability(SoldermaskColor.Green)
) extends BoardCapability {

  def validate(props: BasicBoardProperties): Seq[PropertyError] =
    Validation.validate(
      validateBoardSize(props),
      validate(silkscreenSide, props.silkscreenSide),
      validate(silkscreenColor, props.silkscreenColor),
      validate(hardGold, props.hardGold),
      validate(surfaceFinish, props.surfaceFinish),
      validate(soldermaskSide, props.soldermaskSide),
      validate(soldermaskColor, props.soldermaskColor)
    ).toEither.fold(
      errors => errors.toList,
      _ => Nil
    )

  private def validateBoardSize(props: BasicBoardProperties) =
    // We do the rotated validation first because if both fail,
    // we display the same errors as before (without the board being rotated)
    Validation
      .validate(
        validate(boardWidth, BoardWidth(props.boardHeight.value)),
        validate(boardHeight, BoardHeight(props.boardWidth.value))
      )
      .orElse(
        Validation.validate(
          validate(boardWidth, props.boardWidth),
          validate(boardHeight, props.boardHeight)
        )
      )

  def applyCapabilities(capabilities: Seq[Capability[_]]): BoardBasicCapability = {
    import BoardCapability._
    copy(
      boardWidth = capabilities.getAlternative(boardWidth),
      boardHeight = capabilities.getAlternative(boardHeight),
      silkscreenSide = capabilities.getAlternative(silkscreenSide),
      silkscreenColor = capabilities.getAlternative(silkscreenColor),
      hardGold = capabilities.getAlternative(hardGold),
      surfaceFinish = capabilities.getAlternative(surfaceFinish),
      soldermaskSide = capabilities.getAlternative(soldermaskSide),
      soldermaskColor = capabilities.getAlternative(soldermaskColor)
    )
  }
}
