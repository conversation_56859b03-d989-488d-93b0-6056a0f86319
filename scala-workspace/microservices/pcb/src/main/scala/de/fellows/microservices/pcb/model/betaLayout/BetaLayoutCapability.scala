package de.fellows.microservices.pcb.model.betaLayout

import de.fellows.ems.pcb.api.specification.IPC600Class.{IPC1, IPC2}
import de.fellows.ems.pcb.api.specification.{
  BaseMaterial => PCBBaseMaterial,
  Side,
  SilkscreenColor,
  SurfaceFinish => PCBSurfaceFinish
}
import de.fellows.microservices.pcb.model.pcb._
import de.fellows.microservices.pcb.model.pcb.ManufacturerCapability
import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.{No, Yes}
import de.fellows.microservices.pcb.model.pcb.capability._
import de.fellows.microservices.pcb.model.pcb.props.BaseMaterial.BaseMaterialCapability
import de.fellows.microservices.pcb.model.pcb.props.BoardHeight.BoardHeightCapability
import de.fellows.microservices.pcb.model.pcb.props.BoardWidth.BoardWidthCapability
import de.fellows.microservices.pcb.model.pcb.props.ETest.ETestCapability
import de.fellows.microservices.pcb.model.pcb.props.EdgeMetalization.EdgeMetalizationCapability
import de.fellows.microservices.pcb.model.pcb.props.FinalThickness.FinalThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.HardGold.HardGoldCapability
import de.fellows.microservices.pcb.model.pcb.props.IPCA600Class.IPCA600ClassCapability
import de.fellows.microservices.pcb.model.pcb.props.ImpedanceTested.ImpedanceTestedCapability
import de.fellows.microservices.pcb.model.pcb.props.InnerCopperThickness.InnerCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.MinInnerLayerStructure.MinInnerLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinOuterLayerStructure.MinOuterLayerStructureCapability
import de.fellows.microservices.pcb.model.pcb.props.MinViaDiameter.MinViaDiameterCapability
import de.fellows.microservices.pcb.model.pcb.props.NumberOfLayers.NumberOfLayersCapability
import de.fellows.microservices.pcb.model.pcb.props.OuterCopperThickness.OuterCopperThicknessCapability
import de.fellows.microservices.pcb.model.pcb.props.PeelableMask.PeelableMaskCapability
import de.fellows.microservices.pcb.model.pcb.props.PressFit.PressFitCapability
import de.fellows.microservices.pcb.model.pcb.props.SilkscreenColor.SilkscreenColorCapability
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskSide.SoldermaskSideCapability
import de.fellows.microservices.pcb.model.pcb.props.SurfaceFinish.SurfaceFinishCapability
import de.fellows.microservices.pcb.model.pcb.props.TGValue.TGValueSetCapability
import de.fellows.microservices.pcb.model.pcb.props.ULLayerStack.ULLayerStackCapability
import de.fellows.microservices.pcb.model.pcb.props.{
  FinalThickness,
  Inner35mcr,
  Inner70mcr,
  Outer35mcr,
  Outer70mcr,
  SilkscreenSide
}

object BetaLayoutCapability {
  import de.fellows.microservices.pcb.model.pcb.props.PCBPropertyExtractor._

  val basic = BoardBasicCapability(
    new BoardWidthCapability(0, 430),
    new BoardHeightCapability(0, 380),
    SilkscreenSide.allSidesCapability,
    new SilkscreenColorCapability(SilkscreenColor.White),
    new HardGoldCapability(No),
    new SurfaceFinishCapability(PCBSurfaceFinish.Enig),
    new SoldermaskSideCapability(Side.None, Side.Both)
  )

  val advancedBoardCapability = AdvancedBoardCapability(
    new IPCA600ClassCapability(IPC1, IPC2),
    new ETestCapability(Yes),
    new PressFitCapability(No),
    new ImpedanceTestedCapability(No),
    new PeelableMaskCapability(Side.None),
    new EdgeMetalizationCapability(Yes, No)
  )

  val layerStackCapability = LayerStackCapability(
    ulLayerStack = new ULLayerStackCapability(No),
    numberOfLayers = new NumberOfLayersCapability(props.OneLayer, props.TwoLayers, props.FourLayers),
    finalThickness = FinalThicknessCapability.fromMinMax(
      FinalThickness.Thickness1mm,
      FinalThickness.Thickness16mm
    ),
    baseMaterial = new BaseMaterialCapability(PCBBaseMaterial.FR4),
    outerCopperThickness = new OuterCopperThicknessCapability(Outer35mcr, Outer70mcr),
    innerCopperThickness = new InnerCopperThicknessCapability(),
    minOuterLayerStructure = MinOuterLayerStructureCapability(0.100),
    minInnerLayerStructure = MinInnerLayerStructureCapability(0.100, required = false),
    tgValue = new TGValueSetCapability(required = false, 135, 170)
  )

  val mechanicalCapability = MechanicalCapability(
    new MinViaDiameterCapability(0.1, Float.MaxValue, required = true)
  )

  // BetaLayout's webshop actually requires 1.6mm, but values in the range of 1.0 < thickness <= 1.6
  // are converted to 1.6mm on BetaLayout shop. LQ allows more values to be picked, but it does not allow
  // 1.6, so we allow the closest value, 1.55
  val condition = Seq(
    Condition(
      Rule.GtEq(FinalThickness(1.55)),
      Seq(
        new NumberOfLayersCapability(props.OneLayer, props.TwoLayers, props.FourLayers, props.SixLayers),
        new SurfaceFinishCapability(PCBSurfaceFinish.Enig, PCBSurfaceFinish.HalPbFree)
      )
    ),
    Condition(
      Rule.GtEq(props.NumberOfLayers(4)),
      new InnerCopperThicknessCapability(Inner35mcr, Inner70mcr)
    )
  )

  val capability = ManufacturerCapability(
    basic,
    advancedBoardCapability,
    layerStackCapability,
    mechanicalCapability,
    condition
  )
}
