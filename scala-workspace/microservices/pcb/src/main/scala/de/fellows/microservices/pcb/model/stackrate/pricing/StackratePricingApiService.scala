package de.fellows.microservices.pcb.model.stackrate.pricing

import cats.data.{EitherT, OptionT}
import cats.instances.future._
import de.fellows.app.assembly.commons.{AssemblyReference, SharedAssemblyReference}
import de.fellows.app.assemby.api.{Assembly, AssemblyService, ShareAssemblyTo, SharedAssemblyInfo}
import de.fellows.app.customer.api.{Customer, CustomerService}
import de.fellows.app.price.api._
import de.fellows.app.quotation.QuotationOrigin.APIQuotation
import de.fellows.app.quotation._
import de.fellows.app.supplier
import de.fellows.app.supplier.SupplierService
import de.fellows.app.user.api.Teams.Team
import de.fellows.app.user.api.UserService
import de.fellows.ems.panel.api
import de.fellows.ems.panel.api.{CustomerPanelElement, CustomerPanelHelper, PanelService}
import de.fellows.microservices.pcb._
import de.fellows.microservices.pcb.model.ApiService.{filterApprovedSuppliersWithPricing, CalculatedPanelInfo}
import de.fellows.microservices.pcb.model.lq.PriceType.ListPrice
import de.fellows.microservices.pcb.model.lq._
import de.fellows.microservices.pcb.model.panel.{EmsPreferences, PanelPreferences, PcbMesh}
import de.fellows.microservices.pcb.model.pcb.props.{BoardHeight, BoardWidth}
import de.fellows.microservices.pcb.model.pcb.{Manufacturer, ManufacturerApi, ManufacturerLocation, PCB}
import de.fellows.microservices.pcb.model.{ApiService, Offer, RequestValidation}
import de.fellows.utils.model.ShareId
import de.fellows.utils.{FutureUtils, Region, UUIDUtils}
import io.opentelemetry.api.trace.Span
import play.api.Logging
import sttp.client3.playJson.SttpPlayJsonApi

import java.util.UUID
import scala.annotation.unused
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

case class CalculatedScenarioPrices(
    scenario: ScenarioRequestWithPanel,
    sharedAssembly: SharedAssemblyInfo,
    calculatedPanelInfo: CalculatedPanelInfo,
    panelQuantity: Int,
    prices: Seq[CalculatedPrices]
)

case class CreatedQuotation(
    scenario: ScenarioRequestWithPanel,
    calculatedPanelInfo: CalculatedPanelInfo,
    nre: BigDecimal,
    panelQty: Int,
    quotation: Quotation
)

class StackratePricingApiService(
    implicit
    priceService: PriceService,
    quotationService: QuotationService,
    customerService: CustomerService,
    assemblyService: AssemblyService,
    @unused panelService: PanelService,
    userService: UserService,
    supplierService: SupplierService
) extends SttpPlayJsonApi
    with Logging with SttpHelper with ApiService {

  override def manufacturer: ManufacturerApi =
    ManufacturerApi.Stackrate

  /** Verifies if the customer credentials are correct
    *
    * @return
    */
  override def checkCredentials(
      credentials: Credentials,
      tenant: String
  )(implicit ec: ExecutionContext): Future[Either[String, Boolean]] =
    Future.successful(Right(true))

  /** Create quotes for the given PCB and the list of scenarios/quantities
    *
    * @param pcb            PCB
    * @param scenarios      List of scenarios/quantities of PCBs to order
    * @param emsPreferences Preferences of the EMS customer
    * @param credentials    EMS credentials for the PCB supplier API
    */
  override def makeQuotes(
      tenant: String,
      pcb: PCB,
      manufacturers: Seq[Manufacturer],
      scenarios: Seq[ScenarioRequestWithPanel],
      existingOffers: Seq[ExistingOffer],
      emsPreferences: EmsPreferences,
      credentials: Seq[Credentials],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Seq[ManufacturerApiResponse]] =
    createQuotations(
      requestFromTeam = tenant,
      pcb,
      manufacturers,
      scenarios.filterNot(_.changeStatus == ChangeStatus.Unchanged),
      emsPreferences,
      existingOffers,
      requestValidations
    )

  /** Validate request for API
    *
    * @param pcb            PCB
    * @param quantity       Number of PCB to order
    * @param emsPreferences Preferences of the EMS customer
    */
  override def validateRequest(
      team: String,
      pcb: PCB,
      manufacturer: Seq[Manufacturer],
      quantity: Int,
      emsPreferences: EmsPreferences
  )(implicit ec: ExecutionContext): Future[Seq[RequestValidation]] =
    Future.successful(Seq()) // The stackrate capability check is already done

  def getMesh(pnl: api.CustomerPanel): PcbMesh = {

    def count(a: CustomerPanelElement): (Int, Int) = a match {
      case board: api.CustomerPanelBoard =>
        (board.repeatX.getOrElse(0), board.repeatY.getOrElse(0))
      case block: api.CustomerPanelBlock =>
        val sub = block.elements.map(x => count(x)).reduce((a, b) => (a._1 + b._1, a._2 + b._2))
        (block.repeatX.getOrElse(0) + sub._1, block.repeatY.getOrElse(0) + sub._2)
    }

    val (cols, rows) = pnl.elements.map(count).reduce((a, b) => (a._1 + b._1, a._2 + b._2))
    PcbMesh(
      rows,
      cols
    )
  }

  /** find a matching luminovo manufacturer for a given stackrate supplier
    */
  private def findManufacturerForSupplier(
      manufacturers: Seq[Manufacturer],
      suppliers: Map[UUID, supplier.PCBSupplierDescription],
      supplierId: Option[UUID],
      team: String
  ): Option[(Manufacturer, ManufacturerLocation)] =
    for {
      id       <- supplierId
      supplier <- suppliers.get(id)
      ref      <- supplier.lqReference

      // either the reference is to a supplier or a direct location
      manufacturer <- manufacturers.find { m =>
        m.supplier == ref || m.locations.exists(_.stockLocation == ref)
      }

      // in case it was not a direct location, we still have to find it
      location <- manufacturer.locations.find { l =>
        l.stockLocation == ref || l.region == supplier.region.getOrElse(Region.Unknown)
      }
    } yield manufacturer -> location

  def createQuotations(
      requestFromTeam: String,
      pcb: PCB,
      manufacturers: Seq[Manufacturer],
      scenarios: Seq[ScenarioRequestWithPanel],
      emsPreferences: EmsPreferences,
      existingOffers: Seq[ExistingOffer],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Seq[ManufacturerApiResponse]] = {
    val span = Span.current()
    span.setAttribute("connected_tenants", emsPreferences.connectedTenants.mkString(","))

    val panelPreferences = emsPreferences.panelPreferencesOrDefault
    val priceRequestsByScenario: Map[ScenarioRequestWithPanel, (CalculatedPanelInfo, Int, Seq[ExtendedPriceRequest])] =
      createPriceRequests(
        scenarios = scenarios,
        panelPreferences = panelPreferences,
        pcb = pcb,
        leadTimes = StackratePricingApiService.DEFAULT_LEAD_TIMES
      )

    if (priceRequestsByScenario.exists(_._2._3.nonEmpty)) {
      Future.traverse(emsPreferences.connectedTenants) { tenant =>
        Tracing.withAsyncTrace(s"createQuotationsForTeam ${tenant}") { span =>
          span.setAttribute("pcb_id", pcb.id.toString)
          span.setAttribute("from_team", requestFromTeam)
          span.setAttribute("stackrate_tenant", tenant)

          createQuotationsForTeam(
            tenant = tenant,
            requestFromTeam = requestFromTeam,
            pcb = pcb,
            manufacturers = manufacturers,
            scenariosRequestsWithPanels = scenarios,
            priceRequestsByScenario = priceRequestsByScenario,
            existingOffers = existingOffers,
            requestValidations = requestValidations
          )
        }
      }.map { results =>
        results
          .flatten
          .groupBy(r => (r.manufacturer, r.location))
          .map {
            case ((manufacturer, location), responses) =>
              mergeManufacturerResponses(
                manufacturer = manufacturer,
                manufacturerLocation = location,
                responses = responses
              )
          }
          .toSeq
      }
    } else {
      logger.warn(s"No Price requests generated for pcb ${pcb.id}")
      Future.successful(
        Seq.empty
      )
    }
  }

  private def createQuotationsForTeam(
      tenant: String,
      requestFromTeam: String,
      pcb: PCB,
      manufacturers: Seq[Manufacturer],
      scenariosRequestsWithPanels: Seq[ScenarioRequestWithPanel],
      priceRequestsByScenario: Map[ScenarioRequestWithPanel, (CalculatedPanelInfo, Int, Seq[ExtendedPriceRequest])],
      existingOffers: Seq[ExistingOffer],
      requestValidations: Map[UUID, Seq[RequestValidation]]
  )(implicit ec: ExecutionContext): Future[Seq[ManufacturerApiResponse]] = {
    validateTeam(tenant, manufacturers).flatMap {
      case Some(team) =>
        for {
          (assembly, share) <- createOrGetShare(requestFromTeam, pcb, team)

          reference = SharedAssemblyReference(
            team = share.team,
            id = share.id,
            sharedAssembly = AssemblyReference(
              team = assembly.team,
              id = assembly.id,
              gid = Some(assembly.gid),
              version = share.version
            )
          )

          suppliers <- supplierService._getSuppliers(team.domain).invoke()

          approvedSuppliersWithPricing =
            filterApprovedSuppliersWithPricing(manufacturers, suppliers)
              .flatMap(s => s.id.map(id => (id, s)))

          supplierIds = approvedSuppliersWithPricing.map(_._1)

          prices <- Future.traverse(priceRequestsByScenario.toSeq) {
            case (scenarioRequest, (calculatedPanelInfo, panelQty, requests)) =>
              val panel = scenarioRequest.stackratePanel
              priceService._calculatePricesForShare(
                team = share.team,
                share = share.id,
                specification = pcb.original.specifications.head.id,
                panel = panel.flatMap(_.id.map(_.toString))
              ).invoke(
                FullPriceRequest(
                  requests = requests,
                  supplierIds = supplierIds
                )
              ).map { prices =>
                CalculatedScenarioPrices(
                  scenario = scenarioRequest,
                  sharedAssembly = share,
                  calculatedPanelInfo = calculatedPanelInfo,
                  panelQuantity = panelQty,
                  prices = prices
                )
              }
          }

          supplierMap = approvedSuppliersWithPricing.toMap

          // We only want to consider prices from suppliers that have passed the capability check
          pricesWithNoCapabilityErrors = prices.map { scenarioPrices =>
            scenarioPrices.copy(
              prices = scenarioPrices.prices.map { calculatedPrices =>
                calculatedPrices.copy(
                  pricings = calculatedPrices.pricings.filter { pricing =>
                    findManufacturerForSupplier(
                      manufacturers = manufacturers,
                      suppliers = supplierMap,
                      supplierId = Some(pricing.supplier),
                      team = team.domain
                    ) match {
                      case Some((manufacturer, location)) =>
                        val manufacturerCapabilityErrors = requestValidations.get(manufacturer.supplier)
                        val capabilityErrors = manufacturerCapabilityErrors
                          .flatMap(_.find(_.location.stockLocation == location.stockLocation))
                          .flatMap(_.validation)

                        val hasNoCapabilityErrors = capabilityErrors.isEmpty
                        val isPresentInScenario = scenariosRequestsWithPanels
                          .find(_.sourcingScenarioId == scenarioPrices.scenario.sourcingScenarioId)
                          .exists { sourcingScenario =>
                            sourcingScenario
                              .manufacturers
                              .exists(
                                _.locations.exists(_.stockLocation == location.stockLocation)
                              )
                          }

                        hasNoCapabilityErrors && isPresentInScenario

                      case None => false
                    }
                  }
                )
              }
            )
          }

          _ <- quotationService._deleteQuotationsForShare(team.domain).invoke(
            SharedQuotationDeletion(
              shares = Seq(share),
              names = None,
              requestIds = Some(scenariosRequestsWithPanels.map(_.sourcingScenarioId.value.toString))
            )
          )

          quotationsWithPanel <- createQuotationsFromPrices(
            team,
            share,
            reference,
            requestFromTeam,
            pricesWithNoCapabilityErrors
          )

        } yield {
          logger.info(s"validate for share ${share}: ${pricesWithNoCapabilityErrors}")

          val suppliersByLqReference = approvedSuppliersWithPricing
            .flatMap {
              case t @ (_, supplierDescription) =>
                supplierDescription.lqReference.map { lqReference =>
                  (lqReference, t)
                }
            }
            .groupMap(_._1)(_._2)

          def getSupplierForManufacturer(
              manufacturer: Manufacturer,
              manufacturerLocation: ManufacturerLocation
          ): Option[(UUID, supplier.PCBSupplierDescription)] =
            // check if there's a stackrate supplier that refers to LQ's supplier location directly
            suppliersByLqReference
              .get(manufacturerLocation.stockLocation)
              .flatMap(_.headOption)
              .orElse {
                // check if there's a stackrate supplier that refers to LQ's supplier directly
                suppliersByLqReference
                  .get(manufacturer.supplier)
                  .flatMap { suppliers =>
                    suppliers.find(_._2.region.getOrElse(Region.Unknown) == manufacturerLocation.region)
                  }
              }

          manufacturers.flatMap { manufacturer =>
            manufacturer.locations.flatMap { manufacturerLocation =>
              val response: Option[Either[PcbServerError, ManufacturerQuote]] = {
                val manufacturerCapabilityErrors = requestValidations.get(manufacturer.supplier)
                val mbRequestValidations = manufacturerCapabilityErrors
                  .flatMap(_.find(_.location.stockLocation == manufacturerLocation.stockLocation))

                /* 1) we check if the manufacturer has any capability errors
                 * 2) If there aren't any, we check if the manufacturer has quotations for the associated supplier (if any)
                 * 3) If there aren't any quotes, we return an empty list of quotes *if* there's a capability table defined
                 *
                 * We do the 3rd step because otherwise the manufacturer would be missing from the response.
                 * This would cause the frontend to show a "supplier not evaluated" because for the price radar logic
                 * we have:
                 *  - an approved supplier with no offers or results
                 *  - maybe we have offers or just results for other suppliers, which means we have checked for offers already
                 *
                 * Which could either mean:
                 *  - the supplier was not approved when the offer fetching took place
                 *  - there was no status returned for this supplier
                 *
                 * Which we can't differentiate between.
                 */

                mbRequestValidations
                  .flatMap { requestValidations =>
                    // Even if there are no errors, this manufacturer was referenced in the capability check
                    // so we need to show a result for it
                    requestValidations.validation.map(Left(_))
                  }
                  .orElse {
                    getSupplierForManufacturer(manufacturer, manufacturerLocation)
                      .map {
                        case (supplierId, _) =>
                          quotationsWithPanel.flatMap { quotationsWithPanel =>
                            val manufacturerQuote = StackratePricingApiService.manufacturerQuoteForSupplier(
                              quotationsWithPanel = quotationsWithPanel,
                              manufacturer = manufacturer,
                              manufacturerLocation = manufacturerLocation,
                              supplierId = supplierId,
                              sharedPcbId = Some(ShareId(reference.id))
                            )
                            if (manufacturerQuote.quoteResponses.nonEmpty) {
                              Right(manufacturerQuote)
                            } else {
                              val (pricingErrors, pricingBreaks) =
                                pricesWithNoCapabilityErrors
                                  .flatMap { p =>
                                    p.prices.flatMap(
                                      _.pricings.flatMap {
                                        case p: PricingError if p.supplier == supplierId => Some(Left(p))
                                        case p: PricingBreak if p.supplier == supplierId => Some(Right(p))
                                        case _                                           => None
                                      }
                                    )
                                  }
                                  .partitionMap(identity)

                              (pricingErrors, pricingBreaks) match {
                                case (pricingErrors, _) if pricingErrors.nonEmpty =>
                                  Left(StackratePricingErrors(
                                    pricingErrors.map(StackratePricingMessage.from).distinctBy(_.message)
                                  ))

                                case (_, pricingBreaks) if pricingBreaks.nonEmpty =>
                                  Left(StackratePricingBreaks(
                                    pricingBreaks.map(StackratePricingMessage.from).distinctBy(_.message)
                                  ))

                                case _ => Right(manufacturerQuote)
                              }
                            }
                          }
                      }
                  }.orElse {
                    mbRequestValidations.map(_ => Right(ManufacturerQuote(Seq.empty)))
                  }
              }

              response.map { response =>
                ManufacturerApiResponse(
                  api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate),
                  manufacturer = manufacturer,
                  location = manufacturerLocation,
                  response = response
                )
              }
            }
          }
        }

      case _ => Future.successful(Seq.empty)
    }
  }

  private def mergeManufacturerResponses(
      manufacturer: Manufacturer,
      manufacturerLocation: ManufacturerLocation,
      responses: Seq[ManufacturerApiResponse]
  ): ManufacturerApiResponse =
    responses match {
      case Seq(response) => response
      case responses     =>
        // If there are more than one response for a manufacturer's location,
        // we need to somehow merge them.
        // We do this in a rather optimistic way:
        //  - if there's at least one quote, we show that
        //  - otherwise, if there's at least one error, we try to show the "easiest" to fix
        //  - otherwise, we show an empty quote
        val (errors, quotes) = {
          val (errorsAcc, quotesAcc) =
            responses
              .foldLeft((Seq.newBuilder[PcbServerError], Seq.newBuilder[QuoteResponse])) {
                case ((errors, quotes), response) =>
                  response.response match {
                    case Left(error)  => (errors.addOne(error), quotes)
                    case Right(quote) => (errors, quotes.addAll(quote.quoteResponses))
                  }
              }

          (errorsAcc.result(), quotesAcc.result())
        }

        // if there's at least 1 quote, do we care about the errors?
        // probably not, we can just show the offer we got
        val response = (quotes, errors) match {
          case (quotes, _) if quotes.nonEmpty => Right(ManufacturerQuote(quotes))

          case (_, errors) if errors.nonEmpty =>
            val propertyErrors = errors.collect { case p: PropertyErrors => p }
            val easiest        = propertyErrors.minByOption(d => d.errors.size)

            Left(easiest.getOrElse(errors.head))

          case _ => Right(ManufacturerQuote(Seq.empty))
        }

        ManufacturerApiResponse(
          api = ManufacturerApiWithInformation(ManufacturerApi.Stackrate),
          manufacturer = manufacturer,
          location = manufacturerLocation,
          response = response
        )
    }

  private def createPriceRequests(
      scenarios: Seq[ScenarioRequestWithPanel],
      panelPreferences: PanelPreferences,
      pcb: PCB,
      leadTimes: Seq[Int]
  ): Map[ScenarioRequestWithPanel, (CalculatedPanelInfo, Int, Seq[ExtendedPriceRequest])] = {
    def createPriceRequestsImpl(
        pcbWidth: BoardWidth,
        pcbHeight: BoardHeight
    ): Map[ScenarioRequestWithPanel, (CalculatedPanelInfo, Int, Seq[ExtendedPriceRequest])] =
      scenarios.flatMap { scenarioRequestWithPanel =>
        // dimensions and panel preferences are the same. The only variable is the quantity, so panels are the same for each particular scenario
        ApiService.calculateWorkingPanelInfo(
          pcbWidth = pcbWidth.value,
          pcbHeight = pcbHeight.value,
          quantity = scenarioRequestWithPanel.quantity,
          panelInfo = scenarioRequestWithPanel.panelInfo,
          panelPreferences = panelPreferences,
          panelConstraints = None
        ).map { calculatedPanelInfo =>
          val (
            panelWidth,
            panelHeight,
            pcbsPerPanel,
            depanelization
          ) =
            calculatedPanelInfo match {
              case p: CalculatedPanelInfo.FromPanelDetails =>
                (
                  p.panelDistribution.panel.widthInMm,
                  p.panelDistribution.panel.heightInMm,
                  p.panelDistribution.mesh.size,
                  p.depanelization
                )
              case p: CalculatedPanelInfo.FromExisting =>
                (
                  p.existing.panelWidth,
                  p.existing.panelHeight,
                  p.existing.numberOfPcbs,
                  p.existing.depanelization
                )
            }

          // if there is a stackrate panel, we dont need the helper hack
          val helper = scenarioRequestWithPanel.stackratePanel match {
            case Some(panel) => None
            case None => Some(CustomerPanelHelper(
                width = Some(panelWidth),
                height = Some(panelHeight),
                weight = None,
                count = pcbsPerPanel
              ))
          }

          val requests = leadTimes.map { lt =>
            ExtendedPriceRequest(
              PriceRequest(
                count = scenarioRequestWithPanel.quantity,
                delivery = lt,
                nre = Some(true),
                assembly = None,
                specification = None,
                panel = None
              ),
              panel = helper,
              pcb = None,
              specification = None,
              specificationUser = None,
              layerstack = None,
              customer = None
            )
          }

          scenarioRequestWithPanel -> (calculatedPanelInfo, calculatedPanelInfo.numberOfPanels.value, requests)
        }.toOption
      }.toMap

    val properties = pcb.properties.basic

    if (properties.boardWidth.value > 0 && properties.boardHeight.value > 0) {
      createPriceRequestsImpl(properties.boardWidth, properties.boardHeight)
    } else {
      logger.error(s"no board width or height for pcb ${pcb.id}")
      Map.empty
    }
  }

  def isValidTeam(
      team: Team,
      suppliers: Seq[supplier.PCBSupplierDescription],
      manufacturers: Seq[Manufacturer]
  ): Boolean =
    suppliers.exists { supplierDescription =>
      manufacturers.exists { manufacturer =>
        val containsSupplier = supplierDescription.lqReference.contains(manufacturer.supplier)
        val containsStockLocation =
          manufacturer.locations.exists(l => supplierDescription.lqReference.contains(l.stockLocation))

        containsSupplier || containsStockLocation
      }
    }

  private def validateTeam(
      team: String,
      manufacturers: Seq[Manufacturer]
  )(implicit ec: ExecutionContext): Future[Option[Team]] =
    (for {
      team <- OptionT {
        userService._getTeam(team).invoke().map(Some(_))
          .recover { case e =>
            logger.error(s"error getting team ${team}", e)
            None
          }
      }

      // we select the teams which have a corresponding manufacturer that is not excluded in luminovo
      suppliers <- OptionT.liftF(supplierService._getSuppliers(team.domain).invoke())

      mbTeam <- OptionT.when(isValidTeam(team, suppliers, manufacturers))(team)
    } yield mbTeam).value

  private def createOrGetShare(
      fromTeam: String,
      pcb: PCB,
      team: Team
  )(implicit ec: ExecutionContext): Future[(Assembly, SharedAssemblyInfo)] =
    for {
      customers <- customerService._findCustomerByLumiquote(team.domain, fromTeam).invoke()
      assembly  <- assemblyService._getAssembly(fromTeam, pcb.assemblyId.value).invoke()
      share <- assemblyService._shareAssembly(fromTeam, pcb.assemblyId.value, pcb.id.value).invoke(
        ShareAssemblyTo(
          team = team.domain,
          customer = customers.headOption.flatMap(_.id)
        )
      )
    } yield {
      val shareInfo = SharedAssemblyInfo(share.team, share.id, share.ref.version, share.created)
      assembly.assembly -> shareInfo
    }

  private def createQuotationsFromPrices(
      team: Team,
      share: SharedAssemblyInfo,
      reference: SharedAssemblyReference,
      fromTeam: String,
      pricesss: Seq[CalculatedScenarioPrices]
  )(implicit ec: ExecutionContext): Future[Either[PcbServerError, Seq[CreatedQuotation]]] = {
    // we create a quotation for every scenario. Since luminovo keeps panel info and one time costs on a quote level, we need a different quotation for each
    // the panel depends only on the quantity, so that is enough.
    // the one time costs though do not. so we groups by one time costs too.
    // But most pricings will have one time costs only depending on quantity, so it'll be 1:1 in most cases

    Future.sequence(
      pricesss.map { scenarioPrice =>
        val calculatedPanelInfo = scenarioPrice.calculatedPanelInfo
        // remove price points with a value of 0
        val filteredPrices = scenarioPrice.prices.map { x =>
          x.copy(pricings =
            x.pricings
              .filter(x =>
                x match {
                  case x: CalculatedPricing =>
                    (x.sumDetails.fixSum + x.sumDetails.flexSum + x.sumDetails.nreSum.getOrElse(0)) > 0
                  case _ => true
                }
              )
          )
        }.filter(_.pricings.nonEmpty)

        // create the quotation items
        val createdItemsFuture = Future.sequence(
          filteredPrices.flatMap { calculatedPrices =>
            calculatedPrices.pricings
              .collect {
                case r: CalculatedPricing => r // we only care about successful price calculations at this point
              }
              .map { result =>
                val item = QuotationItemInfo(
                  specification = calculatedPrices.request.specification,
                  supplier = Some(result.supplier),
                  panel =
                    scenarioPrice.scenario.stackratePanel.flatMap(
                      _.id
                    ),
                  technology = None,
                  delivery = Some(calculatedPrices.request.delivery),
                  quantity = Some(calculatedPrices.request.count),
                  panelQuantity = Some(scenarioPrice.panelQuantity),
                  nre = result.sumDetails.nreSum,
                  unitPrice = Some(result.unitPrice),
                  userDelivery = Some(calculatedPrices.request.delivery),
                  userQuantity = Some(calculatedPrices.request.count),
                  userPanelQuantity = Some(scenarioPrice.panelQuantity),
                  userNRE = result.sumDetails.nreSum,
                  userUnitPrice = Some(result.unitPrice),
                  userTotal = Some(result.price),
                  userMarkup = None,
                  total = Some(result.price),
                  details = Some(result.tables),
                  currency = Some(result.currency)
                )

                quotationService
                  ._createQuotationItem(share.team)
                  .invoke(
                    QuotationItemCreation(
                      assembly = reference,
                      name = None,
                      description = None,
                      info = Some(item)
                    )
                  )
                  .map(i => Right(item.nre.getOrElse(BigDecimal(0.0)) -> i))
                  .recover {
                    case NonFatal(e) =>
                      Tracing.exceptionError(e)
                      Left(ServerError(s"Failed to create quotation items: ${e}"))
                  }
              }
          }
        ).map { list =>
          val (errors, quotes) = list.partitionMap(identity)

          if (quotes.nonEmpty) {
            Right(
              quotes
                .groupBy(_._1)
                .map { // group by nre and keep only the item as the value
                  case (k, v) => k -> v.map(_._2)
                }
                .toSeq
            )
          } else {
            errors.headOption match {
              case Some(error) => Left(error)
              case None        => Right(Seq.empty)
            }
          }
        }

        // create the quotation with the previously created items
        (for {
          customers <- EitherT.liftF[Future, ServerError, Seq[Customer]](
            customerService._findCustomerByLumiquote(team.domain, fromTeam).invoke()
          )

          customer = customers.headOption
          _        = logger.info(s"found customer for ${team} ${fromTeam}: ${customer}")

          createdItems <- EitherT(createdItemsFuture)

          quotations <- EitherT {
            val (rows, columns, pcbsPerPanel) = calculatedPanelInfo match {
              case p: CalculatedPanelInfo.FromPanelDetails =>
                (
                  Some(p.panelDistribution.mesh.rows),
                  Some(p.panelDistribution.mesh.columns),
                  p.panelDistribution.mesh.size
                )
              case p: CalculatedPanelInfo.FromExisting =>
                (
                  None,
                  None,
                  p.existing.numberOfPcbs
                )
            }

            Future.sequence(
              createdItems.map { x =>
                val (nre, items) = x
                quotationService
                  ._createQuotation(team.domain)
                  .invoke(
                    QuotationCreation(
                      name = None,
                      assembly = reference,
                      customerId = customer.flatMap(_.id).getOrElse(UUIDUtils.nil),
                      externalID = None,
                      description = None,
                      requestID = Some(scenarioPrice.scenario.sourcingScenarioId.value.toString),
                      requestBy = None,
                      assignee = None,
                      contactId = None,
                      billing = None,
                      shipping = None,
                      publicNotes = Some(
                        s"""
                             |Quotation for Luminovo Scenario of quantity : ${scenarioPrice.panelQuantity}
                             |panels with ${pcbsPerPanel} pcbs each (${columns.fold("-")(_.toString)}x${rows.fold("-")(
                            _.toString
                          )}) and NRE of ${nre} \n
                             |""".stripMargin
                      ),
                      validFor = None,
                      items = Some(items.map(_.id)),
                      requestDate = None,
                      origin = APIQuotation
                    )
                  ).map { qis =>
                    Right(
                      CreatedQuotation(
                        scenario = scenarioPrice.scenario,
                        calculatedPanelInfo = calculatedPanelInfo,
                        nre = nre,
                        panelQty = scenarioPrice.panelQuantity,
                        quotation = qis
                      )
                    )
                  }.recover {
                    case NonFatal(e) =>
                      Tracing.exceptionError(e)
                      Left(ServerError(s"Failed to create quotations: ${e}"))
                  }
              }
            )
              .map { createdQuotations =>
                val (errors, quotations) = createdQuotations.partitionMap(identity)

                if (quotations.nonEmpty) {
                  Right(quotations)
                } else {
                  errors.headOption match {
                    case Some(error) => Left(error)
                    case None        => Right(Seq.empty)
                  }
                }
              }
          }
        } yield quotations).value
      }
    ).map { results =>
      val (errors, quotes) = results.partitionMap(identity)

      if (quotes.nonEmpty) {
        Right(quotes.flatten)
      } else {
        errors.headOption match {
          case Some(error) => Left(error)
          case None        => Right(Seq.empty)
        }
      }
    }
  }

  @unused private def getPanelsForShares(
      panel: Option[api.CustomerPanel],
      shares: Seq[SharedAssemblyInfo]
  )(implicit ec: ExecutionContext): Future[Seq[(UUID, Option[api.CustomerPanelUsageState])]] =
    FutureUtils.option(panel.map { cp =>
      Future.sequence(shares.map { share =>
        panelService._getWorkingPanelUsagesForShare(share.team, share.id).invoke()
          .map { x =>
            share.id -> x.find(_.customerPanel == cp.id.get)
          }
      })
    }).map(_.getOrElse(Seq()))
}

object StackratePricingApiService {
  // TODO something sensible or user configurable
  val DEFAULT_LEAD_TIMES: Seq[Int] = Range.inclusive(1, 15) ++ Range.inclusive(20, 40, 5)

  def manufacturerQuoteForSupplier(
      quotationsWithPanel: Seq[CreatedQuotation],
      manufacturer: Manufacturer,
      manufacturerLocation: ManufacturerLocation,
      supplierId: UUID,
      sharedPcbId: Option[ShareId]
  ): ManufacturerQuote = {
    val quoteResponses = quotationsWithPanel.flatMap { createdQuotation =>
      val quotation = createdQuotation.quotation
      val panelQty  = createdQuotation.panelQty

      val items = quotation
        .items
        .getOrElse(Seq.empty)
        .filter(
          _.resolved.exists(_.info.exists(_.supplier.exists(_ == supplierId)))
        )

      val offers = items.flatMap(_.resolved).map { item =>
        Offer(
          price = item.info.get.total.get.floatValue,
          productionDays = item.info.get.delivery.get.intValue,
          currency = item.info.get.currency,
          oneTimeCosts = item.info.get.nre.flatMap {
            case x if x == 0 => None
            case x           => Some(x.floatValue)
          }
        )
      }

      Option.when(offers.nonEmpty) {
        QuoteResponse(
          offerResponse = OfferResponse(
            api = ManufacturerApiWithInformation(
              ManufacturerApi.Stackrate,
              Some(quotation.team)
            ),
            manufacturer = manufacturer,
            location = manufacturerLocation,
            quantity = panelQty,
            priceType =
              ListPrice, // TODO stackrate should tell us whether it is a list price or contract price
            offers = offers
          ),
          calculatedPanelInfo = createdQuotation.calculatedPanelInfo,
          quoteId = quotation.name,
          url = None,
          sourcingScenarioId = createdQuotation.scenario.sourcingScenarioId,
          existingOfferId = None,
          sharedPcbId = sharedPcbId
        )
      }
    }

    ManufacturerQuote(quoteResponses)
  }
}
