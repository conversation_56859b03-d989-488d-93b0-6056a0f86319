package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.{CtiClass => CtiClassEnum}

object CtiClass {
  val name: String  = "ctiClass"
  val label: String = "pcb.board.advanced.ctiClass"

  def none: CtiClass = CtiClass(Some(CtiClassEnum.None))
}

final case class CtiClass(override val value: Option[CtiClassEnum]) extends PCBOptionalProperty[CtiClassEnum] {
  val fieldName: String = CtiClass.name
  val label: String     = CtiClass.label
  val unit: String      = ""
}
