package de.fellows.microservices.pcb.model.safePcb

/** Describes the content of the panel (single PCB or panel with same PCBs on it)
  */
sealed trait SafePcbPanelMode {
  val value: Int
}

object SafePcbPanelMode {

  /** https://www.notion.so/luminovo/Safe-PCB-API-8eaeac7653a040d9bee44df0ac0cfdb0
    *
    * From page 6 of PCB Compatibility Guide:
    * Single mode
    */
  case object SafePcbPanelSingleMode extends SafePcbPanelMode {
    val value: Int = 11
  }

  /** https://www.notion.so/luminovo/Safe-PCB-API-8eaeac7653a040d9bee44df0ac0cfdb0
    *
    * From page 6 of PCB Compatibility Guide:
    * Panel mode with same circuits ; Ask Safe-PCB engineer to create panel (using value parsed in API)
    */
  case object SafePcbPanelMode extends SafePcbPanelMode {
    val value: Int = 21
  }

  /** https://www.notion.so/luminovo/Safe-PCB-API-8eaeac7653a040d9bee44df0ac0cfdb0
    *
    * From page 6 of PCB Compatibility Guide:
    * Panel mode with same circuits ; Ask Safe-PCB engineer to follow instructions inside the gerber file
    */
  case object SafePcbGerberPanelMode extends SafePcbPanelMode {
    val value: Int = 22
  }
}
