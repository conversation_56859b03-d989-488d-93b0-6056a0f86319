package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.microservices.pcb.model.pcb.capability.YesNoCapability.YesNoCapability

object ETest {
  val name: String  = "eTest"
  val label: String = "pcb.board.advanced.eTest"

  val yes: ETest     = ETest(true)
  val no: ETest      = ETest(false)
  val default: ETest = yes

  def apply(value: Option[Boolean]): ETest =
    value.fold(default)(ETest(_))

  type ETestCapability = YesNoCapability[ETest]
}

final case class ETest(override val value: Boolean) extends YesNoPCBProperty {
  val fieldName: String = ETest.name
  val label: String     = ETest.label
}
