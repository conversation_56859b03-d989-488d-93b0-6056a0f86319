package de.fellows.microservices.pcb.model.panel

import de.fellows.luminovo.LuminovoJson
import de.fellows.microservices.pcb.client.luminovo.LuminovoClient
import de.fellows.microservices.pcb.model.Millimeters
import de.fellows.microservices.pcb.model.wurth.WurthOrderingFiles
import de.fellows.microservices.pcb.{decodeToken, AuthToken, ServiceError}
import de.fellows.utils.security.{Auth0Token, GenericTokenContent}
import play.api.libs.json.{Format, Json, JsonConfiguration}
import de.fellows.luminovo.panel.Depanelization

import scala.concurrent.{ExecutionContext, Future}

/** EMS Preferences for production of a deliver panel .
  *
  * @param panelPreferences Preferences for the delivery panel
  * @param wurthOrderingFiles Defines which files should be included in the order (Wurth specific, for now)
  * @param additionalDocumentation True if additional documentation should be included into the order (increases the price)
  * @param connectedTenants List of tenants to get stackrate pricings from
  */
final case class EmsPreferences(
    panelPreferences: Option[PanelPreferences],
    wurthOrderingFiles: WurthOrderingFiles = WurthOrderingFiles.WurthNoOrdering,
    additionalDocumentation: Boolean = false,
    connectedTenants: Seq[String] = Seq()
) {
  def panelPreferencesOrDefault: PanelPreferences =
    panelPreferences.getOrElse(PanelPreferences.Empty)
}

object EmsPreferences {

  /** Returns constraints for delivery panel for the given tenant
    */
  def fromToken()(implicit
      luminovoClient: LuminovoClient,
      ec: ExecutionContext,
      token: AuthToken
  ): Future[Either[ServiceError, EmsPreferences]] =
    decodeToken(token) match {
      case Left(value)              => Future.successful(Left(value))
      case Right(value: Auth0Token) => fromDecodedToken(value)
      case Right(_)                 => Future.successful(Right(EmsPreferences.default("")))
    }

  def fromDecodedToken(
      decodedToken: GenericTokenContent
  )(
      implicit
      luminovoClient: LuminovoClient,
      ec: ExecutionContext,
      token: AuthToken
  ): Future[Either[ServiceError, EmsPreferences]] =
    preferences(decodedToken.getTeam)

  /** Returns preferences by EMS providers
    */
  def preferences(tenant: String)(implicit
      luminovoClient: LuminovoClient,
      ec: ExecutionContext,
      token: AuthToken
  ): Future[Either[ServiceError, EmsPreferences]] =
    luminovoClient
      .getGlobalPanelPreferences
      .map(_.map(prefs => fromPanelPreferences(tenant, prefs)))

  def preferencesNoToken(tenant: String)(implicit
      luminovoClient: LuminovoClient,
      ec: ExecutionContext
  ): Future[Either[ServiceError, EmsPreferences]] =
    luminovoClient
      .getGlobalPanelPreferencesNoToken(tenant)
      .map(_.map(prefs => fromPanelPreferences(tenant, prefs)))

  def fromPanelPreferences(tenant: String, panelPreferences: Option[PanelPreferences]): EmsPreferences = {
    import Tenants._
    val emsPreferences = tenant match {
      case t @ (Tenants.Zollner | ZollnerUsa)                   => zollner(t)
      case t @ (Tenants.Bmk | Tenants.BmkSeries)                => bmk(t)
      case t @ Tenants.PcbWhizEu                                => pcbwhiz(t)
      case t @ (Tenants.Connectgroup | Tenants.ConnectgroupDev) => connectgroup(t)
      case x                                                    => default(x)
    }
    emsPreferences.copy(panelPreferences = panelPreferences)
  }

  def testPreferences(tenant: String): EmsPreferences =
    fromPanelPreferences(tenant, panelPreferences = None)

  /** Preferences are taken from here:
    * https://www.notion.so/luminovo/Building-Backlog-PCB-956f588176b746d497fd8459b7f12c82?p=885f5e9e008e4ea9addb222433207bcf&pm=s
    */
  private def zollner(tenantId: String): EmsPreferences =
    EmsPreferences(
      panelPreferences = None,
      wurthOrderingFiles = WurthOrderingFiles.WurthWorkingGerber,
      connectedTenants = Seq.empty
    )

  /** Preferences are taken from here:
    * https://www.notion.so/luminovo/Building-Backlog-PCB-956f588176b746d497fd8459b7f12c82?p=885f5e9e008e4ea9addb222433207bcf&pm=s
    */
  def bmk(tenantId: String): EmsPreferences =
    EmsPreferences(
      panelPreferences = None,
      wurthOrderingFiles = WurthOrderingFiles.WurthWorkingGerber,
      additionalDocumentation = true,
      connectedTenants = Seq.empty
    )

  def pcbwhiz(tenantId: String): EmsPreferences =
    default(tenantId).copy(
      panelPreferences = None,
      wurthOrderingFiles = WurthOrderingFiles.WurthNoOrdering
    )

  def connectgroup(tenantId: String): EmsPreferences =
    default(tenantId).copy(
      panelPreferences = None,
      wurthOrderingFiles = WurthOrderingFiles.WurthWorkingGerber
    )

  /** Default preferences are set to be as wide as possible.
    *
    * We also set the maximum number of PCBs per panel to 1 to keep the same logic as before for the clients without
    * explicitly defined constraints.
    */
  def default(tenantId: String): EmsPreferences =
    EmsPreferences(
      panelPreferences = None,
      wurthOrderingFiles = WurthOrderingFiles.WurthNoOrdering,
      connectedTenants = Seq.empty
    )
}

/** EMS Preferences for production of a deliver panel .
  *
  * @param minWidth Minimum possible width of the panel.
  * @param minHeight Minimum possible height of the panel.
  * @param maxWidth Maximum possible width of the panel.
  * @param maxHeight Maximum possible height of the panel.
  * @param maxPCBs Maximum number of PCBs that can be placed on the panel.
  * @param padding Padding for (x, y) axis.
  * @param spacing Gap in (x,y) axis.
  * @param depanelization VCut or Milling
  */
final case class PanelPreferences(
    minWidth: Millimeters,
    minHeight: Millimeters,
    maxWidth: Millimeters,
    maxHeight: Millimeters,
    maxPCBs: Option[Int],
    padding: PanelPadding,
    spacing: PanelGap,
    depanelization: Depanelization
) {
  def maxW: Millimeters = maxWidth - 2 * padding.widthInMm

  def maxH: Millimeters = maxHeight - 2 * padding.heightInMm

  def minW: Millimeters = minWidth - 2 * padding.widthInMm

  def minH: Millimeters = minHeight - 2 * padding.heightInMm

  def minPanel: PanelDimensions = new PanelDimensions(minW, minH)

  def maxPanel: PanelDimensions = new PanelDimensions(maxW, maxH)

  /** Returns the constraints where width is always more or equal to height. As minimum and maximum panels could have
    * different rotations, we returns two sets of panels with modified width, height and correct paddings and spaces
    */
  def harmonize: (PanelBound, PanelBound) = {
    val minPanel =
      if (minHeight > minWidth) {
        PanelBound(
          w = minHeight,
          h = minWidth,
          padding = DistributionPanelPadding.fromPanelPadding(padding.swap),
          gap = spacing.swap
        )
      } else {
        PanelBound(
          w = minWidth,
          h = minHeight,
          padding = DistributionPanelPadding.fromPanelPadding(padding),
          gap = spacing
        )
      }
    val maxPanel =
      if (maxHeight > maxWidth) {
        PanelBound(
          w = maxHeight,
          h = maxWidth,
          padding = DistributionPanelPadding.fromPanelPadding(padding.swap),
          gap = spacing.swap
        )
      } else {
        PanelBound(
          w = maxWidth,
          h = maxHeight,
          padding = DistributionPanelPadding.fromPanelPadding(padding),
          gap = spacing
        )
      }
    (minPanel, maxPanel)
  }

  def harmonizeNoRotation: (PanelBound, PanelBound) = {
    val minPanel =
      PanelBound(
        w = minWidth,
        h = minHeight,
        padding = DistributionPanelPadding.fromPanelPadding(padding),
        gap = spacing
      )
    val maxPanel =
      PanelBound(
        w = maxWidth,
        h = maxHeight,
        padding = DistributionPanelPadding.fromPanelPadding(padding),
        gap = spacing
      )
    (minPanel, maxPanel)
  }

  /** Apply min/max limits from other set of constraints
    */
  def limit(constraints: PanelConstraints): PanelPreferences = {
    val minLimit = Rectangle(constraints.minWidth, constraints.minHeight).makeWidthLarger
    val min      = Rectangle(minHeight, minWidth).makeWidthLarger.adjustUp(minLimit)
    val maxLimit = Rectangle(constraints.maxWidth, constraints.maxHeight).makeWidthLarger
    val max      = Rectangle(maxWidth, maxHeight).makeWidthLarger.adjustDown(maxLimit)
    this.copy(
      minWidth = min.widthInMm,
      minHeight = min.heightInMm,
      maxWidth = max.widthInMm,
      maxHeight = max.heightInMm
    )
  }
}

object PanelPreferences {
  val Empty: PanelPreferences = PanelPreferences(
    minWidth = 0,
    minHeight = 0,
    maxWidth = Float.MaxValue,
    maxHeight = Float.MaxValue,
    maxPCBs = Some(1),
    padding = Rectangle.zero,
    spacing = Rectangle.zero,
    depanelization = Depanelization.VCut
  )

  implicit val format: Format[PanelPreferences] = {
    implicit val config: JsonConfiguration          = LuminovoJson.configuration
    implicit val formatRectangle: Format[Rectangle] = Json.format[Rectangle]

    Json.format[PanelPreferences]
  }
}

object Tenants {
  val Zollner     = "zollner"
  val ZollnerDemo = "zollner-demo"
  val ZollnerUsa  = "zollner-usa"

  val Bmk       = "bmk"
  val BmkSeries = "bmk-series"

  val Central  = "central"
  val Luminovo = "luminovo"
  val Present  = "present"

  val Gatema = "gatema"

  val Starteam = "starteam"
  val EPN      = "epn"
  val MOS      = "mos"

  val NCAB = "ncab"

  val PcbWhizEu   = "pcbwhiz-eu"
  val PcbWhizApac = "pcbwhiz-apac"

  val Connectgroup    = "connectgroup"
  val ConnectgroupDev = "connectgroup-dev"
}
