package de.fellows.microservices.pcb.model.ibr.api

import play.api.libs.json.{Format, JsError, JsNull, JsNumber, JsObject, JsString, JsSuccess, JsValue, Reads, Writes}

import scala.util.{Failure, Success, Try}

/** Description of a PCB for the IBR API.
  *
  * Documentation is available [[https://www.notion.so/luminovo/IBR-Ringler-API-documentation-7fa2deee14c44488849f11baed295cec here]]
  */
case class IbrApiPCB(
    Laenge: Double,
    Breite: Double,
    Lagen: Int,
    ULKanada: Option[Int],
    Stiffener: Option[String],
    Fraesen: Option[Int],
    Nutzenzahl: Option[Int],
    GoldArea: Option[Double],
    AnzahlBohrungen: Option[Int],
    PDTop: Option[Int],
    PDBot: Option[Int],
    BlindVias: Option[Int],
    Nutzentyp: Option[Int],
    SprungRitzen: Option[Int],
    DickeEnddickeMax: Option[Int],
    Bohrkonfiguration: Option[String],
    Hartgold: Option[Double],
    ETestTestPunkte: Option[Int],
    ViaTyp: Option[String],
    BuriedVias: Option[Int],
    ZAchsenFraesen: Option[Int],
    Finish: Option[String],
    BreiteEinzel: Option[Double],
    RoHSsign: Option[String],
    LSTop: Option[Int],
    LSBot: Option[Int],
    LPTyp: Option[String],
    DateCode: Option[String],
    minTrack: Option[Double],
    SenkungenBot: Option[Int],
    ULsign: Option[String],
    ZAchsenFraesenBot: Option[Int],
    ULType: Option[String],
    Ritzen: Option[Int],
    minAbstaende: Option[Double],
    Kantenverzinnung: Option[Int],
    Material: Option[String],
    CarbonTop: Option[Int],
    CarbonBot: Option[Int],
    minRestring: Option[Double],
    Spulentechnik: Option[Int],
    ZAchsenFraesenTiefe: Option[String],
    Seriennummern: Option[String],
    Nutzenaufbau: Option[String],
    ViaTypFüllung: Option[String],
    IPCKlasse: Option[Int],
    Pressfit: Option[Int],
    Bemerkung: Option[String],
    DickeEnddicke: Option[String],
    AnzahlBohrDurchmesser: Option[Int],
    MaterialHalogenfrei: Option[Int],
    PeelingTop: Option[Int],
    PeelingBot: Option[Int],
    PeelingArt: Option[String],
    PadsTop: Option[Int],
    PadsBot: Option[Int],
    Etest: Option[Int],
    Fasung: Option[String],
    Komplexitaet: Option[String],
    Senkungen: Option[Int],
    LSdoppelt: Option[Int],
    Dicke: Option[Double],
    Name: Option[String],
    DKSchlitze: Option[Int],
    MaterialCTIWert: Option[String],
    Kupfer: Option[String],
    Nutzenkeinexout: Option[Int],
    BemerkungPruefbericht: Option[String],
    PDFarbe: Option[String],
    NutzenaufbauText: Option[String],
    LSFarbe: Option[String],
    FraesDurchmesser: Option[Int],
    Lagenaufbau: Option[String],
    minBohrung: Option[Double],
    LaengeEinzel: Option[Double]
) {}

object IbrApiPCB {

  private implicit def optToJs[X](v: Option[X])(implicit conv: X => JsValue): JsValue = v match {
    case Some(value) => value
    case None        => JsNull
  }
  private implicit def intToJs(v: Int): JsValue       = JsNumber(v)
  private implicit def doubleToJs(v: Double): JsValue = JsNumber(v)
  private implicit def stringToJs(v: String): JsValue = JsString(v)

  implicit val format: Format[IbrApiPCB] = {
    Format(
      Reads { v =>
        Try(
          IbrApiPCB(
            Laenge = (v \ "Laenge").as[Double],
            Breite = (v \ "Breite").as[Double],
            Lagen = (v \ "Lagen").as[Int],
            ULKanada = (v \ "ULKanada").asOpt[Int],
            Stiffener = (v \ "Stiffener").asOpt[String],
            Fraesen = (v \ "Fraesen").asOpt[Int],
            Nutzenzahl = (v \ "Nutzenzahl").asOpt[Int],
            GoldArea = (v \ "GoldArea").asOpt[Double],
            AnzahlBohrungen = (v \ "AnzahlBohrungen").asOpt[Int],
            PDTop = (v \ "PDTop").asOpt[Int],
            PDBot = (v \ "PDBot").asOpt[Int],
            BlindVias = (v \ "BlindVias").asOpt[Int],
            Nutzentyp = (v \ "Nutzentyp").asOpt[Int],
            SprungRitzen = (v \ "SprungRitzen").asOpt[Int],
            DickeEnddickeMax = (v \ "DickeEnddickeMax").asOpt[Int],
            Bohrkonfiguration = (v \ "Bohrkonfiguration").asOpt[String],
            Hartgold = (v \ "Hartgold").asOpt[Double],
            ETestTestPunkte = (v \ "ETestTestPunkte").asOpt[Int],
            ViaTyp = (v \ "ViaTyp").asOpt[String],
            BuriedVias = (v \ "BuriedVias").asOpt[Int],
            ZAchsenFraesen = (v \ "ZAchsenFraesen").asOpt[Int],
            Finish = (v \ "Finish").asOpt[String],
            BreiteEinzel = (v \ "BreiteEinzel").asOpt[Double],
            RoHSsign = (v \ "RoHSsign").asOpt[String],
            LSTop = (v \ "LSTop").asOpt[Int],
            LSBot = (v \ "LSBot").asOpt[Int],
            LPTyp = (v \ "LPTyp").asOpt[String],
            DateCode = (v \ "DateCode").asOpt[String],
            minTrack = (v \ "minTrack").asOpt[Double],
            SenkungenBot = (v \ "SenkungenBot").asOpt[Int],
            ULsign = (v \ "ULsign").asOpt[String],
            ZAchsenFraesenBot = (v \ "ZAchsenFraesenBot").asOpt[Int],
            ULType = (v \ "ULType").asOpt[String],
            Ritzen = (v \ "Ritzen").asOpt[Int],
            minAbstaende = (v \ "minAbstaende").asOpt[Double],
            Kantenverzinnung = (v \ "Kantenverzinnung").asOpt[Int],
            Material = (v \ "Material").asOpt[String],
            CarbonTop = (v \ "CarbonTop").asOpt[Int],
            CarbonBot = (v \ "CarbonBot").asOpt[Int],
            minRestring = (v \ "minRestring").asOpt[Double],
            Spulentechnik = (v \ "Spulentechnik").asOpt[Int],
            ZAchsenFraesenTiefe = (v \ "ZAchsenFraesenTiefe").asOpt[String],
            Seriennummern = (v \ "Seriennummern").asOpt[String],
            Nutzenaufbau = (v \ "Nutzenaufbau").asOpt[String],
            ViaTypFüllung = (v \ "ViaTypFüllung").asOpt[String],
            IPCKlasse = (v \ "IPCKlasse").asOpt[Int],
            Pressfit = (v \ "Pressfit").asOpt[Int],
            Bemerkung = (v \ "Bemerkung").asOpt[String],
            DickeEnddicke = (v \ "DickeEnddicke").asOpt[String],
            AnzahlBohrDurchmesser = (v \ "AnzahlBohrDurchmesser").asOpt[Int],
            MaterialHalogenfrei = (v \ "MaterialHalogenfrei").asOpt[Int],
            PeelingTop = (v \ "PeelingTop").asOpt[Int],
            PeelingBot = (v \ "PeelingBot").asOpt[Int],
            PeelingArt = (v \ "PeelingArt").asOpt[String],
            PadsTop = (v \ "PadsTop").asOpt[Int],
            PadsBot = (v \ "PadsBot").asOpt[Int],
            Etest = (v \ "Etest").asOpt[Int],
            Fasung = (v \ "Fasung").asOpt[String],
            Komplexitaet = (v \ "Komplexitaet").asOpt[String],
            Senkungen = (v \ "Senkungen").asOpt[Int],
            LSdoppelt = (v \ "LSdoppelt").asOpt[Int],
            Dicke = (v \ "Dicke").asOpt[Double],
            Name = (v \ "Name").asOpt[String],
            DKSchlitze = (v \ "DKSchlitze").asOpt[Int],
            MaterialCTIWert = (v \ "MaterialCTIWert").asOpt[String],
            Kupfer = (v \ "Kupfer").asOpt[String],
            Nutzenkeinexout = (v \ "Nutzenkeinexout").asOpt[Int],
            BemerkungPruefbericht = (v \ "BemerkungPruefbericht").asOpt[String],
            PDFarbe = (v \ "PDFarbe").asOpt[String],
            NutzenaufbauText = (v \ "NutzenaufbauText").asOpt[String],
            LSFarbe = (v \ "LSFarbe").asOpt[String],
            FraesDurchmesser = (v \ "FraesDurchmesser").asOpt[Int],
            Lagenaufbau = (v \ "Lagenaufbau").asOpt[String],
            minBohrung = (v \ "minBohrung").asOpt[Double],
            LaengeEinzel = (v \ "").asOpt[Double]
          )
        ) match {
          case Success(v) => JsSuccess(v)
          case Failure(v) => JsError() // TODO
        }

      },
      Writes { v =>
        val attributes: Seq[(String, JsValue)] = Seq(
          "ULKanada"              -> v.ULKanada,
          "Stiffener"             -> v.Stiffener,
          "Fraesen"               -> v.Fraesen,
          "Nutzenzahl"            -> v.Nutzenzahl,
          "GoldArea"              -> v.GoldArea,
          "AnzahlBohrungen"       -> v.AnzahlBohrungen,
          "PDTop"                 -> v.PDTop,
          "PDBot"                 -> v.PDBot,
          "BlindVias"             -> v.BlindVias,
          "Nutzentyp"             -> v.Nutzentyp,
          "SprungRitzen"          -> v.SprungRitzen,
          "DickeEnddickeMax"      -> v.DickeEnddickeMax,
          "Bohrkonfiguration"     -> v.Bohrkonfiguration,
          "Hartgold"              -> v.Hartgold,
          "ETestTestPunkte"       -> v.ETestTestPunkte,
          "ViaTyp"                -> v.ViaTyp,
          "BuriedVias"            -> v.BuriedVias,
          "ZAchsenFraesen"        -> v.ZAchsenFraesen,
          "Finish"                -> v.Finish,
          "BreiteEinzel"          -> v.BreiteEinzel,
          "RoHSsign"              -> v.RoHSsign,
          "Lagen"                 -> v.Lagen,
          "LSTop"                 -> v.LSTop,
          "LSBot"                 -> v.LSBot,
          "LPTyp"                 -> v.LPTyp,
          "DateCode"              -> v.DateCode,
          "minTrack"              -> v.minTrack,
          "SenkungenBot"          -> v.SenkungenBot,
          "ULsign"                -> v.ULsign,
          "ZAchsenFraesenBot"     -> v.ZAchsenFraesenBot,
          "ULType"                -> v.ULType,
          "Ritzen"                -> v.Ritzen,
          "minAbstaende"          -> v.minAbstaende,
          "Kantenverzinnung"      -> v.Kantenverzinnung,
          "Material"              -> v.Material,
          "CarbonTop"             -> v.CarbonTop,
          "CarbonBot"             -> v.CarbonBot,
          "minRestring"           -> v.minRestring,
          "Spulentechnik"         -> v.Spulentechnik,
          "ZAchsenFraesenTiefe"   -> v.ZAchsenFraesenTiefe,
          "Seriennummern"         -> v.Seriennummern,
          "Nutzenaufbau"          -> v.Nutzenaufbau,
          "ViaTypFüllung"         -> v.ViaTypFüllung,
          "IPCKlasse"             -> v.IPCKlasse,
          "Pressfit"              -> v.Pressfit,
          "Bemerkung"             -> v.Bemerkung,
          "DickeEnddicke"         -> v.DickeEnddicke,
          "AnzahlBohrDurchmesser" -> v.AnzahlBohrDurchmesser,
          "MaterialHalogenfrei"   -> v.MaterialHalogenfrei,
          "PeelingTop"            -> v.PeelingTop,
          "PeelingBot"            -> v.PeelingBot,
          "PeelingArt"            -> v.PeelingArt,
          "PadsTop"               -> v.PadsTop,
          "PadsBot"               -> v.PadsBot,
          "Etest"                 -> v.Etest,
          "Fasung"                -> v.Fasung,
          "Komplexitaet"          -> v.Komplexitaet,
          "Senkungen"             -> v.Senkungen,
          "LSdoppelt"             -> v.LSdoppelt,
          "Dicke"                 -> v.Dicke,
          "Name"                  -> v.Name,
          "DKSchlitze"            -> v.DKSchlitze,
          "MaterialCTIWert"       -> v.MaterialCTIWert,
          "Laenge"                -> v.Laenge,
          "Kupfer"                -> v.Kupfer,
          "Nutzenkeinexout"       -> v.Nutzenkeinexout,
          "BemerkungPruefbericht" -> v.BemerkungPruefbericht,
          "PDFarbe"               -> v.PDFarbe,
          "NutzenaufbauText"      -> v.NutzenaufbauText,
          "LSFarbe"               -> v.LSFarbe,
          "FraesDurchmesser"      -> v.FraesDurchmesser,
          "Breite"                -> v.Breite,
          "Lagenaufbau"           -> v.Lagenaufbau,
          "minBohrung"            -> v.minBohrung,
          "LaengeEinzel"          -> v.LaengeEinzel
        )
        JsObject(
          attributes.filter(!_._2.isInstanceOf[JsNull.type])
        )
      }
    )

  }

}
