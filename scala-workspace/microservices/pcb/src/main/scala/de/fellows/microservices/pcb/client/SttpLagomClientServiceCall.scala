package de.fellows.microservices.pcb.client

import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.Descriptor.Call
import com.lightbend.lagom.scaladsl.api.deser.MessageSerializer
import com.lightbend.lagom.scaladsl.api.security.ServicePrincipal
import com.lightbend.lagom.scaladsl.api.transport.{<PERSON>er<PERSON><PERSON><PERSON>, MessageProtocol, Method, RequestHeader, ResponseHeader}
import com.lightbend.lagom.scaladsl.api.{transport, Descriptor, ServiceCall}
import de.fellows.microservices.pcb.PcbServer.AsyncBackend
import play.api.Logging
import sttp.client3.{asByteArray, basicRequest, ResponseAs}
import sttp.model.{Head<PERSON>, Uri}

import java.net.URI
import scala.concurrent.{ExecutionContext, Future}

private class SttpLagomClientServiceCall[Request, ResponseMessage, ServiceCallResponse](
    requestHeaderHandler: RequestHeader => RequestHeader,
    responseHandler: (ResponseHeader, ResponseMessage) => ServiceCallResponse,
    serviceName: String,
    descriptor: Descriptor,
    call: Call[Request, ResponseMessage],
    path: String,
    queryParams: Map[String, Seq[String]],
    backend: AsyncBackend
)(implicit ec: ExecutionContext)
    extends ServiceCall[Request, ServiceCallResponse] with Logging {

  private val queryParamString =
    queryParams.filter(_._2.nonEmpty) match {
      case empty if empty.isEmpty => ""
      case _                      => "?" + queryParams.map(x => s"${x._1}=${x._2.head}").mkString("&")
    }

  private val url = s"http://$serviceName:9000$path$queryParamString"
  private val uri = Uri.unsafeParse(url)

  private def callRequestSerializer[RequestR, W](call: Call[RequestR, _]): MessageSerializer[RequestR, W] =
    call.requestSerializer.asInstanceOf[MessageSerializer[RequestR, W]]

  private def callResponseSerializer[Response, W](call: Call[_, Response]): MessageSerializer[Response, W] =
    call.responseSerializer.asInstanceOf[MessageSerializer[Response, W]]

  override def handleRequestHeader(
      handler: RequestHeader => RequestHeader
  ): ServiceCall[Request, ServiceCallResponse] =
    new SttpLagomClientServiceCall(
      requestHeaderHandler.andThen(handler),
      responseHandler,
      serviceName,
      descriptor,
      call,
      path,
      queryParams,
      backend
    )

  override def handleResponseHeader[T](handler: (ResponseHeader, ServiceCallResponse) => T): ServiceCall[Request, T] =
    new SttpLagomClientServiceCall(
      requestHeaderHandler,
      (header: ResponseHeader, message: ResponseMessage) => handler.apply(header, responseHandler(header, message)),
      serviceName,
      descriptor,
      call,
      path,
      queryParams,
      backend
    )

  override def invoke(request: Request): Future[ServiceCallResponse] = {
    def headerFilter: HeaderFilter = descriptor.headerFilter

    val method = call.callId match {
      case id: Descriptor.RestCallId =>
        id.method

      case _ =>
        if (call.requestSerializer.isUsed) {
          Method.POST
        } else {
          Method.GET
        }
    }

    var builder = basicRequest
      .method(sttp.model.Method(method.name), uri)

    val requestSerializer  = callRequestSerializer(call).asInstanceOf[MessageSerializer[Request, ByteString]]
    val responseSerializer = callResponseSerializer(call).asInstanceOf[MessageSerializer[ResponseMessage, ByteString]]
    val negotiatedRequestSerializer = requestSerializer.serializerForRequest
    val negotiatedResponseSerializer =
      responseSerializer.deserializer(negotiatedRequestSerializer.protocol)

    negotiatedRequestSerializer.protocol.contentType.foreach(t => builder = builder.contentType(t))
    val charset = negotiatedRequestSerializer.protocol.charset
    charset.foreach(t => builder = builder.header(Header.acceptCharset(t)))

    val headers =
      requestHeaderHandler(
        headerFilter.transformClientRequest(
          transport.RequestHeader(
            method,
            URI.create(url),
            negotiatedRequestSerializer.protocol,
            responseSerializer.acceptResponseProtocols,
            Some(ServicePrincipal.forServiceNamed(serviceName)),
            Seq()
          )
        )
      ).headers.toMap

    val mapResponse: ResponseAs[Either[String, ResponseMessage], Any] =
      asByteArray.map {
        case Left(error) => Left(error)
        case Right(body) =>
          Right(negotiatedResponseSerializer.deserialize(ByteString(body)))
      }

    (requestSerializer.isStreamed, responseSerializer.isStreamed) match {
      case (false, false) =>
        val requestBody = negotiatedRequestSerializer.serialize(request)
          .decodeString(charset.getOrElse("utf-8"))

        builder
          .body(requestBody)
          .headers(headers)
          .response(mapResponse)
          .send(backend)
          .map { response =>
            response.body match {
              case Left(value) => throw new RuntimeException(s"failed call $method $url: ${value}")
              case Right(value) =>
                val responseHeaders = ResponseHeader(
                  response.code.code,
                  MessageProtocol.fromContentTypeHeader(response.contentType),
                  response.headers.map(h => h.name -> h.value)
                )
                responseHandler(responseHeaders, value)
            }
          }

      case _ => throw new RuntimeException("only strict calls are supported for now")
    }
  }

}
