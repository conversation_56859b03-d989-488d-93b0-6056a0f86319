package de.fellows.microservices.pcb.model.pcb.props

/** The amount of plated holes (vias)
  */
object BlindViaCount {
  val name: String  = "blindViaCount"
  val label: String = "pcb.mechanical.blindViaCount"

  def empty: BlindViaCount                             = BlindViaCount(Option.empty[Int])
  def apply(value: Int): BlindViaCount                 = BlindViaCount(Some(value))
}

final case class BlindViaCount(override val value: Option[Int]) extends IntPCBProperty {
  val fieldName: String = BlindViaCount.name
  val label: String     = BlindViaCount.label
  val unit: String      = "holes"
}
