package de.fellows.microservices.pcb.model.wurth

import de.fellows.microservices.pcb.model.pcb.props
import de.fellows.microservices.pcb.model.pcb.props.{Outer105mcr, Outer18mcr, Outer35mcr, Outer70mcr}

/** Name of param in Wurth API: outward_overlay
  */
sealed trait WurthOuterCopperThickness {
  val value: Int
  val initialValue: props.OuterCopperThickness

  override def equals(obj: Any): Boolean = obj match {
    case that: WurthOuterCopperThickness => that.value == value
    case _                               => false
  }
}
private case class OuterThicknessMcr18(override val initialValue: props.OuterCopperThickness =
  props.OuterCopperThickness(18))
    extends WurthOuterCopperThickness {
  override val value: Int = 4
}
private case class OuterThicknessMcr35(override val initialValue: props.OuterCopperThickness =
  props.OuterCopperThickness(35))
    extends WurthOuterCopperThickness {
  override val value: Int = 1
}
private case class OuterThicknessMcr70(override val initialValue: props.OuterCopperThickness =
  props.OuterCopperThickness(70))
    extends WurthOuterCopperThickness {
  override val value: Int = 2
}
private case class OuterThicknessMcr105(override val initialValue: props.OuterCopperThickness =
  props.OuterCopperThickness(105))
    extends WurthOuterCopperThickness {
  override val value: Int = 3
}

private object WurthOuterCopperThickness {

  def converter(value: props.OuterCopperThickness): Option[WurthOuterCopperThickness] =
    value match {
      case Outer18mcr  => Some(OuterThicknessMcr18(value))
      case Outer35mcr  => Some(OuterThicknessMcr35(value))
      case Outer70mcr  => Some(OuterThicknessMcr70(value))
      case Outer105mcr => Some(OuterThicknessMcr105(value))
      case _           => None
    }

  def fromWurth(value: Int): WurthOuterCopperThickness = value match {
    case 4 => OuterThicknessMcr18()
    case 1 => OuterThicknessMcr35()
    case 2 => OuterThicknessMcr70()
    case 3 => OuterThicknessMcr105()
    case _ => throw new IllegalArgumentException(s"Unknown value for outer copper thickness: $value")
  }
}
