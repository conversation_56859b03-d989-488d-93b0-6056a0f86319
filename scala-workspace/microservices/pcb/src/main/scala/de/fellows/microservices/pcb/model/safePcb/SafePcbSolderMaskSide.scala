package de.fellows.microservices.pcb.model.safePcb

import de.fellows.ems.pcb.api.specification.Side
import de.fellows.ems.pcb.api.specification.Side.{Bottom, Top}
import de.fellows.microservices.pcb.model.pcb.props.SoldermaskSide

trait SafePcbSolderMaskSide {
  val value: String

}
private case object SafePcbSolderMaskSidesTopBottom extends SafePcbSolderMaskSide {
  override val value: String = "TOP.BOT"
}
private case object SafePcbSolderMaskSidesTop extends SafePcbSolderMaskSide {
  override val value: String = "TOP"
}
private case object SafePcbSolderMaskSidesBottom extends SafePcbSolderMaskSide {
  override val value: String = "BOT"
}
private case object SafePcbSolderMaskSidesNone extends SafePcbSolderMaskSide {
  override val value: String = "WITHOUT"
}

private object SafePcbSolderMaskSide {

  def apply(value: SoldermaskSide): SafePcbSolderMaskSide =
    value.value match {
      case Side.None => SafePcbSolderMaskSidesNone
      case Top       => SafePcbSolderMaskSidesTop
      case Bottom    => SafePcbSolderMaskSidesBottom
      case _         => SafePcbSolderMaskSidesTopBottom
    }
}
