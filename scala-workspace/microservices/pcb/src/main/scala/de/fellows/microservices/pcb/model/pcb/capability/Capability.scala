package de.fellows.microservices.pcb.model.pcb.capability

import com.osinka.i18n.Lang
import de.fellows.microservices.pcb.model.pcb.PropertyError
import de.fellows.microservices.pcb.model.pcb.props.{PCBOptionalProperty, PCBProperty, PCBRequiredProperty}
import zio.prelude.Validation

import scala.reflect.ClassTag

trait Capability[T <: PCBProperty] {
  val typeTag: ClassTag[T]
}

trait NonEmptyCapability[T <: PCBOptionalProperty[_], K <: NonEmptyCapability[T, K]] extends Capability[T] {
  val required: Boolean
  def validate(property: T)(implicit lang: Lang): Validation[PropertyError, T]
}

trait NonEmptyRequiredCapability[T <: PCBRequiredProperty[_], K <: NonEmptyRequiredCapability[T, K]]
    extends Capability[T] {
  def validate(property: T)(implicit lang: Lang): Validation[PropertyError, T]
}

object Capability {

  /** Merges two sets of capabilities using this logic:
    *
    * - if a capability exists only in one set, it is added to the result
    * - if a capability exists in both sets, the capability from the right set is added to the result
    */
  def merge(left: Seq[Capability[_]], right: Seq[Capability[_]]): Seq[Capability[_]] = {
    val tags = right.map(_.typeTag)
    left.filterNot(x => tags.contains(x.typeTag)) ++ right
  }
}
