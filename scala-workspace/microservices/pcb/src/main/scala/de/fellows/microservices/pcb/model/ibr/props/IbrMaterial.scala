package de.fellows.microservices.pcb.model.ibr.props

import de.fellows.ems.pcb.api.specification
import de.fellows.microservices.pcb.model.pcb.props
import de.fellows.microservices.pcb.model.pcb.props.TGValue
case class IbrMaterial(value: String) {}

object IbrMaterial {
//  FR4 TG135
//  FR4 TG150
//  FR4 TG170
//  FR4 TG180
//  FR4 TG210
//  FR4 TG260
//  Aluminium 1.0 W/mK
//  Aluminium 1.3 W/mK
//  Aluminium 2.0 W/mK
//  Aluminium 3.0 W/mK
//  Aluminium 5.0 W/mK
//  Aluminium 8.0 W/mK
//  Kupfer
//  CEM1
//  Keramik
//  Teflon
//  Polyimid
//  Rogers
//  Rogers RO4003C
//  Rogers RO4350B

  def converter(value: props.BaseMaterial, tg: TGValue): Option[IbrMaterial] =
    value match {
      case props.BaseMaterial(specification.BaseMaterial.FR4) =>
        tg match {
          case TGValue(Some(x)) if x == 135 => Some(IbrMaterial("FR4 TG135"))
          case TGValue(Some(x)) if x == 150 => Some(IbrMaterial("FR4 TG150"))
          case TGValue(Some(x)) if x == 170 => Some(IbrMaterial("FR4 TG170"))
          case TGValue(Some(x)) if x == 180 => Some(IbrMaterial("FR4 TG180"))
          case TGValue(Some(x)) if x == 210 => Some(IbrMaterial("FR4 TG210"))
          case TGValue(Some(x)) if x == 260 => Some(IbrMaterial("FR4 TG260"))
          case _                            => None
        }

      case props.BaseMaterial(specification.BaseMaterial.CEM1)      => Some(IbrMaterial("CEM1"))
      case props.BaseMaterial(specification.BaseMaterial.Polyimide) => Some(IbrMaterial("Polyimid"))

      case _ => None
    }

}
