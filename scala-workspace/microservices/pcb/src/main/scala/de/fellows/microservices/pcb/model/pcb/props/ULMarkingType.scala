package de.fellows.microservices.pcb.model.pcb.props

import de.fellows.ems.pcb.api.specification.ULMarkingTypeEnum
import de.fellows.ems.pcb.api.specification.ULMarkingTypeEnum.{CustomMarking, DefaultMarking, NoMarking}
import de.fellows.microservices.pcb.model.pcb.capability.SetCapability

object ULMarkingType {
  val name: String  = "ulMarkingType"
  val label: String = "pcb.layer.ulMarkingType"

  val noMarking: ULMarkingType      = ULMarkingType(NoMarking)
  val defaultMarking: ULMarkingType = ULMarkingType(DefaultMarking)
  val customMarking: ULMarkingType  = ULMarkingType(CustomMarking)

  def apply(value: Option[ULMarkingTypeEnum]): ULMarkingType = ULMarkingType(value.getOrElse(NoMarking))

  type ULMarkingTypeCapability = SetCapability[ULMarkingType, ULMarkingTypeEnum]
}

final case class ULMarkingType(override val value: ULMarkingTypeEnum)
    extends PCBRequiredProperty[ULMarkingTypeEnum] {
  val fieldName: String = ULMarkingType.name
  val label: String     = ULMarkingType.label
  val unit: String      = ""
}