include file("/etc/stackrate/environment.conf")

akka.http.server.parsing {
    max-header-value-length = 16k
}

akka.http {
   parsing {
     max-header-value-length = 16k
   }
 }

stackrate {
  pcb = "http://pcb:9000"
  pcb = ${?STACKRATE_URL}

  assembly = "http://assembly:9000"
  assembly = ${?STACKRATE_ASSEMBLY_URL}

  layerstack = "http://layerstack:9000"
  layerstack = ${?STACKRATE_LAYERSTACK_URL}

  renderer = "http://renderer:9000"
  renderer = ${?STACKRATE_RENDERER_URL}

  # used for testing purpose only to make <PERSON><PERSON> request without requesting real PCB's from StackRate
  use_pcb_stub = false
  use_pcb_stub = ${?USE_PCB_STUB}
  use_zip_stub = false
  use_zip_stub = ${?USE_ZIP_STUB}
  use_svg_stub = false
  use_svg_stub = ${?USE_SVG_STUB}
}

jwt {
  secret = "wow"

  token {
    auth.expirationInSeconds = 86400,
    file.expirationInSeconds = 86400,
    refresh.expirationInSeconds = 86400
  }
}

pcb {
  port = 8080
  port = ${?PORT}


  luminovo {
    host = "http://backend-core:5000"

    offer_path = "/api/offers/pcb"

    ignoreLeadTimes = false
    ignoreLeadTimes = ${?WURTHAPI_IGNORE_LEAD_TIMES}
  }

  routes {
    # If ask takes more time than this to complete the request is failed
    ask-timeout = 5s
  }

}

betaLayoutApi {
  host = "https://api.beta-layout.com"
  key = "ebf6e352a4b3d7c4ef1a5a26f1221213"
  key = ${?BETA_LAYOUT_API_KEY}
}

wurthapi {
  host = "https://www.wedirekt.com"
  host = ${?WURTHAPI_HOST}

  user = "<EMAIL>"
  password = "yeq3jpw3tcq-cmc*ADG"
}

ibrApi {
  host: "https://api.pcb-production.com:7433/api/luminovo"
  key: "49ce1604-e505-419a-b180-eb6c062e9a22"

  customer: 15657
  customer_key: "5a8089ae-9a1d-4d43-a0eb-99f6218951cf"
}

apct {
  host = "https://perceptiveresource.com/ErpApctApiSales/webservice.asmx"
}

gatemaApi {
    host = "https://extranet.gatema.cz"
    host = ${?GATEMA_API_HOST}

    username = "luminovo"
    username = ${?GATEMA_API_USERNAME}

    password = "42f048e796f50302b9d3583aa273e34734b19b05f9238a109387e742852dc80413b0ff6c205491bc8595c2bf1b82db8d98c625797655de2fc35cecd5bc1599d1a3eab1447479b6d9e29416782a7572eb05d1d8625782b1136dc50101812c48b44a284afcbe867a91b721208803377f03efd80333eb6cff35a27777ec99d9df9d"
    password = ${?GATEMA_API_PASSWORD}

    dbProfile = "Gatema PCB"
    dbProfile = ${?GATEMA_API_DB_PROFILE}

    serverUrl = "http://hegsrv.gatema.cz/HoldingNephrite"
    serverUrl = ${?GATEMA_API_SERVER_URL}
}

albaApi {
  host = "http://***********:83/api"
}

multiCbApi {
  host = "https://portal.multi-circuit-boards.eu/"
  host = ${?MULTICB_API_HOST}
}
