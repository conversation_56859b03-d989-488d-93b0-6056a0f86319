<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Panel Test Configurator</title>
    <script src="https://code.jquery.com/jquery-3.6.1.min.js"
        integrity="sha256-o88AwQnZB+VDvE9tvIXrMQaPlFFSUTR+nldQm1LuPXQ=" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com?plugins=forms"></script>

    <script>
        !function () {
            var analytics = window.analytics = window.analytics || []; if (!analytics.initialize) if (analytics.invoked) window.console && console.error && console.error("Segment snippet included twice."); else {
                analytics.invoked = !0; analytics.methods = ["trackSubmit", "trackClick", "trackLink", "trackForm", "pageview", "identify", "reset", "group", "track", "ready", "alias", "debug", "page", "once", "off", "on", "addSourceMiddleware", "addIntegrationMiddleware", "setAnonymousId", "addDestinationMiddleware"]; analytics.factory = function (e) { return function () { var t = Array.prototype.slice.call(arguments); t.unshift(e); analytics.push(t); return analytics } }; for (var e = 0; e < analytics.methods.length; e++) { var key = analytics.methods[e]; analytics[key] = analytics.factory(key) } analytics.load = function (key, e) { var t = document.createElement("script"); t.type = "text/javascript"; t.async = !0; t.src = "https://cdn.segment.com/analytics.js/v1/" + key + "/analytics.min.js"; var n = document.getElementsByTagName("script")[0]; n.parentNode.insertBefore(t, n); analytics._loadOptions = e }; analytics._writeKey = "jKnSdeyh8Vb53TAw8sdmcEgYfwgyhCKG";; analytics.SNIPPET_VERSION = "4.15.3";
                analytics.load("jKnSdeyh8Vb53TAw8sdmcEgYfwgyhCKG");
                analytics.page();
            }
        }();
    </script>

    <script>
        var validNavigation = false;

        function wireUpEvents() {
            let dont_confirm_leave = 1; //set dont_confirm_leave to 1 when you want the user to be able to leave without confirmation
            let leave_message = 'You sure you want to leave?'
            function goodbye(e) {

                if (!validNavigation) {
                    if (dont_confirm_leave !== 1) {
                        if (!e) e = window.event;
                        //e.cancelBubble is supported by IE - this will kill the bubbling process.
                        e.cancelBubble = true;
                        e.returnValue = leave_message;
                        //e.stopPropagation works in Firefox.
                        if (e.stopPropagation) {
                            e.stopPropagation();
                            e.preventDefault();
                        }

                        //return works for Chrome and Safari
                        return leave_message;
                    }

                    analytics.track('pcb_panel_distribute_page_exit');
                }
            }
            window.onbeforeunload = goodbye;

            // Attach the event keypress to exclude the F5 refresh
            $(document).bind('keypress', function (e) {
                if (e.keyCode == 116) {
                    validNavigation = true;
                }
            });

            // Attach the event click for all links in the page
            $("a").bind("click", function () {
                validNavigation = true;
            });

            // Attach the event submit for all forms in the page
            $("form").bind("submit", function () {
                validNavigation = true;
            });

            // Attach the event click for all inputs in the page
            $("input[type=submit]").bind("click", function () {
                validNavigation = true;
            });

        }

        // Wire up the events as soon as the DOM tree is ready
        $(document).ready(function () {
            wireUpEvents();
        });
    </script>


</head>

<body>
    <!-- Background color split screen for large screens -->
    <div class="fixed top-0 left-0 w-1/2 h-full bg-white" aria-hidden="true"></div>
    <div class="fixed top-0 right-0 w-1/2 h-full bg-gray-50" aria-hidden="true"></div>
    <div class="relative min-h-screen flex flex-col">

        <!-- 3 column wrapper -->
        <div class="flex-grow w-full max-w-7xl mx-auto xl:px-8 lg:flex">
            <!-- Left sidebar & main wrapper -->
            <div class="flex-1 min-w-0 bg-white xl:flex">
                <div
                    class="border-b border-gray-200 xl:border-b-0 xl:flex-shrink-0 xl:w-64 xl:border-r xl:border-gray-200 bg-white">
                    <div class="h-full pl-4 pr-6 py-6 sm:pl-6 lg:pl-8 xl:pl-0">
                        <!-- Start left column area -->
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700">Number of PCBs</label>
                            <div class="mt-1">
                                <input type="number" name="amount" id="amount"
                                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                    value="100">
                            </div>
                        </div>
                        <div class="mb-2 mt-4 text-xl font-bold">PCB</div>
                        <div class="flex space-x-4">
                            <div>
                                <label for="pcbWidth" class="block text-sm font-medium text-gray-700">width, mm</label>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="pcbWidth" id="pcbWidth"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="10.05">
                                </div>
                            </div>
                            <div>
                                <label for="pcbHeight" class="block text-sm font-medium text-gray-700">height,
                                    mm</label>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="pcbHeight" id="pcbHeight"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="5.05">
                                </div>
                            </div>
                        </div>
                        <div class="mb-2 mt-4 text-xl font-bold">Panel</div>
                        <div>
                            <label for="maxPcb" class="block text-sm font-medium text-gray-700">
                                Max PCB / panel <span class="text-gray-500 block font-normal text-xs">(set to -1 for
                                    unlimited)</span>
                            </label>
                            <div class="mt-1">
                                <input type="number" name="maxPcb" id="maxPcb"
                                    class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                    value="-1">
                            </div>
                        </div>
                        <div class="mb-2 mt-4">Gap (x, y), mm</div>
                        <div class="flex space-x-4">
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="gapX" id="gapX"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="2.00">
                                </div>
                            </div>
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="gapY" id="gapY"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="2.00">
                                </div>
                            </div>
                        </div>
                        <div class="mb-2 mt-4">Padding (x, y), mm</div>
                        <div class="flex space-x-4">
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="paddingX" id="paddingX"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="2.00">
                                </div>
                            </div>
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="paddingY" id="paddingY"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="2.00">
                                </div>
                            </div>
                        </div>
                        <div class="mb-2 mt-4">Minimum (width x height), mm</div>
                        <div class="flex space-x-4">
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="minX" id="minX"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="20.00">
                                </div>
                            </div>
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="minY" id="minY"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="20.00">
                                </div>
                            </div>
                        </div>
                        <div class="mb-2 mt-4">Maximum (width x height), mm</div>
                        <div class="flex space-x-4">
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="maxX" id="maxX"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="200.00">
                                </div>
                            </div>
                            <div>
                                <div class="mt-1">
                                    <input type="number" step="0.01" name="maxY" id="maxY"
                                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                        value="100.00">
                                </div>
                            </div>
                        </div>
                        <button type="button" onclick="onCalculateClick()"
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white mt-5
                            bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Calculate
                        </button>
                        <!-- End left column area -->
                    </div>
                </div>

                <div class="bg-white lg:min-w-0 lg:flex-1">
                    <div class="h-full py-6 px-4 sm:px-6 lg:px-8">
                        <!-- Start main area-->
                        <div class="relative h-full" style="min-height: 36rem">
                            <div class="absolute inset-0 border-2 border-gray-200 border-dashed rounded-lg">
                                <img src="simplePanel.svg" id="distribution" height="500px" width="100%" />
                            </div>
                        </div>
                        <!-- End main area -->
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 pr-4 sm:pr-6 lg:pr-8 lg:flex-shrink-0 lg:border-l lg:border-gray-200 xl:pr-0">
                <div class="h-full pl-6 py-6 lg:w-80">
                    <div class="border-2 border-gray-200 border-dashed rounded-lg p-4">
                        <div class="text-red-500 font-bold my-2" id="error">No distribution found</div>
                        <div><span class="font-bold text-sm">Total waste:</span> <span
                                id="total-waste"></span>mm<sup>2</sup></div>
                        <div><span class="font-bold text-sm">Panel size:</span> <span id="panel-w"></span>mm x <span
                                id="panel-h"></span>mm</div>

                        <div class="my-2 font-bold">Full panels</div>
                        <div><span class="font-bold text-sm"># of PCBs:</span> <span id="full-panel-pcbs">0</span></div>
                        <div><span class="font-bold text-sm"># of panels:</span> <span id="full-panel-number">0</span>
                        </div>
                        <div><span class="font-bold text-sm">Waste:</span> <span
                                id="full-panel-waste"></span>mm<sup>2</sup></div>

                        <div class="my-2 font-bold">Left-over panel</div>
                        <div><span class="font-bold text-sm"># of PCBs:</span> <span id="left-over-pcbs">0</span></div>
                        <div><span class="font-bold text-sm">waste:</span> <span
                                id="left-over-waste">0</span>mm<sup>2</sup></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        const PCB_PANEL_DISTRIBUTE_EVENT = 'pcb_panel_distribute_calculate';

        function getPayload() {
            return {
                "amount": parseInt(document.getElementById('amount').value),
                "pcb_w": parseFloat(document.getElementById('pcbWidth').value),
                "pcb_h": parseFloat(document.getElementById('pcbHeight').value),
                "min_panel_w": parseFloat(document.getElementById('minY').value),
                "min_panel_h": parseFloat(document.getElementById('minX').value),
                "max_panel_w": parseFloat(document.getElementById('maxX').value),
                "max_panel_h": parseFloat(document.getElementById('maxY').value),
                "max_pcb_per_panel": parseInt(document.getElementById('maxPcb').value) === -1 ? null : parseInt(document.getElementById('maxPcb').value),
                "padding_x": parseFloat(document.getElementById('paddingX').value),
                "padding_y": parseFloat(document.getElementById('paddingY').value),
                "gap_x": parseFloat(document.getElementById('gapX').value),
                "gap_y": parseFloat(document.getElementById('gapY').value),
            };
        }
        function sendRequest(payload) {
            fetch('/api/panel-test/distribute', {
                method: 'POST',
                body: JSON.stringify(payload),
                headers: {
                    'Content-Type': 'application/json'
                }
            }).then(response => response.json())
                .then(data => {
                    const distribution = document.getElementById('distribution');
                    if (distribution != null) {
                        document.getElementById('distribution').setAttribute('src', `data:image/svg+xml;base64,${data.svg}`);
                        document.getElementById('total-waste').innerText = data.total_waste;
                        document.getElementById('full-panel-number').innerText = data.full_panel_amount;
                        document.getElementById('full-panel-waste').innerText = data.full_panel_waste;
                        document.getElementById('full-panel-pcbs').innerText = data.full_panel_pcbs;
                        document.getElementById('left-over-waste').innerText = data.left_over_panel_waste;
                        document.getElementById('left-over-pcbs').innerText = data.left_over_panel_pcbs;
                        document.getElementById('panel-w').innerText = data.panel_width;
                        document.getElementById('panel-h').innerText = data.panel_height;
                        document.getElementById('error').innerText = '';
                    } else {
                        document.getElementById('error').innerText = 'No distribution found';
                    }
                    console.log(data);
                });
        }
        sendRequest(getPayload());

        function onCalculateClick() {
            const payload = getPayload();

            analytics.track(PCB_PANEL_DISTRIBUTE_EVENT, payload);

            sendRequest(payload);
        }
    </script>

</body>

</html>