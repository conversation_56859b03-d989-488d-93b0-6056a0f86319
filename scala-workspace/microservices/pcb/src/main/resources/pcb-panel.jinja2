{% macro unit(s) %}
    <span class="badge badge-light badge-unit">{{ s }}</span>
{% endmacro %}

{% macro value(s) %}
    <span class="col_text">{% if s == null %} {{ '--' }} {% else %} {{ s }} {% endif %}</span>
{% endmacro %}

{% macro roundValue(s) %}
    <span class="col_text">{% if s == null %} {{ '--' }} {% else %} {{ s | round(2) }} {% endif %}</span>
{% endmacro %}


{% macro pageheader(s) %}
    <div class="row" style="margin-bottom:-30px">
        <div class="col-xs-title">
            <p class="h1">{{ s }}</p>
        </div>
        <div class="col-xs-logo col_right">
            <img class="logo" src="{{ logo_png }}">
        </div>
    </div>
{% endmacro %}


{% macro pagefooter() %}
    <div class="footer_div row">
        <div class="bottom_text neutral-6">
            <p class="h4">This PCB Specification document was generated by Luminovo, the next generation RFQ software
                for
                the PCB and PCBA industry. For more information about our company and our products please checkout our
                website <a href="https://luminovo.com/">https://luminovo.com/</a>
            </p>
        </div>
    </div>
{% endmacro %}

{% macro sides(s) %}
    {% if(s == 'Both') %}
        Both sides
    {% elif(s == 'Top') %}
        Top side
    {% elif(s == 'Bottom') %}
        Bottom side
    {% elif(s == 'None') %}
        None
    {% endif %}
{% endmacro %}

{% macro sidesplus(s) %}
    {% if(s == 'Both') %}
        Both sides
    {% elif(s == 'Top') %}
        Top side
    {% elif(s == 'Bottom') %}
        Bottom side
    {% elif(s == 'None') %}
        <span class="badge badge-secondary">none</span>
    {% endif %}
{% endmacro %}

{% macro color(s) %}
    {% if(s == 'white') %}
        White
    {% elif(s == 'black') %}
        Black
    {% elif(s == 'green') %}
        Green
    {% elif(s == 'red') %}
        Red
    {% elif(s == 'blue') %}
        Blue
    {% elif(s == 'purple') %}
        Purple
    {% else %}
        {{ s }}
    {% endif %}
{% endmacro %}

{% macro rohs(s) %}
    {% if(s == 'true') %}
        <span class="badge badge-success">yes</span>
    {% elif(s == 'false') %}
        <span class="badge badge-secondary">no</span>
    {% endif %}
{% endmacro %}

{% macro etest(s) %}
    {% if(s == 'true') %}
        <span class="badge badge-success">yes</span>
    {% elif(s == 'false') %}
        <span class="badge badge-secondary">no</span>
    {% endif %}
{% endmacro %}

{% macro nohandle(s) %}
    {% if(s == 'none') %}
        <span class="badge badge-danger">no</span>
    {% else %}
        {{ s }}
    {% endif %}
{% endmacro %}

{% macro markingselection(s) %}
    {% if(s == 'top-soldermask') %}
        Top soldermask
    {% elif(s == 'bottom-soldermask') %}
        Bottom soldermask
    {% elif(s == 'top-silkscreen') %}
        Top silkscreen
    {% elif(s == 'bottom-silkscreen') %}
        Bottom silkscreen
    {% elif(s == 'none') %}
        <span class="badge badge-secondary">none</span>
    {% endif %}
{% endmacro %}


{% macro truefalse(s) %}
    {% if(s == 'true') %}
        <span class="badge badge-success">yes</span>
    {% elif(s == 'false') %}
        <span class="badge badge-danger">no</span>
    {% endif %}
{% endmacro %}


{% macro finish(s) %}
    {% if(s == 'hal') %}
        HAL
    {% elif(s == 'enig') %}
        ENIG
    {% elif(s == 'silver') %}
        Silver
    {% endif %}
{% endmacro %}


{% macro booleanString(s) %}
    {% if(s) %}
        Ja
    {% elif(s == 'enig') %}
        Nein
    {% endif %}
{% endmacro %}

<!--
    Date        2.05.2022
    Version     2
-->
<html>
<head>
    <meta charset="UTF-8"/>
    <link rel="stylesheet" href="bootstrap.min.css"/>

    <style>
        @font-face {
            font-display: swap;
            font-family: 'Noto Sans';
            font-style: normal;
            font-weight: 400;
            src: url({{ noto_sans_ttf }}) format('truetype');
        }

        @font-face {
            font-display: swap;
            font-family: 'Poppins';
            font-style: normal;
            font-weight: 400;
            src: url({{ poppins_regular_ttf }}) format('truetype');
        }

        @font-face {
            font-display: swap;
            font-family: 'Poppins';
            font-style: normal;
            font-weight: 600;
            src: url({{ poppins_bold_ttf }}) format('truetype');
        }

        @page {
            size: A4;
            margin: 20px 40px 20px 40px;
        }

        body {
            padding: 0;
            margin: 0;
        }

        .font-noto {
            font-family: Noto Sans;
            font-style: normal;
            font-weight: 300;
        }

        .font-poppins {
            font-family: Poppins;
            font-style: normal;
            font-weight: 300;
        }

        .h1 {
            font-size: 28px;
            line-height: 40px;
        }

        .h2 {
            font-family: "Noto Sans", serif;
            font-size: 20px;
            line-height: 24px;
            margin-top: 24px;
        }

        .h3 {
            font-size: 11px;
            line-height: 15px;
        }

        .h4 {
            font-size: 9px;
            line-height: 13px;
        }

        .h1, .h3, .h4 {
            font-family: "Noto Sans", serif;
            margin-top: 0;
        }

        .print-format {
            background-color: white;
            /* height = width * 1,41451613 */
            height: 1070px;
            width: 690px;
            padding-top: 0;
            /*padding-top: 30px;*/
            /*was 0in */
            margin: 0;
            border: 0 solid red;
            position: relative;
        }

        .badge-warning {
            background-color: #FFF3DB;
            /* Yellow 2*/
            color: #BB8211;
            /* Yellow 8*/
        }

        .badge-success {
            background-color: #E2F9F1;;
            /* Green 2*/
            color: #3B9B7B;
            /* Green 7*/
        }

        .badge-info {
            background-color: #E5EAFF;
            /* Primary 2*/
            color: #4751D1;
            /* Primary 7*/
        }

        .badge {
            padding-left: 5px;
            padding-right: 5px;
            border-radius: 5px;
            border: 1px solid #E5E5E5;
            width: 40px;
            display: inline-block;
            text-align: center;
            line-height: 13px;
        }

        .badge-light {
            background-color: #F5F5F5;
            color: #999999;
        }

        .badge-secondary {
            background-color: #EAEBF1;
            color: #676D7E;
        }

        .badge-danger {
            background-color: #FDE7E8;
            color: #D1474E;
        }

        .badge-unit {
        }

        .test {
            border: 0 dotted red;
        }

        .col-xs-1,
        .col-xs-2,
        .col-xs-3,
        .col-xs-4,
        .col-xs-5,
        .col-xs-6,
        .col-xs-7,
        .col-xs-8,
        .col-xs-9,
        .col-xs-10,
        .col-xs-11,
        .col-xs-12 {
            float: left;
        }

        .col-xs-6 {
            width: 47%;
        }

        .col-xs-12 {
            width: 100%;
        }

        .col-xs-title {
            width: 70%;
            float: left;
            padding: 0;
            margin: 0;
        }

        .col-xs-logo {
            width: 28%;
            float: right;
            padding: 0;
        }

        .row {
            margin-left: 0;
            margin-top: 20px;
            padding: 0;
        }

        .row_fixed_image {
            height: 370px;
            padding: 0;
            margin: 0;
        }

        .col_right {
            float: right;
        }

        /* marginleft was -20px*/
        .row:before,
        .row:after {
            display: table;
            content: "";
            line-height: 0;
        }

        .row:after {
            clear: both;
        }

        .table-border td {
            border-right: 0;
            margin-right: 0;
            border-bottom: 1px solid lightgrey;
            border-collapse: collapse;
            padding-top: 2px;
            padding-bottom: 2px;
        }

        table {
            width: 100%;
        }

        .table-borderless {
            border: 0;
            padding-right: 0;
        }


        .table-borderless th {
            padding: 0.25rem;
            padding-left: 0;
        }

        .table-borderless td {
            text-align: right;
            padding: 0 4px 0 0;
        }

        .table-basic {
            border-spacing: 0px;
            text-align: left;
            background-color: #FFFFFF;
            padding: 0px;
        }

        .table-basic thead th {
            border-bottom-width: 1px;

        }


        .table-basic tbody th,
        .table-basic thead th {
            font-weight: 600;
        }

        .logo {
            margin-left: auto;
            margin-right: 0;
            margin-top: 0px;
            display: block;
            max-width: 200px;
            max-height: 60px;
        }

        .img_side_preview {
            max-width: 400px;
            max-height: 400px;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto auto;
        }

        .img_side_preview_new {
            height: 383px;
            width: 383px;
            padding: 20px;
            background-color: #F5F5F5;
            border: 2px solid #6C6C6C;
        }

        .image_div {
            height: 270px;
            width: 270px;
            background-color: #F5F5F5;
            border: 2px solid #6C6C6C;
            background-size: contain;
            background-position: center center;
            background-repeat: no-repeat;
            padding: 20px;
        }

        .panel_borderless_image {
            height: 270px;
            width: 270px;
            background-color: #F5F5F5;
            background-size: contain;
            background-position: center center;
            background-repeat: no-repeat;
            padding: 20px;
        }

        .img_container {
            width: 100%;
            height: 100%;
            display: block;
            position: relative;

        }

        .outer_img_container {
            width: 423px;
            height: 423px;
            background-color: #F5F5F5;
            border: 2px solid #6C6C6C;
        }


        .outer_img_container_layerstack {
            width: 423px;
            height: 450px;
            background-color: #F5F5F5;
            border: 2px solid #6C6C6C;
        }


        .additional_notes {
            height: 350px;
            width: 100%;
            border: 1px solid #cccccc;
            padding: 0px;
        }

        .neutral-1 {
            color: #F7F8FA;
        }

        .neutral-6 {
            color: #858CA0;
        }

        .neutral-9 {
            color: #2A3147;
        }

        .bottom_text {
            text-align: center;
            padding-left: 100px;
            padding-right: 100px;
        }

        .link_specification {
            color: blue;
            text-decoration: underline;
        }

        .footer_div {
            position: absolute;
            bottom: 0;
        }

        .bottom_text {
            margin-top: 0;
        }

        .col_text {
            display: inline-block;
            margin-top: 3px;
        }

        .col_unit {
            margin-left: 0;
            text-align: left;
            padding-left: 10px;
            width: 55px;
        }

    </style>
</head>

<body>

{% if (panel_preview) %}
    <div class="print-format">
        <!-- PCB SPECIFICATION -->
        {{ pageheader('') }}

        <div class="row"></div>

        <div class="row" style="margin-top: 60px;">
            <div class="col-xs-6">
                <p class="h2">Panel</p>
            </div>

            <div class="col-xs-6">
                <p class="h2">Panel Legend</p>
            </div>

            <div class="col-xs-6">
                <div class="panel_borderless_image" style="background-image: url({{ panel_preview }});"></div>
            </div>

            <div class="col-xs-6">
                <div class="panel_borderless_image" style="background-image: url({{ panel_legend }});"></div>
            </div>
        </div>

        <div class="row ">
            <div class="col-xs-6 ">
                <p class="h2"></p>
                <table class="table table-basic h3 table-borderless">
                    <tbody class="neutral-9 font-poppins">
                    <tr>
                        <th scope="row">Number of PCBs per panel:</th>
                        <td>{{ value(panel_number_of_pcbs) }}</td>
                        <td class="col_unit"></td>
                    </tr>
                    <tr>
                        <th scope="row">PCB mesh:</th>
                        <td>{{ value(panel_number_of_pcb_cols) }}x{{ value(panel_number_of_pcb_rows) }}</td>
                        <td class="col_unit"></td>
                    </tr>
                    <tr>
                        <th scope="row">Panel width <strong>(W)</strong>:</th>
                        <td>{{ roundValue(panel_width) }}</td>
                        <td class="col_unit">{{ unit('mm') }}</td>
                    </tr>
                    <tr>
                        <th scope="row">Panel height <strong>(H)</strong>:</th>
                        <td>{{ roundValue(panel_height) }}</td>
                        <td class="col_unit">{{ unit('mm') }}</td>
                    </tr>
                    <tr>
                        <th scope="row">Surrounding edge in X <strong>(A)</strong>:</th>
                        <td>{{ roundValue(panel_padding_left) }}</td>
                        <td class="col_unit">{{ unit('mm') }}</td>
                    </tr>
                    <tr>
                        <th scope="row">Surrounding edge in Y <strong>(B)</strong>:</th>
                        <td>{{ roundValue(panel_padding_top) }}</td>
                        <td class="col_unit">{{ unit('mm') }}</td>
                    </tr>
                    <tr>
                        <th scope="row">Distance between PCBs in X <strong>(C)</strong>:</th>
                        <td>{{ roundValue(panel_gap_x) }}</td>
                        <td class="col_unit">{{ unit('mm') }}</td>
                    </tr>
                    <tr>
                        <th scope="row">Distance between PCBs in Y <strong>(D)</strong>:</th>
                        <td>{{ roundValue(panel_gap_y) }}</td>
                        <td class="col_unit">{{ unit('mm') }}</td>
                    </tr>
                    </tbody>
                </table>
            </div>

        </div>

        {{ pagefooter() }}
    </div>
{% endif %}

</body>

</html>