# Properties
pcb.board.basic.width=board width
pcb.board.basic.height=board height
pcb.board.basic.surfaceFinish=surface finish
pcb.board.basic.soldermaskSide=soldermask side
pcb.board.basic.silkscreenSides=silkscreen sides
pcb.board.basic.soldermaskColor=soldermask color
pcb.board.basic.silkscreenColor=silkscreen color
pcb.board.basic.hardGold=hard gold
pcb.basic.outerCopperClearance=outer copper clearance
pcb.basic.innerCopperClearance=inner copper clearance
pcb.basic.outerTraceWidth=outer trace width
pcb.basic.innerTraceWidth=inner trace width
pcb.basic.phAnnularRing=ph annular ring

pcb.board.advanced.eTest=eTest
pcb.board.advanced.pressFit=press fit
pcb.board.advanced.impedanceTested=impedance tested
pcb.board.advanced.edgeMetalization=edge metalization
pcb.board.advanced.peelableMask=peelable mask
pcb.board.advance.ipcA600Class=IPC-A-600 class

pcb.layer.layerstackType=PCB type
pcb.layer.ulLayerStack=UL layer stack
pcb.layer.ulMarkingType=UL Marking type
pcb.layer.baseMaterial=base material
pcb.layer.numberOfLayers=number of layers
pcb.layer.finalThickness=final thickness
pcb.layer.minOuterLayerStructure=min outer layer structure
pcb.layer.minInnerLayerStructure=min inner layer structure
pcb.layer.outerCopperThickness=outer copper thickness
pcb.layer.innerCopperThickness=inner copper thickness
pcb.layer.customStackUp=custom stackup
pcb.layer.tgvalue=tg value

pcb.mechanical.blindVias=blind vias
pcb.mechanical.minViaDiameter=min via diameter
pcb.mechanical.nphMinDiameter=min nph diameter
pcb.mechanical.buriedVias=buried vias
pcb.mechanical.chamfering=chamfering
pcb.mechanical.zAxisMilling=z-axis milling
pcb.mechanical.viaFillingType=via filling

# Request
manufacturer.request.quantity=quantity

# Errors
pcb.error.capability.empty=The value for {0} is empty
pcb.error.capability.min=The value for {0} is below the supported minimum of {1}{2}
pcb.error.capability.max=The value for {0} is above the supported maximum of {1}{2}
pcb.error.capability.set=The value for {0} is not supported
pcb.error.capability.customStackUp=Custom stackups are not supported and need to be reviewed manually
pcb.error.capability.safePcb.hardHold.error=Hard Gold is not supported when HAL surface finish is selected
pcb.error.capability.wurth.blindVias.error=Blind vias are only supported when no buried vias are used
pcb.error.capability.wurth.buriedVias.error=Buried vias are only supported when the number of layers is between 4 and 8, the thickness is between 0.4 and 1.6mm and no blind vias are used
pcb.error.capability.wurth.surfaceFinish.HalPbFree=HAL (lead free) finish is only supported when the outer copper thickness is 35µm
pcb.error.converter.notSupported=The value {0} for {1} is not supported
pcb.error.compare.notSupported=The entered {0} {1}{2} is not supported for instant offers

# Upload warnings/errors
pcb.upload.fileAlreadyExists=Some files were not uploaded because they already exist in the assembly
pcb.upload.duplicateFiles=Duplicate files found in project archive
