# Properties
pcb.board.basic.width=Breite der Leiterplatte
pcb.board.basic.height=<PERSON>ö<PERSON> der Leiterplatte
pcb.board.basic.surfaceFinish=Oberfläche
pcb.board.basic.soldermaskSide=Seiten Lötstoppmaske
pcb.board.basic.silkscreenSides=Seiten Bestückungsdruck
pcb.board.basic.soldermaskColor=Farbe des Lötstopplacks
pcb.board.basic.silkscreenColor=Farbe des Bestückungsdrucks
pcb.board.basic.hardGold=Hartgold
pcb.board.advanced.eTest=E-Test
pcb.board.advanced.edgeMetalization=Kantenmetallisierung
pcb.layer.layerstackType=Leiterplattentyp
pcb.layer.ulMarkingType=UL-Kennzeichnung
pcb.layer.numberOfLayers=Anzahl an Lagen
pcb.layer.finalThickness=Enddicke
pcb.layer.minOuterLayerStructure=Kleinste Strukturbreite außen
pcb.layer.minInnerLayerStructure=Kleinste Strukturbreite innen
pcb.layer.outerCopperThickness=Endkupfer außen
pcb.layer.innerCopperThickness=Endkupfer innen
pcb.layer.customStackUp=Kundenspezifischer StackUp
pcb.layer.tgvalue=TG-Wert
pcb.mechanical.minViaDiameter=Kleinster Bohrdurchmesser
pcb.mechanical.nphMinDiameter=Kleinster Bohrdurchmesser (NPH)
pcb.mechanical.buriedVias=Buried Vias
pcb.mechanical.chamfering=Chamfering

# Manufacturer
manufacturer.request.quantity=Menge

# Errors
pcb.error.capability.empty=Der Wert für {0} ist nicht angegeben
pcb.error.capability.min=Der Wert für {0} liegt unter dem erlaubten Minimum von {1}{2}
pcb.error.capability.max=Der Wert für {0} liegt über dem erlaubten Maximum von {1}{2}
pcb.error.capability.set=Der Wert für {0} wird nicht unterstützt
pcb.error.capability.customStackUp=Kundenspezifische StackUps werden nicht automatisch unterstützt und müssen zunächst manuell geprüft werden.
pcb.error.capability.safePcb.hardHold.error=Hardgold wird zusammen mit HAL Oberflächen leider nicht unterstützt
pcb.error.capability.wurth.blindVias.error=Blind-Vias werden nur unterstütz falls keine Buried-Vias vorhanden sind
pcb.error.capability.wurth.buriedVias.error=Buried-Vias werden nur bei Leiterplatten mit einer Lagenanzahl zwischen 4 und 8, einer Dicke zwischen 0.4 und 1.6mm und falls keine Blind-Vias vorhanden sind, unterstützt.
pcb.error.capability.wurth.surfaceFinish.HalPbFree=HAL Oberflächen werden nur mit 35µm Außenkupfer unterstützt.
pcb.error.converter.notSupported=Der Wert {0} für {1} wird nicht unterstützt
pcb.error.compare.notSupported=Angegeben wurde {0} {1}{2}. Dies wird bei Echtzeitpreisen nicht unterstützt.
