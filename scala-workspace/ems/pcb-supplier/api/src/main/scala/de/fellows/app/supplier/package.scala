package de.fellows.app

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.supplier.Violations.DFMViolations
import de.fellows.ems.pcb.model.PCBSpecification
import de.fellows.utils.{JsonFormats, Region}
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json._

import java.time.Instant
import java.util.UUID

package object supplier {

  case class PCBSupplierDescription(
      name: String,
      id: Option[UUID],
      technologies: Option[Seq[Technology]],
      region: Option[Region],
      lqReference: Option[UUID] = None
  )

  case class PCBSupplier(team: String, id: UUID, name: String, region: Option[Region], lqReference: Option[UUID])

  case class Technology(
      team: Option[String],
      id: Option[String],
      name: String,
      pcbSupplier: Option[UUID],
      capabilities: Seq[Capability],
      priceCategory: Option[PriceCategory] = None
  )

  @deprecated
  case class TechnologySupplier(name: String)

  sealed trait Capability {
    val name: String
    val label: Option[String]
  }

  case class NumericalCapability(
      override val name: String,
      min: Option[BigDecimal],
      max: Option[BigDecimal],
      override val label: Option[String] = None
  ) extends Capability {}

  case class ListCapability(
      override val name: String,
      allowed: Option[Seq[String]],
      forbidden: Option[Seq[String]],
      override val label: Option[String] = None
  ) extends Capability {}

  case class BooleanCapability(override val name: String, allowed: Boolean, override val label: Option[String] = None)
      extends Capability {}

  case class StringCapability(
      override val name: String,
      allowed: Option[String],
      forbidden: Option[String],
      override val label: Option[String] = None
  ) extends Capability {}

  case class PriceCategory(category: Int)

  object PCBSupplierDescription {
    implicit val regionFormat: Format[Region]           = Region.nameFormat
    implicit val format: Format[PCBSupplierDescription] = Json.format
  }

  object PCBSupplier {
    def from(id: UUID, team: String, set: PCBSupplierDescription): PCBSupplier =
      PCBSupplier(
        team = team,
        id = id,
        name = set.name,
        region = set.region,
        lqReference = set.lqReference
      )

    implicit val regionFormat: Format[Region] = Region.nameFormat
    implicit val format: Format[PCBSupplier]  = Json.format
  }

  object TechnologySupplier {
    implicit val format: Format[TechnologySupplier] = Json.format
  }

  object PriceCategory {
    implicit val format: Format[PriceCategory] = Json.format
  }

  object Technology {
    implicit val format: Format[Technology] = Json.format
  }

  object NumericalCapability {
    implicit val format: Format[NumericalCapability] = Json.format
  }

  object ListCapability {
    implicit val format: Format[ListCapability] = Json.format
  }

  object BooleanCapability {
    implicit val format: Format[BooleanCapability] = Json.format
  }

  object StringCapability {
    implicit val format: Format[StringCapability] = Json.format
  }

  object Capability {

    implicit val cfg: JsonConfiguration.Aux[MacroOptions] = JsonFormats.JsonSealedTraitConfig

    implicit val format: Format[Capability] = Json.format
  }

  case class Manufacture(ref: Option[AssemblyReference], matches: Seq[ManufactureMatch])

  case class ManufactureMatch(spec: PCBSpecification, state: MatchingState, suppliers: Seq[SupplierManufacture])

  case class MatchingState(time: Option[Instant] = Some(Instant.now()), state: String)

  case class TechnologySelection(assembly: UUID, version: UUID, supplier: Option[UUID], technology: Option[String])

  case class SupplierManufacture(
      supplierID: String,
      canManufacture: Boolean,
      violations: Seq[DFMViolations],
      manufacturingInfo: Option[ManufacturingInfo]
  )

  case class ManufacturingInfo(
      technology: Technology
  )

  object Manufacture {
    val STATE_MATCHING = "matching"
    val STATE_READY    = "ready"
    val STATE_MISSING  = "missing"

    implicit val format: Format[Manufacture] = Json.format
  }

  object MatchingState {
    implicit val format: Format[MatchingState] = Json.format
  }

  object ManufacturingInfo {
    implicit val format: Format[ManufacturingInfo] = Json.format
  }

  object ManufactureMatch {
    implicit val format: Format[ManufactureMatch] = Json.format
  }

  object SupplierManufacture {
    implicit val format: Format[SupplierManufacture] = Json.format
  }

  object TechnologySelection {
    implicit val format: Format[TechnologySelection] = Json.format
  }

}
