// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.supplier

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.app.supplier.Topics.MatchingMessage
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.tags.Tag

import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate Technology/Supplier API"
  )
)
@Tag(name = SupplierService.API_TAG_ASSEMBLY, description = "Technologies specific for Assemblies")
@Tag(name = SupplierService.API_TAG_GENERAL, description = "Team-Technologies")
abstract class SupplierService extends Service with StackrateServiceAPI {

  private val subPath  = "ems/pcbsupplier"
  val basePath         = "/api/" + subPath
  val internalBasePath = "/internal/" + subPath + "/:team"
  val specificBasePath = s"$basePath/:id/:version"

  //  def dfmStatus(owner: UUID, assembly: UUID, version: UUID): ServiceCall[NotUsed, DFMStatus]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def createSupplier(): ServiceCall[PCBSupplierDescription, PCBSupplier]

  def _createSupplier(team: String): ServiceCall[PCBSupplierDescription, PCBSupplier]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def updateSupplier(nameOrID: String): ServiceCall[PCBSupplierDescription, PCBSupplier]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def getSupplier(nameOrID: String, technologies: Option[Boolean]): ServiceCall[NotUsed, PCBSupplierDescription]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def _getSupplier(
      team: String,
      nameOrID: String,
      technologies: Option[Boolean]
  ): ServiceCall[NotUsed, PCBSupplierDescription]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def getSuppliers(technologies: Option[Boolean]): ServiceCall[NotUsed, Seq[PCBSupplierDescription]]

  def _getSuppliers(team: String): ServiceCall[NotUsed, Seq[PCBSupplierDescription]]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def deleteSupplier(nameOrID: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def addTechnology(skipDefaults: Option[Boolean]): ServiceCall[Technology, Technology]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def addTeamTechnology(team: String, skipDefaults: Option[Boolean]): ServiceCall[Technology, Technology]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def getTechnologies(): ServiceCall[NotUsed, Seq[Technology]]

  def _getTechnologies(team: String): ServiceCall[NotUsed, Seq[Technology]]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def setTechnology(id: String): ServiceCall[Technology, Done]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def getTechnology(id: String): ServiceCall[Done, Technology]

  def _getTechnology(team: String, id: String): ServiceCall[NotUsed, Technology]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def setCapability(id: String): ServiceCall[Capability, Technology]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def setPriceCategory(id: String): ServiceCall[PriceCategory, Technology]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_GENERAL)
  def deleteTechnology(id: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_ASSEMBLY)
  def getSelectedTechnology(assembly: UUID, version: UUID, supplier: UUID): ServiceCall[NotUsed, TechnologySelection]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_ASSEMBLY)
  def getSelectedTechnologies(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[TechnologySelection]]

  def _getSelectedTechnologies(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Seq[TechnologySelection]]

  @StackrateApi
  @Tag(name = SupplierService.API_TAG_ASSEMBLY)
  def setSelectedTechnology(
      assembly: UUID,
      version: UUID,
      supplier: UUID
  ): ServiceCall[TechnologySelection, Seq[TechnologySelection]]

  def _setSelectedTechnology(
      team: String,
      assembly: UUID,
      version: UUID,
      supplier: UUID
  ): ServiceCall[TechnologySelection, Seq[TechnologySelection]]

  //  def setPriceList(id: String, version: UUID): ServiceCall[Seq[PriceBreak], Done]

  //  def getViolations(version: UUID, specification: UUID): ServiceCall[NotUsed, Seq[SupplierManufacture]]

  // TODO: get by supplier

  //  def streamViolations(version: UUID, specification: UUID, bearer: String): ServiceCall[NotUsed, Source[PCBSupplierStreamMessage, NotUsed]]

  def matchingTopic(): Topic[MatchingMessage]

  //  def startMatching(): ServiceCall[NotUsed, Seq[MatchingMessage]]

  //  def priceListTopic(): Topic[PriceListMessage]

  override def descriptor: Descriptor = {
    import Service._

    withDocumentation(subPath)(
      named(name = "pcbsupplier")
        .withCalls(
          //        restCall(Method.GET, s"${specificBasePath}/dfm", dfmStatus _)
          restCall(Method.GET, s"${basePath}/technologies", getTechnologies _),
          restCall(Method.GET, s"${internalBasePath}/technologies", _getTechnologies _),
          restCall(Method.POST, s"${basePath}/technologies?skipDefaults", addTechnology _),
          restCall(Method.POST, s"${basePath}/teams/:team/technologies?skipDefaults", addTeamTechnology _),
          restCall(Method.DELETE, s"${basePath}/technologies/:id", deleteTechnology _),
          restCall(Method.GET, s"${basePath}/technologies/:id", getTechnology _),
          restCall(Method.GET, s"${internalBasePath}/technologies/:id", _getTechnology _),
          restCall(Method.PUT, s"${basePath}/technologies/:id", setTechnology _),
          restCall(Method.PUT, s"${basePath}/technologies/:id/capabilities", setCapability _),
          restCall(Method.PUT, s"${basePath}/technologies/:id/pricecategory", setPriceCategory _),
          restCall(Method.POST, s"$internalBasePath/suppliers", _createSupplier _),
          restCall(Method.POST, s"${basePath}/suppliers", createSupplier _),
          restCall(Method.PUT, s"${basePath}/suppliers/:nameOrID", updateSupplier _),
          restCall(Method.GET, s"${basePath}/suppliers/:nameOrID?technologies", getSupplier _),
          restCall(Method.GET, s"${internalBasePath}/suppliers/:nameOrID?technologies", _getSupplier _),
          restCall(Method.DELETE, s"${basePath}/suppliers/:nameOrID?technologies", deleteSupplier _),
          restCall(Method.GET, s"${basePath}/suppliers?technologies", getSuppliers _),
          restCall(Method.GET, s"${internalBasePath}/suppliers", _getSuppliers _),
          restCall(
            Method.GET,
            s"${basePath}/assemblies/:assembly/versions/:version/technologies",
            getSelectedTechnologies _
          ),
          restCall(
            Method.GET,
            s"${basePath}/assemblies/:assembly/versions/:version/suppliers/:supplier/technology",
            getSelectedTechnology _
          ),
          restCall(
            Method.PUT,
            s"${basePath}/assemblies/:assembly/versions/:version/suppliers/:supplier/technology",
            setSelectedTechnology _
          ),
          restCall(
            Method.PUT,
            s"${internalBasePath}/assemblies/:assembly/versions/:version/suppliers/:supplier/technology",
            _setSelectedTechnology _
          ),
          restCall(
            Method.GET,
            s"${internalBasePath}/assemblies/:assembly/versions/:version/technologies",
            _getSelectedTechnologies _
          )

          //        restCall(Method.POST, s"${specificBasePath}/pricelist", setPriceList _),

          //        restCall(Method.PUT, s"/api/ems/pcbsupplier/matching/:version", startMatching _)
        )
        .withTopics(
          topic(SupplierService.TOPIC_MATCHING, matchingTopic)
          //        topic(SupplierService.PRICE_LIST_TOPIC, priceListTopic)
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"$basePath/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
  }
}

object SupplierService {
  val VERSION        = "v1.1"
  val TOPIC_MATCHING = s"domain.ems.pcbsupplier.matched-$VERSION"
  //  val PRICE_LIST_TOPIC = s"domain.ems.pcbsupplier.pricelist-$VERSION"

  final val API_TAG_GENERAL  = "Technologies"
  final val API_TAG_ASSEMBLY = "Assemblies"

}
//
