package de.fellows.app.supplier

import com.datastax.driver.core.{ ProtocolVersion, TypeCodec, UDTValue, UserType }
import de.fellows.app.quotation.PriceBreak
import de.fellows.ems.pcb.model.codec.PCBCodecHelper

import java.nio.ByteBuffer

class PriceBreakCodec(cdc: TypeCodec[UDTValue]) extends TypeCodec[PriceBreak](cdc.getCqlType, classOf[PriceBreak]) {
  def toUDTValue(value: PriceBreak): UDTValue =
    if (value == null) null
    else
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .setInt("min", value.min)
        .setInt("max", value.max)
        .setDecimal("time", value.deliveryTime.bigDecimal)
        .setDecimal("price", value.unitPrice.map(_.bigDecimal).orNull)
        .setDecimal("panelPrice", value.unitPrice.map(_.bigDecimal).orNull)
        .setInt("panelCount", value.panelCount.getOrElse(-1))
        .setDecimal("fixCosts", value.fixCosts.map(_.bigDecimal).orNull)

  def fromUDTValue(value: UDTValue): PriceBreak =
    if (value == null) null
    else
      PriceBreak(
        value.getInt("min"),
        value.getInt("max"),
        value.getDecimal("time"),
        PCBCodecHelper.getBigDecimal(value, "price"),
        PCBCodecHelper.getBigDecimal(value, "panelPrice"),
        value.getInt("panelCount") match {
          case -1 => None
          case x  => Some(x)
        },
        PCBCodecHelper.getBigDecimal(value, "fixCosts")
      )

  override def serialize(value: PriceBreak, protocolVersion: ProtocolVersion): ByteBuffer =
    cdc.serialize(toUDTValue(value), protocolVersion)

  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): PriceBreak =
    fromUDTValue(cdc.deserialize(bytes, protocolVersion))

  override def parse(value: String): PriceBreak =
    if (value == null || value.isEmpty) null
    else fromUDTValue(cdc.parse(value))

  override def format(value: PriceBreak): String =
    if (value == null) null
    else cdc.format(toUDTValue(value))
}
