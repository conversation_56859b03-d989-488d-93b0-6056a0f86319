package de.fellows.app.supplier.entity.supplier

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.supplier.{ PCBSupplier, Technology }
import play.api.libs.json.{ Format, Json }

sealed trait PCBSupplierCommand {
  val team: String
}

case class SetSupplier(override val team: String, supp: PCBSupplier) extends PCBSupplierCommand
    with ReplyType[PCBSupplier]

case class DeleteSupplier(override val team: String) extends PCBSupplierCommand with ReplyType[PCBSupplier]

case class GetSupplier(override val team: String) extends PCBSupplierCommand with ReplyType[SupplierResponse]

case class SupplierResponse(response: Option[PCBSupplier])

sealed trait PCBTechnologyCommand

case class SetTechnology(team: String, technology: Technology) extends PCBTechnologyCommand with ReplyType[Technology]

case class DeleteTechnology(team: String, id: String) extends PCBTechnologyCommand with ReplyType[Done]

case class GetTechnology(team: String, id: String) extends PCBTechnologyCommand with ReplyType[Technology]

object SupplierResponse {
  implicit val format: Format[SupplierResponse] = Json.format
}

object SetSupplier {
  implicit val format: Format[SetSupplier] = Json.format
}

object DeleteSupplier {
  implicit val format: Format[DeleteSupplier] = Json.format
}

object GetSupplier {
  implicit val format: Format[GetSupplier] = Json.format
}

object SetTechnology {
  implicit val format: Format[SetTechnology] = Json.format
}

object DeleteTechnology {
  implicit val format: Format[DeleteTechnology] = Json.format
}

object GetTechnology {
  implicit val format: Format[GetTechnology] = Json.format
}
