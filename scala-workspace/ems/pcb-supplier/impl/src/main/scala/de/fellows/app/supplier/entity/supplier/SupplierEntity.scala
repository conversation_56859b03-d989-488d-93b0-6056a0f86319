package de.fellows.app.supplier.entity.supplier

import com.lightbend.lagom.scaladsl.api.transport.{ TransportErrorCode, TransportException }
import de.fellows.app.supplier.PCBSupplier
import de.fellows.utils.common.EntityException
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity

class SupplierEntity(implicit override val service: ServiceDefinition) extends SecureTeamEntity[Option[PCBSupplier]] {
  override type Command = PCBSupplierCommand
  override type Event   = PCBSupplierEvent2

  override def initialState: Option[PCBSupplier] = None

  override def isAllowed(a: PCBSupplierCommand, s: Option[PCBSupplier]): Boolean =
    s.map(_.team).getOrElse(a.team) == a.team

  override def entityBehavior(state: Option[PCBSupplier]): Actions =
    Actions()
      .onReadOnlyCommand[GetSupplier, SupplierResponse] {
        case (x: GetSupplier, ctx, s) =>
          ctx.reply(SupplierResponse(s))
      }
      .onCommand[SetSupplier, PCBSupplier] {
        case (x: SetSupplier, ctx, s) =>
          if (x.supp.id != s.map(_.id).getOrElse(x.supp.id)) {
            ctx.commandFailed(EntityException(TransportErrorCode.BadRequest, "Invalid ID"))
            ctx.done
          } else {
            ctx.thenPersist(PCBSupplierChanged(x.supp.team, x.supp, s))(_ => ctx.reply(x.supp))
          }
      }
      .onCommand[DeleteSupplier, PCBSupplier] {
        case (x: DeleteSupplier, ctx, s) =>
          if (s.isEmpty) {
            ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Supplier not Found"))
            ctx.done
          } else {
            ctx.thenPersist(PCBSupplierDeleted(s.get.team, s.get))(_ => ctx.reply(s.get))
          }
      }
      .onEvent {
        case (x: PCBSupplierChanged, _) => Some(x.supp)
        case (x: PCBSupplierDeleted, _) => None
      }
}
