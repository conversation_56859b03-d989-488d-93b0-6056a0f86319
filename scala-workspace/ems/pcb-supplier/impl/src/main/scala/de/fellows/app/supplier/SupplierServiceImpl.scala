// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.supplier

import akka.actor.ActorSystem
import akka.persistence.query.Offset
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.security.AccessControlServiceComposition.authorizedString
import de.fellows.app.supplier.entity.manufacture._
import de.fellows.app.supplier.entity.supplier._
import de.fellows.app.supplier.entity.supplierselection.{
  GetTechnologySelection,
  SelectTechnology,
  TechnologySelectionEntity
}
import de.fellows.utils.UUIDUtils._
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.{FutureUtils, UUIDUtils}
import play.api.Logging

import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class SupplierServiceImpl(
    ereg: PersistentEntityRegistry,
    techRep: TechnologyRepository,
    suppRep: SupplierRepository,
    system: ActorSystem
)(implicit ctx: ExecutionContext, sd: ServiceDefinition) extends SupplierService
    with StackrateAPIImpl with Logging {

  implicit val actorSystem: ActorSystem = system

  private def withSupplier[T](team: String, supp: UUID)(body: PCBSupplier => Future[T]) =
    ereg.refFor[SupplierEntity](supp.toString).ask(GetSupplier(team))
      .map(_.response)
      .flatMap {
        case Some(supp) =>
          body(supp)
        case None =>
          throw new TransportException(TransportErrorCode.BadRequest, "Supplier does not exist")

      }

  override def createSupplier(): ServiceCall[PCBSupplierDescription, PCBSupplier] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:${token.team}:*:supplier:write"
    ) { (token, _) =>
      ServerServiceCall { set =>
        doCreateSupplier(token.getTeam, set)
      }
    }

  override def _createSupplier(team: String): ServiceCall[PCBSupplierDescription, PCBSupplier] =
    ServerServiceCall { set =>
      doCreateSupplier(team, set)
    }

  private def doCreateSupplier(team: String, set: PCBSupplierDescription): Future[PCBSupplier] =
    suppRep.getSupplierByName(team, set.name).flatMap {
      case None =>
        val id = UUID.randomUUID()
        ereg.refFor[SupplierEntity](id.toString).ask(SetSupplier(team, PCBSupplier.from(id, team, set)))

      case _ => throw new TransportException(TransportErrorCode.BadRequest, "Supplier with this name exists")
    }

  override def updateSupplier(nameOrID: String): ServiceCall[PCBSupplierDescription, PCBSupplier] = {
    var id: UUID = null
    authorizedString { token =>
      id = UUIDUtils.maybeInstant(
        nameOrID,
        () =>
          suppRep.getSupplierByName(token.team, nameOrID).map(_.map(_.id))
      ).getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Supplier not found"))

      s"pcbsupplier:${token.team}:${token.team}:$id:supplier:write"
    } { (token, _) =>
      ServerServiceCall { set =>
        ereg.refFor[SupplierEntity](id.toString).ask(SetSupplier(token.team, PCBSupplier.from(id, token.team, set)))

      }
    }
  }

  override def getSupplier(
      nameOrID: String,
      technologies: Option[Boolean]
  ): ServiceCall[NotUsed, PCBSupplierDescription] = {
    var id: UUID = null
    authorizedString { token =>
      id = UUIDUtils.maybeInstant(
        nameOrID,
        () =>
          suppRep.getSupplierByName(token.team, nameOrID).map(_.map(_.id))
      ).getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Supplier not found"))

      s"pcbsupplier:${token.team}:${token.team}:$id:supplier:read"
    } { (token, _) =>
      ServerServiceCall { set =>
        _doGetSupplier(token.team, id, technologies)
      }
    }
  }

  override def _getSupplier(
      team: String,
      nameOrID: String,
      technologies: Option[Boolean]
  ): ServiceCall[NotUsed, PCBSupplierDescription] = {
    var id: UUID = null
    ServerServiceCall { set =>
      id = UUIDUtils.maybeInstant(
        nameOrID,
        () =>
          suppRep.getSupplierByName(team, nameOrID).map(_.map(_.id))
      ).getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Supplier not found"))

      _doGetSupplier(team, id, technologies)
    }
  }

  private def _doGetSupplier(team: String, id: UUID, technologies: Option[Boolean]) =
    ereg.refFor[SupplierEntity](id.toString).ask(GetSupplier(team))
      .map(_.response)
      .map(_.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Supplier not found")))
      .flatMap { _s =>
        resolveTechnologies(technologies, _s.id, team)
          .map(x => (_s, x))
      }
      .map(s =>
        PCBSupplierDescription(
          name = s._1.name,
          id = Some(s._1.id),
          technologies = s._2,
          region = s._1.region,
          lqReference = s._1.lqReference
        )
      )

  override def _getSuppliers(team: String): ServiceCall[NotUsed, Seq[PCBSupplierDescription]] =
    ServerServiceCall { _ =>
      _doGetSupplierDescriptions(team, Some(true))
    }

  override def getSuppliers(technologies: Option[Boolean]): ServiceCall[NotUsed, Seq[PCBSupplierDescription]] =
    authorizedString { token =>
      s"pcbsupplier:${token.team}:${token.team}:*:supplier:read"
    } { (token, _) =>
      ServerServiceCall { set =>
        _doGetSupplierDescriptions(token.team, technologies)
      }
    }

  private def _doGetSupplierDescriptions(team: String, resolve: Option[Boolean]) =
    _doGetSuppliers(team, resolve)
      .map(_.map(s =>
        PCBSupplierDescription(
          name = s._1.name,
          id = Some(s._1.id),
          technologies = s._2,
          region = s._1.region,
          lqReference = s._1.lqReference
        )
      ))

  private def _doGetSuppliers(
      team: String,
      resolve: Option[Boolean]
  ): Future[Seq[(PCBSupplier, Option[Seq[Technology]])]] =
    suppRep.getSuppliersForTeam(team).flatMap { ids =>
      logger.info(s"get suppliers ${ids}")
      Future.sequence(ids.map { dto =>
        ereg.refFor[SupplierEntity](dto.id.toString).ask(GetSupplier(team))
          .map(_.response)
          .flatMap { s =>
            logger.info(s"get supplier ${dto.id} => $s")
            FutureUtils.option(s.map { _s =>
              resolveTechnologies(resolve, _s.id, team)
                .map(x => (_s, x))

            })
          }
      })
    }
      .map(_.flatten)

  private def resolveTechnologies(
      shouldResolve: Option[Boolean],
      supplier: UUID,
      team: String
  ): Future[Option[Seq[Technology]]] =
    (shouldResolve match {
      case Some(true) =>
        this._doGetTechnologiesForTeam(team).map(_.filter(_.pcbSupplier.contains(supplier)))
          .map(Some(_))
      case _ =>
        Future.successful(None)
    })

  override def deleteSupplier(nameOrID: String): ServiceCall[NotUsed, Done] = {
    var id: UUID = null
    authorizedString { token =>
      id = UUIDUtils.maybeInstant(
        nameOrID,
        () =>
          suppRep.getSupplierByName(token.team, nameOrID).map(_.map(_.id))
      ).getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Supplier not found"))

      s"pcbsupplier:${token.team}:${token.team}:$id:supplier:write"
    } { (token, _) =>
      ServerServiceCall { set =>
        ereg.refFor[SupplierEntity](id.toString).ask(DeleteSupplier(token.team))
          .map(_ => Done)
      }
    }
  }

  override def deleteTechnology(id: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:${token.team}:$id:technology:delete"
    ) { (token, _) =>
      ServerServiceCall { set =>
        ereg.refFor[TechnologyEntity](id).ask(DeleteTechnology(token.team, id)).map(_ => Done)
      }
    }

  override def addTechnology(skipDefaults: Option[Boolean]): ServiceCall[Technology, Technology] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:${token.team}:*:technology:create"
    ) { (token, _) =>
      ServerServiceCall { set =>
        withSupplier(
          token.team,
          set.pcbSupplier.getOrElse(throw new TransportException(
            TransportErrorCode.BadRequest,
            "Supplier not specified"
          ))
        ) { _ =>
          val generatedID = UUID.randomUUID().short()

          val c =
            if (skipDefaults.contains(true)) {
              set.capabilities
            } else {
              SupplierServiceImpl.merge(SupplierServiceImpl.defaultCapabilities, set.capabilities)
            }

          ereg.refFor[TechnologyEntity](generatedID).ask(SetTechnology(
            token.team,
            set.copy(id = Some(generatedID), capabilities = c, team = Some(token.team))
          ))
        }
      }
    }

  override def addTeamTechnology(team: String, skipDefaults: Option[Boolean]): ServiceCall[Technology, Technology] =
    authorizedString(token =>
      s"pcbsupplier:${team}:${team}:*:technology:create"
    ) { (token, _) =>
      ServerServiceCall { set =>
        withSupplier(token.team, set.pcbSupplier.get) { _ =>
          val id = UUID.randomUUID().short()
          val c =
            if (skipDefaults.contains(true)) {
              set.capabilities
            } else {
              SupplierServiceImpl.merge(SupplierServiceImpl.defaultCapabilities, set.capabilities)
            }
          ereg.refFor[TechnologyEntity](id).ask(SetTechnology(
            team,
            set.copy(id = Some(id), capabilities = c, team = Some(team))
          ))
        }
      }
    }

  override def getTechnologies(): ServiceCall[NotUsed, Seq[Technology]] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:${token.team}:*:technology:read"
    ) { (token, _) =>
      _getTechnologies(token.team)
    }

  override def _getTechnologies(team: String): ServerServiceCall[NotUsed, Seq[Technology]] =
    ServerServiceCall { _ =>
      _doGetTechnologiesForTeam(team)
    }

  private def _doGetTechnologiesForTeam(team: String) =
    techRep.getTechnologiesForTeam(team)
      .map(_.map(t => ereg.refFor[TechnologyEntity](t.id).ask(GetTechnology(team, t.id))))
      .map(Future.sequence(_))
      .flatten

  override def setTechnology(id: String): ServiceCall[Technology, Done] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:${token.team}:$id:technology:create"
    ) { (token, _) =>
      ServerServiceCall { set =>
        withSupplier(
          token.team,
          set.pcbSupplier.getOrElse(throw new TransportException(
            TransportErrorCode.BadRequest,
            "Supplier not specified"
          ))
        ) { _ =>
          ereg.refFor[TechnologyEntity](id).ask(SetTechnology(token.team, set.copy(team = Some(token.team)))).map(_ =>
            Done
          )
        }
      }
    }

  override def getTechnology(id: String): ServiceCall[Done, Technology] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:${token.team}:$id:technology:read"
    ) { (token, _) =>
      ServerServiceCall { set =>
        ereg.refFor[TechnologyEntity](id).ask(GetTechnology(token.team, id))
      }
    }

  override def _getTechnology(team: String, id: String): ServiceCall[NotUsed, Technology] =
    ServerServiceCall { set =>
      ereg.refFor[TechnologyEntity](id).ask(GetTechnology(team, id))
    }

  import de.fellows.utils.ListUtils._

  override def setCapability(id: String): ServiceCall[Capability, Technology] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:${token.team}:$id:capabilities:create"
    ) { (token, _) =>
      ServerServiceCall { set =>
        val e = ereg.refFor[TechnologyEntity](id)
        e.ask(GetTechnology(token.team, id)).flatMap { t =>
          val updated = t.capabilities.upsert(set, _.name == _.name)
          e.ask(SetTechnology(t.team.get, t.copy(capabilities = updated)))
        }
      }
    }

  override def setPriceCategory(id: String): ServiceCall[PriceCategory, Technology] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:*:*:*:create"
    ) { (token, _) =>
      ServerServiceCall { set =>
        val e = ereg.refFor[TechnologyEntity](id)
        e.ask(GetTechnology(token.team, id)).flatMap { t =>
          e.ask(SetTechnology(t.team.get, t.copy(priceCategory = Some(set))))
        }
      }
    }

  override def getSelectedTechnology(
      assembly: UUID,
      version: UUID,
      supplier: UUID
  ): ServiceCall[NotUsed, TechnologySelection] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:$version:*:selection:read"
    ) { (token, _) =>
      ServerServiceCall { set =>
        val ref = AssemblyReference(token.team, assembly, None, version)
        ereg.refFor[TechnologySelectionEntity](version.toString).ask(GetTechnologySelection(ref))
          .map(_.response)
          .map(_.find(st => st.supplier.contains(supplier)).getOrElse(TechnologySelection(
            assembly,
            version,
            Some(supplier),
            None
          )))
      }
    }

  override def getSelectedTechnologies(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[TechnologySelection]] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:$version:*:selection:read"
    ) { (token, _) =>
      ServerServiceCall { set =>
        _doGetSelectedTechnologies(token.team, assembly, version)
      }
    }

  override def _getSelectedTechnologies(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Seq[TechnologySelection]] =
    ServerServiceCall { set =>
      _doGetSelectedTechnologies(team, assembly, version)
    }

  private def _doGetSelectedTechnologies(team: String, assembly: UUID, version: UUID) = {

    val ref = AssemblyReference(team, assembly, None, version)

    for {
      selected <-
        ereg.refFor[TechnologySelectionEntity](version.toString).ask(GetTechnologySelection(ref)).map(_.response)
      suppliers <- _doGetSuppliers(team, None).map(_.map(_._1))
    } yield suppliers.map { s =>
      selected.find(_.supplier.contains(s.id)) match {
        case Some(value) => value
        case None        => TechnologySelection(assembly, version, Some(s.id), None)
      }
    }

  }

  private def _doSetTechnology(
      team: String,
      assembly: UUID,
      version: UUID,
      supplier: UUID,
      set: TechnologySelection
  ): Future[Seq[TechnologySelection]] = {
    val ref = AssemblyReference(team, assembly, None, version)

    ereg.refFor[SupplierEntity](supplier.toString).ask(GetSupplier(team))
      .map(_.response)
      .flatMap { so =>
        val s = so.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Supplier does not exist"))
        ereg.refFor[TechnologySelectionEntity](version.toString).ask(SelectTechnology(ref, s.id, set.technology)).map(
          _.response
        )
      }
  }

  override def _setSelectedTechnology(
      team: String,
      assembly: UUID,
      version: UUID,
      supplier: UUID
  ): ServiceCall[TechnologySelection, Seq[TechnologySelection]] =
    ServerServiceCall { set =>
      _doSetTechnology(team, assembly, version, supplier, set)
    }

  override def setSelectedTechnology(
      assembly: UUID,
      version: UUID,
      supplier: UUID
  ): ServiceCall[TechnologySelection, Seq[TechnologySelection]] =
    authorizedString(token =>
      s"pcbsupplier:${token.team}:$version:*:selection:write"
    ) { (token, _) =>
      ServerServiceCall { set =>
        _doSetTechnology(token.team, assembly, version, supplier, set)
      }
    }

  def isRelevantEvent(event: MatchingFinished, version: UUID, specification: UUID): Boolean =
    event.spec.id == specification && event.spec.assembly.version == version

  def filterMatchEvents(el: EventStreamElement[ManufactureEvent])
      : immutable.Iterable[(Topics.MatchingMessage, Offset)] = el match {
    case x @ EventStreamElement(_, e: MatchingFinished, _) =>
      immutable.Seq((
        Topics.MatchingMessage(
          e.spec,
          e.result
        ),
        x.offset
      ))
    case _ => Nil
  }

  override def matchingTopic(): Topic[Topics.MatchingMessage] =
    TopicProducer.taggedStreamWithOffset(ManufactureEvent.Tag) { (tag, offset) =>
      ereg.eventStream(tag, offset).mapConcat(filterMatchEvents)
    }
}

object SupplierServiceImpl {

  import de.fellows.ems.pcb.model.DFM.Properties._

  def merge(base: Seq[Capability], add: Seq[Capability]): Seq[Capability] = {
    val baseMap = base.groupBy(_.name)
    val addMap  = add.groupBy(_.name)

    val resMap: Map[String, Seq[Capability]] = (baseMap ++ addMap)

    resMap.map(x => (x._1, x._2.head)).values.toSeq
  }

  def defaultCapabilities: List[Capability] = List(
    new NumericalCapability(DFM.WIDTH, Some(10), Some(400)),
    new NumericalCapability(DFM.HEIGHT, Some(10), Some(500)),

    // ELECTRICAL
    new NumericalCapability(DFM.TRACE_WIDTH, Some(0.15), None),
    new NumericalCapability(DFM.PH_ANNULAR_RING, Some(0.15), None),
    new NumericalCapability(DFM.CLEARANCE, Some(0.125), None),
    new NumericalCapability(DFM.OUTLINE_CLEARANCE, Some(0.1), None),
    new NumericalCapability(DFM.SILKSCREEN_CLEARANCE, Some(0.15), None),
    new ListCapability(Settings.SILKSCREEN_SIDES, Some(Seq("Top", "Bottom")), None),
    new ListCapability(Settings.SILKSCREEN_COLOR, Some(Seq("Top", "Bottom")), None),
    new NumericalCapability(DFM.SOLDERMASK_CLEARANCE, Some(0.125), None),
    new NumericalCapability(DFM.SOLDERMASK_DAM, Some(0.1), None),
    new ListCapability(Settings.SOLDERMASK_SIDES, Some(Seq("Top", "Bottom")), None),
    new ListCapability(Settings.COLOR, Some(Seq(Colors.GREEN, Colors.BLUE, Colors.RED, Colors.WHITE)), None),
    new ListCapability(
      Settings.FINISH,
      Some(Seq(Finish.FINISH_NONE, Finish.HAL, Finish.ENIG, Finish.HAL_LEADFREE, Finish.SILVER, Finish.CHEMICAL_TIN)),
      None
    ),
    new NumericalCapability(DFM.NPH_MIN_SIZE, Some(0.25), None),
    new NumericalCapability(DFM.NPH_MAX_SIZE, None, Some(6)),
    new NumericalCapability(DFM.NPH_COUNT, None, Some(5000)),
    new NumericalCapability(DFM.PH_MIN_SIZE, Some(0.25), None),
    new NumericalCapability(DFM.PH_MAX_SIZE, None, Some(6)),
    new NumericalCapability(DFM.PH_COUNT, None, Some(5000)),
    new NumericalCapability(DFM.BLIND_VIA_MIN_SIZE, Some(0.15), None),
    new NumericalCapability(DFM.BLIND_VIA_MAX_SIZE, None, Some(1)),
    new NumericalCapability(DFM.BLIND_VIA_COUNT, None, Some(5000)),
    new NumericalCapability(DFM.BURIED_VIA_MIN_SIZE, Some(0.15), None),
    new NumericalCapability(DFM.BURIED_VIA_MAX_SIZE, None, Some(1)),
    new NumericalCapability(DFM.BURIED_VIA_COUNT, None, Some(5000))
  )
}
