package de.fellows.app.supplier

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.supplier.entity.manufacture.ManufactureEntity
import de.fellows.app.supplier.entity.supplier.{SupplierEntity, TechnologyEntity}
import de.fellows.app.supplier.entity.supplierselection.TechnologySelectionEntity
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import de.fellows.utils.communication.ServiceDefinition
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class SupplierServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new SupplierServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new SupplierServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[SupplierService])
}

trait SupplierServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("pcbsupplier")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = SupplierServiceSerializerRegistry

  lazy val notificationRepository = wire[TechnologyRepository]
  lazy val supplierRepository     = wire[SupplierRepository]

  val userEventProcessor = wire[TechnologyEventProcessor]
  readSide.register(userEventProcessor)

  val suppEventProcessor = wire[SupplierEventProcessor]
  readSide.register(suppEventProcessor)

  persistentEntityRegistry.register(wire[TechnologyEntity])
  persistentEntityRegistry.register(wire[SupplierEntity])
  persistentEntityRegistry.register(wire[ManufactureEntity])
  persistentEntityRegistry.register(wire[TechnologySelectionEntity])

}

abstract class SupplierServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with SupplierServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  override lazy val lagomServer = serverFor[SupplierService](wire[SupplierServiceImpl])

  lazy val pcbService = serviceClient.implement[PCBService]
}
