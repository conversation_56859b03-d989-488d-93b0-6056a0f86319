package de.fellows.app.supplier.entity.supplier

import akka.Done
import com.lightbend.lagom.scaladsl.api.transport.{ TransportErrorCode, TransportException }
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.app.supplier.Technology
import de.fellows.utils.common.EntityException

class TechnologyEntity extends PersistentEntity {
  override type Command = PCBTechnologyCommand
  override type Event   = PCBSupplierEvent
  override type State   = Option[Technology]

  override def initialState: Option[Technology] = None

  override def behavior: Behavior = {
    case Some(s) => existing(s)
    case None    => missing
  }

  def missingID = throw EntityException(TransportErrorCode.BadRequest, "ID is missing")

  def missing =
    Actions()
      .onCommand[SetTechnology, Technology] {
        case (x: SetTechnology, ctx, _) =>
          ctx.thenPersist(
            TechnologySet(x.technology.id.getOrElse(missingID), x.team, x.technology)
          )(_ => ctx.reply(x.technology))
      }
      .onReadOnlyCommand[DeleteTechnology, Done] {
        case (x: DeleteTechnology, ctx, _) =>
          ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "technology not found"))
      }
      .onReadOnlyCommand[GetTechnology, Technology] {
        case (x: GetTechnology, ctx, _) =>
          ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "technology not found"))

      }
      .onEvent {
        case (x: TechnologySet, _) => Some(x.technology)
      }

  def existing(state: Technology) =
    Actions()
      .onReadOnlyCommand[GetTechnology, Technology] {
        case (x: GetTechnology, ctx, _) => ctx.reply(state)
      }
      .onCommand[SetTechnology, Technology] {
        case (x: SetTechnology, ctx, _) =>
          ctx.thenPersist(
            TechnologySet(x.technology.id.getOrElse(missingID), x.team, x.technology)
          )(_ => ctx.reply(x.technology))
      }
      .onCommand[DeleteTechnology, Done] {
        case (x: DeleteTechnology, ctx, s) =>
          ctx.thenPersist(
            TechnologyDeleted(state.id.getOrElse(missingID), x.team)
          )(_ => ctx.reply(Done))
      }
      .onEvent {
        case (x: TechnologySet, _) =>
          Some(x.technology)
        case (_: TechnologyDeleted, _) =>
          None
      }
}
