package de.fellows.app.supplier.entity.manufacture

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.supplier.{ Manufacture, SupplierManufacture }
import de.fellows.ems.pcb.model.PCBSpecification
import play.api.libs.json
import play.api.libs.json.Json

import java.util.UUID

sealed trait ManufactureCommand

case class StartMatching(spec: PCBSpecification) extends ManufactureCommand with ReplyType[Done]

case class StopMatching(spec: PCBSpecification, suppliers: Seq[SupplierManufacture]) extends ManufactureCommand
    with ReplyType[Done]

case class GetManufactureInfo(version: UUID) extends ManufactureCommand with ReplyType[ManufactureResponse]

case class ManufactureResponse(response: Option[Manufacture])
//case class SetPriceList(supplierId: String, priceList: PriceList) extends ManufactureCommand with ReplyType[Done]

object ManufactureResponse {
  implicit val format: json.Format[ManufactureResponse] = Json.format
}

object StartMatching {
  implicit val format: json.Format[StartMatching] = Json.format
}

object StopMatching {
  implicit val format: json.Format[StopMatching] = Json.format
}
object GetManufactureInfo {
  implicit val format: json.Format[GetManufactureInfo] = Json.format
}

//object SetPriceList {
//  implicit val format: json.Format[SetPriceList] = Json.format
//}
