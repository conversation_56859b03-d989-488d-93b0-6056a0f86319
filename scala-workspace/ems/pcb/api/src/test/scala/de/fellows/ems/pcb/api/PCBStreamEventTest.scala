package de.fellows.ems.pcb.api

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.{DrillSet, GerberFile, MetaStatus, PCBSpecification, PCBVersion}
import de.fellows.utils.collaboration.{SpecificTimelineChange, TimelineCommand, TimelineEvent, TimelineReference}
import de.fellows.utils.internal.FileType
import de.fellows.utils.meta.MetaInfo
import de.fellows.utils.security.TokenContent
import de.fellows.utils.{FilePath, FilePathUtils}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import play.api.libs.json.Json

import java.time.Instant
import java.util.UUID

class PCBStreamEventTest extends AnyFlatSpec with should.Matchers {
  val id                = UUID.randomUUID()
  val version           = UUID.randomUUID()
  val assemblyReference = AssemblyReference("team", id, None, version)
  val path              = FilePathUtils.createTransientTemporaryFile("gerber", "gbr")
  val gerberFile        = GerberFile(id, "test.gb", path, FileType.UNKNOWN, hash = None)
  val pcbSpecification = PCBSpecification(
    assemblyReference,
    id,
    "test",
    MetaInfo(),
    MetaInfo(),
    None,
    base = None,
    preview = None,
    previewRear = None,
    changes = Some(Seq.empty)
  )

  "FileMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(FileMessage(assemblyReference, Seq(gerberFile)))
    (json \ "ass").as[AssemblyReference] shouldBe assemblyReference
    (json \ "gf").as[Seq[GerberFile]] shouldBe Seq(gerberFile)
  }
  "PCBTimelineMessage" should "have a valid JSON representation" in {
    val token = TokenContent(id, "test", "test", None, None)
    val event = TimelineEvent.of(
      "test",
      TimelineCommand.of(token),
      TimelineReference("entity", "entityType"),
      SpecificTimelineChange("category", "change_type", "summary", "description")
    )

    val json = Json.toJson(PCBTimelineMessage(event))
    (json \ "evt").as[TimelineEvent] shouldBe event
  }
  "DrillsAddedMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(DrillsAddedMessage(assemblyReference, gerberFile, path))
    (json \ "ass").as[AssemblyReference] shouldBe assemblyReference
    (json \ "gf").as[GerberFile] shouldBe gerberFile
    (json \ "holes").as[FilePath] shouldBe path
  }
  "DrillSetsAddedMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(DrillSetsAddedMessage(assemblyReference, Seq()))
    (json \ "ass").as[AssemblyReference] shouldBe assemblyReference
    (json \ "gf").as[Seq[DrillSet]] shouldBe Seq()
  }
  "PCBMessage" should "have a valid JSON representation" in {
    val pcbVersion = PCBVersion()
    val json       = Json.toJson(PCBMessage(assemblyReference, pcbVersion))
    (json \ "assRef").as[AssemblyReference] shouldBe assemblyReference
    (json \ "pcb").as[PCBVersion] shouldBe pcbVersion
  }
  "DFMMessage" should "have a valid JSON representation" in {
    val status = MetaStatus("test", Instant.now())
    val json   = Json.toJson(DFMMessage(assemblyReference, status))
    (json \ "assRef").as[AssemblyReference] shouldBe assemblyReference
    (json \ "status").as[MetaStatus] shouldBe status
  }
  "SpecificationMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(SpecificationMessage(pcbSpecification, dfm = false))
    (json \ "spec").as[PCBSpecification] shouldBe pcbSpecification
    (json \ "dfm").as[Boolean] shouldBe false
  }
  "SpecificationSavedMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(SpecificationSavedMessage(Some(pcbSpecification)))
    (json \ "spec").asOpt[PCBSpecification] shouldBe Some(pcbSpecification)
  }
  "MetaInfoMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(MetaInfoMessage(assemblyReference, MetaInfo(), None))
    (json \ "assRef").as[AssemblyReference] shouldBe assemblyReference
    (json \ "meta").as[MetaInfo] shouldBe MetaInfo()
    (json \ "updates").asOpt[MetaInfo] shouldBe Option.empty[MetaInfo]
  }
  "RenderChangeMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(RenderChangeMessage(assemblyReference, Seq(), Seq(), Seq(), Seq(), Seq(), Seq()))
    (json \ "ref").as[AssemblyReference] shouldBe assemblyReference
    (json \ "files").as[Seq[GerberFile]] shouldBe Seq()
    (json \ "matched").as[Seq[GerberFile]] shouldBe Seq()
    (json \ "unmatched").as[Seq[GerberFile]] shouldBe Seq()
    (json \ "known").as[Seq[GerberFile]] shouldBe Seq()
    (json \ "unknown").as[Seq[GerberFile]] shouldBe Seq()
    (json \ "rendered").as[Seq[GerberFile]] shouldBe Seq()
    (json \ "failed").as[Seq[GerberFile]] shouldBe Seq()
  }
  "RenderAddedMessage" should "have a valid JSON representation" in {
    val json = Json.toJson(RenderAddedMessage(assemblyReference, gerberFile, Map()))
    (json \ "ref").as[AssemblyReference] shouldBe assemblyReference
    (json \ "file").as[GerberFile] shouldBe gerberFile
    (json \ "newRender").as[Map[String, FilePath]] shouldBe Map()
  }
}
