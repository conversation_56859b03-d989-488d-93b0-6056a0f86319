package de.fellows.ems.pcb.api

import de.fellows.ems.pcb.api.specification.units.AreaUnit.SquareMillimeter
import de.fellows.ems.pcb.api.specification.units.LengthUnit._
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.{
  BooleanSpecificationCapability,
  CountSpecificationCapability,
  EnumSpecificationCapability,
  LengthSpecificationCapability,
  NumericAreaSpecificationCapability,
  PCBV2AdvancedBoardCapabilities,
  PCBV2AdvancedBoardProperties,
  PCBV2BasicBoardCapabilities,
  PCBV2BasicBoardProperties,
  PCBV2LayerStackCapabilities,
  PCBV2LayerStackProperties,
  PCBV2MechanicalCapabilities,
  PCBV2MechanicalProperties,
  TemperatureSpecificationCapability
}
import de.fellows.ems.pcb.api.specification.units.TemperatureUnit._
import de.fellows.ems.pcb.api.specification.ULMarkingTypeEnum.NoMarking
import de.fellows.ems.pcb.api.specification.units.UnitConversions._
import de.fellows.ems.pcb.api.specification.{
  ApplicationType,
  BaseMaterial,
  Chamfering,
  CtiClass,
  FlammabilityRating,
  IPC600Class,
  LayerstackType,
  PressFitTechnology,
  Reports,
  Side,
  SilkscreenColor,
  SoldermaskColor,
  SurfaceFinish,
  ULMarkingTypeEnum,
  ViaFillingType
}
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{BeforeAndAfterAll, Inside}
import play.api.libs.json.Json

class PCBV2CapabilitySpec extends AnyWordSpec with Matchers with BeforeAndAfterAll with Inside {
  "Conversion" should {
    "work for PCBV2MechanicalCapabilities" in {
      PCBV2MechanicalCapabilities(
        minViaDiameter = LengthSpecificationCapability(default = Some(1), defaultUnit = Millimeter),
        viaFilling = EnumSpecificationCapability(
          (ViaFillingType.ALL),
          default = Some(ViaFillingType.PluggedSingleSided)
        ),
        blindVias = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        buriedVias = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        chamfering = EnumSpecificationCapability(
          (Chamfering.ALL),
          default = Some(Chamfering.None)
        ),
        aspectRatio = CountSpecificationCapability(default = Some(2)),
        totalDrillCount = CountSpecificationCapability(default = Some(3)),
        microVias = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        phCount = CountSpecificationCapability(default = Some(4)),
        phToolCount = CountSpecificationCapability(default = Some(5)),
        nphCount = CountSpecificationCapability(default = Some(6)),
        phMinSize = LengthSpecificationCapability(default = Some(7), defaultUnit = Millimeter),
        nphMaxSize = LengthSpecificationCapability(default = Some(8), defaultUnit = Millimeter),
        phMaxSize = LengthSpecificationCapability(default = Some(9), defaultUnit = Millimeter),
        nphToolCount = CountSpecificationCapability(default = Some(10)),
        phAnnularRing = LengthSpecificationCapability(default = Some(11), defaultUnit = Millimeter),
        nphMinSize = LengthSpecificationCapability(default = Some(12), defaultUnit = Millimeter),
        phSurfaceArea = NumericAreaSpecificationCapability(default = Some(13), defaultUnit = SquareMillimeter)
      ).defaultProperties should be(
        PCBV2MechanicalProperties(
          minViaDiameter = Some(1 millimeters),
          viaFilling = Some(ViaFillingType.PluggedSingleSided),
          blindVias = Some(true),
          blindViaCount = None,
          buriedVias = Some(true),
          buriedViaCount = None,
          chamfering = Some(Chamfering.None),
          outlineLength = None,
          aspectRatio = Some(2),
          totalDrillCount = Some(3),
          microVias = Some(true),
          phCount = Some(4),
          phToolCount = Some(5),
          nphCount = Some(6),
          phMinSize = Some(7 millimeters),
          nphMaxSize = Some(8 millimeters),
          phMaxSize = Some(9 millimeters),
          nphToolCount = Some(10),
          phAnnularRing = Some(11 millimeters),
          nphMinSize = Some(12 millimeters),
          phSurfaceArea = None
        )
      )
    }
    "work for PCBV2LayerStackCapabilities" in {
      PCBV2LayerStackCapabilities(
        layerstackType = EnumSpecificationCapability[LayerstackType](
          allowed = LayerstackType.ALL,
          default = Some(LayerstackType.Flex)
        ),
        layerstackThicknessTolerance = CountSpecificationCapability(default = Some(1)),
        ulLayerStack = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        ulMarkingType = EnumSpecificationCapability(
          allowed = ULMarkingTypeEnum.values,
          default = Some(ULMarkingTypeEnum.NoMarking)
        ),
        layercount = CountSpecificationCapability(default = Some(2)),
        finalThickness = LengthSpecificationCapability(default = Some(3), defaultUnit = Millimeter),
        baseMaterial = EnumSpecificationCapability[BaseMaterial](
          allowed = BaseMaterial.ALL,
          default = Some(BaseMaterial.CEM1)
        ),
        outerCopperThickness = LengthSpecificationCapability(default = Some(4), defaultUnit = Millimeter),
        innerCopperThickness = LengthSpecificationCapability(default = Some(5), defaultUnit = Millimeter),
        minOuterLayerStructure = LengthSpecificationCapability(default = Some(6), defaultUnit = Millimeter),
        minInnerLayerStructure = LengthSpecificationCapability(default = Some(7), defaultUnit = Millimeter),
        rohsCompilant = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        tgValue = TemperatureSpecificationCapability(default = Some(8), defaultUnit = Celsius),
        numberOfPrepregs = CountSpecificationCapability(default = Some(9)),
        numberOfLaminationCycles = CountSpecificationCapability(default = Some(10))
      ).defaultProperties should be(
        PCBV2LayerStackProperties(
          layerstackType = Some(LayerstackType.Flex),
          layerstackThicknessTolerance = Some(1),
          ulLayerStack = Some(true),
          ulMarkingType = Some(NoMarking),
          layercount = Some(2),
          finalThickness = Some(3 millimeters),
          baseMaterial = Some(BaseMaterial.CEM1),
          outerCopperThickness = Some(4 millimeters),
          innerCopperThickness = Some(5 millimeters),
          minOuterLayerStructure = Some(6 millimeters),
          minInnerLayerStructure = Some(7 millimeters),
          rohsCompilant = Some(true),
          tgValue = Some(8 celsius),
          numberOfPrepregs = None,
          numberOfLaminationCycles = None
        )
      )
    }
    "work for PCBV2AdvancedBoardCapabilities" in {
      PCBV2AdvancedBoardCapabilities(
        ipc600Class = EnumSpecificationCapability[IPC600Class](
          allowed = IPC600Class.values,
          default = Some(IPC600Class.IPC1)
        ),
        eTest = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        pressFit = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        pressFitTechnology = EnumSpecificationCapability[PressFitTechnology](
          allowed = PressFitTechnology.ALL,
          default = Some(PressFitTechnology.MassivePressFit)
        ),
        impedanceTested = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        impedanceTolerance = CountSpecificationCapability(default = Some(1)),
        peelableMask = EnumSpecificationCapability[Side](
          allowed = Side.ALL,
          default = Some(Side.Both)
        ),
        captonTape = EnumSpecificationCapability[Side](
          allowed = Side.ALL,
          default = Some(Side.Both)
        ),
        reports = EnumSpecificationCapability[Reports](
          allowed = Reports.ALL,
          default = Some(Reports.Impedance)
        ),
        itar = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        carbonPrint = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        carbonPrintSide = EnumSpecificationCapability[Side](
          allowed = Side.ALL,
          default = Some(Side.Both)
        ),
        edgeMetalization = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        flammabilityRating = EnumSpecificationCapability[FlammabilityRating](
          allowed = FlammabilityRating.ALL,
          default = Some(FlammabilityRating.V2)
        ),
        ctiClass = EnumSpecificationCapability[CtiClass](
          allowed = CtiClass.values,
          default = Some(CtiClass.I)
        ),
        maxXOutsAllowed = CountSpecificationCapability(default = Some(1)),
        halogenFree = BooleanSpecificationCapability(),
        placementSide = EnumSpecificationCapability[Side](
          allowed = Side.ALL,
          default = None
        ),
        ecobond = BooleanSpecificationCapability(),
        numberOfLines = CountSpecificationCapability(default = Some(4)),
        halfCutPlatedVias = BooleanSpecificationCapability()
      ).defaultProperties should be(
        PCBV2AdvancedBoardProperties(
          ipc600Class = Some(IPC600Class.IPC1),
          eTest = Some(true),
          pressFit = Some(true),
          pressFitTechnology = None,
          impedanceTested = Some(true),
          impedanceTolerance = None,
          peelableMask = Some(Side.Both),
          captonTape = Some(Side.Both),
          reports = Some(Seq(Reports.Impedance)),
          itar = Some(true),
          carbonPrint = Some(true),
          carbonPrintSide = Some(Side.Both),
          edgeMetalization = Some(true),
          flammabilityRating = Some(FlammabilityRating.V2),
          ctiClass = Some(CtiClass.I),
          maxXOutsAllowed = Some(1),
          halogenFree = Some(false),
          placementSide = None,
          ecobond = Some(false),
          numberOfLines = Some(4),
          halfCutPlatedVias = Some(false)
        )
      )
    }
    "work for PCBV2BasicBoardCapabilities" in {
      val properties = PCBV2BasicBoardCapabilities(
        boardHeight = LengthSpecificationCapability(default = Some(1), defaultUnit = Millimeter),
        boardWidth = LengthSpecificationCapability(default = Some(2), defaultUnit = Millimeter),
        silkscreenColor = EnumSpecificationCapability[SilkscreenColor](
          allowed = SilkscreenColor.ALL,
          default = Some(SilkscreenColor.Black)
        ),
        silkscreenSide = EnumSpecificationCapability[Side](
          allowed = Side.ALL,
          default = Some(Side.Both)
        ),
        surfaceFinish = EnumSpecificationCapability[SurfaceFinish](
          allowed = SurfaceFinish.ALL,
          default = Some(SurfaceFinish.It)
        ),
        enigThickness = LengthSpecificationCapability(
          allowed = Some(Seq(1, 2, 3)),
          default = Some(2),
          defaultUnit = Microinch
        ),
        applicationType = EnumSpecificationCapability[ApplicationType](
          allowed = ApplicationType.ALL,
          default = Some(ApplicationType.Aerospace)
        ),
        hardGold = BooleanSpecificationCapability(
          allowed = Seq(true, false),
          default = true
        ),
        hardGoldArea = NumericAreaSpecificationCapability(default = Some(3), defaultUnit = SquareMillimeter),
        hardGoldThickness = LengthSpecificationCapability(default = Some(3), defaultUnit = Micrometer),
        exposedCopperArea = NumericAreaSpecificationCapability(default = Some(4), defaultUnit = SquareMillimeter),
        exposedCopperAreaTop = NumericAreaSpecificationCapability(default = Some(5), defaultUnit = SquareMillimeter),
        exposedCopperAreaBottom = NumericAreaSpecificationCapability(default = Some(6), defaultUnit = SquareMillimeter),
        traceWidth = LengthSpecificationCapability(default = Some(7), defaultUnit = Millimeter),
        copperClearance = LengthSpecificationCapability(default = Some(8), defaultUnit = Millimeter),
        soldermaskColor = EnumSpecificationCapability[SoldermaskColor](
          allowed = SoldermaskColor.ALL,
          default = Some(SoldermaskColor.White)
        ),
        soldermaskSide = EnumSpecificationCapability[Side](
          allowed = Side.ALL,
          default = Some(Side.Both)
        ),
        soldermaskDam = LengthSpecificationCapability(default = Some(10), defaultUnit = Millimeter),
        soldermaskClearance = LengthSpecificationCapability(default = Some(11), defaultUnit = Millimeter)
      ).defaultProperties

      properties should be(PCBV2BasicBoardProperties(
        boardHeight = Some(1 millimeters),
        boardWidth = Some(2 millimeters),
        silkscreenColor = Some(SilkscreenColor.Black),
        silkscreenSide = Some(Side.Both),
        surfaceFinish = Some(SurfaceFinish.It),
        enigThickness = Some(2 microinch),
        applicationType = Some(ApplicationType.Aerospace),
        notes = None,
        hardGold = Some(true),
        hardGoldArea = Some(3 squareMillimeters),
        hardGoldThickness = Some(3 micrometers),
        exposedCopperArea = Some(4 squareMillimeters),
        exposedCopperAreaTop = Some(5 squareMillimeters),
        exposedCopperAreaBottom = Some(6 squareMillimeters),
        traceWidth = Some(7 millimeters),
        outerTraceWidth = None,
        innerTraceWidth = None,
        copperClearance = Some(8 millimeters),
        outerCopperClearance = None,
        innerCopperClearance = None,
        soldermaskColor = Some(SoldermaskColor.White),
        soldermaskSide = Some(Side.Both),
        pasteSide = None,
        soldermaskDam = Some(10 millimeters),
        soldermaskClearance = Some(11 millimeters)
      ))
    }
  }
}
