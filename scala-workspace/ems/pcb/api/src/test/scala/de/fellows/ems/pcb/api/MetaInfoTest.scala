package de.fellows.ems.pcb.api

import de.fellows.ems.pcb.api.specification.{IPC600Class, Side, ULMarkingTypeEnum}
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.matchers.should.Matchers
import de.fellows.utils.meta._
import play.api.libs.json._
import org.scalatestplus.scalacheck.ScalaCheckPropertyChecks
import org.scalacheck.{Arbitrary, Gen, Prop}
import org.scalatest.OptionValues

class MetaInfoTest extends AnyWordSpec with Matchers with ScalaCheckPropertyChecks with OptionValues {

  private val propertyNameGen: Gen[String]     = Gen.alphaLowerStr.suchThat(_.nonEmpty)
  private val stringValueGen: Gen[String]      = Arbitrary.arbitrary[String].suchThat(_.nonEmpty)
  private val decimalValueGen: Gen[BigDecimal] = Arbitrary.arbitrary[Double].map(BigDecimal(_))

  "MetaInfo" should {
    "handle empty MetaInfo correctly" in {
      val emptyMetaInfo = MetaInfo(Map.empty)

      val json   = Json.toJson(emptyMetaInfo).toString()
      val parsed = Json.parse(json).as[MetaInfo]

      parsed shouldEqual emptyMetaInfo

      emptyMetaInfo ? "nonexistent" shouldEqual false
    }

    "store and resolve string properties" in {
      forAll(propertyNameGen, stringValueGen) { (name, value) =>
        val metaInfo = MetaInfo(
          Map(
            PCBV2Layer.x(name, value).e
          )
        )

        val json   = Json.toJson(metaInfo).toString()
        val parsed = Json.parse(json).as[MetaInfo]

        parsed ? name shouldEqual true

        val property = parsed.get[StringProperty](name)
        property.value shouldEqual StringProperty(name, value)

        val resolvedValue: Option[String] = parsed \? name
        resolvedValue.value shouldEqual value

        val wat = parsed.get[DecimalProperty](name)
        wat.value shouldEqual StringProperty(name, value)
      }
    }

    "store and resolve decimal properties" in {
      forAll(propertyNameGen, decimalValueGen) { (name, value) =>
        val metaInfo = MetaInfo(
          Map(
            PCBV2Layer.x(name, value).e
          )
        )

        val json   = Json.toJson(metaInfo).toString()
        val parsed = Json.parse(json).as[MetaInfo]

        parsed ? name shouldEqual true

        val property = parsed.get[DecimalProperty](name)
        property.value shouldEqual DecimalProperty(name, value)

        val resolvedValue: Option[BigDecimal] = parsed \? name
        resolvedValue.value shouldEqual value
      }
    }

    "store and resolve boolean properties" in {
      forAll(propertyNameGen, Arbitrary.arbitrary[Boolean]) { (name, value) =>
        val metaInfo = MetaInfo(
          Map(
            PCBV2Layer.x(name, value).e
          )
        )

        val json   = Json.toJson(metaInfo).toString()
        val parsed = Json.parse(json).as[MetaInfo]

        parsed ? name shouldEqual true

        val property = parsed.get[BooleanProperty](name)
        property.value shouldEqual BooleanProperty(name, value)

        val resolvedValue: Option[Boolean] = parsed \? name
        resolvedValue.value shouldEqual value
      }
    }

    "store and resolve object properties" in {
      val name    = "obj-property"
      val jsonObj = Json.obj("field1" -> "value1", "field2" -> 42)

      val metaInfo = MetaInfo(
        Map(
          ObjectProperty.e(name, jsonObj)
        )
      )

      val json   = Json.toJson(metaInfo).toString()
      val parsed = Json.parse(json).as[MetaInfo]

      parsed ? name shouldEqual true

      val property = parsed.get[ObjectProperty](name)
      property.value shouldEqual ObjectProperty(name, jsonObj)

      val resolvedValue: Option[JsObject] = parsed \? name
      resolvedValue.value shouldEqual jsonObj
    }

    "handle case insensitivity correctly" in {
      val mixedCaseName = "MixedCaseName"
      val value         = "test-value"

      val metaInfo = MetaInfo(
        Map(
          StringProperty.e(mixedCaseName, value)
        )
      )

      // Test lowerCase method
      val lowerCaseMetaInfo = metaInfo.lowerCase
      lowerCaseMetaInfo ? mixedCaseName.toLowerCase shouldEqual true
      lowerCaseMetaInfo ? mixedCaseName shouldEqual false

      // Test case-insensitive property retrieval
      metaInfo.get[StringProperty](mixedCaseName.toLowerCase).value.value shouldEqual value
      metaInfo.get[StringProperty](mixedCaseName) shouldEqual None
    }

    "combine MetaInfo objects correctly" in {
      val name1     = "property1"
      val value1    = "value1"
      val newValue1 = "newValue1"
      val name2     = "property2"
      val value2    = BigDecimal(42)

      val metaInfo1 = MetaInfo(
        Map(
          StringProperty.e(name1, value1)
        )
      )
      val metaInfo2 = MetaInfo(
        Map(
          StringProperty.e(name1, newValue1),
          DecimalProperty.e(name2, value2)
        )
      )

      // Test updated method
      val combined1 = metaInfo1.updated(metaInfo2)

      combined1 ? name1 shouldEqual true
      combined1.get[StringProperty](name1).value.value shouldEqual newValue1

      combined1 ? name2 shouldEqual true
      combined1 \? name2 shouldEqual Some(value2)

      // Test ++ operator
      val combined2 = metaInfo1 ++ metaInfo2
      combined2 ? name1 shouldEqual true
      combined2.get[StringProperty](name1).value.value shouldEqual newValue1

      combined2 ? name2 shouldEqual true
      combined2 \? name2 shouldEqual Some(value2)

      // Test + operator
      val newValue2 = BigDecimal(420)
      val combined3 = metaInfo1 + DecimalProperty.e(name2, newValue2)

      combined3 ? name1 shouldEqual true
      combined3.get[StringProperty](name1).value.value shouldEqual value1

      combined3 ? name2 shouldEqual true
      combined3 \? name2 shouldEqual Some(newValue2)
    }

    "store and resolve PropertyWithLabel" in {
      val name       = "labeled-property"
      val label      = "My Label"
      val innerValue = "test-value"

      val metaInfo = MetaInfo(
        Map(
          name -> PropertyWithLabel(name, label, StringProperty("value", innerValue))
        )
      )

      val json   = Json.toJson(metaInfo).toString()
      val parsed = Json.parse(json).as[MetaInfo]

      parsed ? name shouldEqual true

      val property = parsed.get[PropertyWithLabel](name)
      property.value.label shouldEqual label
      property.value.value.getValue shouldEqual innerValue
    }

    "store and resolve PropertyWithUOM" in {
      val name       = "uom-property"
      val uom        = "mm"
      val innerValue = BigDecimal(42.5)

      val metaInfo = MetaInfo(
        Map(
          name -> PropertyWithUOM(name, uom, DecimalProperty("value", innerValue))
        )
      )

      val json   = Json.toJson(metaInfo).toString()
      val parsed = Json.parse(json).as[MetaInfo]

      parsed ? name shouldEqual true

      val property = parsed.get[PropertyWithUOM](name)
      property.value.uom shouldEqual uom
      property.value.value.getValue shouldEqual innerValue
    }

    "throw exception for non-existent properties with \\\\" in {
      val metaInfo = MetaInfo(Map.empty)

      val exception = intercept[IllegalArgumentException] {
        metaInfo \\ "nonexistent"
      }

      exception.getMessage should include("not found")
    }

    "store and resolve ULMarkingTypeEnum properties" in {
      val enumValues = Table(
        ("ULMarkingTypeEnum raw", "enum"),
        ("no-marking", ULMarkingTypeEnum.NoMarking),
        ("default-marking", ULMarkingTypeEnum.DefaultMarking),
        ("custom-marking", ULMarkingTypeEnum.CustomMarking)
      )
      val propertyName = "some-property-name"

      forAll(enumValues) { (value, enumEntry) =>
        val metaInfo = MetaInfo(
          Map(
            PCBV2Layer.x(propertyName, value).e
          )
        )

        val json   = Json.toJson(metaInfo).toString()
        val parsed = Json.parse(json).as[MetaInfo]

        parsed ? propertyName shouldEqual true

        val entry: Option[ULMarkingTypeEnum] = parsed.resolve(propertyName, ULMarkingTypeEnum.res)
        entry.value shouldEqual enumEntry

        val entry2: Option[ULMarkingTypeEnum] = parsed.resolve(propertyName)(ULMarkingTypeEnum.ULMarkingTypeFormat)
        entry2.value shouldEqual enumEntry
      }
    }

    "store and resolve IPC600Class properties" in {
      val enumValues = Table(
        ("ULMarkingTypeEnum raw", "enum"),
        ("", IPC600Class.IPCNone),
        ("1", IPC600Class.IPC1),
        ("2", IPC600Class.IPC2),
        ("2+", IPC600Class.IPC2Plus),
        ("3", IPC600Class.IPC3),
        ("3a", IPC600Class.IPC3a)
      )
      val propertyName = "some-property-name"

      forAll(enumValues) { (value, enumEntry) =>
        val metaInfo = MetaInfo(
          Map(
            PCBV2Layer.x(propertyName, value).e
          )
        )

        val json   = Json.toJson(metaInfo).toString()
        val parsed = Json.parse(json).as[MetaInfo]

        parsed ? propertyName shouldEqual true

        val entry: Option[IPC600Class] = parsed.resolve(propertyName, IPC600Class.res)
        entry.value shouldEqual enumEntry
      }

    }

    "return None on non-existent enum entries" in {
      val propertyName = "some-property-name"
      val metaInfo = MetaInfo(
        Map(
          PCBV2Layer.x(propertyName, "4").e
        )
      )

      metaInfo ? propertyName shouldEqual true

      val entry: Option[IPC600Class] = metaInfo.resolve(propertyName, IPC600Class.res)
      entry shouldEqual None
    }

    "store and resolve older enum entries" in {
      val enumValues = Table(
        ("Side raw", "enum"),
        ("none", Side.None),
        ("top", Side.Top),
        ("t", Side.Top),
        ("bottom", Side.Bottom),
        ("b", Side.Bottom),
        ("both", Side.Both)
      )
      val propertyName = "some-property-name"

      forAll(enumValues) { (value, enumEntry) =>
        val metaInfo = MetaInfo(
          Map(
            PCBV2Layer.x(propertyName, value).e
          )
        )

        val json   = Json.toJson(metaInfo).toString()
        val parsed = Json.parse(json).as[MetaInfo]

        parsed ? propertyName shouldEqual true

        val entry: Option[Side] = parsed.resolve(propertyName, Side.res)
        entry.value shouldEqual enumEntry

        val entry2: Option[Side] = parsed.resolve(propertyName)(Side.f)
        entry2.value shouldEqual enumEntry
      }
    }
  }

}
