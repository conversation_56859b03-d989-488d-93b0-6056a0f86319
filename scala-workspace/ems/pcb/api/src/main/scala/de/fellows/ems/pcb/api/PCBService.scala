// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.ems.pcb.api

import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{<PERSON>er<PERSON><PERSON>er, MessageProtocol, Method, RequestHeader, ResponseHeader}
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.app.assemby.api.AssemblyLifecycleStage
import de.fellows.ems.pcb.api.PCBApi.{CollectedPCB, PCBSpecificationCreation, PCBSpecificationUpdate, SetPreviews}
import de.fellows.ems.pcb.api.PCBService.{API_TAG_V1, API_TAG_V2}
import de.fellows.ems.pcb.api.PCBV2Api._
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.{PCBV2Update, _}
import de.fellows.ems.pcb.model._
import de.fellows.utils.SerializedCompressedFile
import de.fellows.utils.apidoc.{Internal, StackrateApi}
import de.fellows.utils.collaboration.TimelineEvent
import de.fellows.utils.communication.{BinaryMessageSerializer, ServiceExceptionSerializer}
import de.fellows.utils.internal.File
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta._
import de.fellows.utils.security.TokenResponse
import de.fellows.utils.service.StackrateServiceAPI
import de.fellows.utils.streams.StreamMessage
import io.swagger.v3.oas.annotations.extensions.{Extension, ExtensionProperty}
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.media.{Content, Schema}
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.{OpenAPIDefinition, Operation, Parameter}
import de.fellows.utils.communication.VersionedHeaderFilter
import java.util.UUID
import scala.annotation.meta.field

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate PCB API"
  )
)
@Tag(
  name = API_TAG_V1,
  extensions = Array {
    new Extension(
      name = "x-internal",
      properties = Array {
        new ExtensionProperty(
          name = "x-internal",
          value = "true"
        )
      }
    )
  }
)
trait PCBService extends Service with StackrateServiceAPI with StackrateLogging {
  val subPath  = "ems/pcb"
  val basePath = s"/api/$subPath"

  val internalBasePath         = s"/internal/$subPath/:team"
  val specificBasePath         = s"$basePath/:assembly/versions/:version"
  val specificSharedBasePath   = s"$basePath/shares/:share"
  val internalSpecificBasePath = s"$internalBasePath/:assembly/versions/:version"

  val basePathV2               = s"$basePath/v2"
  val specificBasePathV2       = s"$basePathV2/pcbs/:pcb"
  val specificSharedBasePathV2 = s"$basePathV2/shares/:share"

  val internalBasePathV2               = s"/internal/$subPath/v2/:team"
  val internalSpecificBasePathV2       = s"$internalBasePathV2/pcbs/:pcb"
  val internalSharedSpecificBasePathV2 = s"$internalBasePathV2/shares/:share"

  override def descriptor: Descriptor = {
    import Service._
    import de.fellows.ems.pcb.api.specification.units.UnitConversions._
    withDocumentation(subPath)({
      named("pcb")
        .withCalls(
          restCall(Method.GET, s"$specificBasePath", getPCB _),
          restCall(Method.GET, s"$specificBasePath/drills", getDrills _),
          restCall(Method.GET, s"$specificBasePath/collect", getFullPCB _),
          restCall(Method.GET, s"$internalSpecificBasePath", _getPCB _),
          restCall(Method.GET, s"$internalSpecificBasePath/version", _getPCBVersion _),
          restCall(Method.PUT, s"$internalSpecificBasePath/drillsets", _setDrillsSets _),
          restCall(Method.PUT, s"$internalSpecificBasePath/drills", _addDrills _),
          restCall(Method.POST, s"$internalSpecificBasePath/netlist", _setNetList _),
          restCall(Method.GET, s"$internalSpecificBasePath/drills", _getDrills _),
          restCall(Method.DELETE, s"$internalSpecificBasePath/drills", _removeDrills _),
          restCall(Method.PUT, s"$internalSpecificBasePath/uncreconcileddrills", _addUnreconciledDrills _),
          restCall(Method.PUT, s"$specificBasePath", updatePCB _),
          restCall(Method.POST, s"$specificBasePath/outline", setOutline _),
          restCall(Method.GET, s"$specificBasePath/outline?withGraphic", getOutline _),
          restCall(Method.GET, s"$internalSpecificBasePath/outline", _getOutline _),
          restCall(Method.GET, s"$specificSharedBasePathV2/outline?withGraphic", getSharedOutline _),
          restCall(Method.GET, s"$specificBasePath/outlines/:candidate?withGraphic", getOutlineCandidate _),
          restCall(
            Method.GET,
            s"$specificBasePath/outlines/specification/:specification",
            getOutlineCandidateBySpecification _
          ),
          restCall(Method.GET, s"$specificBasePath/outlines?withGraphic", getOutlineCandidates _),
          restCall(Method.GET, s"$specificSharedBasePathV2/outlines?withGraphic", getSharedOutlineCandidates _),
          restCall(
            Method.GET,
            s"$specificSharedBasePathV2/outlines/:candidate?withGraphic",
            getSharedOutlineCandidate _
          ),
          restCall(Method.GET, s"$specificBasePath/layers/:file", getLayer _),
          restCall(Method.PUT, s"$specificBasePath/layers/:file", updateLayer _),
          restCall(Method.GET, s"$specificBasePath/meta", getMeta _),
          restCall(Method.PUT, s"$specificBasePath/meta", setMeta _),
          restCall(Method.DELETE, s"$specificBasePath/meta", removeMeta _),
          restCall(Method.GET, s"$basePath/:assembly/meta", getLatestMeta _),
          restCall(Method.GET, s"$specificBasePath/updates?k", streamVersion _),
          restCall(Method.GET, s"$basePath/updates?k", streamAll _),
          restCall(Method.GET, s"$specificBasePath/specifications", getSpecifications _),
          restCall(Method.GET, s"$specificBasePath/specifications/previewupdates?k", streamPreviews _),
          restCall(Method.GET, s"$specificBasePath/specifications/search?alias", findSpecification _),
          restCall(Method.POST, s"$specificBasePath/specifications", createSpecification _),
          restCall(Method.GET, s"$specificBasePath/specifications/:specification", getSpecification _),
          restCall(Method.DELETE, s"$specificBasePath/specifications/:specification", deleteSpecification _),
          restCall(Method.PUT, s"$specificBasePath/specifications/:specification", changeSpecification _),
          restCall(Method.GET, s"$specificBasePath/specifications/:specification/print?template", printSpecification _)
            .withResponseSerializer(new BinaryMessageSerializer()),
          restCall(Method.PUT, s"$internalSpecificBasePath/meta?overwrite", _setMetaInfoProperty _),
          restCall(Method.DELETE, s"$internalSpecificBasePath/meta", _removeMetaInfoProperty _),
          restCall(Method.PUT, s"$internalSpecificBasePath/layers/:file/meta", _setFileMetaInfoProperty _),
          restCall(Method.DELETE, s"$internalSpecificBasePath/layers/:file/meta", _removeFileMetaInfoProperty _),
          restCall(Method.PUT, s"$specificBasePath/layers/:file/meta", setFileMetaInfoProperty _),
          restCall(Method.DELETE, s"$specificBasePath/layers/:file/meta", removeFileMetaInfoProperty _),
          restCall(Method.POST, s"$internalSpecificBasePath/layers", _addLayerFiles _),
          restCall(Method.GET, s"$internalSpecificBasePath/layers/:file", _getLayer _),
          restCall(Method.PUT, s"$internalSpecificBasePath/outline/specific", _setOutline _),
          restCall(Method.POST, s"$internalSpecificBasePath/outline/candidates", _setOutlineCandidates _),
          restCall(Method.PUT, s"$internalSpecificBasePath/outline/candidates", _addOutlineCandidates _),
          restCall(Method.POST, s"$internalSpecificBasePath/outline/select", _selectBestOutlineCandidate _),
          restCall(Method.PUT, s"$internalSpecificBasePath/specifications/:specification", _setSpecificationPreview _),
          restCall(Method.GET, s"$internalSpecificBasePath/specifications", _getSpecifications _),
          restCall(Method.GET, s"$internalSpecificBasePath/specifications/:specification", _getSpecification _),
          restCall(Method.GET, s"$internalSpecificBasePath/meta?specification", _getMergedMetaInfo _),
          restCall(Method.POST, s"$basePathV2/pcbs", createPCBV2 _),
          restCall(Method.GET, s"$basePathV2/pcbs?orderId", findPCBV2 _),
          restCall(Method.GET, s"$basePathV2/capabilities", getAllCapabilities _),
          restCall(Method.GET, s"$specificSharedBasePathV2", getSharedPCBV2 _),
          restCall(Method.GET, s"$specificSharedBasePathV2/specifications", getSharedPCBV2Specifications _),
          restCall(
            Method.GET,
            s"$specificSharedBasePathV2/specifications/:specification",
            getSharedPCBV2Specification _
          ),
          restCall(Method.GET, s"$specificSharedBasePathV2/capabilities", getCapabilitiesForShare _),
          restCall(Method.GET, s"$specificSharedBasePathV2/files/all?format", getSharedPCBV2Files _),
          restCall(Method.GET, s"$specificBasePathV2", getPCBV2 _),
          restCall(Method.GET, s"$specificBasePathV2/duplicates", getPCBV2Duplicates _),
          restCall(Method.POST, s"$specificBasePathV2/clone", clonePCBV2 _),
          restCall(Method.DELETE, s"$specificBasePathV2", deletePCBV2 _),
          restCall(Method.PUT, s"$specificBasePathV2", updatePCBV2 _),
          restCall(Method.GET, s"$specificBasePathV2/token", getWebSocketToken _),
          restCall(Method.GET, s"$specificBasePathV2/updates?k", streamVersionV2 _),
          restCall(Method.GET, s"$specificBasePathV2/files/all?format", getPCBV2Files _),
          restCall(Method.GET, s"$specificBasePathV2/files/singular/:file", getPCBV2File _),
          restCall(Method.GET, s"$specificBasePathV2/lifecycles", getPCBV2Lifecycles _),
          restCall(Method.GET, s"$specificBasePathV2/lifecycles/:lifecycle", getPCBV2Lifecycle _),
          restCall(Method.GET, s"$specificBasePathV2/specifications", getPCBV2Specifications _),
          restCall(Method.PUT, s"$specificBasePathV2/specifications", createPCBV2Specification _),
          restCall(Method.GET, s"$specificBasePathV2/specifications/:specification", getPCBV2Specification _),
          restCall(
            Method.GET,
            s"$specificBasePathV2/specifications/:specification/previews",
            getPCBV2SpecificationPreviews _
          ),
          restCall(
            Method.PUT,
            s"$specificBasePathV2/specifications/:specification/status",
            updatePCBV2SpecificationStatus _
          ),
          restCall(Method.PUT, s"$specificBasePathV2/specifications/:specification?removeUndefined", updatePCBV2Specification _),
          restCall(Method.DELETE, s"$specificBasePathV2/specifications/:specification", deletePCBV2Specification _),
          restCall(Method.GET, s"$internalBasePathV2/pcbs?orderId", _findPCBV2 _),
          restCall(Method.GET, s"$internalSharedSpecificBasePathV2", _getSharedPCBV2 _),
          restCall(Method.GET, s"$internalSharedSpecificBasePathV2/specifications", _getSharedPCBV2Specifications _),
          restCall(
            Method.GET,
            s"$internalSharedSpecificBasePathV2/specifications/:specification",
            _getSharedPCBV2Specification _
          ),
          restCall(Method.GET, s"$internalSpecificBasePathV2", _getPCBV2 _),
          restCall(Method.GET, s"$internalSpecificBasePathV2/lifecycles", _getPCBV2Lifecycles _),
          restCall(Method.GET, s"$internalSpecificBasePathV2/lifecycles/:lifecycle", _getPCBV2Lifecycle _),
          restCall(Method.GET, s"$internalSpecificBasePathV2/specifications", _getPCBV2Specifications _),
          restCall(Method.GET, s"$internalSpecificBasePathV2/specifications/:specification", _getPCBV2Specification _),
          restCall(Method.GET, s"$specificBasePathV2/capabilities", getCapabilities _),

          // shares
          restCall(Method.GET, s"$specificSharedBasePath/specifications/:specification", getSharedSpecification _),
          restCall(Method.GET, s"$specificSharedBasePath/specifications", getSharedSpecifications _),
          restCall(Method.GET, s"$specificSharedBasePath/pcb", getSharedPCB _),
          restCall(Method.GET, s"$specificSharedBasePath/pcb/meta", getSharedPCBMetaData _)
        )
        .withTopics(
          topic(PCBService.PCB_EVENTS, pcbEvents()),
          topic(PCBService.SPECIFICATION_TIMELINE, specificationTimeline()),
          topic(PCBService.SPECIFICATION_SAVED, specificationSavedTopic()),
          topic(PCBService.PCB_TIMELINE, pcbTimeline())
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"/files/$subPath/.*")),
          ServiceAcl(pathRegex = Some(s"$basePath/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer()).withHeaderFilter(new VersionedHeaderFilter())

    })
  }

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def setOutline(assembly: UUID, version: UUID): ServiceCall[SetOutline, Done]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getOutline(assembly: UUID, version: UUID, withGraphic: Option[Boolean]): ServiceCall[NotUsed, APIOutline]

  def _getOutline(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Outline]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSharedOutline(share: UUID, withGraphic: Option[Boolean]): ServiceCall[NotUsed, APIOutline]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getOutlineCandidate(
      assembly: UUID,
      version: UUID,
      candidate: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, APIOutline]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getOutlineCandidateBySpecification(
      assembly: UUID,
      version: UUID,
      specification: String
  ): ServiceCall[NotUsed, Graphic]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getOutlineCandidates(
      assembly: UUID,
      version: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[APIOutline]]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSharedOutlineCandidates(
      share: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, Seq[APIOutline]]
  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSharedOutlineCandidate(
      share: UUID,
      candidate: UUID,
      withGraphic: Option[Boolean]
  ): ServiceCall[NotUsed, APIOutline]

  def _setOutline(team: String, assembly: UUID, version: UUID): ServiceCall[SetOutline, Done]

  def _setOutlineCandidates(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[Outline], Done]

  def _addOutlineCandidates(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[Outline], Done]

  def _selectBestOutlineCandidate(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Outline]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getFullPCB(assembly: UUID, version: UUID): ServiceCall[NotUsed, CollectedPCB]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getPCB(assembly: UUID, version: UUID): ServiceCall[NotUsed, PCB]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSharedPCB(share: UUID): ServiceCall[NotUsed, PCB]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSharedPCBMetaData(share: UUID): ServiceCall[NotUsed, MetaInfo]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getDrills(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[HoleList]]

  def _getDrills(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[HoleList]]

  def _getPCB(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, PCB]

  def _getPCBVersion(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, PCBVersion]

  def _setDrillsSets(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[DrillSet], Done]

  def _addDrills(team: String, assembly: UUID, version: UUID): ServiceCall[InternalAddDrills, Done]
  def _setNetList(team: String, assembly: UUID, version: UUID): ServiceCall[SerializedCompressedFile[NetList], Done]
  def _removeDrills(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Done]

  def _addUnreconciledDrills(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[InternalAddUnreconciledDrills, Done]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def updatePCB(assembly: UUID, version: UUID): ServiceCall[PCBUpdate, PCB]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getLayer(assembly: UUID, version: UUID, file: UUID): ServiceCall[NotUsed, Map[String, Graphic]]

  def _getLayer(team: String, assembly: UUID, version: UUID, file: UUID): ServiceCall[NotUsed, Map[String, Graphic]]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def updateLayer(assembly: UUID, version: UUID, file: UUID): ServiceCall[LayerFileUpdate, LayerFile]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getMeta(assembly: UUID, version: UUID): ServiceCall[NotUsed, MetaInfo]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def removeMeta(assembly: UUID, version: UUID): ServiceCall[Seq[String], MetaInfo]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getLatestMeta(assembly: String): ServiceCall[NotUsed, MetaInfo]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def setMeta(assembly: UUID, version: UUID): ServiceCall[MetaInfo, MetaInfo]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def streamAll(k: String): ServiceCall[NotUsed, Source[PCBStreamMessage, NotUsed]]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def streamVersion(
      assembly: UUID,
      version: UUID,
      k: String
  ): ServiceCall[NotUsed, Source[PCBStreamMessage, NotUsed]]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSpecifications(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[PCBSpecificationApi]]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSharedSpecifications(share: UUID): ServiceCall[NotUsed, Seq[PCBSpecificationApi]]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSharedSpecification(share: UUID, specification: UUID): ServiceCall[NotUsed, PCBSpecificationApi]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def streamPreviews(
      assembly: UUID,
      version: UUID,
      k: String
  ): ServiceCall[NotUsed, Source[PCBStreamMessage, NotUsed]]

  def _getSpecifications(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[PCBSpecification]]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def createSpecification(assembly: UUID, version: UUID): ServiceCall[PCBSpecificationCreation, PCBSpecificationApi]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def getSpecification(assembly: UUID, version: UUID, specification: UUID): ServiceCall[NotUsed, PCBSpecificationApi]

  def _getSpecification(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[NotUsed, Seq[PCBSpecification]]

  def _getMergedMetaInfo(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: Option[UUID]
  ): ServiceCall[NotUsed, MetaInfo]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def findSpecification(assembly: UUID, version: UUID, alias: String): ServiceCall[NotUsed, PCBSpecificationApi]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def changeSpecification(
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[PCBSpecificationUpdate, PCBSpecificationApi]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  @Operation(
    responses = Array(
      new ApiResponse(
        responseCode = "200",
        description = "Specification was printed",
        content = Array(
          new Content(
            schema = new Schema(
              `type` = "string",
              format = "byte"
            )
          )
        )
      )
    )
  )
  def printSpecification(
      assembly: UUID,
      version: UUID,
      specification: UUID,
      template: Option[String]
  ): ServiceCall[NotUsed, Array[Byte]]

  def _setSpecificationPreview(
      team: String,
      assembly: UUID,
      version: UUID,
      specification: UUID
  ): ServiceCall[SetPreviews, PCBSpecificationApi]

  def _setMetaInfoProperty(
      team: String,
      assembly: UUID,
      version: UUID,
      overwrite: Option[Boolean] = None
  ): ServiceCall[Seq[Property], Done]

  def _removeMetaInfoProperty(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[String], Done]

  def _setFileMetaInfoProperty(
      team: String,
      assembly: UUID,
      version: UUID,
      file: UUID
  ): ServiceCall[Seq[Property], Done]

  def _removeFileMetaInfoProperty(
      team: String,
      assembly: UUID,
      version: UUID,
      file: UUID
  ): ServiceCall[Seq[String], Done]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def setFileMetaInfoProperty(assembly: UUID, version: UUID, file: UUID): ServiceCall[MetaInfo, MetaInfo]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def removeFileMetaInfoProperty(assembly: UUID, version: UUID, file: UUID): ServiceCall[Seq[String], MetaInfo]

  def _addLayerFiles(team: String, assembly: UUID, version: UUID): ServiceCall[Seq[File], Seq[GerberFile]]

  //  def _setAutoOutline(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = API_TAG_V1)
  @Internal
  def deleteSpecification(assembly: UUID, version: UUID, specification: UUID): ServiceCall[NotUsed, Done]

  //  def _updateLayerProperties(owner: UUID, assembly: UUID, version: UUID, file: String): ServiceCall[UpdateLayerProperties, Done]

  def pcbEvents(): Topic[StreamMessage[PCBStreamEvent]]

  //
  //
  def specificationSavedTopic(): Topic[SpecificationSavedMessage]

  //
  //
  def specificationTimeline(): Topic[TimelineEvent]

  //  @deprecated
  //  def renderChangeTopic(): Topic[RenderChangeMessage]
  //
  def pcbTimeline(): Topic[TimelineEvent]

  /** V2
    */

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Create a new PCB",
    description =
      """
This endpoint facilitates the creation of a new PCB project.

The request payload does not take any files, but they can be
once the record is created.

It is possible to use the API without providing any project
files. This means analysis and value extraction will not be
available.

If this PCB belongs to a RfQ and should be linked together, then
the `externalReference` and `orderId` fields should be filled
and the RfQ should be updated with the PCB's UUID.

The creation of a PCB involves the creation of a specification
that can be used to override the default values of the PCB.
""",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was created")
    )
  )
  def createPCBV2(): ServiceCall[PCBV2Creation, PCBV2]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Clone an existing PCB",
    description =
      """
Clone an existing PCB.

This clones the PCB, analysis results, specifications and
files associated.

This operation returns an error if the PCB's analysis is
in progress.
""",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was cloned"),
      new ApiResponse(responseCode = "404", description = "PCB to clone was not found")
    )
  )
  def clonePCBV2(pcb: UUID): ServiceCall[PCBV2Clone, PCBV2Id]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Find a PCB using an order ID",
    description = "Find a PCB using the orderID that was specified during its creation.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was found"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def findPCBV2(orderId: String): ServiceCall[NotUsed, Seq[PCBV2]]

  def _findPCBV2(team: String, orderId: String): ServiceCall[NotUsed, Seq[PCBV2]]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get All Capabilities",
    description =
      """
Returns all the available PCB properties and the values available
for each of them, as well as the default value.

Some capabilities depend on the values of other capabilities,
which means the list of available values is not complete unless
the pcb-specific endpoint is used.
""",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "List of PCB capabilities")
    )
  )
  def getAllCapabilities: ServiceCall[NotUsed, PCBV2SpecificationCapabilities]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get All Capabilities respecting the currently set values of the specification",
    description =
      """
Returns all the available PCB properties and the values available
for each of them, as well as the default value.

Some capabilities depend on the values of other capabilities,
which means the values for certain capabilities change based on
the PCB specification.

An example of this behaviour is the inner copper thickness, which
requires the PCB to have at least 4 layers.
""",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "List of PCB capabilities")
    )
  )
  def getCapabilities(pcb: UUID): ServiceCall[NotUsed, PCBV2SpecificationCapabilities]

  def getCapabilitiesForShare(share: UUID): ServiceCall[NotUsed, PCBV2SpecificationCapabilities]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Update a PCB",
    description = "Updates some of the PCB properties that were set during creation.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was updated"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def updatePCBV2(pcb: UUID): ServiceCall[PCBV2Api.PCBV2Update, PCBV2]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get a PCB",
    description = "Find a PCB by its UUID.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was found"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def getPCBV2(pcb: UUID): ServiceCall[NotUsed, PCBV2]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Internal
  @Operation(
    summary = "Get a PCB",
    description = "Find a PCB by its UUID.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was found"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def getPCBV2Duplicates(version: UUID): ServiceCall[NotUsed, Seq[DuplicatePcb]]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get a Shared PCB",
    description = "Find a Shared PCB by its UUID.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was found"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def getSharedPCBV2(share: UUID): ServiceCall[NotUsed, PCBV2]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Delete a PCB",
    description = "Find and delete a PCB by its UUID.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB was deleted"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def deletePCBV2(pcb: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get the PCB Files as zip",
    description = "Downloads PCB files as a zip folder",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB project zip"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def getPCBV2Files(pcb: UUID): ServiceCall[NotUsed, NotUsed]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get the Shared PCB Files as zip",
    description = "Downloads PCB files of a shared PCB as a zip folder",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB project zip"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def getSharedPCBV2Files(share: UUID): ServiceCall[NotUsed, NotUsed]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get a PCB File",
    description = "Downloads PCB file by name, if it exists.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB project zip"),
      new ApiResponse(responseCode = "404", description = "PCB or file was not found")
    )
  )
  def getPCBV2File(pcb: UUID, file: String): ServiceCall[NotUsed, NotUsed]

  def _getPCBV2(team: String, pcb: UUID): ServiceCall[NotUsed, PCBV2]
  def _getSharedPCBV2(team: String, share: UUID): ServiceCall[NotUsed, PCBV2]

  @StackrateApi
  @Tag(name = API_TAG_V2)
  @Internal
  @Operation(
    summary = "Get an auth token to subscribe to the PCB websocket",
    description = "Get a PCB-specific short-lived token that allows subscribing to any changes made.",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB-specific token"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def getWebSocketToken(pcb: UUID): ServiceCall[NotUsed, TokenResponse]

  @StackrateApi
  @Tag(name = API_TAG_V2)
  @Internal
  @Operation(
    summary = "Subscribe to the PCB updates websocket.",
    description =
      """
Subscribe to a stream PCB changes.

This endpoint requires a token from `getWebSocketToken` as the `k` param.
"""
  )
  def streamVersionV2(pcb: UUID, k: Option[String]): ServiceCall[NotUsed, Source[PCBV2StreamMessage, NotUsed]]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Internal
  @Operation(
    summary = "Get a PCB Lifecycle",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB lifecycle")
    )
  )
  def getPCBV2Lifecycle(pcb: UUID, lifecycle: String): ServiceCall[NotUsed, AssemblyLifecycleStage]

  def _getPCBV2Lifecycle(team: String, pcb: UUID, lifecycle: String): ServiceCall[NotUsed, AssemblyLifecycleStage]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Internal
  @Operation(
    summary = "Get All PCB Lifecycles",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB lifecycles")
    )
  )
  def getPCBV2Lifecycles(pcb: UUID): ServiceCall[NotUsed, Seq[AssemblyLifecycleStage]]

  def _getPCBV2Lifecycles(team: String, pcb: UUID): ServiceCall[NotUsed, Seq[AssemblyLifecycleStage]]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get a PCB Specification",
    description = "Find PCB specification by name or UUID",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB specification"),
      new ApiResponse(responseCode = "404", description = "PCB or specification was not found")
    )
  )
  def getPCBV2Specification(
      pcb: UUID,
      @(Parameter @field)(description = "the name or uuid of the specification")
      specification: String
  ): ServiceCall[NotUsed, PCBV2Specification]

  def getSharedPCBV2Specification(
      share: UUID,
      @(Parameter @field)(description = "the name or uuid of the specification")
      specification: String
  ): ServiceCall[NotUsed, PCBV2Specification]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get a PCB Specification Preview",
    description = "Find PCB specification by name or UUID",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB specification previews"),
      new ApiResponse(responseCode = "404", description = "PCB or specification was not found")
    )
  )
  def getPCBV2SpecificationPreviews(
      pcb: UUID,
      @(Parameter @field)(description = "the name or uuid of the specification")
      specification: String
  ): ServiceCall[NotUsed, PCBPreviews]

  def _getPCBV2Specification(
      team: String,
      pcb: UUID,
      specification: String
  ): ServiceCall[NotUsed, PCBV2Specification]

  def _getSharedPCBV2Specification(
      team: String,
      share: UUID,
      specification: String
  ): ServiceCall[NotUsed, PCBV2Specification]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Get all PCB Specifications",
    description = "Get all PCB specificationss",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB specifications"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def getPCBV2Specifications(pcb: UUID): ServiceCall[NotUsed, Seq[PCBV2Specification]]
  def getSharedPCBV2Specifications(share: UUID): ServiceCall[NotUsed, Seq[PCBV2Specification]]

  def _getPCBV2Specifications(team: String, pcb: UUID): ServiceCall[NotUsed, Seq[PCBV2Specification]]
  def _getSharedPCBV2Specifications(team: String, share: UUID): ServiceCall[NotUsed, Seq[PCBV2Specification]]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Create a new PCB Specification",
    description = "Create anew PCB specifications",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB specification created"),
      new ApiResponse(responseCode = "404", description = "PCB was not found")
    )
  )
  def createPCBV2Specification(pcb: UUID): ServiceCall[PCBV2Specification, PCBV2Specification]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Sets a PCB Specification to editing mode",
    description = "Sets a PCB Specification to editing mode",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB specification in edit mode"),
      new ApiResponse(responseCode = "404", description = "PCB or specification was not found")
    )
  )
  def updatePCBV2SpecificationStatus(
      pcb: UUID,
      specification: UUID
  ): ServiceCall[PCBV2SpecificationStatusUpdateRequest, PCBV2Specification]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Update a PCB Specification",
    description = "Update a PCB Specifications",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB specification updated"),
      new ApiResponse(responseCode = "404", description = "PCB or specification was not found")
    )
  )
  def updatePCBV2Specification(pcb: UUID, specification: UUID, removeUndefined: Option[Boolean]): ServiceCall[PCBV2Update, PCBV2Specification]

  @StackrateApi()
  @Tag(name = API_TAG_V2)
  @Operation(
    summary = "Delete a PCB Specification",
    description = "Delete a PCB Specifications",
    responses = Array(
      new ApiResponse(responseCode = "200", description = "PCB specification deleted"),
      new ApiResponse(responseCode = "404", description = "PCB or specification was not found")
    )
  )
  def deletePCBV2Specification(pcb: UUID, specification: UUID): ServiceCall[NotUsed, Done]
}

object PCBService {

  val VERSION = "v1.5"

  val PCB_TIMELINE = s"domain.ems.pcb.timeline-$VERSION"
  val PCB_EVENTS   = s"domain.ems.pcb.events-$VERSION"

  val FILE_TYPE = s"domain.ems.pcb.type-$VERSION"

  val FILE_SET = s"domain.ems.pcb.fileset-$VERSION"

  val FILE_TYPES_CHANGED = s"domain.ems.pcb.filetypeschanged-$VERSION"

  val OUTLINE_SET = s"domain.ems.pcb.outlineset-$VERSION"

  val METAINFO = s"domain.ems.pcb.metainfo-$VERSION"

  val DFM = s"domain.ems.pcb.dfm-$VERSION"

  val PCB = s"domain.ems.pcb.updates-$VERSION"

  val SPECIFICATION = s"domain.ems.pcb.specification-$VERSION"

  val SPECIFICATION_TIMELINE = s"domain.ems.pcb.specification.timeline-$VERSION"

  val SPECIFICATION_SAVED = s"domain.ems.pcb.specification.saved-$VERSION"

  val RENDERCHANGE = s"domain.ems.pcb.render.changed-$VERSION"

  val RENDERADDED = s"domain.ems.pcb.render.added-$VERSION"

  val PREVIEW = s"domain.ems.pcb.render.preview-$VERSION"

  final val API_TAG_V2 = "PCB"
  final val API_TAG_V1 = "PCB V1"

}
