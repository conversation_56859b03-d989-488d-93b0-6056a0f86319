package de.fellows.ems.pcb.api

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{Assembly, File}
import de.fellows.ems.pcb.api.PCBV2Api.{PCBV2, PCBV2File}
import de.fellows.ems.pcb.api.specification.units.AreaUnit.SquareMillimeter
import de.fellows.ems.pcb.api.specification.Constants.{
  DEFAULT_COPPER_THICKNESS,
  DEFAULT_FINAL_THICKNESS,
  DEFAULT_FLEX_FINAL_THICKNESS
}
import de.fellows.ems.pcb.api.specification.units.LengthUnit.{Microinch, Micrometer, Millimeter}
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi._
import de.fellows.ems.pcb.api.specification.units.TemperatureUnit.Celsius
import de.fellows.ems.pcb.api.specification._
import de.fellows.ems.pcb.api.specification.units.{
  AreaUnit,
  AreaWithUnit,
  LengthUnit,
  LengthWithUnit,
  TemperatureUnit,
  TemperatureWithUnit
}
import de.fellows.ems.pcb.model.DFM.Properties.{DFM, Settings}
import de.fellows.ems.pcb.model._
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.meta.{MetaInfo, ObjectProperty, Property}
import de.fellows.utils.security.{FileSecurityClaim, FileTokenContent, JwtTokenUtil}
import de.fellows.utils.{FileAccess, FilePath, UUIDUtils}
import play.api.libs.json.{Format, _}

import java.util.UUID

object PCBV2Layer {
  def hashString(spec: PCBV2Properties): String = {
    val raw = Seq(
      spec.layerStack.layercount.getOrElse(""),
      spec.layerStack.baseMaterial.map(_.value).getOrElse(""),
      spec.board.basic.boardWidth.map(_.to(Millimeter)).getOrElse(""),
      spec.board.basic.boardHeight.map(_.to(Millimeter)).getOrElse(""),
      spec.layerStack.finalThickness.map(_.to(Millimeter)).getOrElse(""),
      spec.board.basic.soldermaskColor.map(_.value).getOrElse(""),
      spec.board.basic.silkscreenColor.map(_.value).getOrElse(""),
      spec.mechanical.buriedVias.getOrElse(""),
      spec.mechanical.blindVias.getOrElse(""),
      spec.layerStack.minOuterLayerStructure.map(_.to(Millimeter)).getOrElse(""),
      spec.layerStack.outerCopperThickness.map(_.to(Micrometer)).getOrElse(""),
      spec.mechanical.minViaDiameter.map(_.to(Millimeter)).getOrElse(""),
      spec.board.basic.surfaceFinish.map(_.value).getOrElse(""),
      spec.board.basic.soldermaskSide.map(_.getClass.getSimpleName.replace("$", "")).getOrElse(""),
      spec.board.advanced.eTest.getOrElse(""),
      spec.mechanical.chamfering.map(_.value).getOrElse(""),
      spec.board.advanced.edgeMetalization.getOrElse(""),
      spec.board.basic.hardGold.getOrElse(""),
      spec.layerStack.ulLayerStack.getOrElse(""),
      spec.board.basic.enigThickness.map(_.to(Microinch)).getOrElse(""),
      spec.layerStack.tgValue.map(_.to(Celsius)).getOrElse(""),
      spec.board.advanced.halogenFree.getOrElse(""),
      spec.board.advanced.maxXOutsAllowed.getOrElse(""),
      spec.layerStack.layerstackType.map(_.value).getOrElse("")
    ).mkString(":")

    UUIDUtils.encoder.encodeToString(raw.getBytes)
  }

  def to(ass: Assembly, pcb: PCBVersion, specifications: Seq[PCBSpecification]): PCBV2 = {
    implicit val sd: ServiceDefinition = ServiceDefinition("pcb")

    val assemblyId     = ass.id
    val pcbId          = pcb.assembly.map(_.version).get
    val currentVersion = ass.currentVersion.get

    PCBV2(
      id = pcbId,
      assembly = assemblyId,
      name = Some(ass.name),
      description = ass.information.description,
      created = currentVersion.created,
      files = currentVersion.files.map(_.map(to(ass.team, assemblyId, pcbId, _))),
      filesLocked = currentVersion.filesLocked,
      lifecycles = currentVersion.lifecycles,
      projectType = currentVersion.projectType,
      orderId = ass.information.orderId,
      outline = pcb.outline.flatMap(to(_, pcb.files, currentVersion.files.getOrElse(Seq()))).map(_.name),
      specifications = pcb.specifications.flatMap(to(specifications)),
      properties = pcb.meta.map(to).getOrElse(PCBV2Properties.EMPTY),
      customer = ass.information.customer
    )
  }

  def to(file: File): PCBV2File =
    PCBV2File(
      id = file.id,
      name = file.name,
      fileType = file.fType,
      preview = None,
      path = None,
      lifecycles = file.lifecycles
    )

  def to(file: de.fellows.utils.internal.File): PCBV2File =
    PCBV2File(
      id = file.id,
      name = file.name,
      fileType = file.fType,
      preview = None,
      path = None,
      lifecycles = Seq.empty
    )

  def to(
      team: String,
      assemblyId: UUID,
      pcbId: UUID,
      f: File
  )(implicit
      sd: ServiceDefinition
  ): PCBV2File = {
    // unknown file type can have it's initial letter uppercase or not
    val patchedFileType =
      if (f.fType.fileType == LayerConstants.UNKNOWN)
        LayerConstants.UNKNOWN.toLowerCase
      else
        f.fType.fileType
    val fileType = f.fType.copy(fileType = patchedFileType)

    val (preview, path) =
      if (
        f.fType.mimeType == LayerConstants.Mime.gerber || f.fType.mimeType.exists(
          LayerConstants.Mime.isOdb
        ) || f.fType.mimeType == LayerConstants.Mime.drill
      ) {
        (f.preview.map(preview => s"/files/assembly/$assemblyId/versions/$pcbId/$preview"), None)
      } else {
        val subPathed = (f.subPath.getOrElse(Seq()) :+ f.name).mkString("/")
        val resource  = s"$assemblyId/$pcbId"
        val path      = s"upload/${subPathed}"
        val url       = s"/files/assembly/$assemblyId/versions/$pcbId/$path"

        val claim = FileSecurityClaim(sd.name, resource, path)
        val key   = JwtTokenUtil.generateFileToken(FileTokenContent(team, Seq(claim))).authToken

        (None, Some(s"$url?k=$key"))
      }

    PCBV2File(
      id = f.id,
      name = f.name,
      fileType = fileType,
      preview = preview,
      path = path,
      lifecycles = f.lifecycles
    )
  }

  def to(f: Outline, files: Seq[GerberFile], filess: Seq[File]): Option[PCBV2File] =
    files
      .find(file => f.file.contains(file.id))
      .flatMap(_f => filess.find(_.name == _f.name))
      .map(to)

  def to(specifications: Seq[PCBSpecification])(spec: UUID): Option[PCBV2Specification] =
    specifications.find(_.id == spec).map(to)

  def to(spec: PCBSpecification): PCBV2Specification = {
    val s = toSettings(spec)
    PCBV2Specification(
      id = spec.id,
      name = spec.alias,
      previews = to(spec.assembly.id, spec.assembly.version, spec.id, spec.preview, spec.previewRear),
      settings = s,
      hash = hashString(s),
      status = Some(spec.status),
      changes = spec.changes
    )
  }

  private def resolvelist[X](meta: MetaInfo, name: String)(implicit f: Format[X]): Option[Seq[X]] =
    meta.get[ObjectProperty](name).flatMap { op =>
      (op.value \ "val") match {
        case JsDefined(value) => Some(value.as[Seq[X]])
        case _: JsUndefined   => None
      }
    }

  def toInt[T](value: T): Int =
    value match {
      case x: Int        => x
      case x: BigDecimal => x.intValue
      case x: BigInt     => x.intValue
      case x: Number     => x.intValue
    }

  def to(meta: MetaInfo): PCBV2Properties = {
    val phMinSize: Option[LengthWithUnit]  = withUnit(meta \? "phminsize" orElse meta \? DFM.PH_MIN_SIZE, Millimeter)
    val nphMinSize: Option[LengthWithUnit] = withUnit(meta \? "nphminsize" orElse meta \? DFM.NPH_MIN_SIZE, Millimeter)
    val minViaDiameter                     = withUnit(meta \? DFM.VIA_MIN_SIZE, Millimeter) orElse phMinSize

    val carbonPrint: Option[Boolean] = meta \? "carbonprint"
    val carbonPrintSide: Option[Side] =
      meta.resolve("carbon_print_side")(Side.f).orElse(meta.resolve("carbon_print_sides")(Side.f))
    val modifiedCarbonPrintSide = (carbonPrintSide, carbonPrint) match {
      case (None, Some(print)) => if (print) Some(Side.Both) else Some(Side.None)
      case (sides, _)          => sides
    }

    PCBV2Properties(
      board = PCBV2BoardProperties(
        basic = pcbBasicProperties(meta),
        advanced = PCBV2AdvancedBoardProperties(
          ipc600Class = Seq(Settings.IPC_600_CLASS, "ipc600class").flatMap(meta.resolve(_, IPC600Class.res)).headOption,
          eTest = meta \? "etest" orElse meta \? Settings.ETEST,
          pressFit = meta \? "pressfit" orElse meta \? Settings.PRESS_FIT,
          pressFitTechnology = meta.resolve(Settings.PRESS_FIT_TECHNOLOGY, PressFitTechnology.res),
          impedanceTested = meta \? "impedancetested" orElse meta \? Settings.IMPEDANCE_CONTROLLED,
          impedanceTolerance = meta \? Settings.IMPEDANCE_TOLERANCE,
          peelableMask = meta.resolve("peelablemask")(Side.f),
          captonTape = meta.resolve("captontape")(Side.f),
          reports = resolvelist(meta, "reports")(Reports.f), // meta.resolveList("reports", Reports.res),
          itar = meta \? "itar" orElse meta \? Settings.ITAR,
          carbonPrint = carbonPrint,
          carbonPrintSide = modifiedCarbonPrintSide,
          edgeMetalization = meta \? "edgemetalization" orElse meta \? Settings.EDGE_PLATING,
          flammabilityRating = meta.resolve("flammabilityrating")(FlammabilityRating.f),
          ctiClass = meta.resolve("cticlass")(implicitly[Format[CtiClass]]).orElse(Some(CtiClass.None)),
          maxXOutsAllowed = meta \? "maxxoutsallowed",
          halogenFree = meta \? Settings.HALOGEN_FREE,
          placementSide = meta.resolve(Settings.PLACEMENT_SIDES)(Side.f),
          ecobond = meta \? Settings.ECOBOND,
          numberOfLines = meta \? Settings.NUMBER_OF_LINES,
          halfCutPlatedVias = meta \? Settings.HALF_CUT_PLATED_VIAS orElse meta \? Settings.HALF_CUTTED_PLATED_VIAS
        )
      ),
      layerStack = PCBV2LayerStackProperties(
        layerstackType = meta.resolve("layerstacktype")(LayerstackType.f),
        layerstackThicknessTolerance = meta \? "layerstackthicknesstolerance",
        ulLayerStack = meta \? "ullayerstack" orElse meta \? Settings.UL_MATERIAL,
        ulMarkingType = meta.resolve("ulmarkingtype")(ULMarkingTypeEnum.ULMarkingTypeFormat),
        layercount = meta \? "layercount",
        finalThickness = withUnit(meta \? "finalthickness", Millimeter), // orElse meta \? "pressedthickness",
        baseMaterial = meta.resolve("basematerial")(BaseMaterial.f),
        outerCopperThickness =
          withUnit(meta \? "outercopperthickness" orElse meta \? DFM.OUTER_COPPER_HEIGHT, Micrometer),
        innerCopperThickness =
          withUnit(meta \? "innercopperthickness" orElse meta \? DFM.INNER_COPPER_HEIGHT, Micrometer),
        minOuterLayerStructure =
          withUnit(meta \? "minouterlayerstructure" orElse meta \? DFM.MIN_OUTER_STRUCTURE, Millimeter),
        minInnerLayerStructure =
          withUnit(meta \? "mininnerlayerstructure" orElse meta \? DFM.MIN_INNER_STRUCTURE, Millimeter),
        rohsCompilant = meta \? "rohscompilant" orElse meta \? Settings.ROHS,
        tgValue = withTemperatureUnit(meta \? "tgvalue" orElse meta \? "materialtg", Celsius),
        numberOfPrepregs = meta \? Settings.NUMBER_OF_PREPREGS,
        numberOfLaminationCycles = meta \? Settings.NUMBER_OF_LAMINATION_CYCLES
      ),
      mechanical = PCBV2MechanicalProperties(
        minViaDiameter = minViaDiameter,
        viaFilling = Seq(Settings.VIA_PROTECTION, "viafilling").flatMap(meta.resolve(_, ViaFillingType.res)).headOption,
        blindVias = meta \? DFM.BLIND_VIAS,
        buriedVias = meta \? DFM.BURIED_VIAS,
        blindViaCount = (meta \? DFM.BLIND_VIA_COUNT).map(toInt),
        buriedViaCount = (meta \? DFM.BURIED_VIA_COUNT).map(toInt),
        chamfering = meta.resolve("chamfering")(Chamfering.f),
        outlineLength = withUnit(meta \? DFM.OUTLINE_LENGTH, Millimeter),
        aspectRatio = meta \? "aspectratio",
        totalDrillCount = meta \? "totaldrillcount" orElse meta \? DFM.TOTAL_DRILL_COUNT,
        microVias = meta \? "microvias",
        phCount = meta \? "phcount" orElse meta \? DFM.PH_COUNT,
        phToolCount = meta \? "phtoolcount" orElse meta \? DFM.PH_TOOL_COUNT,
        nphCount = meta \? "nphcount" orElse meta \? DFM.NPH_COUNT,
        phMinSize = phMinSize,
        nphMaxSize = withUnit(meta \? "nphmaxsize" orElse meta \? DFM.NPH_MAX_SIZE, Millimeter),
        phMaxSize = withUnit(meta \? "phmaxsize" orElse meta \? DFM.PH_MAX_SIZE, Millimeter),
        nphToolCount = meta \? "nphtoolcount" orElse meta \? DFM.NPH_TOOL_COUNT,
        phAnnularRing = withUnit(meta \? DFM.PH_ANNULAR_RING, Millimeter),
        nphMinSize = nphMinSize,
        phSurfaceArea = meta \? DFM.PH_SURFACE_AREA
      ),
      reports = PCBV2ReportsProperties(
        crossSection = meta \? DFM.CROSS_SECTION_REPORT,
        xRayMeasurement = meta \? DFM.XRAY_MEASUREMENT_REPORT,
        xRayMeasurementPoints = meta \? DFM.XRAY_MEASUREMENT_POINTS_REPORT,
        firstArticleInspection = meta \? DFM.FIRST_ARTICLE_INSPECTION_REPORT,
        certificateOfConformance = meta \? DFM.CERTIFICATE_OF_CONFORMANCE_REPORT
      ),
      markings = PCBV2MarkingsProperties(
        dateCode = meta.resolve(DFM.DATE_CODE_MARKING, DateCodeType.res),
        fabricatorLogo = meta \? DFM.FABRICATOR_LOGO_MARKING,
        datamatrixCode = meta \? DFM.DATAMATRIX_CODE_MARKING
      ),
      miscellaneous = PCBV2MiscellaneousProperties(
        manufacturingInformation = meta \? DFM.MANUFACTURING_INFORMATION
      )
    )
  }

  def withUnit(d: Option[BigDecimal], unit: LengthUnit) =
    d.map(LengthWithUnit(_, unit))
  def withAreaUnit(d: Option[BigDecimal], unit: AreaUnit) =
    d.map(AreaWithUnit(_, unit))
  def withTemperatureUnit(d: Option[BigDecimal], unit: TemperatureUnit) =
    d.map(TemperatureWithUnit(_, unit))

  def pcbBasicProperties(meta: MetaInfo): PCBV2BasicBoardProperties =
    PCBV2BasicBoardProperties(
      boardHeight = withUnit((meta \? "boardheight" orElse meta \? DFM.HEIGHT), LengthUnit.Millimeter),
      boardWidth = withUnit(meta \? "boardwidth" orElse meta \? DFM.WIDTH, LengthUnit.Millimeter),
      silkscreenColor =
        Seq(Settings.SILKSCREEN_COLOR, "silkscreencolor").flatMap(meta.resolve(_, SilkscreenColor.res)).headOption,
      silkscreenSide =
        Seq(Settings.SILKSCREEN_SIDES, "silkscreenside").flatMap(meta.resolve(_, Side.res)).headOption,
      surfaceFinish = Seq(Settings.FINISH, "surfacefinish").flatMap(meta.resolve(_, SurfaceFinish.res)).headOption,
      enigThickness = withUnit(meta \? Settings.ENIG_THICKNESS, LengthUnit.Microinch),
      applicationType = meta.resolve("applicationtype")(ApplicationType.f),
      notes = meta \? "notes",
      hardGold = meta \? Settings.HARD_GOLD,
      hardGoldArea =
        withAreaUnit(meta \? "hardgoldarea" orElse meta \? Settings.HARD_GOLD_AREA, AreaUnit.SquareMillimeter),
      hardGoldThickness = withUnit(meta \? "hardgoldthickness" orElse meta \? Settings.HARD_GOLD_THICKNESS, Micrometer),
      exposedCopperArea = withAreaUnit(meta \? "exposedcopperarea", AreaUnit.SquareMillimeter),
      exposedCopperAreaTop = withAreaUnit(
        meta \? "exposedcopperareatop" orElse meta \? DFM.EXPOSED_COPPER_AREA_TOP,
        AreaUnit.SquareMillimeter
      ),
      exposedCopperAreaBottom = withAreaUnit(
        meta \? "exposedcopperareabottom" orElse meta \? DFM.EXPOSED_COPPER_AREA_BOTTOM,
        AreaUnit.SquareMillimeter
      ),
      traceWidth = withUnit(meta \? "tracewidth" orElse meta \? DFM.TRACE_WIDTH, Millimeter),
      outerTraceWidth = withUnit(meta \? DFM.OUTER_TRACE_WIDTH, Millimeter),
      innerTraceWidth = withUnit(meta \? DFM.INNER_TRACE_WIDTH, Millimeter),
      copperClearance = withUnit(meta \? DFM.CLEARANCE, Millimeter),
      outerCopperClearance = withUnit(meta \? DFM.OUTER_CLEARANCE, Millimeter),
      innerCopperClearance = withUnit(meta \? DFM.INNER_CLEARANCE, Millimeter),
      soldermaskColor = Seq(Settings.COLOR, "soldermaskcolor").flatMap(meta.resolve(_, SoldermaskColor.res)).headOption,
      soldermaskSide = Seq(Settings.SOLDERMASK_SIDES, "soldermaskside").flatMap(meta.resolve(_, Side.res)).headOption,
      pasteSide = Seq(Settings.PASTE_SIDES).flatMap(meta.resolve(_, Side.res)).headOption,
      soldermaskDam = withUnit(meta \? "soldermaskdam" orElse meta \? DFM.SOLDERMASK_DAM, Millimeter),
      soldermaskClearance = withUnit(meta \? "soldermaskclearance" orElse meta \? DFM.SOLDERMASK_CLEARANCE, Millimeter)
    )

  def toSettings(spec: PCBSpecification): PCBV2Properties = {
    val meta = (spec.settings ++ spec.dfm ++ spec.user.getOrElse(MetaInfo())).lowerCase
    to(meta)
  }

  def to(
      assembly: UUID,
      version: UUID,
      specification: UUID,
      front: Option[FilePath],
      rear: Option[FilePath]
  ): PCBPreviews =
    PCBPreviews(
      front = front.map(to(assembly, version, specification, _)),
      rear = rear.map(to(assembly, version, specification, _))
    )

  def to(asssembly: UUID, version: UUID, specification: UUID, filePath: FilePath): FileAccess =
    filePath.toAccess(ServiceDefinition("assembly")) {
      c => s"/files/assembly/${asssembly}/versions/${version}/specifications/$specification/${filePath.filename}"
    }

  def from(assRef: AssemblyReference, spec: PCBV2Specification, layerCount: Option[BigDecimal]): PCBSpecification =
    PCBSpecification(
      assembly = assRef,
      id = spec.id,
      alias = spec.name,
      dfm = MetaInfo(),
      settings = from(spec.settings, layerCount),
      user = None,
      status = SpecificationStatus.Active,
      base = None,
      preview = None,
      previewRear = None,
      changes = None
    )

  protected[api] def x(name: String, v: LengthWithUnit, unit: LengthUnit) =
    Property.of(name, v.to(unit))

  protected[api] def x(name: String, v: AreaWithUnit, unit: AreaUnit) =
    Property.of(name, v.to(unit))

  protected[api] def x(name: String, v: TemperatureWithUnit, unit: TemperatureUnit) =
    Property.of(name, v.to(unit))

  protected[api] def x[T](name: String, v: T)(implicit f: Format[T]): Property =
    v match {
      case vx: ApplicationType    => Property.of(name, ApplicationType.res.to(vx))
      case vx: BaseMaterial       => Property.of(name, BaseMaterial.res.to(vx))
      case vx: Chamfering         => Property.of(name, Chamfering.res.to(vx))
      case vx: CtiClass           => Property.of(name, CtiClass.res.to(vx))
      case vx: FlammabilityRating => Property.of(name, FlammabilityRating.res.to(vx))
      case vx: IPC600Class        => Property.of(name, IPC600Class.res.to(vx))
      case vx: LayerstackType     => Property.of(name, LayerstackType.res.to(vx))
      case vx: Reports            => Property.of(name, Reports.res.to(vx))
      case vx: Side               => Property.of(name, Side.res.to(vx))
      case vx: SilkscreenColor    => Property.of(name, SilkscreenColor.res.to(vx))
      case vx: SoldermaskColor    => Property.of(name, SoldermaskColor.res.to(vx))
      case vx: SurfaceFinish      => Property.of(name, SurfaceFinish.res.to(vx))
      case vx: ViaFillingType     => Property.of(name, ViaFillingType.res.to(vx))
      case vx: ULMarkingTypeEnum  => Property.of(name, ULMarkingTypeEnum.res.to(vx))
      case vx: DateCodeType       => Property.of(name, DateCodeType.res.to(vx))
      case vx: PressFitTechnology => Property.of(name, PressFitTechnology.res.to(vx))
      case x: Seq[T] @unchecked   => ObjectProperty(name, new JsObject(Map("val" -> Json.toJson(x))))

      case any => Property.of(name, any)

    }

  def from(spec: PCBV2Properties, layerCount: Option[BigDecimal]): MetaInfo = {
    val (innerCopperThickness, minInnerLayerStructure) = spec.layerStack.layercount.orElse(layerCount) match {
      case Some(n) if n < 4 => (None, None)
      case _                => (spec.layerStack.innerCopperThickness, spec.layerStack.minInnerLayerStructure)
    }

    val impedanceTolerance = spec.board.advanced.impedanceTested.flatMap { impedanceTested =>
      if (impedanceTested) {
        spec.board.advanced.impedanceTolerance
      } else {
        None
      }
    }

    val props: Map[String, Property] = Seq(
      spec.layerStack.layercount.map(v => x(DFM.LAYERCOUNT, v).e),
      spec.mechanical.minViaDiameter.map(v => x(DFM.VIA_MIN_SIZE, v, Millimeter).e),
      spec.board.basic.boardHeight.map(v => x(DFM.HEIGHT, v, Millimeter).e),
      spec.board.basic.boardWidth.map(v => x(DFM.WIDTH, v, Millimeter).e),
      spec.layerStack.outerCopperThickness.map(v => x(DFM.OUTER_COPPER_HEIGHT, v, Micrometer).e),
      innerCopperThickness.map(v => x(DFM.INNER_COPPER_HEIGHT, v, Micrometer).e),
      spec.layerStack.outerCopperThickness.map(v => x("outercopperthickness", v, Micrometer).e), // compatibility
      innerCopperThickness.map(v => x("innercopperthickness", v, Micrometer).e),                 // compatibility
      spec.layerStack.minOuterLayerStructure.map(v => x(DFM.MIN_OUTER_STRUCTURE, v, Millimeter).e),
      minInnerLayerStructure.map(v => x(DFM.MIN_INNER_STRUCTURE, v, Millimeter).e),
      spec.layerStack.minOuterLayerStructure.map(v => x("minouterlayerstructure", v, Millimeter).e), // compatibility
      minInnerLayerStructure.map(v => x("mininnerlayerstructure", v, Millimeter).e),                 // compatibility
      spec.board.basic.soldermaskColor.map(v => x(Settings.COLOR, v).e),
      spec.board.basic.soldermaskSide.map(v => x(Settings.SOLDERMASK_SIDES, v).e),
      spec.board.basic.pasteSide.map(v => x(Settings.PASTE_SIDES, v).e),
      spec.board.advanced.placementSide.map(v => x(Settings.PLACEMENT_SIDES, v).e),
      spec.board.basic.silkscreenColor.map(v => x(Settings.SILKSCREEN_COLOR, v).e),
      spec.board.basic.silkscreenSide.map(v => x(Settings.SILKSCREEN_SIDES, v).e),
      spec.board.basic.surfaceFinish.map(v => x(Settings.FINISH, v).e),
      spec.board.basic.enigThickness.map(v => x(Settings.ENIG_THICKNESS, v, Microinch).e),
      spec.board.advanced.ipc600Class.map(v => x(Settings.IPC_600_CLASS, v).e),
      spec.layerStack.baseMaterial.map(v => x("basematerial", v).e),
      spec.layerStack.finalThickness.map(v => x("finalthickness", v, Millimeter).e),
      spec.board.advanced.eTest.map(v => x(Settings.ETEST, v).e),
      spec.layerStack.ulLayerStack.map(v => x("ullayerstack", v).e),
      spec.mechanical.viaFilling.map(v => x("viafilling", v).e),
      spec.board.advanced.pressFit.map(v => x(Settings.PRESS_FIT, v).e),
      spec.board.advanced.pressFitTechnology.map(v => x(Settings.PRESS_FIT_TECHNOLOGY, v).e),
      spec.board.advanced.impedanceTested.map(v => x(Settings.IMPEDANCE_CONTROLLED, v).e),
      impedanceTolerance.map(v => x(Settings.IMPEDANCE_TOLERANCE, v).e),
      spec.board.advanced.peelableMask.map(v => x("peelablemask", v).e),
      spec.board.advanced.captonTape.map(v => x("captontape", v).e),
      spec.mechanical.blindVias.map(v => x(DFM.BLIND_VIAS, v).e),
      spec.mechanical.buriedVias.map(v => x(DFM.BURIED_VIAS, v).e),
      spec.mechanical.blindViaCount.map(v => x(DFM.BLIND_VIA_COUNT, v).e),
      spec.mechanical.buriedViaCount.map(v => x(DFM.BURIED_VIA_COUNT, v).e),
      spec.mechanical.chamfering.map(v => x("chamfering", v).e),
      spec.mechanical.outlineLength.map(v => x(DFM.OUTLINE_LENGTH, v, Millimeter).e),
      spec.board.basic.hardGold.map(v => x("hardgold", v).e),
      spec.board.basic.hardGoldThickness.map(v => x(Settings.HARD_GOLD_THICKNESS, v, Micrometer).e),
      spec.board.basic.hardGoldArea.map(v => x(Settings.HARD_GOLD_AREA, v, SquareMillimeter).e),
      spec.board.advanced.maxXOutsAllowed.map(v => x("maxxoutsallowed", v).e),
      spec.board.advanced.reports.map(v => x("reports", v).e),
      spec.board.advanced.itar.map(v => x(Settings.ITAR, v).e),
      spec.board.basic.notes.map(v => x("notes", v).e),
      spec.board.basic.applicationType.map(v => x("applicationtype", v).e),
      spec.board.advanced.carbonPrint.map(v => x("carbonprint", v).e),
      spec.board.advanced.carbonPrintSide.map(v => x("carbon_print_side", v).e),
      spec.board.advanced.edgeMetalization.map(v => x("edgemetalization", v).e),
      spec.layerStack.layerstackThicknessTolerance.map(v => x("layerstackthicknesstolerance", v).e),
      spec.layerStack.tgValue.map(v => x("tgvalue", v, Celsius).e),
      spec.layerStack.numberOfPrepregs.map(v => x(Settings.NUMBER_OF_PREPREGS, v).e),
      spec.layerStack.numberOfLaminationCycles.map(v => x(Settings.NUMBER_OF_LAMINATION_CYCLES, v).e),
      spec.layerStack.rohsCompilant.map(v => x(Settings.ROHS, v).e),
      spec.board.advanced.flammabilityRating.map(v => x("flammabilityrating", v).e),
      spec.layerStack.layerstackType.map(v => x("layerstacktype", v).e),
      spec.layerStack.ulMarkingType.map(v => x("ulmarkingtype", v).e),
      spec.board.advanced.ctiClass.map(v => x("cticlass", v).e),
      spec.board.advanced.ecobond.map(v => x(Settings.ECOBOND, v).e),
      spec.board.advanced.numberOfLines.map(v => x(Settings.NUMBER_OF_LINES, v).e),
      spec.board.advanced.halfCutPlatedVias.map(v => x(Settings.HALF_CUT_PLATED_VIAS, v).e),
      spec.mechanical.aspectRatio.map(v => x("aspectratio", v).e),
      spec.board.basic.soldermaskDam.map(v => x(DFM.SOLDERMASK_DAM, v, Millimeter).e),
      spec.board.basic.soldermaskClearance.map(v => x(DFM.SOLDERMASK_CLEARANCE, v, Millimeter).e),
      spec.board.basic.exposedCopperArea.map(v => x("exposedcopperarea", v, SquareMillimeter).e),
      spec.board.basic.exposedCopperAreaTop.map(v => x(DFM.EXPOSED_COPPER_AREA_TOP, v, SquareMillimeter).e),
      spec.board.basic.exposedCopperAreaBottom.map(v => x(DFM.EXPOSED_COPPER_AREA_BOTTOM, v, SquareMillimeter).e),
      spec.board.basic.outerTraceWidth.map(v => x(DFM.OUTER_TRACE_WIDTH, v, Millimeter).e),
      spec.board.basic.innerTraceWidth.map(v => x(DFM.INNER_TRACE_WIDTH, v, Millimeter).e),
      spec.board.basic.copperClearance.map(v => x(DFM.CLEARANCE, v, Millimeter).e),
      spec.board.basic.outerCopperClearance.map(v => x(DFM.OUTER_CLEARANCE, v, Millimeter).e),
      spec.board.basic.innerCopperClearance.map(v => x(DFM.INNER_CLEARANCE, v, Millimeter).e),
      spec.mechanical.totalDrillCount.map(v => x("totaldrillcount", v).e),
      spec.mechanical.microVias.map(v => x("microvias", v).e),
      spec.mechanical.phCount.map(v => x(DFM.PH_COUNT, v).e),
      spec.board.basic.traceWidth.map(v => x(DFM.TRACE_WIDTH, v, Millimeter).e),
      spec.mechanical.phToolCount.map(v => x(DFM.PH_TOOL_COUNT, v).e),
      spec.mechanical.nphCount.map(v => x(DFM.NPH_COUNT, v).e),
      spec.mechanical.phMinSize.map(v => x(DFM.PH_MIN_SIZE, v, Millimeter).e),
      spec.mechanical.phMaxSize.map(v => x(DFM.PH_MAX_SIZE, v, Millimeter).e),
      spec.mechanical.nphMaxSize.map(v => x(DFM.NPH_MAX_SIZE, v, Millimeter).e),
      spec.mechanical.nphToolCount.map(v => x(DFM.NPH_TOOL_COUNT, v).e),
      spec.board.basic.copperClearance.map(v => x(DFM.CLEARANCE, v, Millimeter).e),
      spec.mechanical.nphMinSize.map(v => x(DFM.NPH_MIN_SIZE, v, Millimeter).e),
      spec.mechanical.phAnnularRing.map(v => x(DFM.PH_ANNULAR_RING, v, Millimeter).e),
      spec.board.advanced.halogenFree.map(v => x(Settings.HALOGEN_FREE, v).e),
      spec.board.advanced.placementSide.map(v => x(Settings.PLACEMENT_SIDES, v).e),
      spec.reports.crossSection.map(v => x(DFM.CROSS_SECTION_REPORT, v).e),
      spec.reports.xRayMeasurement.map(v => x(DFM.XRAY_MEASUREMENT_REPORT, v).e),
      spec.reports.xRayMeasurementPoints.map(v => x(DFM.XRAY_MEASUREMENT_POINTS_REPORT, v).e),
      spec.reports.firstArticleInspection.map(v => x(DFM.FIRST_ARTICLE_INSPECTION_REPORT, v).e),
      spec.reports.certificateOfConformance.map(v => x(DFM.CERTIFICATE_OF_CONFORMANCE_REPORT, v).e),
      spec.markings.dateCode.map(v => x(DFM.DATE_CODE_MARKING, v).e),
      spec.markings.datamatrixCode.map(v => x(DFM.DATAMATRIX_CODE_MARKING, v).e),
      spec.markings.fabricatorLogo.map(v => x(DFM.FABRICATOR_LOGO_MARKING, v).e),
      spec.miscellaneous.manufacturingInformation.map(v => x(DFM.MANUFACTURING_INFORMATION, v).e),
      spec.mechanical.phSurfaceArea.map(v => x(DFM.PH_SURFACE_AREA, v).e)
    ).flatten.toMap

    MetaInfo(
      props
    )
  }

  /** Disables or enables InnerCopperThickness and InnerLayerStructure capabilities depending on the number of layers
    */
  protected def getInnerLayerCapabilities(numberOfLayers: Option[Int] = None)
      : (LengthSpecificationCapability, LengthSpecificationCapability) =
    numberOfLayers match {
      case Some(n) if n < 4 =>
        (
          LengthSpecificationCapability(disabled = true, defaultUnit = Micrometer),
          LengthSpecificationCapability(disabled = true, defaultUnit = Millimeter)
        )
      case _ =>
        (
          LengthSpecificationCapability(
            allowed = Some(DEFAULT_COPPER_THICKNESS),
            default = Some(35),
            defaultUnit = Micrometer
          ),
          LengthSpecificationCapability(defaultUnit = Millimeter)
        )
    }

  private def getEnigThicknessCapability(surfaceFinish: Option[SurfaceFinish]): LengthSpecificationCapability =
    surfaceFinish match {
      case Some(SurfaceFinish.Enig) | None =>
        LengthSpecificationCapability(
          allowed = Some(Seq(1, 2, 3, 4)),
          default = Some(2),
          defaultUnit = Microinch
        )
      case _ =>
        LengthSpecificationCapability(disabled = true, defaultUnit = Microinch)
    }

  private def getFinalThicknessCapability(layerstackType: Option[LayerstackType]): LengthSpecificationCapability =
    layerstackType match {
      case Some(LayerstackType.Flex) =>
        LengthSpecificationCapability(
          allowed = Some(DEFAULT_FLEX_FINAL_THICKNESS),
          default = Some(0.12),
          defaultUnit = Millimeter
        )
      case _ =>
        LengthSpecificationCapability(
          allowed = Some(DEFAULT_FINAL_THICKNESS),
          default = Some(1.55),
          defaultUnit = Millimeter
        )
    }

  def defaultCapabilities(
      layerstackType: Option[LayerstackType] = None,
      numberOfLayers: Option[Int] = None,
      surfaceFinish: Option[SurfaceFinish] = None
  ): PCBV2SpecificationCapabilities = {
    val (innerCopperThicknessCapability, minInnerLayerStructureCapability) = getInnerLayerCapabilities(numberOfLayers)
    val enigThicknessCapability                                            = getEnigThicknessCapability(surfaceFinish)
    val finalThicknessCapability                                           = getFinalThicknessCapability(layerstackType)

    PCBV2SpecificationCapabilities(
      board = PCBV2BoardCapabilities(
        basic = PCBV2BasicBoardCapabilities(
          boardHeight = LengthSpecificationCapability(defaultUnit = Millimeter),
          boardWidth = LengthSpecificationCapability(defaultUnit = Millimeter),
          silkscreenColor =
            EnumSpecificationCapability((SilkscreenColor.ALL), default = Some(SilkscreenColor.White)),
          silkscreenSide = EnumSpecificationCapability((Side.ALL), default = Some(Side.Both)),
          surfaceFinish = EnumSpecificationCapability((SurfaceFinish.ALL), default = Some(SurfaceFinish.Enig)),
          enigThickness = enigThicknessCapability,
          applicationType = EnumSpecificationCapability((ApplicationType.ALL)),
          hardGold = BooleanSpecificationCapability(),
          hardGoldArea = NumericAreaSpecificationCapability(defaultUnit = SquareMillimeter),
          hardGoldThickness = LengthSpecificationCapability(default = Some(1), defaultUnit = Micrometer),
          exposedCopperArea = NumericAreaSpecificationCapability(defaultUnit = SquareMillimeter),
          exposedCopperAreaTop = NumericAreaSpecificationCapability(defaultUnit = SquareMillimeter),
          exposedCopperAreaBottom = NumericAreaSpecificationCapability(defaultUnit = SquareMillimeter),
          traceWidth = LengthSpecificationCapability(defaultUnit = Millimeter),
          copperClearance = LengthSpecificationCapability(defaultUnit = Millimeter),
          soldermaskColor = EnumSpecificationCapability((SoldermaskColor.ALL), default = Some(SoldermaskColor.Green)),
          soldermaskSide = EnumSpecificationCapability((Side.ALL), default = Some(Side.Both)),
          soldermaskDam = LengthSpecificationCapability(defaultUnit = Millimeter),
          soldermaskClearance = LengthSpecificationCapability(defaultUnit = Millimeter)
        ),
        advanced = PCBV2AdvancedBoardCapabilities(
          ipc600Class = EnumSpecificationCapability(IPC600Class.values, default = Some(IPC600Class.IPC2)),
          eTest = BooleanSpecificationCapability(default = true),
          pressFit = BooleanSpecificationCapability(),
          pressFitTechnology =
            EnumSpecificationCapability(PressFitTechnology.ALL, default = Some(PressFitTechnology.MassivePressFit)),
          impedanceTested = BooleanSpecificationCapability(),
          impedanceTolerance = CountSpecificationCapability(default = Some(10), allowed = Some(Seq(10, 8, 5))),
          peelableMask = EnumSpecificationCapability((Side.ALL), default = Some(Side.None)),
          captonTape = EnumSpecificationCapability(Side.ALL, default = Some(Side.None)),
          reports = EnumSpecificationCapability((Reports.ALL)),
          itar = BooleanSpecificationCapability(),
          carbonPrint = BooleanSpecificationCapability(),
          carbonPrintSide = EnumSpecificationCapability(Side.ALL, default = Some(Side.None)),
          edgeMetalization = BooleanSpecificationCapability(),
          flammabilityRating =
            EnumSpecificationCapability((FlammabilityRating.ALL), default = Some(FlammabilityRating.V0)),
          ctiClass = EnumSpecificationCapability(CtiClass.values, default = Some(CtiClass.None)),
          maxXOutsAllowed = CountSpecificationCapability(),
          halogenFree = BooleanSpecificationCapability(),
          placementSide = EnumSpecificationCapability(Side.ALL, default = None),
          ecobond = BooleanSpecificationCapability(),
          numberOfLines = CountSpecificationCapability(default = Some(4)),
          halfCutPlatedVias = BooleanSpecificationCapability()
        )
      ),
      layerStack = PCBV2LayerStackCapabilities(
        layerstackType = EnumSpecificationCapability(
          allowed = LayerstackType.ALL,
          default = Some(LayerstackType.Rigid)
        ),
        layerstackThicknessTolerance = CountSpecificationCapability(),
        ulLayerStack = BooleanSpecificationCapability(),
        ulMarkingType = EnumSpecificationCapability(
          allowed = ULMarkingTypeEnum.values,
          default = Some(ULMarkingTypeEnum.DefaultMarking)
        ),
        layercount = CountSpecificationCapability(),
        finalThickness = finalThicknessCapability,
        baseMaterial = EnumSpecificationCapability((BaseMaterial.ALL), default = Some(BaseMaterial.FR4)),
        outerCopperThickness = LengthSpecificationCapability(
          allowed =
            Some(DEFAULT_COPPER_THICKNESS),
          default = Some(35),
          defaultUnit = Micrometer
        ),
        innerCopperThickness = innerCopperThicknessCapability,
        minOuterLayerStructure = LengthSpecificationCapability(defaultUnit = Millimeter),
        minInnerLayerStructure = minInnerLayerStructureCapability,
        rohsCompilant = BooleanSpecificationCapability(default = true),
        tgValue = TemperatureSpecificationCapability(
          allowed = Some(Seq(130, 150, 170, 250)),
          default = Some(150),
          defaultUnit = Celsius
        ),
        numberOfPrepregs = CountSpecificationCapability(default = Some(2)),
        numberOfLaminationCycles = CountSpecificationCapability(default = Some(10))
      ),
      mechanical = PCBV2MechanicalCapabilities(
        minViaDiameter = LengthSpecificationCapability(defaultUnit = Millimeter),
        viaFilling = EnumSpecificationCapability((ViaFillingType.ALL), default = Some(ViaFillingType.None)),
        blindVias = BooleanSpecificationCapability(),
        buriedVias = BooleanSpecificationCapability(),
        chamfering = EnumSpecificationCapability((Chamfering.ALL), default = Some(Chamfering.None)),
        aspectRatio = CountSpecificationCapability(),
        totalDrillCount = CountSpecificationCapability(),
        microVias = BooleanSpecificationCapability(),
        phCount = CountSpecificationCapability(),
        phToolCount = CountSpecificationCapability(),
        nphCount = CountSpecificationCapability(),
        phMinSize = LengthSpecificationCapability(defaultUnit = Millimeter),
        nphMaxSize = LengthSpecificationCapability(defaultUnit = Millimeter),
        phMaxSize = LengthSpecificationCapability(defaultUnit = Millimeter),
        nphToolCount = CountSpecificationCapability(),
        phAnnularRing = LengthSpecificationCapability(defaultUnit = Millimeter),
        nphMinSize = LengthSpecificationCapability(defaultUnit = Millimeter),
        phSurfaceArea = NumericAreaSpecificationCapability(defaultUnit = SquareMillimeter)
      ),
      reports = PCBV2ReportsCapabilities.defaultCapabilities,
      markings = PCBV2MarkingsCapabilities.defaultCapabilities,
      miscellaneous = PCBV2MiscellaneousCapabilities.defaultCapabilities
    )
  }

}
