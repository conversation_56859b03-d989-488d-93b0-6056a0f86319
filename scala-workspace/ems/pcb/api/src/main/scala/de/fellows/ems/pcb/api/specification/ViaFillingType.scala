package de.fellows.ems.pcb.api.specification

import de.fellows.utils.JsonFormats
import de.fellows.utils.JsonFormats.EnumResolver
import de.fellows.utils.apidoc.StackrateAPIObject
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json.Format

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("none", "Ia", "Ib", "IIIa", "IIIb", "IVa", "IVb", "V", "VI", "VII"))
sealed abstract class ViaFillingType(val value: String, val label: String) {
  override def toString: String = s"$label ($value)"
}

object ViaFillingType {
  object None extends ViaFillingType("none", "None")

  object TentedSingleSided extends ViaFillingType("Ia", "Tented - single-sided")

  object TentedDoubleSided extends ViaFillingType("Ib", "Tented - double-sided")

  object TentedAndCoveredDoubleSided extends ViaFillingType("IIb", "Tented and covered - double-sided")

  object PluggedSingleSided extends ViaFillingType("IIIa", "Plugged - single-sided")

  object PluggedDoubleSided extends ViaFillingType("IIIb", "Plugged - double-sided")

  object PluggedAndCoveredSingleSided extends ViaFillingType("IVa", "Plugged and covered - single-sided")

  object PluggedAndCoveredDoubleSided extends ViaFillingType("IVb", "Plugged and covered - double-sided")

  object Filled extends ViaFillingType("V", "Filled (fully plugged)")

  object FilledAndCovered extends ViaFillingType("VI", "Filled and covered")

  object FilledAndCapped extends ViaFillingType("VII", "Filled and capped")

  val ALL = Seq(
    None,
    TentedSingleSided,
    TentedDoubleSided,
    TentedAndCoveredDoubleSided,
    PluggedSingleSided,
    PluggedDoubleSided,
    PluggedAndCoveredSingleSided,
    PluggedAndCoveredDoubleSided,
    Filled,
    FilledAndCovered,
    FilledAndCapped
  )

  implicit val res: EnumResolver[ViaFillingType] = JsonFormats.resolver(ALL, _.value)
  implicit val f: Format[ViaFillingType]         = JsonFormats.enumFormat[ViaFillingType]
}
