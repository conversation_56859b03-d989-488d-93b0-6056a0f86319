package de.fellows.ems.pcb.api.specification.units

import de.fellows.utils.apidoc.StackrateAPIObject
import enumeratum.{Enum, EnumEntry, PlayJsonEnum}
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json.{Format, Json, Reads, Writes}

object TemperatureWithUnit {

  def celsius(value: BigDecimal): TemperatureWithUnit    = TemperatureWithUnit(value, TemperatureUnit.Celsius)
  def fahrenheit(value: BigDecimal): TemperatureWithUnit = TemperatureWithUnit(value, TemperatureUnit.Fahrenheit)

  val format: Format[TemperatureWithUnit] = Json.format
  val oldFormat: Format[TemperatureWithUnit] = Format(
    Reads {
      v => v.validate[BigDecimal].map(TemperatureWithUnit(_, TemperatureUnit.Celsius))
    },
    Writes {
      v => Json.toJson(v.value)
    }
  )
}

case class TemperatureWithUnit(value: BigDecimal, unit: TemperatureUnit)
    extends NumberWithUnit[TemperatureWithUnit, TemperatureUnit] {
  override def create(value: BigDecimal, unit: TemperatureUnit): TemperatureWithUnit = TemperatureWithUnit(value, unit)
}

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("Celsius", "Fahrenheit"))
sealed trait TemperatureUnit extends EnumEntry with NumberUnit[TemperatureUnit] {
  def to(targetUnit: TemperatureUnit)(value: BigDecimal): BigDecimal
}

object TemperatureUnit extends Enum[TemperatureUnit] with PlayJsonEnum[TemperatureUnit] {
  val values: IndexedSeq[TemperatureUnit] = findValues

  case object Celsius extends TemperatureUnit {
    override def to(targetUnit: TemperatureUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case Fahrenheit => value * 9 / 5 + 32
      case Celsius    => value
    }
  }

  case object Fahrenheit extends TemperatureUnit {
    override def to(targetUnit: TemperatureUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case Fahrenheit => value
      case Celsius    => (value - 32) * 5 / 9
    }
  }
}
