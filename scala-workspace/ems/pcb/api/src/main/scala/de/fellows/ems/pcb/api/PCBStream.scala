package de.fellows.ems.pcb.api

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.{APIOutline, Graphic, LayerFile, MetaStatus}
import de.fellows.utils.meta._
import play.api.libs.json
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

case class PCBStreamMessage(t: String, m: Message, ref: Option[AssemblyReference])

sealed trait Message {}

case class FileFormatChanged(file: LayerFile) extends Message

case class FileSetMsg(file: LayerFile) extends Message

case class AnalysisMsg(status: Option[MetaStatus], meta: MetaInfo) extends Message

case class OutlineSetMsg(outline: LayerFile) extends Message

case class OutlineCandidateMsg(outline: APIOutline)       extends Message
case class OutlineCandidatesMsg(outline: Seq[APIOutline]) extends Message

case class DrillsAddedMsg(fileId: UUID, drillFile: String) extends Message

case class Ping(time: Instant) extends Message

case class LayerRendered(id: UUID, data: Map[String, Graphic]) extends Message

case class PreviewChangedMessage(specification: UUID, preview: String) extends Message

object Message {
  implicit val format: json.Format[Message] = Json.format
}

object PreviewChangedMessage {
  implicit val format: Format[PreviewChangedMessage] = Json.format
}

object PCBStreamMessage {
  implicit val format: json.Format[PCBStreamMessage] = Json.format
}

object FileFormatChanged {
  implicit val format: json.Format[FileFormatChanged] = Json.format
}

object OutlineSetMsg {
  implicit val format: json.Format[OutlineSetMsg] = Json.format
}
object OutlineCandidateMsg {
  implicit val format: json.Format[OutlineCandidateMsg] = Json.format
}
object OutlineCandidatesMsg {
  implicit val format: json.Format[OutlineCandidatesMsg] = Json.format
}

object AnalysisMsg {
  implicit val format: json.Format[AnalysisMsg] = Json.format
}

object DrillsAddedMsg {
  implicit val format: json.Format[DrillsAddedMsg] = Json.format
}

object FileSetMsg {
  implicit val format: json.Format[FileSetMsg] = Json.format
}

object Ping {
  implicit val format: json.Format[Ping] = Json.format
}

object LayerRendered {
  implicit val format: json.Format[LayerRendered] = Json.format
}
