package de.fellows.ems.pcb.api.specification.units

import de.fellows.utils.apidoc.StackrateAPIObject
import enumeratum.{Enum, EnumEntry, PlayJsonEnum}
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json.{Format, Json, Reads, Writes}

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("SquareMillimeter", "SquareInch"))
sealed trait AreaUnit extends EnumEntry with NumberUnit[AreaUnit] {
  def to(targetUnit: AreaUnit)(value: BigDecimal): BigDecimal
}

object AreaUnit extends Enum[AreaUnit] with PlayJsonEnum[AreaUnit] {
  val values: IndexedSeq[AreaUnit] = findValues

  case object SquareMillimeter extends AreaUnit {
    override def to(targetUnit: AreaUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case SquareInch       => value / 645.16
      case SquareMillimeter => value
    }
  }
  case object SquareInch extends AreaUnit {
    override def to(targetUnit: AreaUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case SquareInch       => value
      case SquareMillimeter => value * 645.16
    }
  }
}

case class AreaWithUnit(value: BigDecimal, unit: AreaUnit) extends NumberWithUnit[AreaWithUnit, AreaUnit] {

  def create(value: BigDecimal, unit: AreaUnit): AreaWithUnit = AreaWithUnit(value, unit)

  def **(other: LengthWithUnit): VolumeWithUnit = {
    val cubicMM = this.to(AreaUnit.SquareMillimeter) * other.to(LengthUnit.Millimeter)
    VolumeWithUnit(cubicMM, VolumeUnit.CubicMillimeter)
  }
}

object AreaWithUnit {
  def sqmm(value: BigDecimal): AreaWithUnit   = AreaWithUnit(value, AreaUnit.SquareMillimeter)
  def sqinch(value: BigDecimal): AreaWithUnit = AreaWithUnit(value, AreaUnit.SquareInch)

  val format: Format[AreaWithUnit] = Json.format
  val oldFormat: Format[AreaWithUnit] = Format(
    Reads {
      v => v.validate[BigDecimal].map(AreaWithUnit(_, AreaUnit.SquareMillimeter))
    },
    Writes {
      v => Json.toJson(v.value)
    }
  )
}
