package de.fellows.ems.pcb.api.specification

import de.fellows.utils.JsonFormats
import de.fellows.utils.JsonFormats.EnumResolver
import de.fellows.utils.apidoc.StackrateAPIObject
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json.Format

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("none", "default", "custom"))
sealed abstract class DateCodeType(val value: String) {}

object DateCodeType {

  object NoDateCode extends DateCodeType("none")

  object DefaultDateCode extends DateCodeType("default")

  object CustomDateCode extends DateCodeType("custom")

  val ALL = Seq(
    NoDateCode,
    DefaultDateCode,
    CustomDateCode
  )

  implicit val res: EnumResolver[DateCodeType] = JsonFormats.resolver(ALL, _.value)
  implicit val f: Format[DateCodeType]         = JsonFormats.enumFormat[DateCodeType]
}
