package de.fellows.ems.pcb.api.specification.units

import de.fellows.utils.apidoc.StackrateAPIObject
import enumeratum._
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json._

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("Micrometer", "Inch", "Mil", "Millimeter", "Microinch"))
sealed trait LengthUnit extends EnumEntry with NumberUnit[LengthUnit] {
  def to(targetUnit: LengthUnit)(value: BigDecimal): BigDecimal
}

object LengthUnit extends Enum[LengthUnit] with PlayJsonEnum[LengthUnit] {
  val values: IndexedSeq[LengthUnit] = findValues

  case object Millimeter extends LengthUnit {
    override def to(targetUnit: LengthUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case Inch       => value / 25.4
      case Micrometer => value * 1000
      case Mil        => value / 0.0254
      case Millimeter => value
      case Microinch  => value / 2.54e-05
    }
  }
  case object Inch extends LengthUnit {
    override def to(targetUnit: LengthUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case Millimeter => value * 25.4
      case Micrometer => value * 25400
      case Mil        => value * 1000
      case Inch       => value
      case Microinch  => value * 1000000
    }
  }
  case object Micrometer extends LengthUnit {
    override def to(targetUnit: LengthUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case Inch       => value / 25400
      case Millimeter => value / 1000
      case Mil        => value / 25.4
      case Micrometer => value
      case Microinch  => value / 0.0254
    }
  }
  case object Mil extends LengthUnit {
    override def to(targetUnit: LengthUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case Inch       => value * 0.001
      case Millimeter => value * 0.0254
      case Micrometer => value * 25.4
      case Mil        => value
      case Microinch  => value * 1000
    }
  }

  case object Microinch extends LengthUnit {
    override def to(targetUnit: LengthUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case Inch       => value / 1000000
      case Millimeter => value / 25400
      case Mil        => value / 1000
      case Micrometer => value * 25.4 / 1000
      case Microinch  => value
    }
  }
}

case class LengthWithUnit(value: BigDecimal, unit: LengthUnit) extends NumberWithUnit[LengthWithUnit, LengthUnit] {
  def create(value: BigDecimal, unit: LengthUnit): LengthWithUnit = LengthWithUnit(value, unit)
}

object LengthWithUnit {
  def mm(value: BigDecimal): LengthWithUnit         = LengthWithUnit(value, LengthUnit.Millimeter)
  def inch(value: BigDecimal): LengthWithUnit       = LengthWithUnit(value, LengthUnit.Inch)
  def microinch(value: BigDecimal): LengthWithUnit  = LengthWithUnit(value, LengthUnit.Microinch)
  def micrometer(value: BigDecimal): LengthWithUnit = LengthWithUnit(value, LengthUnit.Micrometer)
  def mil(value: BigDecimal): LengthWithUnit        = LengthWithUnit(value, LengthUnit.Mil)

  val format: Format[LengthWithUnit] = Json.format

  val oldFormat: Format[LengthWithUnit] = Format(
    Reads {
      v => v.validate[BigDecimal].map(LengthWithUnit(_, LengthUnit.Millimeter))
    },
    Writes {
      v => Json.toJson(v.value)
    }
  )
}
