package de.fellows.ems.pcb.api.specification.units

import enumeratum.{Enum, EnumEntry, PlayJsonEnum}

sealed trait DensityUnit extends EnumEntry with NumberUnit[DensityUnit] {}

object DensityUnit extends Enum[DensityUnit] with PlayJsonEnum[DensityUnit] {

  val values: IndexedSeq[DensityUnit] = findValues
  case object KilogramPerCubicMeter extends DensityUnit {
    override def to(targetUnit: DensityUnit)(value: BigDecimal): BigDecimal = targetUnit match {
      case KilogramPerCubicMeter => value
    }
  }
}

case class DensityWithUnit(value: BigDecimal, unit: DensityUnit) extends NumberWithUnit[DensityWithUnit, DensityUnit] {
  override def create(value: BigDecimal, unit: DensityUnit): DensityWithUnit = DensityWithUnit(value, unit)
}

object DensityWithUnit {
  def kgPerCubicMeter(value: BigDecimal): DensityWithUnit = DensityWithUnit(value, DensityUnit.KilogramPerCubicMeter)
}
