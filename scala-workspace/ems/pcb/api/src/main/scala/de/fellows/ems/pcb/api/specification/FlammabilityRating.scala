package de.fellows.ems.pcb.api.specification

import de.fellows.utils.JsonFormats
import de.fellows.utils.JsonFormats.EnumResolver
import de.fellows.utils.apidoc.StackrateAPIObject
import io.swagger.v3.oas.annotations.media.Schema
import play.api.libs.json.Format

@StackrateAPIObject()
@Schema(`type` = "string", allowableValues = Array("none", "V-0", "V-1", "V-2"))
sealed abstract class FlammabilityRating(val value: String,val label: String) {
}



object FlammabilityRating {
  object None extends FlammabilityRating("none", "None")
  object V0 extends FlammabilityRating("V-0", "V-0")
  object V1 extends FlammabilityRating("V-1", "V-1")
  object V2 extends FlammabilityRating("V-2", "V-2")

  val ALL = Seq(
    None,
    V0,
    V1,
    V2,
  )

  implicit val res: EnumResolver[FlammabilityRating] = JsonFormats.resolver(ALL, _.value)
  implicit val f: Format[FlammabilityRating] = JsonFormats.enumFormat[FlammabilityRating]
}


