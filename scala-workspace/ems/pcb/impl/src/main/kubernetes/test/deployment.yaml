apiVersion: "apps/v1beta2"
kind: Deployment
metadata:
  name: "pcb-test"
  labels:
    app: pcb
    group: fellows
    log: logback
    appNameVersion: "pcb-test"
    "akka.lightbend.com/service-name": pcb
spec:
  replicas: 2
  selector:
    matchLabels:
      appNameVersion: "pcb-test"
  template:
    metadata:
      labels:
        app: pcb
        group: fellows
        log: logback
        sha: "${GIT_SHA_FULL}"
        appNameVersion: "pcb-test"
        "akka.lightbend.com/service-name": pcb
    spec:
      restartPolicy: Always
      imagePullSecrets:
        - name: gitlab-auth
      containers:
        - name: pcb
          image: "jira.electronic-fellows.de:5000/app/backend/pcb-impl:latest"
          imagePullPolicy: Always
          env:
            - name: "REQUIRED_CONTACT_POINT_NR"
              value: "2"
            - name: "JAVA_OPTS"
              value: "-Dplay.crypto.secret=amazingsecret"
          volumeMounts:
            - name: hubstore
              mountPath: /opt/hubstore
          ports:
            - containerPort: 9000
              name: http
            - containerPort: 2552
              name: remoting
            - containerPort: 8558
              name: management

          readinessProbe:
            httpGet:
              path: "/ready"
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          livenessProbe:
            httpGet:
              path: "/alive"
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
      securityContext:
        fsGroup: 1
      volumes:
        - name: hubstore
          flexVolume:
            driver: ceph.rook.io/rook
            fsType: ceph
            options:
              fsName: hubstore
              clusterNamespace: rook-ceph # namespace where the Rook cluster is deployed
              # by default the path is /, but you can override and mount a specific path of the filesystem by using the path attribute
              # the path must exist on the filesystem, otherwise mounting the filesystem at that path will fail
              # path: /some/path/inside/cephfs
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: pcb
  name: pcb
spec:
  ports:
    - name: http
      port: 9000
      protocol: TCP
      targetPort: 9000
    - name: remoting
      port: 2552
      protocol: TCP
      targetPort: 2552
    - name: management
      port: 8558
      protocol: TCP
      targetPort: 8558
  selector:
    app: pcb
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: { }
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: pcb
spec:
  rules:
    - http:
        paths:
          - path: /api/ems/pcb
            backend:
              serviceName: pcb
              servicePort: 9000

          - path: /files/ems/pcb
            backend:
              serviceName: pcb
              servicePort: 9000

status:
  loadBalancer: { }

