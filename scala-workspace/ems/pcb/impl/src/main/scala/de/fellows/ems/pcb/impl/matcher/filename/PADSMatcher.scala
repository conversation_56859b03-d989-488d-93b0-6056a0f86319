package de.fellows.ems.pcb.impl.matcher.filename

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.{FileMatch, RegexFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.{ExcellonFileTypeDetector, GerberFileTypeDetector}

import scala.util.matching.Regex
import scala.util.matching.Regex.Match

class PADSMatcher(implicit serviceDefinition: ServiceDefinition) extends RegexFileMatcher(false) {
  override def id: String = "pads-matcher"

  val service = serviceDefinition.name

  override def confidence = MediumConfidence

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME, ExcellonFileTypeDetector.MIME))

  override val patterns = Seq[(Regex, Match => FileMatch)](
    ("^art([0-9]+)\\.pho$".r, { m => createMatch(LayerConstants.COPPER_MID, index = Some(m.group(1).toInt)) }),
    (
      "bsp\\.pho$".r,
      { m =>
        createMatch(LayerConstants.PASTE_BOTTOM)
      }
    ),
    (
      "bss\\.pho$".r,
      { m =>
        createMatch(LayerConstants.SILKSCREEN_BOTTOM)
      }
    ),
    (
      "tsp\\.pho$".r,
      { m =>
        createMatch(LayerConstants.PASTE_TOP)
      }
    ),
    (
      "tss\\.pho$".r,
      { m =>
        createMatch(LayerConstants.SILKSCREEN_TOP)
      }
    ),
    (
      "^sm([0-9]+)\\.pho$".r,
      { m =>
        val i = (m.group(1)).find(_ != '0').map(_.toString.toInt)
        if (i.contains(1)) {
          createMatch(LayerConstants.SOLDERMASK_TOP)
        } else {
          createMatch(LayerConstants.SOLDERMASK_BOTTOM)
        }
      }
    ),
    (
      "^smd([0-9]*)\\.pho$".r,
      { m =>
        val i = (m.group(1)).find(_ != '0').map(_.toString.toInt)
        if (i.contains(1)) {
          createMatch(LayerConstants.PASTE_TOP)
        } else {
          createMatch(LayerConstants.PASTE_BOTTOM)
        }
      }
    ),
    (
      "^sst([0-9]*)\\.pho$".r,
      { m =>
        createMatch(LayerConstants.SILKSCREEN_TOP)
      }
    ),
    (
      "^ssb([0-9]*)\\.pho$".r,
      { m =>
        createMatch(LayerConstants.SILKSCREEN_BOTTOM)
      }
    ),

    // another pads alternative
    (
      ".*\\.ccx$".r,
      _ => createMatch(LayerConstants.COPPER_TOP)
    ),
    (
      ".*\\.scx$".r,
      _ => createMatch(LayerConstants.COPPER_BOTTOM)
    ),
    (
      ".*\\.i([0-9]+)x$".r,
      m => createMatch(LayerConstants.COPPER_MID, index = Option(m.group(1)).flatMap(_.toIntOption))
    ),
    (
      ".*\\.oux$".r,
      _ => createMatch(LayerConstants.OUTLINE)
    ),
    (
      ".*\\.csx$".r,
      _ => createMatch(LayerConstants.SILKSCREEN_TOP)
    ),
    (
      ".*\\.ssx$".r,
      _ => createMatch(LayerConstants.SILKSCREEN_BOTTOM)
    ),
    (
      ".*\\.crx$".r,
      _ => createMatch(LayerConstants.SOLDERMASK_TOP)
    ),
    (
      ".*\\.srx$".r,
      _ => createMatch(LayerConstants.SOLDERMASK_BOTTOM)
    ),
    (
      ".*\\.clx$".r,
      _ => createMatch(LayerConstants.PEELABLE_TOP, cat = LayerConstants.Categories.mechanical)
    ),
    (
      ".*\\.slx$".r,
      _ => createMatch(LayerConstants.PEELABLE_BOTTOM, cat = LayerConstants.Categories.mechanical)
    ),
    (
      ".*\\.cpx$".r,
      _ => createMatch(LayerConstants.PASTE_TOP)
    ),
    (
      ".*\\.spx$".r,
      _ => createMatch(LayerConstants.PASTE_BOTTOM)
    )
  )
}
