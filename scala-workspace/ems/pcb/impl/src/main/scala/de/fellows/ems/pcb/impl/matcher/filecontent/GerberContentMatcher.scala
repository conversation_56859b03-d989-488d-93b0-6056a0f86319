package de.fellows.ems.pcb.impl.matcher.filecontent

import de.fellows.ems.gerber.parser.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ars<PERSON>}
import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.{FileMatch, SingleFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.GerberFileTypeDetector
import org.antlr.v4.runtime._
import org.antlr.v4.runtime.tree.ParseTreeWalker

import java.io.{File, FileInputStream}

class GerberContentMatcher(implicit serviceDefinition: ServiceDefinition) extends SingleFileMatcher(false) {
  override def id: String = "content-matcher"

  val service = serviceDefinition.name

  val confidence = FullConfidence

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME))

  override def matchFile(filename: String, file: FilePath, mime: Option[String]): Seq[FileMatch] = {

    val gp = GerberContentMatcher.createGerberParser(new File(file.toPath))

    var sErr = false
    gp.addErrorListener(new BaseErrorListener {
      override def syntaxError(
          recognizer: Recognizer[_, _],
          offendingSymbol: Any,
          line: Int,
          charPositionInLine: Int,
          msg: String,
          e: RecognitionException
      ): Unit = {
        println(s"Error: ${offendingSymbol} @ $line:$charPositionInLine: $msg")
        sErr = true
      }
    })
    val walker = new ParseTreeWalker

    try {

      val collector = new InformationCollector(filename)
      walker.walk(collector, gp.gerber())

      if (collector.infoConfidence > 0) {
        Seq(FileMatch(
          confidence,
          id,
          service = Some(serviceDefinition.name),
          category = collector.category,
          fileType = collector.filetype,
          productionFile = Some(true),
          mimeType = collector.mime,
          index = collector.physicalOrder.map(_.intValue),
          from = collector.from,
          to = collector.to
        ))
      } else {
        if (!sErr) {
          Seq(FileMatch(
            NoConfidence,
            id,
            service = Some(serviceDefinition.name),
            category = LayerConstants.Categories.unknown,
            fileType = Some(LayerConstants.UNKNOWN),
            productionFile = Some(true),
            mimeType = LayerConstants.Mime.gerber
          ))
        } else {
          Seq(FileMatch(
            NoConfidence,
            id,
            service = Some(serviceDefinition.name),
            category = LayerConstants.Categories.unknown,
            fileType = Some(LayerConstants.UNKNOWN),
            productionFile = Some(true),
            mimeType = LayerConstants.Mime.gerber
          ))
        }
      }
    } catch {
      case e: Throwable =>
        e.printStackTrace()
        throw e;
    }

  }
}

object GerberContentMatcher {
  def createGerberParser(f: java.io.File): GerberParser = {
    val stream =
      CharStreams.fromStream(new FileInputStream(f)) // Reads the entire contents [...] then closes the InputStream.
    val l: GerberLexer = new GerberLexer(stream)
    l.removeErrorListeners()
    val ts: CommonTokenStream = new CommonTokenStream(l)
    val parser                = new GerberParser(ts)
    parser.removeErrorListeners()
    parser
  }
}
