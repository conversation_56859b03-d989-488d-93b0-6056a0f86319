package de.fellows.ems.pcb.impl.matcher.filename

import de.fellows.ems.pcb.impl.matcher.Confidence.{HighConfidence, MediumConfidence}
import de.fellows.ems.pcb.impl.matcher.{FileMatch, RegexFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.{ExcellonFileTypeDetector, GerberFileTypeDetector}

import scala.util.matching.Regex
import scala.util.matching.Regex.Match

class ViewMateMatcher(implicit serviceDefinition: ServiceDefinition) extends RegexFileMatcher(false) {

  override def id: String = "viewmate-matcher"

  override def confidence = HighConfidence

  override def service: String = serviceDefinition.name

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME, ExcellonFileTypeDetector.MIME))

  override val patterns: Seq[(Regex, Regex.Match => FileMatch)] =
    Seq[(Regex, Match => FileMatch)](
      ("^fmb$".r, { m => createMatch(LayerConstants.SOLDERMASK_TOP) }),
      ("^fml$".r, { m => createMatch(LayerConstants.SOLDERMASK_BOTTOM) }),

      ("^bes$".r, { m => createMatch(LayerConstants.COPPER_TOP) }),
      ("^loe$".r, { m => createMatch(LayerConstants.COPPER_BOTTOM) }),

      ("^pdb$".r, { m => createMatch(LayerConstants.PASTE_TOP) }),
      ("^pdl$".r, { m => createMatch(LayerConstants.PASTE_BOTTOM) }),

      ("^bdb$".r, { m => createMatch(LayerConstants.SILKSCREEN_TOP) }),
      ("^bdl$".r, { m => createMatch(LayerConstants.SILKSCREEN_BOTTOM) }),

      ("^inf$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
      ("^boh$".r, { m => createMatch(LayerConstants.DRILL, cat = LayerConstants.Categories.mechanical) }),
      ("^bpl$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
      ("^bkf$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
      ("^il([0-9]+)$".r, { m => createMatch(LayerConstants.COPPER_MID, index = Some(m.group(1).toInt)) })
    )
}
