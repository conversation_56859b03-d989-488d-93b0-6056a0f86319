package de.fellows.ems.pcb.impl

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.camunda.bridge.api.CamundaBridgeService
import de.fellows.app.user.api.UserService
import de.fellows.ems.dfm.api.DFMService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.impl.entity.PCBServiceSerializerRegistry
import de.fellows.ems.pcb.impl.entity.pcb.PCBEntity
import de.fellows.ems.pcb.impl.entity.specification.SpecificationEntity
import de.fellows.ems.pcb.impl.queue.PCBEventQueue
import de.fellows.ems.renderer.api.RendererService
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.redislog.RedisLogComponents
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.luminovo.client.customparts.alerts.CustomPartsAlertsService
import de.fellows.luminovo.client.customparts.alerts.CustomPartsAlerts
import de.fellows.utils.health.HealthCheckComponents

class PCBServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new PCBServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new PCBServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[PCBService])
}

abstract class PCBServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with PCBServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)
  //  lazy val securityService = serviceClient.implement[SecurityService]

  lazy val assemblyService                 = serviceClient.implement[AssemblyService]
  lazy val rendererService                 = serviceClient.implement[RendererService]
  lazy val layerstackService               = serviceClient.implement[LayerstackService]
  lazy val dfmService                      = serviceClient.implement[DFMService]
  lazy val panelService                    = serviceClient.implement[PanelService]
  lazy val camunda                         = serviceClient.implement[CamundaBridgeService]
  lazy val userService                     = serviceClient.implement[UserService]
  lazy val luminovoCustomPartAlertsService = serviceClient.implement[CustomPartsAlertsService]

  lazy val luminovoCustomPartAlerts                 = wire[CustomPartsAlerts]
  implicit val pcbEventProcessor: PCBEventProcessor = wire[PCBEventProcessor]
  readSide.register(pcbEventProcessor)

  implicit val specProcessor: SpecificationProcessor = wire[SpecificationProcessor]
  readSide.register(specProcessor)

  lazy val pcbServiceImpl: PCBService = wire[PCBServiceImpl]

  private val queue: PCBEventQueue = wire[PCBEventQueue]
  readSide.register(queue)

  lazy val fileRouter: PCBFileUploadService = wire[PCBFileUploadService].withApp(this)
  override lazy val lagomServer = serverFor[PCBService](pcbServiceImpl)
    .additionalRouter(fileRouter.router)

  implicit val fileChangeListener: AssemblyListener = wire[AssemblyListener]
  implicit val renderListener: RenderListener       = wire[RenderListener]

  //    .additionalRouter(fileRouter.router)

}

trait PCBServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents with RedisLogComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("pcb")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  println(s"ENVIRONMENT IS ${environment}")

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = PCBServiceSerializerRegistry

  lazy val pcbRep  = wire[PCBRepository]
  lazy val specRep = wire[SpecificationRepository]

  //  val pcbEventProcessor = wire[PCBEventProcessor]
  //  readSide.register(pcbEventProcessor)

  //  readSide.register(wire[SessionEventProcessor])

  persistentEntityRegistry.register({
    implicit val conf = configuration.underlying
    wire[PCBEntity]
  })
  persistentEntityRegistry.register(wire[SpecificationEntity])

}

// 2
