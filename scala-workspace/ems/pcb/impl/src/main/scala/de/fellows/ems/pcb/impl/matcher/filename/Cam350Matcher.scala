package de.fellows.ems.pcb.impl.matcher.filename

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.{FileMatch, RegexFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.pcb.model.LayerConstants.Mime
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.{ExcellonFileTypeDetector, GerberFileTypeDetector}

import scala.util.matching.Regex
import scala.util.matching.Regex.Match

class Cam350Matcher(implicit serviceDefinition: ServiceDefinition) extends RegexFileMatcher(false) {
  override def id: String = "cam350-matcher"

  val service = serviceDefinition.name

  override def confidence = MediumConfidence

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME, ExcellonFileTypeDetector.MIME))

  override val patterns = Seq[(Regex, Match => FileMatch)](
    ("^*_ri\\.gbr$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^*_fs\\.gbr$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    (
      "^*_nk\\.gbr$".r,
      { m => createMatch(LayerConstants.NPH_DRILL, Mime.drill, LayerConstants.Categories.mechanical) }
    ),
    ("^*_dk\\.gbr$".r, { m => createMatch(LayerConstants.PH_DRILL, Mime.drill, LayerConstants.Categories.mechanical) }),
    ("^*_ra\\.gbr$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^*_ms\\.gbr$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^*_map\\.gbr$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^*_dd\\.gbr$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^*_fr\\.gbr$".r, { m => createMatch(LayerConstants.OUTLINE, cat = LayerConstants.Categories.mechanical) }),
    ("^*_cm\\.gbr$".r, { m => createMatch(LayerConstants.SOLDERMASK_TOP) }),
    ("^*_cl\\.gbr$".r, { m => createMatch(LayerConstants.COPPER_TOP) }),
    ("^*_m([0-9]+)\\.gbr$".r, { m => createMatch(LayerConstants.COPPER_MID, index = m.group(1).toIntOption) }),
    ("^*_sl\\.gbr$".r, { m => createMatch(LayerConstants.COPPER_BOTTOM) }),
    ("^*_sm\\.gbr$".r, { m => createMatch(LayerConstants.SOLDERMASK_BOTTOM) })
  )
}
