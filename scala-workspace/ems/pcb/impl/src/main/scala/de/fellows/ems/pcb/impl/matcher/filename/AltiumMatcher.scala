package de.fellows.ems.pcb.impl.matcher.filename

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.{FileMatch, SingleFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.pcb.model.LayerConstants.Mime
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.{ExcellonFileTypeDetector, GerberFileTypeDetector}

import scala.util.matching.Regex
import scala.util.matching.Regex.Match

class AltiumMatcher(implicit serviceDefinition: ServiceDefinition) extends SingleFileMatcher(false) {
  override def id: String = "altium-matcher"

  val service    = serviceDefinition.name
  val confidence = MediumConfidence

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME, ExcellonFileTypeDetector.MIME))

  val patterns = Seq[(Regex, Match => FileMatch)](
    (
      "^*\\.gm([0-9]+)$".r,
      { m =>
        createMatch(LayerConstants.MECHANICAL, Mime.gerber, cat = LayerConstants.Categories.mechanical).copy(index =
          Some(m.group(1).toInt)
        )
      }
    ),
    (
      "^*\\.gml$".r,
      { m => createMatch(LayerConstants.MECHANICAL, Mime.gerber, cat = LayerConstants.Categories.mechanical) }
    ),
    (
      "^*\\.gko$".r,
      { m => createMatch(LayerConstants.KEEP_OUT, Mime.gerber, cat = LayerConstants.Categories.mechanical) }
    ),
    ("^*\\.gtp$".r, { m => createMatch(LayerConstants.PASTE_TOP) }),
    // https://chatgpt.com/share/682f2617-3660-8002-be37-04ff7c5f1cce
    //  Top Soldermask Tent layer
    ("^*\\.gpt$".r, { m => createMatch(LayerConstants.PASTE_TOP) }),
    ("^*\\.gbp$".r, { m => createMatch(LayerConstants.PASTE_BOTTOM) }),
    // https://chatgpt.com/share/682f2617-3660-8002-be37-04ff7c5f1cce
    //  Top Soldermask Tent layer
    ("^*\\.gpb$".r, { m => createMatch(LayerConstants.PASTE_BOTTOM) }),
    ("^*\\.gto$".r, { m => createMatch(LayerConstants.SILKSCREEN_TOP) }),
    ("^*\\.gbo$".r, { m => createMatch(LayerConstants.SILKSCREEN_BOTTOM) }),
    ("^*\\.gts$".r, { m => createMatch(LayerConstants.SOLDERMASK_TOP) }),
    ("^*\\.gbs$".r, { m => createMatch(LayerConstants.SOLDERMASK_BOTTOM) }),
    ("^*\\.gtl$".r, { m => createMatch(LayerConstants.COPPER_TOP) }),
    ("^*\\.g([0-9]+)$".r, { m => createMatch(LayerConstants.COPPER_MID).copy(index = Some(m.group(1).toInt)) }),
    ("^*\\.gp([0-9])+$".r, { m => createMatch(LayerConstants.PLANE_MID).copy(index = Some(m.group(1).toInt)) }),
    ("^*\\.gbl$".r, { m => createMatch(LayerConstants.COPPER_BOTTOM) }),
    ("^*\\.gg([0-9]+)$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^*\\.gd([0-9]+)$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^*\\.gm$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),

    // Todo: same as above, combine!

    (
      "^gm([0-9]+)$".r,
      { m =>
        createMatch(LayerConstants.MECHANICAL, Mime.gerber, cat = LayerConstants.Categories.mechanical).copy(index =
          Some(m.group(1).toInt)
        )
      }
    ),
    (
      "^gml$".r,
      { m => createMatch(LayerConstants.MECHANICAL, Mime.gerber, cat = LayerConstants.Categories.mechanical) }
    ),
    ("^gko$".r, { m => createMatch(LayerConstants.KEEP_OUT, Mime.gerber, cat = LayerConstants.Categories.mechanical) }),
    ("^gtp$".r, { m => createMatch(LayerConstants.PASTE_TOP) }),
    ("^gbp$".r, { m => createMatch(LayerConstants.PASTE_BOTTOM) }),
    ("^gto$".r, { m => createMatch(LayerConstants.SILKSCREEN_TOP) }),
    ("^gbo$".r, { m => createMatch(LayerConstants.SILKSCREEN_BOTTOM) }),
    ("^gts$".r, { m => createMatch(LayerConstants.SOLDERMASK_TOP) }),
    ("^gbs$".r, { m => createMatch(LayerConstants.SOLDERMASK_BOTTOM) }),
    ("^gtl$".r, { m => createMatch(LayerConstants.COPPER_TOP) }),
    ("^g([0-9]+)$".r, { m => createMatch(LayerConstants.COPPER_MID).copy(index = Some(m.group(1).toInt)) }),
    ("^gp([0-9])+$".r, { m => createMatch(LayerConstants.PLANE_MID).copy(index = Some(m.group(1).toInt)) }),
    ("^gbl$".r, { m => createMatch(LayerConstants.COPPER_BOTTOM) }),
    ("^gg([0-9]+)$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^gd([0-9]+)$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^dr([0-9]+)$".r, { m => createMatch(LayerConstants.DRILL, cat = LayerConstants.Categories.mechanical) }),
    ("^drl$".r, { m => createMatch(LayerConstants.DRILL, cat = LayerConstants.Categories.mechanical) }),
    ("^vcut$".r, { m => createMatch(LayerConstants.MECHANICAL, cat = LayerConstants.Categories.mechanical) }),
    ("^ko$".r, { m => createMatch(LayerConstants.KEEP_OUT, cat = LayerConstants.Categories.mechanical) })
  )

  override def matchFile(filename: String, f: FilePath, mime: Option[String]): Seq[FileMatch] = {
    val matched = patterns.map { ft =>
      ft._1.findFirstMatchIn(filename.toLowerCase()) match {
        case Some(m) =>
          Some(ft._2(m))
        case None => None
      }
    }

    matched.flatten
  }
}
