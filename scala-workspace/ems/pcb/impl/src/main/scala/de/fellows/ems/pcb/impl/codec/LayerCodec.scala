//package de.fellows.ems.pcb.impl.codec
//
//import java.nio.ByteBuffer
//
//import com.datastax.driver.core.{ProtocolVersion, TypeCodec, UDTValue, UserType}
//import de.fellows.app.assembly.commons.internal.{FilePath, FileType}
//import de.fellows.ems.pcb.impl.entity.pcb.LayerDTO
//import de.fellows.ems.pcb.model.{Format, GerberFile}
//
//class LayerCodec(cdc: TypeCodec[UDTValue]) extends TypeCodec[LayerDTO](cdc.getCqlType, classOf[LayerDTO]) {
//
//  //            typeService text,
//  //            typeCategory text,
//  //            typeType text,
//  //            typeProd boolean,
//  //            typeMime text,
//  //            typeIndex decimal,
//  def to(value: LayerDTO): UDTValue = {
//    if (value == null) null
//    else
//      cdc.getCqlType.asInstanceOf[UserType].newValue()
//        .setUUID("file", value.file.map(_.id).orNull)
//        .setString("name", value.file.map(_.name).orNull)
//        .set("path", value.file.map(_.path).orNull, classOf[FilePath])
//        .set("ftype", value.file.map(_.fType).orNull, classOf[FileType])
//        .set("format", value.file.map(_.format.orNull).orNull, classOf[Format])
//        .setString("material", value.material.orNull)
//        .setDecimal("thickness", value.thickness.map(_.bigDecimal).orNull)
//        .setString("display", value.displayColor.orNull)
//        .setString("manuf", value.manufacturingColor.orNull)
//        .setBool("inverted", value.inverted)
//
//  }
//
//  def getBigDecimal(value: UDTValue, name: String) = {
//    val v: java.math.BigDecimal = value.getDecimal(name)
//
//    v match {
//      case null => None
//      case bd => Some(BigDecimal(bd))
//    }
//  }
//
//  def from(value: UDTValue): LayerDTO = {
//    //    FilePath(value.getString("root"), value.getString("base"), value.getString("name"))
//    if (value == null) null
//    else
//      LayerDTO(
//        value.getUUID("file") match {
//          case null => None
//          case x => Some(GerberFile(
//            x,
//            value.getString("name"),
//            value.get("path", classOf[FilePath]),
//            value.get("fType", classOf[FileType]),
//            Option(value.get("format", classOf[Format])),
//          ))
//        },
//        Option(value.getString("material")),
//        getBigDecimal(value, "thickness"),
//        Option(value.getString("display")),
//        Option(value.getString("manuf")),
//        value.getBool("inverted"),
//      )
//  }
//
//  override def serialize(value: LayerDTO, protocolVersion: ProtocolVersion): ByteBuffer = cdc.serialize(to(value), protocolVersion)
//
//  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): LayerDTO = from(cdc.deserialize(bytes, protocolVersion))
//
//
//  override def parse(value: String): LayerDTO =
//    if (value == null || value.isEmpty) null
//    else from(cdc.parse(value))
//
//  override def format(value: LayerDTO): String =
//    if (value == null) null
//    else cdc.format(to(value))
//}
