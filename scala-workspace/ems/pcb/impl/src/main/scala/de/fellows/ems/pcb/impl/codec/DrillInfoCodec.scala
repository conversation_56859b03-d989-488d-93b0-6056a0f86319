package de.fellows.ems.pcb.impl.codec

import com.datastax.driver.core.{ TypeCodec, UDTValue, UserType }
import de.fellows.ems.pcb.model.DrillInfo
import de.fellows.utils.codec.AbstractCodec
import de.fellows.utils.meta._

class DrillInfoCodec(cdc: TypeCodec[UDTValue]) extends AbstractCodec[DrillInfo](cdc, classOf[DrillInfo]) {
  override def toValue(value: DrillInfo): UDTValue =
    if (value == null) null
    else
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .setDecimal("fromLayer", value.from.map(_.bigDecimal).orNull)
        .setDecimal("toLayer", value.to.map(_.bigDecimal).orNull)
        .set("meta", value.metaInfo.orNull, classOf[MetaInfo])

  override def fromValue(value: UDTValue): DrillInfo =
    if (value == null) null
    else
      DrillInfo(
        from = AbstractCodec.getBigDecimal(value, "fromLayer").map(_.bigDecimal),
        to = AbstractCodec.getBigDecimal(value, "toLayer").map(_.bigDecimal),
        metaInfo = Option(value.get("meta", classOf[MetaInfo]))
      )
}
