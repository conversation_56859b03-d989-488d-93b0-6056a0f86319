package de.fellows.ems.pcb.impl.matcher.filename

import de.fellows.ems.pcb.impl.matcher.Confidence.MediumHighConfidence
import de.fellows.ems.pcb.impl.matcher.{FileMatch, RegexFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.{ExcellonFileTypeDetector, GerberFileTypeDetector}

import scala.util.matching.Regex
import scala.util.matching.Regex.Match

class BoschMatcher(implicit serviceDefinition: ServiceDefinition) extends RegexFileMatcher(false) {

  override def id: String = "b-matcher"

  override def confidence = MediumHighConfidence

  override def service: String = serviceDefinition.name

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME, ExcellonFileTypeDetector.MIME))

  override val patterns: Seq[(Regex, Regex.Match => FileMatch)] = Seq[(Regex, Match => FileMatch)](
    ("conductive[-_ ]?([0-9]+)".r, { m => createMatch(LayerConstants.COPPER_MID, index = m.group(1).toIntOption) }),
    ("conductive[-_ ]?[at]".r, { m => createMatch(LayerConstants.COPPER_TOP) }),
    ("conductive[-_ ]?b".r, { m => createMatch(LayerConstants.COPPER_BOTTOM) }),
    ("resist[-_ ]?[at]".r, { m => createMatch(LayerConstants.SOLDERMASK_TOP) }),
    ("resist[-_ ]?[b]".r, { m => createMatch(LayerConstants.SOLDERMASK_BOTTOM) }),
    ("^sr[-_ ]?bot".r, { m => createMatch(LayerConstants.SOLDERMASK_BOTTOM) }),
    ("^sr[-_ ]?top".r, { m => createMatch(LayerConstants.SOLDERMASK_TOP) }),
    ("^sm[-_ ]?bot".r, { m => createMatch(LayerConstants.SOLDERMASK_BOTTOM) }),
    ("^sm[-_ ]?top".r, { m => createMatch(LayerConstants.SOLDERMASK_TOP) }),
    ("^spt[-_ ]?bot".r, { m => createMatch(LayerConstants.PASTE_BOTTOM) }),
    ("^spt[-_ ]?top".r, { m => createMatch(LayerConstants.PASTE_TOP) }),
    ("^spt[-_ ]?sup[-_ ]?bot".r, { m => createMatch(LayerConstants.PASTE_BOTTOM) }),
    ("^spt[-_ ]?sup[-_ ]?top".r, { m => createMatch(LayerConstants.PASTE_TOP) })
  )
}
