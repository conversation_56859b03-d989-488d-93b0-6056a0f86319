package de.fellows.ems.pcb.impl.entity.pcb

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model._
import de.fellows.utils.SerializedCompressedFile
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.meta._
import play.api.libs.json
import play.api.libs.json.Json

import java.time.Instant
import java.util.UUID

sealed trait PCBCommand

case class GetVersion(team: String, id: UUID) extends PCBCommand with ReplyType[PCBVersion]

case class CloneFromPcbVersion(
    team: String,
    assRef: AssemblyReference,
    originalPCB: PCBVersion
) extends PCBCommand with ReplyType[PCBVersion]

object CloneFromPcbVersion {
  implicit val format: json.Format[CloneFromPcbVersion] = Json.format
}

case class SetVersion(team: String, assRef: AssemblyReference) extends PCBCommand with ReplyType[Done]

case class SetFile(team: String, assRef: AssemblyReference, f: GerberFile) extends PCBCommand
    with ReplyType[GerberFilesResponse]

case class SetFiles(team: String, assRef: AssemblyReference, fs: Seq[GerberFile]) extends PCBCommand
    with ReplyType[GerberFilesResponse]

case class SetFileTypes(team: String, assRef: AssemblyReference, fs: Seq[GerberFile]) extends PCBCommand
    with ReplyType[GerberFilesResponse]

case class UpdateFile(team: String, file: UUID, f: LayerFileUpdate, tcmd: TimelineCommand) extends PCBCommand
    with ReplyType[GerberFile]

case class DeleteFile(team: String, assRef: AssemblyReference, f: UUID) extends PCBCommand with ReplyType[Done]

case class SetFileFormat(team: String, assRef: AssemblyReference, f: GerberFile, dim: Format) extends PCBCommand
    with ReplyType[Done]

case class SetOutlineCommand(
    team: String,
    assRef: AssemblyReference,
    candidate: UUID,
    setByUser: Boolean,
    tcmd: TimelineCommand
) extends PCBCommand with ReplyType[Outline]

case class SetOutlineCandidates(
    team: String,
    assRef: AssemblyReference,
    outlines: Seq[Outline],
    tcmd: TimelineCommand
) extends PCBCommand with ReplyType[Done]
case class AddOutlineCandidates(
    team: String,
    assRef: AssemblyReference,
    outlines: Seq[Outline],
    tcmd: TimelineCommand
) extends PCBCommand with ReplyType[Done]

case class AddUnreconciledDrills(
    team: String,
    assRef: AssemblyReference,
    f: GerberFile,
    drills: SerializedCompressedFile[UnreconciledHoleList]
) extends PCBCommand with ReplyType[Done]

case class AddDrills(team: String, assRef: AssemblyReference, drills: ReconciledSerializedHoleList)
    extends PCBCommand with ReplyType[Done]
case class RemoveDrills(team: String, assembly: UUID, version: UUID)
    extends PCBCommand with ReplyType[Done]

case class SetNetList(team: String, assembly: UUID, version: UUID, netlist: SerializedCompressedFile[NetList])
    extends PCBCommand with ReplyType[Done]

case class AddDrillSets(team: String, version: UUID, sets: Seq[DrillSet]) extends PCBCommand with ReplyType[PCBVersion]

case class FailRender(team: String, assRef: AssemblyReference, file: GerberFile, msg: String) extends PCBCommand
    with ReplyType[Done]

case class SetMetaInfoProperty(
    team: String,
    assRef: AssemblyReference,
    prop: Seq[Property],
    user: Option[UUID],
    tcmd: TimelineCommand
) extends PCBCommand with ReplyType[MetaInfo]

case class RemoveMetaInfoProperty(
    team: String,
    assRef: AssemblyReference,
    prop: Seq[String],
    user: Option[UUID],
    tcmd: TimelineCommand
) extends PCBCommand with ReplyType[MetaInfo]

case class SetFileMetaInfoProperty(
    team: String,
    assRef: AssemblyReference,
    file: UUID,
    prop: Seq[Property],
    user: Option[UUID]
) extends PCBCommand with ReplyType[MetaInfo]

case class RemoveFileMetaInfoProperty(
    team: String,
    assRef: AssemblyReference,
    file: UUID,
    prop: Seq[String],
    user: Option[UUID]
) extends PCBCommand with ReplyType[MetaInfo]

case class SetMetaInfoFormat(team: String, assRef: AssemblyReference, f: Format) extends PCBCommand
    with ReplyType[MetaInfo]

case class UpdatePCB(team: String, update: PCBUpdate, tcmd: TimelineCommand) extends PCBCommand
    with ReplyType[PCBVersion]

case class FinishFileMatching(team: String, assRef: AssemblyReference, stack: Seq[GerberFile]) extends PCBCommand
    with ReplyType[Done]

case class AddSpecification(team: String, specification: UUID, default: Boolean, tcmd: TimelineCommand)
    extends PCBCommand with ReplyType[Done]

case class RemoveSpecification(team: String, specification: UUID, tcmd: TimelineCommand) extends PCBCommand
    with ReplyType[Done]

case class GerberFilesResponse(response: Seq[GerberFile])

case class MetaInfoResponse(response: Option[MetaInfo])

object GerberFilesResponse {
  implicit val format: json.Format[GerberFilesResponse] = Json.format
}

object MetaInfoResponse {
  implicit val format: json.Format[MetaInfoResponse] = Json.format
}

object AddDrillSets {
  implicit val format: json.Format[AddDrillSets] = Json.format
}

object FailRender {
  implicit val format: json.Format[FailRender] = Json.format
}

object SetVersion {
  implicit val format: json.Format[SetVersion] = Json.format
}

object GetVersion {
  implicit val format: json.Format[GetVersion] = Json.format
}

object SetFile {
  implicit val format: json.Format[SetFile] = Json.format
}

object SetFiles {
  implicit val format: json.Format[SetFiles] = Json.format
}

object SetFileTypes {
  implicit val format: json.Format[SetFileTypes] = Json.format
}

object UpdateFile {
  implicit val format: json.Format[UpdateFile] = Json.format
}

object SetFileFormat {
  implicit val format: json.Format[SetFileFormat] = Json.format
}

object SetOutlineCommand {
  implicit val format: json.Format[SetOutlineCommand] = Json.format
}

object SetOutlineCandidates {
  implicit val format: json.Format[SetOutlineCandidates] = Json.format
}
object AddOutlineCandidates {
  implicit val format: json.Format[AddOutlineCandidates] = Json.format
}

object DeleteFile {
  implicit val format: json.Format[DeleteFile] = Json.format
}

object AddDrills {
  implicit val format: json.Format[AddDrills] = Json.format
}

object SetNetList {
  implicit val format: json.Format[SetNetList] = Json.format
}
object RemoveDrills {
  implicit val format: json.Format[RemoveDrills] = Json.format
}

object AddUnreconciledDrills {
  implicit val format: json.Format[AddUnreconciledDrills] = Json.format
}
object FinishFileMatching {
  implicit val format: json.Format[FinishFileMatching] = Json.format
}

object SetMetaInfoProperty {
  implicit val format: json.Format[SetMetaInfoProperty] = Json.format
}

object RemoveMetaInfoProperty {
  implicit val format: json.Format[RemoveMetaInfoProperty] = Json.format
}

object SetMetaInfoFormat {
  implicit val format: json.Format[SetMetaInfoFormat] = Json.format
}

object SetFileMetaInfoProperty {
  implicit val format: json.Format[SetFileMetaInfoProperty] = Json.format
}

object RemoveFileMetaInfoProperty {
  implicit val format: json.Format[RemoveFileMetaInfoProperty] = Json.format
}

object AddSpecification {
  implicit val format: json.Format[AddSpecification] = Json.format
}

object RemoveSpecification {
  implicit val format: json.Format[RemoveSpecification] = Json.format
}

object UpdatePCB {
  implicit val format: json.Format[UpdatePCB] = Json.format
}
