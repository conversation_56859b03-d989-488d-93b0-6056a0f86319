package de.fellows.ems.pcb.impl.matcher.filename

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.{FileMatch, SingleFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.pcb.model.LayerConstants.Mime
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.spi.{ExcellonFileTypeDetector, GerberFileTypeDetector}
import de.fellows.utils.FilePath

class DrillFileNameMatcher(implicit serviceDefinition: ServiceDefinition) extends SingleFileMatcher(false) {
  val id      = "drill-filename-matcher"
  val service = serviceDefinition.name

  override def mime: Option[Seq[String]] = Some(Seq(GerberFileTypeDetector.MIME, ExcellonFileTypeDetector.MIME))

  override def matchFile(filename: String, f: FilePath, mime: Option[String]): Seq[FileMatch] = {
    val name = filename.toLowerCase()

    if (isDrillFile(name)) {
      createDrillMatch(name)
    } else {
      Seq()
    }
  }

  private def isDrillFile(name: String): Boolean = {
    // Check for drill-related keywords
    val drillKeywords   = Seq("drill", "hole")
    val drillExtensions = Seq(".drd", ".drl", ".txt")

    // Check for drill keywords (but exclude drawing files)
    val hasDrillKeyword = drillKeywords.exists(name.contains) && !name.contains("drawing")

    // Check for drill file extensions
    val hasDrillExtension = drillExtensions.exists(name.endsWith)

    // Check for milling (which is also drill-related)
    val isMilling = name.contains("milling")

    // Check for drill parameter files
    val isDrillParams = name.endsWith(".txt") && name.contains("nc_param")

    hasDrillKeyword || hasDrillExtension || isMilling || isDrillParams
  }

  private def createDrillMatch(name: String): Seq[FileMatch] = {
    // High confidence if filename explicitly contains "drill"
    val confidence =
      if (name.contains("drill")) {
        10 // Very high confidence for explicit drill files
      } else if (name.contains("hole") || name.contains("milling")) {
        8 // High confidence for hole/milling files
      } else if (name.endsWith(".drd") || name.endsWith(".drl")) {
        9 // Very high confidence for drill extensions
      } else {
        MediumConfidence // Medium confidence for other drill-related patterns
      }

    if (name.endsWith(".txt") && name.contains("nc_param")) {
      // Drill parameter files
      Seq(createMatch(
        LayerConstants.DRILL_PARAMETERS,
        cat = LayerConstants.Categories.mechanical,
        mime = Some("text/drillparams"),
        conf = Some(confidence)
      ))
    } else if (name.contains("npth")) {
      Seq(createMatch(
        LayerConstants.NPH_DRILL,
        Mime.gerber,
        LayerConstants.Categories.mechanical,
        conf = Some(confidence + 1)
      ))
    } else if (name.contains("pth")) {
      Seq(createMatch(
        LayerConstants.PH_DRILL,
        Mime.gerber,
        LayerConstants.Categories.mechanical,
        conf = Some(confidence + 1)
      ))
    } else {
      // Regular drill files
      Seq(createMatch(
        LayerConstants.DRILL,
        Mime.gerber,
        LayerConstants.Categories.mechanical,
        conf = Some(confidence)
      ))
    }
  }

  override def confidence: Int = MediumConfidence
}
