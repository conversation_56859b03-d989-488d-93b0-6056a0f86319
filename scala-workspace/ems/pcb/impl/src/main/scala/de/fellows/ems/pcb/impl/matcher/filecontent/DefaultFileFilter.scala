package de.fellows.ems.pcb.impl.matcher.filecontent

import de.fellows.utils.internal.FileReader.withResource
import de.fellows.utils.{FilePath, PathUtils}
import play.api.Logging

import java.io.FileInputStream
import scala.util.{Failure, Success}

class DefaultFileFilter extends Logging {

  val BINARY_READ_LIMIT = 20

  def isPrintable(next: Int): Boolean = {

    val correctAscii       = (next <= 128) && (next >= 7)
    val extendedAsciiUpper = (next >= 192 && next <= 221 && next != 215)
    val extendedAsciiLower = (next >= 223 && next <= 255 && next != 247)

    correctAscii || extendedAsciiLower || extendedAsciiUpper

  }

  def isBinary(is: FileInputStream): Option[String] = {
    var index = 0
    while (is.available() > 0 && index <= BINARY_READ_LIMIT) {
      val next = is.read()
      if (!isPrintable(next)) {
        return Some(s"found character ${next.toHexString}: ${next.asInstanceOf[Char]}")
      }
      index += 1
    }

    return None
  }

  def isBinary(file: FilePath, filename: String): Option[String] = {
    val jf = file.toJavaFile
    if (jf.exists()) {
      if (jf.isFile) {
        withResource(new FileInputStream(jf)) { is =>
          val bin = isBinary(is)
          if (bin.isDefined) {
            Some(s"File is Binary: ${bin.get}")
          } else {
            None
          }
        }
      } else {
        Some("File is a directory")
      }
    } else {
      Some("File does not exist")
    }
  }

  def getMimeType(file: FilePath, filename: String): Option[String] = {

    val path = file.toJavaPath
    if (path.toFile.isFile) {
      PathUtils.probeContentType(path) match {
        case Failure(exception) =>
          logger.error(exception.getMessage, exception)
          None
        case Success(value) => value
      }
    } else {
      Some("directory")
    }
  }

  def valid(file: FilePath, filename: String): Option[String] = {
    val jf = file.toJavaFile

    if (filename.endsWith(".zip")) {
      Some("zip files can not be matched")
    } else {
      None
    }
  }
}
