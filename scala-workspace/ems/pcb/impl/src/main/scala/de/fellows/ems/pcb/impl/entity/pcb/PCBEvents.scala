package de.fellows.ems.pcb.impl.entity.pcb

import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.api.{
  ClonedPCBVersionMessage,
  DFMMessage,
  DrillSetsAddedMessage,
  DrillsAddedMessage,
  FileMessage,
  MetaInfoMessage,
  OutlineSetMessage,
  OutlinesSetMessage,
  PCBStreamEvent,
  PCBTimelineMessage,
  PCBUpdatedMessage,
  RenderAddedMessage,
  RenderChangeMessage
}
import de.fellows.ems.pcb.model._
import de.fellows.utils.collaboration.TimelineEvent
import de.fellows.utils.internal.FileType
import de.fellows.utils.meta._
import de.fellows.utils.streams.{EmptyMessage, StreamMessage, ValidMessage}
import de.fellows.utils.{FilePath, SerializedCompressedFile}
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

sealed trait PCBEvent extends AggregateEvent[PCBEvent] {
  override def aggregateTag = PCBEvent.Tag
  def toStreamEvent: StreamMessage[PCBStreamEvent]
}

sealed trait EmptyMessageEvent extends PCBEvent {
  override def toStreamEvent: StreamMessage[PCBStreamEvent] = EmptyMessage()
}

object PCBEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[PCBEvent](NumShards)
}

case class VersionSet(assRef: AssemblyReference) extends EmptyMessageEvent

case class PcbVersionSet(
    pcbVersion: PCBVersion,
    assRef: AssemblyReference,
    clonedFrom: AssemblyReference
) extends PCBEvent {
  override def toStreamEvent: StreamMessage[PCBStreamEvent] =
    ValidMessage(
      ClonedPCBVersionMessage(
        assRef = assRef,
        origAssRef = clonedFrom
      )
    )

}

object PcbVersionSet {
  implicit val format: Format[PcbVersionSet] = Json.format
}

@deprecated
case class FileSet(assRef: AssemblyReference, f: GerberFile, files: Seq[GerberFile]) extends PCBEvent {
  override def toStreamEvent: ValidMessage[PCBStreamEvent] = ValidMessage(FileMessage(assRef, Seq(f)))
}

case class FilesSet(assRef: AssemblyReference, f: Seq[GerberFile], files: Seq[GerberFile]) extends PCBEvent {
  override def toStreamEvent: ValidMessage[PCBStreamEvent] = ValidMessage(FileMessage(assRef, f))
}

case class FileTypesChanged(assRef: AssemblyReference, f: Seq[GerberFile], files: Seq[GerberFile]) extends PCBEvent {
  override def toStreamEvent: ValidMessage[PCBStreamEvent] = ValidMessage(FileMessage(assRef, f))
}

case class UnreconciledDrillsAdded(
    assRef: AssemblyReference,
    f: GerberFile,
    file: SerializedCompressedFile[UnreconciledHoleList]
) extends EmptyMessageEvent

case class UnreconciledDrillsSet(assRef: AssemblyReference, unreconciledHoles: Seq[UnreconciledSerializedHoleList])
    extends PCBEvent with EmptyMessageEvent

case class ReconciledDrillsAdded(
    assRef: AssemblyReference,
    holes: ReconciledSerializedHoleList
) extends EmptyMessageEvent {}

case class NetListSet(
    assRef: AssemblyReference,
    nets: SerializedCompressedFile[NetList]
) extends EmptyMessageEvent {}

case class ReconciledDrillsRemoved(
    assRef: AssemblyReference
) extends EmptyMessageEvent {}

case class DrillsAdded(
    assRef: AssemblyReference,
    f: GerberFile,
    holes: FilePath,
    updated: Seq[DrillFile],
    info: Option[DrillInfo]
) extends PCBEvent {
  override def toStreamEvent = ValidMessage(DrillsAddedMessage(assRef, f, holes))
}

case class DrillSetsAdded(assRef: AssemblyReference, added: Seq[DrillSet], allSets: Seq[DrillSet]) extends PCBEvent {
  override def toStreamEvent = ValidMessage(DrillSetsAddedMessage(assRef, added))
}

case class FileDeleted(assRef: AssemblyReference, f: GerberFile, files: Seq[GerberFile]) extends EmptyMessageEvent

case class FileDimensionSet(assRef: AssemblyReference, f: GerberFile, currentFiles: Option[Seq[GerberFile]] = None)
    extends EmptyMessageEvent

case class MetaInfoSet(assRef: AssemblyReference, f: MetaInfo, changes: Option[MetaInfo], user: Option[UUID])
    extends PCBEvent {
  override def toStreamEvent = ValidMessage(MetaInfoMessage(assRef, f, changes))
}

case class FileMetaInfoSet(
    assRef: AssemblyReference,
    file: GerberFile,
    f: MetaInfo,
    changes: Option[MetaInfo],
    user: Option[UUID]
) extends EmptyMessageEvent

@deprecated
case class OutlineSet(assRef: AssemblyReference, f: GerberFile, setByUser: Boolean = true) extends PCBEvent {
  override def toStreamEvent = EmptyMessage() // ValidMessage(OutlineSetMessage(assRef, f))
}

case class OutlineCandidateSet(
    assRef: AssemblyReference,
    candidate: Outline
) extends PCBEvent {
  override def toStreamEvent = ValidMessage(OutlineSetMessage(assRef, candidate))
}

case class OutlineCandidatesSet(
    assRef: AssemblyReference,
    candidates: Seq[Outline]
) extends PCBEvent {
  override def toStreamEvent = ValidMessage(OutlinesSetMessage(assRef, candidates))
}

case class RenderAdded(assRef: AssemblyReference, file: GerberFile, newRender: Map[String, FilePath], time: Instant)
    extends PCBEvent {
  override def toStreamEvent = ValidMessage(RenderAddedMessage(assRef, file, newRender))
}

case class FileTypesSet(assRef: AssemblyReference, files: Seq[GerberFile]) extends EmptyMessageEvent

case class PCBTimelineChanged(evt: TimelineEvent) extends PCBEvent {
  override def toStreamEvent = ValidMessage(PCBTimelineMessage(evt))
}

case class AnalysisStateChanged(assRef: AssemblyReference, status: MetaStatus, meta: Option[MetaInfo])
    extends PCBEvent {
  override def toStreamEvent = ValidMessage(DFMMessage(assRef, status))
}

case class SpecificationsChanged(assRef: AssemblyReference, specifications: Seq[UUID], default: Option[UUID] = None)
    extends EmptyMessageEvent

case class PCBUpdated(pcb: PCBVersion, update: PCBUpdate) extends PCBEvent {
  override def toStreamEvent = ValidMessage(PCBUpdatedMessage(pcb.assembly.get, update))
}

case class DefaultSpecificationChanged(assRef: AssemblyReference, newSpec: UUID, oldSpec: Option[UUID])
    extends EmptyMessageEvent

case class RenderStateChanged(
    ref: AssemblyReference = AssemblyReference("", new UUID(0, 0), None, new UUID(0, 0)),
    files: Seq[GerberFile]
) extends PCBEvent {
  override def toStreamEvent = {
    val (unmatched, matched) = files.partition(gf => gf.fType == FileType.UNMATCHED)
    val (unknown, known)     = matched.partition(gf => gf.fType == FileType.UNKNOWN)
    val (result, noResult)   = files.partition(_.renderinfo.isDefined)
    val (rendered, failed)   = result.partition(_.renderinfo.get.status == RenderInfo.NAME_SUCCESS)

    ValidMessage(RenderChangeMessage(ref, files, matched, unmatched, known, unknown, rendered, failed))
  }
}

object DrillSetsAdded {
  implicit val format: Format[DrillSetsAdded] = Json.format
}

object MetaInfoSet {
  implicit val format: Format[MetaInfoSet] = Json.format
}

object FileMetaInfoSet {
  implicit val format: Format[FileMetaInfoSet] = Json.format
}

object VersionSet {
  implicit val format: Format[VersionSet] = Json.format
}

object FileSet {
  implicit val format: Format[FileSet] = Json.format
}

object FilesSet {
  implicit val format: Format[FilesSet] = Json.format
}

object FileDimensionSet {
  implicit val format: Format[FileDimensionSet] = Json.format
}

object OutlineSet {
  implicit val format: Format[OutlineSet] = Json.format
}

object OutlineCandidateSet {
  implicit val format: Format[OutlineCandidateSet] = Json.format
}

object OutlineCandidatesSet {
  implicit val format: Format[OutlineCandidatesSet] = Json.format
}

object RenderAdded {
  implicit val format: Format[RenderAdded] = Json.format
}

object FileDeleted {
  implicit val format: Format[FileDeleted] = Json.format
}

object DrillsAdded {
  implicit val format: Format[DrillsAdded] = Json.format
}

object ReconciledDrillsAdded {
  implicit val format: Format[ReconciledDrillsAdded] = Json.format
}
object NetListSet {
  implicit val format: Format[NetListSet] = Json.format
}
object ReconciledDrillsRemoved {
  implicit val format: Format[ReconciledDrillsRemoved] = Json.format
}

object UnreconciledDrillsAdded {
  implicit val format: Format[UnreconciledDrillsAdded] = Json.format
}

object UnreconciledDrillsSet {
  implicit val format: Format[UnreconciledDrillsSet] = Json.format
}

object FileTypesSet {
  implicit val format: Format[FileTypesSet] = Json.format
}

object PCBTimelineChanged {
  implicit val format: Format[PCBTimelineChanged] = Json.format
}

object AnalysisStateChanged {
  implicit val format: Format[AnalysisStateChanged] = Json.format
}

object SpecificationsChanged {
  implicit val format: Format[SpecificationsChanged] = Json.format
}

object PCBUpdated {
  implicit val format: Format[PCBUpdated] = Json.format
}

object RenderStateChanged {
  implicit val format: Format[RenderStateChanged] = Json.format
}

object DefaultSpecificationChanged {
  implicit val format: Format[DefaultSpecificationChanged] = Json.format
}

object FileTypesChanged {
  implicit val format: Format[FileTypesChanged] = Json.format
}
