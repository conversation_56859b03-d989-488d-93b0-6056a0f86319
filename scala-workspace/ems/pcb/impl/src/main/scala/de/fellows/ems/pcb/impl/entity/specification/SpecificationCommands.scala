package de.fellows.ems.pcb.impl.entity.specification

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.{PCBSpecification, SpecificationStatus}
import de.fellows.utils.FilePath
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.meta._
import play.api.libs.json.{Format, Json}

import java.util.UUID

object SpecificationCommands {

  sealed trait SpecificationCommand

  case class GetSpecification(team: String, id: UUID) extends SpecificationCommand with ReplyType[PCBSpecification]

  case class CreateSpecification(team: String, ass: AssemblyReference, s: PCBSpecification, tcmd: TimelineCommand)
      extends SpecificationCommand with ReplyType[PCBSpecification]

  case class SetDFMProperties(team: String, id: UUID, props: Seq[Property], tcmd: TimelineCommand)
      extends SpecificationCommand with ReplyType[PCBSpecification]

  case class SetSettingProperties(
      team: String,
      id: UUID,
      props: Option[Seq[Property]],
      remove: Option[Seq[Property]],
      tcmd: TimelineCommand
  ) extends SpecificationCommand with ReplyType[PCBSpecification]

  case class SetUserProperties(team: String, id: UUID, props: Seq[Property], tcmd: TimelineCommand)
      extends SpecificationCommand with ReplyType[PCBSpecification]

  case class SetSpecificationStatus(
      team: String,
      id: UUID,
      status: SpecificationStatus,
      info: CollaborativeEventInfo,
      tcmd: TimelineCommand
  ) extends SpecificationCommand with ReplyType[PCBSpecification]

  object SetSpecificationStatus {
    implicit val format: Format[SetSpecificationStatus] = Json.format[SetSpecificationStatus]
  }

  case class RemoveSpecification(team: String, id: UUID, tcmd: TimelineCommand) extends SpecificationCommand
      with ReplyType[Done]

  case class SaveSpecification(
      team: String,
      id: UUID,
      base: Option[MetaInfo],
      info: Option[CollaborativeEventInfo],
      tcmd: TimelineCommand
  ) extends SpecificationCommand with ReplyType[PCBSpecification]

  case class SetSpecificationPreview(team: String, id: UUID, path: FilePath, rear: Option[Boolean])
      extends SpecificationCommand with ReplyType[PCBSpecification]

  object GetSpecification {
    implicit val f: Format[GetSpecification] = Json.format[GetSpecification]
  }

  object CreateSpecification {
    implicit val f: Format[CreateSpecification] = Json.format[CreateSpecification]
  }

  object SetDFMProperties {
    implicit val f: Format[SetDFMProperties] = Json.format[SetDFMProperties]
  }

  object SetSettingProperties {
    implicit val f: Format[SetSettingProperties] = Json.format[SetSettingProperties]
  }

  object RemoveSpecification {
    implicit val f: Format[RemoveSpecification] = Json.format[RemoveSpecification]
  }

  object SaveSpecification {
    implicit val f: Format[SaveSpecification] = Json.format[SaveSpecification]
  }

  object SetSpecificationPreview {
    implicit val f: Format[SetSpecificationPreview] = Json.format[SetSpecificationPreview]
  }

  object SetUserProperties {
    implicit val f: Format[SetUserProperties] = Json.format[SetUserProperties]
  }

}
