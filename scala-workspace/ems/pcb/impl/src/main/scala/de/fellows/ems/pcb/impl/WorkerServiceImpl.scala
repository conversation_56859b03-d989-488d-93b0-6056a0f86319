//package de.fellows.ems.pcb.impl
//
//import akka.actor.ActorSystem
//import akka.cluster.Cluster
//import akka.cluster.routing.{ClusterRouterGroup, ClusterRouterGroupSettings}
//import akka.routing.ConsistentHashingGroup
//import akka.util.Timeout
//import com.lightbend.lagom.scaladsl.api.ServiceCall
//import org.jboss.netty.channel.socket.Worker
//
//class WorkerServiceImpl(system: ActorSystem) extends WorkerService {
//  if (Cluster.get(system).selfRoles("worker-node")) {
//    // start a worker actor on each node that has the "worker-node" role
//    system.actorOf(Worker.props, "worker")
//  }
//
//  // start a consistent hashing group router,
//  // which will delegate jobs to the workers. It is grouping
//  // the jobs by their task, i.e. jobs with same task will be
//  // delegated to same worker node
//  val workerRouter = {
//    val paths = List("/user/worker")
//    val groupConf = ConsistentHashingGroup(paths, hashMapping = {
//      case Job(_, task, _) => task
//    })
//    val routerProps = ClusterRouterGroup(
//      groupConf,
//      ClusterRouterGroupSettings(
//        totalInstances = 1000,
//        routeesPaths = paths,
//        allowLocalRoutees = true,
//        useRoles = Set("worker-node")
//      )
//    ).props
//    system.actorOf(routerProps, "workerRouter")
//  }
//
//  def doWork = ServiceCall { job =>
//    implicit val timeout = Timeout(5.seconds)
//    (workerRouter ? job).mapTo[JobAccepted]
//  }
//}
