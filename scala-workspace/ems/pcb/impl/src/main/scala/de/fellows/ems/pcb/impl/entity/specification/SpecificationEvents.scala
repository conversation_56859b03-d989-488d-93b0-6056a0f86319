package de.fellows.ems.pcb.impl.entity.specification

import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.PCBSpecification
import de.fellows.utils.FilePath
import de.fellows.utils.collaboration.TimelineEvent
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.meta._
import play.api.libs.json.{Format, Json}

import java.util.UUID

object SpecificationEvents {

  sealed trait SpecificationEvent extends AggregateEvent[SpecificationEvent] {
    override def aggregateTag = SpecificationEvent.Tag
  }

  object SpecificationEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[SpecificationEvent](NumShards)
  }

  case class SpecificationTimelineChanged(evt: TimelineEvent) extends SpecificationEvent

  case class SpecificationCreated(ass: AssemblyReference, s: PCBSpecification) extends SpecificationEvent

  case class DFMPropertySet(oldProperties: Seq[Property], spec: PCBSpecification) extends SpecificationEvent

  case class SettingPropertySet(oldProperties: Seq[Property], spec: PCBSpecification) extends SpecificationEvent

  case class UserPropertySet(oldProperties: Seq[Property], spec: PCBSpecification) extends SpecificationEvent

  case class SpecificationStatusSet(spec: PCBSpecification) extends SpecificationEvent

  object SpecificationStatusSet {
    implicit val format: Format[SpecificationStatusSet] = Json.format[SpecificationStatusSet]
  }

  case class SpecificationRemoved(ass: AssemblyReference, id: UUID, alias: String) extends SpecificationEvent

  case class SpecificationSaved(
      spec: PCBSpecification,
      base: Option[MetaInfo] = None,
      info: Option[CollaborativeEventInfo]
  ) extends SpecificationEvent

  case class SpecificationPreviewChanged(spec: PCBSpecification, preview: FilePath, rear: Option[Boolean] = Some(false))
      extends SpecificationEvent

  object SpecificationCreated {
    implicit val f: Format[SpecificationCreated] = Json.format[SpecificationCreated]
  }

  object SpecificationTimelineChanged {
    implicit val f: Format[SpecificationTimelineChanged] = Json.format[SpecificationTimelineChanged]
  }

  object DFMPropertySet {
    implicit val f: Format[DFMPropertySet] = Json.format[DFMPropertySet]
  }

  object SettingPropertySet {
    implicit val f: Format[SettingPropertySet] = Json.format[SettingPropertySet]
  }

  object UserPropertySet {
    implicit val f: Format[UserPropertySet] = Json.format[UserPropertySet]
  }

  object SpecificationRemoved {
    implicit val f: Format[SpecificationRemoved] = Json.format[SpecificationRemoved]
  }

  object SpecificationSaved {
    implicit val f: Format[SpecificationSaved] = Json.format[SpecificationSaved]
  }

  object SpecificationPreviewChanged {
    implicit val f: Format[SpecificationPreviewChanged] = Json.format[SpecificationPreviewChanged]
  }

}
