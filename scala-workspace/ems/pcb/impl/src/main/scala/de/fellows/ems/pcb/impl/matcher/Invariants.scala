package de.fellows.ems.pcb.impl.matcher

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.pcb.model.LayerConstants.{COPPER_BOTTOM, COPPER_MID, COPPER_TOP}
import de.fellows.utils.FilePath
import de.fellows.utils.logging.StackrateLogging
import play.api.libs.json.Json

/** enforces invariants in the list of all file types.
  *
  * Takes the current best choice, and can return a new set of choices to work with. If None, everything is fine.
  *
  * @tparam X
  */
trait FileTypeInvariant[X] {
  def resolve(state: Map[X, FileMatchChoice], getFilePath: X => FilePath): Option[Map[X, Seq[FileMatch]]]
}

class GerberProjectCountLimit[X] extends FileTypeInvariant[X] with StackrateLogging {

  override def resolve(state: Map[X, FileMatchChoice], getFilePath: X => FilePath): Option[Map[X, Seq[FileMatch]]] = {

    val foldersWithCopper = state.flatMap { x =>
      if (
        x._2.chosen.flatMap(_.fileType).exists(t => LayerConstants.COPPER.contains(t)) && x._2.chosen.flatMap(
          _.category
        ) == LayerConstants.Categories.gerber
      ) {
        Some(getFilePath(x._1).toJavaPath.getParent)
      } else {
        None
      }
    }.toSet

    if (foldersWithCopper.size > 1) {
      // group state by parent folder
      val groupedState = state
        .groupBy { x =>
          getFilePath(x._1).toJavaPath.getParent
        }

      val foldersToRemove = foldersWithCopper.toSeq
        // sort the folders by average confidence of the gerber files
        .sortBy { path =>
          val gerberFilesOfThisFolder =
            groupedState(path).flatMap(_._2.chosen).filter(x => x.category == LayerConstants.Categories.gerber)

          // average confidence:
          gerberFilesOfThisFolder.map(_.confidence).sum / gerberFilesOfThisFolder.size
        }
        .reverse
        .drop(1)

      Some(
        state.map { x =>
          val path = getFilePath(x._1).toJavaPath
          if (foldersToRemove.contains(path.getParent)) {
            x._1 -> x._2.chosen.toSeq.map(_.copy(
              fileType = Some(LayerConstants.UNKNOWN)
            ))
          } else {
            x._1 -> x._2.options
          }
        }
      )
    } else {
      None
    }
  }
}

/** restricts the type of project to either gerber or ODB. if there are multiple types of projects, the others will end up as unknown files
  * @param preferGerber
  * @tparam X
  */
class ProjectTypeLimit[X](preferGerber: Boolean = true) extends FileTypeInvariant[X] {
  override def resolve(state: Map[X, FileMatchChoice], getFilePath: X => FilePath): Option[Map[X, Seq[FileMatch]]] = {

    val allMatches = state.flatMap(_._2.chosen)

    if (
      allMatches.exists(c => c.category == LayerConstants.Categories.odb) &&
      allMatches.exists(c => c.category == LayerConstants.Categories.gerber)
    ) {
      // We have both gerber and odb projects.

      val categoryToRemove =
        if (preferGerber) {
          LayerConstants.Categories.odb
        } else {
          LayerConstants.Categories.gerber
        }

      Some(state.map { s =>
        if (s._2.chosen.exists(_.category == categoryToRemove)) {
          s._1 ->
            s._2.chosen.toSeq.map(_.copy(
              fileType = Some(LayerConstants.UNKNOWN)
            ))

        } else {
          s._1 -> s._2.options
        }
      })

    } else {
      // We only have one type of project
      None
    }

  }
}

/** Only allows for a limited number of files of the same file type. If there are more it removes the option, which
  * causes the matcher to choose the next best option.
  */
class SimpleFileTypeLimit[X](fileType: String, limit: Int) extends FileTypeInvariant[X] {
  override def resolve(state: Map[X, FileMatchChoice], getFilePath: X => FilePath): Option[Map[X, Seq[FileMatch]]] = {

    val count = state.flatMap(_._2.chosen.flatMap(_.fileType)).count(_ == fileType)
    if (count > limit) {
      // the limit is exceeded, ban least likely

      val toBan = count - limit

      val sorted =
        state.toSeq.sortBy(
          _._2.options.find(_.fileType.contains(fileType)).map(_.confidence).getOrElse(Integer.MAX_VALUE)
        ) // lowest confidence in front
      val s = sorted.splitAt(toBan)

      Some((s._1.map { m =>
        m._1 -> m._2.options.filterNot(fm => fm.fileType.contains(fileType))
      } ++ s._2.map(x => x._1 -> x._2.options)).toMap)
    } else {
      None
    }

  }
}

/** This invariant only allows one top copper and one bottom copper. If there are more than 1 each, it chooses one, and
  * forces the others to be copper mids
  *
  * @tparam X
  */
class InnerOuterCopperCount[X] extends FileTypeInvariant[X] {

  def changeFiletype(
      state: Map[X, Seq[FileMatch]],
      files: Seq[X],
      toType: String,
      fromType: Option[String] = None
  ): Map[X, Seq[FileMatch]] =
    state.map {
      case x if files.contains(x._1) =>
        x._1 -> x._2.map {
          case ftype if fromType.isEmpty || ftype.fileType == fromType =>
            ftype.copy(fileType = Some(toType))
          case ftype => ftype
        }
      case x => x
    }

  def handle(
      state: Map[X, Seq[FileMatch]],
      relevantChoices: Map[X, FileMatchChoice],
      fileType: String
  ): Option[Map[X, Seq[FileMatch]]] =
    if (relevantChoices.size > 1) {
      val changed = changeFiletype(state, relevantChoices.keys.toSeq, COPPER_MID)
      if (changed != state) {
        Some(changed)
      } else {
        None
      }

    } else {
      None
    }

  override def resolve(ostate: Map[X, FileMatchChoice], getFilePath: X => FilePath): Option[Map[X, Seq[FileMatch]]] = {
    // find coppers with the same index and only use the copper with best confidence
    val (copper, noncopper) = ostate.partition(x => x._2.chosen.flatMap(_.fileType).exists(LayerConstants.COPPER.contains))
    val filtered = copper.groupBy(x => {
      x._2.chosen.flatMap(c => (c.index))
    }).flatMap(group => {
      if(group._1.nonEmpty){
        val (bestChoice, rest) = group._2.toSeq.sortBy(_._2.chosen.map(_.confidence).getOrElse(NoConfidence)).reverse.splitAt(1)

        bestChoice.toMap ++ rest.map(x => {
          val newChosen=x._2.chosen.map(m => m.copy(fileType = Some(LayerConstants.UNKNOWN)))
          x._1 -> x._2.copy(
            chosen = newChosen,
            options = newChosen.toSeq
          )
        })
      }else{
        group._2
      }

    }) ++ noncopper

    // get all copper top and bottom options
    val top: Seq[(X, FileMatchChoice)] =
      filtered.filter(x => x._2.chosen.flatMap(_.fileType).contains(LayerConstants.COPPER_TOP))
        .toSeq
        .sortBy(_._2.chosen.map(_.confidence).getOrElse(NoConfidence))
        .reverse
    val bottom: Seq[(X, FileMatchChoice)] =
      filtered.filter(x => x._2.chosen.flatMap(_.fileType).contains(LayerConstants.COPPER_BOTTOM))
        .toSeq
        .sortBy(_._2.chosen.map(_.confidence).getOrElse(NoConfidence))
        .reverse

    val (restWithIndex, restWithoutIndex) = filtered.filter { x =>
      (
        LayerConstants.COPPER.exists(t => x._2.chosen.flatMap(_.fileType).contains(t)) &&
        !top.map(_._1).contains(x._1) && !bottom.map(_._1).contains(x._1)
      )

    }
      .toSeq
      .sortBy(_._2.chosen.flatMap(_.index).getOrElse(0))
      .partition(_._2.chosen.flatMap(_.index).nonEmpty)

    val mappedState = filtered.map(x => x._1 -> x._2.options)

    val (topOption, botOption) = selectBestOptions(top, bottom, restWithIndex, restWithoutIndex)

    val allMid      = changeFiletype(mappedState, top.map(_._1) ++ bottom.map(_._1), COPPER_MID)
    val assignedTop = changeFiletype(allMid, topOption.map(_._1).toSeq, COPPER_TOP)
    val assignedBot = changeFiletype(assignedTop, botOption.map(_._1).toSeq, COPPER_BOTTOM)

    Some(
      assignedBot
    )
  }

  /** take the first available of:
    *
    * <ol>
    *
    * <li>the top/bottom matched</li>
    *
    * <li> the copper mid with the highest/lowest index for top and bottom respectively</li>
    *
    * <li> the first/last copper mid without index (this starts to be more fuzzy)</li>
    *
    * <li> the second file in the list of the opposite type. e.g. there are >= 2 copper tops, take the second as copper
    * bottom if the previous steps do not apply (which will only happen if nothing else that copper top exists)</li>
    *
    * </ol>
    * @param top
    * @param bottom
    * @param restWithIndex
    * @param restWithoutIndex
    * @return
    */
  private def selectBestOptions(
      top: Seq[(X, FileMatchChoice)],
      bottom: Seq[(X, FileMatchChoice)],
      restWithIndex: Seq[(X, FileMatchChoice)],
      restWithoutIndex: Seq[(X, FileMatchChoice)]
  ): (Option[(X, FileMatchChoice)], Option[(X, FileMatchChoice)]) = {
    val topOption =
      top.headOption orElse restWithIndex.headOption orElse restWithoutIndex.headOption orElse bottom.toSeq.lift(1)
    val botOption =
      bottom.headOption orElse restWithIndex.lastOption orElse restWithoutIndex.lastOption orElse top.toSeq.lift(1)

    (topOption, botOption)

  }
}
