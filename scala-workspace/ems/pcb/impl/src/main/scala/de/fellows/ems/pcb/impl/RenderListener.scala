package de.fellows.ems.pcb.impl

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.api.broker.Message
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assemby.api.{AssemblyService, FileUpdate}
import de.fellows.ems.pcb.impl.entity.pcb.{PCBEntity, SetFileFormat}
import de.fellows.ems.renderer.api.{PreviewAdded, RendererService}
import de.fellows.utils.TopicUtils
import de.fellows.utils.logging.{StackrateLogger, StackrateLogging}
import de.fellows.utils.telemetry.KamonUtils
import kamon.Kamon
import TopicUtils.defaultNaming

import scala.concurrent.ExecutionContext

class RenderListener(rs: RendererService, ereg: PersistentEntityRegistry, ass: AssemblyService)(implicit
    ctx: ExecutionContext
) extends StackrateLogging {
  val started = System.currentTimeMillis()

  TopicUtils.subscribeLatest(rs.renderAdded(), started) { msg =>
    val ref = ereg.refFor[PCBEntity](msg.payload.assRef.version.toString)
    ref.ask(SetFileFormat(
      msg.payload.assRef.team,
      msg.payload.assRef,
      msg.payload.file,
      msg.payload.format
    )).map(_ => Done).recover {
      case e: Throwable =>
        e.printStackTrace()
        Done
    }

  }

  TopicUtils.subscribe(rs.previewAdded(), 5) {
    _.payload match {
      case msg =>
        ass._updateFile(
          msg.assRef.team,
          msg.assRef.id,
          Some(msg.assRef.version),
          msg.file.name
        ).invoke(
          FileUpdate(
            function = None,
            detectedTypes = None,
            preview = Some(msg.preview)
          )
        ).recover {
          case e: Throwable =>
            logger.error(e.getMessage, e)
            Kamon.currentSpan().fail(e)
            Done;
        }
    }
  }

}
