package de.fellows.ems.pcb.impl.entity.pcb

import de.fellows.ems.pcb.model.GerberFile
import play.api.libs.json
import play.api.libs.json.Json

case class LayerDTO(
    file: Option[GerberFile] = None,
    material: Option[String] = None,
    thickness: Option[BigDecimal] = None,
    displayColor: Option[String] = None,
    manufacturingColor: Option[String] = None,
    inverted: Boolean
)

object LayerDTO {
  implicit val format: json.Format[LayerDTO] = Json.format
}
