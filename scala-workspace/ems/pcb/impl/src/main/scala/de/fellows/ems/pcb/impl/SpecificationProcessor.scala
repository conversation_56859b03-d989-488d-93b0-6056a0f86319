package de.fellows.ems.pcb.impl

import akka.Done
import com.datastax.driver.core.{ BoundStatement, PreparedStatement }
import com.lightbend.lagom.scaladsl.persistence.cassandra.{ CassandraReadSide, CassandraSession }
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEventTag, ReadSideProcessor }
import de.fellows.ems.pcb.impl.entity.specification.SpecificationEvents.{
  SpecificationCreated,
  SpecificationEvent,
  SpecificationRemoved
}

import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ ExecutionContext, Future }

class SpecificationRepository(session: CassandraSession)(implicit ec: ExecutionContext) {
  def getSpecificationIDsByAlias(team: String, assembly: UUID, version: UUID, alias: String) =
    // language=SQL

    session.selectAll(
      "SELECT * FROM specbyalias WHERE team = ? AND assembly = ? AND version = ? AND alias = ?",
      team,
      assembly,
      version: UUID,
      alias
    ).map(_.map(_.getUUID("id")))

}

class SpecificationProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit ec: ExecutionContext)
    extends ReadSideProcessor[SpecificationEvent] {

  var setSpecByAliasStmt: PreparedStatement    = _
  var removeSpecByAliasStmt: PreparedStatement = _

  def prepareStatements(): Future[Done] =
    for {
      setSpecByName <- session.prepare(
        // language=SQL
        """
          | UPDATE specByAlias SET id = :id WHERE team = :team AND assembly = :assembly AND alias = :alias AND version = :version
          |""".stripMargin
      )

      removeSpecByName <- session.prepare(
        // language=SQL
        """
          | DELETE FROM specByAlias WHERE team = :team AND assembly = :assembly AND alias = :alias AND version = :version
          |""".stripMargin
      )
    } yield {
      setSpecByAliasStmt = setSpecByName
      removeSpecByAliasStmt = removeSpecByName

      Done
    }

  def addSpec(event: SpecificationCreated): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      setSpecByAliasStmt.bind()
        .setUUID("id", event.s.id)
        .setString("team", event.ass.team)
        .setUUID("assembly", event.ass.id)
        .setUUID("version", event.ass.version)
        .setString("alias", event.s.alias)
    ))

  def removeSpec(event: SpecificationRemoved): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      removeSpecByAliasStmt.bind()
        .setString("team", event.ass.team)
        .setUUID("assembly", event.ass.id)
        .setUUID("version", event.ass.version)
        .setString("alias", event.alias)
    ))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[SpecificationEvent] =
    readSide.builder[SpecificationEvent]("pcbspecrepo-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[SpecificationCreated](e => addSpec(e.event))
      .setEventHandler[SpecificationRemoved](e => removeSpec(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[SpecificationEvent]] = SpecificationEvent.Tag.allTags

  def createTables() =
    for {
      //      _ <- PCBCodecHelper.loadTypes(session)

      _ <- session.executeCreateTable(
        // language=SQL
        """
          | CREATE TABLE IF NOT EXISTS specByAlias (
          |   id uuid,
          |   alias text,
          |   assembly uuid,
          |   version uuid,
          |   team text,
          |
          |   PRIMARY KEY ( team,assembly,version,alias )
          |
          | )
          |""".stripMargin
      )
    } yield Done
}
