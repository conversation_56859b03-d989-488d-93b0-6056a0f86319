package de.fellows.ems.pcb.impl.matcher

import de.fellows.app.assemby.api.FileDescription
import de.fellows.ems.pcb.impl.matcher.DefaultFilesMatcher.DEFAULT_INVARIANTS
import de.fellows.ems.pcb.impl.matcher.filecontent.{
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrillFileContentMatcher,
  ExtrepAnalyzer,
  GerberContentMatcher
}
import de.fellows.ems.pcb.impl.matcher.filename._
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.pcb.model.LayerConstants.Mime
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.internal.FileType
import de.fellows.utils.telemetry.KamonUtils
import kamon.Kamon
import org.slf4j.LoggerFactory
import play.api.Logging
import play.api.libs.json.{Format, Json}

import scala.util.matching.Regex
import scala.util.matching.Regex.Match

abstract class SingleFileMatcher(override val binary: Boolean) extends FileMatcher(binary) {
  def matchFile(filename: String, file: FilePath, mime: Option[String]): Seq[FileMatch]

  override final def matchFile(
      filename: String,
      file: FilePath,
      mime: Option[String],
      allFiles: Seq[FilePath]
  ): Map[FilePath, Seq[FileMatch]] =
    Map(file -> matchFile(filename, file, mime))
}

abstract class FileMatcher(val binary: Boolean) {
  def id: String

  def confidence: Int

  def service: String

  def matchFile(
      filename: String,
      file: FilePath,
      mime: Option[String],
      allFiles: Seq[FilePath]
  ): Map[FilePath, Seq[FileMatch]]

  def mime: Option[Seq[String]]

  def createMatch(
      ltype: String,
      mime: Option[String] = Mime.gerber,
      cat: Option[String] = LayerConstants.Categories.gerber,
      index: Option[Int] = None,
      conf: Option[Int] = None
  ) =
    FileMatch(conf.getOrElse(confidence), id, Some(service), cat, Some(ltype), Some(true), mime, index = index)
}

abstract class RegexFileMatcher(binary: Boolean) extends SingleFileMatcher(binary: Boolean) {
  val patterns: Seq[(Regex, Match => FileMatch)]

  override final def matchFile(
      filename: String,
      file: FilePath,
      mime: Option[String]
  ): Seq[FileMatch] = {
    val matched = patterns.map { ft =>
      ft._1.findFirstMatchIn(filename.toLowerCase()) match {
        case Some(m) =>
          val fileMatch = ft._2(m)
          postProcess(fileMatch, file, mime)
        case None => None
      }
    }

    matched.flatten
  }

  def postProcess(m: FileMatch, f: FilePath, mime: Option[String]): Option[FileMatch] =
    Some(m)
}

object FileMatcher {}

/** Matches a list of files to their file types.
  *
  * This reuses the DefaultFileMatcher, but also consolidates the results into the most likely matches, using the all
  * available information.
  *
  * @param files
  * all files
  * @param c
  * a converter function to create FilePaths for the given files
  * @param types
  * a function to get an optional filetype for the given files. This is used to provide some known hints about the files to the matcher
  */
class DefaultFilesMatcher[X](files: Seq[X], c: X => FilePath, types: X => Option[FileType])(implicit
    serviceDefinition: ServiceDefinition
) extends Logging {

  def createMatches(): Map[X, Seq[FileMatch]] = {

    val (odb, nonOdb) = files.partition(f => types(f).flatMap(_.category).contains(FileType.CATEGORY_ODB))

    logger.info(s"odb: ${odb.map(c).map(_.filename).mkString(", ")}")
    logger.info(s"rest: ${nonOdb.map(c).map(_.filename).mkString(", ")}")

    val odbMatches: Map[X, Seq[FileMatch]] =
      (if (odb.nonEmpty) {
         new ODBMatcher(odb.map(c)).matchFiles()
       } else {
         Map.empty
       }).flatMap(x =>
        files.find(ff => c(ff) == x._1) // map the match back to the file in the full file list
          .map(_ -> x._2)
      )

    odbMatches ++
      (nonOdb.flatMap { f =>
        val cf       = c(f)
        val filename = cf.filename.split("/").last
        val m        = new DefaultFileMatcher(cf, filename, files.map(c)).matchFile()
        m.flatMap(x =>
          files.find(ff => c(ff) == x._1) // map the match back to the file in the full file list
            .map(_ -> x._2)
        )
      }).groupBy(_._1).collect {
        case (k, v) => k -> {
            consolidate(v.flatMap(_._2))
          }
      }
  }

  /** remove duplicates in filetypes by summing their confidences
    */
  private def consolidate(matches: Seq[FileMatch]): Seq[FileMatch] =
    matches.groupBy(_.fileType).map { entry =>
      // reduce the filematches sorted by confidence. this makes sure that the most confident results have precendence
      entry._2.sortBy(_.confidence).reverse.reduce { (a, b) =>
        a.copy(
          confidence = a.confidence + b.confidence,
          matcher = s"${a.matcher}, ${b.matcher}",
          service = a.service,
          category = a.category,
          fileType = a.fileType orElse b.fileType,
          productionFile = a.productionFile,
          mimeType = a.mimeType,
          index = a.index orElse b.index,
          from = a.from orElse b.from,
          to = a.from orElse b.from
        )
      }
    }.toSeq

  def matchFileType(invariants: Seq[FileTypeInvariant[X]] = DEFAULT_INVARIANTS[X]): Map[X, Option[FileType]] = {
    val matches = createMatches()

    DefaultFilesMatcher.chooseMatches(matches, invariants, c)
      .map { x =>
        x._1 -> x._2.map(m => DefaultFilesMatcher.toType(m, serviceDefinition))
      }
  }
}

case class FileMatchChoice(chosen: Option[FileMatch], options: Seq[FileMatch])

object DefaultFilesMatcher {
  def DEFAULT_INVARIANTS[X] = Seq(
    new ProjectTypeLimit[X](),
    new GerberProjectCountLimit[X](),
    new SimpleFileTypeLimit[X](LayerConstants.SOLDERMASK_BOTTOM, 1),
    new SimpleFileTypeLimit[X](LayerConstants.SOLDERMASK_TOP, 1),
    new InnerOuterCopperCount[X]()
  )

  def apply(files: Seq[FilePath])(implicit serviceDefinition: ServiceDefinition): DefaultFilesMatcher[FilePath] =
    new DefaultFilesMatcher[FilePath](files, identity, _ => None)(serviceDefinition)
  def forDescriptions(files: Seq[FileDescription])(implicit
      serviceDefinition: ServiceDefinition
  ): DefaultFilesMatcher[FileDescription] =
    new DefaultFilesMatcher[FileDescription](files, _.filePath, f => Some(f.fType))(serviceDefinition)

  /** Choose the best matches for each file based on the given invariants.
    *
    * This function will try to resolve the matches based on the given invariants. If the invariants are not met, it will
    * try to resolve the matches again, until the invariants are met or no more changes can be made.
    *
    * @param matches
    * the matches to resolve
    * @param invariants
    * the invariants to resolve the matches with
    * @tparam X
    * the type of the files
    * @return
    * the resolved matches
    */
  def chooseMatches[X](
      matches: Map[X, Seq[FileMatch]],
      invariants: Seq[FileTypeInvariant[X]],
      c: X => FilePath
  ): Map[X, Option[FileMatch]] = {

    def tryChoosing(matches: Map[X, Seq[FileMatch]]) =
      matches.map { x =>
        val matches     = x._2
        val sortedTypes = matches.sortBy(_.confidence).reverse
        x._1 -> sortedTypes.foldLeft[Option[FileMatch]](None) { (currentOption, newType) =>
          currentOption match {
            case None => Some(newType)
            case Some(current) =>
              val merged = Some(current merge newType)
              merged
          }
        }
      }

    val resolved = invariants.foldLeft(matches) { (foldedState, invariant) =>
      // recreate the choice after each invariant, since the invariants can change the matches
      val currentChoice = tryChoosing(foldedState)
      invariant.resolve(
        foldedState.map(x =>
          x._1 -> FileMatchChoice(
            currentChoice(x._1),
            x._2
          )
        ),
        c
      ) match {
        case Some(resolvedState) => resolvedState
        case None                => foldedState
      }
    }

    if (resolved == matches) {
      tryChoosing(matches)
    } else {
      chooseMatches(resolved, invariants, c)
    }
  }

  def toType(m: FileMatch, serviceDefinition: ServiceDefinition): FileType =
    m.fileType match {
      case Some(ft) =>
        FileType(
          service = serviceDefinition.name,
          category = m.category,
          fileType = ft,
          productionFile = m.productionFile.getOrElse(true),
          mimeType = m.mimeType,
          index = m.index,
          from = m.from,
          to = m.to
        )
      case None => FileType.UNKNOWN
    }
}

/** Matches a single file to Filetypes.
  *
  * it can result in multiple different matches for that file, but also filetypes for completely different files since
  * some files contain information about related files as well.
  *
  * @param file
  * The file to match
  * @param filename
  * the name of the file
  * @param allFiles
  * all files in the current matching context. this list contains all files this matcher instance *could* create
  * matches for.
  */
class DefaultFileMatcher(file: FilePath, filename: String, allFiles: Seq[FilePath])(implicit
    serviceDefinition: ServiceDefinition
) extends Logging {
  private val log = LoggerFactory.getLogger(classOf[FileMatcher])

  val filter = new DefaultFileFilter()
  val defaultMatchers = Seq[FileMatcher](
    new AltiumMatcher(),
    new BoschMatcher(),
    new EagleMatcher(),
    new KicadMatcher(),
    new XpeditionMatcher(),
    new PADSMatcher(),
    new PhoenixMatcher(),
    new DrillFileNameMatcher(),
    new LowConfidenceMatcher(),
    new OtherMatcher(),
    new GerberContentMatcher(),
    new NativeMatcher(),
    new Cam350Matcher(),
    new PCadMatcher(),
    new DrillFileContentMatcher(),
    new ExtrepAnalyzer(),
    new ViewMateMatcher()
  )

  def matchFile(): Seq[(FilePath, Seq[FileMatch])] = {
    val filterMessage = filter.valid(file, filename)
    val binary        = filter.isBinary(file, filename).isDefined
    val mime          = filter.getMimeType(file, filename)

    if (filterMessage.isEmpty) {
      KamonUtils.span(s"match file ${file.filename}") {
        defaultMatchers.flatMap { matcher =>
          if (matcher.mime.forall(mimes => mimes.contains(mime.getOrElse("")))) {
            try {
              val matcherName = matcher.getClass.getSimpleName
              if (binary) {
                if (matcher.binary) {
                  KamonUtils.span(s"match with $matcherName") {
                    Kamon.currentSpan().tag("stackrate.filematcher", matcherName)
                    matcher.matchFile(filename, file, mime, allFiles)
                  }
                } else {
                  Seq()
                }
              } else {
                KamonUtils.span(s"match with $matcherName") {
                  Kamon.currentSpan().tag("stackrate.filematcher", matcherName)
                  matcher.matchFile(filename, file, mime, allFiles)
                }
              }
            } catch {
              case e: Throwable =>
                log.error(s"Failed to match file: ${e.getMessage}")
                Seq()
            }
          } else {
            Seq()
          }
        }
      }
    } else {
      Seq()
    }
  }
}

//case class FileMatch(fileType: FileType, confidence: Int)

case class FileMatch(
    confidence: Int,
    matcher: String,
    service: Option[String] = None,
    category: Option[String] = None,
    fileType: Option[String] = None,
    productionFile: Option[Boolean] = None,
    mimeType: Option[String] = None,
    index: Option[Int] = None,
    from: Option[Int] = None,
    to: Option[Int] = None
) {
  def merge(other: FileMatch) =
    this.copy(
      confidence = this.confidence max other.confidence,
      matcher = this.matcher,
      service = this.service orElse other.service,
      category = this.category orElse other.category,
      fileType = this.fileType orElse other.fileType,
      productionFile = this.productionFile orElse other.productionFile,
      mimeType = this.mimeType orElse other.mimeType,
      index = this.index orElse other.index,
      from = this.from orElse other.from,
      to = this.to orElse other.to
    )
}

object FileMatch {
  implicit val format: Format[FileMatch] = Json.format

}
