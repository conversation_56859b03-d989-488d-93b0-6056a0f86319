include "main-application.conf"

play.application.loader = de.fellows.ems.pcb.impl.PCBServiceLoader


pcb.cassandra.keyspace = ${?fellows.persistence.rootKeyspace}pcb


cassandra-journal {
  keyspace = ${pcb.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${pcb.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${pcb.cassandra.keyspace}read
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "pcb"
# fellows.serviceconfig = ${fellows.services.pcb}

akka {
  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # Log level for the very basic logger activated during ActorSystem startup.
  # This logger prints the log messages to stdout (System.out).
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  stdout-loglevel = "DEBUG"
}

fellows.storage {
  service = ${fellows.storage.base}/pcb
}

matching-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 5
  }
  throughput = 1
}
