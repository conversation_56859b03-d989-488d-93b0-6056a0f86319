G04 Generated by UcamX v2017.04-170404 on 2017.4.7*
G04 Ucamco copyright*
%FSLAX26Y26*%
%MOIN*%
%AMBOXR*
0 Rectangle with Rounded corners*
0 $1 width*
0 $2 height*
0 $3 corner radius*
0 $4 rotation angle*
0 Calculate half width, half height and corner diameter*
$5=$1/2-$3*
$6=$2/2-$3*
$7=2X$3*
0 Create two overlapping rectangles that omit the rounded corner areas*
21,1,$1,$2-$7,0,0,$4*
21,1,$1-$7,$2,0,0,$4*
0 Add circles at the corners*
1,1,$7,$5,$6,$4*
1,1,$7,-$5,$6,$4*
1,1,$7,-$5,-$6,$4*
1,1,$7,$5,-$6,$4*%
%ADD10BOXR,0.01000X0.00500X0.00200X0.00*%
%ADD11BOXR,0.02000X0.01000X0.00200X10.00*%
%ADD12BOXR,0.02550X0.01000X0.00200X30.00*%
%ADD13BOXR,0.02775X0.01000X0.00200X45.00*%
%AMBOXS*
0 Rectangle with Straight cutoff corners*
0 $1 width*
0 $2 height*
0 $3 X cutoff value*
0 $4 Y cutoff value*
0 $5 rotation angle*
0 Calculate values for the coordinates*
$6=$1/2*
$7=$2/2*
$8=$6-$3*
$9=$7-$4*
0 Create Outline describing the image*
4,1,8,-$8,$7,$8,$7,$6,$9,$6,-$9,$8,-$7,-$8,-$7,-$6,-$9,-$6,$9,-$8,$7,$5*%
%ADD14BOXS,0.01000X0.00500X0.00300X0.00200X0.00*%
%ADD15BOXS,0.02000X0.01000X0.00300X0.00200X10.00*%
%ADD16BOXS,0.02550X0.01000X0.00300X0.00200X30.00*%
%ADD17BOXS,0.02775X0.01000X0.00300X0.00200X45.00*%
%AMTHERS4T*
0 Round Thermal with 4 gaps with Straight Stroke*
0 $1 outer diameter*
0 $2 inner diameter*
0 $3 gab thickness*
0 $4 rotation angle*
0 Thermal primitive*
7,0,0,$1,$2,$3,$4*%
%ADD18THERS4T,0.01000X0.00500X0.00200X0.00*%
%ADD19THERS4T,0.02000X0.01000X0.00200X10.00*%
%ADD20THERS4T,0.02500X0.02000X0.00600X30.00*%
%ADD21THERS4T,0.02700X0.02000X0.00600X45.00*%
%AMTHERR4*
0 Round Thermal with 4 gaps  with Round Stroke*
0 $1 outer diameter*
0 $2 inner diameter*
0 $3 sin of the angle between the vertical line and the center point of a rounded edge of the THErmal*
0    value can be calculated by [gap + ($1-$2)/2] / [$2 + ($1-$2)/2 ]*
0 $4 cos of angle calculated by $3*
0 $5 rotation angle*
0 Calculate reference point for defining gap*
$6=$1/2*
$7=$6X$3/$4*
0 Calculate thickness*
$8=($1-$2)/2*
0 Calculate radius of circle in middle between outer and inner diameter*
$9=($8+$2)/2*
0 Create donut with outer and inner diameter*
1,1,$1,0,0,0*
1,0,$2,0,0,0*
0 Create 4 outlines creating gaps*
4,0,3,0.00000,0.00000,$7,$1/2,0-$7,$1/2,0.00000,0.00000,$5*
4,0,3,0.00000,0.00000,$7,$1/2,0-$7,$1/2,0.00000,0.00000,90+$5*
4,0,3,0.00000,0.00000,$7,$1/2,0-$7,$1/2,0.00000,0.00000,180+$5*
4,0,3,0.00000,0.00000,$7,$1/2,0-$7,$1/2,0.00000,0.00000,270+$5*
0 Add circles at the gaps*
1,1,$8,$9X$3,$9X$4,$5*
1,1,$8,$9X$3,$9X$4,90+$5*
1,1,$8,$9X$3,$9X$4,180+$5*
1,1,$8,$9X$3,$9X$4,270+$5*
1,1,$8,$9X$4,$9X$3,$5*
1,1,$8,$9X$4,$9X$3,90+$5*
1,1,$8,$9X$4,$9X$3,180+$5*
1,1,$8,$9X$4,$9X$3,270+$5*%
%ADD22THERR4,0.01000X0.00500X0.6000X0.8000X0*%
%ADD23THERR4,0.02000X0.01000X0.4667X0.8844X10*%
%ADD24THERR4,0.02500X0.02000X0.3778X0.9259X30*%
%ADD25THERR4,0.02700X0.02000X0.40430X0.9146X45*%
%AMDONSS*
0 Donut with Square Outside and Square Inside*
0 $1 outer dimension*
0 $2 hole dimension*
0 $3 rotation angle*
0 Create rectangle describing Outside*
21,1,$1,$1,0,0,$3*
0 Create rectangle describing Inside*
21,0,$2,$2,0,0,$3*%
%ADD26DONSS,0.01000X0.00500X0.00*%
%ADD27DONSS,0.01000X0.00500X10.00*%
%ADD28DONSS,0.02000X0.01000X30.00*%
%ADD29DONSS,0.02000X0.01500X45.00*%
G04 Donut with Square Outside and Round Inside, without rotation*
%ADD30R,0.01000X0.01000X0.00500*%
%AMDONSR*
0 Donut with Square Outside and Round Inside*
0 $1 outer dimension*
0 $2 hole diameter*
0 $3 rotation angle*
0 Create rectangle describing Outside*
21,1,$1,$1,0,0,$3*
0 Create circle describing Inside*
1,0,$2,0,0,0*%
%ADD31DONSR,0.01000X0.00500X10.00*%
%ADD32DONSR,0.02000X0.01000X30.00*%
%ADD33DONSR,0.02000X0.01500X45.00*%
G04 Circle aperture creating background*
%ADD34C,0.001*%

G04 layer name: sample_macro*
%LPD*%
G04 Define background pattern*
G36*
X180000Y-25000D02*
Y215000D01*
X345000*
Y-25000*
X180000*
G37*
%SRX1Y79I0.00000J0.00300*%
D34*
X-20000Y278000D02*
X143000Y278000D01*
X180000Y278000D02*
X343000Y278000D01*
%SR*%
%SRX54Y1I0.00300J0.00000*%
X-18000Y276000D02*
X-18000Y514000D01*
X182000Y276000D02*
X182000Y514000D01*
%SR*%

G04 flashes, using dark polarity, with macro definitions*
%SRX1Y2I0.00000J0.30000*%
D10*
X0Y0D03*
D11*
X40000D03*
D12*
X80000D03*
D13*
X120000D03*
D14*
X0Y30000D03*
D15*
X40000D03*
D16*
X80000D03*
D17*
X120000D03*
D18*
X0Y80000D03*
D19*
X40000D03*
D20*
X80000D03*
D21*
X120000D03*
D22*
X0Y110000D03*
D23*
X40000D03*
D24*
X80000D03*
D25*
X120000D03*
D26*
X0Y160000D03*
D27*
X40000D03*
D28*
X80000D03*
D29*
X120000D03*
D30*
X0Y190000D03*
D31*
X40000D03*
D32*
X80000D03*
D33*
X120000D03*

G04 flashes, using clear polarity, with macro definitions*
%LPC*%
D10*
X200000Y0D03*
D11*
X240000D03*
D12*
X280000D03*
D13*
X320000D03*
D14*
X200000Y30000D03*
D15*
X240000D03*
D16*
X280000D03*
D17*
X320000D03*
D18*
X200000Y80000D03*
D19*
X240000D03*
D20*
X280000D03*
D21*
X320000D03*
D22*
X200000Y110000D03*
D23*
X240000D03*
D24*
X280000D03*
D25*
X320000D03*
D26*
X200000Y160000D03*
D27*
X240000D03*
D28*
X280000D03*
D29*
X320000D03*
D30*
X200000Y190000D03*
D31*
X240000D03*
D32*
X280000D03*
D33*
X320000D03*
%SR*%
%LPD*%
M02*