G04 Generated by UcamX v2017.04_Devbuild on 2017.4.7*
G04 Ucamco copyright*
%FSLAX45Y45*%
%MOMM*%
G01*
G04 Create aperture macro*
%AMRECTROUNDCORNERS*
0 Rectangle with rounded corners*
0 $1 width *
0 $2 height *
0 $3 corner radius *
0 $4 flash origin X offset *
0 $5 flash origin Y offset *
0 $6 rotation angle *
0 Create two overlapping rectangles that omit the rounded corner areas*
21,1,$1,$2-2x$3,$4,$5,$6*
21,1,$1-2x$3,$2,$4,$5,$6*
0 Add circles at the corners. *
1,1,2x$3,$4+$1/2-$3,$5+$2/2-$3,$6*
1,1,2x$3,$4-$1/2+$3,$5+$2/2-$3,$6*
1,1,2x$3,$4-$1/2+$3,$5-$2/2+$3,$6*
1,1,2x$3,$4+$1/2-$3,$5-$2/2+$3,$6*%
G04 Create aperture*
%ADD10RECTROUNDCORNERS,4X3X0.5X0X0X0*%
%ADD11RECTROUNDCORNERS,4X3X0.5X0X0X10*%
%ADD12RECTROUNDCORNERS,4X3X0.5X0X0X30*%
%ADD13RECTROUNDCORNERS,4X3X0.5X0X0X45*%
%ADD20RECTROUNDCORNERS,4X3X0.5X1.0X2.0X0*%
%ADD21RECTROUNDCORNERS,4X3X0.5X1.0X2.0X10*%
%ADD22RECTROUNDCORNERS,4X3X0.5X1.0X2.0X30*%
%ADD23RECTROUNDCORNERS,4X3X0.5X1.0X2.0X45*%
G04 Select aperture*
D10*
G04 Flash aperture*
X0Y0D03*
D11*
X600000Y0D03*
D12*
X1200000Y0D03*
D13*
X1800000Y0D03*
D20*
X0Y500000D03*
D21*
X600000Y500000D03*
D22*
X1200000Y500000D03*
D23*
X1800000Y500000D03*
M02*