package de.fellows.ems.pcb.impl.matcher

import de.fellows.ems.pcb.impl.matcher.DefaultFilesMatcher
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.OptionValues
import org.yaml.snakeyaml.Yaml

import java.io.{File, FileInputStream}
import java.nio.file.{Path, Paths}
import scala.collection.mutable.ArrayBuffer
import scala.jdk.CollectionConverters.{getClass, _}
import scala.util.Using

class RealFileMatcherSpec extends AnyWordSpec with Matchers with OptionValues {

  implicit val serviceDefinition: ServiceDefinition = ServiceDefinition("test-service")

  case class ExpectedFileMatch(
      filename: String,
      expectedFileType: Option[String],
      expectedCategory: Option[String]
  )

  case class TestProject(
      name: String,
      path: Path,
      expectedMatches: Seq[ExpectedFileMatch]
  )

  private def loadExpectedResults(projectPath: Path): Seq[ExpectedFileMatch] = {
    val yamlFile = projectPath.resolve("expected.yaml").toFile
    if (!yamlFile.exists()) {
      fail(s"Expected YAML file not found: ${yamlFile.getAbsolutePath}")
    }

    val yaml = new Yaml()
    Using(new FileInputStream(yamlFile)) { inputStream =>
      yaml.load(inputStream) match {
        case data: java.util.Map[_, _] =>
          data.get("files") match {
            case files: java.util.List[_] =>
              files.asScala.toSeq.collect {
                case fileData: java.util.Map[_, _] =>
                  ExpectedFileMatch(
                    filename = getString(fileData, "filename"),
                    expectedFileType = getOptionalString(fileData, "expectedFileType"),
                    expectedCategory = getOptionalString(fileData, "expectedCategory")
                  )
              }
            case _ => fail("Invalid YAML: 'files' should be a list")
          }
        case _ => fail("Invalid YAML: root should be a map")
      }
    }.fold(
      error => fail(s"Failed to read YAML: $error"),
      identity
    )
  }

  private def getString(map: java.util.Map[_, _], key: String): String = {
    Option(map.get(key)).map(_.toString).getOrElse("")
  }

  private def getOptionalString(map: java.util.Map[_, _], key: String): Option[String] = {
    Option(map.get(key)).map(_.toString).filter(_ != "null")
  }

  private def discoverTestProjects(): Seq[TestProject] = {
    val gerberResourcesPath = Paths.get(getClass.getResource("/gerber-assets").toURI)

    gerberResourcesPath.toFile.listFiles()
      .filter(_.isDirectory)
      .filter(dir => new File(dir, "expected.yaml").exists())
      .map { projectDir =>
        val projectPath = projectDir.toPath
        TestProject(
          name = projectDir.getName,
          path = projectPath,
          expectedMatches = loadExpectedResults(projectPath)
        )
      }.toSeq
  }

  private def testProject(project: TestProject): Unit = {
    info(s"Testing project: ${project.name}")

    // Get all files in the project directory (excluding expected.yaml)
    val allFiles = project.path.toFile.listFiles()
      .filter(_.isFile)
      .filter(_.getName != "expected.yaml")
      .map(f => FilePath(f.getAbsolutePath))
      .toSeq

    info(s"Found ${allFiles.length} files to test")

    // Run the DefaultFilesMatcher
    val matcher = DefaultFilesMatcher(allFiles)
    val results = matcher.matchFileType()

    info(s"Matcher processed ${results.size} files")

    // Check each expected match
    project.expectedMatches.foreach { expected =>
      val filePath = allFiles.find(_.toFile.getName == expected.filename)
      
      filePath should be(defined)
      
      val actualResult = results.get(filePath.get)
      
      expected.expectedFileType match {
        case Some(expectedType) =>
          actualResult should be(defined)
          actualResult.get.fileType shouldEqual expectedType
          
        case None =>
          actualResult shouldBe empty
      }
      
      // Note: Category checking would require access to the Categories object
      // which might not be available in this test context
    }
  }

  "DefaultFilesMatcher" should {

    "match files according to YAML expectations for all test projects" in {
      val projects = discoverTestProjects()

      if (projects.isEmpty) {
        fail("No test projects found with expected.yaml files. Please add test projects to /gerber-assets/ resources.")
      } else {
        info(s"Found ${projects.length} test projects: ${projects.map(_.name).mkString(", ")}")
        projects.foreach(testProject)
      }
    }
  }

  // Individual test methods for each discovered project will be generated dynamically
  discoverTestProjects().foreach { project =>
    s"DefaultFilesMatcher for project ${project.name}" should {
      s"correctly match all files in ${project.name}" in {
        testProject(project)
      }
    }
  }
}
