package de.fellows.ems.pcb.regression

import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.matchers.should.Matchers
import de.fellows.ems.pcb.impl.matcher.{DefaultFileMatcher, FileMatch}
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.ems.pcb.model.LayerConstants._
import java.nio.file.{Files, Path, Paths}
import scala.jdk.CollectionConverters._
import org.scalatest.prop.TableDrivenPropertyChecks
import de.fellows.ems.pcb.impl.matcher.DefaultFilesMatcher
import org.scalatest.OptionValues
import org.scalatest.prop.TableFor3
import java.io.File

class RealFileMatcherTest extends AnyWordSpec with Matchers with TableDrivenPropertyChecks with OptionValues {
  implicit val serviceDefinition: ServiceDefinition = ServiceDefinition("test-service")

  val `2_Layer_PTV0730700A0_GX2_2` = Paths.get(getClass.getResource("/gerber/2_Layer_PTV0730700A0_GX2_2").toURI)
  val `2_Layer_PTV0730700A0_GX2_2_Files` = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("Gerber X2 Files_PTH_Drill.gbr", Some(PH_DRILL), Categories.mechanical),
    ("Gerber X2 Files_Profile.gbr", Some(OUTLINE), Categories.mechanical),
    ("Gerber X2 Files_Paste_Bot.gbr", Some(PASTE_BOTTOM), Categories.gerber),
    ("Gerber X2 Files_Paste_Top.gbr", Some(PASTE_TOP), Categories.gerber),
    ("Gerber X2 Files_Soldermask_Bot.gbr", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("Gerber X2 Files.EXTREP", None, None),
    ("Gerber X2 Files.apr", None, None),
    ("Gerber X2 Files_Copper_Signal_Top.gbr", Some(COPPER_TOP), Categories.gerber),
    ("Gerber X2 Files_Milling_(11).gbr", Some(MECHANICAL), Categories.mechanical),
    ("Gerber X2 Files_Soldermask_Top.gbr", Some(SOLDERMASK_TOP), Categories.gerber),
    ("Gerber X2 Files.RUL", None, None),
    ("Gerber X2 Files_Copper_Signal_Bot.gbr", Some(COPPER_BOTTOM), Categories.gerber),
    ("Gerber X2 Files-macro.APR_LIB", None, None),
    ("Gerber X2 Files_Drillmap_1.gbr", Some(DRILL), Categories.mechanical),
    ("Gerber X2 Files_Drawing_1.gbr", Some(MECHANICAL), Categories.mechanical),
    ("Gerber X2 Files.REP", None, None),
    ("Gerber X2 Files_Legend_Bot.gbr", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("Gerber X2 Files_NPTH_Drill.gbr", Some(NPH_DRILL), Categories.mechanical),
    ("Gerber X2 Files_Keep-out.gbr", Some(KEEP_OUT), Categories.mechanical)
  )

  val P_3677229_Netlist = Paths.get(getClass.getResource("/gerber/3677229_Netlist").toURI)
  val P_3677229_NetlistFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("Gerber Battery Board Octavian RevA0.apr", None, None),
    ("Gerber Battery Board Octavian RevA0.EXTREP", None, None),
    ("Gerber Battery Board Octavian RevA0.G1", Some(COPPER_MID), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.G2", Some(COPPER_MID), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GBL", Some(COPPER_BOTTOM), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GBO", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GBP", Some(PASTE_BOTTOM), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GBS", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GM", Some(MECHANICAL), Categories.mechanical),
    ("Gerber Battery Board Octavian RevA0.GP1", Some(PLANE_MID), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GP2", Some(PLANE_MID), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GTL", Some(COPPER_TOP), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GTO", Some(SILKSCREEN_TOP), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GTP", Some(PASTE_TOP), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0.GTS", Some(SOLDERMASK_TOP), Categories.gerber),
    ("Gerber Battery Board Octavian RevA0-macro.APR_LIB", None, None),
    ("Gerber Battery Board Octavian RevA0.REP", None, None),
    ("Gerber Battery Board Octavian RevA0.RUL", None, None),
    ("NC Drill Battery Board Octavian RevA0.DRL", None, None),
    ("NC Drill Battery Board Octavian RevA0.DRR", None, None),
    ("NC Drill Battery Board Octavian RevA0.LDP", Some(DRILLSETS), Categories.mechanical),
    ("NC Drill Battery Board Octavian RevA0-NonPlated.TXT", Some(DRILL), Categories.mechanical),
    ("NC Drill Battery Board Octavian RevA0-Plated.TXT", Some(DRILL), Categories.mechanical),
    ("Netlist Battery Board Octavian RevA0.ipc", None, None),
    ("Netlist Battery Board Octavian RevA0.REP", None, None)
  )

  val P_3677263_Gerber = Paths.get(getClass.getResource("/gerber/3677263_Gerber").toURI)
  val P_3677263_GerberFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("N216S_Switching_V2.1", Some(UNKNOWN), Categories.gerber),
    ("N216S_Switching_V2.2", Some(UNKNOWN), Categories.gerber),
    ("N216S_Switching_V2.3", Some(UNKNOWN), Categories.gerber),
    ("N216S_Switching_V2.apr", None, None),
    ("N216S_Switching_V2.EXTREP", None, None),
    ("N216S_Switching_V2.G1", Some(COPPER_MID), Categories.gerber),
    ("N216S_Switching_V2.G2", Some(COPPER_MID), Categories.gerber),
    ("N216S_Switching_V2.GBL", Some(COPPER_BOTTOM), Categories.gerber),
    ("N216S_Switching_V2.GBO", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("N216S_Switching_V2.GBP", Some(PASTE_BOTTOM), Categories.gerber),
    ("N216S_Switching_V2.GBS", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("N216S_Switching_V2.GD1", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GG1", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GKO", Some(KEEP_OUT), Categories.mechanical),
    ("N216S_Switching_V2.GM", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM1", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM11", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM12", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM13", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM14", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM15", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM16", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM17", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM18", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM2", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM3", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM4", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GM5", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GML", Some(MECHANICAL), Categories.mechanical),
    ("N216S_Switching_V2.GPB", Some(PASTE_BOTTOM), Categories.gerber),
    ("N216S_Switching_V2.GPT", Some(PASTE_TOP), Categories.gerber),
    ("N216S_Switching_V2.GTL", Some(COPPER_TOP), Categories.gerber),
    ("N216S_Switching_V2.GTO", Some(SILKSCREEN_TOP), Categories.gerber),
    ("N216S_Switching_V2.GTP", Some(PASTE_TOP), Categories.gerber),
    ("N216S_Switching_V2.GTS", Some(SOLDERMASK_TOP), Categories.gerber),
    ("N216S_Switching_V2-macro.APR_LIB", None, None),
    ("N216S_Switching_V2.REP", None, None),
    ("N216S_Switching_V2.RUL", None, None)
  )

  val P_3693822_EVA = Paths.get(getClass.getResource("/gerber/3693822_EVA").toURI)
  val P_3693822_EVAFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("border+drill.gbr", Some(DRILL), Categories.mechanical),
    ("bot.gbr", Some(COPPER_BOTTOM), Categories.gerber),
    ("maskbot.gbr", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("masktop.gbr", Some(SOLDERMASK_TOP), Categories.gerber),
    ("pastetop.gbr", Some(PASTE_TOP), Categories.gerber),
    ("silkbot.gbr", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("silktop.gbr", Some(SILKSCREEN_TOP), Categories.gerber),
    ("solderpluggbot.gbr", Some(COPPER_MID), Categories.gerber),
    ("top.gbr", Some(COPPER_TOP), Categories.gerber)
  )

  val A2619 = Paths.get(getClass.getResource("/gerber/A2619 External IO Board_Gerber_V1.00_06072022").toURI)
  val A2619Files = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("CNC.drl", Some(DRILL), Categories.mechanical),
    ("CNC.lst", None, None),
    ("CNC.rep", None, None),
    ("DRILL.pho", Some(DRILL), Categories.mechanical),
    ("DRILL.rep", None, None),
    ("L1LP.pho", Some(UNKNOWN), Categories.gerber),
    ("L1LP.rep", None, None),
    ("L1.pho", Some(UNKNOWN), Categories.gerber),
    ("L1.rep", None, None),
    ("L1SM.pho", Some(UNKNOWN), Categories.gerber),
    ("L1SM.rep", None, None),
    ("L1SP.pho", Some(COPPER_TOP), Categories.gerber),
    ("L1SP.rep", None, None),
    ("L2LP.pho", Some(UNKNOWN), Categories.gerber),
    ("L2LP.rep", None, None),
    ("L2.pho", Some(COPPER_BOTTOM), Categories.gerber),
    ("L2.rep", None, None),
    ("L2SM.pho", Some(UNKNOWN), Categories.gerber),
    ("L2SM.rep", None, None),
    ("L2SP.pho", Some(UNKNOWN), Categories.gerber),
    ("L2SP.rep", None, None)
  )

  val A3640 = Paths.get(getClass.getResource("/gerber/A3640").toURI)
  val A3640Files: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("A3640_copper_bottom.gbr", Some(COPPER_BOTTOM), Categories.gerber),
    ("A3640_copper_inner1.gbr", Some(UNKNOWN), Categories.gerber),
    ("A3640_copper_inner2.gbr", Some(COPPER_MID), Categories.gerber),
    ("A3640_copper_top.gbr", Some(COPPER_TOP), Categories.gerber),
    ("A3640.DRL", Some(DRILL), Categories.mechanical),
    ("A3640_silkscreen_top.gbr", Some(SILKSCREEN_TOP), Categories.gerber),
    ("A3640_smdmask_top.gbr", Some(SOLDERMASK_TOP), Categories.gerber),
    ("A3640_soldermask_bottom.gbr", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("A3640_soldermask_top.gbr", None, None),
    ("generated-outline.gbr", Some(OUTLINE), Categories.mechanical)
  )

  val HDMIBoard = Paths.get(getClass.getResource("/gerber/AnfrageHDMI_LRCnxFe3Tf4").toURI)
  val HDMIBoardFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("Colibri_HDMI_Adapter.GBL", Some(COPPER_BOTTOM), Categories.gerber),
    ("Colibri_HDMI_Adapter.GBO", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("Colibri_HDMI_Adapter.GBP", Some(PASTE_BOTTOM), Categories.gerber),
    ("Colibri_HDMI_Adapter.GBS", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("Colibri_HDMI_Adapter.GM15", Some(MECHANICAL), Categories.mechanical),
    ("Colibri_HDMI_Adapter.GP1", Some(PLANE_MID), Categories.gerber),
    ("Colibri_HDMI_Adapter.GP2", Some(PLANE_MID), Categories.gerber),
    ("Colibri_HDMI_Adapter.GTL", Some(COPPER_TOP), Categories.gerber),
    ("Colibri_HDMI_Adapter.GTO", Some(SILKSCREEN_TOP), Categories.gerber),
    ("Colibri_HDMI_Adapter.GTP", Some(PASTE_TOP), Categories.gerber),
    ("Colibri_HDMI_Adapter.GTS", Some(SOLDERMASK_TOP), Categories.gerber),
    ("Colibri_HDMI_Adapter.TXT", Some(DRILL), Categories.mechanical),
    ("msg-5460-916.html", None, None),
    ("Nutzen.pdf", None, None),
    ("Standard_ML4_Tg135_163_18.pdf", None, None)
  )

  val TigoMaster = Paths.get(getClass.getResource("/gerber/Crt002.01rb3_-BYY93RPQ-g").toURI)
  val TigoMasterFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("Status Report.Txt", None, None),
    ("TigoMaster_Drill.DRR", None, None),
    ("TigoMaster_Drill.DRRPreview", None, None),
    ("TigoMaster_Drill.LDP", Some(DRILLSETS), Categories.mechanical),
    ("TigoMaster_Drill.LDPPreview", None, None),
    ("TigoMaster_Drill-Plated.TX2", Some(DRILL), Categories.mechanical),
    ("TigoMaster_Drill-Plated.TX4", Some(DRILL), Categories.mechanical),
    ("TigoMaster_Drill-Plated.TX6", Some(DRILL), Categories.mechanical),
    ("TigoMaster_Drill-Plated.TXT", Some(DRILL), Categories.mechanical),
    ("TigoMaster_Gerber.apr", None, None),
    ("TigoMaster_Gerber.EXTREP", None, None),
    ("TigoMaster_Gerber.G1", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.G2", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.G3", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.G4", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.G5", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.G6", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.G7", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.G8", Some(COPPER_MID), Categories.gerber),
    ("TigoMaster_Gerber.GBL", Some(COPPER_BOTTOM), Categories.gerber),
    ("TigoMaster_Gerber.GBP", Some(PASTE_BOTTOM), Categories.gerber),
    ("TigoMaster_Gerber.GBS", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("TigoMaster_Gerber.GD1", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GD2", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GD3", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GD4", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GM1", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GM10", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GM2", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GM23", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GM30", Some(MECHANICAL), Categories.mechanical),
    ("TigoMaster_Gerber.GTL", Some(COPPER_TOP), Categories.gerber),
    ("TigoMaster_Gerber.GTP", Some(PASTE_TOP), Categories.gerber),
    ("TigoMaster_Gerber.GTS", Some(SOLDERMASK_TOP), Categories.gerber),
    ("TigoMaster_Gerber-macro.APR_LIB", None, None),
    ("TigoMaster_Gerber.REP", None, None),
    ("TigoMaster_Gerber.REPPreview", None, None)
  )

  val EIDBoard = Paths.get(getClass.getResource("/gerber/EID-Board_doKeiBOqTKs").toURI)
  val EIDBoardFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("BRE0062588-00-BM.pho", Some(UNKNOWN), Categories.gerber),
    ("BRE0062588-00-BOARD.pho", Some(OUTLINE), Categories.mechanical),
    ("BRE0062588-00.drl", Some(DRILL), Categories.mechanical),
    ("BRE0062588-00.ipc", None, None),
    ("BRE0062588-00-L1.pho", Some(COPPER_TOP), Categories.gerber),
    ("BRE0062588-00-L2.pho", Some(COPPER_MID), Categories.gerber),
    ("BRE0062588-00-L3.pho", Some(COPPER_MID), Categories.gerber),
    ("BRE0062588-00-L4.pho", Some(COPPER_MID), Categories.gerber),
    ("BRE0062588-00-L5.pho", Some(COPPER_MID), Categories.gerber),
    ("BRE0062588-00-L6.pho", Some(COPPER_BOTTOM), Categories.gerber),
    ("BRE0062588-00-TM.pho", Some(UNKNOWN), Categories.gerber),
    ("BRE0062588-00-TP.pho", Some(UNKNOWN), Categories.gerber),
    ("BRE0062588-00-TS.pho", Some(UNKNOWN), Categories.gerber)
  )

  val NordicEval = Paths.get(getClass.getResource("/gerber/Nordic-Eval").toURI)
  val NordicEvalFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("400150_Assembly_Drawing.pdf", None, None),
    ("400150.DRL", None, None),
    ("400150.DRR", None, None),
    ("400150.G1", Some(COPPER_MID), Categories.gerber),
    ("400150.G2", Some(COPPER_MID), Categories.gerber),
    ("400150.GBL", Some(COPPER_BOTTOM), Categories.gerber),
    ("400150.GBO", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("400150.GBP", Some(PASTE_BOTTOM), Categories.gerber),
    ("400150.GBS", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("400150.GM1", Some(MECHANICAL), Categories.mechanical),
    ("400150.GTL", Some(COPPER_TOP), Categories.gerber),
    ("400150.GTO", Some(SILKSCREEN_TOP), Categories.gerber),
    ("400150.GTP", Some(PASTE_TOP), Categories.gerber),
    ("400150.GTS", Some(SOLDERMASK_TOP), Categories.gerber),
    ("400150_PCB_Laminate_Specification.pdf", None, None),
    ("400150_Pick_And_Place.txt", None, None),
    ("400150-RoundHoles.TXT", Some(DRILL), Categories.mechanical),
    ("400150-SlotHoles.TXT", Some(DRILL), Categories.mechanical),
    ("PCA10040_BOM_Web.xls", None, None)
  )

  val Player = Paths.get(getClass.getResource("/gerber/Player").toURI)
  val PlayerFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("coturn-B_Adhes.gbr", Some(UNKNOWN), Categories.gerber),
    ("coturn-B_Cu.gbr", Some(COPPER_BOTTOM), Categories.gerber),
    ("coturn-B_Fab.gbr", Some(MECHANICAL), Categories.mechanical),
    ("coturn-B_Mask.gbr", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("coturn-bottom-pos.csv", None, None),
    ("coturn-B_Paste.gbr", Some(PASTE_BOTTOM), Categories.gerber),
    ("coturn-B_SilkS.gbr", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("coturn.drl", Some(DRILL), Categories.mechanical),
    ("coturn-Edge_Cuts.gbr", Some(OUTLINE), Categories.mechanical),
    ("coturn-F_Adhes.gbr", Some(UNKNOWN), Categories.gerber),
    ("coturn-F_Cu.gbr", Some(COPPER_TOP), Categories.gerber),
    ("coturn-F_Fab.gbr", Some(MECHANICAL), Categories.mechanical),
    ("coturn-F_Mask.gbr", Some(SOLDERMASK_TOP), Categories.gerber),
    ("coturn-F_Paste.gbr", Some(PASTE_TOP), Categories.gerber),
    ("coturn-F_SilkS.gbr", Some(SILKSCREEN_TOP), Categories.gerber),
    ("coturn-In1_Cu.gbr", Some(COPPER_MID), Categories.gerber),
    ("coturn-In2_Cu.gbr", Some(COPPER_MID), Categories.gerber),
    ("coturn.pdf", None, None),
    ("coturn-sorted-by-value.csv", None, None),
    ("coturn-sortet-by-ref.csv", None, None),
    ("coturn-top-pos.csv", None, None),
    ("ibom.html", None, None)
  )

  val SawTooth = Paths.get(getClass.getResource("/gerber/SawTooth_Ar7_sHhYQ10").toURI)
  val SawToothFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("SawToothOrganDIP-B_Cu.gbr", Some(COPPER_BOTTOM), Categories.gerber),
    ("SawToothOrganDIP-B_Mask.gbr", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("SawToothOrganDIP-B_Paste.gbr", Some(PASTE_BOTTOM), Categories.gerber),
    ("SawToothOrganDIP-B_SilkS.gbr", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("SawToothOrganDIP-Edge_Cuts.gbr", Some(OUTLINE), Categories.mechanical),
    ("SawToothOrganDIP-F_Cu.gbr", Some(COPPER_TOP), Categories.gerber),
    ("SawToothOrganDIP-F_Mask.gbr", Some(SOLDERMASK_TOP), Categories.gerber),
    ("SawToothOrganDIP-F_Paste.gbr", Some(PASTE_TOP), Categories.gerber),
    ("SawToothOrganDIP-F_SilkS.gbr", Some(SILKSCREEN_TOP), Categories.gerber),
    ("SawToothOrganDIP-job.gbrjob", None, None),
    ("SawToothOrganDIP-NPTH.drl", Some(PH_DRILL), Categories.mechanical),
    ("SawToothOrganDIP-PTH.drl", Some(NPH_DRILL), Categories.mechanical)
  )

  val TYAA_CHL_SM = Paths.get(getClass.getResource("/gerber/TYAA_CHL_SM").toURI)
  val TYAA_CHL_SM_Files: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("Copper Bottom.pho", Some(COPPER_BOTTOM), Categories.gerber),
    ("Copper Bottom.rep", None, None),
    ("Copper Top.pho", Some(COPPER_TOP), Categories.gerber),
    ("Copper Top.rep", None, None),
    ("Drill.pho", Some(DRILL), Categories.mechanical),
    ("Drill.rep", None, None),
    ("NC.drl", Some(DRILL), Categories.mechanical),
    ("NC.lst", None, None),
    ("NC.rep", None, None),
    ("Parts Placement.xlsx", None, None),
    ("Paste Top.pho", Some(PASTE_TOP), Categories.gerber),
    ("Paste Top.rep", None, None),
    ("Silkscreen Top.pho", Some(SILKSCREEN_TOP), Categories.gerber),
    ("Silkscreen Top.rep", None, None),
    ("Soldermask Bottom.pho", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("Soldermask Bottom.rep", None, None),
    ("Soldermask Top.pho", Some(SOLDERMASK_TOP), Categories.gerber),
    ("Soldermask Top.rep", None, None),
    ("TYAA_CHL_SM_PCB_C_LH-Rev3.pdf", None, None)
  )

  // Define test expectations for WemosD1_clone_RGB project
  val WemosD1Project = Paths.get(getClass.getResource("/gerber/WemosD1_clone_RGB").toURI)
  val WemosD1Files: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("WemosD1_clone_RGB_conn-CopperTop.gtl", Some(COPPER_TOP), Categories.gerber),
    ("WemosD1_clone_RGB_conn-CopperBottom.gbl", Some(COPPER_BOTTOM), Categories.gerber),
    ("WemosD1_clone_RGB_conn-Outline.gm1", Some(OUTLINE), Categories.mechanical),
    ("WemosD1_clone_RGB_conn-NPTH-drl_map.pdf", None, None),
    ("WemosD1_clone_RGB_conn-NPTH.drl", Some(PH_DRILL), Categories.mechanical),
    ("WemosD1_clone_RGB_conn-PTH-drl_map.pdf", None, None),
    ("WemosD1_clone_RGB_conn-PTH.drl", Some(NPH_DRILL), Categories.mechanical),
    ("WemosD1_clone_RGB_conn-PasteBottom.gbp", Some(PASTE_BOTTOM), Categories.gerber),
    ("WemosD1_clone_RGB_conn-PasteTop.gtp", Some(PASTE_TOP), Categories.gerber),
    ("WemosD1_clone_RGB_conn-SilkscreenBottom.gbo", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("WemosD1_clone_RGB_conn-SilkscreenTop.gto", Some(SILKSCREEN_TOP), Categories.gerber),
    ("WemosD1_clone_RGB_conn-SoldermaskBottom.gbs", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("WemosD1_clone_RGB_conn-SoldermaskTop.gts", Some(SOLDERMASK_TOP), Categories.gerber),
    ("WemosD1_clone_RGB_conn.kicad_pcb", Some(NATIVE_KICAD), Categories.unknown),
    ("project.gbrjob", None, None)
  )

  // Define test expectations for Xmas-led project
  val XmasLedProject = Paths.get(getClass.getResource("/gerber/Xmas-led-001_20181207_232223.zip_dbz_WgmpR8k").toURI)
  val XmasLedFiles: TableFor3[String, Option[String], Option[String]] = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("xmas-led-001.gtl", Some(COPPER_TOP), Categories.gerber),
    ("xmas-led-001.gbl", Some(COPPER_BOTTOM), Categories.gerber),
    ("xmas-led-001.gts", Some(SOLDERMASK_TOP), Categories.gerber),
    ("xmas-led-001.gbs", Some(SOLDERMASK_BOTTOM), Categories.gerber),
    ("xmas-led-001.gto", Some(SILKSCREEN_TOP), Categories.gerber),
    ("xmas-led-001.gbo", Some(SILKSCREEN_BOTTOM), Categories.gerber),
    ("xmas-led-001.gtp", Some(PASTE_TOP), Categories.gerber),
    ("xmas-led-001.gbp", Some(PASTE_BOTTOM), Categories.gerber),
    ("xmas-led-001.g2", Some(COPPER_MID), Categories.gerber),
    ("xmas-led-001.g3", Some(COPPER_MID), Categories.gerber),
    ("xmas-led-001.gml", Some(OUTLINE), Categories.mechanical),
    ("xmas-led-001.txt", Some(DRILL), Categories.mechanical)
  )

  // Define test expectations for AXO_Trimming project
  // val AxoTrimmingProject = Paths.get(getClass.getResource("/gerber/AXO_Trimming_TFM-PLCC28").toURI)
  val AxoTrimmingFiles = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("AXO_Trimming_TFM-PLCC28 - Top Copper.gbr", COPPER_TOP, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - Bottom Copper.gbr", COPPER_BOTTOM, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - Top Copper Resist.gbr", SOLDERMASK_TOP, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - Bottom Copper Resist.gbr", SOLDERMASK_BOTTOM, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - Top Silkscreen.gbr", SILKSCREEN_TOP, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - Bottom Silkscreen.gbr", SILKSCREEN_BOTTOM, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - Top Copper Paste.gbr", PASTE_TOP, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - Bottom Copper Paste.gbr", PASTE_BOTTOM, Categories.gerber),
    ("AXO_Trimming_TFM-PLCC28 - NC Drill Data - Through Hole.drl", PH_DRILL, Categories.mechanical),
    ("AXO_Trimming_TFM-PLCC28 - NC Drill Data - Through Hole Unplated.drl", NPH_DRILL, Categories.mechanical)
  )

  // Define test expectations for Z4P4396A project
  val Z4P4396AFiles = Table(
    ("fileName", "expectedFileType", "expectedCategory"),
    ("LAY1.gdo", COPPER_TOP, Categories.gerber),
    ("LAY10.gdo", COPPER_BOTTOM, Categories.gerber),
    ("LAY2.gdo", COPPER_MID, Categories.gerber),
    ("LAY3.gdo", COPPER_MID, Categories.gerber),
    ("LSM1.gdo", SOLDERMASK_TOP, Categories.gerber),
    ("LSM10.gdo", SOLDERMASK_BOTTOM, Categories.gerber),
    ("PAS1.gdo", PASTE_TOP, Categories.gerber),
    ("PAS10.gdo", PASTE_BOTTOM, Categories.gerber),
    ("ThruHolePlated.ncd", PH_DRILL, Categories.mechanical),
    ("ThruHoleNonPlated.ncd", NPH_DRILL, Categories.mechanical)
  )

  "DefaultFileMatcher" should {
    "correctly match WemosD1_clone_RGB Gerber files" in {
      testFilesFromTable(WemosD1Project, WemosD1Files)
    }

    "correctly match P_3677229_Netlist Gerber files" in {
      testFilesFromTable(P_3677229_Netlist, P_3677229_NetlistFiles)
    }

    "correctly match P_3677263_Gerber Gerber files" in {
      testFilesFromTable(P_3677263_Gerber, P_3677263_GerberFiles)
    }

    "correctly match P_3693822_EVA Gerber files" in {
      testFilesFromTable(P_3693822_EVA, P_3693822_EVAFiles)
    }

    "correctly match Xmas-led Gerber files" in {
      testFilesFromTable(XmasLedProject, XmasLedFiles)
    }

    "correctly match 2_Layer_PTV0730700A0_GX2_2 Gerber files" in {
      testFilesFromTable(`2_Layer_PTV0730700A0_GX2_2`, `2_Layer_PTV0730700A0_GX2_2_Files`)
    }

    // these are not deterministic, for some reason
    // "correctly match A2619 Gerber files" in {
    //   testFilesFromTable(A2619, A2619Files)
    // }

    // "correctly match A3640 Gerber files" in {
    //   testFilesFromTable(A3640, A3640Files)
    // }

    "correctly match HDMIBoard Gerber files" in {
      testFilesFromTable(HDMIBoard, HDMIBoardFiles)
    }

    "correctly match TigoMaster Gerber files" in {
      testFilesFromTable(TigoMaster, TigoMasterFiles)
    }

    "correctly match EIDBoard Gerber files" in {
      testFilesFromTable(EIDBoard, EIDBoardFiles)
    }

    "correctly match NordicEval Gerber files" in {
      testFilesFromTable(NordicEval, NordicEvalFiles)
    }

    "correctly match Player Gerber files" in {
      testFilesFromTable(Player, PlayerFiles)
    }

    "correctly match SawTooth Gerber files" in {
      testFilesFromTable(SawTooth, SawToothFiles)
    }

    "correctly match TYAA_CHL_SM Gerber files" in {
      testFilesFromTable(TYAA_CHL_SM, TYAA_CHL_SM_Files)
    }
  }

  private def testFilesFromTable(
      project: Path,
      table: TableFor3[String, Option[String], Option[String]]
  ) = {
    val files   = project.toFile.listFiles().toSeq.map(f => FilePath(f.getAbsolutePath))
    val matcher = DefaultFilesMatcher.apply(files).matchFileType()

    // make sure that the number of matches is the same as the number of files in the project
    val expectedFileNames = table.toSeq.map(_._1).sorted
    val actualFileNames   = matcher.keySet.toSeq.map(_.filename).sorted

    // expectedFileNames should be(actualFileNames)

    forAll(table) { (fileName: String, expectedFileType: Option[String], expectedCategory: Option[String]) =>
      val fileMatch = withClue(s"File $fileName should have a match") {
        val testFile = FilePath(project.resolve(fileName).toString)
        matcher.get(testFile).value
      }

      withClue(s"File $fileName should have filetype $expectedFileType and category $expectedCategory:") {
        val fileType = fileMatch.map(_.fileType)
        val category = fileMatch.flatMap(_.category)
        (fileType, category) should be((expectedFileType, expectedCategory))
      }
    }
  }
}
