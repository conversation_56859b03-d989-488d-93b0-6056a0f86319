package de.fellows.ems.pcb.impl.matcher.filecontent

import de.fellows.ems.pcb.impl.matcher.{DefaultFiles<PERSON><PERSON><PERSON>, FileMatch}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import org.scalatest.BeforeAndAfterAll
import org.scalatest.Inspectors.forAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import play.api.libs.json.Json

import java.nio.charset.Charset
import java.nio.file.Files
import scala.jdk.CollectionConverters._

class ExtrepSpec extends AsyncWordSpec with BeforeAndAfterAll with Matchers {

  implicit val sd: ServiceDefinition = ServiceDefinition("unit-test")

  "Extrep matcher" should {
    "exclude duplicates" in {
      val extrep =
        """
------------------------------------------------------------------------------------------
Gerber File Extension Report For: S2Wireless.GBR   09.04.2018  15:56:05
------------------------------------------------------------------------------------------


------------------------------------------------------------------------------------------
Layer Extension     Layer Description
------------------------------------------------------------------------------------------
.GTL                Top Layer
.GTL                Bottom Layer
.GTL                Top Overlay
.GTL                Top Paste
.GTL                Top Solder
.GTL                Bottom Solder
.GTL                Bottom Paste
.GBO                Bottom Overlay
.GTL                Dimension 32
------------------------------------------------------------------------------------------
    """

      val maintmp     = Files.createTempDirectory("unit-test")
      val tmp         = Files.createDirectories(maintmp.resolve("files/test"))
      val tmpfile     = tmp.resolve("duplicates.extrep")
      val tmpfilepath = FilePath(tmpfile.toAbsolutePath.toString)

      val suppFiles = Map(
        "test.gtl"   -> None,
        "test.gtl"   -> None,
        "test.gtl"   -> None,
        "test.gtl"   -> None,
        "test.gtl"   -> None,
        "test.gtl"   -> None,
        "test.gtl"   -> None,
        "test.gbo"   -> Some(LayerConstants.SILKSCREEN_BOTTOM),
        "test.gtl"   -> None,
        "test.wrong" -> None
      ).map(x => FilePath(tmp.resolve(x._1).toAbsolutePath.toString) -> x._2)

      Files.write(tmpfile, extrep.split("\n").toSeq.asJava, Charset.defaultCharset())
      val matches = new ExtrepAnalyzer()
        .matchFile(
          tmpfile.toFile.getName,
          tmpfilepath,
          None,
          Seq(tmpfilepath) ++ suppFiles.keys
        )

      type X = FilePath
      val chosenMatches: Map[X, Option[FileMatch]] = DefaultFilesMatcher
        .chooseMatches[X](matches, DefaultFilesMatcher.DEFAULT_INVARIANTS[X], identity)

      assertMatches(suppFiles, chosenMatches)
    }

    "work" in {
      val validExtrep =
        """
------------------------------------------------------------------------------------------
Gerber File Extension Report For: S2Wireless.GBR   09.04.2018  15:56:05
------------------------------------------------------------------------------------------


------------------------------------------------------------------------------------------
Layer Extension     Layer Description
------------------------------------------------------------------------------------------
.GTL                Top Layer
.GBL                Bottom Layer
.GTO                Top Overlay
.GTP                Top Paste
.GTS                Top Solder
.GBS                Bottom Solder
.GBP                Bottom Paste
.GBO                Bottom Overlay
.GM32               Dimension 32
------------------------------------------------------------------------------------------
    """

      val maintmp = Files.createTempDirectory("unit-test")
      val tmp     = Files.createDirectories(maintmp.resolve("files/test"))
      val tmpfile = tmp.resolve("valid.extrep")

      val suppFiles = Map(
        "test.gtl"   -> Some(LayerConstants.COPPER_TOP),
        "test.gbl"   -> Some(LayerConstants.COPPER_BOTTOM),
        "test.gto"   -> Some(LayerConstants.SILKSCREEN_TOP),
        "test.gtp"   -> Some(LayerConstants.PASTE_TOP),
        "test.gts"   -> Some(LayerConstants.SOLDERMASK_TOP),
        "test.gbs"   -> Some(LayerConstants.SOLDERMASK_BOTTOM),
        "test.gbp"   -> Some(LayerConstants.PASTE_BOTTOM),
        "test.gbo"   -> Some(LayerConstants.SILKSCREEN_BOTTOM),
        "test.gm32"  -> Some(LayerConstants.OUTLINE),
        "test.wrong" -> None
      ).map(x => FilePath(tmp.resolve(x._1).toAbsolutePath.toString) -> x._2)

      suppFiles.foreach(s => Files.write(s._1.toJavaPath, Seq().asJava, Charset.defaultCharset()))

      Files.write(tmpfile, validExtrep.split("\n").toSeq.asJava, Charset.defaultCharset())

      val tmpfilepath = FilePath(tmpfile.toAbsolutePath.toString)

      val matches = new ExtrepAnalyzer()
        .matchFile(
          tmpfile.toFile.getName,
          tmpfilepath,
          None,
          Seq(tmpfilepath) ++ suppFiles.keys
        )

      type X = FilePath
      val chosenMatches: Map[X, Option[FileMatch]] = DefaultFilesMatcher
        .chooseMatches[X](matches, DefaultFilesMatcher.DEFAULT_INVARIANTS[X], identity)

      assertMatches(suppFiles, chosenMatches)

    }
  }

  private def assertMatches(
      suppFiles: Map[FilePath, Option[String]],
      chosenMatches: Map[FilePath, Option[FileMatch]]
  ) = {
    forAll(suppFiles) { x =>
      val m = chosenMatches.get(x._1).flatten

      if (x._2.nonEmpty) {
        m should not be empty
      } else {
        m shouldBe empty
      }
    }

    forAll(suppFiles) { x =>
      val m = chosenMatches.get(x._1).flatten

      x._2 match {
        case Some(value) => m.flatMap(_.fileType) should contain(value)
        case None        => assert(true)
      }
    }
  }
}
