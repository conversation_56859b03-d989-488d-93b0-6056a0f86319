package de.fellows.ems.pcb.impl.matcher

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.FilePath
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import play.api.libs.json.{Format, Json}

import java.nio.file.Paths

class InvariantsSpec extends AsyncWordSpec with BeforeAndAfterAll with Matchers {
  case class TestSubject(id: String, filePath: FilePath)

  object TestSubject {
    implicit val f: Format[TestSubject] = Json.format[TestSubject]
  }

  "InnerOuterCopperCount" should {
    "select most likely top" in {

      val max = 9
      val resolved = createAndResolveCopper(
        max,
        index =>
          FileMatch(
            confidence = index,
            matcher = "???",
            category = Some("gerber"),
            fileType = Some(LayerConstants.COPPER_TOP),
            index = Some(index)
          )
      )

      val resolvedMatches = resolved.get.flatMap(_._2.flatMap(_.fileType))

      resolvedMatches.count(_ == LayerConstants.COPPER_TOP) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_BOTTOM) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_MID) should be(max - 2)

      resolved.get.flatMap(_._2).find(_.fileType.contains(LayerConstants.COPPER_TOP)).map(_.confidence) should contain(
        FullConfidence
      )

    }
    "select most likely bottom" in {

      val max = 9
      val resolved = createAndResolveCopper(
        max,
        index =>
          FileMatch(
            confidence = index,
            matcher = "???",
            category = Some("gerber"),
            fileType = Some(LayerConstants.COPPER_BOTTOM),
            index = Some(index)
          )
      )

      val resolvedMatches = resolved.get.flatMap(_._2.flatMap(_.fileType))

      resolvedMatches.count(_ == LayerConstants.COPPER_TOP) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_BOTTOM) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_MID) should be(max - 2)

      resolved.get.flatMap(_._2).find(_.fileType.contains(LayerConstants.COPPER_BOTTOM)).map(
        _.confidence
      ) should contain(
        FullConfidence
      )

    }

    "select top and bottom from only tops" in {

      val max = 9
      val resolved = createAndResolveCopper(
        max,
        index =>
          FileMatch(
            confidence = MediumConfidence,
            matcher = "???",
            category = Some("gerber"),
            fileType = Some(LayerConstants.COPPER_TOP),
            index = Some(index)
          )
      )
      val resolvedMatches = resolved.get.flatMap(_._2.flatMap(_.fileType))
      resolvedMatches.count(_ == LayerConstants.COPPER_TOP) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_BOTTOM) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_MID) should be(max - 2)
    }
    "select top and bottom from only bottoms" in {

      val max = 9
      val resolved = createAndResolveCopper(
        max,
        index =>
          FileMatch(
            confidence = MediumConfidence,
            matcher = "???",
            category = Some("gerber"),
            fileType = Some(LayerConstants.COPPER_BOTTOM),
            index = Some(index)
          )
      )
      val resolvedMatches = resolved.get.flatMap(_._2.flatMap(_.fileType))

      resolvedMatches.count(_ == LayerConstants.COPPER_TOP) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_BOTTOM) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_MID) should be(max - 2)
    }

    "select top and bottom from inner coppers" in {
      val max = 9
      val resolved = createAndResolveCopper(
        max,
        index =>
          FileMatch(
            confidence = MediumConfidence,
            matcher = "???",
            category = Some("gerber"),
            fileType = Some(LayerConstants.COPPER_MID),
            index = Some(index)
          )
      )

      val resolvedMatches = resolved.get.flatMap(_._2.flatMap(_.fileType))

      resolvedMatches.count(_ == LayerConstants.COPPER_TOP) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_BOTTOM) should be(1)
      resolvedMatches.count(_ == LayerConstants.COPPER_MID) should be(max - 2)
    }
  }

  private def createAndResolveCopper(max: Int, c: Int => FileMatch): Option[Map[TestSubject, Seq[FileMatch]]] = {
    val parentPath = Paths.get("/tmp/test/unit/invariants")
    val coppers = Range.inclusive(1, max).map { index =>
      val m = c(index)

      TestSubject(index.toString, FilePath(parentPath.resolve(index.toString).toString)) -> FileMatchChoice(
        chosen = Option(m),
        options = Seq(m)
      )
    }.toMap

    val resolved = new InnerOuterCopperCount[TestSubject].resolve(coppers, _.filePath)
    resolved should not be empty

    resolved

  }

  "SimpleFileTypeLimit" should {
    "limit correctly" in {
      val parentPath = Paths.get("/tmp/test/unit/invariants")
      val ftype      = LayerConstants.COPPER_TOP

      val copperMatches = Seq(5, 6).map(conf =>
        FileMatch(
          confidence = conf,
          matcher = "???",
          category = Some("gerber"),
          fileType = Some(LayerConstants.COPPER_TOP)
        )
      )
      val alternatives = Seq(LayerConstants.SILKSCREEN_BOTTOM, LayerConstants.SILKSCREEN_TOP).map(ft =>
        FileMatch(
          confidence = MediumLowConfidence,
          matcher = "???",
          category = Some("gerber"),
          fileType = Some(ft)
        )
      )

      val resolved = new SimpleFileTypeLimit[TestSubject](ftype, 1).resolve(
        Map(
          TestSubject("1", FilePath(parentPath.resolve("1").toString)) -> FileMatchChoice(
            chosen = Some(copperMatches(0)),
            options = Seq(copperMatches(0), alternatives(0))
          ),
          TestSubject("2", FilePath(parentPath.resolve("2").toString)) -> FileMatchChoice(
            chosen = Some(copperMatches(1)),
            options = Seq(copperMatches(1), alternatives(1))
          )
        ),
        _.filePath
      )

      resolved should not be empty
      resolved.get(TestSubject("1", FilePath(parentPath.resolve("1").toString))) should be(Seq(alternatives(0)))
      resolved.get(TestSubject("2", FilePath(parentPath.resolve("2").toString))) should be(Seq(
        copperMatches(1),
        alternatives(1)
      ))
    }

  }
}
