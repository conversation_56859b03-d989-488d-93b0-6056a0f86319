package de.fellows.ems.pcb.model.graphics

import de.fellows.ems.pcb.model.graphics.Paths.FunctionalPathIterator
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.awt.geom.{Ellipse2D, Line2D, Rectangle2D}

class PathIteratorSpec extends AnyWordSpec with BeforeAndAfterAll with Matchers {

  "PathIterator" should {
    "rectangle" in {
      val rec    = new Rectangle2D.Double(0, 0, 100, 100)
      val curves = rec.getPathIterator(null).toSeq()

      curves should be(Seq(
        MoveTo(0.0, 0.0, 1),
        LineTo(0.0, 0.0, 100.0, 0.0, 1),
        LineTo(100.0, 0.0, 100.0, 100.0, 1),
        LineTo(100.0, 100.0, 0.0, 100.0, 1),
        LineTo(0.0, 100.0, 0.0, 0.0, 1),
        Close(0.0, 0.0, 0.0, 0.0, 1)
      ))
    }
    "circle" in {
      val rec    = new Ellipse2D.Double(0, 0, 100, 100)
      val curves = rec.getPathIterator(null).toSeq()

      curves should be(Seq(
        MoveTo(100.0, 50.0, 1),
        CubicTo(100.0, 50.0, 100.0, 77.61423749153965, 77.61423749153965, 100.0, 50.0, 100.0, 1),
        CubicTo(50.0, 100.0, 22.38576250846033, 100.0, 0.0, 77.61423749153965, 0.0, 50.0, 1),
        CubicTo(0.0, 50.0, 0.0, 22.38576250846033, 22.38576250846033, 0.0, 50.0, 0.0, 1),
        CubicTo(50.0, 0.0, 77.61423749153965, 0.0, 100.0, 22.38576250846033, 100.0, 50.0, 1),
        Close(100.0, 50.0, 100.0, 50.0, 1)
      ))
    }
    "line" in {
      val rec    = new Line2D.Double(0, 0, 100, 100)
      val curves = rec.getPathIterator(null).toSeq()
      curves should be(Seq(
        MoveTo(0.0, 0.0, 1),
        LineTo(0.0, 0.0, 100.0, 100.0, 1)
      ))
    }
  }
}
