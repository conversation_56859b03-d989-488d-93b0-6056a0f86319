//package de.fellows.ems.pcb.model.codec
//
//import java.nio.ByteBuffer
//
//import com.datastax.driver.core.{ProtocolVersion, TypeCodec, UDTValue, UserType}
//import de.fellows.ems.pcb.model.{Format, LayerFile}
//import de.fellows.utils.internal.FileType
//
//class LayerFileCodec(cdc: TypeCodec[UDTValue]) extends TypeCodec[LayerFile](cdc.getCqlType, classOf[LayerFile]) {
//
//  //            typeService text,
//  //            typeCategory text,
//  //            typeType text,
//  //            typeProd boolean,
//  //            typeMime text,
//  //            typeIndex decimal,
//  def to(value: LayerFile): UDTValue = {
//    if (value == null) null
//    else
//      cdc.getCqlType.asInstanceOf[UserType].newValue()
//        .setUUID("id", value.id.orNull)
//        .setString("name", value.name.orNull)
//        .setString("data", value.data.orNull)
//        .set("format", value.format.orNull, classOf[Format])
//        .set("fileType", value.fileType.orNull, classOf[FileType])
//        .setBool("inverted", value.inverted)
//
//  }
//
//  def getBigDecimal(value: UDTValue, name: String) = {
//    val v: java.math.BigDecimal = value.getDecimal(name)
//
//    v match {
//      case null => None
//      case bd => Some(BigDecimal(bd))
//    }
//  }
//
//  def from(value: UDTValue): LayerFile = {
//    //    FilePath(value.getString("root"), value.getString("base"), value.getString("name"))
//    if (value == null) null
//    else
//      LayerFile(
//        Option(value.getUUID("id")),
//        Option(value.getString("name")),
//        Option(value.getString("data")),
//        Option(value.get("format", classOf[Format])),
//        Option(value.get("fileType", classOf[FileType])),
//        Option(value.getBool("inverted")).getOrElse(false),
//      )
//  }
//
//  override def serialize(value: LayerFile, protocolVersion: ProtocolVersion): ByteBuffer = cdc.serialize(to(value), protocolVersion)
//
//  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): LayerFile = from(cdc.deserialize(bytes, protocolVersion))
//
//
//  override def parse(value: String): LayerFile =
//    if (value == null || value.isEmpty) null
//    else from(cdc.parse(value))
//
//  override def format(value: LayerFile): String =
//    if (value == null) null
//    else cdc.format(to(value))
//}
