package de.fellows.ems.pcb.model.codec

import com.datastax.driver.core.{ ProtocolVersion, TypeCodec, UDTValue }

import java.nio.ByteBuffer

@deprecated
abstract class AbstractCodec[X >: Null](cdc: TypeCodec[UDTValue], cl: Class[X])
    extends TypeCodec[X](cdc.getCqlType, cl) {

  def toUDTValue(value: X): UDTValue

  def toPoint(value: UDTValue): X

  override def serialize(value: X, protocolVersion: ProtocolVersion): ByteBuffer =
    cdc.serialize(toUDTValue(value), protocolVersion)

  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): X =
    toPoint(cdc.deserialize(bytes, protocolVersion))

  def getBigDecimal(value: UDTValue, name: String) = {
    val v: java.math.BigDecimal = value.getDecimal(name)

    v match {
      case null => None
      case bd   => Some(BigDecimal(bd))
    }
  }

  override def parse(value: String): X =
    if (value == null || value.isEmpty) null
    else toPoint(cdc.parse(value))

  override def format(value: X): String =
    if (value == null) null
    else cdc.format(toUDTValue(value))
}
