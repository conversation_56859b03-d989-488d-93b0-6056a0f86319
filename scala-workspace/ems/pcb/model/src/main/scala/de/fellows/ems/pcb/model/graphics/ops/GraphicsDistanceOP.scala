package de.fellows.ems.pcb.model.graphics.ops

import de.fellows.ems.pcb.model.graphics.Paths.FunctionalPathIterator
import de.fellows.ems.pcb.model.graphics.parts.{ComplexRegion, Curve, Flash, Line, Polygon}
import de.fellows.ems.pcb.model.graphics.tree.Distance
import de.fellows.ems.pcb.model.graphics.{Graphic, IntersectablePathSegment}

import java.awt.Shape

/** Gets the distance between two [[Graphic Graphics]]
  *
  * This class is more of a helper keeping all combinations of graphics in mind, and choosing a good strategy for
  * distance analysis for that particular combination. It most often just fallbacks to [[DistanceOp]], which does
  * distance measuring between arbitrary [[Shape Shapes]]
  */
class GraphicsDistanceOP(
    g1: Graphic,
    g2: Graphic,
    scaling: Double,
    margin: Option[Double] = None
) {

  private def genericDistance(x: Graphic, g2: Graphic): Seq[Distance] =
    new DistanceOp(x.shape, x.bounds, g2.shape, g2.bounds, scaling, margin).distance()

  private def polygonToPolygon(x: Polygon, y: Polygon): Seq[Distance] = {
    val orderedByComplexity = Seq(x, y).sortBy(_.complexity)

    orderedByComplexity.head.pathIterator.flatMap {
      case c1: IntersectablePathSegment => getDistances(orderedByComplexity(1), c1)
      case _                            => Seq.empty
    }
  }

  private def polygonToShape(x: Polygon, shape: Shape): Seq[Distance] =
    shape.getPathIterator(null).flatMap {
      case ycurve: IntersectablePathSegment => getDistances(x, ycurve)
      case _                                => Seq.empty
    }

  private def getDistances(x: Polygon, ycurve: IntersectablePathSegment): Seq[Distance] = {
    val neighbors = x.tree.neighbor(ycurve, margin.getOrElse(0))
    neighbors.flatMap { xcurve =>
      DistanceOp.distance(xcurve, ycurve)
    }
  }

  private def flashDistance(x: Flash, g2: Graphic) =
    // all possible combinations at this point are:
    // Flash -> Flash
    g2 match {
      case y => genericDistance(x, y)
    }

  private def lineDistance(x: Line, g2: Graphic) =
    // all possible combinations at this point are:
    // Line -> Line
    // Line -> Flash
    g2 match {
      case y => genericDistance(x, y)
    }

  private def curveDistance(x: Curve, g2: Graphic) =
    // all possible combinations at this point are:
    // Curve -> Curve
    // Curve -> Line
    // Curve -> Flash

    g2 match {
      case y => genericDistance(x, y)
    }

  private def complexRegionDistance(x: ComplexRegion, g2: Graphic) =
    // all possible combinations at this point are:
    // ComplexRegion -> ComplexRegion
    // ComplexRegion -> Curve
    // ComplexRegion -> Line
    // ComplexRegion -> Flash
    g2 match {
      case y => genericDistance(x, y)
    }

  private def polygonDistance(x: Polygon, g2: Graphic) = {
    // all possible combinations at this point are:
    // Polygon -> Polygon
    // Polygon -> ComplexRegion
    // Polygon -> Curve
    // Polygon -> Line
    // Polygon -> Flash

    val dst = g2 match {
      case y: Polygon =>
        polygonToPolygon(x, y)

      case y => polygonToShape(x, y.shape)
    }

    dst.minByOption(_.distance).toSeq
  }

  private def sort(d: Seq[Graphic]): Seq[Graphic] = {
    val prio = Seq(classOf[Polygon], classOf[ComplexRegion], classOf[Curve], classOf[Line], classOf[Flash])
    d.sortBy { i =>
      val index = prio.indexOf(i.getClass)
      if (index >= 0) {
        index
      } else {
        prio.length + 1
      }
    }
  }

  def distance(): Seq[Distance] = {
    val sorted = sort(Seq(g1, g2))
    val second = sorted(1)

    // all possible combinations at this point are:
    // Polygon -> Polygon
    // Polygon -> ComplexRegion
    // Polygon -> Curve
    // Polygon -> Line
    // Polygon -> Flash
    // ComplexRegion -> ComplexRegion
    // ComplexRegion -> Curve
    // ComplexRegion -> Line
    // ComplexRegion -> Flash
    // Curve -> Curve
    // Curve -> Line
    // Curve -> Flash
    // Line -> Line
    // Line -> Flash
    // Flash -> Flash

    sorted.head match {
      case x: Polygon =>
        polygonDistance(x, second)
      case x: ComplexRegion =>
        complexRegionDistance(x, second)
      case x: Curve =>
        curveDistance(x, second)
      case x: Line =>
        lineDistance(x, second)
      case x: Flash =>
        flashDistance(x, second)
    }
  }

}
