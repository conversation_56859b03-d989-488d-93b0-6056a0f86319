package de.fellows.ems.pcb.model.graphics.bvh

import de.fellows.ems.pcb.model.graphics.lookup.{LookupCollector, LookupWalker}
import de.fellows.ems.pcb.model.graphics.{CollisionChecker, Intersectable}

import java.awt.geom.Rectangle2D

class BVHInnerNode[T <: Intersectable[T]](var left: BVHNode[T], var right: BVHNode[T]) extends BVHNode[T] {
  lazy val box = {

    val l = left.boundingBox
    val r = right.boundingBox

    l.add(r)

    l
  }

  override def boundingBox: Rectangle2D = box

  def withChild[A](query: T)(x: (Option[BVHNode[T]], Option[BVHNode[T]], T) => A) = {
    val l = left.boundingBox
    val r = right.boundingBox

    val queryBounds = query.bounds

    val li = l.intersects(queryBounds)
    val ri = r.intersects(queryBounds)
    if (li && ri) {
      //      throw new IllegalStateException("Query Intersects both child nodes")

      x(Some(left), Some(right), query)
    } else if (li) {
      x(Some(left), None, query)
    } else if (ri) {
      x(None, Some(right), query)
    } else {
      x(None, None, query)
    }
  }

  def collect[U](walker: LookupCollector[T, U]) =
    right.collect(walker) ++
      left.collect(walker)

  def walk(walker: LookupWalker[T]) = {
    right.walk(walker)
    left.walk(walker)
  }

  def walkTree(walker: BVHWalker[T], depth: Int) = {
    walker.accept(this, depth)

    right.walkTree(walker, depth + 1)
    left.walkTree(walker, depth + 1)
  }

  def head(): Option[T] = right.head().orElse(left.head())

  def copy: BVHNode[T] =
    new BVHInnerNode[T](left.copy, right.copy)

  override def collide(
      check: T,
      envelope: Rectangle2D,
      pop: Boolean,
      collisionChecker: CollisionChecker[T, T]
  ): Seq[T] =
    withChild(check.asInstanceOf[T]) {
      (l, r, t) =>
        Seq(
          l.map(x => x.collide(t, envelope, pop, collisionChecker)),
          r.map(x => x.collide(t, envelope, pop, collisionChecker))
        ).flatten.flatten
    }
  //    withChild(check) { (n, t) =>
  //      n.collide(t, collisionChecker)
  //    }

  //  override def pop(value: T => Boolean): Seq[T] = {
  //    val lr = popChild(left, value)
  //    val rr = popChild(right, value)
  //
  //    left = lr._2.orNull
  //    right = rr._2.orNull
  //
  //
  //
  //    ???
  //    //    right match {
  //    //      case x: BVHInnerNode[T] => x.pop(value)
  //    //      case x: BVHLeaf[T] => {
  //    //        val r = Seq(x.value)
  //    //        if (value(x.value)) {
  //    //          left = null
  //    //        }
  //    //        r
  //    //      }
  //    //    }
  //  }
  //
  //  private def popChild(n: BVHNode[T], value: T => Boolean) = {
  //    n match {
  //      case x: BVHInnerNode[T] => {
  //        val r = x.pop(value)
  //        if (x.left == null && x.right == null) {
  //          (r, None)
  //        } else {
  //          (r, Some(n))
  //        }
  //      }
  //
  //      case x: BVHLeaf[T] => {
  //        val r = Seq(x.value)
  //        if (value(x.value)) {
  //          (r, None)
  //        } else {
  //          (r, Some(n))
  //        }
  //      }
  //    }
  //  }
}
