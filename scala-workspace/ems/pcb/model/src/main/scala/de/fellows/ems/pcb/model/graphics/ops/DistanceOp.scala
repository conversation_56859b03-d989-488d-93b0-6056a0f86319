package de.fellows.ems.pcb.model.graphics.ops

import de.fellows.ems.pcb.model.graphics.Geometry._
import de.fellows.ems.pcb.model.graphics.tree.Distance
import de.fellows.ems.pcb.model.graphics.{
  Close,
  CubicTo,
  GPoint,
  Geometry,
  Graphic,
  IntersectablePathSegment,
  LineTo,
  QuadTo
}

import java.awt.Shape
import java.awt.geom.{CubicCurve2D, Line2D, Rectangle2D}
import scala.collection.mutable.ListBuffer

/** Measure the distance between g1 and g2 by running through the paths of both shapes. WARNNG: This has exponentional
  * runtime
  *
  * @param scaling
  */
case class DistanceOp(
    g1s: Shape,
    g1Bounds: Rectangle2D,
    g2s: Shape,
    g2Bounds: Rectangle2D,
    scaling: Double,
    margin: Option[Double] = None
) {

  def distance(): Seq[Distance] = {
    val g2ExtBounds                  = margin.map(m => Graphic.extendRect(g2Bounds, m))
    val g1ExtBounds                  = margin.map(m => Graphic.extendRect(g1Bounds, m))
    var currentMin: Option[Distance] = None

    PathVisitor(g1s, g2ExtBounds).visit { (segment1, points1) =>
      val fd = DistanceOp.fastDistance(segment1, points1, g2Bounds)

      currentMin match {
        case Some(min) if fd > min.distance =>
        // do nothing if distance is higher than the current min

        // if no current min or if the distance is less than the current min, we need to check the distance
        case _ =>
          PathVisitor(g2s, g1ExtBounds).visit { (segment2, points2) =>
            val res = DistanceOp.distance(segment1, points1, segment2, points2)
              .filter(d => d.distance >= (DistanceOp.MIN_DISTANCE * scaling))

            res.minByOption(_.distance) match {
              case min @ Some(_) =>
                currentMin = currentMin match {
                  case None                                       => min
                  case Some(v) if min.value.distance < v.distance => min
                  case _                                          => currentMin
                }
              case None =>
            }
          }
      }
    }

    currentMin.toSeq
  }

}

object DistanceOp {
  private val MIN_DISTANCE = 0.0001

  def bounds(segment1: String, points1: Seq[GPoint]): Rectangle2D =
    segment1 match {
      case "LINE" =>
        new Line2D.Double(points1(0), points1(1)).getBounds2D
      case "CUBIC" =>
        val v = new CubicCurve2D.Double(
          points1(0).x,
          points1(0).y,
          points1(1).x,
          points1(1).y,
          points1(2).x,
          points1(2).y,
          points1(3).x,
          points1(3).y
        )
        Geometry.bounds(v).get
    }

  // Experiment: calculate the distance between edges of the rectangle
  //  private def fastDistance2(segment1: String, points1: Seq[GPoint], rec2: Rectangle2D): Double = {
  //    val rec1      = DistanceOp.bounds(segment1, points1)
  //    val xDistance = Math.abs(rec1.getCenterX - rec2.getCenterX) - ((rec1.getWidth + rec2.getWidth) / 2)
  //    val yDistance = Math.abs(rec1.getCenterY - rec2.getCenterY) - ((rec1.getHeight + rec2.getHeight) / 2)
  //    val d         = Math.max(xDistance, yDistance)
  //
  //    // distance is less than 0 if the rectangles intersect
  //    Math.max(0.0, d)
  //  }

  private def fastDistance2(
      segment1: String,
      points1: Seq[GPoint],
      rec2: Rectangle2D
  ): Option[Distance] = {
    val rec1 = DistanceOp.bounds(segment1, points1)

    if (rec1.intersects(rec2)) {
      None
    } else {
      val p1 = Array(
        GPoint(rec1.getMinX, rec1.getMinY),
        GPoint(rec1.getMinX, rec1.getMaxY),
        GPoint(rec1.getMaxX, rec1.getMinY),
        GPoint(rec1.getMaxX, rec1.getMaxY)
      )

      val p2 = Array(
        GPoint(rec2.getMinX, rec2.getMinY),
        GPoint(rec2.getMinX, rec2.getMaxY),
        GPoint(rec2.getMaxX, rec2.getMinY),
        GPoint(rec2.getMaxX, rec2.getMaxY)
      )

      val sides1 = Array(
        (GPoint(rec1.getMinX, rec1.getMinY), GPoint(rec1.getMaxX, rec1.getMinY)), // top
        (GPoint(rec1.getMinX, rec1.getMaxY), GPoint(rec1.getMaxX, rec1.getMaxY)), // bot
        (GPoint(rec1.getMinX, rec1.getMinY), GPoint(rec1.getMinX, rec1.getMaxY)), // left
        (GPoint(rec1.getMaxX, rec1.getMinY), GPoint(rec1.getMaxX, rec1.getMaxY))  // right
      )

      val sides2 = Array(
        (GPoint(rec2.getMinX, rec2.getMinY), GPoint(rec2.getMaxX, rec2.getMinY)), // top
        (GPoint(rec2.getMinX, rec2.getMaxY), GPoint(rec2.getMaxX, rec2.getMaxY)), // bot
        (GPoint(rec2.getMinX, rec2.getMinY), GPoint(rec2.getMinX, rec2.getMaxY)), // left
        (GPoint(rec2.getMaxX, rec2.getMinY), GPoint(rec2.getMaxX, rec2.getMaxY))  // right
      )

      ((for {
        pt1 <- p1
        pt2 <- p2
      } yield Distance(pt1, pt2)) ++
        p1.flatMap { p =>
          sides2.map { d =>
            pointToLine(p, d._1, d._2)
          }
        } ++
        p2.flatMap { p =>
          sides1.map { d =>
            pointToLine(p, d._1, d._2)
          }
        }).minByOption(_.distance)
    }
  }

  private def fastDistance(segment1: String, points1: Seq[GPoint], rec2: Rectangle2D): Double =
    fastDistance2(segment1, points1, rec2).fold(0.0)(_.distance)

  def distance(
      segment1: IntersectablePathSegment,
      segment2: IntersectablePathSegment
  ): Seq[Distance] = {

    def toSegmentName(s: IntersectablePathSegment) = s match {
      case _: LineTo  => Some("LINE")
      case _: QuadTo  => Some("QUAD")
      case _: CubicTo => Some("CUBIC")
      case _: Close   => Some("LINE")
    }

    (toSegmentName(segment1), toSegmentName(segment2)) match {
      case (Some(s1), Some(s2)) => distance(s1, segment1.toPoints, s2, segment2.toPoints)
      case _                    => Seq()
    }

  }

  def distance(
      segment1: String,
      points1: Seq[GPoint],
      segment2: String,
      points2: Seq[GPoint]
  ): Seq[Distance] =
    (segment1, segment2) match {
      case ("LINE", "LINE") =>
        Seq(
          pointToLine(points1(0), points2(0), points2(1)),
          pointToLine(points1(1), points2(0), points2(1)),
          pointToLine(points2(0), points1(0), points1(1)),
          pointToLine(points2(1), points1(0), points1(1))
        )

      case ("CUBIC", "LINE") =>
        cubicToLine(points1, points2)
      case ("LINE", "CUBIC") =>
        cubicToLine(points2, points1)
      case ("CUBIC", "CUBIC") =>
        cubicToCubic(points1, points2)

      case _ => ???
    }

  def cubicToCubic(points1: Seq[GPoint], points2: Seq[GPoint]): Seq[Distance] = {
    val sx1 = Geometry.radius(points1)
    val sx2 = Geometry.radius(points2)

    // three cases:
    // 1: edge to edge point. just create distances from/to all edges
    // 2: one of the ends is on the arc.
    // 3: both of the ends are on the arc. extra case: if the centerpoints are exactly the same, use case 1 (more accurate)

    // case 1
    (Seq(
      Distance(points1(0), points2(0)),
      Distance(points1(0), points2(3)),
      Distance(points1(3), points2(0)),
      Distance(points1(3), points2(3))
    ) ++ ((sx1, sx2) match {
      case (Some(x1), Some(x2)) =>
        val centerDistance = Distance(x1.center, x2.center)
        // case 2, create distances to the centers

        if (centerDistance.distance < 1) {
          Seq() // radii are the same, use another case.
        } else {
          val case2 = Seq(
            Geometry.clampWithArc(Distance(x1.center, points2(0)).inv, x1.center, x1.edgepoint, x1.radius, points1),
            Geometry.clampWithArc(Distance(x1.center, points2(3)).inv, x1.center, x1.edgepoint, x1.radius, points1),
            Geometry.clampWithArc(Distance(x2.center, points1(0)).inv, x2.center, x2.edgepoint, x2.radius, points2),
            Geometry.clampWithArc(Distance(x2.center, points1(3)).inv, x2.center, x2.edgepoint, x2.radius, points2)
          ).flatten

          // case 3, create distances from center to center
          val case3 = Seq(
            Geometry.clampWithArc(
              distance = centerDistance,
              arcCenter = x2.center,
              arcEdge = x2.edgepoint,
              arcRadius = x2.radius,
              arcPoints = points2
              //
            )
              .flatMap { d =>
                Geometry.clampWithArc(
                  distance = d.inv,
                  arcCenter = x1.center,
                  arcEdge = x1.edgepoint,
                  arcRadius = x1.radius,
                  arcPoints = points1
                ).map(_.inv)
              }
          ).flatten

          val arcRads1 = calcRadiansOnArc(x2.center, points2(0))
          val arcRads2 = calcRadiansOnArc(x2.center, points2(3))
          val srt      = Seq(arcRads1, arcRads2).sorted

          val res = (case2 ++
            case3)
            .filter { dst => // now filtering the distances that are outside of the original arc
              val prad = calcRadiansOnArc(x2.center, dst.end)

              val r =
                if (srt.forall(_ < 0) && prad < 0) {
                  prad <= srt(0) && prad >= srt(1)
                } else {
                  prad >= srt(0) && prad <= srt(1)
                }

              r
              //              true
            }
          res
        }

      case x =>
        //              throw new NotImplementedError(s"impl missing for ${x}")
        Seq()
    })
    //        ).minBy(_.distance)

    )
  }

  def cubicToPoint(cubicPoints: Seq[GPoint], point: GPoint): Seq[Distance] = {
    val _arc = Geometry.radius(cubicPoints)
    val a = _arc.flatMap(a =>
      Geometry.clampWithArc(Distance(a.center, point).inv, a.center, a.edgepoint, a.radius, cubicPoints)
    )
    if (a.isDefined) {
      Seq(
        a.get,
        Distance(cubicPoints(0), point),
        Distance(cubicPoints(3), point)
      )
    } else {
      Seq(
        Distance(cubicPoints(0), point),
        Distance(cubicPoints(3), point)
      )
    }
  }

  def cubicToLine(cubicPoints: Seq[GPoint], linePoints: Seq[GPoint]): Seq[Distance] = {

    val _arc = Geometry.radius(cubicPoints)

    val a =
      _arc.flatMap(a =>
        Geometry.clampWithArc(
          pointToLine(a.center, linePoints(0), linePoints(1)).inv,
          a.center,
          a.edgepoint,
          a.radius,
          cubicPoints
        )
      )

    if (a.isDefined) {
      Seq(
        a.get,
        Distance(cubicPoints(0), linePoints(0)),
        Distance(cubicPoints(0), linePoints(1)),
        Distance(cubicPoints(3), linePoints(0)),
        Distance(cubicPoints(3), linePoints(1))
      )
    } else {
      Seq(
        Distance(cubicPoints(0), linePoints(0)),
        Distance(cubicPoints(0), linePoints(1)),
        Distance(cubicPoints(3), linePoints(0)),
        Distance(cubicPoints(3), linePoints(1))
      )
    }

  }

}
