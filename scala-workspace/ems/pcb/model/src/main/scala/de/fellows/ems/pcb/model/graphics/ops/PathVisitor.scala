package de.fellows.ems.pcb.model.graphics.ops

import de.fellows.ems.pcb.model.graphics.GPoint

import java.awt.Shape
import java.awt.geom.PathIterator._
import java.awt.geom.Rectangle2D
import scala.collection.mutable.{Array<PERSON><PERSON>er, ListBuffer}
import scala.math.Ordering

/** visits all paths in a shape.
  * @deprecated
  *   use [[FunctionalPathIterator]] instead
  * @tparam T
  */
@deprecated
@Deprecated
case class PathVisitor[T](shape: Shape, bounds: Option[Rectangle2D] = None) {

  private def cubicBounds(
      bounds: Option[Rectangle2D],
      start: GPoint,
      param1: GPoint,
      param2: GPoint,
      end: GPoint
  ): Boolean =
    bounds.forall { rec =>
      rec.intersectsLine(start.x, start.y, param1.x, param1.y) ||
      rec.intersectsLine(param1.x, param1.y, param2.x, param2.y) ||
      rec.intersectsLine(param2.x, param2.y, end.x, end.y) ||
      rec.intersectsLine(end.x, end.y, start.x, start.y)
    }

  val check = new Array[Double](6)

  def visit(handle: (String, Seq[GPoint]) => Unit): Unit = {
    val paths = shape.getPathIterator(null)

    var curPoint: Option[GPoint]   = None
    var startPoint: Option[GPoint] = None
    var wasCircle                  = false

    while (!paths.isDone) {
      val cs = paths.currentSegment(check)

      cs match {
        case SEG_MOVETO =>
          val newPoint = GPoint(check(0), check(1))
          curPoint = Some(newPoint)
          startPoint = Some(newPoint)
          wasCircle = false

        case SEG_LINETO =>
          val newPoint = GPoint(check(0), check(1))
          if (bounds.forall(_.intersectsLine(curPoint.get.x, curPoint.get.y, newPoint.x, newPoint.y))) {
            handle("LINE", Seq(curPoint.get, newPoint))
          }
          curPoint = Some(newPoint)
          wasCircle = false

        case SEG_QUADTO =>
          ???
        //          val newPoint = GPoint(check(2), check(3))
        //          val param = GPoint(check(0), check(1))
        //          resultCollector += handle("QUAD", Seq(curPoint.get, param, newPoint))
        //
        //          curPoint = Some(newPoint)
        //          wasCircle = true
        case SEG_CUBICTO =>
          val newPoint = GPoint(check(4), check(5))

          val param1 = GPoint(check(0), check(1))
          val param2 = GPoint(check(2), check(3))
          if (cubicBounds(bounds, curPoint.get, param1, param2, newPoint)) {
            handle("CUBIC", Seq(curPoint.get, param1, param2, newPoint))
          }

          curPoint = Some(newPoint)
          wasCircle = true
        case SEG_CLOSE =>
          if (curPoint != startPoint) {
            if (bounds.forall(_.intersectsLine(curPoint.get.x, curPoint.get.y, startPoint.get.x, startPoint.get.y))) {
              handle("LINE", Seq(curPoint.get, startPoint.get))
            }
          }

          curPoint = None

        case _ =>
      }

      paths.next()
    }
  }

}
