package de.fellows.ems.pcb.model.graphics.parts

import de.fellows.ems.pcb.model.graphics._
import de.fellows.ems.pcb.model.graphics.tree.ElementId

import java.awt.Shape
import java.awt.geom.Rectangle2D

case class Line(
    aperture: ApertureDefinition,
    s: Shape,
    pol: Polarity,
    i: ElementId[Int],
    from: GPoint,
    to: GPoint,
    a: Option[Map[String, Seq[String]]]
) extends Graphic(pol, i, a) {

  override def shape: Shape = s

  override def intersects(other: Graphic): Boolean =
    this.index.x == other.index.x || (other match {
      case otherLine: Flash =>
        val otherArea = otherLine.shape
        doCollisionWithContour(other, otherArea)
      case otherLine: Line =>
        val otherArea = otherLine.shape
        doCollisionWithContour(other, otherArea)
      case _ =>
        super.intersects(other)
    })

  private def doCollisionWithContour(other: Graphic, otherArea: Shape) =
    if (!super.intersects(other)) {
      Paths.countourIntersects(shape, otherArea)
    } else {
      true
    }
}
