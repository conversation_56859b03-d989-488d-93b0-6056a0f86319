package de.fellows.ems.pcb.model

import enumeratum._
import enumeratum.EnumEntry.Lowercase

sealed trait SpecificationStatus extends EnumEntry with Lowercase

object SpecificationStatus extends Enum[SpecificationStatus] with PlayJsonEnum[SpecificationStatus] {
  val values: IndexedSeq[SpecificationStatus] = findValues

  case object Changed extends SpecificationStatus
  case object Active  extends SpecificationStatus
  case object Removed extends SpecificationStatus
}
