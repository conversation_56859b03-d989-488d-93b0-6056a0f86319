package de.fellows.ems.pcb.model.graphics.bvh

import de.fellows.ems.pcb.model.graphics.lookup.{LookupCollector, LookupWalker}
import de.fellows.ems.pcb.model.graphics.{CollisionChecker, Intersectable}

import java.awt.geom.Rectangle2D

class BVHLeaf[T <: Intersectable[T]](var value: T) extends BVHNode[T] {
  lazy val _b = value.bounds

  override def boundingBox: Rectangle2D = _b

  def collect[U](walker: LookupCollector[T, U]) =
    walker.accept(value)

  def walk(walker: LookupWalker[T]) =
    walker.accept(value)

  def head(): Option[T] = Some(value)

  def copy: BVHNode[T] =
    new BVHLeaf[T](value)

  override def walkTree(walker: BVHWalker[T], depth: Int): Unit =
    walker.accept(this, depth)

  override def collide(
      check: T,
      envelope: Rectangle2D,
      pop: <PERSON><PERSON><PERSON>,
      collisionChecker: Collision<PERSON>hecker[T, T]
  ): Vector[T] =
    //    Vector()
    if (check != value && boundingBox.intersects(envelope) && collisionChecker.collides(check, value)) {
      Vector(value)
    } else {
      Vector()
    }

}
