package de.fellows.ems.pcb.model.graphics

trait CollisionChecker[X, T <: Intersectable[T]] {

  def collides(one: X, two: T): Boolean
}

class CachedCollisionChecker[T <: Intersectable[T]] extends Collision<PERSON>hecker[T, T] {

  override def collides(one: T, two: T): <PERSON>olean =
    if (one.fastIntersects(two)) {
      one.intersects(two)
    } else {
      false
    }
}

class CachedNeighborChecker[T <: Intersectable[T]](margin: Double) extends CollisionChecker[T, T] {

  override def collides(one: T, two: T): Boolean =
    one.isNeighbor(two, margin)

}

abstract class ChainedCollisionChecker[X, T <: Intersectable[T]](chain: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[X, T])
    extends CollisionChecker[X, T] {
  def doCollides(one: X, two: T): Boolean

  override final def collides(one: X, two: T): Boolean =
    if (doCollides(one, two)) {
      chain.collides(one, two)
    } else {
      false
    }
}
