package de.fellows.ems.pcb.model.graphics.tree

import akka.Done
import de.fellows.ems.pcb.model.BigPoint
import de.fellows.ems.pcb.model.graphics.lookup._
import de.fellows.ems.pcb.model.graphics.{
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  GPoint,
  Graphic,
  Intersectable
}
import de.fellows.utils.FilePath
import de.fellows.utils.internal.FileWriter
import play.api.Logging
import play.api.libs.json._

import java.awt.Shape
import java.awt.geom.{Point2D, Rectangle2D}
import scala.annotation.tailrec
import scala.collection.mutable
import scala.collection.mutable.ArrayBuffer

/** This quad tree keeps [[Intersectable graphical elements]] in an index that can be used for fast collision detection
  * @param min
  * @param max
  * @param maxPerBox
  * @tparam T
  */
class QuadTree[T <: Intersectable[T]](
    val min: GPoint,
    val max: GPoint,
    val maxPerBox: BigDecimal,
    val maxDepth: Option[Int]
) extends GraphicLookup[T] with Serializable with Logging {

  private var root = new Node[T](
    center = (min + max) / 2,
    width = (max.abs() - min.abs()).abs(),
    children = None,
    depth = Some(0)
  )

  override def finish(): Unit = {}

  override def size(detailed: Boolean = false): Int = {
    def sizeRec(node: Node[T]): Int = {
      var count = 0

      (detailed match {
        case true  => count += node.elements.size
        case false => count += 1
      })

      node.children match {
        case Some(s) => s.foreach(n => count += sizeRec(n))
        case None    =>
      }

      count
    }

    sizeRec(this.root)
  }

  def pop(value: (T => Boolean)): Seq[T] = {
    val coll = Seq.newBuilder[T]
    this.walkTree { node =>
      val (found, rest) = node.elements.partition(value)
      node.elements = rest
      coll ++= found
    }

    coll.result()
  }

  def remove(value: T => Boolean): Unit =
    this.walkTree { node =>
      node.elements.filterInPlace(e => !value(e))
    }

  def getComplexity: Int = {
    var c = 0
    this.walkTree { s =>
      c += s.elements.size
    }

    c
  }

  def getRoot(): Node[T] =
    root

  def popCollide(check: T): Vector[T] =
    collide(check, pop = true)

  def neighbor(check: T, margin: Double, collisionChecker: CollisionChecker[T, T]): Vector[T] =
    collideWithCheckerOrBoundsIter(
      check = check,
      envelope = Graphic.extendRect(check.bounds, 2 * margin),
      startNode = root,
      pop = false,
      collisionChecker = collisionChecker
    )

  def neighbor(check: T, margin: Double): Vector[T] =
    collideWithCheckerOrBoundsIter(
      check,
      Graphic.extendRect(check.bounds, 2 * margin),
      root,
      pop = false,
      new CachedNeighborChecker[T](margin)
    )

  def collide(check: T): Vector[T] =
    collide(check, pop = false)

  def getObjectAt(coord: GPoint): Option[T] =
    firstCollisionAt(
      coord,
      root,
      g =>
        g.contains(coord)
    )

  private def neighbors(margin: Double)(check: T, element: T): Boolean =
    element.isNeighbor(check, margin)

  private def firstCollisionAt(
      point: GPoint,
      n: Node[T],
      collisionChecker: T => Boolean
  ): Option[T] = {
    val collisionsHere = // if (n != root) {
      n.elements.filter(ne => collisionChecker(ne))

    val col = collisionsHere.headOption
    println(s"collisions here: ${collisionsHere}")
    col.orElse({
      //      val ch = n.whichChild(new Rectangle2D.Double(point.x, point.y, 0, 0))
      val ch = n.whichChild(new Point2D.Double(point.x, point.y))

      ch match {
        case Some(child) if child != n =>
          println(s"new childnode : ${ch}")
          firstCollisionAt(point, child, collisionChecker)
        case Some(child) =>
          println(s"self childnode : ${ch}, ${child.elements.filter(ne => collisionChecker(ne))} ${child.children}")
          child.children match {
            case None => None
            case Some(c) =>
              var found = Option.empty[T]
              c.foreach { n =>
                found = found.orElse(firstCollisionAt(point, n, collisionChecker))
              }
              found
          }
        case None =>
          println("no childnode")
          None
      }
    })
  }

  def collideWithCheckerOrBoundsIter[X <: Intersectable[T]](
      check: X,
      envelope: Rectangle2D,
      startNode: Node[T],
      pop: Boolean,
      collisionChecker: CollisionChecker[X, T]
  ): Vector[T] = {
    val collisions = Vector.newBuilder[T]

    def processElement(element: T): Boolean = {
      val collides = collisionChecker.collides(check, element)

      if (collides) {
        collisions += element
      }

      !collides
    }

    val nodes = new mutable.Queue[Node[T]]()
    nodes.enqueue(startNode)

    while (nodes.nonEmpty) {
      val n = nodes.dequeue()

      if (pop) {
        n.elements.filterInPlace(ne => processElement(ne))
      } else {
        n.elements.foreach(ne => processElement(ne))
      }

      val ch = n.whichChildrenQuery(envelope)

      nodes.enqueueAll(ch)
    }

    collisions.result()
  }

  override def collideWithChecker(
      check: T,
      envelope: Rectangle2D,
      pop: Boolean,
      collisionChecker: CollisionChecker[T, T]
  ): Vector[T] =
    collideWithCheckerOrBoundsIter(check, envelope, this.root, pop, collisionChecker)

  private lazy val collisionChecker = new CachedCollisionChecker[T]

  private def collide(check: T, pop: Boolean): Vector[T] =
    collideWithCheckerOrBoundsIter(
      check,
      Graphic.extendRect(check.bounds, 1),
      root,
      pop,
      collisionChecker
    )

  def insert(query: T): Unit = {
    def splitAndReorder(node: Node[T]): Unit = {
      node.makeChildren()
      val temp = node.elements
      node.elements = new mutable.ListBuffer[T]()

      for (element <- temp)
        insertRecursively(element, node)
    }

    @tailrec def insertRecursively(query: T, node: Node[T], prev: Option[Node[T]] = None): Unit =
      if (prev.isDefined && prev.get == node) {
        node.elements.append(query)
      } else {
        node.children match {
          case None =>
            val canSplit = (node.depth, maxDepth) match {
              case (Some(depth), Some(maxDepth)) => depth < maxDepth
              case _                             => true
            }
            if (node.elements.size >= maxPerBox && canSplit) {
              splitAndReorder(node)
              insertRecursively(query, node)
            } else {
              node.elements.append(query)
            }

          case Some(_) =>
            node.whichChild(query.bounds) match {
              case Some(n) => insertRecursively(query, n, Some(node))
              case None    =>
            }
        }
      }

    insertRecursively(query, root)
  }

  def walkTree(walker: TreeWalker[T]): Unit =
    root.walk(walker)

  override def collect[U](walker: LookupCollector[T, U]): Vector[U] = {
    val builder = Vector.newBuilder[U]

    walk { g =>
      builder ++= walker.accept(g)
    }

    builder.result()
  }

  override def walk(walker: LookupWalker[T]): Unit =
    root.walk(w => w.elements.foreach(walker.accept))

  /** find the first element in this tree where the given check applies. Optionally removes it from the tree as well.
    */
  def findFirst(check: (T => Boolean), remove: Boolean = false): Option[T] = {
    root.walk { x =>
      val res =
        if (remove) {
          val idx = x.elements.indexWhere(check) match {
            case -1 => None
            case i  => Some(i)
          }
          idx.map(x.elements.remove)
        } else {
          x.elements.find(check)
        }

      if (res.isDefined) {
        return res
      }
    }

    None
  }

  def nonEmpty: Boolean = {
    def r(node: Node[T]): Boolean =
      node.elements.nonEmpty || (node.children match {
        case Some(ns) => r(ns.topleft) || (r(ns.topright)) || (r(ns.bottomleft)) || (r(ns.bottomright))
        case None     => false
      })

    r(root)
  }

  def head(): Option[T] = {
    def headRec(n: Node[T]): Option[T] =
      if (n.elements.nonEmpty) {
        Some(n.elements.remove(0))
      } else {

        n.children match {
          case Some(ns) =>
            headRec(ns.topleft).orElse(headRec(ns.topright)).orElse(headRec(ns.bottomleft)).orElse(headRec(
              ns.bottomright
            ))
          case None => None
        }
      }

    headRec(root)
  }

  def copy(): QuadTree[T] = {
    val copied = new QuadTree[T](min, max, maxPerBox, maxDepth)

    copied.root = root.copy()

    copied
  }

  override def processElements(processor: T => Option[T]): Done = {
    walkTree { n =>
      n.elements.zipWithIndex.foreach { case (el, i) =>
        processor(el) match {
          case Some(newEl) => n.elements.update(i, newEl)
          case None        => ()
        }
      }
    }
    Done

  }
}

case class NodeContainer[T <: Intersectable[T]](
    topleft: Node[T],
    topright: Node[T],
    bottomleft: Node[T],
    bottomright: Node[T]
) {
  def foreach(f: Node[T] => Unit): Unit = {
    f(topleft)
    f(topright)
    f(bottomleft)
    f(bottomright)
  }
}

trait TreeWalker[T <: Intersectable[T]] {
  def accept(n: Node[T]): Unit
}

object QuadTree {

  def create[T <: Intersectable[T]](
      min: GPoint,
      max: GPoint,
      maxPerBox: BigDecimal,
      root: Node[T],
      maxDepth: Option[Int]
  ): QuadTree[T] = {
    val q = new QuadTree[T](min, max, maxPerBox, maxDepth)
    q.root = root
    q
  }

  implicit val qt: Writes[QuadTree[Graphic]] = new Writes[QuadTree[Graphic]] {
    override def writes(o: QuadTree[Graphic]): JsValue =
      Json.obj(
        "min"       -> o.min,
        "max"       -> o.max,
        "maxPerBox" -> o.maxPerBox,
        "root"      -> o.root
      )

  }

  import play.api.libs.functional.syntax._

  implicit val qtr: Reads[QuadTree[Graphic]] =
    ((JsPath \ "min").read[GPoint] and
      (JsPath \ "max").read[GPoint] and
      (JsPath \ "maxPerBox").read[BigDecimal] and
      (JsPath \ "maxDepth").readNullable[Int] and
      (JsPath \ "root").read[Node[Graphic]])((min, max, maxPerBox, maxDepth, root) =>
      QuadTree.create[Graphic](min, max, maxPerBox, root, maxDepth)
    )

  def stream(quadTree: QuadTree[Graphic], filePath: FilePath): Option[FilePath] = {
    val j = Json.toJson(quadTree)
    filePath.createParentDir()

    val bytes = Json.stringify(j).getBytes()
    val path  = filePath.copy(filename = s"${filePath.filename}.gzip")
    //    Files.write(path.toJavaPath, bytes)

    FileWriter.writeCompressed(path.toJavaPath, bytes)

    Some(filePath)
  }

  object NodeContainer {}

}
