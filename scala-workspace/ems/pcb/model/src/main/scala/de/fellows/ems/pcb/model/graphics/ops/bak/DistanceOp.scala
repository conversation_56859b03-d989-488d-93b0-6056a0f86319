//package de.fellows.ems.pcb.model.graphics.ops.bak
//
//import java.awt.geom.PathIterator
//import java.awt.geom.PathIterator._
//
//import de.fellows.ems.pcb.model.graphics.parts.Curve
//import de.fellows.ems.pcb.model.graphics.tree.Distance
//import de.fellows.ems.pcb.model.graphics.{GPoint, Geometry, Graphic}
//import math.geom2d.{Point2D, Vector2D}
//
//import scala.util.Random
//
//case class DistanceOp(g1: Graphic, g2: Graphic, precision: Int = 5) {
//
//  var inner = 0
//  var outer = 0
//
//
//  def pointToLine(p: GPoint, p0: GPoint, p1: GPoint) = {
//    val v = p1 - p0
//    val w = p - p0
//
//    val c1 = w dot v
//    val c2 = v dot v
//
//    if (c1 <= 0) {
//      Distance(p, p0)
//      //      Distance(BigPoint(0, 0), BigPoint(0, 0))
//
//    } else if (c2 <= c1) {
//      Distance(p, p1)
//      //      Distance(BigPoint(0, 0), BigPoint(0, 0))
//
//    } else {
//      val b = c1 / c2
//      val pb = p0 + (v * b)
//
//      Distance(p, pb)
//      //      Distance(p, p1, Seq(), s"pointToLine fake")
//
//      //      Distance(BigPoint(0,0), BigPoint(0,0))
//    }
//    //    Distance(p0, p1)
//  }
//
//  def lineToLine(fromLineStart: GPoint, fromLineEnd: GPoint, toLineStart: GPoint, toLineEnd: GPoint): Seq[Distance] = {
//    Seq(pointToLine(fromLineStart, toLineStart, toLineEnd),
//      pointToLine(fromLineEnd, toLineStart, toLineEnd))
//  }
//
//
//  def lineTo(fromLineStart: GPoint, fromLineEnd: GPoint, g2: Graphic): Seq[Distance] = {
//
//    val iter2 = g2.area().getPathIterator(null)
//
//    val distances = path(iter2){ (t, pts) =>
//      t match {
//        case "LINE" => lineToLine(fromLineStart, fromLineEnd, pts.head, pts(1)).map(x => x.copy(t = s"LINE-${x.t}"))
//        case "QUAD" => ???
//        case "CUBIC" => {
//          Geometry.radius(pts) match {
//            case Some((cntr, edge, r)) =>
//              val dcntr = pointToLine(cntr, fromLineStart, fromLineEnd)
//              val dedge = pointToLine(edge, fromLineStart, fromLineEnd)
//
//              //              if (dedge.distance < dcntr.distance) {
//              //                Geometry.clampFront(r, cntr, edge, dcntr).map(x => x.copy(t = s"CUBIC-${x.t}")) :+ Distance(fromLineStart, fromLineEnd)
//              //              } else {
//              //                Geometry.clampFront(r, cntr, edge, dcntr).map(x => x.copy(t = s"BUGGY")) :+ Distance(fromLineStart, fromLineEnd)
//              //              }
//              Seq()
//            //              Seq(dcntr).map(x => x.copy(t = s"CUBIC-${x.t}"))
//
//            case None => Seq()
//          }
//          //          val middlePoint = point(pts.head, pts(1), pts(2), pts(3)) //
//          //          val cntr = center(pts.head, middlePoint, pts(3))
//
//          //          if (cntr.isDefined) {
//          //            val d1: Double = calculateAngle(cntr.get, middlePoint, pts(3))
//          ////            val edge = Geometry.rotate2(middlePoint, cntr.get, d1.doubleValue())
//          //
//          //            clampFront()
//          //            Seq(pointToLine(cntr.get, fromLineStart, fromLineEnd).copy(supp = Seq(cntr.get)))
//          //          } else {
//          //            //            drawPoint(pts.head, clr = Color.CYAN)
//          //            Seq()
//          //          }
//        }
//      }
//    }
//
//    if (distances.isEmpty) {
//      Seq()
//    } else {
//      Seq(distances.minBy(_.distance))
//    }
//
//    distances
//  }
//
//  def pointTo(startPoint: GPoint, g2: Graphic): Seq[Distance] = {
//
//    g2 match {
//      case Curve(aperture, s, pol, i, multiquadrant, clockwise, from, to, rel) => {
//        //        if(!multiquadrant){
//        val arccenter = from + rel.get
//
//        Seq(Distance(startPoint, arccenter))
//
//        //        }
//
//        //        ???
//      }
//      case x => {
//        val iter2 = g2.shape.getPathIterator(null)
//
//        path(iter2){ (t, pts) =>
//          t match {
//            case "LINE" =>
//              //          drawBoundingBox(pts(0), pts(1), clr = Color.BLUE)
//
//              Seq(
//                pointToLine(startPoint, pts.head, pts(1))
//              ).map(x => x.copy(t = s"LINE-${x.t}"))
//            case "QUAD" => ???
//            case "CUBIC" => {
//              this.inner += 1
//              Geometry.radius(pts) match {
//                case Some((cntr, edge, r)) =>
//                  //              drawBoundingBox(cntr, edge, clr = Color.BLUE)
//
//                  val d = Distance(startPoint, cntr, Seq(cntr, edge))
//                  clampBack(r, cntr, edge, d).map(x => x.copy(t = s"CUBIC-${x.t}"))
//                //                          Seq(d).map(x => x.copy(t = s"CUBIC-${x.t}"))
//                case None => {
//                  println(s"point to cubic ${g2}")
//
//                  Seq()
//                }
//
//              }
//              //          val middlePoint = point(pts.head, pts(1), pts(2), pts(3))
//              //          val cntr = center(pts.head, middlePoint, pts(3))
//              //
//              //          if (cntr.isDefined) {
//              //
//              //            clampToCircle(radius, d)
//              //            Seq(Distance(startPoint, cntr.get))
//              //          } else {
//              //            //            drawPoint(startPoint)
//              //
//              //            Seq()
//              //          }
//            }
//          }
//        }
//      }
//    }
//
//
//
//
//    //    if (distances.isEmpty) {
//    //      Seq()
//    //    } else {
//    //      Seq(distances.minBy(_.distance))
//    //    }
//  }
//
//
//  def path(paths: PathIterator)(handle: (String, Seq[GPoint]) => Seq[Distance]) = {
//    val check = new Array[Double](6)
//
//    var curPoint: Option[GPoint] = None
//    var startPoint: Option[GPoint] = None
//    var wasCircle = false
//    val distanceCollector = Seq.newBuilder[Distance]
//    while (!paths.isDone) {
//
//      paths.currentSegment(check) match {
//        case SEG_MOVETO =>
//          val newPoint = GPoint(check(0), check(1))
//          curPoint = Some(newPoint)
//          startPoint = Some(newPoint)
//          wasCircle = false
//
//        case SEG_LINETO =>
//          val newPoint = GPoint(check(0), check(1))
//          handle("LINE", Seq(curPoint.get, newPoint)).foreach(distanceCollector.+=)
//          curPoint = Some(newPoint)
//          wasCircle = false
//
//        case SEG_QUADTO =>
//          val newPoint = GPoint(check(2), check(3))
//          val param = GPoint(check(0), check(1))
//          handle("QUAD", Seq(curPoint.get, param, newPoint)).foreach(distanceCollector.+=)
//
//          curPoint = Some(newPoint)
//          wasCircle = true
//        case SEG_CUBICTO =>
//          val newPoint = GPoint(check(4), check(5))
//
//          val param1 = GPoint(check(0), check(1))
//          val param2 = GPoint(check(2), check(3))
//          handle("CUBIC", Seq(curPoint.get, param1, param2, newPoint)).foreach(distanceCollector.+=)
//
//          curPoint = Some(newPoint)
//          wasCircle = true
//        case SEG_CLOSE =>
//          if (curPoint != startPoint) {
//            handle("LINE", Seq(curPoint.get, startPoint.get)).foreach(distanceCollector.+=)
//          }
//
//          curPoint = None
//      }
//
//      paths.next()
//    }
//
//    distanceCollector.result()
//  }
//
//  def radius(g: Graphic) = {
//    g match {
//      case x: Curve =>
//      case x =>
//    }
//  }
//
//
//  def distance(): Seq[Distance] = {
//    //    val oldc = graphics.getColor
//    //    val c = new Color(r.nextInt(255), r.nextInt(255), r.nextInt(255))
//    //    graphics.setColor(c)
//
//    val distances: Seq[Distance] = (g1 match {
//      case x: Curve => {
//        println("distance from curve")
//        Seq()
//      }
//
//      case x => {
//        val paths = g1.shape.getPathIterator(null)
//        path(paths){ (t, pts) =>
//          val r = t match {
//            case "LINE" => {
//              //          drawBoundingBox(pts(0), pts(1), clr = Color.ORANGE)
//              lineTo(pts(0), pts(1), g2).map(x => x.copy(t = s"LINE-${x.t}"))
//              //          Seq()
//            }
//            case "QUAD" => ???
//            case "CUBIC" => {
//              Geometry.radius(pts) match {
//                case Some((cntr, edge, r)) =>
//                  //              drawBoundingBox(cntr, edge, clr = Color.ORANGE)
//
//                  this.outer += 1
//
//
//                  val toEdge = pointTo(edge, g2).map(d => d.copy(supp = d.supp ++ Seq(cntr, edge))).minBy(_.distance)
//                  val toCenters = pointTo(cntr, g2).map(d => d.copy(supp = d.supp ++ Seq(cntr, edge))).minBy(_.distance)
//                  //              val toCenter = toCenters.minBy(_.distance)
//
//                  //              if (toEdge.distance < toCenter.distance) {
//                  //The other point is on the convex side of the curve, close on the inside
//
//                  //                  clampFront(r, cntr, edge, toCenters).map(x => x.copy(t = s"CUBIC-${x.t}"))
//                  Seq()
//                //              } else {
//                //                //the other point is on the concave side
//                //                clampFront(r, cntr, edge, toCenter).map(x => x.copy(t = s"BUGGY"))
//                //                //                Seq()
//                //              }
//
//                //              toCenters.map(x => x.copy(t = s"CUBIC-${x.t}"))
//
//                //              pointTo(cntr, g2)
//                //                .flatMap(d => {
//                //                  //                  d
//                //                  //                  if (r > Math.pow(10, -precision)) {
//                //
//                //                                    clampFront(r, cntr, edge, d).map(x => x.copy(t = s"CUBIC-${x.t}"))
//                ////                  Seq(d.copy(t = s"CUBIC-${d.t}"))
//                //                  //                  } else {
//                //                  //                    Seq()
//                //                  //                  }
//                //                })
//                case None => {
//                  println(s"distance from $g1 to... $g2")
//
//                  Seq()
//                }
//              }
//            }
//          }
//          r
//        }
//      }
//    })
//
//
//
//    //    graphics.setColor(oldc)
//
//    distances match {
//      case x if x.isEmpty => Seq()
//      case x =>
//        Seq(distances.minBy(_.distance))
//
//    }
//
//    distances
//  }
//
//  //  /**
//  //    * Remove the radius from the given distance (back end)
//  //    *
//  //    * @param radius
//  //    * @param centerpoint
//  //    * @param edgePoint
//  //    * @param d
//  //    * @return
//  //    */
//  private def clampBack(radius: BigDecimal, centerpoint: GPoint, edgePoint: GPoint, d: Distance) = {
//
//    val distVec = new Vector2D(
//      new Point2D(d.start.getX, d.start.getY),
//      new Point2D(d.end.getX, d.end.getY)
//    )
//
//    val angle = distVec.angle() - Math.PI
//    val d1 = new Point2D(centerpoint.getX, centerpoint.getY)
//    val newEnd = Vector2D.createPolar(radius.doubleValue, angle).plus(new Vector2D(d1))
//
//    //
//    //    Distance(d.start, d.end * (radius))
//    //
//    //    val angle1 = Geometry.calculateRadians2(d.end, edgePoint, centerpoint.getX <= edgePoint.x)
//    //    val angle2 = Geometry.calculateRadians2(d.end, d.start, centerpoint.getX <= d.end.x)
//    //
//    //    val d1 = angle2 - angle1
//    //
//    Seq(d)
//  }
//
//
//  private def calculateAngle(start: GPoint, edgePoint: GPoint, secPoint: GPoint)
//
//  = {
//    val angle1 = Geometry.calculateRadians2(start, edgePoint, start.getX >= edgePoint.x)
//    val angle2 = Geometry.calculateRadians2(start, secPoint, start.getX >= secPoint.x)
//    val d1 = angle2 - angle1
//    d1
//  }
//
//  //  private def drawPoint(pt: BigPoint, h: Double = 0.001, clr: Color = Color.WHITE) = {
//  //    val c = graphics.getColor()
//  //    graphics.setColor(clr)
//  //    graphics.fill(new Ellipse2D.Double(pt.getX - (h / 2), pt.getY - (h / 2), h, h))
//  //    graphics.setColor(c)
//  //  }
//  //
//  //  private def drawBoundingBox(min: BigPoint, max: BigPoint, h: Double = 0.001, clr: Color = Color.WHITE) = {
//  //    val c = graphics.getColor()
//  //    graphics.setColor(clr)
//  //    val h = max - min
//  //    val rec = new Line2D.Double(min.getX, min.getY, max.getX, max.getY)
//  //    graphics.draw(rec)
//  //    graphics.setColor(c)
//  //  }
//}
//
//object DistanceOp {
//  val r = new Random()
//}
