package de.fellows.ems.pcb.model.graphics.tree

import de.fellows.ems.pcb.model.graphics.GPoint

import java.awt.geom.{Area, Rectangle2D}
import scala.collection.mutable

/** n-dimensional QuadTree data structure; partitions spatial data for faster queries (e.g. KNN query)cala
  *
  * Many additional methods were added to the class both for efficient KNN queries and generalizing to n-dim.
  *
  * @param minVec
  *   vector of the corner of the bounding box with smallest coordinates
  * @param maxVec
  *   vector of the corner of the bounding box with smallest coordinates
  * @param maxPerBox
  *   threshold for number of points in each box before slitting a box
  */
class AreaQuadTree(
    min: GPoint,
    max: GPoint,
    maxPerBox: BigDecimal
) {

  println(s"create quadtree $min x $max")

  def popCollide(check: Area): Vector[Area] =
    collide(check, pop = true)

  def collide(check: Area): Vector[Area] =
    collide(check, pop = false)

  private def collide(check: Area, pop: Boolean): Vector[Area] = {
    val bounds = check.getBounds2D

    def collides(check: Area, element: Area): Boolean =
      if (element.intersects(bounds)) {
        val c = new Area(check)
        c.intersect(element)
        !c.isEmpty
      } else {
        false
      }

    def collideRec(check: Area, n: AreaNode): Vector[Area] = {
      val s: mutable.Builder[Area, Vector[Area]] = Vector.newBuilder[Area]

      val collisionsHere = n.elements.partition(ne => collides(check, ne))
      s ++= collisionsHere._1
      if (pop) {
        n.elements.clear()
        n.elements ++= collisionsHere._2
      }

      val ch = n.whichChild(bounds)

      s ++=
        (ch match {
          case Some(child) if child != n => collideRec(check, child)
          case Some(child) if child == n =>
            child.children match {
              case None    => Vector[Area]()
              case Some(c) => c.list().flatMap(p => collideRec(check, p))
            }

          case _ => Vector[Area]()
        })

      //      if (ch.get != n) {
      //        s ++= collideRec(check, ch.get)
      //      } else {
      //        s ++= (ch.get.children match {
      //          case None => Vector[Area]()
      //          case Some(c) => c.list().flatMap(p => collideRec(check, p))
      //
      //          //          case Some(_) => Vector[Area]()
      //        })
      //      }

      s.result()
    }

    collideRec(check, root)
  }

  val root = new AreaNode((min + max) / 2, max.abs() + min.abs(), None)

  def printTree() = {

    def elementString(area: Area): String = {
      val bounds = area.getBounds2D
      s"Area(${bounds.getX}, ${bounds.getY}, ${bounds.getX + bounds.getWidth}, ${bounds.getY + bounds.getHeight})"
    }

    def printTreeRecur(node: AreaNode, indent: Int) {
      println(s"${" " * indent}${node.toString}: ${node.elements.size} -> ${node.elements.map(elementString)}")

      node.children match {
        case None =>

        case Some(c) =>
          for (node <- c.list())
            printTreeRecur(node, indent + 1)
      }
    }

    printTreeRecur(root, 0)
  }

  def insert(query: Area) = {

    val bounds = query.getBounds2D

    def splitAndReorder(node: AreaNode): Unit = {
      node.makeChildren()
      val elements = new mutable.ListBuffer[Area]
      elements.appendAll(node.elements)

      // reinsert elements of this node
      node.elements.clear()
      for (element <- elements)
        insertWithCheck(element, node, element.getBounds2D)
      //        insertRecursively(element, node.whichChild(bounds), Some(node))
    }

    def insertWithCheck(query: Area, node: AreaNode, bounds: Rectangle2D) =
      node.whichChild(bounds) match {
        case Some(n) => insertRecursively(query, n, Some(node))
        case None => // throw new IllegalStateException("Trying to add an area to a Node that completely not intersecting")
      }

    def insertRecursively(query: Area, node: AreaNode, prev: Option[AreaNode] = None): Unit =
      if (prev.isDefined && prev.get == node) {
        node.elements.append(query)
      } else {
        node.children match {
          case None =>
            if (node.elements.size >= maxPerBox) {
              splitAndReorder(node)
              insertWithCheck(query, node, bounds)
            } else {
              node.elements.append(query)
            }

          case Some(_) =>
            insertWithCheck(query, node, bounds)
        }
      }

    //    val bounds = query.getBounds2D
    //    if (bounds.getMinX < this.min.getX || bounds.getMinY < this.min.getY || bounds.getMaxX > this.max.getX || bounds.getMaxY > this.max.getY) {
    //      throw new IndexOutOfBoundsException(s"bounds: $bounds")
    //    }
    insertRecursively(query, root)
  }

  def walk(walker: QuadTreeWalker) =
    root.walk(walker)

  def head(): Option[Area] = {
    def headRec(n: AreaNode): Option[Area] =
      if (n.elements.nonEmpty) {
        Some(n.elements.remove(0))
      } else {

        n.children match {
          case Some(ns) =>
            headRec(ns.topleft).orElse(headRec(ns.topright)).orElse(headRec(ns.bottomleft)).orElse(headRec(
              ns.bottomright
            ))
          case None => None
        }
      }

    headRec(root)
  }
}

case class AreaNodeContainer(topleft: AreaNode, topright: AreaNode, bottomleft: AreaNode, bottomright: AreaNode) {
  def list(): Vector[AreaNode] =
    Vector(topleft, topright, bottomleft, bottomright)
}

class AreaNode(
    center: GPoint,
    width: GPoint,
    var children: Option[AreaNodeContainer]
) {

  val elements: mutable.ListBuffer[Area] = new mutable.ListBuffer[Area]()

  lazy val nodeArea: Area = {
    val zero = center - (width / 2)
    new Area(new Rectangle2D.Double(zero.getX, zero.getY, width.getX, width.getY))
  }

  lazy val nodeBounds: Rectangle2D = {
    val zero = center - (width / 2)
    new Rectangle2D.Double(zero.getX, zero.getY, width.getX, width.getY)
  }

  /** Tests whether the queryPoint is in the node, or a child of that node
    *
    * @param queryPoint
    *   a point to test
    * @return
    *   whether the given point is in the node, or a child of this node
    */
  def contains(queryPoint: Area): Boolean = {
    val n = new Area(nodeArea)
    n.intersect(queryPoint)
    !n.isEmpty
  }

  def containsFast(queryPoint: Rectangle2D): Boolean =
    nodeBounds.intersects(queryPoint)

  /** retrieves the direct node containing the query, or this node if more than one contain the query
    *
    * @param query
    * @return
    */
  def whichChild(query: Rectangle2D): Option[AreaNode] =
    children match {
      case None => Some(this)
      case Some(c) =>
        val nodesContaining = c.list().filter(_.containsFast(query))
        if (nodesContaining.size == 1) {
          Some(nodesContaining.head)
        } else if (nodesContaining.isEmpty) {
          None
        } else {
          Some(this)
        }
    }

  /** Makes children nodes by partitioning the box into equal sub-boxes and adding a node for each sub-box
    */
  def makeChildren() {
    val mappedWidth = width * 0.5
    val centerDelta = mappedWidth * 0.5

    val tlc = center - centerDelta
    val trc = center + centerDelta
    val brc = GPoint(center.x + centerDelta.getBX, center.y - centerDelta.getBY)
    val blc = GPoint(center.x - centerDelta.getBX, center.y + centerDelta.getBY)

    children = Some(
      AreaNodeContainer(
        topleft = new AreaNode(tlc, mappedWidth, None),
        topright = new AreaNode(trc, mappedWidth, None),
        bottomleft = new AreaNode(blc, mappedWidth, None),
        bottomright = new AreaNode(brc, mappedWidth, None)
      )
    )
  }

  override def toString: String = {
    val l =
      if (children.isDefined) {
        "NODE"
      } else {
        "LEAF"
      }
    s"$l: ${center.getX}, ${center.getY}; ${width.getX}, ${width.getY}"
  }

  def bounds(): Rectangle2D.Double = {
    val zero = center - (width / 2)
    new Rectangle2D.Double(zero.getX, zero.getY, width.getX, width.getY)
  }

  def walk(walker: QuadTreeWalker): Unit = {
    walker.accept(this)
    this.children match {
      case Some(c) => c.list().foreach(_.walk(walker))
      case None    =>
    }

  }
}

trait QuadTreeWalker {
  def accept(n: AreaNode)
}
