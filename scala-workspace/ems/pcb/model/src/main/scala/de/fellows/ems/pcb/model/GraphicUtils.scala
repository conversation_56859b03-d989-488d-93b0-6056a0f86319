package de.fellows.ems.pcb.model

import de.fellows.ems.pcb.model.graphics.GPoint
import de.fellows.ems.pcb.model.graphics.tree.Distance
import de.fellows.utils.FilePath
import de.fellows.utils.internal.FileReader
import de.fellows.utils.logging.StackrateLogging
import play.api.libs.json.JsSuccess

import java.awt.Shape
import java.awt.geom.{Path2D, Point2D, Rectangle2D}
import java.nio.file.Paths
import scala.util.Success

object GraphicUtils extends StackrateLogging {

  def flat(s: Shape): Path2D = {
    val flat     = s.getPathIterator(null, 5)
    val flatPath = new Path2D.Double()
    flatPath.append(flat, false)
    flatPath
  }

  def readFile(path: FilePath): Option[Graphic] =
    readFile(path.toPath)

  def readFile(path: String): Option[Graphic] =
    FileReader.json[Graphic](Paths.get(path)) match {
      case Success(JsSuccess(value, _)) => Some(value)
      case _                            => None
    }

  def to2DPoint(bigPoint: graphics.GPoint): Point2D =
    new Point2D.Double(bigPoint.x.doubleValue, bigPoint.y.doubleValue)

  def isBoundsNeighbor(margin: Double, one: Rectangle2D, two: Rectangle2D) = {
    val m = margin

    boundsIntersect(
      one.getMinX - m,
      one.getMinY - m,
      one.getWidth + (2 * m),
      one.getHeight + (2 * m),
      two.getMinX - m,
      two.getMinY - m,
      two.getWidth + (2 * m),
      two.getHeight + (2 * m)
    )
  }

  def distanceBetweenRectangles(rec1: Rectangle2D, rec2: Rectangle2D): Distance = {
    val p2s = getPointsOfRectangle(rec2)
    val set = getPointsOfRectangle(rec1).flatMap(p1 => p2s.map(p2 => (p1, p2)))

    val d = set.minBy(p => p._1.distance(p._2))

    Distance(d._1, d._2, None, "rec")

  }

  def getPointsOfRectangle(rec: Rectangle2D): Seq[GPoint] =
    Seq(
      GPoint(rec.getMinX, rec.getMinY),
      GPoint(rec.getMaxX, rec.getMinY),
      GPoint(rec.getMinX, rec.getMaxY),
      GPoint(rec.getMaxX, rec.getMaxY)
    )

  def boundsIntersect(
      r1x: Double,
      r1y: Double,
      r1w: Double,
      r1h: Double,
      r2x: Double,
      r2y: Double,
      r2w: Double,
      r2h: Double
  ) = {
    val r1empty = (r1w <= 0.0) || (r1h <= 0.0);
    val r2empty = (r2w <= 0.0) || (r2h <= 0.0);

    if (r1empty || r2empty) {
      false
    } else {
      r2x + r2w > r1x &&
      r2y + r2h > r1y &&
      r2x < r1x + r1w &&
      r2y < r1y + r1h
    }
  }

}
