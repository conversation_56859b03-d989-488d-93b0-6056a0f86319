package de.fellows.ems.pcb.model.codec

import com.datastax.driver.core.{ ProtocolVersion, TypeCodec, UDTValue, UserType }
import de.fellows.ems.pcb.model.{ Dimension, Format }

import java.nio.ByteBuffer

class FormatCodec(cdc: TypeCodec[UDTValue]) extends TypeCodec[Format](cdc.getCqlType, classOf[Format]) {

  //            typeService text,
  //            typeCategory text,
  //            typeType text,
  //            typeProd boolean,
  //            typeMime text,
  //            typeIndex decimal,
  def to(value: Format): UDTValue =
    if (value == null) null
    else
      cdc.getCqlType.asInstanceOf[UserType].newValue()
        .set("dimension", value.dimension.orNull, classOf[Dimension])
        .setString("unit", value.unit)
        .setDecimal("resolution", value.resolution.bigDecimal)
        .setDecimal("scaling", value.scaling.map(BigDecimal.apply(_).bigDecimal).orNull)
        .setDecimal("gerberscale", value.gerberscale.map(BigDecimal.apply(_).bigDecimal).orNull)

  def getBigDecimal(value: UDTValue, name: String) = {
    val v: java.math.BigDecimal = value.getDecimal(name)

    v match {
      case null => None
      case bd   => Some(BigDecimal(bd))
    }
  }

  def from(value: UDTValue): Format =
    //    FilePath(value.getString("root"), value.getString("base"), value.getString("name"))
    if (value == null) null
    else
      Format(
        dimension = Option(value.get("dimension", classOf[Dimension])),
        unit = value.getString("unit"),
        resolution = getBigDecimal(value, "resolution").get,
        scaling = getBigDecimal(value, "scaling").map(_.doubleValue),
        gerberscale = getBigDecimal(value, "gerberscale").map(_.doubleValue)
      )

  override def serialize(value: Format, protocolVersion: ProtocolVersion): ByteBuffer =
    cdc.serialize(to(value), protocolVersion)

  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): Format =
    from(cdc.deserialize(bytes, protocolVersion))

  override def parse(value: String): Format =
    if (value == null || value.isEmpty) null
    else from(cdc.parse(value))

  override def format(value: Format): String =
    if (value == null) null
    else cdc.format(to(value))
}
