package de.fellows.ems.pcb.model.graphics

import de.luminovo.odb.odbpp.model.features.symbols._
import play.api.libs.json.{Format, JsDefined, JsString, Json, OFormat, Reads, Writes}

import java.awt.geom.Rectangle2D

sealed trait ApertureDefinition {
  def bounds: Option[GPoint]
  def bounds(target: GPoint, scaling: Int): Option[Rectangle2D.Double]
  def width(scaling: Double): Option[Double]
  def diameter(scaling: Double): Option[Double]

  def id: String

  val attributes: Option[Map[String, Seq[String]]]
}
case class ODBSymbolDefinition(symbol: ODBSymbol) extends ApertureDefinition {
  override def bounds: Option[GPoint]                                               = None
  override def bounds(target: GPoint, scaling: Int = 1): Option[Rectangle2D.Double] = None
  def width(scaling: Double): Option[Double] =
    diameter(scaling)

  def diameter(scaling: Double): Option[Double] =
    (symbol match {
      case RoundSymbol(symbolId, d) => Some(d)
      case NullSymbol(id)           => None
      case _                        => bounds.map(_.magnitude)
    }).flatMap { d =>
      if (d < 0.001) { // cut off way too small features. They are very expensive and cant be seen anyway
        None
      } else {
        Some(d * scaling)
      }
    }

  def id = symbol.symbolId.toString

  val attributes: Option[Map[String, Seq[String]]] = None
}

case class GerberApertureDefinition(
    dCode: String,
    template: String,
    args: Seq[String],
    attributes: Option[Map[String, Seq[String]]]
) extends ApertureDefinition {
  def id: String                              = dCode
  def getDouble(i: Int): Double               = doubles(i).get
  def getDoubleOption(i: Int): Option[Double] = doubles.lift(i).flatten

  def diameter: Option[Double]                  = doubles.headOption.flatten
  def diameter(scaling: Double): Option[Double] = diameter.map(_ * scaling)

  private val doubles = args.map { s =>
    try
      Some(BigDecimal(s).doubleValue)
    catch {
      case e: Throwable => None
    }
  }

  override def bounds: Option[GPoint] =
    template match {
      case "C" | "P" =>
        val rad = doubles(0).get

        Some(GPoint(rad, rad))
      case "R" | "O" =>
        val x = doubles(0).get
        val y = doubles(1).get

        Some(GPoint(x, y))
      case x =>
        None
    }

  override def bounds(target: GPoint, scaling: Int = 1): Option[Rectangle2D.Double] =
    template match {
      case "C" | "P" =>
        val rad = doubles(0).get * scaling
        Some(new Rectangle2D.Double(target.x - rad, target.y - rad, rad * 2, rad * 2))
      case "R" | "O" =>
        val width  = doubles(0).get * scaling
        val height = doubles(1).get * scaling
        Some(new Rectangle2D.Double(target.x - width / 2, target.y - height / 2, width, height))

      case x =>
        None
    }

  override def width(scaling: Double): Option[Double] =
    template match {
      case "C" | "P" =>
        Some(doubles(0).get * scaling)
      case "R" | "O" =>
        val x = doubles(0).get * scaling
        val y = doubles(1).get * scaling
        Some(Math.sqrt(x * x + y * y))
      case _ => None
    }
}

object GerberApertureDefinition {
  implicit val format: OFormat[GerberApertureDefinition] = Json.format[GerberApertureDefinition]
}

object ODBSymbolDefinition {
  implicit val unscaledDouble: Format[UnscaledDouble] = Json.format[UnscaledDouble]

  implicit val roundFormat: Format[RoundSymbol]                           = Json.format[RoundSymbol]
  implicit val squareFormat: Format[SquareSymbol]                         = Json.format[SquareSymbol]
  implicit val rectangleFormat: Format[RectangleSymbol]                   = Json.format[RectangleSymbol]
  implicit val roundedRectangleFormat: Format[RoundedRectangleSymbol]     = Json.format[RoundedRectangleSymbol]
  implicit val chamferedRectangleFormat: Format[ChamferedRectangleSymbol] = Json.format[ChamferedRectangleSymbol]
  implicit val ovalFormat: Format[OvalSymbol]                             = Json.format[OvalSymbol]
  implicit val diamondFormat: Format[DiamondSymbol]                       = Json.format[DiamondSymbol]
  implicit val octagonFormat: Format[OctagonSymbol]                       = Json.format[OctagonSymbol]
  implicit val roundDonutFormat: Format[RoundDonutSymbol]                 = Json.format[RoundDonutSymbol]
  implicit val squareDonutFormat: Format[SquareDonutSymbol]               = Json.format[SquareDonutSymbol]
  implicit val squareRoundDonutFormat: Format[SquareRoundDonutSymbol]     = Json.format[SquareRoundDonutSymbol]
  implicit val roundedSquareDonutFormat: Format[RoundedSquareDonutSymbol] = Json.format[RoundedSquareDonutSymbol]
  implicit val rectangleDonutFormat: Format[RectangleDonutSymbol]         = Json.format[RectangleDonutSymbol]
  implicit val roundedRectangleDonutFormat: Format[RoundedRectangleDonutSymbol] =
    Json.format[RoundedRectangleDonutSymbol]
  implicit val ovalDonutFormat: Format[OvalDonutSymbol]                     = Json.format[OvalDonutSymbol]
  implicit val horizontalHexagonFormat: Format[HorizontalHexagonSymbol]     = Json.format[HorizontalHexagonSymbol]
  implicit val verticalHexagonFormat: Format[VerticalHexagonSymbol]         = Json.format[VerticalHexagonSymbol]
  implicit val butterflyFormat: Format[ButterflySymbol]                     = Json.format[ButterflySymbol]
  implicit val squareButterflyFormat: Format[SquareButterflySymbol]         = Json.format[SquareButterflySymbol]
  implicit val triangleFormat: Format[TriangleSymbol]                       = Json.format[TriangleSymbol]
  implicit val halfOvalFormat: Format[HalfOvalSymbol]                       = Json.format[HalfOvalSymbol]
  implicit val roundThermalRoundedFormat: Format[RoundThermalRoundedSymbol] = Json.format[RoundThermalRoundedSymbol]
  implicit val roundThermalSquaredFormat: Format[RoundThermalSquaredSymbol] = Json.format[RoundThermalSquaredSymbol]
  implicit val squareThermalFormat: Format[SquareThermalSymbol]             = Json.format[SquareThermalSymbol]
  implicit val openSquareThermalFormat: Format[OpenSquareThermalSymbol]     = Json.format[OpenSquareThermalSymbol]
  implicit val lineThermalFormat: Format[LineThermalSymbol]                 = Json.format[LineThermalSymbol]
  implicit val squareRoundThermalFormat: Format[SquareRoundThermalSymbol]   = Json.format[SquareRoundThermalSymbol]
  implicit val rectangularThermalFormat: Format[RectangularThermalSymbol]   = Json.format[RectangularThermalSymbol]
  implicit val openRectangularThermalFormat: Format[OpenRectangularThermalSymbol] =
    Json.format[OpenRectangularThermalSymbol]
  implicit val roundedSquareThermalFormat: Format[RoundedSquareThermalSymbol] = Json.format[RoundedSquareThermalSymbol]
  implicit val openRoundedSquareThermalFormat: Format[OpenRoundedSquareThermalSymbol] =
    Json.format[OpenRoundedSquareThermalSymbol]
  implicit val roundedRectangleThermalFormat: Format[RoundedRectangleThermalSymbol] =
    Json.format[RoundedRectangleThermalSymbol]
  implicit val ovalThermalFormat: Format[OvalThermalSymbol]             = Json.format[OvalThermalSymbol]
  implicit val oblongThermalFormat: Format[OblongThermalSymbol]         = Json.format[OblongThermalSymbol]
  implicit val homePlateFormat: Format[HomePlateSymbol]                 = Json.format[HomePlateSymbol]
  implicit val invertedHomePlateFormat: Format[InvertedHomePlateSymbol] = Json.format[InvertedHomePlateSymbol]
  implicit val flatHomePlateFormat: Format[FlatHomePlateSymbol]         = Json.format[FlatHomePlateSymbol]
  implicit val radiusedInvertedHomePlateFormat: Format[RadiusedInvertedHomePlateSymbol] =
    Json.format[RadiusedInvertedHomePlateSymbol]
  implicit val radiusedHomePlateFormat: Format[RadiusedHomePlateSymbol] = Json.format[RadiusedHomePlateSymbol]
  implicit val crossFormat: Format[CrossSymbol]                         = Json.format[CrossSymbol]
  implicit val dogboneFormat: Format[DogboneSymbol]                     = Json.format[DogboneSymbol]
  implicit val dPackFormat: Format[DPackSymbol]                         = Json.format[DPackSymbol]
  implicit val ellipseFormat: Format[EllipseSymbol]                     = Json.format[EllipseSymbol]
  implicit val moireFormat: Format[MoireSymbol]                         = Json.format[MoireSymbol]
  implicit val holeFormat: Format[HoleSymbol]                           = Json.format[HoleSymbol]
  implicit val nullFormat: Format[NullSymbol]                           = Json.format[NullSymbol]
  implicit val userFormat: Format[UserSymbol]                           = Json.format[UserSymbol]

  implicit val basicSymbolFormat: Format[BasicStandardSymbol] = Json.format[BasicStandardSymbol]
  implicit val odbSymbolFormat: Format[ODBSymbol]             = Json.format[ODBSymbol]

  implicit val format: OFormat[ODBSymbolDefinition] = Json.format[ODBSymbolDefinition]
}

object ApertureDefinition {
  implicit val reads: Reads[ApertureDefinition] = (
    Reads {
      v =>
        (v \ "apertureType") match {
          case JsDefined(value) =>
            value.as[String] match {
              case "odb" =>
                ODBSymbolDefinition.format.reads(v)
              case _ =>
                GerberApertureDefinition.format.reads(v)
            }
          case _ =>
            GerberApertureDefinition.format.reads(v)
        }
    }
  )

  implicit val writes: Writes[ApertureDefinition] = Writes {
    case s: ODBSymbolDefinition =>
      ODBSymbolDefinition.format.writes(s) + ("apertureType" -> JsString("odb"))
    case s: GerberApertureDefinition =>
      GerberApertureDefinition.format.writes(s) + ("apertureType" -> JsString("gerber"))
  }
}
