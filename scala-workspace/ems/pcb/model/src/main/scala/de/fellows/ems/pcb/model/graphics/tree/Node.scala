package de.fellows.ems.pcb.model.graphics.tree

import de.fellows.ems.pcb.model.graphics.{GPoint, Graphic, Intersectable}
import play.api.libs.json._

import java.awt.geom.{Ellipse2D, Line2D, Point2D, Rectangle2D}
import scala.collection.mutable

/** Represents a Node in the Quadtree. This has either exactly 4 Children of type Node or None A Node is always Square
  *
  * @param center
  *   the center point of the Node
  * @param width
  *   the width of the node
  * @param children
  *   The children of the Node or None.
  * @tparam T
  */
class Node[T <: Intersectable[T]](
    val center: GPoint,
    val width: GPoint,
    var children: Option[NodeContainer[T]],
    val depth: Option[Int]
) extends Serializable {

  var elements = new mutable.ListBuffer[T]()

  /** Tests whether the queryPoint is in the node, or a child of that node
    *
    * @param queryPoint
    *   a point to test
    * @return
    *   whether the given point is in the node, or a child of this node
    */
  def contains(queryPoint: T): Boolean =
    queryPoint.intersects(bounds)

  /** Approximates whether this node contains the query point. This is faster than #contains, but less exact. Generally
    * it just checks the intersection of the outline. Most importantly, Node#contains is true if, and only if
    * Node#containsFast is true
    *
    * @param queryPoint
    * @return
    */
  def containsFast(queryPoint: Rectangle2D): Boolean =
    bounds.intersects(queryPoint) || bounds.contains(queryPoint)

  def containsFast(queryPoint: Point2D): Boolean =
    bounds.contains(queryPoint)

  /** retrieves the direct node containing the query, or this node if more than one contain the query
    *
    * @param query
    * @return
    */
  def whichChild(query: Rectangle2D): Option[Node[T]] = {
    val r = whichChildrenQuery(query)
    if (r.size == 1) {
      Some(r.head)
    } else if (r.size > 1) {
      Some(this)
    } else {
      None
    }
  }

  def whichChildrenQuery(query: Rectangle2D): mutable.ArrayBuffer[Node[T]] = {
    val childrenResult = new mutable.ArrayBuffer[Node[T]](4)

    children match {
      case Some(c) =>
        val left   = query.getMinX
        val right  = query.getMaxX
        val top    = query.getMaxY
        val bottom = query.getMinY

        var topLeft     = false
        var topRight    = false
        var bottomLeft  = false
        var bottomRight = false

        // if these look weird, it's because top right and bottom right are inverted!
        if (top > center.y) {
          if (left < center.x) {
            bottomLeft = true
          }

          if (right > center.x) {
            topRight = true
          }
        }

        if (bottom < center.y) {
          if (left < center.x) {
            topLeft = true
          }

          if (right > center.x) {
            bottomRight = true
          }
        }

        // This is done to make sure that the order of the children is always the same.
        // This is relevant for the outline task, which breaks if the order of the children is not the same
        if (topLeft) childrenResult += c.topleft
        if (topRight) childrenResult += c.topright
        if (bottomLeft) childrenResult += c.bottomleft
        if (bottomRight) childrenResult += c.bottomright

      case None =>
    }

    childrenResult
  }

  /** retrieves the direct node containing the query, or this node if more than one contain the query
    *
    * @param query
    * @return
    */
  def whichChild(query: Point2D): Option[Node[T]] =
    whichChildImpl(_.containsFast(query))

  private def whichChildImpl(contains: Node[T] => Boolean): Option[Node[T]] =
    children match {
      case None => Some(this)
      case Some(c) =>
        var node  = this
        var count = 0
        c.foreach { n =>
          if (contains(n)) {
            if (node == this) {
              node = n
            }
            count += 1
          }
        }

        count match {
          case 0 => None
          case 1 => Some(node)
          case _ => Some(this)
        }
    }

  /** Makes children nodes by partitioning the box into equal sub-boxes and adding a node for each sub-box
    */
  def makeChildren(): Unit = {
    val mappedWidth = width * 0.5
    val centerDelta = mappedWidth * 0.5

    val tlc = center - centerDelta
    val trc = center + centerDelta
    val brc = GPoint(center.x + centerDelta.x, center.y - centerDelta.y)
    val blc = GPoint(center.x - centerDelta.x, center.y + centerDelta.y)

    val topleft     = new Node[T](tlc, mappedWidth, None, depth.map(_ + 1))
    val topright    = new Node[T](trc, mappedWidth, None, depth.map(_ + 1))
    val bottomleft  = new Node[T](blc, mappedWidth, None, depth.map(_ + 1))
    val bottomright = new Node[T](brc, mappedWidth, None, depth.map(_ + 1))

    children = Some(
      NodeContainer[T](
        topleft = topleft,
        topright = topright,
        bottomleft = bottomleft,
        bottomright = bottomright
      )
    )
  }

  /** Get the bounds of this Node. Always a Square
    *
    * @return
    */
  lazy val bounds: Rectangle2D.Double = {
    val zero = center - (width / 2)
    new Rectangle2D.Double(zero.x.doubleValue, zero.y.doubleValue, width.x.doubleValue, width.y.doubleValue)
  }

  override def toString: String = {
    val l =
      if (children.isDefined) {
        "NODE"
      } else {
        "LEAF"
      }
    s"$l: ${center.x.doubleValue}, ${center.y.doubleValue}; ${width.x.doubleValue}, ${width.y.doubleValue}"
  }

  def walk(walker: TreeWalker[T]): Unit = {
    walker.accept(this)

    this.children.foreach { n =>
      n.foreach(_.walk(walker))
    }
  }

  def copy(): Node[T] = {
    val copied: Node[T] = new Node[T](
      center,
      width,
      children.map(nc =>
        NodeContainer(
          nc.topleft.copy(),
          nc.topright.copy(),
          nc.bottomleft.copy(),
          nc.bottomright.copy()
        )
      ),
      depth
    )

    copied.elements = mutable.ListBuffer[T]()
    copied.elements.addAll(this.elements)

    copied
  }

}

object Node {

  def create[T <: Intersectable[T]](
      center: GPoint,
      width: GPoint,
      children: Option[NodeContainer[T]],
      elements: mutable.ListBuffer[T],
      depth: Option[Int]
  ): Node[T] = {
    val n = new Node[T](center, width, children, depth)
    n.elements = elements
    n
  }

  import play.api.libs.functional.syntax._

  implicit lazy val nr: Reads[Node[Graphic]] = (
    (JsPath \ "center").read[GPoint] and
      (JsPath \ "width").read[GPoint] and
      (JsPath \ "children").lazyReadNullable[NodeContainer[Graphic]](ncr) and
      (JsPath \ "depth").readNullable[Int] and
      (JsPath \ "elements").readNullable[mutable.ListBuffer[Graphic]]
  ) { (center, width, ch, depth, elems) =>
    Node.create[Graphic](center, width, ch, elems.getOrElse(mutable.ListBuffer()), depth)
  }

  implicit lazy val ncr: Reads[NodeContainer[Graphic]] = (
    (JsPath \ "topLeft").lazyRead[Node[Graphic]](nr) and
      (JsPath \ "topRight").lazyRead[Node[Graphic]](nr) and
      (JsPath \ "bottomLeft").lazyRead[Node[Graphic]](nr) and
      (JsPath \ "bottomRight").lazyRead[Node[Graphic]](nr)
  )((tl, tr, bl, br) => new NodeContainer(tl, tr, bl, br))

  implicit val qt: Writes[Node[Graphic]] = new Writes[Node[Graphic]] {
    override def writes(o: Node[Graphic]): JsValue =
      Json.obj(
        "center"   -> o.center,
        "width"    -> o.width,
        "elements" -> o.elements.toList
      ) ++
        o.children.map { ch =>
          Json.obj(
            "children" ->
              Json.obj(
                "topLeft"     -> Json.toJson(ch.topleft)(this),
                "topRight"    -> Json.toJson(ch.topright)(this),
                "bottomLeft"  -> Json.toJson(ch.bottomleft)(this),
                "bottomRight" -> Json.toJson(ch.bottomright)(this)
              )
          )
        }.getOrElse(Json.obj())
  }
}
