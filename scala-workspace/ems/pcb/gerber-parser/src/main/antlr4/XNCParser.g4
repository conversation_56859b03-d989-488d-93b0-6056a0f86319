parser grammar XNCParser ;

//@header {
//
//}
options { tokenVocab = XNCLexer ;}


xnc
    : header body M30 NEWLINE*;

header
    : comment* startHeader comment* setUnit toolTable  endHeader;


toolTable: (toolDeclaration | comment) *;

body
    : (drillSelection|routSelection|comment)*;

drillSelection
    : drillMode? (selectTool (drillHit *) ) + ;

routSelection
    : setRoutMode (selectTool toolDown rout* toolUp)*;

rout
    : (linearRout|cwRout|ccwRout);

//end of general grammar

comment: COMMENT COMMENTCONTENT? CLOSE;

startHeader: M48 NEWLINE;
setUnit: (INCH|METRIC)NEWLINE ;
toolDeclaration:
        T toolNumber (
            (C holeDiameter)
            |(S decimal)
            |(B decimal)
            |(F decimal)
        )+ NEWLINE;

endHeader: EOH NEWLINE;

drillMode: G05 NEWLINE;
setRoutMode: G00 X decimal Y decimal NEWLINE;

selectTool: T toolNumber NEWLINE;

drillHit: (X decimal)? (Y decimal)? NEWLINE;

toolDown: M15 NEWLINE;
toolUp: M16 NEWLINE;

linearRout: G01 X decimal Y decimal NEWLINE;

cwRout: G02 X decimal Y decimal A decimal;
ccwRout: G03 X decimal Y decimal A decimal;

toolNumber
	: 	DIGIT+ ;

holeDiameter
    :   decimal;


decimal:
    | integer DOT integer
    | integer ;

integer
	: 	DIGIT + ;