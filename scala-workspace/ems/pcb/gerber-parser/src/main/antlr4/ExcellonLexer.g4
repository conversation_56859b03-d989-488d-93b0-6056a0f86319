lexer grammar ExcellonLexer ;


PPERC: '%';
M48 : 'M48' -> pushMode(HEADERMODE);
//M95 : 'M48';
//

//mode MAINMODE;

M30 : 'M30';

//G05: 'G05';
//G01: 'G01';
//G02: 'G02';
//G03: 'G03';
//G00: 'G00';
//M15: 'M15';
//M16: 'M16';



EOH : '%';
BDOT : '.';
BMINUS : '-';


COMMENT: ';'  -> pushMode(COMMENTMODE) ;


NEWLINE: ('\r\n'|'\n'|'\r');

DIGIT
	: '0'..'9' ;
POSDIGIT
	: '1'..'9' ;

GC : 'G';
MC : 'M';
TC : 'T';
XC : 'X';
YC : 'Y';


mode COMMENTMODE;

COMMENTCONTENT: STRING ;

STRING : ~('\r' | '\n')+ ;

CLOSE
: ('\r\n'|'\n'|'\r') -> popMode ;



mode HEADERMODE;
//HEADER COMMANDS
AFS: 'AFS';
M72: 'M72';
M71: 'M71';
ATC: 'ATC';
BLKD: 'BLKD';
CCW: 'CCW';
CP: 'CP';
DETECT: 'DETECT';
DN: 'DN';
DTMDIST: 'DTMDIST';
EXDA: 'EXDA';
FMAT: 'FMAT';
FSB: 'FSB';
HPCK: 'HPCK';
ICI: 'ICI';
INCH: 'INCH';
METRIC: 'METRIC';
NCSL: 'NCSL';
OM48: 'OM48';
OSTOP: 'OSTOP';
OTCLMP: 'OTCLMP';
PCKPARAM: 'PCKPARAM';
PF: 'PF';
PPR: 'PPR';
PVS: 'PVS';
RC: 'R,C';
RCP: 'R,CP';
RCR: 'R,CR';
RD: 'R,D';
RH: 'R,H';
RT: 'R,T';
SBK: 'SBK';
SG: 'SG';
SIXM: 'SIXM';
TCST: 'TCST';
UP: 'UP';
VER: 'VER';
Z: 'Z';
ZA: 'ZA';
ZC: 'ZC';
ZS: 'ZS';
ZP: 'Z+';
ZM: 'Z-';


LZ : 'LZ';
TZ : 'TZ';
COMMA: ',';

ON: 'ON';
OFF: 'OFF';

MINUS : '-';



HEADERCOMMENT: ';'  -> pushMode(COMMENTMODE) ;
HNEWLINE: ('\r\n'|'\n'|'\r');

T : 'T';
C : 'C';
X : 'X';
Y : 'Y';
A : 'A';
S : 'S';
F : 'F';
B : 'B';
G : 'G';
M : 'M';

DOT : '.';


HDIGIT
	: '0'..'9' ;


M95: 'M95'-> popMode;
PERC: '%'-> popMode;

