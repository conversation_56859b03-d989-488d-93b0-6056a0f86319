parser grammar GerberParser ;

//@header {
//
//}
options { tokenVocab = GerberLexer ;
}

gerber
	: (statement | comment)* (D INT)? (M02 | M00);

statement
	: data EOB // Command
	| PARAM (ext_command)+ PARAM ; // or %-ext command

data
	: op
	| g=(G36 | G37)
	//| (D d=integ) //TODO Changed
	| deprecated ;

op
	: inter=(G01|G02|G03| G74 | G75 | G55)?
	(X x=number)?
	(Y y=number)?
	(I i=number)?
	(J j=number)?
	(D oper=integ)? ; //TODO Changed


deprecated
	:
    | g54
	| G55
	| G70
	| G71
	| G90
	| G91
	| M01
	| AS
	| IN
	| LN ;

g54:
    G54 D integ;

//Ext commands

ext_command
	: fs EOB
	| mo EOB
	| tf EOB
	| ad EOB
	| lp EOB
	| ta EOB
	| td EOB
	| to (EOB | CLOSE )
	| am
	| as
	| sr EOB
	| deprecated_command EOB;

deprecated_command
	:
	| ir
	| in
	| ip
	| of
	| mi
	| sf
	| in
	| ICAS
	| ln ;

sf
	: SF A number B number ;
in
	: IN string? ;

of
	: OF A number B number ;

mi
	: MI A number B number ;

ip
	: IP string ;

ir
	: IR number ;

//commands

comment
	: (G04) COMMENTCONTENT? CLOSE ;

ln
	: LN (~EOB)+ ;

fs
	: FS oz=(D|L|T)? cn=(A|I) (N2)? X x=number Y y=number ;

mo
	: MO s=(IN | MM) ;

tf
	: TF (attstring (COMMA attval)*) ;

ad
	: AD (D name=integ (template=identifier))?
	(
		COMMA number
		(X number)*
	)? ;

lp
	: LP polarity=(C|D) ;

ta
	: T A (string (COMMA attval)? ) ;

td
	: TD (string) ? ;
to
	: TO DOT attribute=attributeidentifier toattr toattr*;

toattr
    : COMMA attvalue=field;

am
	: AM name=string EOB macrocontent ;

as
	: AS a=string X b=string Y ;


ab
	: AB (D name=integ)? EOB ;

sr
    : SR X xrep=number Y yrep=number I idist=number J jdist=number
    | SR;


macrocontent
	: macrodirective* ;

macrodirective
    : ( (var=vardef | pr=prim | INT string) EOB ) ;

prim
	: primitive=number (COMMA primdef ) + ;

primdef
	: ((number|X|DOLLAR)+ | arith );


vardef
	: DOLLAR name=number EQ arith ;

varuse
	: DOLLAR name=number ;

arith
	: (~ (EOB|COMMA))+ ; // TODO: arithmetic

attval
	: attstring (COMMA attstring)* ;

string
	: (~ (EOB))+ ;
attstring
    : (~ (COMMA | EOB))+ ;

identifier
	: (~(INT|EOB|COMMA|DOLLAR)) (~(EOB | COMMA))* ;

attributeidentifier
	: (~(EOB|COMMA|DOLLAR)) field ;

field
	: (~(EOB|COMMA|DOLLAR|PARAM))* ;

number
	: MINUS? decimal_ ;

decimal_
	: DOT integ
	| integ DOT integ
	| integ DOT
	| integ ;

integ
	: 	INT ;

//Lexer

