// Generated from /home/<USER>/src/epibator/scala-workspace/ems/pcb/gerber-parser/src/main/antlr4/GerberParser.g4 by ANTLR 4.13.1
package de.fellows.ems.gerber.parser;
import org.antlr.v4.runtime.tree.ParseTreeListener;

/**
 * This interface defines a complete listener for a parse tree produced by
 * {@link GerberParser}.
 */
public interface GerberParserListener extends ParseTreeListener {
	/**
	 * Enter a parse tree produced by {@link GerberParser#gerber}.
	 * @param ctx the parse tree
	 */
	void enterGerber(GerberParser.GerberContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#gerber}.
	 * @param ctx the parse tree
	 */
	void exitGerber(GerberParser.GerberContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#statement}.
	 * @param ctx the parse tree
	 */
	void enterStatement(GerberParser.StatementContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#statement}.
	 * @param ctx the parse tree
	 */
	void exitStatement(GerberParser.StatementContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#data}.
	 * @param ctx the parse tree
	 */
	void enterData(GerberParser.DataContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#data}.
	 * @param ctx the parse tree
	 */
	void exitData(GerberParser.DataContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#op}.
	 * @param ctx the parse tree
	 */
	void enterOp(GerberParser.OpContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#op}.
	 * @param ctx the parse tree
	 */
	void exitOp(GerberParser.OpContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#deprecated}.
	 * @param ctx the parse tree
	 */
	void enterDeprecated(GerberParser.DeprecatedContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#deprecated}.
	 * @param ctx the parse tree
	 */
	void exitDeprecated(GerberParser.DeprecatedContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#g54}.
	 * @param ctx the parse tree
	 */
	void enterG54(GerberParser.G54Context ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#g54}.
	 * @param ctx the parse tree
	 */
	void exitG54(GerberParser.G54Context ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#ext_command}.
	 * @param ctx the parse tree
	 */
	void enterExt_command(GerberParser.Ext_commandContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#ext_command}.
	 * @param ctx the parse tree
	 */
	void exitExt_command(GerberParser.Ext_commandContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#deprecated_command}.
	 * @param ctx the parse tree
	 */
	void enterDeprecated_command(GerberParser.Deprecated_commandContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#deprecated_command}.
	 * @param ctx the parse tree
	 */
	void exitDeprecated_command(GerberParser.Deprecated_commandContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#sf}.
	 * @param ctx the parse tree
	 */
	void enterSf(GerberParser.SfContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#sf}.
	 * @param ctx the parse tree
	 */
	void exitSf(GerberParser.SfContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#in}.
	 * @param ctx the parse tree
	 */
	void enterIn(GerberParser.InContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#in}.
	 * @param ctx the parse tree
	 */
	void exitIn(GerberParser.InContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#of}.
	 * @param ctx the parse tree
	 */
	void enterOf(GerberParser.OfContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#of}.
	 * @param ctx the parse tree
	 */
	void exitOf(GerberParser.OfContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#mi}.
	 * @param ctx the parse tree
	 */
	void enterMi(GerberParser.MiContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#mi}.
	 * @param ctx the parse tree
	 */
	void exitMi(GerberParser.MiContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#ip}.
	 * @param ctx the parse tree
	 */
	void enterIp(GerberParser.IpContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#ip}.
	 * @param ctx the parse tree
	 */
	void exitIp(GerberParser.IpContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#ir}.
	 * @param ctx the parse tree
	 */
	void enterIr(GerberParser.IrContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#ir}.
	 * @param ctx the parse tree
	 */
	void exitIr(GerberParser.IrContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#comment}.
	 * @param ctx the parse tree
	 */
	void enterComment(GerberParser.CommentContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#comment}.
	 * @param ctx the parse tree
	 */
	void exitComment(GerberParser.CommentContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#ln}.
	 * @param ctx the parse tree
	 */
	void enterLn(GerberParser.LnContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#ln}.
	 * @param ctx the parse tree
	 */
	void exitLn(GerberParser.LnContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#fs}.
	 * @param ctx the parse tree
	 */
	void enterFs(GerberParser.FsContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#fs}.
	 * @param ctx the parse tree
	 */
	void exitFs(GerberParser.FsContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#mo}.
	 * @param ctx the parse tree
	 */
	void enterMo(GerberParser.MoContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#mo}.
	 * @param ctx the parse tree
	 */
	void exitMo(GerberParser.MoContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#tf}.
	 * @param ctx the parse tree
	 */
	void enterTf(GerberParser.TfContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#tf}.
	 * @param ctx the parse tree
	 */
	void exitTf(GerberParser.TfContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#ad}.
	 * @param ctx the parse tree
	 */
	void enterAd(GerberParser.AdContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#ad}.
	 * @param ctx the parse tree
	 */
	void exitAd(GerberParser.AdContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#lp}.
	 * @param ctx the parse tree
	 */
	void enterLp(GerberParser.LpContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#lp}.
	 * @param ctx the parse tree
	 */
	void exitLp(GerberParser.LpContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#ta}.
	 * @param ctx the parse tree
	 */
	void enterTa(GerberParser.TaContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#ta}.
	 * @param ctx the parse tree
	 */
	void exitTa(GerberParser.TaContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#td}.
	 * @param ctx the parse tree
	 */
	void enterTd(GerberParser.TdContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#td}.
	 * @param ctx the parse tree
	 */
	void exitTd(GerberParser.TdContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#to}.
	 * @param ctx the parse tree
	 */
	void enterTo(GerberParser.ToContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#to}.
	 * @param ctx the parse tree
	 */
	void exitTo(GerberParser.ToContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#toattr}.
	 * @param ctx the parse tree
	 */
	void enterToattr(GerberParser.ToattrContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#toattr}.
	 * @param ctx the parse tree
	 */
	void exitToattr(GerberParser.ToattrContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#am}.
	 * @param ctx the parse tree
	 */
	void enterAm(GerberParser.AmContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#am}.
	 * @param ctx the parse tree
	 */
	void exitAm(GerberParser.AmContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#as}.
	 * @param ctx the parse tree
	 */
	void enterAs(GerberParser.AsContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#as}.
	 * @param ctx the parse tree
	 */
	void exitAs(GerberParser.AsContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#ab}.
	 * @param ctx the parse tree
	 */
	void enterAb(GerberParser.AbContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#ab}.
	 * @param ctx the parse tree
	 */
	void exitAb(GerberParser.AbContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#sr}.
	 * @param ctx the parse tree
	 */
	void enterSr(GerberParser.SrContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#sr}.
	 * @param ctx the parse tree
	 */
	void exitSr(GerberParser.SrContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#macrocontent}.
	 * @param ctx the parse tree
	 */
	void enterMacrocontent(GerberParser.MacrocontentContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#macrocontent}.
	 * @param ctx the parse tree
	 */
	void exitMacrocontent(GerberParser.MacrocontentContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#macrodirective}.
	 * @param ctx the parse tree
	 */
	void enterMacrodirective(GerberParser.MacrodirectiveContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#macrodirective}.
	 * @param ctx the parse tree
	 */
	void exitMacrodirective(GerberParser.MacrodirectiveContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#prim}.
	 * @param ctx the parse tree
	 */
	void enterPrim(GerberParser.PrimContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#prim}.
	 * @param ctx the parse tree
	 */
	void exitPrim(GerberParser.PrimContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#primdef}.
	 * @param ctx the parse tree
	 */
	void enterPrimdef(GerberParser.PrimdefContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#primdef}.
	 * @param ctx the parse tree
	 */
	void exitPrimdef(GerberParser.PrimdefContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#vardef}.
	 * @param ctx the parse tree
	 */
	void enterVardef(GerberParser.VardefContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#vardef}.
	 * @param ctx the parse tree
	 */
	void exitVardef(GerberParser.VardefContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#varuse}.
	 * @param ctx the parse tree
	 */
	void enterVaruse(GerberParser.VaruseContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#varuse}.
	 * @param ctx the parse tree
	 */
	void exitVaruse(GerberParser.VaruseContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#arith}.
	 * @param ctx the parse tree
	 */
	void enterArith(GerberParser.ArithContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#arith}.
	 * @param ctx the parse tree
	 */
	void exitArith(GerberParser.ArithContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#attval}.
	 * @param ctx the parse tree
	 */
	void enterAttval(GerberParser.AttvalContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#attval}.
	 * @param ctx the parse tree
	 */
	void exitAttval(GerberParser.AttvalContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#string}.
	 * @param ctx the parse tree
	 */
	void enterString(GerberParser.StringContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#string}.
	 * @param ctx the parse tree
	 */
	void exitString(GerberParser.StringContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#attstring}.
	 * @param ctx the parse tree
	 */
	void enterAttstring(GerberParser.AttstringContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#attstring}.
	 * @param ctx the parse tree
	 */
	void exitAttstring(GerberParser.AttstringContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#identifier}.
	 * @param ctx the parse tree
	 */
	void enterIdentifier(GerberParser.IdentifierContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#identifier}.
	 * @param ctx the parse tree
	 */
	void exitIdentifier(GerberParser.IdentifierContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#attributeidentifier}.
	 * @param ctx the parse tree
	 */
	void enterAttributeidentifier(GerberParser.AttributeidentifierContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#attributeidentifier}.
	 * @param ctx the parse tree
	 */
	void exitAttributeidentifier(GerberParser.AttributeidentifierContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#field}.
	 * @param ctx the parse tree
	 */
	void enterField(GerberParser.FieldContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#field}.
	 * @param ctx the parse tree
	 */
	void exitField(GerberParser.FieldContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#number}.
	 * @param ctx the parse tree
	 */
	void enterNumber(GerberParser.NumberContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#number}.
	 * @param ctx the parse tree
	 */
	void exitNumber(GerberParser.NumberContext ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#decimal_}.
	 * @param ctx the parse tree
	 */
	void enterDecimal_(GerberParser.Decimal_Context ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#decimal_}.
	 * @param ctx the parse tree
	 */
	void exitDecimal_(GerberParser.Decimal_Context ctx);
	/**
	 * Enter a parse tree produced by {@link GerberParser#integ}.
	 * @param ctx the parse tree
	 */
	void enterInteg(GerberParser.IntegContext ctx);
	/**
	 * Exit a parse tree produced by {@link GerberParser#integ}.
	 * @param ctx the parse tree
	 */
	void exitInteg(GerberParser.IntegContext ctx);
}