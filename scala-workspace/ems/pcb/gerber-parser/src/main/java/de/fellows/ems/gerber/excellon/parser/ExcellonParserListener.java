// Generated from /home/<USER>/src/epibator/scala-workspace/ems/pcb/gerber-parser/src/main/antlr4/ExcellonParser.g4 by ANTLR 4.13.1
package de.fellows.ems.gerber.excellon.parser;
import org.antlr.v4.runtime.tree.ParseTreeListener;

/**
 * This interface defines a complete listener for a parse tree produced by
 * {@link ExcellonParser}.
 */
public interface ExcellonParserListener extends ParseTreeListener {
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#excellon}.
	 * @param ctx the parse tree
	 */
	void enterExcellon(ExcellonParser.ExcellonContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#excellon}.
	 * @param ctx the parse tree
	 */
	void exitExcellon(ExcellonParser.ExcellonContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#comment}.
	 * @param ctx the parse tree
	 */
	void enterComment(ExcellonParser.CommentContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#comment}.
	 * @param ctx the parse tree
	 */
	void exitComment(ExcellonParser.CommentContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#header}.
	 * @param ctx the parse tree
	 */
	void enterHeader(ExcellonParser.HeaderContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#header}.
	 * @param ctx the parse tree
	 */
	void exitHeader(ExcellonParser.HeaderContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#headerStart}.
	 * @param ctx the parse tree
	 */
	void enterHeaderStart(ExcellonParser.HeaderStartContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#headerStart}.
	 * @param ctx the parse tree
	 */
	void exitHeaderStart(ExcellonParser.HeaderStartContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#headerEnd}.
	 * @param ctx the parse tree
	 */
	void enterHeaderEnd(ExcellonParser.HeaderEndContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#headerEnd}.
	 * @param ctx the parse tree
	 */
	void exitHeaderEnd(ExcellonParser.HeaderEndContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#headerPart}.
	 * @param ctx the parse tree
	 */
	void enterHeaderPart(ExcellonParser.HeaderPartContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#headerPart}.
	 * @param ctx the parse tree
	 */
	void exitHeaderPart(ExcellonParser.HeaderPartContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#headerCommand}.
	 * @param ctx the parse tree
	 */
	void enterHeaderCommand(ExcellonParser.HeaderCommandContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#headerCommand}.
	 * @param ctx the parse tree
	 */
	void exitHeaderCommand(ExcellonParser.HeaderCommandContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#toolDeclaration}.
	 * @param ctx the parse tree
	 */
	void enterToolDeclaration(ExcellonParser.ToolDeclarationContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#toolDeclaration}.
	 * @param ctx the parse tree
	 */
	void exitToolDeclaration(ExcellonParser.ToolDeclarationContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#diameter}.
	 * @param ctx the parse tree
	 */
	void enterDiameter(ExcellonParser.DiameterContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#diameter}.
	 * @param ctx the parse tree
	 */
	void exitDiameter(ExcellonParser.DiameterContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#body}.
	 * @param ctx the parse tree
	 */
	void enterBody(ExcellonParser.BodyContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#body}.
	 * @param ctx the parse tree
	 */
	void exitBody(ExcellonParser.BodyContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#command}.
	 * @param ctx the parse tree
	 */
	void enterCommand(ExcellonParser.CommandContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#command}.
	 * @param ctx the parse tree
	 */
	void exitCommand(ExcellonParser.CommandContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#drillHit}.
	 * @param ctx the parse tree
	 */
	void enterDrillHit(ExcellonParser.DrillHitContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#drillHit}.
	 * @param ctx the parse tree
	 */
	void exitDrillHit(ExcellonParser.DrillHitContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#params}.
	 * @param ctx the parse tree
	 */
	void enterParams(ExcellonParser.ParamsContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#params}.
	 * @param ctx the parse tree
	 */
	void exitParams(ExcellonParser.ParamsContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#selectTool}.
	 * @param ctx the parse tree
	 */
	void enterSelectTool(ExcellonParser.SelectToolContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#selectTool}.
	 * @param ctx the parse tree
	 */
	void exitSelectTool(ExcellonParser.SelectToolContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#gCode}.
	 * @param ctx the parse tree
	 */
	void enterGCode(ExcellonParser.GCodeContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#gCode}.
	 * @param ctx the parse tree
	 */
	void exitGCode(ExcellonParser.GCodeContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#mCode}.
	 * @param ctx the parse tree
	 */
	void enterMCode(ExcellonParser.MCodeContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#mCode}.
	 * @param ctx the parse tree
	 */
	void exitMCode(ExcellonParser.MCodeContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#decimal}.
	 * @param ctx the parse tree
	 */
	void enterDecimal(ExcellonParser.DecimalContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#decimal}.
	 * @param ctx the parse tree
	 */
	void exitDecimal(ExcellonParser.DecimalContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#measuring}.
	 * @param ctx the parse tree
	 */
	void enterMeasuring(ExcellonParser.MeasuringContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#measuring}.
	 * @param ctx the parse tree
	 */
	void exitMeasuring(ExcellonParser.MeasuringContext ctx);
	/**
	 * Enter a parse tree produced by {@link ExcellonParser#integer}.
	 * @param ctx the parse tree
	 */
	void enterInteger(ExcellonParser.IntegerContext ctx);
	/**
	 * Exit a parse tree produced by {@link ExcellonParser#integer}.
	 * @param ctx the parse tree
	 */
	void exitInteger(ExcellonParser.IntegerContext ctx);
}