package de.fellows.ems.analysis.api

import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.ems.analysis.api
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate Analysis API"
  )
)
abstract class AnalysisService extends Service with StackrateServiceAPI {
  private val subPath = "ems/analysis"
  val basePath        = "/api/" + subPath

  @StackrateApi
  def requestAnalysis(): ServiceCall[AnalysisRequest, Analysis]

  @StackrateApi
  def setAnalysisStatus(id: String): ServiceCall[StateUpdate, api.Analysis]

  override def descriptor: Descriptor = {
    import Service._
    withDocumentation(subPath)(
      named("ems-analysis")
        .withCalls(
          restCall(Method.POST, s"$basePath/v1/requests", requestAnalysis _),
          restCall(Method.PUT, s"$basePath/v1/requests/:id/status", setAnalysisStatus _)
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"$basePath/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
  }
}
//

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
