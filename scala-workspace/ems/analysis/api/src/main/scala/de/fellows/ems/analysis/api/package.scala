package de.fellows.ems.analysis

import de.fellows.app.assembly.commons.AssemblyFeature
import de.fellows.utils.meta._
import play.api.libs.json.{Format, Json}

package object api {

  object Features {
    val META  = AssemblyFeature("analysis", "meta")
    val DFM   = AssemblyFeature("analysis", "dfm")
    val IMAGE = AssemblyFeature("analysis", "image")
  }

  case class StateUpdate(states: Map[String, String])

  case class AnalysisRequest(
      name: String
  )

  case class AnalysisResult(
      id: String,
      states: Map[String, String],
      metaInfo: Option[MetaInfo],
      dfm: Option[MetaInfo]
  )

  case class Analysis(
      id: String,
      states: Map[String, String]
  )

  object AnalysisRequest {
    implicit val f: Format[AnalysisRequest] = Json.format[AnalysisRequest]
  }

  object Analysis {
    implicit val f: Format[Analysis] = Json.format[Analysis]
  }

  object StateUpdate {
    implicit val f: Format[StateUpdate] = Json.format[StateUpdate]
  }

}
