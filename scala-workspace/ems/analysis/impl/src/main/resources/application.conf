include "main-application.conf"

play.application.loader = de.fellows.ems.analysis.impl.AnalysisServiceLoader


analysis.cassandra.keyspace = ${fellows.persistence.rootKeyspace}analysis
# analysis.cassandra.keyspace = analysis


cassandra-journal {
  keyspace = ${analysis.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${analysis.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${analysis.cassandra.keyspace}read
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "analysis"
# fellows.serviceconfig = ${fellows.services.analysis}

akka {
  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # Log level for the very basic logger activated during ActorSystem startup.
  # This logger prints the log messages to stdout (System.out).
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  stdout-loglevel = "DEBUG"
}

fellows.storage {
  service = ${fellows.storage.base}/analysis
}

fellows.analysis.thresholds {
  max-copper-clearance = 0.5
}
