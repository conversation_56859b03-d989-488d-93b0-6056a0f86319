include "main-application.conf"

play.application.loader = de.fellows.ems.panel.impl.PanelServiceLoader


panel.cassandra.keyspace = ${fellows.persistence.rootKeyspace}panel


cassandra-journal {
  keyspace = ${panel.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${panel.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${panel.cassandra.keyspace}read
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "panel"
# fellows.serviceconfig = ${fellows.services.panel}

akka {
  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # Log level for the very basic logger activated during ActorSystem startup.
  # This logger prints the log messages to stdout (System.out).
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  stdout-loglevel = "DEBUG"
}

fellows.storage {
  service = ${fellows.storage.base}/panel
}

panel-dispatcher {
  type = Dispatcher
  executor = "thread-pool-executor"
  thread-pool-executor {
    fixed-pool-size = 10
  }
  throughput = 1
}
