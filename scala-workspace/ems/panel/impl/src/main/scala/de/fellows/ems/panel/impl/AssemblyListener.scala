package de.fellows.ems.panel.impl

import akka.Done
import akka.actor.ActorSystem
import akka.dispatch.MessageDispatcher
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assembly.commons.SharedAssemblyReference
import de.fellows.app.assemby.api.{AssemblyService, AssemblySharedMessage, SharedAssembly}
import de.fellows.ems.panel.api.CustomerPanel
import de.fellows.ems.panel.impl.cookie.{SharedAssemblyCookiePersistence, StatelessCookieCutter}
import de.fellows.ems.panel.impl.entity.customer.{CustomerPanelEntity, GetCustomerPanel}
import de.fellows.ems.panel.impl.read.customer.CustomerPanelRepository
import de.fellows.ems.panel.impl.read.working.WorkingPanelRepository
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.streams.{EmptyMessage, ValidMessage}
import de.fellows.utils.{FilePath, TopicUtils}
import play.api.Logging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class AssemblyListener(
    system: ActorSystem,
    assemblyService: AssemblyService,
    ereg: PersistentEntityRegistry,
    pcb: PCBService,
    rep: CustomerPanelRepository,
    workingPanels: WorkingPanelRepository
)(implicit ec: ExecutionContext, sd: ServiceDefinition) extends Logging {
  val started                             = System.currentTimeMillis()
  val blockableContext: MessageDispatcher = system.dispatchers.lookup("panel-dispatcher")

  def matchShares(share: SharedAssembly, panels: Seq[CustomerPanel]) = {
    logger.info(s"match share for ${share.id}} and panels ${panels.map(_.id)}}")
    val persistence = new SharedAssemblyCookiePersistence(
      ereg = ereg,
      pcb = pcb,
      workingPanels = workingPanels,
      assembly = SharedAssemblyReference(
        team = share.team,
        id = share.id,
        sharedAssembly = share.ref
      )
    )

    Future.sequence(
      panels.map { panel =>
        val (_: FilePath, wpanelPath: FilePath) =
          PanelServiceImpl.filePaths(share.id, panel.id.get, share.team)
        new StatelessCookieCutter(
          teamOfAssembly = share.ref.team,
          teamOfWorkingPanels = share.team,
          panel = panel,
          customerPreview = None,
          workingPreview = wpanelPath,
          persistence = persistence
        ).cut(this.blockableContext, true)
      }
    )

  }

  def getPanels(share: SharedAssembly): Future[Seq[UUID]] =
    rep.getCustomerPanelsForVersion(share.ref.team, share.ref.version)

  TopicUtils.subscribeLatest(assemblyService.shareTopic(), started) { msg =>
    msg.payload match {
      case ValidMessage(AssemblySharedMessage(share)) =>
        logger.info(s"match share for ${share.id}}")
        for {
          panelIds <- getPanels(share)
          customerPanels <-
            Future.sequence(panelIds.map(id => ereg.refFor[CustomerPanelEntity](id.toString).ask(GetCustomerPanel(id))))
          _ <- matchShares(share, customerPanels)
        } yield Done

      case EmptyMessage() => Future.successful(Done)
    }
  }

}
