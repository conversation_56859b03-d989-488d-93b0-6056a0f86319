package de.fellows.ems.panel.impl.read.customer

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BatchStatement, BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.panel.api
import de.fellows.ems.panel.impl.entity.customer.{CustomerPanelEvent, CustomerPanelUpdated}
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._

class CustomerPanelRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  def getCustomerPanelsForVersion(team: String, version: UUID): Future[Seq[UUID]] =
    session.selectAll(
      // language=SQL
      "SELECT * FROM customerpanelbyname WHERE team = ? and version = ?",
      team,
      version
    )
      .map(rws => rws.map(_.getUUID("id")))

  def getCustomerPanelForVersionByName(team: String, version: UUID, name: String): Future[Option[UUID]] =
    session.selectOne(
      // language=SQL
      "SELECT * FROM customerpanelbyname WHERE team = ? and version = ? and name = ?",
      team,
      version,
      name
    )
      .map(rws => rws.map(_.getUUID("id")))
}

private[impl] class CustomerPanelEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[CustomerPanelEvent] with StackrateLogging {

  var setCustomerPanelStmt: PreparedStatement    = _
  var deleteCustomerPanelStmt: PreparedStatement = _

  // language=SQL
  def createTables(): Future[Done] =
    for {
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS customerpanelbyname(
          |  team text,
          |  version uuid,
          |  id uuid,
          |  name text,
          |  PRIMARY KEY (team, version, name)
          | )
          |""".stripMargin
      )
    } yield Done

  def prepareStatements(): Future[Done] =
    // language=SQL
    for {
      setCustomerPanel <- session
        .prepare("UPDATE customerpanelbyname SET id =:id WHERE team = :team AND version = :version AND name = :name")
      deleteCustomerPanel <- session
        .prepare("DELETE FROM customerpanelbyname WHERE team = :team AND version = :version AND name = :name")
    } yield {
      PCBCodecHelper.registerPCBCodecs(session)

      this.setCustomerPanelStmt = setCustomerPanel
      this.deleteCustomerPanelStmt = deleteCustomerPanel
      Done
    }

  def delete(assRef: AssemblyReference, get: api.CustomerPanel) =
    Seq(
      deleteCustomerPanelStmt.bind()
        .setString("team", assRef.team)
        .setUUID("version", assRef.version)
        .setString("name", get.name)
    )

  def add(assRef: AssemblyReference, get: api.CustomerPanel) =
    Seq(
      setCustomerPanelStmt.bind()
        .setUUID("id", get.id.orNull)
        .setString("team", assRef.team)
        .setUUID("version", assRef.version)
        .setString("name", get.name)
    )

  def _update(event: CustomerPanelUpdated): (Seq[Seq[BoundStatement]]) =
    if (event.panel.isDefined) {
      if (event.previous.isDefined) {
        if (event.previous.get.name != event.panel.get.name) {
          logger.info(s"Updating customer panel ${event.previous.get.name} to ${event.panel.get.name}")
          Seq(delete(event.assRef, event.previous.get), add(event.assRef, event.panel.get))
        } else {
          Seq()
        }
      } else {
        Seq(add(event.assRef, event.panel.get))
      }
    } else {
      if (event.previous.isDefined) {
        Seq(delete(event.assRef, event.previous.get))
      } else {
        Seq()
      }
    }

  def update(event: CustomerPanelUpdated) =
    Future.sequence(_update(event).map { s =>
      session.executeWriteBatch(new BatchStatement().addAll(s.asJava))
    }).map(_ => Seq())

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[CustomerPanelEvent] =
    readSide.builder[CustomerPanelEvent]("customerpanel-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[CustomerPanelUpdated](e => update(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[CustomerPanelEvent]] = CustomerPanelEvent.Tag.allTags
}
