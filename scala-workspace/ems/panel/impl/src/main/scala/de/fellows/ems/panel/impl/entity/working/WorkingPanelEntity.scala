package de.fellows.ems.panel.impl.entity.working

import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.ems.panel.api.WorkingPanel

class WorkingPanelEntity extends PersistentEntity {
  override type Command = WorkingPanelCommand
  override type Event   = WorkingPanelEvent
  override type State   = Option[WorkingPanel]

  override def initialState: Option[WorkingPanel] = None

  override def behavior: Behavior = {
    case Some(s) => existing(s)
    case None    => missing
  }

  def missing: Actions =
    Actions()
      .onCommand[CreateWorkingPanel, WorkingPanel] {
        case (x: CreateWorkingPanel, ctx, _) =>
          ctx.thenPersist(
            WorkingPanelUpdated(x.team, Some(x.panel), None)
          )(_ => ctx.reply(x.panel))
      }
      .onReadOnlyCommand[GetWorkingPanel, WorkingPanel] {
        case (x: GetWorkingPanel, ctx, _) => notFound(ctx)
      }
      .onReadOnlyCommand[UpdateWorkingPanel, WorkingPanel] {
        case (x: GetWorkingPanel, ctx, _) => notFound(ctx)
      }
      .onReadOnlyCommand[DeleteWorkingPanel, WorkingPanel] {
        case (x: GetWorkingPanel, ctx, _) => notFound(ctx)
      }
      .onEvent {
        case (x: WorkingPanelUpdated, y) => x.panel
      }

  private def notFound(ctx: ReadOnlyCommandContext[WorkingPanel]) =
    ctx.invalidCommand("Working Panel does not exist")

  def existing(state: WorkingPanel): Actions =
    Actions()
      .onReadOnlyCommand[CreateWorkingPanel, WorkingPanel] {
        case (x: GetWorkingPanel, ctx, _) => notFound(ctx)

      }
      .onReadOnlyCommand[GetWorkingPanel, WorkingPanel] {
        case (x: GetWorkingPanel, ctx, _) =>
          ctx.reply(state)
      }
      .onCommand[UpdateWorkingPanel, WorkingPanel] {
        case (x: UpdateWorkingPanel, ctx, _) =>
          ctx.thenPersist(
            WorkingPanelUpdated(x.team, Some(x.panel.copy(id = state.id orElse x.panel.id)), Some(state))
          )(_ => ctx.reply(x.panel))
      }
      .onCommand[DeleteWorkingPanel, WorkingPanel] {
        case (x: DeleteWorkingPanel, ctx, _) =>
          ctx.thenPersist(
            WorkingPanelUpdated(x.team, None, Some(state))
          )(_ => ctx.reply(state))
      }
      .onEvent {
        case (x: WorkingPanelUpdated, y) => x.panel
      }
}
