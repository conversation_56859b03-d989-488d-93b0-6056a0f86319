package de.fellows.ems.panel.impl

import akka.actor.ActorSystem
import akka.dispatch.MessageDispatcher
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRef, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, AssemblyService, AssemblyUtils}
import de.fellows.app.security.AccessControlServiceComposition.authorizedString
import de.fellows.app.security.CombinedTokenAccessServiceComposition.auth
import de.fellows.ems.panel.api
import de.fellows.ems.panel.api._
import de.fellows.ems.panel.impl.cookie.{CustomerPanelRender, DefaultCookiePersistence, StatelessCookieCutter}
import de.fellows.ems.panel.impl.entity.customer._
import de.fellows.ems.panel.impl.entity.usage.{GetWorkingPanelsUsage, WorkingPanelsUsageEntity, WorkingPanelsUsageState}
import de.fellows.ems.panel.impl.entity.working._
import de.fellows.ems.panel.impl.read.customer.CustomerPanelRepository
import de.fellows.ems.panel.impl.read.working.WorkingPanelRepository
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.api.specification.units.WeightUnit.Gram
import de.fellows.ems.pcb.api.specification.units.WeightWithUnit
import de.fellows.ems.pcb.model.DFM.Properties.DFM
import de.fellows.ems.renderer.api.RendererService
import de.fellows.utils.FilePath
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.collaboration.{TimelineCommand, TimelineEvent}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta.DecimalProperty
import de.fellows.utils.security.{Auth0Token, TokenContent}
import play.api.libs.json.Json

import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class PanelServiceImpl(
    system: ActorSystem,
    ereg: PersistentEntityRegistry,
    ass: AssemblyService,
    customerPanels: CustomerPanelRepository,
    workingPanels: WorkingPanelRepository,
    pcb: PCBService,
    renderer: RendererService
)(implicit ec: ExecutionContext, sd: ServiceDefinition) extends PanelService
    with StackrateAPIImpl with StackrateLogging {

  val blockableContext: MessageDispatcher = system.dispatchers.lookup("panel-dispatcher")
  implicit val actorSystem: ActorSystem   = system

  override def _getCustomerPanels(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServerServiceCall[NotUsed, Seq[api.CustomerPanel]] =
    ServerServiceCall { _ =>
      _doGetGustomerPanels(team, version)
    }

  private def _doGetGustomerPanels(team: String, version: UUID): Future[Seq[CustomerPanel]] =
    customerPanels.getCustomerPanelsForVersion(team, version).flatMap(ids =>
      Future.sequence(ids.map { id =>
        ereg.refFor[CustomerPanelEntity](id.toString).ask(GetCustomerPanel(id))
      })
    )

  override def getCustomerPanels(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[api.CustomerPanelAPI]] =
    authorizedString(token =>
      s"customerpanels:${token.team}:${token.team}:${version}:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _doGetGustomerPanels(token.team, version).map(_.map(_.toApi))
      }
    }

  override def getSharedCustomerPanels(share: UUID): ServiceCall[NotUsed, Seq[CustomerPanelAPI]] =
    authorizedString(token =>
      s"customerpanels:${token.team}:${token.team}:${share}:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        for {
          a      <- ass._getAssemblyShare(token.team, share).invoke()
          panels <- _doGetGustomerPanels(a.assembly.team, a.share.ref.version).map(_.map(_.toApi))
        } yield panels
      }
    }
  override def addCustomerPanel(
      assembly: UUID,
      version: UUID,
      autoselect: Option[Boolean]
  ): ServiceCall[api.CustomerPanelAPI, api.CustomerPanelAPI] =
    authorizedString(token =>
      s"customerpanels:${token.team}:${token.team}:${version}:*:write"
    ) { (token, _) =>
      ServerServiceCall { panel =>
        val tcmd = TimelineCommand.of(token)
        _doAddCustomerPanel(
          assembly,
          version,
          token.team,
          panel,
          autoselect.getOrElse(false),
          tcmd,
          Some(token.username)
        )
      }
    }

  override def _deleteCustomerPanelByExternalId(
      team: String,
      pcbid: UUID,
      externalid: String
  ): ServiceCall[NotUsed, Done] =
    ServerServiceCall { _ =>
      pcb._getPCBV2(team, pcbid).invoke().flatMap { pcb =>
        customerPanels.getCustomerPanelsForVersion(team, pcb.id).flatMap(pids =>
          Future.sequence(pids.map { pid =>
            ereg.refFor[CustomerPanelEntity](pid.toString).ask(GetCustomerPanel(pid))
          })
        ).flatMap { existing =>
          existing.find(_.externalId.contains(externalid)) match {
            case Some(value) =>
              logger.info(s"delete luminovo panel ${externalid}: ${Json.prettyPrint(Json.toJson(value))}")

              val tcmd    = TimelineCommand.system
              val ass     = AssemblyReference(team, pcb.assembly, None, pcb.id)
              val panelId = value.id.get
              ereg.refFor[CustomerPanelEntity](panelId.toString).ask(DeleteCustomerPanel(ass, panelId, tcmd))
            case None => throw new TransportException(TransportErrorCode.NotFound, "Panel not found")
          }
        }
      }
    }

  override def _setCustomerPanelFromLuminovo(
      team: String,
      pcbid: UUID
  ): ServiceCall[LuminovoCustomerPanel, CustomerPanelAPI] =
    ServerServiceCall { panelWrapper =>
      logger.info(s"set panel from luminovo: $panelWrapper")
      pcb._getPCBV2(team, pcbid).invoke().flatMap { pcb =>
        val id = panelWrapper.panel match {
          case panel: Details =>
            panel.details.id.map(_.value).getOrElse(throw new TransportException(
              TransportErrorCode.NotAcceptable,
              "Panel ID is missing"
            ))

          case existing: Existing =>
            existing.existing.id.map(_.value).getOrElse(throw new TransportException(
              TransportErrorCode.NotAcceptable,
              "Panel ID is missing"
            ))
        }

        val convertedPanel = panelWrapper.convert(id, pcb.assembly, pcb.id)

        this._doUpsertLuminovoCustomerPanel(
          pcb.assembly,
          pcb.id,
          team,
          convertedPanel,
          false,
          TimelineCommand.system
        )

      }

    }

  def _doUpsertLuminovoCustomerPanel(
      assembly: UUID,
      version: UUID,
      team: String,
      panel: CustomerPanelAPI,
      autoselect: Boolean,
      tcmd: TimelineCommand
  ): Future[CustomerPanelAPI] =
    if (panel.externalId.isEmpty) {
      throw new TransportException(TransportErrorCode.BadRequest, "externalId is required")
    } else {
      AssemblyUtils.lifecycle(
        AssemblyReference(team, assembly, None, version),
        ass,
        AssemblyLifecycleStageName.CustomerPanel
      ) {
        customerPanels.getCustomerPanelsForVersion(team, version).flatMap(pids =>
          Future.sequence(pids.map { pid =>
            ereg.refFor[CustomerPanelEntity](pid.toString).ask(GetCustomerPanel(pid))
          })
        ).flatMap { existing =>
          val ass = AssemblyReference(team, assembly, None, version)
          val res: Future[CustomerPanel] = existing.find(_.externalId.contains(panel.externalId.get)) match {
            case Some(value) =>
              logger.info(
                s"update luminovo panel ${panel.name} (${panel.externalId}) for scenario ${panel.sourcingScenario}"
              )

              val entity = ereg.refFor[CustomerPanelEntity](value.id.get.toString)
              entity.ask(UpdateCustomerPanel(ass, panel.toInternal, tcmd))
            case None =>
              val id                                                = UUID.randomUUID()
              val entity: PersistentEntityRef[CustomerPanelCommand] = ereg.refFor[CustomerPanelEntity](id.toString)
              logger.info(
                s"create luminovo panel ${panel.name} (${panel.externalId}) for scenario ${panel.sourcingScenario}"
              )
              entity.ask(CreateCustomerPanel(ass, panel.copy(id = Some(id)).toInternal, tcmd))
          }

          res.flatMap { x =>
            cutCustomerPanel(team, tcmd, ass, x)
              .recover {
                case e =>
                  logger.error(s"failed to cut customer board", e)
                  x
              }
              .map(_.toApi)
          }
        }

      }
    }

  def _doAddCustomerPanel(
      assembly: UUID,
      version: UUID,
      team: String,
      panel: CustomerPanelAPI,
      autoselect: Boolean,
      tcmd: TimelineCommand,
      username: Option[String]
  ): Future[CustomerPanelAPI] =
    AssemblyUtils.lifecycle(
      AssemblyReference(team, assembly, None, version),
      ass,
      AssemblyLifecycleStageName.CustomerPanel
    ) {
      val id  = UUID.randomUUID()
      val ass = AssemblyReference(team, assembly, None, version)

      val entity: PersistentEntityRef[CustomerPanelCommand] = ereg.refFor[CustomerPanelEntity](id.toString)
      entity.ask(CreateCustomerPanel(ass, panel.copy(id = Some(id)).toInternal, tcmd)).flatMap { x =>
        val cutter = StatelessCookieCutter.withEntity(
          team,
          ass,
          x,
          ereg,
          workingPanels,
          pcb,
          renderer,
          Some(tcmd)
        )
        cutter.cut(
          this.blockableContext,
          autoselect
        ).map { x =>
          x.toApi
        }
      }
    }

  override def _updateCustomerPanelWeight(
      team: String,
      assembly: UUID,
      version: UUID,
      panel: String
  ): ServiceCall[String, CustomerPanelAPI] = {
    println(s"team: ${team} assembly: ${assembly} version: ${version} panelId: ${panel}")
    ServerServiceCall { newWeight =>
      val decimalWeight = WeightWithUnit(BigDecimal.apply(newWeight), Gram)
      _doUpdateCustomerPanelWeight(team, assembly, version, panel, decimalWeight)
    }
  }

  private def _doUpdateCustomerPanelWeight(
      team: String,
      assembly: UUID,
      version: UUID,
      panel: String,
      weight: WeightWithUnit
  ): Future[CustomerPanelAPI] = {
    val tcmd = TimelineCommand.system
    withCustomerPanel(team, assembly, version, panel) { id =>
      getCustomerPanel(assembly, version, team, id).map { customerPanel =>
        val ass     = AssemblyReference(team, assembly, None, version)
        val entity  = ereg.refFor[CustomerPanelEntity](panel.toString)
        val uppanel = customerPanel.copy(weight = Some(weight.to(Gram)))
        entity.ask(UpdateCustomerPanel(ass, uppanel, tcmd)).flatMap { x =>
          cutCustomerPanel(team, tcmd, ass, x).map(cp => cp.toApi)
        }
      }.flatten
    }
  }

  private def cutCustomerPanel(
      team: String,
      tcmd: TimelineCommand,
      ass: AssemblyReference,
      x: CustomerPanel
  ) =
    StatelessCookieCutter.withEntity(
      team,
      ass,
      x,
      ereg,
      workingPanels,
      pcb,
      renderer,
      Some(tcmd)
    ).cut(
      this.blockableContext,
      false
    )

  override def _updateCustomerPanel(
      team: String,
      assembly: UUID,
      version: UUID,
      panel: String
  ): ServiceCall[CustomerPanelAPI, CustomerPanelAPI] = {
    println(s"team: ${team} assembly: ${assembly} version: ${version} panelId: ${panel}")
    ServerServiceCall { uppanel =>
      val tcmd = TimelineCommand.system
      doUpdateCustomerPanel(assembly, version, panel, team, uppanel, tcmd)
    }
  }

  override def _reCutCustomerPanels(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Done] =
    ServerServiceCall { uppanel =>
      val tcmd = TimelineCommand.system
      val r: Future[Done] = this.customerPanels.getCustomerPanelsForVersion(team, version).flatMap { panelIds =>
        AssemblyUtils.lifecycle(
          AssemblyReference(team, assembly, None, version),
          ass,
          AssemblyLifecycleStageName.CustomerPanel
        ) {
          Future.sequence(panelIds.map { panel =>
            val ass = AssemblyReference(team, assembly, None, version)

            val entity = ereg.refFor[CustomerPanelEntity](panel.toString)
            entity.ask(GetCustomerPanel(panel)).flatMap { x =>
              cutCustomerPanel(team, tcmd, ass, x).map(cp => cp.toApi)
            }

          })

        }.map(_ => Done)

      }

      r
    }

  override def updateCustomerPanel(
      assembly: UUID,
      version: UUID,
      panel: String
  ): ServiceCall[api.CustomerPanelAPI, api.CustomerPanelAPI] =
    authorizedString(token =>
      s"customerpanels:${token.team}:${token.team}:${version}:*:write"
    ) { (token, _) =>
      ServerServiceCall { uppanel =>
        val tcmd = TimelineCommand.of(token)
        doUpdateCustomerPanel(assembly, version, panel, token.team, uppanel, tcmd)
      }
    }

  private def doUpdateCustomerPanel(
      assembly: UUID,
      version: UUID,
      panel: String,
      team: String,
      uppanel: CustomerPanelAPI,
      tcmd: TimelineCommand
  ) =
    AssemblyUtils.lifecycle(
      AssemblyReference(team, assembly, None, version),
      ass,
      AssemblyLifecycleStageName.CustomerPanel
    ) {
      withCustomerPanel(team, assembly, version, panel) { id =>
        val ass    = AssemblyReference(team, assembly, None, version)
        val entity = ereg.refFor[CustomerPanelEntity](panel.toString)
        entity.ask(UpdateCustomerPanel(ass, uppanel.toInternal, tcmd)).flatMap { x =>
          cutCustomerPanel(team, tcmd, ass, x).map(cp => cp.toApi)
        }
      }
    }

  override def _getCustomerPanelByID(team: String, panel: UUID): ServerServiceCall[NotUsed, api.CustomerPanel] =
    ServerServiceCall { _ =>
      ereg.refFor[CustomerPanelEntity](panel.toString).ask(GetCustomerPanel(panel))
    }

  override def _getCustomerPanel(
      team: String,
      assembly: UUID,
      version: UUID,
      panel: String
  ): ServerServiceCall[NotUsed, api.CustomerPanel] =
    ServerServiceCall { _ =>
      withCustomerPanel(team, assembly, version, panel) { id =>
        getCustomerPanel(assembly, version, team, id)
      }
    }

  override def getCustomerPanel(
      assembly: UUID,
      version: UUID,
      panel: String
  ): ServiceCall[NotUsed, api.CustomerPanelAPI] =
    auth {
      case _: Auth0Token => "view:pcb"

      case token => s"customerpanels:${token.getTeam}:${token.getTeam}:${version}:*:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        _doGetCustomerPanel(token.getTeam, assembly, version, panel)
      }
    }

  private def _doGetCustomerPanel(team: String, assembly: UUID, version: UUID, panel: String) =
    withCustomerPanel(team, assembly, version, panel) { id =>
      getCustomerPanel(assembly, version, team, id).map(_.toApi)
    }

  override def getSharedCustomerPanel(share: UUID, panel: String): ServiceCall[NotUsed, CustomerPanelAPI] =
    authorizedString(token =>
      s"customerpanels:${token.team}:${token.team}:${share}:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        for {
          a     <- ass._getAssemblyShare(token.team, share).invoke()
          panel <- _doGetCustomerPanel(a.assembly.team, a.assembly.id, a.share.ref.version, panel)
        } yield panel

      }
    }

  private def withCustomerPanel[T](
      team: String,
      assembly: UUID,
      version: UUID,
      panel: String
  )(cb: UUID => Future[T]): Future[T] =
    try {
      val id = UUID.fromString(panel)
      cb(id)
    } catch {
      case e: IllegalArgumentException =>
        customerPanels.getCustomerPanelForVersionByName(team, version, panel).flatMap { ido =>
          ido.map { id =>
            cb(id)
          //            getCustomerPanel(assembly, version, team, id)
          }.getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Panel not found"))
        }
    }

  private def getCustomerPanel(assembly: UUID, version: UUID, team: String, id: UUID) = {
    val ass = AssemblyReference(team, assembly, None, version)

    ereg.refFor[CustomerPanelEntity](id.toString).ask(GetCustomerPanel(id))
      .recover {
        case cmd: InvalidCommandException => throw new TransportException(TransportErrorCode.NotAcceptable, cmd.message)
      }
  }

  override def deleteCustomerPanel(assembly: UUID, version: UUID, panel: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"customerpanels:${token.team}:${token.team}:${version}:*:write"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        withCustomerPanel(token.team, assembly, version, panel) { id =>
          val tcmd = TimelineCommand.of(token)
          val ass  = AssemblyReference(token.team, assembly, None, version)
          ereg.refFor[CustomerPanelEntity](id.toString).ask(DeleteCustomerPanel(ass, id, tcmd))
        }

      }
    }

  override def getWorkingPanels(): ServiceCall[NotUsed, Seq[api.WorkingPanel]] =
    authorizedString(token =>
      s"workingpanels:${token.team}:${token.team}:*:*:read"
    ) { (token, _) =>
      _getWorkingPanels(token.team)
    }

  override def addWorkingPanelToTeam(team: String): ServiceCall[api.WorkingPanel, api.WorkingPanel] =
    authorizedString(token =>
      s"workingpanels:${team}:${team}:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { panel =>
        val id = UUID.randomUUID()
        ereg.refFor[WorkingPanelEntity](id.toString).ask(CreateWorkingPanel(team, panel.copy(id = Some(id))))
      }
    }

  override def addWorkingPanel(): ServiceCall[api.WorkingPanel, api.WorkingPanel] =
    authorizedString(token =>
      s"workingpanels:${token.team}:${token.team}:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { panel =>
        val id = panel.id.getOrElse(UUID.randomUUID())
        ereg.refFor[WorkingPanelEntity](id.toString).ask(CreateWorkingPanel(token.team, panel.copy(id = Some(id))))
      }
    }

  override def updateWorkingPanel(panel: UUID): ServiceCall[api.WorkingPanel, api.WorkingPanel] =
    authorizedString(token =>
      s"workingpanels:${token.team}:${token.team}:${panel}:*:write"
    ) { (token, _) =>
      ServerServiceCall { p =>
        ereg.refFor[WorkingPanelEntity](panel.toString).ask(UpdateWorkingPanel(token.team, p))
      }
    }

  override def deleteWorkingPanel(panel: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"workingpanels:${token.team}:${token.team}:${panel}:*:write"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        ereg.refFor[WorkingPanelEntity](panel.toString).ask(DeleteWorkingPanel(token.team, panel)).map(_ => Done)
      }
    }

  override def _getWorkingPanel(team: String, panel: UUID): ServerServiceCall[NotUsed, WorkingPanel] =
    ServerServiceCall { _ =>
      ereg.refFor[WorkingPanelEntity](panel.toString).ask(GetWorkingPanel(team, panel))
        .recover {
          case e: InvalidCommandException =>
            throw new TransportException(TransportErrorCode.NotFound, e.getLocalizedMessage)
        }

    }

  override def _getWorkingPanels(team: String): ServerServiceCall[NotUsed, Seq[WorkingPanel]] =
    ServerServiceCall { _ =>
      doGetWorkingPanels(team)
    }

  private def doGetWorkingPanels(team: String) =
    workingPanels.getWorkingPanelsForTeam(team).flatMap(ids =>
      Future.sequence(ids.map { id =>
        ereg.refFor[WorkingPanelEntity](id.toString).ask(GetWorkingPanel(team, id))
      })
    ).recover {
      case e: InvalidCommandException =>
        throw new TransportException(TransportErrorCode.NotFound, e.getLocalizedMessage)
    }

  override def _getWorkingPanelUsagesForVersion(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Seq[CustomerPanelUsageState]] =
    ServerServiceCall { _ =>
      val entity = ereg.refFor[WorkingPanelsUsageEntity](WorkingPanelsUsageState.entityId(
        team,
        version
      ))

      for {
        persistedUsages <- entity.ask(GetWorkingPanelsUsage(team, AssemblyReference(team, assembly, None, version)))
          .map { panels =>
            panels.panels
          }.recover { _ =>
            logger.warn(s"Failed to get working panel usage for $team $assembly $version")
            Seq()
          }

        // for backwards compatibility we need to check the customer panels
        customerPanels <- _doGetGustomerPanels(team, version).map(_.map(cp =>
          CustomerPanelUsageState(
            customerPanel = cp.id.get,
            selectedWorkingPanel = cp.selected,
            workingPanels = cp.working.getOrElse(Seq()).map { wp =>
              WorkingPanelUsageState(
                workingPanel = wp.workingPanel,
                customerPanels = wp.customerPanels,
                customerBoards = wp.customerBoards,
                panelYield = wp.panelYield,
                preview = wp.preview
              )
            }
          )
        ))
      } yield persistedUsages ++ customerPanels

    }
  override def _getWorkingPanelUsagesForShare(
      team: String,
      shareId: UUID
  ): ServiceCall[NotUsed, Seq[CustomerPanelUsageState]] =
    ServerServiceCall { _ =>
      for {
        share <- ass._getAssemblyShare(team, shareId).invoke()
        entity <- Future.successful(ereg.refFor[WorkingPanelsUsageEntity](WorkingPanelsUsageState.entityId(
          team,
          share.toReference
        )))
        panels <- entity.ask(GetWorkingPanelsUsage(team, share.toReference))
          .map(_.panels)
          .recover(_ => Seq())
      } yield panels

    }

  override def _getTransientWorkingPanelUsage(team: String): ServiceCall[CustomerPanelHelper, Seq[WorkingPanelUsage]] =
    ServerServiceCall { helper =>
      doGetWorkingPanels(team).map(_.map { wp =>
        StatelessCookieCutter.cutSimplePanel(
          wp,
          helper.width.map(_.doubleValue).getOrElse(0),
          helper.height.map(_.doubleValue).getOrElse(0),
          helper.count
        )
      })
    }

  override def getWorkingPanel(panel: UUID): ServiceCall[NotUsed, WorkingPanel] =
    authorizedString(token =>
      s"workingpanels:${token.team}:${token.team}:${panel}:*:read"
    ) { (token, _) =>
      _getWorkingPanel(token.team, panel)
    }

  override def customerPanelTimeline(): Topic[TimelineEvent] =
    TopicProducer.taggedStreamWithOffset(CustomerPanelEvent.Tag) { (tag, offset) =>
      ereg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: CustomerPanelTimelineChanged, _) => immutable.Seq((ev.evt, x.offset))
        case _                                                              => Nil
      }
    }

  override def _calculatePanelWeight(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Done] =
    ServerServiceCall { _ =>
      val ref = AssemblyReference(
        team,
        assembly,
        None,
        version
      )
      customerPanels.getCustomerPanelsForVersion(team, version).flatMap { pids =>
        Future.sequence(pids.map { pid =>
          ereg.refFor[CustomerPanelEntity](pid.toString).ask(GetCustomerPanel(pid))
        })
      }.flatMap { panels =>
        val persistence = new DefaultCookiePersistence(
          ereg = ereg,
          workingPanels = workingPanels,
          assembly = ref,
          tcmd = None,
          pcb = pcb
        )

        StatelessCookieCutter.collectPCBInfo(team, panels, persistence).flatMap { retrievedPcbs =>
          val weights = panels.flatMap { panel =>
            val renderedPanel =
              new CustomerPanelRender(team, panel, retrievedPcbs.map(x => x._1 -> (x._2.pcb -> x._2.outline))).render(
                None
              )
            StatelessCookieCutter.calculatePanelWeight(retrievedPcbs, renderedPanel, panel.count())
              .map(weight => panel.id.get -> weight)
          }.toMap

          Future.sequence(weights.map { x =>
            this._doUpdateCustomerPanelWeight(
              team = team,
              assembly = assembly,
              version = version,
              panel = x._1.toString,
              weight = x._2
            )
          })
            .flatMap { _ =>
              weights.headOption.map { x =>
                persistence.setPCBMetaData(
                  team,
                  assembly,
                  version,
                  Seq(DecimalProperty(DFM.PANEL_WEIGHT, x._2.to(Gram)))
                )
              }.getOrElse(Future.successful(Done))
            }

        }
      }

    }
}

object PanelServiceImpl {
  def filePaths(version: UUID, panel: UUID, token: TokenContent): (FilePath, FilePath) =
    filePaths(version, panel, token.team)

  def filePaths(version: UUID, panel: UUID, team: String): (FilePath, FilePath) = {
    val path       = PanelFileService.createFilePath(team, version.toString, panel.toString, "")
    val cpanelPath = path.copy(base = s"${path.base}/previews/customer", filename = "panel.svg")
    val wpanelPath = path.copy(base = s"${path.base}/previews/working")
    (cpanelPath, wpanelPath)
  }
}
