package de.fellows.ems.panel.impl.cookie

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assembly.commons.{AssemblyReference, SharedAssemblyReference}
import de.fellows.ems.layerstack.api.LayerstackDFM
import de.fellows.ems.panel.api
import de.fellows.ems.panel.api._
import de.fellows.ems.panel.impl.PanelServiceImpl
import de.fellows.ems.panel.impl.entity.customer.{
  CustomerPanelEntity,
  SetCustomerPanelInWorkingPanels,
  SetCustomerPanelPreview,
  UpdateCustomerPanel
}
import de.fellows.ems.panel.impl.entity.usage.{SetWorkingPanelsUsage, WorkingPanelsUsageEntity, WorkingPanelsUsageState}
import de.fellows.ems.panel.impl.entity.working.{GetWorkingPanel, WorkingPanelEntity}
import de.fellows.ems.panel.impl.read.working.WorkingPanelRepository
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.ems.pcb.api.specification.units.WeightUnit.Gram
import de.fellows.ems.pcb.api.specification.units.{AreaWithUnit, WeightWithUnit}
import de.fellows.ems.pcb.model.DFM.Properties.DFM
import de.fellows.ems.pcb.model.Outline
import de.fellows.ems.renderer.api.RendererService
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.FilePath
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta.{DecimalProperty, MetaInfo, Property}
import org.apache.batik.anim.dom.SVGDOMImplementation
import org.apache.batik.svggen.{SVGGeneratorContext, SVGGraphics2D}

import java.awt.font.TextLayout
import java.awt.geom.{AffineTransform, Rectangle2D}
import java.awt.{BasicStroke, Color, Dimension, Shape}
import java.util.UUID
import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}
import de.fellows.ems.pcb.api.specification.units.LengthWithUnit
import de.fellows.ems.pcb.api.specification.units.LengthUnit
import scala.util.Success
import scala.util.Failure
import org.apache.regexp.recompile
import de.fellows.ems.pcb.model

case class AlignmentDescription(
    offsetX: Double,
    offsetY: Double,
    countHorizontal: Int,
    countVertical: Int,
    fractal: Seq[AlignmentDescription]
) {
  def flip(odd: Boolean): AlignmentDescription = odd match {
    case false => this
    case true => AlignmentDescription(
        offsetX,
        offsetY,
        countVertical,
        countHorizontal,
        fractal.map(_.flip(!odd))
      )
  }

  override def toString: String = {
    val fstring = fractal.map(f => s"(${f.toString})").mkString(" + ")
    s"$countHorizontal x $countVertical${fstring}"
  }

  def >(other: AlignmentDescription): Boolean =
    this.count > other.count

  def >=(other: AlignmentDescription): Boolean =
    this.count >= other.count

  def <(other: AlignmentDescription): Boolean =
    this.count < other.count

  def <=(other: AlignmentDescription): Boolean =
    this.count <= other.count

  def max(other: AlignmentDescription): AlignmentDescription =
    if (other.count > count) {
      other
    } else {
      this
    }

  def count: Int =
    countHorizontal * countVertical + fractal.map(_.count).sum

}

case class SpecifiedDimensions(width: Option[Double], height: Option[Double])

object SpecifiedDimensions {
  def apply(width: Option[Double], height: Option[Double]): SpecifiedDimensions =
    new SpecifiedDimensions(width, height)

  def apply(widthXheight: (Option[Double], Option[Double])): SpecifiedDimensions =
    new SpecifiedDimensions(widthXheight._1, widthXheight._2)
}
case class OutlineInformation(outline: Option[Outline], dimensions: SpecifiedDimensions) {
  def getOptionalDimensions(bounds: Rectangle2D): Option[Rectangle2D] =
    if (dimensions.width.isEmpty && dimensions.height.isEmpty) {
      None
    } else {
      Some(
        new Rectangle2D.Double(
          0,
          0,
          dimensions.width.getOrElse(bounds.getWidth()),
          dimensions.height.getOrElse(bounds.getHeight)
        )
      )
    }

  def withExistingPanelDimensions(width: BigDecimal, height: BigDecimal): OutlineInformation =
    OutlineInformation(
      outline = outline,
      dimensions = SpecifiedDimensions(Some(width.doubleValue), Some(height.doubleValue))
    )
}

trait CookiePersistence {
  def getPCB(team: String, assembly: UUID, version: UUID): Future[PCBV2]

  def getPCBMetaData(team: String, assembly: UUID, version: UUID): Future[MetaInfo]
  def setPCBMetaData(team: String, assembly: UUID, version: UUID, data: Seq[Property]): Future[Done]

  def getOutline(team: String, assembly: UUID, version: UUID): Future[OutlineInformation]

  def setSelectedWorkingPanel(panel: CustomerPanel, bestYield: Option[UUID]): Future[CustomerPanel]

  def setCustomerPanelPreview(panel: CustomerPanel, cp: GCustomerPanel, preview: FilePath): Future[Done]

  def getWorkingPanelsForTeam(team: String): Future[Seq[WorkingPanel]]

  def setWorkingPanelInfo(
      customerPanel: CustomerPanel,
      usages: Seq[WorkingPanelUsage],
      chosen: Option[UUID]
  ): Future[CustomerPanel]

  def toState(usages: Seq[WorkingPanelUsage]): Seq[WorkingPanelUsageState] =
    usages.map { u =>
      WorkingPanelUsageState(
        workingPanel = u.workingPanel,
        customerPanels = u.customerPanels,
        customerBoards = u.customerBoards,
        panelYield = u.panelYield,
        preview = u.preview
      )
    }
}

class SharedAssemblyCookiePersistence(
    ereg: PersistentEntityRegistry,
    pcb: PCBService,
    workingPanels: WorkingPanelRepository,
    assembly: SharedAssemblyReference
)(implicit ec: ExecutionContext) extends CookiePersistence {
  val pcbCache: mutable.Map[UUID, PCBV2]                  = new mutable.HashMap()
  val outlineCache: mutable.Map[UUID, OutlineInformation] = new mutable.HashMap()

  val usageEntity =
    ereg.refFor[WorkingPanelsUsageEntity](WorkingPanelsUsageState.entityId(assembly.team, assembly))
  override def getPCB(team: String, assembly: UUID, version: UUID): Future[PCBV2] =
    if (pcbCache.contains(version)) {
      Future.successful(pcbCache(version))
    } else {
      pcb._getPCBV2(team, version).invoke()
        .map { pcb =>
          pcbCache.put(version, pcb)
          pcb
        }
    }

  override def getPCBMetaData(team: String, assembly: UUID, version: UUID): Future[MetaInfo] =
    pcb._getPCB(team, assembly, version).invoke().flatMap { p =>
      pcb._getSpecification(team, assembly, version, p.specifications.head).invoke().map { spec =>
        p.metaInfo.getOrElse(MetaInfo()) ++
          spec.headOption.map(_.settings).getOrElse(MetaInfo())
      }
    }

  override def setPCBMetaData(team: String, assembly: UUID, version: UUID, data: Seq[Property]): Future[Done] =
    Future.successful(Done)

  override def getOutline(team: String, assembly: UUID, version: UUID): Future[OutlineInformation] =
    if (outlineCache.contains(version)) {
      Future.successful(outlineCache(version))
    } else {

      for {
        ol <- pcb._getOutline(team, assembly, version).invoke()
          .map(Success(_))
          .recover(Failure(_))

        rec <- pcb._getPCBV2Specifications(team, version).invoke().map { specs =>
          val w = specs.head.settings.board.basic.boardWidth.map(_.to(LengthUnit.Millimeter).doubleValue)
          val h = specs.head.settings.board.basic.boardHeight.map(_.to(LengthUnit.Millimeter).doubleValue)

          (w, h)

        }
      } yield {
        val olInfo = OutlineInformation(
          outline = ol.toOption,
          dimensions = SpecifiedDimensions(rec)
        )

        outlineCache.put(
          version,
          olInfo
        )

        olInfo
      }
    }

  override def setSelectedWorkingPanel(panel: CustomerPanel, bestYield: Option[UUID]): Future[CustomerPanel] =
    Future.successful(panel)

  override def setCustomerPanelPreview(panel: CustomerPanel, cp: GCustomerPanel, preview: FilePath): Future[Done] =
    Future.successful(Done)

  override def getWorkingPanelsForTeam(team: String): Future[Seq[WorkingPanel]] =
    workingPanels.getWorkingPanelsForTeam(team).flatMap(wps =>
      Future.sequence(wps.map { wp =>
        ereg.refFor[WorkingPanelEntity](wp.toString).ask(GetWorkingPanel(team, wp))
      })
    )

  override def setWorkingPanelInfo(
      panel: CustomerPanel,
      usages: Seq[WorkingPanelUsage],
      chosen: Option[UUID]
  ): Future[CustomerPanel] =
    usageEntity.ask(SetWorkingPanelsUsage(
      team = assembly.team,
      assembly = assembly,
      panels = Seq(CustomerPanelUsageState(
        customerPanel = panel.id.get,
        selectedWorkingPanel = chosen,
        workingPanels = toState(usages)
      ))
    )).map(_ => panel)
}

class DefaultCookiePersistence(
    ereg: PersistentEntityRegistry,
    workingPanels: WorkingPanelRepository,
    assembly: AssemblyReference,
    tcmd: Option[TimelineCommand],
    pcb: PCBService
)(implicit ec: ExecutionContext) extends CookiePersistence {

  val usageEntity =
    ereg.refFor[WorkingPanelsUsageEntity](WorkingPanelsUsageState.entityId(assembly.team, assembly))

  def setCustomerPanelPreview(
      customerPanel: CustomerPanel,
      cp: GCustomerPanel,
      customerPreview: FilePath
  ): Future[Done] = {
    val entity = ereg.refFor[CustomerPanelEntity](customerPanel.id.get.toString)
    entity.ask(SetCustomerPanelPreview(
      assembly,
      Some(customerPreview),
      cp.paddedOutline.getWidth,
      cp.paddedOutline.getHeight
    )).map(_ => Done)
  }

  def getWorkingPanelsForTeam(team: String): Future[Seq[WorkingPanel]] =
    workingPanels.getWorkingPanelsForTeam(team).flatMap(wps =>
      Future.sequence(wps.map { wp =>
        ereg.refFor[WorkingPanelEntity](wp.toString).ask(GetWorkingPanel(team, wp))
      })
    )

  override def setWorkingPanelInfo(
      customerPanel: CustomerPanel,
      usages: Seq[WorkingPanelUsage],
      chosen: Option[UUID]
  ): Future[CustomerPanel] = {
    val entity = ereg.refFor[CustomerPanelEntity](customerPanel.id.get.toString)
    for {
      p <-
        entity.ask(SetCustomerPanelInWorkingPanels(
          assembly,
          Some(usages),
          chosen,
          tcmd.getOrElse(TimelineCommand.system)
        ))
      _ <- usageEntity.ask(
        SetWorkingPanelsUsage(
          team = assembly.team,
          assembly = assembly,
          panels =
            Seq(CustomerPanelUsageState(
              customerPanel = p.id.get,
              selectedWorkingPanel = chosen,
              workingPanels = toState(usages)
            ))
        )
      )
    } yield p
  }

  override def setSelectedWorkingPanel(r: CustomerPanel, bestYield: Option[UUID]): Future[CustomerPanel] = {
    val entity = ereg.refFor[CustomerPanelEntity](r.id.get.toString)
    entity.ask(UpdateCustomerPanel(
      assembly,
      r.copy(selected = r.bestYield),
      tcmd.getOrElse(TimelineCommand.system)
    ))
  }

  override def getPCB(team: String, assembly: UUID, version: UUID): Future[PCBV2] =
    pcb._getPCBV2(team, version).invoke()

  override def getPCBMetaData(team: String, assembly: UUID, version: UUID): Future[MetaInfo] =
    pcb._getPCB(team, assembly, version).invoke().flatMap { p =>
      pcb._getSpecification(team, assembly, version, p.specifications.head).invoke().map { spec =>
        p.metaInfo.getOrElse(MetaInfo()) ++
          spec.headOption.map(_.settings).getOrElse(MetaInfo())
      }
    }

  override def setPCBMetaData(team: String, assembly: UUID, version: UUID, data: Seq[Property]): Future[Done] =
    pcb._setMetaInfoProperty(team, assembly, version, Some(true)).invoke(data)

  override def getOutline(team: String, assembly: UUID, version: UUID): Future[OutlineInformation] =
    for {

      ol <- pcb._getOutline(team, assembly, version).invoke()
        .map(Success(_))
        .recover(Failure(_))

      rec <- pcb._getPCBV2Specifications(team, version).invoke().map { specs =>
        val w = specs.head.settings.board.basic.boardWidth.map(_.to(LengthUnit.Millimeter).doubleValue)
        val h = specs.head.settings.board.basic.boardHeight.map(_.to(LengthUnit.Millimeter).doubleValue)

        (w, h)
      }
    } yield OutlineInformation(
      outline = ol.toOption,
      dimensions = SpecifiedDimensions(rec)
    )

}

class StatelessCookieCutter(
    teamOfAssembly: String,
    teamOfWorkingPanels: String,
    panel: CustomerPanel,
    customerPreview: Option[FilePath],
    workingPreview: FilePath,
    persistence: CookiePersistence
) {
  val svgNS = "http://www.w3.org/2000/svg"

  def createGraphics: SVGGraphics2D = {
    val domImpl  = SVGDOMImplementation.getDOMImplementation
    val document = domImpl.createDocument(svgNS, "svg", null)
    val ctx      = SVGGeneratorContext.createDefault(document)
    new SVGGraphics2D(ctx, false)
  }

  def cut(implicit ctx: ExecutionContext, autoselect: Boolean): Future[CustomerPanel] = {

    val pcbs = StatelessCookieCutter.collectPCBInfo(teamOfAssembly, Seq(panel), persistence)

    pcbs
      .flatMap { retrievedPcbs =>
        val render =
          new CustomerPanelRender(teamOfAssembly, panel, retrievedPcbs.map(x => x._1 -> (x._2.pcb -> x._2.outline)))
        val cp = render.render(customerPreview)

        // get the layer count of the panel.
        // this will fail for panels with different boards that have different layer counts
        val layerCounts = retrievedPcbs.map(_._2.pcb).flatMap { pcb =>
          pcb.properties.layerStack.layercount orElse (pcb.specifications.headOption.flatMap(
            _.settings.layerStack.layercount
          ))
        }.toSet.headOption

        customerPreview
          .map(persistence.setCustomerPanelPreview(panel, cp, _))
          .getOrElse(Future.successful(Done))
          .flatMap { _ =>
            persistence.getWorkingPanelsForTeam(teamOfWorkingPanels)
              .flatMap { wPanels =>
                val filteredPanels = wPanels.filter { wp =>
                  // no restriction
                  layerCounts.isEmpty ||
                  (
                    wp.maxLayerCount.forall(max => max >= layerCounts.get) &&
                      wp.minLayerCount.forall(min => min <= layerCounts.get)
                  )
                }
                val usages: Seq[WorkingPanelUsage] = StatelessCookieCutter.cut(filteredPanels, cp, panel, saveImg _)
                val harry: Option[UUID]            = usages.maxByOption(_.panelYield).map(_.workingPanel)
                persistence.setWorkingPanelInfo(panel, usages, harry).flatMap { r =>
                  if (autoselect) {
                    persistence.setSelectedWorkingPanel(r, r.bestYield)
                  } else {
                    Future.successful(r)
                  }
                }
              }
          }.recover {
            case e: Throwable =>
              e.printStackTrace()
              throw e
          }

      }
  }

  def saveImg(workingPanel: WorkingPanel): SVGGraphics2D => FilePath = {
    def saveWPImg(gr: SVGGraphics2D): FilePath = {
      val path = workingPreview.copy(filename = s"panel-${workingPanel.name}-${workingPanel.id.get}.svg")
      gr.setSVGCanvasSize(new Dimension(workingPanel.width.intValue, workingPanel.height.intValue))
      Renderer.writeSVG(gr, path)
      path
    }

    saveWPImg
  }

}

object StatelessCookieCutter extends StackrateLogging {

  def collectPCBs(teamOfAssembly: String, panels: Seq[CustomerPanel]): Set[AssemblyReference] =
    panels.flatMap { panel =>
      panel.elements.flatMap {
        case x: api.CustomerPanelBoard => Seq(AssemblyReference(teamOfAssembly, x.assembly, None, x.version))
        case x: api.CustomerPanelBlock => x.elements.map { x =>
            AssemblyReference(teamOfAssembly, x.assembly, None, x.version)
          }
      }
    }.toSet

  case class PCBInfo(pcb: PCBV2, outline: OutlineInformation, meta: MetaInfo)

  def collectPCBInfo(teamOfAssembly: String, panel: Seq[CustomerPanel], persistence: CookiePersistence)(implicit
      ec: ExecutionContext
  ): Future[Map[AssemblyReference, PCBInfo]] = {
    val pcbs = collectPCBs(teamOfAssembly, panel)
    Future.sequence(pcbs.map { pcb =>
      (for {
        meta    <- persistence.getPCBMetaData(pcb.team, pcb.id, pcb.version)
        pcbv2   <- persistence.getPCB(pcb.team, pcb.id, pcb.version)
        outline <- persistence.getOutline(pcb.team, pcb.id, pcb.version)
      } yield PCBInfo(pcbv2, outline, meta)).map(p => pcb -> p)
    })
      .map(_.toMap)
  }

  def calculatePanelWeight(
      retrievedPcbs: Map[AssemblyReference, PCBInfo],
      cp: GCustomerPanel,
      pcbCount: Int
  ): Option[WeightWithUnit] =
    if (retrievedPcbs.map(_._2.pcb.id).toSet.size == 1) {

      // we only found 1 pcb. This should be the normal and only case right now, but we still have to check,
      // because we currently can have different pcbs in one panel and no checks right now to ensure that
      // this is actually possible
      // if there are more than 1 pcb found, we just skip the weight calculation

      val weightWithoutCopper = retrievedPcbs.head._2.meta.get[DecimalProperty](LayerstackDFM.WEIGHT_WITHOUT_CU).map(
        gram => WeightWithUnit(gram.value, Gram)
      )
      val weightWithCopper = retrievedPcbs.head._2.meta.get[DecimalProperty](LayerstackDFM.WEIGHT).map(gram =>
        WeightWithUnit(gram.value, Gram)
      )
      val pcbArea = retrievedPcbs.head._2.meta.get[DecimalProperty](DFM.AREA).map(gram => AreaWithUnit.sqmm(gram.value))

      for {
        weightWithoutCopper <- weightWithoutCopper
        weightWithCopper    <- weightWithCopper
        pcbArea             <- pcbArea
      } yield {
        val summedPCBWeight   = weightWithCopper * pcbCount
        val areaOfCustomPanel = AreaWithUnit.sqmm(cp.paddedOutline.getWidth * cp.paddedOutline.getHeight)
        val areaOfPCBS        = pcbArea * pcbCount
        val restArea          = areaOfCustomPanel - areaOfPCBS

        val weightWithoutCopperPerArea = weightWithoutCopper.perArea(pcbArea)
        val restWeight                 = weightWithoutCopperPerArea * restArea

        summedPCBWeight + restWeight
      }
    } else {
      None
    }

  def withEntity(
      team: String,
      ass: AssemblyReference,
      panel: CustomerPanel,
      ereg: PersistentEntityRegistry,
      workingPanels: WorkingPanelRepository,
      pcb: PCBService,
      renderer: RendererService,
      tcmd: Option[TimelineCommand]
  )(implicit ectx: ExecutionContext) = {

    val (cpanelPath: FilePath, wpanelPath: FilePath) = PanelServiceImpl.filePaths(ass.version, panel.id.get, team)

    val persistence = new DefaultCookiePersistence(
      ereg = ereg,
      workingPanels = workingPanels,
      assembly = ass,
      tcmd = tcmd,
      pcb = pcb
    )
    new StatelessCookieCutter(
      teamOfAssembly = team,
      teamOfWorkingPanels = team,
      panel = panel,
      customerPreview = Some(cpanelPath),
      workingPreview = wpanelPath,
      persistence = persistence
    )
  }

  private def drawCustomerPanelInWorkingPanel(
      cPanel: GCustomerPanel,
      rotateTransform: AffineTransform,
      rotate: Boolean,
      outline: Rectangle2D,
      g: SVGGraphics2D,
      trans: AffineTransform
  ) = {

    val t = new AffineTransform()
    t.concatenate(trans)
    if (rotate) {
      t.concatenate(rotateTransform)
    }

    g.setStroke(new BasicStroke(3f))
    g.setColor(new Color(0x546e7a))
    val transformedOutline = t.createTransformedShape(outline)
    g.draw(transformedOutline)
    g.setColor(new Color(0xaab0bec5, true))
    g.fill(transformedOutline)

    g.setStroke(new BasicStroke(1f))
    val d = cPanel ~ t

    d.elements.foreach {
      case x: GBlock => drawBlock(g)(x)
      case x: GBoard => drawBoard(g)(x)
    }
  }

  /** do a full cut of an existing customer panel on a working panel and do the image generation with the proper outline
    */
  def cut(
      workingPanels: Seq[WorkingPanel],
      cPanel: GCustomerPanel,
      panel: CustomerPanel,
      saveImg: WorkingPanel => (SVGGraphics2D => FilePath)
  ): Seq[WorkingPanelUsage] =
    try
      workingPanels.map(wp => cut(wp, cPanel, panel, saveImg(wp)))
    catch {
      case e: OutOfMemoryError =>
        e.printStackTrace()
        Seq()
    }

  private def drawBlock(gr: SVGGraphics2D)(xx: GBlock) =
    xx.boards.foreach(drawBoard(gr))

  private def drawBoard(gr: SVGGraphics2D)(xx: GBoard) = {
    gr.setColor(Color.WHITE)
    gr.draw(xx.paddedOutline)
    gr.setColor(new Color(0x004200))

    val bounds      = xx.paddedOutline.getBounds2D
    val shapeBounds = xx.shape.getBounds2D

    gr.fill(xx.shape)

    if (xx.multiplier > 1) {
      val oldFont = gr.getFont
      gr.setColor(new Color(0xffffff))
      var fits                    = false
      val str                     = "x" + xx.multiplier
      var fontsize                = 20f;
      var textbounds: Rectangle2D = null
      var textoutline: Shape      = null
      while (!fits && fontsize > 1) {
        val newFont = oldFont.deriveFont(fontsize)
        val textTl  = new TextLayout(str, newFont, gr.getFontRenderContext)
        textoutline = textTl.getOutline(null)
        textbounds = textoutline.getBounds2D

        if (textbounds.getWidth < bounds.getWidth) {
          fits = true;
        } else {
          fontsize -= 0.1f
        }
      }
      val movedOutline = AffineTransform.getTranslateInstance(
        bounds.getCenterX - (textbounds.getWidth / 2),
        bounds.getCenterY + (textbounds.getHeight / 2)
      )
        .createTransformedShape(textoutline)

      gr.fill(movedOutline)
      gr.setColor(new Color(0x000000))
      gr.setStroke(new BasicStroke(fontsize / 25))
      gr.draw(movedOutline)

    }

  }

  /** Do a simple cut with a nonexistent customer panel (we just know the dimensions) on a working panel, no image generation.
    * @return
    */
  def cutSimplePanel(workingPanel: WorkingPanel, width: Double, height: Double, boardsOnCustomerPanel: Int) = {

    val availableWidth  = workingPanel.width.doubleValue
    val availableHeight = workingPanel.height.doubleValue

    val rotated  = align(availableWidth, availableHeight, width, height)
    val straight = align(availableWidth, availableHeight, height, width)

    val maximalAlignment =
      if (rotated > straight) {
        rotated
      } else {
        straight
      }

    val cPanelArea = width * height
    val wPanelArea = workingPanel.width * workingPanel.height

    val customerPanels = maximalAlignment.count
    val boardYield     = ((cPanelArea * customerPanels) / wPanelArea).doubleValue

    WorkingPanelUsage(
      workingPanel.id.get,
      customerPanels,
      customerPanels * boardsOnCustomerPanel,
      boardYield,
      None
    )
  }

  def cut(
      workingPanel: WorkingPanel,
      cPanel: GCustomerPanel,
      panel: CustomerPanel,
      imgHandler: SVGGraphics2D => FilePath
  ): WorkingPanelUsage = {
    val tsg: ThreadSafeGraphics = new ThreadSafeGraphics(() => Renderer.createGraphics)
    val spacing: BigDecimal     = workingPanel.spacing.getOrElse(0)
    val cPanelOutline = new Rectangle2D.Double(
      (cPanel.paddedOutline.getX - (spacing / 2)).doubleValue,
      (cPanel.paddedOutline.getY - (spacing / 2)).doubleValue,
      (cPanel.paddedOutline.getWidth + (spacing)).doubleValue,
      (cPanel.paddedOutline.getHeight + (spacing)).doubleValue
    )

    if (cPanelOutline.getWidth == 0 || cPanelOutline.getHeight == 0) {
      throw new IllegalArgumentException("Empty Outline")
    }

    val rotateTransform = new AffineTransform()

    rotateTransform.rotate(Math.toRadians(90), cPanelOutline.getMinX, cPanelOutline.getMinY)
    rotateTransform.translate(0, -cPanelOutline.getHeight)

    // get the full available witdth of the working panel
    val availableWidth  = (workingPanel.width - spacing).doubleValue
    val availableHeight = (workingPanel.height - spacing).doubleValue

    val rotatedPanelOutline = rotateTransform.createTransformedShape(cPanelOutline).getBounds2D
    val panelOutline        = cPanelOutline.getBounds2D

    val origCustomerWidth =
      (panelOutline.getWidth + panel.spacing.horizontalSpacing.getOrElse(BigDecimal(0)).doubleValue)
    val origCustomerHeight =
      (panelOutline.getHeight + panel.spacing.verticalSpacing.getOrElse(BigDecimal(0)).doubleValue)

    val rotatedOrigCustomerWidth =
      (rotatedPanelOutline.getWidth + panel.spacing.horizontalSpacing.getOrElse(BigDecimal(0)).doubleValue)
    val rotatedCustomerHeight =
      (rotatedPanelOutline.getHeight + panel.spacing.verticalSpacing.getOrElse(BigDecimal(0)).doubleValue)

    // we run the algorithm twice, once starting with the straight panel and once rotated by 90 degrees
    val rotated  = align(availableWidth, availableHeight, rotatedOrigCustomerWidth, rotatedCustomerHeight)
    val straight = align(availableWidth, availableHeight, origCustomerWidth, origCustomerHeight)

    val (rotate, outline, maximalAlignment) =
      if (rotated > straight) {
        (true, cPanelOutline, rotated)
      } else {
        (false, cPanelOutline, straight)
      }

    var customerPanels = 0
    var customerBoards = 0

    val customerWidth =
      (outline.getBounds2D.getWidth + panel.spacing.horizontalSpacing.getOrElse(BigDecimal(0)).doubleValue)
    val customerHeight =
      (outline.getBounds2D.getHeight + panel.spacing.verticalSpacing.getOrElse(BigDecimal(0)).doubleValue)
    val (cp, cb) = tsg.submit { g =>
      // TODO: count boards is 0
      var countBoards         = 0
      var countCustomerPanels = 0

      val panelFill = new Rectangle2D.Double(0, 0, workingPanel.width.doubleValue, workingPanel.height.doubleValue)
      g.setStroke(new BasicStroke(3f))

      g.setColor(new Color(0xbcaaa4))
      g.fill(panelFill)
      g.setColor(new Color(0xaa6d4c41, true))
      g.draw(panelFill)
      g.setStroke(new BasicStroke(1f))

      def drawFractal(_alignment: AlignmentDescription, odd: Boolean): Unit = {
        val alignment = _alignment.flip(odd)

        for {
          cx <- 1 to alignment.countHorizontal
          cy <- 1 to alignment.countVertical
        } yield {
          val trans = new AffineTransform()
          trans.translate(alignment.offsetX, alignment.offsetY)
          // translate the working panel margin
          trans.translate(
            workingPanel.spacing.map(_.doubleValue).getOrElse(0),
            workingPanel.spacing.map(_.doubleValue).getOrElse(0)
          )

          if (rotate ^ odd) {
            trans.translate(
              panel.spacing.leftPadding.getOrElse(BigDecimal(0)).doubleValue + (cx - 1) * customerHeight,
              panel.spacing.topPadding.getOrElse(BigDecimal(0)).doubleValue + (cy - 1) * customerWidth
            )
          } else {
            trans.translate(
              panel.spacing.leftPadding.getOrElse(BigDecimal(0)).doubleValue + (cx - 1) * customerWidth,
              panel.spacing.topPadding.getOrElse(BigDecimal(0)).doubleValue + (cy - 1) * customerHeight
            )
          }

          drawCustomerPanelInWorkingPanel(cPanel, rotateTransform, rotate ^ odd, outline, g, trans)

          countCustomerPanels += 1
          countBoards += panel.count() // cPanel.boards

        }

        alignment.fractal.foreach { frac =>
          drawFractal(frac, !odd)
        }
      }

      drawFractal(maximalAlignment, false)

      (countCustomerPanels, countBoards)
    }

    customerPanels += cp
    customerBoards += cb

    val path = tsg.submit { gr =>
      imgHandler(gr)
    }

    val cPanelArea = outline.getWidth * outline.getHeight
    val wPanelArea = workingPanel.width * workingPanel.height

    val boardYield = ((cPanelArea * customerPanels) / wPanelArea).doubleValue
    println(
      s"found $customerBoards boards on ${customerPanels} panels. Yield:  ${cPanelArea} * ${customerPanels} $boardYield"
    )

    WorkingPanelUsage(
      workingPanel.id.get,
      customerPanels,
      customerBoards,
      boardYield,
      Some(path)
    )
  }

  /** align a rectangle of size customerWidth x customerHeight in a rectangle of size workingWidth x workingHeight as efficiently as possible
    * @param workingWidth
    * @param workingHeight
    * @param customerWidth
    * @param customerHeight
    * @return
    */
  def align(
      workingWidth: Double,
      workingHeight: Double,
      customerWidth: Double,
      customerHeight: Double
  ): AlignmentDescription = {

    def doAlign(
        offsetX: Double,
        offsetY: Double,
        width: Double,
        height: Double,
        customerWidth: Double,
        customerHeight: Double
    ): AlignmentDescription = {
      val countHorizontal = Math.floor(width / customerWidth).asInstanceOf[Int]
      val countVertical   = Math.floor(height / customerHeight).asInstanceOf[Int]

      val thisWidth = countHorizontal * customerWidth

      val remainingHorizontal = width - thisWidth
      val thisHeight          = countVertical * customerHeight

      val remainingVertical = height - thisHeight

      val fractal =
        if (countHorizontal * countVertical > 0) {
          // flip axes and align again
          // i.e. take the remaining space and align the rotated panel on it.
          // since we put as many of the straight panels as possible on the current space, the only way something *may*
          // still fit is if we rotate it and try on the remaining space
          Seq(
            doAlign(offsetX + thisWidth, offsetY, height, remainingHorizontal, customerWidth, customerHeight),
            doAlign(offsetX, offsetY + thisHeight, remainingVertical, width, customerWidth, customerHeight)
          )
        } else {
          Seq()
        }

      AlignmentDescription(offsetX, offsetY, countHorizontal, countVertical, fractal)

    }

    doAlign(0, 0, workingWidth, workingHeight, customerWidth, customerHeight)
  }

}
