package de.fellows.ems.panel.impl.read.working

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.ems.panel.api
import de.fellows.ems.panel.impl.entity.working.{WorkingPanelEvent, WorkingPanelUpdated}
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.communication.ServiceDefinition

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class WorkingPanelRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  def getWorkingPanelsForTeam(team: String): Future[Seq[UUID]] =
    session.selectAll(
      // language=SQL
      "SELECT * FROM workingpanelbyname WHERE team = ?",
      team
    )
      .map(rws => rws.map(_.getUUID("id")))
}

private[impl] class WorkingPanelEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[WorkingPanelEvent] {

  var setWorkingPanelStmt: PreparedStatement    = _
  var deleteWorkingPanelStmt: PreparedStatement = _

  // language=SQL
  def createTables(): Future[Done] =
    for {
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS workingpanelbyname(
          |  team text,
          |  id uuid,
          |  name text,
          |  PRIMARY KEY (team, name)
          | )
          |""".stripMargin
      )
    } yield Done

  def prepareStatements(): Future[Done] =
    // language=SQL
    for {
      setWorkingPanel <- session
        .prepare("UPDATE workingpanelbyname SET id =:id WHERE team = :team AND name = :name")
      deleteWorkingPanel <- session
        .prepare("DELETE FROM workingpanelbyname WHERE team = :team AND name = :name")
    } yield {
      PCBCodecHelper.registerPCBCodecs(session)

      this.setWorkingPanelStmt = setWorkingPanel
      this.deleteWorkingPanelStmt = deleteWorkingPanel
      Done
    }

  def delete(team: String, get: api.WorkingPanel) =
    Seq(
      deleteWorkingPanelStmt.bind()
        .setString("team", team)
        .setString("name", get.name)
    )

  def add(team: String, get: api.WorkingPanel) =
    Seq(
      setWorkingPanelStmt.bind()
        .setUUID("id", get.id.orNull)
        .setString("team", team)
        .setString("name", get.name)
    )

  def update(event: WorkingPanelUpdated): Future[Seq[BoundStatement]] =
    Future.successful(
      if (event.panel.isDefined) {
        if (event.previous.isDefined) {
          if (event.previous.get.name != event.panel.get.name) {
            delete(event.team, event.previous.get) ++ add(event.team, event.panel.get)
          } else {
            Seq()
          }
        } else {
          add(event.team, event.panel.get)
        }
      } else {
        if (event.previous.isDefined) {
          delete(event.team, event.previous.get)
        } else {
          Seq()
        }
      }
    )

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[WorkingPanelEvent] =
    readSide.builder[WorkingPanelEvent]("workingpanel-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[WorkingPanelUpdated](e => update(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[WorkingPanelEvent]] = WorkingPanelEvent.Tag.allTags
}
