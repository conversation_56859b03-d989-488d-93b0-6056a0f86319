package de.fellows.ems.panel.impl.entity

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.panel.api.{CustomerPanel, WorkingPanelUsage}
import de.fellows.utils.FilePath
import de.fellows.utils.collaboration.{TimelineCommand, TimelineEvent}
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object customer {

  sealed trait CustomerPanelCommand

  sealed trait CustomerPanelEvent extends AggregateEvent[CustomerPanelEvent] {
    override def aggregateTag = CustomerPanelEvent.Tag
  }

  object CustomerPanelEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[CustomerPanelEvent](NumShards)
  }

  case class CreateCustomerPanel(assRef: AssemblyReference, panel: CustomerPanel, tcmd: TimelineCommand)
      extends CustomerPanelCommand with ReplyType[CustomerPanel]

  case class GetCustomerPanel(panel: UUID) extends CustomerPanelCommand with ReplyType[CustomerPanel]

  case class UpdateCustomerPanel(assRef: AssemblyReference, panel: CustomerPanel, tcmd: TimelineCommand)
      extends CustomerPanelCommand with ReplyType[CustomerPanel]

  case class SetCustomerPanelInWorkingPanels(
      assRef: AssemblyReference,
      working: Option[Seq[WorkingPanelUsage]],
      bestYield: Option[UUID],
      tcmd: TimelineCommand
  ) extends CustomerPanelCommand with ReplyType[CustomerPanel]

  case class SetCustomerPanelPreview(
      assRef: AssemblyReference,
      preview: Option[FilePath],
      width: BigDecimal,
      height: BigDecimal
  ) extends CustomerPanelCommand with ReplyType[CustomerPanel]

  case class DeleteCustomerPanel(assRef: AssemblyReference, panel: UUID, tcmd: TimelineCommand)
      extends CustomerPanelCommand with ReplyType[Done]

  case class CustomerPanelUpdated(
      assRef: AssemblyReference,
      panel: Option[CustomerPanel],
      previous: Option[CustomerPanel]
  ) extends CustomerPanelEvent

  case class CustomerPanelInWorkingPanelsUpdated(
      assRef: AssemblyReference,
      panel: Option[CustomerPanel],
      previous: Option[CustomerPanel]
  ) extends CustomerPanelEvent

  case class CustomerPanelTimelineChanged(evt: TimelineEvent) extends CustomerPanelEvent

  object CreateCustomerPanel {
    implicit val ctx: Format[CreateCustomerPanel] = Json.format[CreateCustomerPanel]
  }

  object GetCustomerPanel {
    implicit val ctx: Format[GetCustomerPanel] = Json.format[GetCustomerPanel]
  }

  object UpdateCustomerPanel {
    implicit val ctx: Format[UpdateCustomerPanel] = Json.format[UpdateCustomerPanel]
  }

  object DeleteCustomerPanel {
    implicit val ctx: Format[DeleteCustomerPanel] = Json.format[DeleteCustomerPanel]
  }

  object SetCustomerPanelInWorkingPanels {
    implicit val ctx: Format[SetCustomerPanelInWorkingPanels] = Json.format[SetCustomerPanelInWorkingPanels]
  }

  object SetCustomerPanelPreview {
    implicit val ctx: Format[SetCustomerPanelPreview] = Json.format[SetCustomerPanelPreview]
  }

  object CustomerPanelUpdated {
    implicit val ctx: Format[CustomerPanelUpdated] = Json.format[CustomerPanelUpdated]
  }

  object CustomerPanelInWorkingPanelsUpdated {
    implicit val ctx: Format[CustomerPanelInWorkingPanelsUpdated] = Json.format[CustomerPanelInWorkingPanelsUpdated]
  }

  object CustomerPanelTimelineChanged {
    implicit val ctx: Format[CustomerPanelTimelineChanged] = Json.format[CustomerPanelTimelineChanged]
  }

}
