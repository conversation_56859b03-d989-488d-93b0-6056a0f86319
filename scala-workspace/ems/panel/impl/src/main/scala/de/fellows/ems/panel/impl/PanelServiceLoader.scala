// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.ems.panel.impl

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.panel.impl.entity.PanelSerializerRegistry
import de.fellows.ems.panel.impl.entity.customer.CustomerPanelEntity
import de.fellows.ems.panel.impl.entity.usage.WorkingPanelsUsageEntity
import de.fellows.ems.panel.impl.entity.working.WorkingPanelEntity
import de.fellows.ems.panel.impl.read.customer.{CustomerPanelEventProcessor, CustomerPanelRepository}
import de.fellows.ems.panel.impl.read.working.{WorkingPanelEventProcessor, WorkingPanelRepository}
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.renderer.api.RendererService
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class PanelServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new PanelServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new PanelServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[PanelService])
}

abstract class PanelServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with PanelServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)
  lazy val pcb                                   = serviceClient.implement[PCBService]
  lazy val assembly                              = serviceClient.implement[AssemblyService]
  lazy val renderer                              = serviceClient.implement[RendererService]

  implicit val customerPanelProcessor: CustomerPanelEventProcessor = wire[CustomerPanelEventProcessor]
  readSide.register(customerPanelProcessor)

  implicit val workingPaneProcessor: WorkingPanelEventProcessor = wire[WorkingPanelEventProcessor]
  readSide.register(workingPaneProcessor)

  lazy val files: PanelFileService = wire[PanelFileService]
    .withApp(this)

  lazy val impl: PanelServiceImpl = wire[PanelServiceImpl]
  override lazy val lagomServer = serverFor[PanelService](impl)
    .additionalRouter(files.router)

  val pcblistener      = wire[PCBListener]
  val assemblylistener = wire[AssemblyListener]
}
trait PanelServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("panel")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = PanelSerializerRegistry

  lazy val customerRep = wire[CustomerPanelRepository]
  lazy val workingRep  = wire[WorkingPanelRepository]

  persistentEntityRegistry.register(wire[CustomerPanelEntity])
  persistentEntityRegistry.register(wire[WorkingPanelEntity])
  persistentEntityRegistry.register(wire[WorkingPanelsUsageEntity])

}

//
