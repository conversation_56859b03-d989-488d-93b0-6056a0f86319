package de.fellows.ems.panel.impl

import de.fellows.ems.panel.api._
import de.fellows.ems.panel.impl.cookie.{GBlock, GBoard, GCustomerPanel, StatelessCookieCutter}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.FilePath
import org.apache.batik.svggen.SVGGraphics2D

import java.awt.Dimension
import java.awt.geom.{AffineTransform, Path2D, Rectangle2D}
import java.util.UUID

object CookieCutterTest extends App {

  val wp = WorkingPanel(Some(UUID.randomUUID()), "test", 460, 305, None, None, None)
  val emptySpacing = Spacing(
    None,
    None,
    None,
    None,
    None,
    None
  )

  val cp = CustomerPanel(
    id = Some(UUID.randomUUID()),
    externalId = None,
    sourcingScenario = None,
    name = "testpanel",
    elements = Seq(
      CustomerPanelBlock(
        elements = Seq(
          CustomerPanelBoard(
            assembly = UUID.randomUUID(),
            version = UUID.randomUUID(),
            x = 0,
            y = 0,
            angle = Some(10),
            spacing = emptySpacing,
            multiplier = Some(20)
          )
        ),
        areaInformation = None,
        x = 0,
        y = 0,
        angle = Some(10),
        spacing = emptySpacing
      )
    ),
    description = None,
    preview = None,
    spacing = emptySpacing,
    working = None,
    selected = None,
    bestYield = None,
    width = None,
    height = None
  )

  val boardOutline   = new Rectangle2D.Double(0, 0, 15, 123)
  val boardShapePath = new Path2D.Double()
  boardShapePath.moveTo(0, 0)
  boardShapePath.lineTo(15, 0)
  boardShapePath.lineTo(7, 123)
  boardShapePath.lineTo(0, 0)
  boardShapePath.closePath()

  private val shapeBounds: Rectangle2D = boardShapePath.getBounds2D
  val at                               = new AffineTransform()
  at.scale(1, -1)
  at.translate(0, -shapeBounds.getHeight)

  val boardShape = at.createTransformedShape(boardShapePath)

  //  val boardShape = new Rectangle2D.Double(0, 0, 15, 123)

  val gcp = GCustomerPanel(
    boardShape,
    boardOutline,
    Seq(
      GBlock(
        boardShape,
        boardOutline,
        Seq(
          GBoard(
            boardShape,
            boardOutline,
            20
          )
        )
      )
    )
  )

  StatelessCookieCutter.cut(
    wp,
    gcp,
    cp,
    (gr: SVGGraphics2D) => {
      val path = FilePath("/tmp/rotate/test/test.svg")
      gr.setSVGCanvasSize(new Dimension(1200, 1200))
      Renderer.writeSVG(gr, path)
      path
    }
  )
}
