// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.ems.panel.api

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.collaboration.TimelineEvent
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info

import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate PCB Panel API"
  )
)
abstract class PanelService extends StackrateServiceAPI with Service {

  val subPath  = "ems/panel"
  val basePath = s"/api/$subPath"

  val internalBasePath = s"/internal/$subPath/:team"

  @StackrateApi
  def getCustomerPanels(assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[CustomerPanelAPI]]

  def getSharedCustomerPanels(share: UUID): ServiceCall[NotUsed, Seq[CustomerPanelAPI]]

  def _getCustomerPanels(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Seq[CustomerPanel]]

  @StackrateApi
  def addCustomerPanel(
      assembly: UUID,
      version: UUID,
      autoselect: Option[Boolean] = Some(false)
  ): ServiceCall[CustomerPanelAPI, CustomerPanelAPI]

  def _setCustomerPanelFromLuminovo(
      team: String,
      pcb: UUID
  ): ServiceCall[LuminovoCustomerPanel, CustomerPanelAPI]

  def _deleteCustomerPanelByExternalId(
      team: String,
      pcb: UUID,
      externalid: String
  ): ServiceCall[NotUsed, Done]

  @StackrateApi
  def updateCustomerPanel(assembly: UUID, version: UUID, panel: String): ServiceCall[CustomerPanelAPI, CustomerPanelAPI]

  def _updateCustomerPanel(
      team: String,
      assembly: UUID,
      version: UUID,
      panel: String
  ): ServiceCall[CustomerPanelAPI, CustomerPanelAPI]

  def _reCutCustomerPanels(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Done]

  def _updateCustomerPanelWeight(
      team: String,
      assembly: UUID,
      version: UUID,
      panel: String
  ): ServiceCall[String, CustomerPanelAPI]

  @StackrateApi
  def getCustomerPanel(assembly: UUID, version: UUID, panel: String): ServiceCall[NotUsed, CustomerPanelAPI]

  def getSharedCustomerPanel(share: UUID, panel: String): ServiceCall[NotUsed, CustomerPanelAPI]

  def _getCustomerPanel(team: String, assembly: UUID, version: UUID, panel: String): ServiceCall[NotUsed, CustomerPanel]

  def _getCustomerPanelByID(team: String, panel: UUID): ServiceCall[NotUsed, CustomerPanel]

  @StackrateApi
  def deleteCustomerPanel(assembly: UUID, version: UUID, panel: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  def getWorkingPanels(): ServiceCall[NotUsed, Seq[WorkingPanel]]

  @StackrateApi
  def addWorkingPanel(): ServiceCall[WorkingPanel, WorkingPanel]

  @StackrateApi
  def addWorkingPanelToTeam(team: String): ServiceCall[WorkingPanel, WorkingPanel]

  @StackrateApi
  def updateWorkingPanel(panel: UUID): ServiceCall[WorkingPanel, WorkingPanel]

  @StackrateApi
  def deleteWorkingPanel(panel: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  def getWorkingPanel(panel: UUID): ServiceCall[NotUsed, WorkingPanel]

  def _getWorkingPanel(team: String, panel: UUID): ServiceCall[NotUsed, WorkingPanel]

  def _getWorkingPanels(team: String): ServiceCall[NotUsed, Seq[WorkingPanel]]

  def _getWorkingPanelUsagesForVersion(
      team: String,
      assembly: UUID,
      version: UUID
  ): ServiceCall[NotUsed, Seq[CustomerPanelUsageState]]
  def _getWorkingPanelUsagesForShare(team: String, share: UUID): ServiceCall[NotUsed, Seq[CustomerPanelUsageState]]

  def _getTransientWorkingPanelUsage(team: String): ServiceCall[CustomerPanelHelper, Seq[WorkingPanelUsage]]

  def _calculatePanelWeight(team: String, assembly: UUID, version: UUID): ServiceCall[NotUsed, Done]

  def customerPanelTimeline(): Topic[TimelineEvent]

  override def descriptor: Descriptor = {
    val CUSTOMER_PANELS        = "customerpanels"
    val SHARED_CUSTOMER_PANELS = "sharedcustomerpanels"
    val WORKING_PANELS         = "workingpanels"
    val PANEL_USAGE            = "workingpanelusages"

    import Service._
    withDocumentation(subPath)(
      named("panel")
        .withCalls(
          restCall(
            Method.GET,
            s"$basePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version",
            getCustomerPanels _
          ),
          restCall(
            Method.POST,
            s"$basePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version?autoselect",
            addCustomerPanel _
          ),
          restCall(
            Method.PUT,
            s"$basePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/panels/:panel",
            updateCustomerPanel _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/$CUSTOMER_PANELS/by-id/:pcbid/panels",
            _setCustomerPanelFromLuminovo _
          ),
          restCall(
            Method.DELETE,
            s"$internalBasePath/$CUSTOMER_PANELS/by-id/:pcbid/panels/by-external-id/:externalid",
            _deleteCustomerPanelByExternalId _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/panels/:panel",
            _updateCustomerPanel _
          ),
          restCall(
            Method.POST,
            s"$internalBasePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/recut",
            _reCutCustomerPanels _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/calculate_weight",
            _calculatePanelWeight _
          ),
          restCall(
            Method.POST,
            s"$internalBasePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/panels/:panel/weight",
            _updateCustomerPanelWeight _
          ),
          restCall(
            Method.GET,
            s"$basePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/panels/:panel",
            getCustomerPanel _
          ),
          restCall(
            Method.GET,
            s"$internalBasePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version",
            _getCustomerPanels _
          ),
          restCall(
            Method.GET,
            s"$internalBasePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/panels/:panel",
            _getCustomerPanel _
          ),
          restCall(Method.GET, s"$internalBasePath/$CUSTOMER_PANELS/panels/:panel", _getCustomerPanelByID _),
          restCall(
            Method.DELETE,
            s"$basePath/$CUSTOMER_PANELS/assemblies/:assembly/versions/:version/panels/:panel",
            deleteCustomerPanel _
          ),
          restCall(Method.GET, s"$basePath/$WORKING_PANELS", getWorkingPanels _),
          restCall(Method.POST, s"$basePath/$WORKING_PANELS", addWorkingPanel _),
          restCall(Method.POST, s"$basePath/teams/:team/$WORKING_PANELS", addWorkingPanelToTeam _),
          restCall(Method.PUT, s"$basePath/$WORKING_PANELS/:panel", updateWorkingPanel _),
          restCall(Method.GET, s"$basePath/$WORKING_PANELS/:panel", getWorkingPanel _),
          restCall(Method.DELETE, s"$basePath/$WORKING_PANELS/:panel", deleteWorkingPanel _),
          restCall(Method.GET, s"$internalBasePath/$WORKING_PANELS/:panel", _getWorkingPanel _),
          restCall(Method.GET, s"$internalBasePath/$WORKING_PANELS", _getWorkingPanels _),
          restCall(
            Method.GET,
            s"$internalBasePath/$PANEL_USAGE/assemblies/:assembly/versions/:version",
            _getWorkingPanelUsagesForVersion _
          ),
          restCall(
            Method.GET,
            s"$internalBasePath/$PANEL_USAGE/shares/:share",
            _getWorkingPanelUsagesForShare _
          ),
          restCall(
            Method.GET,
            s"$internalBasePath/$PANEL_USAGE/transient",
            _getTransientWorkingPanelUsage _
          ),

          // shared

          restCall(
            Method.GET,
            s"$basePath/$CUSTOMER_PANELS/shared/:share/panels/:panel",
            getSharedCustomerPanel _
          ),
          restCall(
            Method.GET,
            s"$basePath/$CUSTOMER_PANELS/shared/:share",
            getSharedCustomerPanels _
          )
        )
        .withTopics(
          topic(PanelService.TIMELINE, customerPanelTimeline())
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"/files/$subPath/.*")),
          ServiceAcl(pathRegex = Some(s"$basePath/.*")),
          ServiceAcl(pathRegex = Some(s"/_openapi/$subPath/.*"))
        )
    ).withExceptionSerializer(new ServiceExceptionSerializer())
  }

}

object PanelService {
  val V = s"v1.6"

  val TIMELINE = s"domain.panel.customer.timeline-$V"

  final val CUSTOMER_PANEL_LIFECYCLE = "customer-panel"
}
//
