package de.fellows.ems.panel.api

import scala.math.BigDecimal.RoundingMode

object Rectangle<PERSON><PERSON><PERSON><PERSON><PERSON> extends App {

  def count(cookieWidth: BigDecimal, cookieHeight: BigDecimal, plateWidth: BigDecimal, plateHeight: BigDecimal): Int = {
    val (norm: BigDecimal, rotated: BigDecimal) = getValues(cookieWidth, cookieHeight, plateWidth, plateHeight)

    (
      if (norm + rotated == 0) {
        BigDecimal(0)
      } else if (norm > rotated) {
        val restHeight = plateHeight % cookieHeight
        val restWidth  = plateWidth  % cookieWidth

        val rec1: Int =
          if (restHeight >= cookieHeight.min(cookieWidth)) {
            val v = getValues(cookieWidth, cookieHeight, plateWidth, restHeight)

            v._2.intValue // recurse once, rotated

          } else {
            0
          }

        val rec2: Int =
          if (restWidth >= cookieHeight.min(cookieWidth)) {
            val v = getValues(cookieWidth, cookieHeight, restWidth, plateHeight)
            v._2.intValue // recurse once, rotated
          } else {
            0
          }

        norm + rec1 + rec2
      } else {
        val restHeight = plateWidth  % cookieHeight
        val restWidth  = plateHeight % cookieWidth

        println(s"rest is $restWidth, $restHeight")

        val rec1: Int =
          if (restHeight >= cookieHeight.min(cookieWidth)) {
            val v = getValues(cookieWidth, cookieHeight, plateWidth, restHeight)
            v._1.intValue // recurse once, not rotated
          } else {
            0
          }

        val rec2: Int =
          if (restWidth >= cookieHeight.min(cookieWidth)) {
            val v = getValues(cookieWidth, cookieHeight, restWidth, plateHeight)
            v._1.intValue // recurse once, not rotated
          } else {
            0
          }

        rotated + rec1 + rec2
      }
    ).setScale(0, RoundingMode.FLOOR).intValue
  }

  private def getValues(
      cookieWidth: BigDecimal,
      cookieHeight: BigDecimal,
      plateWidth: BigDecimal,
      plateHeight: BigDecimal
  ) = {
    val normHeight = plateHeight / cookieHeight
    val normWidth  = plateWidth / cookieWidth

    val rotWidth  = plateWidth / cookieHeight
    val rotHeight = plateHeight / cookieWidth

    val norm =
      if (normHeight < 1 || normWidth < 1) {
        BigDecimal(0)
      } else {
        normHeight.setScale(0, RoundingMode.FLOOR) + ((normWidth.setScale(0, RoundingMode.FLOOR)) - 1)
      }
    val rotated =
      if (rotWidth < 1 || rotHeight < 1) {
        BigDecimal(0)
      } else {
        rotWidth.setScale(0, RoundingMode.FLOOR) + ((rotHeight.setScale(0, RoundingMode.FLOOR)) - 1)
      }

    println(
      s" fit ${cookieWidth}x${cookieHeight} on ${plateWidth}x${plateHeight} ->  norm: ${norm}, rotated: ${rotated}"
    )

    (norm, rotated)
  }

  println(count(2, 12, 427, 567))

}

//
// 150x100
// 80x2
// n1 = 50
// n2 =  1.875
// r1 = 75
// r2 = 1.25
