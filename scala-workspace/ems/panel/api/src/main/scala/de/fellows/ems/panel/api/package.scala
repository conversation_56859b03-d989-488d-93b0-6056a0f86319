package de.fellows.ems.panel

import de.fellows.app.assembly.commons.AbstractAssemblyReference
import de.fellows.luminovo.panel.{Depanelization, ExistingPanel, LuminovoPadding, PanelDetails, PanelId}
import de.fellows.utils.FilePath
import de.fellows.utils.UUIDUtils._
import de.fellows.utils.communication.ServiceDefinition
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json._

import java.util.UUID
import scala.util.Try

package object api {

  sealed trait PanelWrapper {}
  final case class Details(details: PanelDetails)    extends PanelWrapper
  final case class Existing(existing: ExistingPanel) extends PanelWrapper

  object Details {
    implicit val writes: Writes[Details] = Writes { details =>
      Json.obj(
        "type" -> "Details",
        "data" -> Json.toJson(details.details)
      )
    }
    implicit val reads: Reads[Details] = Reads { json =>
      json.validate[PanelDetails].map(Details(_))
    }

    implicit val format: Format[Details] = Format(reads, writes)
  }

  object Existing {
    implicit val writes: Writes[Existing] = Writes { existing =>
      Json.obj(
        "type" -> "Existing",
        "data" -> Json.toJson(existing.existing)
      )
    }
    implicit val reads: Reads[Existing] = Reads { json =>
      json.validate[ExistingPanel].map(Existing(_))
    }

    implicit val format: Format[Existing] = Format(reads, writes)
  }

  object PanelWrapper {
    implicit val reads: Reads[PanelWrapper] = Reads { json =>
      (json \ "type").as[String] match {
        case "Details"  => (json \ "data").validate[Details]
        case "Existing" => (json \ "data").validate[Existing]
        case jsType     => JsError(s"Unknown type: $jsType")
      }
    }
    implicit val writes: Writes[PanelWrapper] = Writes {
      case details: Details   => Json.toJson(details)(Details.writes)
      case existing: Existing => Json.toJson(existing)(Existing.writes)
    }

    implicit val format: Format[PanelWrapper] = Format(reads, writes)
  }

  case class LuminovoCustomerPanel(
      panel: PanelWrapper,
      sourcing_scenario: Option[UUID]
  ) {

    def convert(id: UUID, assembly: UUID, version: UUID): CustomerPanelAPI =
      panel match {
        case panel: Details =>
          convertPanel(id, assembly, version, panel)
        case existing: Existing =>
          convertExisting(id, assembly, version, existing)
      }

    def convertPanel(id: UUID, assembly: UUID, version: UUID, panel: Details) =
      CustomerPanelAPI(
        id = None,
        externalId = Some(id.toString),
        sourcingScenario = sourcing_scenario,
        name = s"luminovo panel ${id.short()}",
        description = None,
        spacing = Spacing(
          bottomPadding = Some(panel.details.padding.bottomInMm),
          topPadding = Some(panel.details.padding.topInMm),
          leftPadding = Some(panel.details.padding.leftInMm),
          rightPadding = Some(panel.details.padding.rightInMm)
        ),
        elements = Seq(
          CustomerPanelBlock(
            elements = Seq(
              CustomerPanelBoard(
                assembly = assembly,
                version = version,
                x = 0,
                y = 0,
                angle = Some(if (panel.details.pcbIsRotated) 90 else 0),
                spacing = Spacing(
                )
              )
            ),
            x = 0,
            y = 0,
            angle = Some(0),
            repeatX = Some(panel.details.columnCount),
            repeatY = Some(panel.details.rowCount),
            spacing = Spacing(
              horizontalSpacing = Some(panel.details.horizontalSpacingInMm),
              verticalSpacing = Some(panel.details.verticalSpacingInMm)
            )
          )
        ),
        preview = None
      )

    def convertExisting(id: UUID, assembly: UUID, version: UUID, existing: Existing) =
      CustomerPanelAPI(
        id = None,
        externalId = Some(id.toString),
        sourcingScenario = sourcing_scenario,
        name = s"luminovo panel ${id.short()}",
        description = None,
        spacing = Spacing(
          bottomPadding = None,
          topPadding = None,
          leftPadding = None,
          rightPadding = None
        ),
        elements = Seq(
          CustomerPanelBlock(
            elements = Seq(
              CustomerPanelBoard(
                multiplier = Some(existing.existing.numberOfPcbs),
                assembly = assembly,
                version = version,
                x = 0,
                y = 0,
                angle = Some(if (existing.existing.pcbIsRotated) 90 else 0),
                spacing = Spacing(),
                width = Some(existing.existing.panelWidth),
                height = Some(existing.existing.panelHeight)
              )
            ),
            x = 0,
            y = 0,
            angle = Some(0),
            repeatX = None,
            repeatY = None,
            spacing = Spacing(
              horizontalSpacing = None,
              verticalSpacing = None
            )
          )
        ),
        preview = None
      )
  }

  case class CustomerPanelAPI(
      id: Option[UUID],
      externalId: Option[String],
      sourcingScenario: Option[UUID],
      name: String,
      elements: Seq[CustomerPanelElement],
      description: Option[String],
      preview: Option[String],
      spacing: Spacing,
      working: Option[Seq[WorkingPanelUsageAPI]] = None,
      selected: Option[UUID] = None,
      bestYield: Option[UUID] = None,
      width: Option[BigDecimal] = None,
      height: Option[BigDecimal] = None,
      weight: Option[BigDecimal] = None
  ) {
    def toInternal: CustomerPanel =
      CustomerPanel(
        id = id,
        externalId = externalId,
        sourcingScenario = sourcingScenario,
        name = name,
        elements = elements,
        description = description,
        preview = None,
        spacing = spacing,
        working = working.map(_.map(_.toInternal())),
        selected = selected,
        bestYield = bestYield,
        width = width,
        height = height,
        weight = weight
      )

    def count(): Int =
      elements.map(_.count).sum

  }

  case class CustomerPanel(
      id: Option[UUID],
      externalId: Option[String],
      sourcingScenario: Option[UUID],
      name: String,
      elements: Seq[CustomerPanelElement],
      description: Option[String],
      preview: Option[FilePath],
      spacing: Spacing,
      working: Option[Seq[WorkingPanelUsage]] = None,
      selected: Option[UUID] = None,
      bestYield: Option[UUID] = None,
      width: Option[BigDecimal] = None,
      height: Option[BigDecimal] = None,
      weight: Option[BigDecimal] = None
  ) {
    def toApi(implicit sd: ServiceDefinition): CustomerPanelAPI =
      CustomerPanelAPI(
        id = id,
        externalId = externalId,
        sourcingScenario = sourcingScenario,
        name = name,
        elements = elements,
        description = description,
        preview = preview.map(_.toApi),
        spacing = spacing,
        working = working.map(_.map(_.toApi)),
        selected = selected,
        bestYield = bestYield,
        width = width,
        height = height,
        weight = weight
      )

    def count(): Int =
      elements.map(_.count).sum

  }

  case class WorkingPanelUsageState(
      workingPanel: UUID,
      customerPanels: Int,
      customerBoards: Int,
      panelYield: Double,
      preview: Option[FilePath]
  )

  case class CustomerPanelUsageState(
      customerPanel: UUID,
      selectedWorkingPanel: Option[UUID],
      workingPanels: Seq[WorkingPanelUsageState]
  ) {
    def getSelectedPanel: Option[WorkingPanelUsageState] =
      selectedWorkingPanel.flatMap(id => workingPanels.find(_.workingPanel == id))
  }

  object CustomerPanelUsageState {
    implicit val format: Format[CustomerPanelUsageState] = Json.format
  }

  object WorkingPanelUsageState {
    implicit val format: Format[WorkingPanelUsageState] = Json.format
  }
  case class CustomerPanelUsageApi(
      customerPanel: UUID,
      selectedWorkingPanel: Option[UUID],
      workingPanels: Seq[WorkingPanelUsageAPI]
  ) {
    def getSelectedPanel: Option[WorkingPanelUsageAPI] =
      selectedWorkingPanel.flatMap(id => workingPanels.find(_.workingPanel == id))

  }

  case class WorkingPanelUsageAPI(
      workingPanel: UUID,
      customerPanels: Int,
      customerBoards: Int,
      panelYield: Double,
      preview: Option[String]
  ) {
    def toInternal(): WorkingPanelUsage =
      WorkingPanelUsage(
        workingPanel = workingPanel,
        customerPanels = customerPanels,
        customerBoards = customerBoards,
        panelYield = panelYield,
        preview = None
      )
  }

  case class WorkingPanelUsage(
      workingPanel: UUID,
      customerPanels: Int,
      customerBoards: Int,
      panelYield: Double,
      preview: Option[FilePath]
  ) {
    def toApi(implicit sd: ServiceDefinition): WorkingPanelUsageAPI =
      WorkingPanelUsageAPI(
        workingPanel,
        customerPanels,
        customerBoards,
        panelYield,
        preview.map(_.toApi)
      )

    def countForCustomerBoards(requestedCustomerBoards: Int): BigDecimal =
      WorkingPanelUsage.countForCustomerBoards(requestedCustomerBoards, customerBoards)

    def countForCustomerPanels(requestedCustomerPanels: Int): BigDecimal =
      WorkingPanelUsage.countForCustomerPanels(requestedCustomerPanels, customerPanels)
  }

  sealed abstract class CustomerPanelElement(
      x: BigDecimal,
      y: BigDecimal,
      angle: Option[BigDecimal],
      spacing: Spacing,
      repeatX: Option[Int],
      repeatY: Option[Int]
  ) {
    def count: Int
  }

  case class CustomerPanelBoard(
      assembly: UUID,
      version: UUID, // TODO: add specification?
      x: BigDecimal,
      y: BigDecimal,
      angle: Option[BigDecimal],
      spacing: Spacing,
      repeatX: Option[Int] = None,
      repeatY: Option[Int] = None,
      multiplier: Option[Int] = None,
      width: Option[BigDecimal] = None,
      height: Option[BigDecimal] = None
  ) extends CustomerPanelElement(x, y, angle, spacing, repeatX, repeatY) {
    def count = repeatX.getOrElse(1) * repeatY.getOrElse(1) * multiplier.getOrElse(1)

  }

  case class CustomerPanelBlock(
      elements: Seq[CustomerPanelBoard],
      areaInformation: Option[AreaInformation] = None,
      x: BigDecimal,
      y: BigDecimal,
      angle: Option[BigDecimal],
      spacing: Spacing,
      repeatX: Option[Int] = None,
      repeatY: Option[Int] = None
  ) extends CustomerPanelElement(x, y, angle, spacing, repeatX, repeatY) {
    def count = repeatX.getOrElse(1) * repeatY.getOrElse(1) * elements.map(_.count).sum
  }

  case class Spacing(
      topPadding: Option[BigDecimal] = None,
      rightPadding: Option[BigDecimal] = None,
      leftPadding: Option[BigDecimal] = None,
      bottomPadding: Option[BigDecimal] = None,
      verticalSpacing: Option[BigDecimal] = None,
      horizontalSpacing: Option[BigDecimal] = None
  )

  case class AreaInformation(
      width: Option[BigDecimal] = None,
      height: Option[BigDecimal] = None,
      area: Option[BigDecimal] = None,
      panelYield: Option[BigDecimal] = None
  )

  case class WorkingPanelRequest(
      assemblyReference: AbstractAssemblyReference,
      forTeam: String
  )

  object WorkingPanelRequest {
    implicit val format: Format[WorkingPanelRequest] = Json.format[WorkingPanelRequest]
  }

  object Spacing {
    implicit val format: Format[Spacing] = Json.format[Spacing]
  }

  object AreaInformation {
    implicit val format: Format[AreaInformation] = Json.format[AreaInformation]
  }

  object CustomerPanelBoard {
    implicit lazy val format: Format[CustomerPanelBoard] = Json.format[CustomerPanelBoard]
  }

  object CustomerPanelBlock {
    implicit lazy val format: Format[CustomerPanelBlock] = Json.format[CustomerPanelBlock]
  }

  object CustomerPanelElement {
    def JsonSealedTraitNaming: JsonNaming =
      JsonNaming { fullName =>
        val names = fullName.split("\\.")
        names.last
      }

    implicit val cfg: JsonConfiguration.Aux[MacroOptions] =
      JsonConfiguration(
        discriminator = "_t",
        typeNaming = JsonSealedTraitNaming
      )

    implicit lazy val format: Format[CustomerPanelElement] = Json.format[CustomerPanelElement]
  }

  object WorkingPanelUsage {
    implicit val format: Format[WorkingPanelUsage] = Json.format[WorkingPanelUsage]

    def countForCustomerBoards(requestedCustomerBoards: Int, customerBoards: Int): BigDecimal =
      BigDecimal(requestedCustomerBoards) / BigDecimal(customerBoards)

    def countForCustomerPanels(requestedCustomerPanels: Int, customerPanels: Int): BigDecimal =
      BigDecimal(requestedCustomerPanels) / BigDecimal(customerPanels)
  }

  object WorkingPanelUsageAPI {
    implicit val format: Format[WorkingPanelUsageAPI] = Json.format[WorkingPanelUsageAPI]
  }
  object CustomerPanelUsageApi {
    implicit val format: Format[CustomerPanelUsageApi] = Json.format[CustomerPanelUsageApi]
  }

  object CustomerPanel {
    implicit val format: Format[CustomerPanel] = Json.format[CustomerPanel]
  }

  object LuminovoCustomerPanel {
    implicit val format: Format[LuminovoCustomerPanel] = Json.format[LuminovoCustomerPanel]

    def convert(
        panel: CustomerPanelAPI,
        millingDistanceMM: BigDecimal,
        depanelization: Depanelization
    ): (UUID, (PanelDetails, Int)) = {
      val padding                   = panel.spacing
      val block: CustomerPanelBlock = panel.elements.head.asInstanceOf[CustomerPanelBlock]
      val board: CustomerPanelBoard = block.elements.head.asInstanceOf[CustomerPanelBoard]
      val spacing                   = block.spacing
      val panelId: Option[PanelId]  = panel.externalId.flatMap(i => Try(PanelId(UUID.fromString(i))).toOption)
      val rx                        = block.repeatX
      val ry                        = block.repeatY

      val multiplier: Int  = board.multiplier.getOrElse(1)
      val stackratePanelId = panel.id.get
      stackratePanelId ->
        (PanelDetails(
          id = panelId,
          rowCount = ry.getOrElse(1),
          columnCount = rx.getOrElse(1),
          horizontalSpacingInMm = spacing.horizontalSpacing.getOrElse(0),
          verticalSpacingInMm = spacing.verticalSpacing.getOrElse(0),
          minMillingDistanceInMm = millingDistanceMM,
          padding = LuminovoPadding(
            topInMm = padding.topPadding.getOrElse(0),
            rightInMm = padding.rightPadding.getOrElse(0),
            bottomInMm = padding.bottomPadding.getOrElse(0),
            leftInMm = padding.leftPadding.getOrElse(0)
          ),
          depanelization = depanelization,
          pcbIsRotated = board.angle.getOrElse(0) == 90
        ) -> multiplier)
    }
  }
  object CustomerPanelAPI {
    implicit val format: Format[CustomerPanelAPI] = Json.format[CustomerPanelAPI]
  }

  case class WorkingPanel(
      id: Option[UUID],
      name: String,
      height: BigDecimal,
      width: BigDecimal,
      spacing: Option[BigDecimal],
      minLayerCount: Option[BigDecimal],
      maxLayerCount: Option[BigDecimal]
  )

  object WorkingPanel {
    implicit val format: Format[WorkingPanel] = Json.format[WorkingPanel]
  }

}
