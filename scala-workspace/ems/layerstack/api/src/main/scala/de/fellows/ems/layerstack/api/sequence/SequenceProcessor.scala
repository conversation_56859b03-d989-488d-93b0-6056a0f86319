package de.fellows.ems.layerstack.api.sequence

import scala.concurrent.{ ExecutionContext, Future }

trait SequenceProcessor[T <: SequenceRoot] {
  def process(d: T): T
}

trait ApiSequenceProcessor {
  def process(d: SequencedLayerstackDefinitionAPI): SequencedLayerstackDefinitionAPI
}

trait AsyncSequenceProcessor {
  def process(d: SequencedLayerstackDefinition)(implicit ec: ExecutionContext): Future[SequencedLayerstackDefinition]
}

trait AsyncApiSequenceProcessor {
  def process(d: SequencedLayerstackDefinitionAPI)(implicit
      ec: ExecutionContext
  ): Future[SequencedLayerstackDefinitionAPI]
}

class SequenceProcessingException(msg: String) extends Exception(msg)
