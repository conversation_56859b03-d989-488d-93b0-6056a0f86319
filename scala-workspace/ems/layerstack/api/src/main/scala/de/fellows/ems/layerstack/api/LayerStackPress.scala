package de.fellows.ems.layerstack.api

import de.fellows.ems.pcb.model.Density
import de.fellows.utils.meta._

import java.util.UUID
import scala.collection.mutable

object LayerStackPress {

  def thickness2(stack: Seq[LayerDefinition], layerMetas: Map[UUID, MetaInfo]): BigDecimal =
    stack.flatMap { l =>
      val base: Option[DecimalProperty] = l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.BaseThickness)
      val finished: Option[DecimalProperty] =
        l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.FinishedThickness)
      val maskTh: Option[DecimalProperty] = l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.MaskThickness)
      val inkTh: Option[DecimalProperty]  = l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.InkThickness)

      val mi                               = layerMetas.get(l.id.get)
      val pressed: Option[DecimalProperty] = mi.flatMap(_ \ LayerstackDFM.PRESSED_THICKNESS)

      val cuCount =
        (l.meta.getOrElse(MetaInfo()) \ MaterialProperties.CuCount).asInstanceOf[Option[DecimalProperty]].map(_.value)
      val cuthickness =
        if (cuCount.contains(1)) {
          l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.CuThickness).asInstanceOf[
            Option[DecimalProperty]
          ].map(_.value)
        } else if (cuCount.contains(2)) {
          val ut: Option[DecimalProperty] = l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.UpperCuThickness)
          val lt: Option[DecimalProperty] = l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.LowerCuThickness)
          val allt: Option[DecimalProperty] = l.material.flatMap(_.meta).flatMap(_ \ MaterialProperties.CuThickness)

          if (ut.isDefined && lt.isDefined) {
            Some(Seq(ut.map(_.value), lt.map(_.value)).flatten.sum)
          } else if (allt.isDefined) {
            Some((allt.get.value * 2))
          } else {
            Some(BigDecimal(0.0))
          }
        } else {
          Some(BigDecimal(0.0))
        }

      pressed.orElse(base).orElse(finished).orElse(maskTh).orElse(inkTh).map(_.value).map(_ + cuthickness.getOrElse(
        BigDecimal(0)
      ))
    }.sum

  def thickness(stack: SubStackDefinition, layerMetas: Map[UUID, Map[String, Property]]): BigDecimal =
    thickness2(stack.layers.getOrElse(Seq()), layerMetas.map(x => x._1 -> MetaInfo(x._2)))

  def thickness(stack: SubStack, layerMetas: Map[UUID, Map[String, Property]]): BigDecimal =
    thickness2(stack.layers.map(_.definition), layerMetas.map(x => x._1 -> MetaInfo(x._2)))

  /** @param densities
    *   map from layerid and file index to its density
    * @return
    *   map from id of LayerDefinition to properties, valid under the given densities
    */
  def press(densities: Map[(UUID, Int), Density])(stack: SubStackDefinition): Map[UUID, Seq[(String, Property)]] = {
    val layerProps: mutable.Map[UUID, Seq[(String, Property)]] = mutable.Map()
    val pressIns                                               = Seq.newBuilder[(UUID, Double)]

    val paddedStack = null +: stack.layers.getOrElse(Seq()) :+ null

    def doLayer(ls: Seq[LayerDefinition], l: LayerDefinition) =
      if (l.layerType.exists(Seq(MaterialTypes.CORE, MaterialTypes.FOIL, MaterialTypes.FLEXCORE).contains)) {
        // middle layer is a layer with some copper

        val material   = l.material.get
        val matMeta    = material.meta.getOrElse(MetaInfo())
        val frontLayer = ls(0)
        val backLayer  = ls(2)

        if (material.copperCount() == 0) {
          // ???
        } else if (material.copperCount() == 1) {
          // single foil
          val thickness: Option[DecimalProperty] = (matMeta \ MaterialProperties.CuThickness)
          val density                            = densities.get((l.id.get, 0))
          //          val density = densities.map(x => (x._1.id -> x._2)).get(l.files.get.head.id)

          if (thickness.isEmpty || density.isEmpty) {
            throw new RuntimeException("thickness or density is missing")
          }

          val pressing = thickness.map(_.value.doubleValue).get * (1.0 - density.get.portionOfCandidateIsCopper / 100.0)

          if (isCompressable(frontLayer) && isCompressable(backLayer)) {
            val p = pressing / 2
            pressIns += frontLayer.id.get -> p
            pressIns += backLayer.id.get  -> p
            // press both
          } else {
            if (isCompressable(frontLayer)) {
              pressIns += frontLayer.id.get -> pressing
            } else if (isCompressable(backLayer)) {
              pressIns += backLayer.id.get -> pressing
            }
          }

        } else if (material.copperCount() > 1) {
          // double foil with material in between
          println(s"check core : ${l}")
          val upperThickness: Option[DecimalProperty] =
            (matMeta \ MaterialProperties.UpperCuThickness).orElse(matMeta \ MaterialProperties.CuThickness)
          val lowerThickness: Option[DecimalProperty] =
            (matMeta \ MaterialProperties.LowerCuThickness).orElse(matMeta \ MaterialProperties.CuThickness)
          val upperDensity = densities.get((l.id.get, 0))
          val lowerDensity = densities.get((l.id.get, 1))
          println(s"ut : ${upperThickness}, lt: $lowerThickness, uD: $upperDensity, lD: $lowerDensity")

          if (upperThickness.nonEmpty && upperDensity.nonEmpty) {
            val upperPressing =
              upperThickness.map(_.value.doubleValue).get * ((1 - upperDensity.get.portionOfCandidateIsCopper / 100))
            if (isCompressable(frontLayer)) {
              pressIns += frontLayer.id.get -> upperPressing
            }
          }

          if (lowerThickness.nonEmpty && lowerDensity.nonEmpty) {
            val lowerPressing =
              lowerThickness.map(_.value.doubleValue).get * (1 - lowerDensity.get.portionOfCandidateIsCopper / 100)
            if (isCompressable(backLayer)) {
              pressIns += backLayer.id.get -> lowerPressing
            }
          }
        }
      }

    paddedStack.sliding(3, 1).foreach { ls =>
      val l = ls(1) // middle layer

      try
        doLayer(ls, l)
      catch {
        case e: Throwable => e.printStackTrace()
      }
    }

    val pi = pressIns.result()
    stack.layers.getOrElse(Seq()).foreach { l =>
      if (l.material.flatMap(_.materialType).contains(MaterialTypes.PREPREG)) {
        val matMeta = l.material.get.meta.getOrElse(MetaInfo())

        val s: Seq[Property] = Seq(
          matMeta \ MaterialProperties.BaseThickness,
          matMeta \ MaterialProperties.FinishedThickness
        ).flatten

        val thickness: Option[DecimalProperty] =
          s.filter(x => x.isInstanceOf[DecimalProperty]).map(_.asInstanceOf[DecimalProperty]).headOption

        if (thickness.isEmpty) {
          throw new RuntimeException("Thickness is missing")
        }

        val press = pi.filter(_._1 == l.id.get).map(_._2).sum
        val th    = thickness.map(_.value)

        val finThickness     = th.map(_.doubleValue).get - press
        val originalThicknes = th.get.doubleValue
        val resin            = (originalThicknes - finThickness) / originalThicknes * 100

        layerProps.put(
          l.id.get,
          layerProps.getOrElse(l.id.get, Seq()) ++ Seq(
            DecimalProperty.e(LayerstackDFM.PRESSED_THICKNESS, finThickness),
            DecimalProperty.e(LayerstackDFM.REQUIRED_RESIN, resin)
          )
        )
      }
    }

    layerProps.toMap
  }

  def isCompressable(l: LayerDefinition): Boolean =
    l != null && l.material.get.materialType.contains(MaterialTypes.PREPREG)
}
