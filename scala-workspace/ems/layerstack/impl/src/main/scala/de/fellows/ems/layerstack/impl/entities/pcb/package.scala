package de.fellows.ems.layerstack.impl.entities

import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.ems.layerstack.api.{LayerStack, LayerStacks}
import de.fellows.utils.collaboration.{TimelineCommand, TimelineEvent}
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object pcb {

  sealed trait LayerStackCommand

  sealed trait LayerStackEvent extends AggregateEvent[LayerStackEvent] {
    override def aggregateTag = LayerStackEvent.Tag
  }

  object LayerStackEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[LayerStackEvent](NumShards)
  }

  case class SetLayerStacks(team: String, version: UUID, stack: LayerStacks, user: Boolean, tcmd: TimelineCommand)
      extends LayerStackCommand with ReplyType[LayerStacks]

  case class SetLayerStackMeta(team: String, version: UUID, stack: LayerStack, tcmd: TimelineCommand)
      extends LayerStackCommand with ReplyType[LayerStack]

  case class GetLayerStacks(team: String, version: UUID) extends LayerStackCommand with ReplyType[LayerStacks]

  case class LayerStackSet(stack: LayerStacks, user: Boolean = false) extends LayerStackEvent

  case class LayerStackChanged(stack: LayerStack) extends LayerStackEvent

  case class LayerStackTimelineChanged(stack: TimelineEvent) extends LayerStackEvent

  object SetLayerStacks {
    implicit val format: Format[SetLayerStacks] = Json.format[SetLayerStacks]
  }

  object SetLayerStackMeta {
    implicit val format: Format[SetLayerStackMeta] = Json.format[SetLayerStackMeta]
  }

  object GetLayerStacks {
    implicit val format: Format[GetLayerStacks] = Json.format[GetLayerStacks]
  }

  object LayerStackSet {
    implicit val format: Format[LayerStackSet] = Json.using[Json.WithDefaultValues].format[LayerStackSet]
  }

  object LayerStackChanged {
    implicit val format: Format[LayerStackChanged] = Json.using[Json.WithDefaultValues].format[LayerStackChanged]
  }

  object LayerStackTimelineChanged {
    implicit val format: Format[LayerStackTimelineChanged] =
      Json.using[Json.WithDefaultValues].format[LayerStackTimelineChanged]
  }

}
