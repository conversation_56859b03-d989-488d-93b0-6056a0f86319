package de.fellows.ems.layerstack.impl.utils.convert.ipc2581

object LayerFunctionType extends Enumeration {
  val ASSEMBLY            = Value
  val BOARDFAB            = Value
  val BOARD_OUTLINE       = Value
  val CAPACITIVE          = Value
  val COATINGCOND         = Value
  val COATINGNONCOND      = Value
  val COMPONENT           = Value
  val COMPONENT_BOTTOM    = Value
  val COMPONENT_TOP       = Value
  val CONDFILM            = Value
  val CONDFOIL            = Value
  val CONDUCTIVE_ADHESIVE = Value
  val CONDUCTOR           = Value
  val COURTYARD           = Value
  val DIELBASE            = Value
  val DIELCORE            = Value
  val DIELPREG            = Value
  val DIELADHV            = Value
  val DIELBONDPLY         = Value
  val DIELCOVERLAY        = Value
  val DOCUMENT            = Value
  val DRILL               = Value
  val FIXTURE             = Value
  val GLUE                = Value
  val GRAPHIC             = Value
  val HOLEFILL            = Value
  val SOLDERBUMP          = Value
  val PASTEMASK           = Value
  val EMBEDDED_COMPONENT  = Value
  val LANDPATTERN         = Value
  val LEGEND              = Value
  val MIXED               = Value
  val OTHER               = Value
  val PIN                 = Value
  val PLANE               = Value
  val PROBE               = Value
  val RESISTIVE           = Value
  val SIGNAL              = Value
  val SILKSCREEN          = Value
  val SOLDERMASK          = Value
  val SOLDERPASTE         = Value
  val STACKUP_COMPOSITE   = Value
  val REWORK              = Value
  val ROUT                = Value
  val V_CUT               = Value
  val EDGE_CHAMFER        = Value
  val THIEVING_KEEP_INOUT = Value
}
