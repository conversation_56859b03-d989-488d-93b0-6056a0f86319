package de.fellows.ems.layerstack.impl.utils.convert.ipc2581

import de.fellows.ems.layerstack.api.LayerStackProperties

sealed trait BoardTechnologyType {
  val ipcName: String
  val stackrateName: String

}

case object RIGID extends BoardTechnologyType {
  override val ipcName: String       = "RIGID"
  override val stackrateName: String = LayerStackProperties.RIGID
}

case object RIGID_FLEX extends BoardTechnologyType {
  override val ipcName: String       = "RIGID_FLEX"
  override val stackrateName: String = LayerStackProperties.RIGIDFLEX
}

case object FLEX extends BoardTechnologyType {
  override val ipcName: String       = "FLEX"
  override val stackrateName: String = LayerStackProperties.FLEX
}

case object HDI extends BoardTechnologyType {
  override val ipcName: String       = "HDI"
  override val stackrateName: String = LayerStackProperties.HDI
}

case object EMBEDDED_COMPONENT extends BoardTechnologyType {
  override val ipcName: String       = "EMBEDDED_COMPONENT"
  override val stackrateName: String = LayerStackProperties.EMBEDDED_COMPONENT
}

case object OTHER extends BoardTechnologyType {
  override val ipcName: String       = "OTHER"
  override val stackrateName: String = LayerStackProperties.UNKNOWN
}
