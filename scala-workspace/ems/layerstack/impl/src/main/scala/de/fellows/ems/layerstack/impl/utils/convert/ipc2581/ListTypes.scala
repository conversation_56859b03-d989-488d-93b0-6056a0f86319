package de.fellows.ems.layerstack.impl.utils.convert.ipc2581

import de.fellows.ems.layerstack.api.MaterialProperties

sealed trait ListType extends Enumeration

object ListType {
  val LIST_MAP = Map[String, String](
    DielectricListType.DIELECTRIC_CONSTANT.toString -> MaterialProperties.DielectricConstant,
    DielectricListType.LOSS_TANGENT.toString        -> MaterialProperties.LossTangent,
    DielectricListType.RESIN_CONTENT.toString       -> MaterialProperties.ResinContent
  )
}

object DielectricListType extends ListType {
  val DIELECTRIC_CONSTANT = Value // (MaterialProperties.DielectricConstant)
  val LOSS_TANGENT        = Value // (MaterialProperties.LossTangent)
  val GLASS_TYPE          = Value
  val GLASS_STYLE         = Value
  val RESIN_CONTENT       = Value // (MaterialProperties.ResinContent)
  val PROCESSABILITY_TEMP = Value
  val OTHER               = Value
}

object ConductorListType extends ListType {
  val CONDUCTIVITY                 = Value
  val SURFACE_ROUGHNESS_UPFACING   = Value
  val SURFACE_ROUGHNESS_DOWNFACING = Value
  val SURFACE_ROUGHNESS_TREATED    = Value
  val ETCH_FACTOR                  = Value
  val FINISHED_HEIGHT              = Value
  val OTHER                        = Value
}

object ComplianceListType extends ListType {
  val ROHS              = Value
  val CONFLICT_MINERALS = Value
  val WEEE              = Value
  val REACH             = Value
  val HALOGEN_FREE      = Value
  val OTHER             = Value
}

object TechnologyListType extends ListType {
  val RIGID              = Value
  val RIGID_FLEX         = Value
  val FLEX               = Value
  val HDI                = Value
  val EMBEDDED_COMPONENT = Value
  val OTHER              = Value
}

object GeneralListType extends ListType {
  val ELECTRICAL    = Value
  val THERMAL       = Value
  val MATERIAL      = Value
  val INSTRUCTION   = Value
  val STANDARD      = Value
  val CONFIGURATION = Value
  val OTHER         = Value
}

object TemperatureListType extends ListType {
  val THERMAL_DELAMINATION = Value
  val EXPANSION_Z_AXIS     = Value
  val EXPANSION_X_Y_AXIS   = Value
  val OTHER                = Value
}

object VCutListType extends ListType {
  val ANGLE               = Value
  val THICKNESS_REMAINING = Value
  val OFFSET              = Value
  val OTHER               = Value
}

object ToolListType extends ListType {
  val CARBIDE   = Value
  val ROUTER    = Value
  val LASER     = Value
  val FLATNOSE  = Value
  val EXTENSION = Value
  val V_CUTTER  = Value
}

object ImpedanceListType extends ListType {
  val IMPEDANCE               = Value
  val LINEWIDTH               = Value
  val SPACING                 = Value
  val REF_PLANE_LAYER_ID      = Value
  val COPLANAR_GROUND_SPACING = Value
  val OTHER                   = Value
}

object ThievingListType extends ListType {
  val KEEP_IN  = Value
  val KEEP_OUT = Value
}

object EdgeChamferListType extends ListType {
  val ANGLE = Value
  val WIDTH = Value
  val SIDE  = Value
}
