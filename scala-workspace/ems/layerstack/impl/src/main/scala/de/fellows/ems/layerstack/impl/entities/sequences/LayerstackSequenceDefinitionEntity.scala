package de.fellows.ems.layerstack.impl.entities.sequences

import akka.Done
import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.ems.layerstack.api.sequence.{ LayerStackNode, MaterialSequence, SequencedLayerstackDefinition }
import de.fellows.utils.collaboration._
import de.fellows.utils.common.EntityException
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity
import de.fellows.utils.meta.MetaInfo

import java.util.UUID

class UpdaterContext[CONTENT](var found: Boolean = false, var illegal: Boolean = false, var c: Option[CONTENT] = None)

class LayerstackSequenceDefinitionEntity(implicit override val service: ServiceDefinition)
    extends SecureTeamEntity[Option[SequencedLayerstackDefinition]] {
  override type Command = SequencedLayerstackDefinitionCommand
  override type Event   = SequencedLayerstackDefinitionEvent

  override def initialState: State = None

  override def entityBehavior(state: Option[SequencedLayerstackDefinition]): Actions = (state match {
    case Some(value) => existing(value)
    case None        => missing
  })
    .onEvent {
      case (x: LayerstackDefinitionMetaChanged, s) => s.map { st =>
          updateMeta(new UpdaterContext[MetaInfo](), st, x.sequence, x.meta)
        }
      case (x: LayerstackDefinitionTimelineEvent, s) => s
      case (x: LayerstackDefinitionChanged, s)       => Some(x.definition)
      case (x: LayerstackDefinitionCreated, s)       => Some(x.definition)
      case (x: LayerstackDefinitionDeleted, s)       => None
      case (x: LayerstackDefinitionMaterialChanged, s) =>
        s.map { st =>
          updateMaterial(new UpdaterContext[Any](), st, x.sequence, x.material)
        }
    }

  private val TIMELINE_ID = "sequencedlayerstackdefinition"

  def timelineRef(s: SequencedLayerstackDefinition): TimelineReference =
    TimelineReference(
      entity = s.id.toString,
      entityType = TIMELINE_ID
    )

  private def timelineEvent(
      evt: AnyRef,
      x: TimelineCommand,
      ls: SequencedLayerstackDefinition
  ): LayerstackDefinitionTimelineEvent = {
    val reference = timelineRef(ls)
    timelineEvent(ls.team, evt, x, reference)
  }

  private def timelineEvent(
      team: String,
      evt: AnyRef,
      x: TimelineCommand,
      reference: TimelineReference
  ): LayerstackDefinitionTimelineEvent =
    LayerstackDefinitionTimelineEvent(TimelineEvent.of(
      team,
      x,
      reference,
      PredefinedTimelineChange(
        changeCategory = TIMELINE_ID,
        changeType = evt.getClass.getSimpleName,
        TimelineUtils.summaryId(TIMELINE_ID, evt),
        TimelineUtils.descriptionId(TIMELINE_ID, evt),
        params = Map()
      )
    ))

  def existing(definition: SequencedLayerstackDefinition): Actions =
    Actions()
      .onReadOnlyCommand[GetLayerstackSequenceDefinition, SequencedLayerstackResponse] {
        case (command: GetLayerstackSequenceDefinition, ctx, state) =>
          ctx.reply(SequencedLayerstackResponse(Some(definition)))
      }
      .onCommand[SetLayerstackSequenceDefinition, SequencedLayerstackDefinition] {
        case (command: SetLayerstackSequenceDefinition, ctx, state) =>
          val evt = LayerstackDefinitionChanged(command.definition, definition.name, definition.metaInfo)

          val oldMeta = definition.metaInfo
          val newMeta = command.definition.metaInfo

          val evts = Seq(
            evt,
            timelineEvent(evt, command.timeline, command.definition)
          ) ++ (
            if (!oldMeta.equals(newMeta)) {
              Seq(
                LayerstackDefinitionMetaChanged(definition.team, definition.id, None, newMeta, oldMeta)
              )
            } else {
              Seq()
            }
          )

          ctx.thenPersistAll(
            evts: _*
          )(() => ctx.reply(command.definition))
      }
      .onCommand[DeleteLayerstackDefinition, Done] {
        case (command: DeleteLayerstackDefinition, ctx, state) =>
          val evt = LayerstackDefinitionDeleted(definition.team, definition.name, definition.metaInfo, definition.id)
          ctx.thenPersistAll(
            evt,
            timelineEvent(evt, command.timeline, definition)
          )(() => ctx.reply(Done))
      }
      .onCommand[SetSequenceMaterial, SequencedLayerstackDefinition] {
        case (command: SetSequenceMaterial, ctx, state) =>
          val uctx   = new UpdaterContext[Any]()
          val update = updateMaterial(uctx, definition, command.sequence, command.materialID)

          if (uctx.found) {
            if (uctx.illegal) {
              ctx.commandFailed(EntityException(
                TransportErrorCode.BadRequest,
                "Specified Sequence is not a material layer"
              ))
              ctx.done
            } else {
              val evt = LayerstackDefinitionMaterialChanged(definition.id, command.sequence, command.materialID)
              ctx.thenPersistAll(
                evt,
                timelineEvent(evt, command.timeline, definition)
              )(() => ctx.reply(update))
            }
          } else {
            ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Sequence Not Found"))
            ctx.done
          }
      }
      .onCommand[SetSequenceMeta, SequencedLayerstackDefinition] {
        case (command: SetSequenceMeta, ctx, state) =>
          val uctx   = new UpdaterContext[MetaInfo]()
          val update = updateMeta(uctx, definition, Some(command.sequence), command.properties)

          if (uctx.found) {
            val evt = LayerstackDefinitionMetaChanged(
              definition.team,
              definition.id,
              Some(command.sequence),
              uctx.c.get,
              definition.metaInfo
            )
            ctx.thenPersistAll(
              evt,
              timelineEvent(evt, command.timeline, definition)
            )(() => ctx.reply(update))
          } else {
            ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Sequence Not Found"))
            ctx.done
          }
      }

  def missing: Actions =
    Actions()
      .onReadOnlyCommand[SetSequenceMeta, SequencedLayerstackDefinition] {
        case (_, ctx, _) => ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Definition Not Found"))
      }
      .onReadOnlyCommand[SetSequenceMaterial, SequencedLayerstackDefinition] {
        case (_, ctx, _) => ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Definition Not Found"))
      }
      .onReadOnlyCommand[DeleteLayerstackDefinition, Done] {
        case (_, ctx, _) => ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Definition Not Found"))
      }
      .onCommand[SetLayerstackSequenceDefinition, SequencedLayerstackDefinition] {
        case (command: SetLayerstackSequenceDefinition, ctx, state) =>
          val evt = LayerstackDefinitionCreated(command.definition)
          ctx.thenPersistAll(
            evt,
            timelineEvent(evt, command.timeline, command.definition)
          )(() => ctx.reply(command.definition))
      }
      .onReadOnlyCommand[GetLayerstackSequenceDefinition, SequencedLayerstackResponse] {
        case (command: GetLayerstackSequenceDefinition, ctx, state) =>
          ctx.reply(SequencedLayerstackResponse(None))
      }

  private def updateMaterial(
      c: UpdaterContext[Any],
      definition: SequencedLayerstackDefinition,
      sequence: UUID,
      material: String
  ) =
    definition.transform { s =>
      if (s.internalId.get == sequence) {
        s match {
          case value: MaterialSequence =>
            c.found = true
            c.illegal = false
            Some(value.copy(material = material))
          case value =>
            c.found = true
            c.illegal = true
            Some(value)
        }
      } else {
        None
      }
    }

  private def updateMeta(
      c: UpdaterContext[MetaInfo],
      definition: SequencedLayerstackDefinition,
      sequence: Option[UUID],
      meta: MetaInfo
  ) =
    if (sequence.isDefined) {
      definition.transform { s =>
        if (s.internalId.get == sequence.get) {
          val updated = s match {
            case value: MaterialSequence =>
              value.copy(meta = value.meta.map(_ ++ meta))
            case value: LayerStackNode =>
              value.copy(meta = value.meta.map(_ ++ meta))
          }
          c.found = true
          c.c = updated.meta
          Some(updated)
        } else {
          None
        }
      }
    } else {
      definition.copy(metaInfo = definition.metaInfo ++ meta)
    }

  override def isAllowed(a: SequencedLayerstackDefinitionCommand, s: Option[SequencedLayerstackDefinition]): Boolean =
    a match {
      case SetLayerstackSequenceDefinition(definition, _) =>
        s.map(_.team).forall(_ == definition.team) && s.map(_.id).forall(_ == definition.id)
      case DeleteLayerstackDefinition(team, definition, _) =>
        s.map(_.id).contains(definition) && s.map(_.team).contains(team)
      case SetSequenceMeta(team, definition, sequence, properties, timeline) =>
        s.map(_.id).contains(definition) && s.map(_.team).contains(team)
      case SetSequenceMaterial(team, definition, sequence, materialID, timeline) =>
        s.map(_.id).contains(definition) && s.map(_.team).contains(team)
      case GetLayerstackSequenceDefinition(team, id) =>
        s.map(_.id).contains(id) && s.map(_.team).contains(team)

      case _ => false
    }
}
