package de.fellows.ems.layerstack.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.ems.layerstack.api.Material
import de.fellows.utils.entities.CollaborativeEventInfo
import play.api.libs.json.{Format, Json}

package object material {

  sealed trait MaterialCommand

  sealed trait MaterialEvent extends AggregateEvent[MaterialEvent] {
    override def aggregateTag = MaterialEvent.Tag
  }

  object MaterialEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[MaterialEvent](NumShards)
  }

  case class CreateMaterial(mat: Material, info: CollaborativeEventInfo) extends MaterialCommand
      with ReplyType[Material]

  case class SetMaterial(mat: Material, info: CollaborativeEventInfo) extends MaterialCommand with ReplyType[Material]

  case class GetMaterial(mat: String) extends MaterialCommand with ReplyType[Material]

  case class DeleteMaterial(id: String, info: CollaborativeEventInfo) extends MaterialCommand with ReplyType[Done]

  case class MaterialCreated(mat: Material, info: CollaborativeEventInfo) extends MaterialEvent

  case class MaterialChanged(updated: Material, old: Material, info: CollaborativeEventInfo) extends MaterialEvent

  case class MaterialDeleted(deleted: Material, info: CollaborativeEventInfo) extends MaterialEvent

  object CreateMaterial {
    implicit val format: Format[CreateMaterial] = Json.format[CreateMaterial]
  }

  object SetMaterial {
    implicit val format: Format[SetMaterial] = Json.format[SetMaterial]
  }

  object GetMaterial {
    implicit val format: Format[GetMaterial] = Json.format[GetMaterial]
  }

  object DeleteMaterial {
    implicit val format: Format[DeleteMaterial] = Json.format[DeleteMaterial]
  }

  object MaterialCreated {
    implicit val format: Format[MaterialCreated] = Json.format[MaterialCreated]
  }

  object MaterialChanged {
    implicit val format: Format[MaterialChanged] = Json.format[MaterialChanged]
  }

  object MaterialDeleted {
    implicit val format: Format[MaterialDeleted] = Json.format[MaterialDeleted]
  }

}
