package de.fellows.ems.layerstack.impl.entities.material

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.ems.layerstack.api.Material

class MaterialEntity extends PersistentEntity {
  override type Command = MaterialCommand
  override type Event   = MaterialEvent
  override type State   = Option[Material]

  override def initialState: State = None

  override def behavior: Behavior = {
    case Some(s) => existing(s)
    case None    => missing
  }

  def existing(material: Material): Actions =
    Actions()
      .onReadOnlyCommand[CreateMaterial, Material] {
        case (_, ctx, _) => ctx.invalidCommand("Material exists")
      }
      .onReadOnlyCommand[GetMaterial, Material] {
        case (_, ctx, _) => ctx.reply(material.copy(meta = material.meta.map(_.toLowerCase)))
      }
      .onCommand[SetMaterial, Material] {
        case (x: SetMaterial, ctx, _) =>
          val updated = material.copy(
            name = x.mat.name.orElse(material.name),
            materialType = x.mat.materialType.orElse(material.materialType),
            meta = material.meta.map(_.copy(
              properties =
                material.meta.map(_.properties).getOrElse(Map()) ++ x.mat.meta.map(_.properties).getOrElse(Map())
            )).map(_.toLowerCase)
          )
          ctx.thenPersist(
            MaterialChanged(
              updated = updated,
              old = material,
              info = x.info
            )
          )(_ => ctx.reply(updated))
      }
      .onCommand[DeleteMaterial, Done] {
        case (x: DeleteMaterial, ctx, _) =>
          ctx.thenPersist(
            MaterialDeleted(material, x.info)
          )(_ => ctx.reply(Done))
      }
      .onEvent {
        case (x: MaterialChanged, _) => Some(x.updated)
        case (x: MaterialDeleted, _) => None
      }

  def missing(): Actions =
    Actions()
      .onReadOnlyCommand[GetMaterial, Material] {
        case (_, ctx, _) => ctx.invalidCommand("Material does not exist")
      }
      .onReadOnlyCommand[SetMaterial, Material] {
        case (_, ctx, _) => ctx.invalidCommand("Material does not exist")
      }
      .onReadOnlyCommand[DeleteMaterial, Done] {
        case (_, ctx, _) => ctx.invalidCommand("Material does not exist")
      }
      .onCommand[CreateMaterial, Material] {
        case (x: CreateMaterial, ctx, _) =>
          if (x.mat.team.isEmpty || x.mat.id.isEmpty) {
            ctx.invalidCommand("Creation needs team and id")
            ctx.done
          } else {
            ctx.thenPersist(
              MaterialCreated(x.mat.copy(meta = x.mat.meta.map(_.toLowerCase)), x.info)
            )(_ => ctx.reply(x.mat))
          }
      }
      .onEvent {
        case (x: MaterialCreated, _) => Some(x.mat)
      }
}
