package de.fellows.ems.layerstack.impl.read.layerstack

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import de.fellows.ems.layerstack.api
import de.fellows.ems.layerstack.api.{LayerStack, LayerstackDefinitionDescription}
import de.fellows.ems.layerstack.impl.entities.definition.{
  LayerstackDefinitionChanged,
  LayerstackDefinitionCreated,
  LayerstackDefinitionDeleted,
  LayerstackDefinitionEvent,
  LayerstackDefinitionMetaChanged
}
import de.fellows.ems.layerstack.impl.read.material.MaterialRepository
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta.MetaInfo
import play.api.Logging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

case class LayerstackDefinitionDescriptionDTO(
    team: String,
    id: UUID,
    name: String,
    copperCount: Int,
    stackType: String,
    metaInfo: MetaInfo,
    image: Option[FilePath],
    price: api.LayerstackPrices
) {
  def toApi(implicit sd: ServiceDefinition): LayerstackDefinitionDescription =
    LayerstackDefinitionDescription(team, id, name, copperCount, stackType, metaInfo, image.map(_.toApi), price)
}

class LayerstackMetaRepo(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends StackrateLogging {
  def getMetaInfoForID(team: String, id: UUID): Future[Option[LayerstackDefinitionDescriptionDTO]] =
    session.selectOne("SELECT * FROM layerstackMeta WHERE team = ? AND id = ?", team, id).map(_.map(mapRow))

  def getMetaInfoForTeam(team: String): Future[Seq[LayerstackDefinitionDescriptionDTO]] =
    session.selectAll("SELECT * FROM layerstackMeta WHERE team = ?", team).map(_.map(mapRow))

  private def mapRow(x: Row) =
    LayerstackDefinitionDescriptionDTO(
      x.getString("team"),
      x.getUUID("id"),
      x.getString("name"),
      x.getInt("copperCount"),
      x.getString("stacktype"),
      x.get("meta", classOf[MetaInfo]),
      Option(x.get("image", classOf[FilePath])),
      api.LayerstackPrices(
        Option(x.getDecimal("unitprice")),
        Option(x.getDecimal("areaprice"))
      )
    )
}
class LayerstackMetaProcessor(session: CassandraSession, readSide: CassandraReadSide, materials: MaterialRepository)(
    implicit ec: ExecutionContext
) extends ReadSideProcessor[LayerstackDefinitionEvent] with Logging {
  PCBCodecHelper.registerPCBCodecs(session)
  var updateType: PreparedStatement = _
  var deleteType: PreparedStatement = _

  def createTables(): Future[Done] =
    for {
      _ <- PCBCodecHelper.loadTypes(session)
      _ <- session.executeCreateTable(
        s"""
           | CREATE TABLE IF NOT EXISTS layerstackMeta (
           |   team text,
           |   id uuid,
           |   name text,
           |   copperCount int,
           |   stacktype text,
           |   image frozen<filepath>,
           |   meta frozen<metainfotype>,
           |   areaprice decimal,
           |   unitprice decimal,
           |   PRIMARY KEY(team, id)
           | )
           |""".stripMargin
      )

    } yield Done

  def prepareStatements(): Future[Done] =
    for {
      _ <- PCBCodecHelper.registerPCBCodecs(session)
      updateType <- session.prepare(
        s"UPDATE layerstackMeta SET meta =:meta, name =:name, copperCount =:copperCount, stackType =:stackType, image =:image, areaprice =:areaprice, unitprice =:unitprice WHERE team = :team AND id=:id"
      )
      deleteType <- session.prepare(s"DELETE FROM layerstackMeta WHERE team = :team AND id=:id")
    } yield {
      this.updateType = updateType
      this.deleteType = deleteType
      Done
    }

  override def aggregateTags: Set[AggregateEventTag[LayerstackDefinitionEvent]] = LayerstackDefinitionEvent.Tag.allTags

  def getPrice(ls: api.LayerstackDefinition): Future[api.LayerstackPrices] =
    Future.sequence(ls.stacks.getOrElse(Seq()).flatMap(ss => ss.layers.getOrElse(Seq()))
      .flatMap(_.materialRef)
      .map { ref =>
        materials.getMaterial(ls.team.get, ref)
      })
      .map(_.flatten)
      .map(LayerStack.sumPrices)

  def addLayerstack(event: LayerstackDefinitionCreated): Future[Seq[BoundStatement]] = {
    val copperCount = LayerstackEventProcessor.countCopper(event.ls)

    getPrice(event.ls).flatMap { price =>
      updateMeta(
        event.ls.team.get,
        event.ls.id,
        event.ls.name,
        copperCount,
        event.ls.stacks.toSeq.flatMap(_.flatMap(_.stackType)).headOption,
        event.ls.metaInfo,
        event.ls.image,
        price
      )
    }

  }

  private def updateMeta(
      team: String,
      id: UUID,
      name: String,
      copperCount: Int,
      stackType: Option[String],
      metaInfo: MetaInfo,
      image: Option[FilePath],
      price: api.LayerstackPrices
  ) = {
    val bindUpdateType = updateType.bind()
    bindUpdateType.setString("team", team)
    bindUpdateType.setUUID("id", id)
    bindUpdateType.setString("name", name)
    bindUpdateType.setString("stackType", stackType.getOrElse(""))
    bindUpdateType.setInt("copperCount", copperCount)
    bindUpdateType.set("meta", metaInfo, classOf[MetaInfo])
    bindUpdateType.set("image", image.orNull, classOf[FilePath])
    bindUpdateType.setDecimal("areaprice", price.areaPrice.map(_.bigDecimal).orNull)
    bindUpdateType.setDecimal("unitprice", price.unitPrice.map(_.bigDecimal).orNull)
    Future.successful(Seq(bindUpdateType))
  }

  def removeLayerstack(event: LayerstackDefinitionDeleted): Future[Seq[BoundStatement]] = {
    val bindDeleteType = deleteType.bind()
    bindDeleteType.setString("team", event.team)
    bindDeleteType.setUUID("id", event.id)
    Future.successful(Seq(bindDeleteType))
  }

  def changeLayerstack(event: LayerstackDefinitionChanged): Future[Seq[BoundStatement]] = {
    val copperCount = LayerstackEventProcessor.countCopper(event.updated)
    getPrice(event.updated).flatMap { price =>
      updateMeta(
        event.updated.team.get,
        event.updated.id,
        event.updated.name,
        copperCount,
        event.updated.stacks.toSeq.flatMap(_.flatMap(_.stackType)).headOption,
        event.updated.metaInfo,
        event.updated.image,
        price
      )
    }
  }

  def changeLayerstackMeta(event: LayerstackDefinitionMetaChanged): Future[Seq[BoundStatement]] = {
    val copperCount = LayerstackEventProcessor.countCopper(event.ls)
    getPrice(event.ls).flatMap { price =>
      updateMeta(
        event.ls.team.get,
        event.ls.id,
        event.ls.name,
        copperCount,
        event.ls.stacks.toSeq.flatMap(_.flatMap(_.stackType)).headOption,
        event.meta,
        event.ls.image,
        price
      )
    }
  }

  override def buildHandler(): ReadSideHandler[LayerstackDefinitionEvent] =
    readSide.builder[LayerstackDefinitionEvent]("layerstackmetarepo-v1.4")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[LayerstackDefinitionCreated](e => addLayerstack(e.event))
      .setEventHandler[LayerstackDefinitionDeleted](e => removeLayerstack(e.event))
      .setEventHandler[LayerstackDefinitionChanged](e => changeLayerstack(e.event))
      .setEventHandler[LayerstackDefinitionMetaChanged](e => changeLayerstackMeta(e.event))
      .build()

}
