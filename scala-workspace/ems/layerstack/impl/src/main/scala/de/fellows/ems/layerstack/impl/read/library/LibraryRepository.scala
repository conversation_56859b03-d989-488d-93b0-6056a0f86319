package de.fellows.ems.layerstack.impl.read.library

import akka.Done
import akka.stream.Materializer
import akka.stream.scaladsl.Sink
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row, SimpleStatement}
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.ems.layerstack.api.Library
import de.fellows.ems.layerstack.impl.entities.library.{LibraryDeleted, LibraryEvent, LibrarySet}
import de.fellows.ems.layerstack.impl.read.material.MaterialRepository
import de.fellows.ems.layerstack.impl.utils.RequestFilter
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.UUIDUtils
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta._

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class LibraryRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  PCBCodecHelper.registerPCBCodecs(session)

  def getLibraryId(team: String, name: String): Future[Option[UUID]] =
    UUIDUtils.fromString(name) match {
      case Some(x) => Future.successful(Some(x))
      case None =>
        session.selectOne("SELECT * FROM libraries WHERE team = ? AND name = ?", team, name).map(_.map(_.getUUID("id")))
    }

  def toLib(r: Row): Library =
    Library(
      team = Option(r.getString("team")),
      name = r.getString("name"),
      id = Option(r.getUUID("id")),
      supplier = Option(r.getString("supplier")),
      libraryType = Option(r.getString("libraryType")),
      materials = None
    )

  def getLibraryTypes(team: String): Future[Map[String, Int]] =
    session.selectAll("SELECT librarytype FROM libraries WHERE team = ? ", team).map(
      _.map(_.getString("libraryType")).groupBy(identity).map { (x: (String, Seq[String])) =>
        (Option(x._1).getOrElse(""), x._2.length)
      }
    )

  def countLibrary(team: String): Future[BigDecimal] =
    session.selectOne("SELECT COUNT(id) AS c FROM libraries WHERE team = ? ", team).map(_.map { x =>
      val bd = x.getLong("c")
      BigDecimal(bd)
    }.getOrElse(BigDecimal(0)))

  def getMaterialTypes(team: String): Future[Seq[(String, Int)]] =
    session.selectAll("SELECT materialtype FROM materialsbyid WHERE team = ? ", team).map(
      _.map(_.getString("materialtype")).groupBy(identity).map { (x: (String, Seq[String])) =>
        (Option(x._1).getOrElse(""), x._2.length)
      }.toSeq
    )

  def countMaterials(team: String, `type`: Option[String]): Future[BigDecimal] =
    `type` match {
      case None =>
        session.selectOne("SELECT COUNT(id) AS c FROM materialsbyid WHERE team = ? ", team).map(_.map { x =>
          val bd = x.getLong("c")
          BigDecimal(bd)
        }.getOrElse(BigDecimal(0)))

      case Some(t) => session.selectOne(
          "SELECT COUNT(id) AS c FROM propertymaterialtype WHERE team = ? AND indexvalue = ?",
          team,
          t.toLowerCase
        ).map(_.map { x =>
          val bd = x.getLong("c")
          BigDecimal(bd)
        }.getOrElse(BigDecimal(0)))
    }

  def getLibrarySuppliers(team: String): Future[Seq[(String, Int)]] =
    session.selectAll("SELECT supplier FROM libraries WHERE team = ? ", team).map(
      _.map(_.getString("supplier")).groupBy(identity).map { (x: (String, Seq[String])) =>
        (Option(x._1).getOrElse(""), x._2.length)
      }.toSeq
    )

  def getMaterialSuppliers(team: String): Future[Seq[(String, Int)]] =
    session.selectAll("SELECT indexvalue FROM propertysupplier WHERE team = ? ", team).map(
      _.map(_.getString("indexvalue")).groupBy(identity).map { (x: (String, Seq[String])) =>
        (Option(x._1).getOrElse(""), x._2.length)
      }.toSeq
    )

  /** retrieves the libraries, with *no* material information.
    *
    * the material info is not indexed by this read side, and has to be retrieved elsewhere.
    *
    * @return
    */
  def getLibrariesWithoutMaterials(
      team: String,
      page: Int,
      pagesize: Int,
      filter: Library => Boolean
  ): Future[Seq[Library]] =
    getLibrariesWithoutMaterials[Library](team, page, pagesize, toLib, filter).runWith(Sink.seq)

  def getLibraryIDs(
      team: String,
      page: Int,
      pagesize: Int,
      filter: Option[Library => Boolean]
  ): Future[Seq[UUID]] =
    getLibrariesWithoutMaterials[Library](team, page, pagesize, toLib, filter.getOrElse(_ => true)).map(
      _.id.get
    ).runWith(Sink.seq)

  private def getLibrariesWithoutMaterials[T](
      team: String,
      page: Int,
      pagesize: Int,
      convert: (Row => T),
      filter: (T => Boolean)
  ) = {

    val statement = new SimpleStatement(s"SELECT * FROM libraries WHERE team = ?", team)
    statement.setFetchSize(pagesize)

    val source = session.select(
      statement
    )

    source.map(convert).filter(filter).drop(page * pagesize).take(pagesize)
  }
}

object LibraryRepository {
  def createFilter(filter: Option[String]): Library => Boolean = {
    val f = filter.map(RequestFilter.apply)
    val libFilter: Library => Boolean = library =>
      f match {
        case Some(filter) =>
          filter.elements.find(_._1.toLowerCase == "type").forall(e =>
            filter.allows(e, library.libraryType.map(StringProperty("type", _)))
          ) &&
          filter.elements.find(_._1.toLowerCase == "supplier").forall(e =>
            filter.allows(e, library.supplier.map(StringProperty("supplier", _)))
          ) &&
          filter.elements.find(_._1.toLowerCase == "name").forall(e =>
            filter.allows(e, StringProperty("name", library.name))
          )
        case None => true
      }
    libFilter
  }
}

private[impl] class LibraryEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[LibraryEvent] with StackrateLogging {

  PCBCodecHelper.registerPCBCodecs(session)

  var setLibraryStmt: PreparedStatement    = _
  var deleteLibraryStmt: PreparedStatement = _

  // language=SQL
  def createTables(): Future[Done] =
    for {
      _ <- PCBCodecHelper.loadTypes(session)

      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS libraries (
          |   team text,
          |   name text,
          |   libraryType text,
          |   supplier text,
          |   id uuid,
          |   PRIMARY KEY(team, name)
          | )
          |""".stripMargin
      )

    } yield Done

  def prepareStatements(): Future[Done] =
    // language=SQL
    for {
      setLibraryByID <- session
        .prepare(
          "UPDATE libraries SET id =:id, libraryType= :libraryType, supplier = :supplier WHERE team = :team AND name = :name"
        )
      deleteLibraryByID <- session
        .prepare("DELETE FROM libraries WHERE team = :team AND name = :name")
    } yield {
      PCBCodecHelper.registerPCBCodecs(session)

      this.setLibraryStmt = setLibraryByID
      this.deleteLibraryStmt = deleteLibraryByID
      Done
    }

  def setLibrary(e: LibrarySet): Future[Seq[BoundStatement]] = {

    val oldName = e.old.map(_.name)
    Future.successful(
      (if (oldName.isDefined && oldName.get != e.lib.name) {
         List(deleteLibraryStmt.bind()
           .setString("team", e.lib.team.orNull)
           .setString("name", oldName.get))
       } else {
         List()
       }) ++
        List(
          setLibraryStmt.bind()
            .setUUID("id", e.lib.id.orNull)
            .setString("libraryType", e.lib.libraryType.map(_.toLowerCase).orNull)
            .setString("supplier", e.lib.supplier.orNull)
            .setString("team", e.lib.team.orNull)
            .setString("name", e.lib.name)
        )
    )
  }

  def deleteLibrary(e: LibraryDeleted): Future[Seq[BoundStatement]] = {
    logger.info(s"library deleted ${e.lib.id}")
    Future.successful(List(
      deleteLibraryStmt.bind()
        .setString("team", e.lib.team.orNull)
        .setString("name", e.lib.name)
    ))
  }

  override def buildHandler(): ReadSideHandler[LibraryEvent] = readSide.builder[LibraryEvent]("libraryrepo-v1.1")
    .setGlobalPrepare(createTables)
    .setPrepare(_ => prepareStatements())
    .setEventHandler[LibrarySet](e => setLibrary(e.event))
    .setEventHandler[LibraryDeleted](e => deleteLibrary(e.event))
    .build()

  override def aggregateTags: Set[AggregateEventTag[LibraryEvent]] = LibraryEvent.Tag.allTags
}
