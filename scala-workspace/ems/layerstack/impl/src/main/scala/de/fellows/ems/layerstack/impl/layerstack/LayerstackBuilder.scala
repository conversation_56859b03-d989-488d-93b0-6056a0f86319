package de.fellows.ems.layerstack.impl.layerstack

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assemby.api.Assembly
import de.fellows.ems.layerstack.api.sequence.{
  AsyncSequenceProcessor,
  InternalAllocateUUIDs,
  LayerStackNode,
  LayerstackSequence,
  MaterialSequence,
  SequenceFiles,
  SequencedLayerstack,
  SequencedLayerstackDefinition
}
import de.fellows.ems.layerstack.api.{ MaterialProperties, MaterialTypes }
import de.fellows.ems.layerstack.impl.entities.material.{ GetMaterial, MaterialEntity }
import de.fellows.ems.pcb.model.{ GerberFile, LayerConstants, PCBVersion }
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta.{ DecimalProperty, MetaInfo }

import scala.concurrent.{ ExecutionContext, Future }

/** Creates a layerstack using a given layerstack definition for use in a specific pcb
  *
  * @param definition
  * @param pcb
  * @param assembly
  * @param ec
  */
class LayerstackBuilder(
    definition: SequencedLayerstackDefinition,
    pcb: PCBVersion,
    assembly: Assembly,
    ereg: PersistentEntityRegistry
)(implicit ec: ExecutionContext) {

  def build(): Future[SequencedLayerstack] =
    for {
      // allocate new UUIDs
      pdef <- Future.successful(new InternalAllocateUUIDs().process(definition))

      // copy the materials metadata
      pdef <- new MaterialMetaResolver(ereg).process(pdef)

      // create the file-to-sequence links
      files <- LayerstackBuilder.matchFiles(definition.iterator(), ereg, pcb.files)
    } yield SequencedLayerstack(
      team = pdef.team,
      id = pdef.id,
      name = pdef.name,
      topSequence = pdef.topSequence,
      children = pdef.children,
      sequenceLinks = pdef.sequenceLinks,
      metaInfo = pdef.metaInfo,
      files = files,
      preview = None,
      definition = Some(definition.id)
    )

}

object LayerstackBuilder extends StackrateLogging {
  def combineMeta(a: Option[MetaInfo], b: Option[MetaInfo]): Option[MetaInfo] =
    (a, b) match {
      case (None, None) => None
      case _            => Some(a.getOrElse(MetaInfo()) ++ b.getOrElse(MetaInfo()))
    }

  def resolveMaterial(x: MaterialSequence, ereg: PersistentEntityRegistry)(implicit ec: ExecutionContext) =
    ereg.refFor[MaterialEntity](x.material)
      .ask(GetMaterial(x.material))
      .map { resolvedMaterial =>
        Some(x.copy(
          meta = combineMeta(x.meta, resolvedMaterial.meta)
        ))
      }
      .recover(t => None)

  def matchFiles(
      definition: Iterator[LayerstackSequence],
      ereg: PersistentEntityRegistry,
      pcbfiles: Seq[GerberFile]
  )(implicit ec: ExecutionContext): Future[Seq[SequenceFiles]] = {

    def getCopperCount(m: MaterialSequence) =
      (m.meta.getOrElse(MetaInfo()).get[DecimalProperty](MaterialProperties.CuCount).map(_.value.intValue) match {
        case Some(value) => Future.successful(value)
        case None => LayerstackBuilder.resolveMaterial(m, ereg).map(_.flatMap(
            _.meta.getOrElse(MetaInfo()).get[DecimalProperty](MaterialProperties.CuCount).map(_.value.intValue)
          ))
            .map(_.getOrElse(1))
      })

    def copper(m: MaterialSequence, count: Int, files: Seq[GerberFile]): (SequenceFiles, Seq[GerberFile]) = {
      val (use, rest) = files.splitAt(count)
      (SequenceFiles(m.internalId.get, use.map(_.id)), rest)
    }

    Future.sequence(
      definition.flatMap {
        case leaf: MaterialSequence => Some(leaf)
        case _                      => None
      }.map(ms => getCopperCount(ms).map(i => (ms, i)))
    ).map { files =>
      var copperfiles = pcbfiles.filter(_.fType.fileType == LayerConstants.COPPER_TOP) ++
        pcbfiles.filter(x =>
          x.fType.fileType == LayerConstants.COPPER_MID || x.fType.fileType == LayerConstants.PLANE_MID
        ).sortBy(_.fType.index) ++
        pcbfiles.filter(_.fType.fileType == LayerConstants.COPPER_BOTTOM)

      files.flatMap { entry =>
        (entry._1.layerType match {
          case MaterialTypes.FOIL     => Some(copper(entry._1, entry._2, copperfiles))
          case MaterialTypes.CORE     => Some(copper(entry._1, entry._2, copperfiles))
          case MaterialTypes.FLEXCORE => Some(copper(entry._1, entry._2, copperfiles))

          case MaterialTypes.RCC       => None
          case MaterialTypes.PREPREG   => None
          case MaterialTypes.IDENT     => None
          case MaterialTypes.PEELABLE  => None
          case MaterialTypes.COVERLAY  => None
          case MaterialTypes.BONDPLY   => None
          case MaterialTypes.ADHESIVE  => None
          case MaterialTypes.COMPONENT => None
          case x =>
            logger.warn(s"unknown material type ${x}")
            None
        }).map { x =>
          copperfiles = x._2
          x._1
        }
      }.toSeq
    }
  }
}

/** Adds Material Meta info to a layerstackdefinition
  */
class MaterialMetaResolver(ereg: PersistentEntityRegistry) extends AsyncSequenceProcessor {
  override def process(d: SequencedLayerstackDefinition)(implicit
      ec: ExecutionContext
  ): Future[SequencedLayerstackDefinition] =
    for {
      ts <-
        d.topSequence.asyncTransform {
          case x: MaterialSequence => LayerstackBuilder.resolveMaterial(x, ereg).map(_.getOrElse(x))
          case x                   => Future.successful(x)
        }
      ch <-
        Future.sequence(d.children.map(_.asyncTransform {
          case x: MaterialSequence =>
            LayerstackBuilder.resolveMaterial(x, ereg)
          case x: LayerStackNode => Future.successful(None)
        }))
    } yield d.copy(
      topSequence = ts,
      children = ch
    )
}

//
