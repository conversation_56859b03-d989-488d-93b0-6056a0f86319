package de.fellows.ems.layerstack.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventTag}
import de.fellows.ems.layerstack.api.Library
import de.fellows.utils.entities.CollaborativeEventInfo
import play.api.libs.json.{Format, Json}

import java.util.UUID

package object library {

  sealed trait LibraryCommand

  sealed trait LibraryEvent extends AggregateEvent[LibraryEvent] {
    override def aggregateTag = LibraryEvent.Tag
  }

  object LibraryEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[LibraryEvent](NumShards)
  }

  case class SetLibrary(lib: Library, info: CollaborativeEventInfo) extends LibraryCommand with ReplyType[Library]

  case class DeleteLibrary(id: UUID, info: CollaborativeEventInfo) extends LibraryCommand with ReplyType[Done]

  case class SetMaterials(id: UUID, materials: Seq[String], info: CollaborativeEventInfo) extends LibraryCommand
      with ReplyType[Library]

  case class GetLibrary(id: UUID) extends LibraryCommand with ReplyType[Library]

  case class LibrarySet(lib: Library, old: Option[Library], info: CollaborativeEventInfo) extends LibraryEvent

  case class LibraryDeleted(lib: Library, info: CollaborativeEventInfo) extends LibraryEvent

  object SetLibrary {
    implicit val format: Format[SetLibrary] = Json.format[SetLibrary]
  }

  object DeleteLibrary {
    implicit val format: Format[DeleteLibrary] = Json.format[DeleteLibrary]
  }

  object SetMaterials {
    implicit val format: Format[SetMaterials] = Json.format[SetMaterials]
  }

  object GetLibrary {
    implicit val format: Format[GetLibrary] = Json.format[GetLibrary]
  }

  object LibrarySet {
    implicit val format: Format[LibrarySet] = Json.format[LibrarySet]
  }

  object LibraryDeleted {
    implicit val format: Format[LibraryDeleted] = Json.format[LibraryDeleted]
  }

}
