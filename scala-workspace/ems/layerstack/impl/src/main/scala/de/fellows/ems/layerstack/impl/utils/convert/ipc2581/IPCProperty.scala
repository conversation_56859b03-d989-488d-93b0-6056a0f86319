package de.fellows.ems.layerstack.impl.utils.convert.ipc2581

import scala.xml.Node

case class IPCProperty(
    name: Option[String],
    value: Option[String],
    text: Option[String],
    unit: Option[String],
    tolPlus: Option[String],
    tolMinus: Option[String],
    tolPercent: Option[String],
    refUnit: Option[String],
    refValue: Option[String],
    refText: Option[String],
    layerOrGroupRef: Option[String],
    comment: Option[String]
) {}

object IPCProperty {
  def apply(n: Node): IPCProperty =
    new IPCProperty(
      n.attribute("name").map(_.text),
      n.attribute("value").map(_.text),
      n.attribute("text").map(_.text),
      n.attribute("unit").map(_.text),
      n.attribute("tolPlus").map(_.text),
      n.attribute("tolMinus").map(_.text),
      n.attribute("tolPercent").map(_.text),
      n.attribute("refUnit").map(_.text),
      n.attribute("refValue").map(_.text),
      n.attribute("refText").map(_.text),
      n.attribute("layerOrGroupRef").map(_.text),
      n.attribute("comment").map(_.text)
    )
}
