include "main-application.conf"

play.application.loader = de.fellows.ems.layerstack.impl.LayerstackServiceLoader


layerstack.cassandra.keyspace = ${?fellows.persistence.rootKeyspace}layerstack


cassandra-journal {
  keyspace = ${layerstack.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${layerstack.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${layerstack.cassandra.keyspace}read
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "layerstack"
# fellows.serviceconfig = ${fellows.services.layerstack}

akka {
  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # Log level for the very basic logger activated during ActorSystem startup.
  # This logger prints the log messages to stdout (System.out).
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  stdout-loglevel = "DEBUG"
}

fellows.storage {
  service = ${fellows.storage.base}/layerstack
}

stackrate.layerstack.standard {
  layerstacks = [
    "LS-1 1.0 35 TG-135",
    "LS-2 1.0 35 TG-135",
    "LS-2 1.6 35 TG-135",
    "LS-4 1.2 35_18 TG-135",
    "LS-4 1.6 35_18 TG-135",
    "LS-4 2.0 35_18 TG-135",
    "LS-6 1.0 35_18 TG-135",
    "LS-6 1.55 35_18 TG-135",
    "LS-8 1.1 35_18 TG-135",
    "LS-8 1.55 35_18 TG-135",
    "LS-8 2.0 35_18 TG-135",
    "LS-8 2.2 35_18 TG-135",
    "LS-10 1.55 35_18 TG-135",
    "LS-10 2.0 35_18 TG-135",
    "LS-12 2.0 35_18 TG-135",
    "LS-14 2.2 35_18 TG-135",
    "LS-16 2.2 35_18 TG-135",
    "LS-18 2.4 35_18 TG-135",
    "LS-20 2.8 35_18 TG-135",
    "LS-22 2.2 35_18 TG-135",
    "LS-24 2.2 35_18 TG-135",
    "LS-26 2.2 35_18 TG-135",
    "LS-28 2.2 35_18 TG-135",
    "LS-30 2.2 35_18 TG-135",
    "LS-32 2.2 35_18 TG-135",
  ]

  excludedteams: [
    "epn",
    "pragoboard",
    "princitec",
    "schweizer",
    "richter",
    "present",
    "bosch"
  ]
}


