package de.fellows.ems.layerstack.impl

import de.fellows.ems.layerstack.impl.utils.convert.ipc2581.IPC2581Reader
import de.fellows.utils.UUIDUtils
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec

import java.io.FileInputStream
import java.nio.file.Paths

class StandardLibSpec extends AsyncWordSpec with BeforeAndAfterAll with Matchers {

  "Standard Layerstacks" should {
    "import correctly" in {
      val ls    = Paths.get("./ems/layerstack/impl/src/main/resources/standard/layerstacks/LS-1 1.0 35 TG-135.xml")
      val lsdef = new IPC2581Reader(ls.getFileName.toString).convert(new FileInputStream(ls.toFile))

      val layerIds = lsdef.stacks.map(_.flatMap(_.layers.map(_.flatMap(_.id)))).getOrElse(Seq()).flatten

      layerIds should be(Seq(
        UUIDUtils.ofShort("xdAJLfK5QOWut8pSz59ccw"),
        UUIDUtils.ofShort("lNg75ieTRZSrjcpRcbqprQ"),
        UUIDUtils.ofShort("HDBTaTTGQ0aPWaiZvd0ozA"),
        UUIDUtils.ofShort("aLgxPhwnQiSMqYW53eKUJQ"),
        UUIDUtils.ofShort("ZLftja6gRfWq4kYXFU-riQ"),
        UUIDUtils.ofShort("D2bba6fsQs-pVMXnfHvnxg"),
        UUIDUtils.ofShort("KsJ7mo2NSZqyhXCuiVDMuQ")
      ))

      lsdef.id should be(UUIDUtils.ofShort("noBnAP38T3Kgm8r1KCB97w"))
    }
    "import all" in {
      val files = Paths.get("./ems/layerstack/impl/src/main/resources/standard/layerstacks")
        .toFile.listFiles().map(_.toPath)

      val ipcs = files.map(p => new IPC2581Reader(p.getFileName.toString).convert(p))

      ipcs.map(_.id) should not contain null
    }

  }

}
