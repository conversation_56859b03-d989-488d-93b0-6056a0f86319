package de.fellows.ems.dfm

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.supplier.Capability
import de.fellows.ems.pcb.model.graphics.GPoint
import de.fellows.ems.renderer.api.{DistanceDescription, FileReference}
import de.fellows.utils.meta._
import play.api.libs.json.{Format, Json}

package object api {

  case class ViolationState(status: String)

  case class ViolationStates(states: Map[String, String])

  case class Violations(
      assembly: AssemblyReference,
      violations: Seq[Violation],
      elements: Seq[ViolationElement],
      inProgress: Boolean = false
  ) {
    def toApi(): ViolationsAPI =
      ViolationsAPI(
        assembly,
        violations.flatMap(x =>
          elements.find(_.id == x.element) match {
            case Some(e) => Some(x.toApi(e))
            case None    => None
          }
        ),
        violations.map(_.occurences).sum
      )

    def isEmpty(): Boolean =
      violations.isEmpty

    def ++(v: Violations): Violations = {
      val (elements1, vios1) = (this.elements, this.violations)
      val (elements2, vios2) = (v.elements, v.violations)

      val distinct = elements1.filter(e1 => elements2.exists(_.id == e1.id)).distinct ++
        elements2.filter(e2 => elements1.exists(_.id == e2.id)).distinct

      if (distinct.size > 2) {
        throw new IllegalStateException(s"merging different elements with same id: ${distinct.map(_.id)}")
      } else {
        Violations(
          this.assembly,
          this.violations ++ v.violations,
          this.elements ++ v.elements,
          this.inProgress
        )
      }
    }
  }

  case class ViolationsAPI(assembly: AssemblyReference, violations: Seq[ViolationAPI], count: Int)

  case class ViolationElement(
      id: String,
      status: String,
      propertyName: String,
      distance: Option[DistanceDescription],
      points: Option[Seq[GPoint]],
      size: Option[Double],
      scaling: Option[BigDecimal],
      file: Option[FileReference],
      element: Option[String],
      trace: Option[String],
      property: Property
  )

  case class Violation(
      technology: String,
      id: String,
      severity: Int,
      status: String,
      element: String,
      capability: Capability,
      occurences: Int = 1
  ) {
    def toApi(element: ViolationElement): ViolationAPI =
      ViolationAPI(
        technology,
        id,
        severity,
        status,
        element,
        capability,
        occurences
      )
  }

  case class ViolationAPI(
      technology: String,
      id: String,
      severity: Int,
      status: String,
      element: ViolationElement,
      capability: Capability,
      occurences: Int
  )

  case class Metrics(count: Int, technologies: Seq[TechnologyMetrics])

  case class TechnologyMetrics(
      technology: String,
      count: Int,
      severities: Seq[SeverityMetrics],
      properties: Seq[PropertyMetrics],
      statusMetrics: Seq[StatusMetrics]
  )

  case class SeverityMetrics(severity: Int, count: Int)

  case class PropertyMetrics(property: String, count: Int)

  case class StatusMetrics(status: String, count: Int)

  object SeverityMetrics {
    implicit val f: Format[SeverityMetrics] = Json.format[SeverityMetrics]
  }

  object PropertyMetrics {
    implicit val f: Format[PropertyMetrics] = Json.format[PropertyMetrics]
  }

  object StatusMetrics {
    implicit val f: Format[StatusMetrics] = Json.format[StatusMetrics]
  }

  object TechnologyMetrics {
    implicit val f: Format[TechnologyMetrics] = Json.format[TechnologyMetrics]
  }

  object Metrics {
    implicit val f: Format[Metrics] = Json.format[Metrics]
  }

  object Violation {
    val STATUS_ACTIVE  = "active"
    val STATUS_IGNORED = "ignored"

    val STATES = Seq(STATUS_ACTIVE, STATUS_IGNORED)

    implicit val f: Format[Violation] = Json.using[Json.WithDefaultValues].format[Violation]
  }

  object ViolationElement {
    implicit val f: Format[ViolationElement] = Json.format[ViolationElement]
  }

  object ViolationAPI {
    implicit val f: Format[ViolationAPI] = Json.format[ViolationAPI]
  }

  object Violations {

    implicit val f: Format[Violations] = Json.format[Violations]
  }

  object ViolationsAPI {
    implicit val f: Format[ViolationsAPI] = Json.format[ViolationsAPI]
  }

  object ViolationState {
    implicit val f: Format[ViolationState] = Json.format[ViolationState]
  }

  object ViolationStates {
    implicit val f: Format[ViolationStates] = Json.format[ViolationStates]
  }

}
