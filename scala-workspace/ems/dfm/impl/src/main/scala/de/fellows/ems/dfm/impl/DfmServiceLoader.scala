package de.fellows.ems.dfm.impl

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.supplier.SupplierService
import de.fellows.ems.dfm.api.DFMService
import de.fellows.ems.dfm.impl.entity.JsonSerializerRegistry
import de.fellows.ems.dfm.impl.entity.violation.DFMViolations
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.renderer.api.RendererService
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class DfmServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new DFMServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new DFMServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[DFMService])
}

abstract class DFMServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with DFMServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  lazy val pcb           = serviceClient.implement[PCBService]
  lazy val renderService = serviceClient.implement[RendererService]
  lazy val assService    = serviceClient.implement[AssemblyService]
  lazy val suppService   = serviceClient.implement[SupplierService]
  lazy val stackService  = serviceClient.implement[LayerstackService]

  val assListener = wire[AssemblyListener].withApp(this)

  lazy val srv: DFMServiceImpl  = wire[DFMServiceImpl]
  override lazy val lagomServer = serverFor[DFMService](srv)

}

trait DFMServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("dfm")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = JsonSerializerRegistry

  //  lazy val read = wire[DFMRepository]

  persistentEntityRegistry.register(wire[DFMViolations])

  //  val dfmanalyzer = wire[DFMAnalyzer]
  //  readSide.register(dfmanalyzer)
}

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
