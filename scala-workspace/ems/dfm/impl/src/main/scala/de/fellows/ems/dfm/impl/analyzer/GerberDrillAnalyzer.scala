package de.fellows.ems.dfm.impl.analyzer

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.supplier
import de.fellows.app.supplier.{NumericalCapability, Technology}
import de.fellows.ems.dfm.api.{Violation, ViolationElement, Violations}
import de.fellows.ems.pcb.model.DFM.Properties.DFM
import de.fellows.ems.pcb.model.{DrillFile, HoleList, Tool}
import de.fellows.utils.UUIDUtils._
import de.fellows.utils.internal.FileReader
import de.fellows.utils.meta._
import play.api.libs.json.JsSuccess

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.math.BigDecimal.RoundingMode
import scala.util.Success

class GerberDrillAnalyzer(
    ass: AssemblyReference,
    drillFiles: Seq[DrillFile],
    ereg: PersistentEntityRegistry
)(implicit c: ExecutionContext) extends Analyzer {

  def analyzeHoles(fileIdx: Int, x: HoleList, technologies: Seq[Technology]): Violations = {
    val scaling = x.scaling.getOrElse(BigDecimal(100))
    val list = x.tools.map { tool =>
      val element_ph_max  = createElement(fileIdx, x, tool, DFM.PH_MAX_SIZE)
      val element_ph_min  = createElement(fileIdx, x, tool, DFM.PH_MIN_SIZE)
      val element_nph_max = createElement(fileIdx, x, tool, DFM.NPH_MAX_SIZE)
      val element_nph_min = createElement(fileIdx, x, tool, DFM.NPH_MIN_SIZE)

      val diameter = (tool.diameter / scaling).setScale(DFM_ROUND_SCALE, RoundingMode.HALF_UP)

      val vios = technologies.flatMap { tech =>
        val phMaxCap  = tech.capabilities.find(_.name == DFM.PH_MAX_SIZE)
        val phMinCap  = tech.capabilities.find(_.name == DFM.PH_MIN_SIZE)
        val nphMaxCap = tech.capabilities.find(_.name == DFM.NPH_MAX_SIZE)
        val nphMinCap = tech.capabilities.find(_.name == DFM.NPH_MIN_SIZE)

        val phmax  = phMaxCap.flatMap(_.asInstanceOf[NumericalCapability].max)
        val phmin  = phMinCap.flatMap(_.asInstanceOf[NumericalCapability].min)
        val nphmax = nphMaxCap.flatMap(_.asInstanceOf[NumericalCapability].max)
        val nphmin = nphMinCap.flatMap(_.asInstanceOf[NumericalCapability].min)

        val occurences = tool.drills.size
        if (tool.drillType == Tool.PLATED) {
          if (phmax.exists(d => diameter > d)) {
            Seq(createDrillViolation(tech, phMaxCap.get, element_ph_max.id, occurences))
          } else if (phmin.exists(d => diameter < d)) {
            Seq(createDrillViolation(tech, phMinCap.get, element_ph_min.id, occurences))
          } else {
            Seq()
          }
        } else if (tool.drillType == Tool.NON_PLATED) {
          if (nphmax.exists(d => diameter > d)) {
            Seq(createDrillViolation(tech, nphMaxCap.get, element_nph_max.id, occurences))
          } else if (nphmin.exists(d => diameter < d)) {
            Seq(createDrillViolation(tech, nphMinCap.get, element_nph_min.id, occurences))
          } else {
            Seq()
          }
        } else {
          Seq()
        }
      }

      (Seq(element_ph_max, element_ph_min, element_nph_max, element_nph_min), vios)
    }

    Violations(
      ass,
      list.flatMap(_._2),
      list.flatMap(_._1)
    )
  }

  private def createElement(fileIdx: Int, x: HoleList, tool: Tool, prop: String) =
    ViolationElement(
      id = s"hole-${tool.name}-F$fileIdx-L${x.from}-L${x.to}-$prop",
      status = Violation.STATUS_ACTIVE,
      propertyName = prop,
      distance = None,
      points = None,
      size = None,
      scaling = x.scaling,
      file = None,
      Some(tool.name),
      None,
      property = DecimalProperty(prop, tool.diameter / x.scaling.getOrElse(1))
    )

  private def createDrillViolation(
      tech: supplier.Technology,
      cap: supplier.Capability,
      element: String,
      occurences: Int
  ): Violation =
    Violation(
      tech.id.get,
      UUID.randomUUID().short(),
      4,
      Violation.STATUS_ACTIVE,
      element = element,
      capability = cap,
      occurences = occurences
    )

  override def analyze(technologies: Seq[Technology]): Future[Seq[Violations]] = {
    val holes = drillFiles.flatMap { df =>
      FileReader.json[HoleList](df.holes.toJavaPath) match {
        case Success(JsSuccess(value, path)) =>
          Some(value)
        case _ => None
      }
    }

    Future {
      val x = holes.zipWithIndex.map(x => analyzeHoles(x._2, x._1, technologies)).map(v => (v.elements, v.violations))

      val res = Seq(Violations(
        ass,
        x.flatMap(_._2),
        x.flatMap(_._1)
      ))

      res
    }

  }
}
