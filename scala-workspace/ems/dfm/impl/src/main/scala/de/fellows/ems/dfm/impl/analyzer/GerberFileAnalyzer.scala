package de.fellows.ems.dfm.impl.analyzer

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.supplier.{NumericalCapability, SupplierService, Technology}
import de.fellows.ems.dfm.api.{Violation, ViolationElement, Violations}
import de.fellows.ems.pcb.model.DFM.Properties.DFM
import de.fellows.ems.pcb.model.graphics.tree.TraceWidthDescription
import de.fellows.ems.pcb.model.{GerberFile, GraphicElement, LayerConstants, Net, NetList, PCBVersion}
import de.fellows.ems.renderer.api.{DistanceDescription, FileReference, RendererService}
import de.fellows.utils.UUIDUtils
import de.fellows.utils.meta._
import play.api.Logging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.math.BigDecimal.RoundingMode
import scala.util.{Failure, Success}

class GerberFileAnalyzer(
    ass: AssemblyReference,
    pcbv: PCBVersion,
    file: GerberFile,
    _renderer: RendererService,
    _supp: SupplierService,
    netlist: Option[NetList]
)(implicit ctx: ExecutionContext) extends Analyzer with Logging {

  /** Limit the amount of violations to keep them at a manageable level.
    *
    * This is a best guess value
    */
  val MAX_VIOLATIONS = 100



  def getNetForId(ido: Option[Int]): Option[Net] =
    ido.flatMap { id =>
      val elementId = GraphicElement.elementID(file, id)
      netlist.flatMap(_.findNetByElement(elementId))
    }

  override def analyze(technologies: Seq[Technology]): Future[Seq[Violations]] =
    if (LayerConstants.COPPER.contains(file.fType.fileType)) {
      for {
        distances <- analyzeDistances(technologies)
        widths    <- analyzeTracewidth(technologies)
      } yield {
        val allVios = distances.toSeq ++ widths.toSeq
        // sanity check whether violations can be merged
        val mergedVios = allVios.fold(Violations(ass, Seq(), Seq()))(_ ++ _)
        if (mergedVios.isEmpty()) {
          Seq()
        } else {
          Seq(mergedVios)
        }

      }
    } else {
      Future.successful(Seq())
    }

  import UUIDUtils._

  private def createViolations(
      elements: Seq[ViolationElement],
      suppliers: Seq[Technology],
      property: String,
      scale: Double
  ): Option[Violations] = {
    val violations = suppliers.flatMap { tech =>
      tech.capabilities.find(_.name == property).map(_.asInstanceOf[NumericalCapability]).map { cap =>
        elements.flatMap { element =>
          if (violates(element.property, cap)) {
            Some(Violation(
              technology = tech.id.get,
              id = UUID.randomUUID().short(),
              severity = 4,
              status = Violation.STATUS_ACTIVE,
              element = element.id,
              capability = cap
            ))
          } else {
            None
          }
        }
      }

    }.flatten

    Some(Violations(
      ass,
      violations,
      elements
    ))
  }

  private def createViolationElementsFromDistances(
      allDistances: Seq[DistanceDescription],
      property: String,
      scale: BigDecimal
  ): Seq[ViolationElement] = {
    val unknownTrace = "---"
    val distances = allDistances.groupBy(_.fromTrace.getOrElse(unknownTrace)).flatMap {
      case (v, x) if v == unknownTrace => x
      case (_, x) => x.minByOption(_.distance.distance).toSeq // only use the smallest clearance for each trace
    }

    distances.map { d =>
      ViolationElement(
        id = s"${property}-${file.id}-${d.from}-${d.to}",
        status = Violation.STATUS_ACTIVE,
        propertyName = property,
        distance = Some(d),
        points = None,
        size = None,
        scaling = file.format.flatMap(_.scaling.map(BigDecimal.apply)),
        file =
          Some(FileReference(file.id, file.name, file.fType, file.format.flatMap(_.scaling.map(BigDecimal.apply)))),
        None,
        trace = d.fromTrace,
        property = DecimalProperty(property, d.distance.distance / scale)
      )
    }.toSeq
  }

  private def createViolationElementsFromTraceWidths(
      traceWidths: Seq[TraceWidthDescription],
      property: String,
      scale: BigDecimal
  ): Seq[ViolationElement] = {

    val widths = traceWidths.sortBy(_.width).slice(0, MAX_VIOLATIONS)

    widths.map { width =>
      ViolationElement(
        id = s"${property}-${file.id}--${width.element}",
        status = Violation.STATUS_ACTIVE,
        propertyName = property,
        distance = width.distance.map(d =>
          DistanceDescription(
            Some(width.element),
            Some(width.element),
            d,
            d.distance,
            width.trace,
            width.trace
          )
        ),
        points = None,
        size = None,
        scaling = file.format.flatMap(_.scaling.map(BigDecimal.apply)),
        file =
          Some(FileReference(file.id, file.name, file.fType, file.format.flatMap(_.scaling.map(BigDecimal.apply)))),
        element = Some(GraphicElement.elementID(file, width.element)),
        trace = width.trace,
        property = DecimalProperty(property, width.width / scale)
      )
    }
  }

  /** Get tracewidth violations. If its an inverted layer the measured distances become traceWidths.
    *
    * @param suppliers
    * @return
    */
  private def analyzeTracewidth(suppliers: Seq[Technology]): Future[Option[Violations]] = {
    val property = DFM.TRACE_WIDTH
    val scale    = file.format.flatMap(_.scaling).getOrElse(100.0)

    if (file.inverted.contains(true)) {
      createDistanceViolations(suppliers, property, scale)
    } else {
      createTracewidthViolations(suppliers, property, scale)
    }
  }

  /** Get distance violations. If its an inverted layer the measured traceWidths become distances.
    *
    * @param suppliers
    * @return
    */
  private def analyzeDistances(suppliers: Seq[Technology]): Future[Option[Violations]] = {
    val property = DFM.CLEARANCE
    val scale    = file.format.flatMap(_.scaling).getOrElse(100.0)

    if (file.inverted.contains(true)) {
      createTracewidthViolations(suppliers, property, scale)
    } else {
      createDistanceViolations(suppliers, property, scale, ignoreSameNet = true)
    }
  }

  private def createTracewidthViolations(
      suppliers: Seq[Technology],
      property: Base64EncodedString,
      scale: Double
  ): Future[Option[Violations]] =
    _renderer._getAllTracewidths(ass.team, ass.id, ass.version, file.name).invoke().map { x =>
      val elements = createViolationElementsFromTraceWidths(x, property, scale)
      val vio      = createViolations(elements, suppliers, property, scale)
      vio
    }

  private def createDistanceViolations(
      suppliers: Seq[Technology],
      property: Base64EncodedString,
      scale: Double,
      ignoreSameNet: Boolean = false
  ): Future[Option[Violations]] =
    _renderer._getAllDistances(ass.team, ass.id, ass.version, file.name, None).invoke()
      .map { x =>
        val filteredElements = if (ignoreSameNet) {
          x.filter { dd =>
            val fromnet = getNetForId(dd.from)
            val tonet   = getNetForId(dd.to)

            (fromnet.isEmpty && tonet.isEmpty) || fromnet != tonet
          }
        }else{
          x
        }

        val elements = createViolationElementsFromDistances(filteredElements, property, scale)
        createViolations(elements, suppliers, property, scale)
      }

  private def violates(value: Property, cap: NumericalCapability): Boolean =
    value match {
      case PropertyWithLabel(name, label, value) => violates(value, cap)
      case PropertyWithUOM(name, uom, value)     => violates(value, cap)
      case DecimalProperty(name, value) =>
        cap.max.exists(m => value.setScale(DFM_ROUND_SCALE, RoundingMode.HALF_UP) > (m)) ||
        cap.min.exists(m => value.setScale(DFM_ROUND_SCALE, RoundingMode.HALF_UP) < (m))
      case BooleanProperty(name, value) => false
      case StringProperty(name, value)  => false
      case ListProperty(name, value)    => false
      case ObjectProperty(name, value)  => false
    }

  private def violates(d: Double, scale: Double, cap: NumericalCapability) =
    cap.max.exists(m => BigDecimal(d).setScale(DFM_ROUND_SCALE, RoundingMode.HALF_UP) > (m * scale)) ||
      cap.min.exists(m => BigDecimal(d).setScale(DFM_ROUND_SCALE, RoundingMode.HALF_UP) < (m * scale))
}
