package de.fellows.ems.renderer.api.job

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.utils.redislog.jobs.JobType.JobTypeProductionAnalysis
import de.fellows.utils.redislog.jobs.{JobEntry, JobType}
import play.api.libs.json.{Format, Json}

case class ProductionAnalysisJobEntry(assembly: AssemblyReference) extends JobEntry {
  override val jobType: JobType = JobTypeProductionAnalysis
}

object ProductionAnalysisJobEntry {
  implicit val format: Format[ProductionAnalysisJobEntry] = Json.format[ProductionAnalysisJobEntry]
}
