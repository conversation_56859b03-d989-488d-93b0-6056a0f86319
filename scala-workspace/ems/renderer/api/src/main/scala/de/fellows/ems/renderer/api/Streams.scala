package de.fellows.ems.renderer.api

import de.fellows.app.assembly.commons.AssemblyReference
import play.api.libs.json
import play.api.libs.json.{ Format, Json }

import java.time.Instant

object Streams {

  sealed trait Message {
    val t: String
    val time: Instant

  }

  case class RenderStreamMessage(assRef: Option[AssemblyReference], m: Message)

  case class Ping(override val time: Instant) extends Message {
    override val t: String = "ping"
  }

  case class StatusChangedMessage(override val time: Instant, file: String, status: String) extends Message {
    override val t: String = "status"
  }

  case class MessageChangedMessage(override val time: Instant, file: String, messages: Seq[RenderLogMessage])
      extends Message {
    override val t: String = "message"
  }

  object Ping {
    implicit val format: Format[Ping] = Json.format
  }

  object StatusChangedMessage {
    implicit val format: Format[StatusChangedMessage] = Json.format
  }

  object MessageChangedMessage {
    implicit val format: Format[MessageChangedMessage] = Json.format
  }

  object RenderStreamMessage {
    implicit val format: json.Format[RenderStreamMessage] = Json.format
  }

  object Message {
    implicit val format: json.Format[Message] = Json.format
  }

}
