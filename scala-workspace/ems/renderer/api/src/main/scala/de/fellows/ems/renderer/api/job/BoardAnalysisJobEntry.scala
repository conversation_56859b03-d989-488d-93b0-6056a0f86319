package de.fellows.ems.renderer.api.job

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.utils.redislog.jobs.JobType.JobTypeBoardAnalysis
import de.fellows.utils.redislog.jobs.{JobEntry, JobType}
import play.api.libs.json.{Format, Json}

case class BoardAnalysisJobEntry(assembly: AssemblyReference) extends JobEntry {
  override val jobType: JobType = JobTypeBoardAnalysis
}

object BoardAnalysisJobEntry {
  implicit val format: Format[BoardAnalysisJobEntry] = Json.format[BoardAnalysisJobEntry]
}
