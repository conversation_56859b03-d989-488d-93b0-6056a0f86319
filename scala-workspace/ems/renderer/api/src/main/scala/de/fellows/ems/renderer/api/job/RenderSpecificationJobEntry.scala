package de.fellows.ems.renderer.api.job

import de.fellows.app.assemby.api.AssemblyLifecycleStageName
import de.fellows.utils.redislog.jobs.JobType.JobTypeRenderSpecification
import de.fellows.utils.redislog.jobs.{JobEntry, JobType}
import play.api.libs.json.{Format, Json}

import java.util.UUID

case class RenderSpecificationJobEntry(
    team: String,
    assembly: UUID,
    lifecycle: Option[AssemblyLifecycleStageName]
) extends JobEntry {
  override val jobType: JobType = JobTypeRenderSpecification
}

object RenderSpecificationJobEntry {
  implicit val format: Format[RenderSpecificationJobEntry] = Json.format[RenderSpecificationJobEntry]
}
