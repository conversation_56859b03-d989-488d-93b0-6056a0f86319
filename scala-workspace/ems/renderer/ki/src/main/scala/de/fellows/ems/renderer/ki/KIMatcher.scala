package de.fellows.ems.renderer.ki

import de.fellows.utils.internal.FileReader
import de.fellows.utils.internal.FileReader._
import org.tensorflow.op.Ops
import org.tensorflow.op.image.DecodePng
import org.tensorflow.{ SavedModelBundle, TensorFlow }
import play.api.Logging
import play.api.libs.json.Json

import java.nio.file.Path
import scala.reflect.ClassTag

object KIMatcher extends Logging {

  object SpecialisedArray {
    def apply[@specialized(Float, Double) T: ClassTag](size: Int) = new Array[T](size)
  }

  def loadBundle(): SavedModelBundle = {
    val tags = FileReader.withResource(getClass.getResourceAsStream("/generic-layers/labels.json")) { r =>
      Json.parse(r).as[Seq[String]]
    }

    SavedModelBundle.load(
      "/home/<USER>/Code/fellows/backend/app-backend/ems/renderer/ki/src/main/resources/generic-layers/cnn_classifier",
      "serve"
    )
  }

  def createMatches(path: Path) = {
    println(TensorFlow.version())
    println("loading graph")
    withResource(loadBundle()) { g =>
      val session = g.session()
      val tf      = Ops.create()

      val file     = tf.io.readFile(tf.constant(path.toString))
      val decoded  = tf.image.decodePng(file, DecodePng.channels(1L))
      val asFloats = tf.dtypes.cast(decoded, classOf[java.lang.Float])
      //      tf.image.randomCrop(asFloats, TFUtil.array(Seq(256L, 256L, 1L).map(long2Long).asJavaCollection, tf))

      //      session.runner()
      //        .feed("")

      g.graph().operations().forEachRemaining { op =>
        println(s"${op.name()}: ${op.`type`()}")
      }

      val output =
        session
          .runner()
          .feed("serving_default_conv2d_input", asFloats.asOutput().tensor())
          .feed("saver_filename", tf.constant("").asOutput().tensor())
          .fetch("total")
          .run
          .get(0)
          .expect(classOf[java.lang.Float])

      val rshape  = output.shape()
      val nlabels = rshape(0).toInt
      println(s"xxx $nlabels")
      val percentages = output.copyTo(new Array[Float](nlabels))
      println(s"RESULT: ${percentages.map(_ * 100).mkString(", ")}")

    //          .feed("input", 0, input)
    //          .fetch("total")
    //          .run()
    //          .get(0)
    //          .expect(classOf[java.lang.Float])
    //
    //
    //      val shape = output.shape();
    //      val batchSize = shape(0).asInstanceOf[Int];
    //      val labelNum = shape(1).asInstanceOf[Int];
    //      //n ew float[batchSize][labelNum]
    //      val resultValues = output.copyTo(Array.ofDim[java.lang.Float](2));
    //      System.out.println(resultValues);
    //      val input = TFloat32.tensorOf(...)
    //      println(g.metaGraphDef().().get("serving_default"))
    //      g.session().runner().
    //      cleanly(g.session()){s => {
    //        g.graph().opBuilder("constant", "input")
    //
    //        g.graph().operations().forEachRemaining(println)
    //        b.constant("input", imageBytes)
    //        s.runner.fetch(output.op.name).run.get(0).expect(classOf[Float])
    }
  }
}
