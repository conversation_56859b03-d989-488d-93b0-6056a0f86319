��
    ��
    8
Const
output"dtype"
valuetensor"
dtypetype

NoOp
C
Placeholder
output"dtype"
dtypetype"
shapeshape: 
@
ReadVariableOp
resource
value"dtype"
dtypetype�
�
StatefulPartitionedCall
args2Tin
output2Tout"
Tin
list(type)("
Tout
list(type)("
ffunc"
configstring "
config_protostring "

executor_typestring �
q
VarHandleOp
resource"
containerstring "
shared_namestring "
dtypetype"
shapeshape�"serve*2.2.02v2.2.0-rc4-8-g2b96f3662b8б
~

conv2d/kernel VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_name
conv2d/kernel
w
!conv2d/kernel/Read/ReadVariableOpReadVariableOp
conv2d/kernel*&
_output_shapes
    :  *
dtype0
n
conv2d/bias VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_name
 conv2d/bias
g
conv2d/bias/Read/ReadVariableOpReadVariableOp conv2d/bias*
_output_shapes
:  *
dtype0
�
conv2d_1/kernel VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  �*
shared_nameconv2d_1/kernel
|
#conv2d_1/kernel/Read/ReadVariableOpReadVariableOpconv2d_1/kernel*'
_output_shapes
    :  �*
dtype0
s

conv2d_1/bias VarHandleOp*
_output_shapes
:  *
dtype0*
shape: �*
shared_name
conv2d_1/bias
l
!conv2d_1/bias/Read/ReadVariableOpReadVariableOp
conv2d_1/bias*
_output_shapes
: �*
    dtype0
�
conv2d_2/kernel VarHandleOp*
_output_shapes
:  *
dtype0*
shape: ��*
shared_nameconv2d_2/kernel
}
#conv2d_2/kernel/Read/ReadVariableOpReadVariableOpconv2d_2/kernel*(
_output_shapes
    : ��*
dtype0
s

conv2d_2/bias VarHandleOp*
_output_shapes
:  *
dtype0*
shape: �*
shared_name
conv2d_2/bias
l
!conv2d_2/bias/Read/ReadVariableOpReadVariableOp
conv2d_2/bias*
_output_shapes
: �*
    dtype0
�
conv2d_3/kernel VarHandleOp*
_output_shapes
:  *
dtype0*
shape: ��*
shared_nameconv2d_3/kernel
}
#conv2d_3/kernel/Read/ReadVariableOpReadVariableOpconv2d_3/kernel*(
_output_shapes
    : ��*
dtype0
s

conv2d_3/bias VarHandleOp*
_output_shapes
:  *
dtype0*
shape: �*
shared_name
conv2d_3/bias
l
!conv2d_3/bias/Read/ReadVariableOpReadVariableOp
conv2d_3/bias*
_output_shapes
: �*
    dtype0
v
dense/kernel VarHandleOp*
_output_shapes
:  *
dtype0*
shape :
    ��*
shared_name dense/kernel
o
dense/kernel/Read/ReadVariableOpReadVariableOp dense/kernel*
_output_shapes
    :
    ��*
dtype0
m

dense/bias VarHandleOp*
_output_shapes
:  *
dtype0*
shape: �*
shared_name 
dense/bias
f
dense/bias/Read/ReadVariableOpReadVariableOp
dense/bias*
_output_shapes
: �*
    dtype0
y
dense_1/kernel VarHandleOp*
_output_shapes
:  *
dtype0*
shape : �`*
shared_namedense_1/kernel
r
"dense_1/kernel/Read/ReadVariableOpReadVariableOpdense_1/kernel*
_output_shapes
: �`*
dtype0
p
dense_1/bias VarHandleOp*
_output_shapes
:  *
dtype0*
shape: `*
shared_name dense_1/bias
i
dense_1/bias/Read/ReadVariableOpReadVariableOp dense_1/bias*
_output_shapes
    : `*
    dtype0
x
dense_2/kernel VarHandleOp*
_output_shapes
:  *
dtype0*
shape
: `*
shared_namedense_2/kernel
q
"dense_2/kernel/Read/ReadVariableOpReadVariableOpdense_2/kernel*
_output_shapes

: `*
dtype0
p
dense_2/bias VarHandleOp*
_output_shapes
:  *
dtype0*
shape: *
shared_name dense_2/bias
i
dense_2/bias/Read/ReadVariableOpReadVariableOp dense_2/bias*
_output_shapes
    : *
    dtype0
f
Adam/iter VarHandleOp*
_output_shapes
:  *
dtype0  *
shape:  *
shared_name   Adam/iter
_
Adam/iter/Read/ReadVariableOpReadVariableOp  Adam/iter*
_output_shapes
:  *
dtype0
j
Adam/beta_1 VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_name
 Adam/beta_1
c
Adam/beta_1/Read/ReadVariableOpReadVariableOp Adam/beta_1*
_output_shapes
:  *
dtype0
j
Adam/beta_2 VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_name
 Adam/beta_2
c
Adam/beta_2/Read/ReadVariableOpReadVariableOp Adam/beta_2*
_output_shapes
:  *
dtype0
h

Adam/decay VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_name 
Adam/decay
a
Adam/decay/Read/ReadVariableOpReadVariableOp
Adam/decay*
_output_shapes
:  *
dtype0
x
Adam/learning_rate VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *#
shared_nameAdam/learning_rate
q
&Adam/learning_rate/Read/ReadVariableOpReadVariableOpAdam/learning_rate*
_output_shapes
:  *
dtype0
^
total VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_nametotal
W
total/Read/ReadVariableOpReadVariableOptotal*
_output_shapes
:  *
dtype0
^
count VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_namecount
W
count/Read/ReadVariableOpReadVariableOpcount*
_output_shapes
:  *
dtype0
b
total_1 VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_name  total_1
[
total_1/Read/ReadVariableOpReadVariableOptotal_1*
_output_shapes
:  *
dtype0
b
count_1 VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *
shared_name  count_1
[
count_1/Read/ReadVariableOpReadVariableOpcount_1*
_output_shapes
:  *
dtype0
�
Adam/conv2d/kernel/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *%
shared_nameAdam/conv2d/kernel/m
�
(Adam/conv2d/kernel/m/Read/ReadVariableOpReadVariableOpAdam/conv2d/kernel/m*&
_output_shapes
    :  *
dtype0
|
Adam/conv2d/bias/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  *#
shared_nameAdam/conv2d/bias/m
u
&Adam/conv2d/bias/m/Read/ReadVariableOpReadVariableOpAdam/conv2d/bias/m*
_output_shapes
:  *
dtype0
�
Adam/conv2d_1/kernel/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape:  �*'
shared_nameAdam/conv2d_1/kernel/m
�
*Adam/conv2d_1/kernel/m/Read/ReadVariableOpReadVariableOpAdam/conv2d_1/kernel/m*'
_output_shapes
    :  �*
dtype0
�
Adam/conv2d_1/bias/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape: �*%
shared_nameAdam/conv2d_1/bias/m
z
(Adam/conv2d_1/bias/m/Read/ReadVariableOpReadVariableOpAdam/conv2d_1/bias/m*
_output_shapes
: �*
    dtype0
�
Adam/conv2d_2/kernel/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape: ��*'
shared_nameAdam/conv2d_2/kernel/m
�
*Adam/conv2d_2/kernel/m/Read/ReadVariableOpReadVariableOpAdam/conv2d_2/kernel/m*(
_output_shapes
    : ��*
dtype0
�
Adam/conv2d_2/bias/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape: �*%
shared_nameAdam/conv2d_2/bias/m
z
(Adam/conv2d_2/bias/m/Read/ReadVariableOpReadVariableOpAdam/conv2d_2/bias/m*
_output_shapes
: �*
    dtype0
�
Adam/conv2d_3/kernel/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape: ��*'
shared_nameAdam/conv2d_3/kernel/m
�
*Adam/conv2d_3/kernel/m/Read/ReadVariableOpReadVariableOpAdam/conv2d_3/kernel/m*(
_output_shapes
    : ��*
dtype0
�
Adam/conv2d_3/bias/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape: �*%
shared_nameAdam/conv2d_3/bias/m
z
(Adam/conv2d_3/bias/m/Read/ReadVariableOpReadVariableOpAdam/conv2d_3/bias/m*
_output_shapes
: �*
    dtype0
�
Adam/dense/kernel/m VarHandleOp*
_output_shapes
:  *
dtype0*
shape :
    ��*$
shared_nameAdam/dense/kernel/m
}
'Adam/dense/kernel/m/Read/ReadVariableOpReadVariableOpAdam/dense/kernel/m*
_output_shapes
    :
    ��*
dtype0
{
    Adam/dense/bias/m VarHandleOp*
        _output_shapes
        :  *
        dtype0*
            shape: �*"
        shared_nameAdam/dense/bias/m
        t
        %Adam/dense/bias/m/Read/ReadVariableOpReadVariableOpAdam/dense/bias/m*
        _output_shapes
            : �*
        dtype0
        �
        Adam/dense_1/kernel/m VarHandleOp*
            _output_shapes
            :  *
            dtype0*
                shape: �`*&
            shared_nameAdam/dense_1/kernel/m
            �
            )Adam/dense_1/kernel/m/Read/ReadVariableOpReadVariableOpAdam/dense_1/kernel/m*
            _output_shapes
            : �`*
            dtype0
        ~
        Adam/dense_1/bias/m VarHandleOp*
        _output_shapes
        :  *
            dtype0*
        shape: `*$
        shared_nameAdam/dense_1/bias/m
        w
        'Adam/dense_1/bias/m/Read/ReadVariableOpReadVariableOpAdam/dense_1/bias/m*
        _output_shapes
        : `*
        dtype0
            �
            Adam/dense_2/kernel/m VarHandleOp*
        _output_shapes
            :  *
            dtype0*
        shape
        :`*&
        shared_nameAdam/dense_2/kernel/m
        
        )Adam/dense_2/kernel/m/Read/ReadVariableOpReadVariableOpAdam/dense_2/kernel/m*
        _output_shapes

            : `*
        dtype0
        ~
            Adam/dense_2/bias/m VarHandleOp*
        _output_shapes
        :  *
        dtype0*
        shape: *$
        shared_nameAdam/dense_2/bias/m
        w
        'Adam/dense_2/bias/m/Read/ReadVariableOpReadVariableOpAdam/dense_2/bias/m*
        _output_shapes
        : *
        dtype0
        �
        Adam/conv2d/kernel/v VarHandleOp*
        _output_shapes
        :  *
        dtype0*
        shape:  *%
        shared_nameAdam/conv2d/kernel/v
        �
        (Adam/conv2d/kernel/v/Read/ReadVariableOpReadVariableOpAdam/conv2d/kernel/v*&
        _output_shapes
        :  *
        dtype0
        |
        Adam/conv2d/bias/v VarHandleOp*
        _output_shapes
        :  *
        dtype0*
        shape:  *#
        shared_nameAdam/conv2d/bias/v
        u
        &Adam/conv2d/bias/v/Read/ReadVariableOpReadVariableOpAdam/conv2d/bias/v*
        _output_shapes
        :  *
        dtype0
        �
            Adam/conv2d_1/kernel/v VarHandleOp*
        _output_shapes
        : *
        dtype0*
        shape:  �*'
            shared_nameAdam/conv2d_1/kernel/v
            �
            *Adam/conv2d_1/kernel/v/Read/ReadVariableOpReadVariableOpAdam/conv2d_1/kernel/v*'
            _output_shapes
            : �*
            dtype0
            �
            Adam/conv2d_1/bias/v VarHandleOp*
        _output_shapes
        :  *
        dtype0*
        shape: �*%
        shared_nameAdam/conv2d_1/bias/v
        z
        (Adam/conv2d_1/bias/v/Read/ReadVariableOpReadVariableOpAdam/conv2d_1/bias/v*
        _output_shapes
        : �*
        dtype0
        �
        Adam/conv2d_2/kernel/v VarHandleOp*
        _output_shapes
        :  *
        dtype0*
        shape: ��*'
        shared_nameAdam/conv2d_2/kernel/v
        �
        *Adam/conv2d_2/kernel/v/Read/ReadVariableOpReadVariableOpAdam/conv2d_2/kernel/v*(
        _output_shapes
            : ��*
    dtype0
    �
    Adam/conv2d_2/bias/v VarHandleOp*
    _output_shapes
    :  *
        dtype0*
    shape: �*%
    shared_nameAdam/conv2d_2/bias/v
    z
    (Adam/conv2d_2/bias/v/Read/ReadVariableOpReadVariableOpAdam/conv2d_2/bias/v*
    _output_shapes
    : �*
    dtype0
    �
    Adam/conv2d_3/kernel/v VarHandleOp*
    _output_shapes
    :  *
        dtype0*
    shape: ��*'
    shared_nameAdam/conv2d_3/kernel/v
    �
        *Adam/conv2d_3/kernel/v/Read/ReadVariableOpReadVariableOpAdam/conv2d_3/kernel/v*(
    _output_shapes
    : ��*
    dtype0
    �
        Adam/conv2d_3/bias/v VarHandleOp*
    _output_shapes
    : *
    dtype0*
    shape: �*%
    shared_nameAdam/conv2d_3/bias/v
    z
    (Adam/conv2d_3/bias/v/Read/ReadVariableOpReadVariableOpAdam/conv2d_3/bias/v*
    _output_shapes
    : �*
    dtype0
    �
        Adam/dense/kernel/v VarHandleOp*
    _output_shapes
    : *
    dtype0*
    shape:
        ��*$
    shared_nameAdam/dense/kernel/v
    }
'Adam/dense/kernel/v/Read/ReadVariableOpReadVariableOpAdam/dense/kernel/v*
_output_shapes
    :
    ��*
dtype0
{
Adam/dense/bias/v VarHandleOp*
    _output_shapes
    :  *
    dtype0*
        shape: �*"
    shared_nameAdam/dense/bias/v
    t
    %Adam/dense/bias/v/Read/ReadVariableOpReadVariableOpAdam/dense/bias/v*
    _output_shapes
        : �*
    dtype0
    �
    Adam/dense_1/kernel/v VarHandleOp*
        _output_shapes
        :  *
        dtype0*
            shape: �`*&
        shared_nameAdam/dense_1/kernel/v
        �
        )Adam/dense_1/kernel/v/Read/ReadVariableOpReadVariableOpAdam/dense_1/kernel/v*
        _output_shapes
        : �`*
        dtype0
    ~
    Adam/dense_1/bias/v VarHandleOp*
    _output_shapes
    :  *
        dtype0*
    shape: `*$
    shared_nameAdam/dense_1/bias/v
    w
    'Adam/dense_1/bias/v/Read/ReadVariableOpReadVariableOpAdam/dense_1/bias/v*
    _output_shapes
    : `*
    dtype0
        �
        Adam/dense_2/kernel/v VarHandleOp*
    _output_shapes
        :  *
        dtype0*
    shape
    :`*&
    shared_nameAdam/dense_2/kernel/v
    
    )Adam/dense_2/kernel/v/Read/ReadVariableOpReadVariableOpAdam/dense_2/kernel/v*
    _output_shapes

        : `*
    dtype0
    ~
        Adam/dense_2/bias/v VarHandleOp*
    _output_shapes
    :  *
    dtype0*
    shape: *$
    shared_nameAdam/dense_2/bias/v
    w
    'Adam/dense_2/bias/v/Read/ReadVariableOpReadVariableOpAdam/dense_2/bias/v*
    _output_shapes
    : *
    dtype0

    NoOpNoOp
    �Q
    ConstConst"
        /device: CPU: 0*
    _output_shapes
    : *
    dtype0*�Q
    value�QB�Q B�Q
    �
    layer_with_weights-0
    layer-0
    layer-1
    layer_with_weights-1
    layer-2
    layer-3
    layer_with_weights-2
    layer-4
    layer_with_weights-3
    layer-5
    layer-6
    layer-7
      layer_with_weights-4
      layer-8
    
    layer_with_weights-5
        
        layer-9
     layer_with_weights-6
     layer-10

       optimizer
    
    regularization_losses

      variables
    trainable_variables

      keras_api
    
    signatures
        h

        kernel
        bias
    regularization_losses

      variables
    trainable_variables

      keras_api
    R
    regularization_losses

      variables
        trainable_variables

          keras_api
            h

            kernel
        bias
            regularization_losses

          variables
         trainable_variables

        !  keras_api
        R
        "regularization_losses

        #	variables
        $trainable_variables

        %  keras_api
        h

            &kernel
            'bias
            (regularization_losses

        )  variables
        *trainable_variables

        +  keras_api
        h

        , kernel
            -bias
    .regularization_losses

    /  variables
    0trainable_variables

    1  keras_api
    R
    2regularization_losses

    3  variables
    4trainable_variables

    5  keras_api
    R
    6regularization_losses

    7  variables
    8trainable_variables

    9  keras_api
    h

    : kernel
    ;bias
    < regularization_losses

    =  variables
     >trainable_variables

    ?  keras_api
    h

    @kernel
    Abias
    Bregularization_losses

    C  variables
    Dtrainable_variables

    E  keras_api
    h

    Fkernel
    Gbias
    Hregularization_losses

    I  variables
    Jtrainable_variables

    K  keras_api
    �
    Liter

    Mbeta_1

    Nbeta_2
    Odecay
    P
    learning_ratem�m�m�m�&m�'m�,m�-m�:m�;m�@m�Am�Fm�Gm�v�v�v�v�&v�'v�, v�-v�: v�;v�@v�Av�Fv�Gv�
     
    f
    0
    1
    2
    3
    &4
    '5
    , 6
    -7
    : 8
    ;9
        @10
    A11
    F12
    G13
    f
        0
        1
    2
    3
    &4
    '5
    , 6
    -7
        : 8
    ;9
    @10
    A11
    F12
        G13
    �

    Qlayers
    
    regularization_losses
    Rmetrics
    S
    layer_metrics
    Tnon_trainable_variables

      variables
        trainable_variables
    Ulayer_regularization_losses
         
        YW
    VARIABLE_VALUE
        conv2d/kernel6layer_with_weights-0/kernel/.ATTRIBUTES/VARIABLE_VALUE
    US
    VARIABLE_VALUE conv2d/bias4layer_with_weights-0/bias/.ATTRIBUTES/VARIABLE_VALUE
     
    
        0
        1
    
    0
    1
        �

        Vlayers
        Wmetrics
        regularization_losses
        X
        layer_metrics
        Ynon_trainable_variables

          variables
        trainable_variables
        Zlayer_regularization_losses
         
         
         
        �

        [layers
        \metrics
        regularization_losses
        ]
        layer_metrics
        ^non_trainable_variables

          variables
        trainable_variables
        _layer_regularization_losses
        [Y
    VARIABLE_VALUEconv2d_1/kernel6layer_with_weights-1/kernel/.ATTRIBUTES/VARIABLE_VALUE
    WU
    VARIABLE_VALUE
    conv2d_1/bias4layer_with_weights-1/bias/.ATTRIBUTES/VARIABLE_VALUE
     
    
    0
    1
        
        0
    1
    �

    `layers
    ametrics
    regularization_losses
    b
    layer_metrics
    cnon_trainable_variables

      variables
     trainable_variables
    dlayer_regularization_losses
     
     
     
    �

    elayers
    fmetrics
    "regularization_losses
    g
    layer_metrics
    hnon_trainable_variables

    #	variables
    $trainable_variables
    ilayer_regularization_losses
    [Y
    VARIABLE_VALUEconv2d_2/kernel6layer_with_weights-2/kernel/.ATTRIBUTES/VARIABLE_VALUE
    WU
    VARIABLE_VALUE
        conv2d_2/bias4layer_with_weights-2/bias/.ATTRIBUTES/VARIABLE_VALUE
     
    
    &0
        '1
        
        &0
            '1
            �

        jlayers
        kmetrics
        (regularization_losses
        l
        layer_metrics
        mnon_trainable_variables

        )  variables
        *trainable_variables
        nlayer_regularization_losses
        [Y
        VARIABLE_VALUEconv2d_3/kernel6layer_with_weights-3/kernel/.ATTRIBUTES/VARIABLE_VALUE
        WU
        VARIABLE_VALUE
        conv2d_3/bias4layer_with_weights-3/bias/.ATTRIBUTES/VARIABLE_VALUE
     
    
    , 0
    -1
    
    , 0
    -1
    �

    olayers
    pmetrics
    .regularization_losses
    q
    layer_metrics
    rnon_trainable_variables

    /  variables
    0trainable_variables
    slayer_regularization_losses
     
     
         
        �

        tlayers
        umetrics
    2regularization_losses
    v
        layer_metrics
        wnon_trainable_variables

        3  variables
        4trainable_variables
        xlayer_regularization_losses
         
         
         
        �

            ylayers
            zmetrics
        6regularization_losses
        { 
        layer_metrics
        |non_trainable_variables

        7  variables
        8trainable_variables
         }layer_regularization_losses
        XV
        VARIABLE_VALUE dense/kernel6layer_with_weights-4/kernel/.ATTRIBUTES/VARIABLE_VALUE
        TR
        VARIABLE_VALUE
        dense/bias4layer_with_weights-4/bias/.ATTRIBUTES/VARIABLE_VALUE
     
    
    :0
    ;1
    
    : 0
        ;1
    �

    ~layers
    metrics
    < regularization_losses
    �
        layer_metrics
        �non_trainable_variables

    =  variables
     >trainable_variables
    �layer_regularization_losses
    ZX
    VARIABLE_VALUEdense_1/kernel6layer_with_weights-5/kernel/.ATTRIBUTES/VARIABLE_VALUE
    VT
    VARIABLE_VALUE dense_1/bias4layer_with_weights-5/bias/.ATTRIBUTES/VARIABLE_VALUE
     
    
    @0
    A1
        
        @0
    A1
    �
    �layers
    �metrics
    Bregularization_losses
    �
    layer_metrics
    �non_trainable_variables

    C  variables
    Dtrainable_variables
    �layer_regularization_losses
    ZX
    VARIABLE_VALUEdense_2/kernel6layer_with_weights-6/kernel/.ATTRIBUTES/VARIABLE_VALUE
    VT
        VARIABLE_VALUE dense_2/bias4layer_with_weights-6/bias/.ATTRIBUTES/VARIABLE_VALUE
     
    
    F0
    G1
    
    F0
    G1
    �
    �layers
        �metrics
    Hregularization_losses
    �
    layer_metrics
    �non_trainable_variables

    I  variables
    Jtrainable_variables
    �layer_regularization_losses
    HF
    VARIABLE_VALUE  Adam/iter)optimizer/iter/.ATTRIBUTES/VARIABLE_VALUE
    LJ
    VARIABLE_VALUE Adam/beta_1+optimizer/beta_1/.ATTRIBUTES/VARIABLE_VALUE
    LJ
    VARIABLE_VALUE Adam/beta_2+optimizer/beta_2/.ATTRIBUTES/VARIABLE_VALUE
    JH
    VARIABLE_VALUE
    Adam/decay*optimizer/decay/.ATTRIBUTES/VARIABLE_VALUE
    ZX
    VARIABLE_VALUEAdam/learning_rate2optimizer/learning_rate/.ATTRIBUTES/VARIABLE_VALUE
    N
    0
    1
        2
    3
    4
    5
    6
        7
      8
    
    9
     10
    
    �0
    �1
     
     
     
     
     
     
     
     
 
     
     
     
     
     
     
     
     
     
     
         
         
         
         
         
         
         
         
         
     
         
         
         
         
         
         
         
         
         
     
         
         
         
         
         
         
         
         
         
     
         
         
         
         
         
         
         
         
         
    8

        �total

        �count
    �  variables
    �  keras_api
    I

    �total

    �count
    �
    _fn_kwargs
    �  variables
�  keras_api
    OM
    VARIABLE_VALUEtotal4keras_api/metrics/0/total/.ATTRIBUTES/VARIABLE_VALUE
    OM
    VARIABLE_VALUEcount4keras_api/metrics/0/count/.ATTRIBUTES/VARIABLE_VALUE
    
        �0
        �1
    
    �  variables
    QO
    VARIABLE_VALUEtotal_14keras_api/metrics/1/total/.ATTRIBUTES/VARIABLE_VALUE
    QO
    VARIABLE_VALUEcount_14keras_api/metrics/1/count/.ATTRIBUTES/VARIABLE_VALUE
     
    
    �0
    �1
    
    �  variables
    |z
    VARIABLE_VALUEAdam/conv2d/kernel/mRlayer_with_weights-0/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    xv
    VARIABLE_VALUEAdam/conv2d/bias/mPlayer_with_weights-0/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    ~|
    VARIABLE_VALUEAdam/conv2d_1/kernel/mRlayer_with_weights-1/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    zx
    VARIABLE_VALUEAdam/conv2d_1/bias/mPlayer_with_weights-1/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    ~|
    VARIABLE_VALUEAdam/conv2d_2/kernel/mRlayer_with_weights-2/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    zx
    VARIABLE_VALUEAdam/conv2d_2/bias/mPlayer_with_weights-2/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    ~|
    VARIABLE_VALUEAdam/conv2d_3/kernel/mRlayer_with_weights-3/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    zx
    VARIABLE_VALUEAdam/conv2d_3/bias/mPlayer_with_weights-3/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    { y
        VARIABLE_VALUEAdam/dense/kernel/mRlayer_with_weights-4/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
        wu
        VARIABLE_VALUEAdam/dense/bias/mPlayer_with_weights-4/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
        }{
    VARIABLE_VALUEAdam/dense_1/kernel/mRlayer_with_weights-5/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    yw
    VARIABLE_VALUEAdam/dense_1/bias/mPlayer_with_weights-5/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
    }{
    VARIABLE_VALUEAdam/dense_2/kernel/mRlayer_with_weights-6/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
        yw
        VARIABLE_VALUEAdam/dense_2/bias/mPlayer_with_weights-6/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUE
        |z
        VARIABLE_VALUEAdam/conv2d/kernel/vRlayer_with_weights-0/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        xv
        VARIABLE_VALUEAdam/conv2d/bias/vPlayer_with_weights-0/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        ~|
        VARIABLE_VALUEAdam/conv2d_1/kernel/vRlayer_with_weights-1/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        zx
        VARIABLE_VALUEAdam/conv2d_1/bias/vPlayer_with_weights-1/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        ~|
        VARIABLE_VALUEAdam/conv2d_2/kernel/vRlayer_with_weights-2/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        zx
        VARIABLE_VALUEAdam/conv2d_2/bias/vPlayer_with_weights-2/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        ~|
        VARIABLE_VALUEAdam/conv2d_3/kernel/vRlayer_with_weights-3/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        zx
        VARIABLE_VALUEAdam/conv2d_3/bias/vPlayer_with_weights-3/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        { y
            VARIABLE_VALUEAdam/dense/kernel/vRlayer_with_weights-4/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
            wu
            VARIABLE_VALUEAdam/dense/bias/vPlayer_with_weights-4/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
            }{
        VARIABLE_VALUEAdam/dense_1/kernel/vRlayer_with_weights-5/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        yw
        VARIABLE_VALUEAdam/dense_1/bias/vPlayer_with_weights-5/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
        }{
        VARIABLE_VALUEAdam/dense_2/kernel/vRlayer_with_weights-6/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
            yw
            VARIABLE_VALUEAdam/dense_2/bias/vPlayer_with_weights-6/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE
            �
            serving_default_conv2d_input Placeholder*1
            _output_shapes
            :  �����������*
        dtype0*&
        shape:  �����������
        �
        StatefulPartitionedCallStatefulPartitionedCallserving_default_conv2d_input
        conv2d/kernel conv2d/biasconv2d_1/kernel
        conv2d_1/biasconv2d_2/kernel
        conv2d_2/biasconv2d_3/kernel
        conv2d_3/bias dense/kernel
        dense/biasdense_1/kernel dense_1/biasdense_2/kernel dense_2/bias*
        Tin
        2*
        Tout
        2*'
        _output_shapes
        :  ���������*0
        _read_only_resource_inputs
        

        **
        config_proto
        
        CPU
        
        GPU 2J 8*,
        f'R%
        #__inference_signature_wrapper_44781
        O
        saver_filename Placeholder*
            _output_shapes
        :  *
            dtype0*
        shape:  
        �
        StatefulPartitionedCall_1StatefulPartitionedCallsaver_filename!conv2d/kernel/Read/ReadVariableOpconv2d/bias/Read/ReadVariableOp#conv2d_1/kernel/Read/ReadVariableOp!conv2d_1/bias/Read/ReadVariableOp#conv2d_2/kernel/Read/ReadVariableOp!conv2d_2/bias/Read/ReadVariableOp#conv2d_3/kernel/Read/ReadVariableOp!conv2d_3/bias/Read/ReadVariableOp dense/kernel/Read/ReadVariableOpdense/bias/Read/ReadVariableOp"dense_1/kernel/Read/ReadVariableOp dense_1/bias/Read/ReadVariableOp"dense_2/kernel/Read/ReadVariableOp dense_2/bias/Read/ReadVariableOpAdam/iter/Read/ReadVariableOpAdam/beta_1/Read/ReadVariableOpAdam/beta_2/Read/ReadVariableOpAdam/decay/Read/ReadVariableOp&Adam/learning_rate/Read/ReadVariableOptotal/Read/ReadVariableOpcount/Read/ReadVariableOptotal_1/Read/ReadVariableOpcount_1/Read/ReadVariableOp(Adam/conv2d/kernel/m/Read/ReadVariableOp&Adam/conv2d/bias/m/Read/ReadVariableOp*Adam/conv2d_1/kernel/m/Read/ReadVariableOp(Adam/conv2d_1/bias/m/Read/ReadVariableOp*Adam/conv2d_2/kernel/m/Read/ReadVariableOp(Adam/conv2d_2/bias/m/Read/ReadVariableOp*Adam/conv2d_3/kernel/m/Read/ReadVariableOp(Adam/conv2d_3/bias/m/Read/ReadVariableOp'Adam/dense/kernel/m/Read/ReadVariableOp%Adam/dense/bias/m/Read/ReadVariableOp)Adam/dense_1/kernel/m/Read/ReadVariableOp'Adam/dense_1/bias/m/Read/ReadVariableOp)Adam/dense_2/kernel/m/Read/ReadVariableOp'Adam/dense_2/bias/m/Read/ReadVariableOp(Adam/conv2d/kernel/v/Read/ReadVariableOp&Adam/conv2d/bias/v/Read/ReadVariableOp*Adam/conv2d_1/kernel/v/Read/ReadVariableOp(Adam/conv2d_1/bias/v/Read/ReadVariableOp*Adam/conv2d_2/kernel/v/Read/ReadVariableOp(Adam/conv2d_2/bias/v/Read/ReadVariableOp*Adam/conv2d_3/kernel/v/Read/ReadVariableOp(Adam/conv2d_3/bias/v/Read/ReadVariableOp'Adam/dense/kernel/v/Read/ReadVariableOp%Adam/dense/bias/v/Read/ReadVariableOp)Adam/dense_1/kernel/v/Read/ReadVariableOp'Adam/dense_1/bias/v/Read/ReadVariableOp)Adam/dense_2/kernel/v/Read/ReadVariableOp'Adam/dense_2/bias/v/Read/ReadVariableOpConst*@
        Tin9
        725  *
        Tout
        2*
        _output_shapes
        : *
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
        
        GPU 2J 8*'
        f"R
        __inference__traced_save_45214
        �
        StatefulPartitionedCall_2StatefulPartitionedCallsaver_filename
        conv2d/kernel conv2d/biasconv2d_1/kernel
        conv2d_1/biasconv2d_2/kernel
        conv2d_2/biasconv2d_3/kernel
            conv2d_3/bias dense/kernel
        dense/biasdense_1/kernel dense_1/biasdense_2/kernel dense_2/bias  Adam/iter Adam/beta_1 Adam/beta_2
        Adam/decayAdam/learning_ratetotalcounttotal_1count_1Adam/conv2d/kernel/mAdam/conv2d/bias/mAdam/conv2d_1/kernel/mAdam/conv2d_1/bias/mAdam/conv2d_2/kernel/mAdam/conv2d_2/bias/mAdam/conv2d_3/kernel/mAdam/conv2d_3/bias/mAdam/dense/kernel/mAdam/dense/bias/mAdam/dense_1/kernel/mAdam/dense_1/bias/mAdam/dense_2/kernel/mAdam/dense_2/bias/mAdam/conv2d/kernel/vAdam/conv2d/bias/vAdam/conv2d_1/kernel/vAdam/conv2d_1/bias/vAdam/conv2d_2/kernel/vAdam/conv2d_2/bias/vAdam/conv2d_3/kernel/vAdam/conv2d_3/bias/vAdam/dense/kernel/vAdam/dense/bias/vAdam/dense_1/kernel/vAdam/dense_1/bias/vAdam/dense_2/kernel/vAdam/dense_2/bias/v*?
        Tin8
        624*
        Tout
        2*
        _output_shapes
        :  *
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
        
        GPU 2J 8**
        f%R#
        !__inference__traced_restore_45379��
        �
        }
        (__inference_conv2d_3_layer_call_fn_44407

            inputs
            unknown
        unknown_0
        identity��StatefulPartitionedCall�
        StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0*
        Tin
        2*
            Tout
        2*B
        _output_shapes0
        .:,  ��������� ��������� ����������*$
        _read_only_resource_inputs
        **
        config_proto
        
        CPU
        
        GPU 2J 8*L
        fGRE
        C__inference_conv2d_3_layer_call_and_return_conditional_losses_443972
        StatefulPartitionedCall�
        IdentityIdentity StatefulPartitionedCall:output: 0^StatefulPartitionedCall*
        T0*B
        _output_shapes0
        .:,  ��������� ��������� ����������2

        Identity"
        identityIdentity: output: 0*I

        _input_shapes8
            6:,  ��������� ��������� ����������: : 22
        StatefulPartitionedCallStatefulPartitionedCall: j f
        B
        _output_shapes0
        .:,  ��������� ��������� ����������

        _user_specified_nameinputs: 
        
        _output_shapes
            :  : 
        
        _output_shapes
        : 
        �1
        �
        E__inference_sequential_layer_call_and_return_conditional_losses_44631

        inputs
        conv2d_44591
        conv2d_44593
        conv2d_1_44597
        conv2d_1_44599
        conv2d_2_44603
        conv2d_2_44605
        conv2d_3_44608
        conv2d_3_44610
        dense_44615
        dense_44617

        dense_1_44620

        dense_1_44622

        dense_2_44625

        dense_2_44627
        identity��conv2d/StatefulPartitionedCall� conv2d_1/StatefulPartitionedCall� conv2d_2/StatefulPartitionedCall� conv2d_3/StatefulPartitionedCall�dense/StatefulPartitionedCall�dense_1/StatefulPartitionedCall�dense_2/StatefulPartitionedCall�
        conv2d/StatefulPartitionedCallStatefulPartitionedCallinputs conv2d_44591 conv2d_44593*
        Tin
        2*
        Tout
        2*/
        _output_shapes
        : ���������?? *$
        _read_only_resource_inputs
        **
            config_proto
        
        CPU
        
        GPU 2J 8*J
        fERC
            A__inference_conv2d_layer_call_and_return_conditional_losses_443072
            conv2d/StatefulPartitionedCall�
        max_pooling2d/PartitionedCallPartitionedCall'conv2d/StatefulPartitionedCall:output:0*
        Tin
        2*
        Tout
            2*/
        _output_shapes
            :  ��������� *
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
        
        GPU 2J 8*Q
        fLRJ
        H__inference_max_pooling2d_layer_call_and_return_conditional_losses_443232
        max_pooling2d/PartitionedCall�
        conv2d_1/StatefulPartitionedCallStatefulPartitionedCall&max_pooling2d/PartitionedCall: output: 0conv2d_1_44597conv2d_1_44599*
        Tin
        2*
        Tout
        2*0
        _output_shapes
        :  ����������*$
        _read_only_resource_inputs
        **
        config_proto
        
        CPU
        
        GPU 2J 8*L
        fGRE
        C__inference_conv2d_1_layer_call_and_return_conditional_losses_443412"
            conv2d_1/StatefulPartitionedCall�
            max_pooling2d_1/PartitionedCallPartitionedCall)conv2d_1/StatefulPartitionedCall: output: 0*
        Tin
        2*
        Tout
        2*0
        _output_shapes
        : ����������*
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
            
            GPU 2J 8*S
        fNRL
        J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_443572!
        max_pooling2d_1/PartitionedCall�
        conv2d_2/StatefulPartitionedCallStatefulPartitionedCall(max_pooling2d_1/PartitionedCall: output: 0conv2d_2_44603conv2d_2_44605*
        Tin
        2*
        Tout
        2*0
        _output_shapes
        : ���������  �*$
        _read_only_resource_inputs
        **
            config_proto
        
        CPU
        
        GPU 2J 8*L
        fGRE
            C__inference_conv2d_2_layer_call_and_return_conditional_losses_443752"
            conv2d_2/StatefulPartitionedCall�
        conv2d_3/StatefulPartitionedCallStatefulPartitionedCall)conv2d_2/StatefulPartitionedCall: output: 0conv2d_3_44608conv2d_3_44610*
        Tin
        2*
        Tout
        2*0
            _output_shapes
        :  ���������
        
        �*$
        _read_only_resource_inputs
        **
        config_proto
        
        CPU
        
        GPU 2J 8*L
        fGRE
        C__inference_conv2d_3_layer_call_and_return_conditional_losses_443972"
        conv2d_3/StatefulPartitionedCall�
        max_pooling2d_2/PartitionedCallPartitionedCall)conv2d_3/StatefulPartitionedCall: output: 0*
        Tin
        2*
        Tout
            2*0
        _output_shapes
            :  ����������*
        _read_only_resource_inputs
         **
        config_proto
            
            CPU
        
            GPU 2J 8*S
        fNRL
        J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_444132!
        max_pooling2d_2/PartitionedCall�
        flatten/PartitionedCallPartitionedCall(max_pooling2d_2/PartitionedCall: output: 0*
        Tin
        2*
        Tout
        2*(
        _output_shapes
        :  ����������*
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
            
            GPU 2J 8*K
        fFRD
        B__inference_flatten_layer_call_and_return_conditional_losses_444522
        flatten/PartitionedCall�
        dense/StatefulPartitionedCallStatefulPartitionedCall flatten/PartitionedCall: output: 0 dense_44615 dense_44617*
        Tin
        2*
        Tout
        2*(
        _output_shapes
        :  ����������*$
        _read_only_resource_inputs
        **
        config_proto
        
        CPU
        
        GPU 2J 8*I
        fDRB
        @__inference_dense_layer_call_and_return_conditional_losses_444712
        dense/StatefulPartitionedCall�
        dense_1/StatefulPartitionedCallStatefulPartitionedCall&dense/StatefulPartitionedCall: output: 0
        dense_1_44620
        dense_1_44622*
        Tin
        2*
        Tout
        2*'
        _output_shapes
        :  ���������`*$
        _read_only_resource_inputs
        **
        config_proto
        
            CPU
            
            GPU 2J 8*K
        fFRD
        B__inference_dense_1_layer_call_and_return_conditional_losses_444982!
        dense_1/StatefulPartitionedCall�
        dense_2/StatefulPartitionedCallStatefulPartitionedCall(dense_1/StatefulPartitionedCall: output: 0
        dense_2_44625
        dense_2_44627*
            Tin
            2*
        Tout
        2*'
        _output_shapes
        : ���������*$
        _read_only_resource_inputs
        **
        config_proto
            
            CPU
        
            GPU 2J 8*K
        fFRD
        B__inference_dense_2_layer_call_and_return_conditional_losses_445252!
        dense_2/StatefulPartitionedCall�
        IdentityIdentity(dense_2/StatefulPartitionedCall: output: 0^conv2d/StatefulPartitionedCall!^conv2d_1/StatefulPartitionedCall!^conv2d_2/StatefulPartitionedCall!^conv2d_3/StatefulPartitionedCall^dense/StatefulPartitionedCall ^dense_1/StatefulPartitionedCall ^dense_2/StatefulPartitionedCall*
        T0*'
        _output_shapes
            :  ���������2

        Identity"
        identityIdentity: output: 0*h

        _input_shapesW
        U:  �����������: : : : : : : : : : : : : : 2@
        conv2d/StatefulPartitionedCallconv2d/StatefulPartitionedCall2D
        conv2d_1/StatefulPartitionedCall conv2d_1/StatefulPartitionedCall2D
        conv2d_2/StatefulPartitionedCall conv2d_2/StatefulPartitionedCall2D
        conv2d_3/StatefulPartitionedCall conv2d_3/StatefulPartitionedCall2 >
        dense/StatefulPartitionedCalldense/StatefulPartitionedCall2B
        dense_1/StatefulPartitionedCalldense_1/StatefulPartitionedCall2B
        dense_2/StatefulPartitionedCalldense_2/StatefulPartitionedCall: Y U
        1
        _output_shapes
        :  �����������

        _user_specified_nameinputs: 
        
        _output_shapes
            :  : 
        
        _output_shapes
        : : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  :
        
        _output_shapes
        :  : 
            
        _output_shapes
            :  : 
        
        _output_shapes
        : : 
        
        _output_shapes
        :  :   
        
            _output_shapes
            :  : 
        
        
        _output_shapes
        : :  
        
        _output_shapes
        :  :  
        
        _output_shapes
            :  : 
        
            
            _output_shapes
            :  : 
        
        _output_shapes
        :  
        �

        �
        C__inference_conv2d_1_layer_call_and_return_conditional_losses_44341

        inputs"
        conv2d_readvariableop_resource#
        biasadd_readvariableop_resource
        identity��
        Conv2D/ReadVariableOpReadVariableOpconv2d_readvariableop_resource*'
        _output_shapes
        :  �*
        dtype02
        Conv2D/ReadVariableOp�
        Conv2DConv2DinputsConv2D/ReadVariableOp:value: 0*
        T0*B
        _output_shapes0
        .:, ��������� ��������� ����������*
        paddingVALID*
        strides
        2
        Conv2D�
        BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02
        BiasAdd/ReadVariableOp�
        BiasAddBiasAddConv2D: output:0BiasAdd/ReadVariableOp: value: 0*
        T0*B
        _output_shapes0
            .:,  ��������� ��������� ����������2
        BiasAdds
        ReluReluBiasAdd: output: 0*
        T0*B
        _output_shapes0
        .:,  ��������� ��������� ����������2
        Relu�
        IdentityIdentityRelu: activations: 0*
        T0*B
        _output_shapes0
        .:,  ��������� ��������� ����������2

        Identity"
        identityIdentity:output: 0*H

        _input_shapes7
        5:+ ��������� ��������� ��������� : : : i e
        A
        _output_shapes/
        -: + ��������� ��������� ���������

        _user_specified_nameinputs: 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  
        �
        C
        '__inference_flatten_layer_call_fn_44974

        inputs
            identity�
        PartitionedCallPartitionedCallinputs*
        Tin
        2*
        Tout
            2*(
            _output_shapes
                :  ����������*
            _read_only_resource_inputs
             **
            config_proto
            
            CPU
            
            GPU 2J 8*K
            fFRD
            B__inference_flatten_layer_call_and_return_conditional_losses_444522
            PartitionedCallm
            IdentityIdentityPartitionedCall: output: 0*
            T0*(
            _output_shapes
        :  ����������2

        Identity"
        identityIdentity: output: 0*/

        _input_shapes
        :  ����������: X T
        0
        _output_shapes
        : ����������

        _user_specified_nameinputs
        �
        d
        H__inference_max_pooling2d_layer_call_and_return_conditional_losses_44323

        inputs
        identity�
        MaxPoolMaxPoolinputs*J
        _output_shapes8
            6: 4 ��������� ��������� ��������� ���������*
        ksize
        *
        paddingVALID*
            strides
        2
        MaxPool�
        IdentityIdentityMaxPool: output: 0*
        T0*J
            _output_shapes8
        6: 4 ��������� ��������� ��������� ���������2

        Identity"
        identityIdentity: output: 0*I

        _input_shapes8
        6: 4 ��������� ��������� ��������� ���������: r n
        J
        _output_shapes8
        6: 4 ��������� ��������� ��������� ���������

        _user_specified_nameinputs
        �
        ^
        B__inference_flatten_layer_call_and_return_conditional_losses_44452

        inputs
        identity_
        ConstConst*
        _output_shapes
        :*
        dtype0*
        valueB"����   2
        Consth
        ReshapeReshapeinputsConst: output: 0*
        T0*(
            _output_shapes
        :  ����������2
        Reshapee
        IdentityIdentityReshape: output: 0*
        T0*(
        _output_shapes
        :  ����������2

        Identity"
        identityIdentity: output: 0*/

        _input_shapes
        :  ����������:X T
        0
        _output_shapes
        :  ����������

        _user_specified_nameinputs
        �?
        �
        E__inference_sequential_layer_call_and_return_conditional_losses_44897

        inputs)
        %conv2d_conv2d_readvariableop_resource*
        &conv2d_biasadd_readvariableop_resource+
        'conv2d_1_conv2d_readvariableop_resource,
        (conv2d_1_biasadd_readvariableop_resource+
            'conv2d_2_conv2d_readvariableop_resource,
            (conv2d_2_biasadd_readvariableop_resource+
        'conv2d_3_conv2d_readvariableop_resource,
        (conv2d_3_biasadd_readvariableop_resource(
        $dense_matmul_readvariableop_resource)
            %dense_biasadd_readvariableop_resource*
        &dense_1_matmul_readvariableop_resource+
        'dense_1_biasadd_readvariableop_resource*
        &dense_2_matmul_readvariableop_resource+
        'dense_2_biasadd_readvariableop_resource
        identity��
        conv2d/Conv2D/ReadVariableOpReadVariableOp%conv2d_conv2d_readvariableop_resource*&
        _output_shapes
            :  *
            dtype02
            conv2d/Conv2D/ReadVariableOp�

            conv2d/Conv2DConv2Dinputs$conv2d/Conv2D/ReadVariableOp: value: 0*
            T0*/
            _output_shapes
                :  ���������?? *
        paddingVALID*
        strides
        2

        conv2d/Conv2D�
        conv2d/BiasAdd/ReadVariableOpReadVariableOp&conv2d_biasadd_readvariableop_resource*
        _output_shapes
        : *
        dtype02
        conv2d/BiasAdd/ReadVariableOp�
        conv2d/BiasAddBiasAddconv2d/Conv2D: output: 0%conv2d/BiasAdd/ReadVariableOp: value: 0*
        T0*/
        _output_shapes
        :  ���������?? 2
        conv2d/BiasAddu
        conv2d/ReluReluconv2d/BiasAdd: output: 0*
        T0*/
        _output_shapes
        :  ���������?? 2
        conv2d/Relu�
        max_pooling2d/MaxPoolMaxPoolconv2d/Relu: activations: 0*/
        _output_shapes
        :  ��������� *
        ksize
        *
        paddingVALID*
        strides
        2
        max_pooling2d/MaxPool�
        conv2d_1/Conv2D/ReadVariableOpReadVariableOp'conv2d_1_conv2d_readvariableop_resource*'
        _output_shapes
        :  �*
        dtype02
        conv2d_1/Conv2D/ReadVariableOp�
        conv2d_1/Conv2DConv2Dmax_pooling2d/MaxPool: output: 0&conv2d_1/Conv2D/ReadVariableOp: value: 0*
        T0*0
        _output_shapes
        :  ����������*
        paddingVALID*
        strides
        2
        conv2d_1/Conv2D�
        conv2d_1/BiasAdd/ReadVariableOpReadVariableOp(conv2d_1_biasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02!
        conv2d_1/BiasAdd/ReadVariableOp�
        conv2d_1/BiasAddBiasAddconv2d_1/Conv2D: output:0'conv2d_1/BiasAdd/ReadVariableOp:value:0*
        T0*0
        _output_shapes
        :  ����������2
        conv2d_1/BiasAdd|

        conv2d_1/ReluReluconv2d_1/BiasAdd: output: 0*
        T0*0
        _output_shapes
        :  ����������2

        conv2d_1/Relu�
            max_pooling2d_1/MaxPoolMaxPoolconv2d_1/Relu: activations: 0*0
        _output_shapes
        : ����������*
        ksize
        *
        paddingVALID*
        strides
            2
            max_pooling2d_1/MaxPool�
            conv2d_2/Conv2D/ReadVariableOpReadVariableOp'conv2d_2_conv2d_readvariableop_resource*(
            _output_shapes
            : ��*
            dtype02
            conv2d_2/Conv2D/ReadVariableOp�
            conv2d_2/Conv2DConv2D max_pooling2d_1/MaxPool: output: 0&conv2d_2/Conv2D/ReadVariableOp: value: 0*
            T0*0
            _output_shapes
            :  ���������  �*
            paddingVALID*
            strides
                2
            conv2d_2/Conv2D�
            conv2d_2/BiasAdd/ReadVariableOpReadVariableOp(conv2d_2_biasadd_readvariableop_resource*
            _output_shapes
            : �*
            dtype02!
            conv2d_2/BiasAdd/ReadVariableOp�
            conv2d_2/BiasAddBiasAddconv2d_2/Conv2D: output: 0'conv2d_2/BiasAdd/ReadVariableOp:value:0*
            T0*0
            _output_shapes
            :  ���������  �2
            conv2d_2/BiasAdd|

            conv2d_2/ReluReluconv2d_2/BiasAdd: output: 0*
            T0*0
            _output_shapes
            :  ���������  �2

            conv2d_2/Relu�
            conv2d_3/Conv2D/ReadVariableOpReadVariableOp'conv2d_3_conv2d_readvariableop_resource*(
            _output_shapes
            : ��*
            dtype02
            conv2d_3/Conv2D/ReadVariableOp�
            conv2d_3/Conv2DConv2Dconv2d_2/Relu: activations: 0&conv2d_3/Conv2D/ReadVariableOp: value: 0*
            T0*0
            _output_shapes
                :  ���������
            
            �*
            paddingVALID*
            strides
            2
            conv2d_3/Conv2D�
            conv2d_3/BiasAdd/ReadVariableOpReadVariableOp(conv2d_3_biasadd_readvariableop_resource*
            _output_shapes
            :�*
            dtype02!
            conv2d_3/BiasAdd/ReadVariableOp�
            conv2d_3/BiasAddBiasAddconv2d_3/Conv2D: output: 0'conv2d_3/BiasAdd/ReadVariableOp:value:0*
            T0*0
            _output_shapes
                :  ���������
            
            �2
            conv2d_3/BiasAdd|

            conv2d_3/ReluReluconv2d_3/BiasAdd:output: 0*
            T0*0
            _output_shapes
            :  ���������
            
            �2

            conv2d_3/Relu�
            max_pooling2d_2/MaxPoolMaxPoolconv2d_3/Relu: activations: 0*0
            _output_shapes
            :  ����������*
            ksize
            *
            paddingVALID*
            strides
            2
            max_pooling2d_2/MaxPoolo

            flatten/ConstConst*
            _output_shapes
            : *
            dtype0*
            valueB"����   2

            flatten/Const�
            flatten/ReshapeReshape max_pooling2d_2/MaxPool: output: 0flatten/Const: output: 0*
            T0*(
            _output_shapes
            :  ����������2
            flatten/Reshape�
            dense/MatMul/ReadVariableOpReadVariableOp$dense_matmul_readvariableop_resource*
            _output_shapes
            :
            ��*
            dtype02
            dense/MatMul/ReadVariableOp�
            dense/MatMulMatMulflatten/Reshape: output:0#dense/MatMul/ReadVariableOp:value:0*
            T0*(
            _output_shapes
                :  ����������2
            dense/MatMul�
            dense/BiasAdd/ReadVariableOpReadVariableOp%dense_biasadd_readvariableop_resource*
            _output_shapes
            : �*
            dtype02
        dense/BiasAdd/ReadVariableOp�

        dense/BiasAddBiasAdddense/MatMul: product: 0$dense/BiasAdd/ReadVariableOp: value:0*
        T0*(
        _output_shapes
        :  ����������2

        dense/BiasAddk

        dense/ReluReludense/BiasAdd: output: 0*
        T0*(
        _output_shapes
        :  ����������2

        dense/Relu�
        dense_1/MatMul/ReadVariableOpReadVariableOp&dense_1_matmul_readvariableop_resource*
        _output_shapes
        : �`*
        dtype02
        dense_1/MatMul/ReadVariableOp�
        dense_1/MatMulMatMuldense/Relu: activations: 0%dense_1/MatMul/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
            :  ���������`2
        dense_1/MatMul�
        dense_1/BiasAdd/ReadVariableOpReadVariableOp'dense_1_biasadd_readvariableop_resource*
        _output_shapes
        : `*
        dtype02
        dense_1/BiasAdd/ReadVariableOp�
        dense_1/BiasAddBiasAdddense_1/MatMul: product: 0&dense_1/BiasAdd/ReadVariableOp: value: 0*
        T0*'
            _output_shapes
        :  ���������`2
        dense_1/BiasAddp
        dense_1/ReluReludense_1/BiasAdd: output: 0*
        T0*'
        _output_shapes
        : ���������`2
        dense_1/Relu�
        dense_2/MatMul/ReadVariableOpReadVariableOp&dense_2_matmul_readvariableop_resource*
        _output_shapes

        : `*
        dtype02
        dense_2/MatMul/ReadVariableOp�
        dense_2/MatMulMatMuldense_1/Relu: activations: 0%dense_2/MatMul/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
        :  ���������2
        dense_2/MatMul�
        dense_2/BiasAdd/ReadVariableOpReadVariableOp'dense_2_biasadd_readvariableop_resource*
        _output_shapes
            : *
        dtype02
        dense_2/BiasAdd/ReadVariableOp�
        dense_2/BiasAddBiasAdddense_2/MatMul: product: 0&dense_2/BiasAdd/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
        :  ���������2
        dense_2/BiasAddy
            dense_2/SoftmaxSoftmaxdense_2/BiasAdd: output: 0*
        T0*'
        _output_shapes
        :  ���������2
        dense_2/Softmaxm
        IdentityIdentitydense_2/Softmax: softmax: 0*
        T0*'
        _output_shapes
        :  ���������2

        Identity"
        identityIdentity:output: 0*h

        _input_shapesW
        U: �����������: : : :: : : : :: : : : :: Y U
        1
        _output_shapes
            :  �����������

        _user_specified_nameinputs: 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  :   
        
        _output_shapes
        :  : 
        
        
            _output_shapes
            :  :  
        
        _output_shapes
        : :  
        
        _output_shapes
        :  : 
        
        
        _output_shapes
            :  : 
        
        _output_shapes
        : 
        �K
        �
        __inference__wrapped_model_44295
        conv2d_input4
        0sequential_conv2d_conv2d_readvariableop_resource5
        1sequential_conv2d_biasadd_readvariableop_resource6
        2sequential_conv2d_1_conv2d_readvariableop_resource7
        3sequential_conv2d_1_biasadd_readvariableop_resource6
        2sequential_conv2d_2_conv2d_readvariableop_resource7
        3sequential_conv2d_2_biasadd_readvariableop_resource6
        2sequential_conv2d_3_conv2d_readvariableop_resource7
        3sequential_conv2d_3_biasadd_readvariableop_resource3
        /sequential_dense_matmul_readvariableop_resource4
        0sequential_dense_biasadd_readvariableop_resource5
        1sequential_dense_1_matmul_readvariableop_resource6
        2sequential_dense_1_biasadd_readvariableop_resource5
        1sequential_dense_2_matmul_readvariableop_resource6
        2sequential_dense_2_biasadd_readvariableop_resource
        identity��
        'sequential/conv2d/Conv2D/ReadVariableOpReadVariableOp0sequential_conv2d_conv2d_readvariableop_resource*&
        _output_shapes
        :  *
        dtype02)
            'sequential/conv2d/Conv2D/ReadVariableOp�
            sequential/conv2d/Conv2DConv2D conv2d_input/sequential/conv2d/Conv2D/ReadVariableOp:value: 0*
        T0*/
        _output_shapes
        :  ���������?? *
        paddingVALID*
        strides
        2
        sequential/conv2d/Conv2D�
        (sequential/conv2d/BiasAdd/ReadVariableOpReadVariableOp1sequential_conv2d_biasadd_readvariableop_resource*
        _output_shapes
        :  *
        dtype02*
        (sequential/conv2d/BiasAdd/ReadVariableOp�
        sequential/conv2d/BiasAddBiasAdd!sequential/conv2d/Conv2D: output: 00sequential/conv2d/BiasAdd/ReadVariableOp: value: 0*
        T0*/
        _output_shapes
        :  ���������?? 2
        sequential/conv2d/BiasAdd�
        sequential/conv2d/ReluRelu"sequential/conv2d/BiasAdd:output:0*
        T0*/
        _output_shapes
        :  ���������?? 2
        sequential/conv2d/Relu�
        sequential/max_pooling2d/MaxPoolMaxPool$sequential/conv2d/Relu: activations: 0*/
        _output_shapes
        :  ��������� *
        ksize
        *
        paddingVALID*
        strides
        2"
        sequential/max_pooling2d/MaxPool�
        )sequential/conv2d_1/Conv2D/ReadVariableOpReadVariableOp2sequential_conv2d_1_conv2d_readvariableop_resource*'
        _output_shapes
        :  �*
        dtype02+
        )sequential/conv2d_1/Conv2D/ReadVariableOp�
        sequential/conv2d_1/Conv2DConv2D)sequential/max_pooling2d/MaxPool: output:01sequential/conv2d_1/Conv2D/ReadVariableOp: value: 0*
        T0*0
        _output_shapes
        : ����������*
        paddingVALID*
        strides
        2
        sequential/conv2d_1/Conv2D�
            *sequential/conv2d_1/BiasAdd/ReadVariableOpReadVariableOp3sequential_conv2d_1_biasadd_readvariableop_resource*
        _output_shapes
        :�*
        dtype02,
        *sequential/conv2d_1/BiasAdd/ReadVariableOp�
        sequential/conv2d_1/BiasAddBiasAdd#sequential/conv2d_1/Conv2D:output:02sequential/conv2d_1/BiasAdd/ReadVariableOp:value:0*
        T0*0
        _output_shapes
        : ����������2
        sequential/conv2d_1/BiasAdd�
        sequential/conv2d_1/ReluRelu$sequential/conv2d_1/BiasAdd: output: 0*
        T0*0
        _output_shapes
        :  ����������2
        sequential/conv2d_1/Relu�
        "sequential/max_pooling2d_1/MaxPoolMaxPool&sequential/conv2d_1/Relu:activations:0*0
            _output_shapes
            :  ����������*
        ksize
        *
        paddingVALID*
        strides
        2$
        "sequential/max_pooling2d_1/MaxPool�
            )sequential/conv2d_2/Conv2D/ReadVariableOpReadVariableOp2sequential_conv2d_2_conv2d_readvariableop_resource*(
        _output_shapes
        : ��*
        dtype02+
        )sequential/conv2d_2/Conv2D/ReadVariableOp�
        sequential/conv2d_2/Conv2DConv2D+sequential/max_pooling2d_1/MaxPool: output: 01sequential/conv2d_2/Conv2D/ReadVariableOp: value: 0*
        T0*0
        _output_shapes
        :  ���������  �*
        paddingVALID*
        strides
        2
            sequential/conv2d_2/Conv2D�
        *sequential/conv2d_2/BiasAdd/ReadVariableOpReadVariableOp3sequential_conv2d_2_biasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02,
        *sequential/conv2d_2/BiasAdd/ReadVariableOp�
        sequential/conv2d_2/BiasAddBiasAdd#sequential/conv2d_2/Conv2D:output:02sequential/conv2d_2/BiasAdd/ReadVariableOp:value:0*
        T0*0
        _output_shapes
        :  ���������  �2
        sequential/conv2d_2/BiasAdd�
        sequential/conv2d_2/ReluRelu$sequential/conv2d_2/BiasAdd: output: 0*
        T0*0
        _output_shapes
        : ���������  �2
        sequential/conv2d_2/Relu�
        )sequential/conv2d_3/Conv2D/ReadVariableOpReadVariableOp2sequential_conv2d_3_conv2d_readvariableop_resource*(
        _output_shapes
        : ��*
        dtype02+
        )sequential/conv2d_3/Conv2D/ReadVariableOp�
        sequential/conv2d_3/Conv2DConv2D&sequential/conv2d_2/Relu: activations: 01sequential/conv2d_3/Conv2D/ReadVariableOp: value: 0*
        T0*0
        _output_shapes
        :  ���������
        
        �*
        paddingVALID*
        strides
            2
        sequential/conv2d_3/Conv2D�
        *sequential/conv2d_3/BiasAdd/ReadVariableOpReadVariableOp3sequential_conv2d_3_biasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02,
            *sequential/conv2d_3/BiasAdd/ReadVariableOp�
        sequential/conv2d_3/BiasAddBiasAdd#sequential/conv2d_3/Conv2D:output:02sequential/conv2d_3/BiasAdd/ReadVariableOp:value:0*
        T0*0
        _output_shapes
        :  ���������
        
        �2
        sequential/conv2d_3/BiasAdd�
        sequential/conv2d_3/ReluRelu$sequential/conv2d_3/BiasAdd: output: 0*
        T0*0
            _output_shapes
        :  ���������
        
            �2
        sequential/conv2d_3/Relu�
            "sequential/max_pooling2d_2/MaxPoolMaxPool&sequential/conv2d_3/Relu:activations:0*0
            _output_shapes
                :  ����������*
            ksize
            *
            paddingVALID*
            strides
            2$
                "sequential/max_pooling2d_2/MaxPool�
            sequential/flatten/ConstConst*
            _output_shapes
            : *
        dtype0*
        valueB"����   2
        sequential/flatten/Const�
        sequential/flatten/ReshapeReshape+sequential/max_pooling2d_2/MaxPool: output: 0!sequential/flatten/Const: output: 0*
        T0*(
        _output_shapes
        :  ����������2
        sequential/flatten/Reshape�
        &sequential/dense/MatMul/ReadVariableOpReadVariableOp/sequential_dense_matmul_readvariableop_resource*
        _output_shapes
        :
            ��*
        dtype02(
        &sequential/dense/MatMul/ReadVariableOp�
        sequential/dense/MatMulMatMul#sequential/flatten/Reshape:output:0.sequential/dense/MatMul/ReadVariableOp:value:0*
        T0*(
        _output_shapes
        :  ����������2
        sequential/dense/MatMul�
        'sequential/dense/BiasAdd/ReadVariableOpReadVariableOp0sequential_dense_biasadd_readvariableop_resource*
        _output_shapes
        :�*
        dtype02)
        'sequential/dense/BiasAdd/ReadVariableOp�
        sequential/dense/BiasAddBiasAdd!sequential/dense/MatMul: product: 0/sequential/dense/BiasAdd/ReadVariableOp: value: 0*
        T0*(
        _output_shapes
        :  ����������2
        sequential/dense/BiasAdd�
        sequential/dense/ReluRelu!sequential/dense/BiasAdd: output: 0*
        T0*(
        _output_shapes
        : ����������2
        sequential/dense/Relu�
            (sequential/dense_1/MatMul/ReadVariableOpReadVariableOp1sequential_dense_1_matmul_readvariableop_resource*
        _output_shapes
        : �`*
        dtype02*
        (sequential/dense_1/MatMul/ReadVariableOp�
        sequential/dense_1/MatMulMatMul#sequential/dense/Relu:activations:00sequential/dense_1/MatMul/ReadVariableOp:value:0*
        T0*'
        _output_shapes
        :  ���������`2
        sequential/dense_1/MatMul�
        )sequential/dense_1/BiasAdd/ReadVariableOpReadVariableOp2sequential_dense_1_biasadd_readvariableop_resource*
        _output_shapes
        : `*
            dtype02+
            )sequential/dense_1/BiasAdd/ReadVariableOp�
            sequential/dense_1/BiasAddBiasAdd#sequential/dense_1/MatMul:product:01sequential/dense_1/BiasAdd/ReadVariableOp:value:0*
            T0*'
            _output_shapes
            :  ���������`2
            sequential/dense_1/BiasAdd�
                sequential/dense_1/ReluRelu#sequential/dense_1/BiasAdd:output:0*
            T0*'
            _output_shapes
        :  ���������`2
        sequential/dense_1/Relu�
        (sequential/dense_2/MatMul/ReadVariableOpReadVariableOp1sequential_dense_2_matmul_readvariableop_resource*
        _output_shapes

        : `*
        dtype02*
        (sequential/dense_2/MatMul/ReadVariableOp�
        sequential/dense_2/MatMulMatMul%sequential/dense_1/Relu: activations: 00sequential/dense_2/MatMul/ReadVariableOp:value: 0*
        T0*'
        _output_shapes
        :  ���������2
        sequential/dense_2/MatMul�
        )sequential/dense_2/BiasAdd/ReadVariableOpReadVariableOp2sequential_dense_2_biasadd_readvariableop_resource*
        _output_shapes
        : *
        dtype02+
        )sequential/dense_2/BiasAdd/ReadVariableOp�
        sequential/dense_2/BiasAddBiasAdd#sequential/dense_2/MatMul:product:01sequential/dense_2/BiasAdd/ReadVariableOp:value:0*
        T0*'
        _output_shapes
        :  ���������2
        sequential/dense_2/BiasAdd�
        sequential/dense_2/SoftmaxSoftmax#sequential/dense_2/BiasAdd:output:0*
        T0*'
        _output_shapes
        : ���������2
        sequential/dense_2/Softmaxx
        IdentityIdentity$sequential/dense_2/Softmax:softmax: 0*
        T0*'
        _output_shapes
        :  ���������2

        Identity"
        identityIdentity: output: 0*h

        _input_shapesW
        U:  �����������: : : : :: : : : :: : : : :_ [
        1
        _output_shapes
        :  �����������
        &
        _user_specified_name conv2d_input: 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  : 
            
                _output_shapes
                :  : 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  : 
            
            _output_shapes
    :  : 
    
        _output_shapes
        :  :   
    
    _output_shapes
    : : 
    
    
    _output_shapes
    :  :  
    
    _output_shapes
        :  :  
    
    _output_shapes
    :  : 
    
    
    _output_shapes
    :  : 
        
    _output_shapes
        :  
        �
        �
    B__inference_dense_2_layer_call_and_return_conditional_losses_45025

    inputs"
    matmul_readvariableop_resource#
    biasadd_readvariableop_resource
    identity��
    MatMul/ReadVariableOpReadVariableOpmatmul_readvariableop_resource*
    _output_shapes

    : `*
    dtype02
    MatMul/ReadVariableOps
    MatMulMatMulinputsMatMul/ReadVariableOp: value: 0*
    T0*'
    _output_shapes
    :  ���������2
        MatMul�
        BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
        _output_shapes
        : *
        dtype02
            BiasAdd/ReadVariableOp�
        BiasAddBiasAddMatMul: product: 0BiasAdd/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
    :  ���������2
        BiasAdda
            SoftmaxSoftmaxBiasAdd: output: 0*
        T0*'
        _output_shapes
        :  ���������2
        Softmaxe
            IdentityIdentitySoftmax: softmax: 0*
        T0*'
        _output_shapes
    :  ���������2

        Identity"
            identityIdentity: output: 0*.

        _input_shapes
        :  ���������`: : : O K
        '
            _output_shapes
            :  ���������`

        _user_specified_nameinputs: 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  
    �1
    �
    E__inference_sequential_layer_call_and_return_conditional_losses_44707

    inputs
    conv2d_44667
    conv2d_44669
    conv2d_1_44673
    conv2d_1_44675
    conv2d_2_44679
    conv2d_2_44681
    conv2d_3_44684
    conv2d_3_44686
    dense_44691
    dense_44693

    dense_1_44696

    dense_1_44698

    dense_2_44701

    dense_2_44703
    identity��conv2d/StatefulPartitionedCall� conv2d_1/StatefulPartitionedCall� conv2d_2/StatefulPartitionedCall� conv2d_3/StatefulPartitionedCall�dense/StatefulPartitionedCall�dense_1/StatefulPartitionedCall�dense_2/StatefulPartitionedCall�
    conv2d/StatefulPartitionedCallStatefulPartitionedCallinputs conv2d_44667 conv2d_44669*
    Tin
    2*
    Tout
    2*/
    _output_shapes
    :  ���������?? *$
    _read_only_resource_inputs
    **
    config_proto
    
        CPU
        
        GPU 2J 8*J
    fERC
    A__inference_conv2d_layer_call_and_return_conditional_losses_443072
    conv2d/StatefulPartitionedCall�
    max_pooling2d/PartitionedCallPartitionedCall'conv2d/StatefulPartitionedCall:output:0*
        Tin
    2*
    Tout
    2*/
    _output_shapes
    :  ��������� *
    _read_only_resource_inputs
     **
    config_proto
    
    CPU
    
    GPU 2J 8*Q
    fLRJ
    H__inference_max_pooling2d_layer_call_and_return_conditional_losses_443232
    max_pooling2d/PartitionedCall�
    conv2d_1/StatefulPartitionedCallStatefulPartitionedCall&max_pooling2d/PartitionedCall: output: 0conv2d_1_44673conv2d_1_44675*
    Tin
    2*
    Tout
    2*0
        _output_shapes
    :  ����������*$
    _read_only_resource_inputs
        **
    config_proto
    
    CPU
    
    GPU 2J 8*L
    fGRE
    C__inference_conv2d_1_layer_call_and_return_conditional_losses_443412"
    conv2d_1/StatefulPartitionedCall�
        max_pooling2d_1/PartitionedCallPartitionedCall)conv2d_1/StatefulPartitionedCall: output: 0*
        Tin
        2*
        Tout
        2*0
        _output_shapes
        :  ����������*
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
        
        GPU 2J 8*S
        fNRL
        J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_443572!
        max_pooling2d_1/PartitionedCall�
        conv2d_2/StatefulPartitionedCallStatefulPartitionedCall(max_pooling2d_1/PartitionedCall:output: 0conv2d_2_44679conv2d_2_44681*
        Tin
        2*
        Tout
        2*0
        _output_shapes
        :  ���������  �*$
        _read_only_resource_inputs
        **
        config_proto
        
            CPU
            
            GPU 2J 8*L
        fGRE
        C__inference_conv2d_2_layer_call_and_return_conditional_losses_443752"
        conv2d_2/StatefulPartitionedCall�
        conv2d_3/StatefulPartitionedCallStatefulPartitionedCall)conv2d_2/StatefulPartitionedCall: output: 0conv2d_3_44684conv2d_3_44686*
        Tin
        2*
        Tout
        2*0
        _output_shapes
        : ���������
        
        �*$
        _read_only_resource_inputs
        **
            config_proto
        
        CPU
        
        GPU 2J 8*L
        fGRE
            C__inference_conv2d_3_layer_call_and_return_conditional_losses_443972"
            conv2d_3/StatefulPartitionedCall�
        max_pooling2d_2/PartitionedCallPartitionedCall)conv2d_3/StatefulPartitionedCall: output: 0*
        Tin
        2*
        Tout
        2*0
        _output_shapes
        :  ����������*
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
        
        GPU 2J 8*S
        fNRL
        J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_444132!
        max_pooling2d_2/PartitionedCall�
        flatten/PartitionedCallPartitionedCall(max_pooling2d_2/PartitionedCall: output: 0*
        Tin
        2*
        Tout
            2*(
            _output_shapes
                :  ����������*
            _read_only_resource_inputs
             **
            config_proto
            
            CPU
            
            GPU 2J 8*K
            fFRD
            B__inference_flatten_layer_call_and_return_conditional_losses_444522
            flatten/PartitionedCall�
            dense/StatefulPartitionedCallStatefulPartitionedCall flatten/PartitionedCall:output: 0 dense_44691 dense_44693*
            Tin
        2*
        Tout
            2*(
        _output_shapes
            :  ����������*$
        _read_only_resource_inputs
        **
            config_proto
        
        CPU
        
        GPU 2J 8*I
        fDRB
            @__inference_dense_layer_call_and_return_conditional_losses_444712
            dense/StatefulPartitionedCall�
        dense_1/StatefulPartitionedCallStatefulPartitionedCall&dense/StatefulPartitionedCall: output: 0
        dense_1_44696
        dense_1_44698*
        Tin
        2*
            Tout
        2*'
        _output_shapes
        :  ���������`*$
        _read_only_resource_inputs
        **
        config_proto
        
        CPU
        
        GPU 2J 8*K
        fFRD
        B__inference_dense_1_layer_call_and_return_conditional_losses_444982!
            dense_1/StatefulPartitionedCall�
            dense_2/StatefulPartitionedCallStatefulPartitionedCall(dense_1/StatefulPartitionedCall: output: 0
            dense_2_44701
            dense_2_44703*
            Tin
            2*
            Tout
            2*'
            _output_shapes
            :  ���������*$
            _read_only_resource_inputs
            **
            config_proto
            
            CPU
            
            GPU 2J 8*K
        fFRD
        B__inference_dense_2_layer_call_and_return_conditional_losses_445252!
        dense_2/StatefulPartitionedCall�
        IdentityIdentity(dense_2/StatefulPartitionedCall: output: 0^conv2d/StatefulPartitionedCall!^conv2d_1/StatefulPartitionedCall!^conv2d_2/StatefulPartitionedCall!^conv2d_3/StatefulPartitionedCall^dense/StatefulPartitionedCall ^dense_1/StatefulPartitionedCall ^dense_2/StatefulPartitionedCall*
        T0*'
            _output_shapes
        :  ���������2

    Identity"
    identityIdentity: output: 0*h

    _input_shapesW
    U:  �����������: : : : : : : : : : : : : : 2@
    conv2d/StatefulPartitionedCallconv2d/StatefulPartitionedCall2D
    conv2d_1/StatefulPartitionedCall conv2d_1/StatefulPartitionedCall2D
    conv2d_2/StatefulPartitionedCall conv2d_2/StatefulPartitionedCall2D
    conv2d_3/StatefulPartitionedCall conv2d_3/StatefulPartitionedCall2 >
    dense/StatefulPartitionedCalldense/StatefulPartitionedCall2B
    dense_1/StatefulPartitionedCalldense_1/StatefulPartitionedCall2B
    dense_2/StatefulPartitionedCalldense_2/StatefulPartitionedCall: Y U
    1
        _output_shapes
        :  �����������

    _user_specified_nameinputs: 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  :
    
    _output_shapes
    :  : 
        
    _output_shapes
        :  : 
    
    _output_shapes
    : : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  :
    
    _output_shapes
    :  :   
        
        _output_shapes
    :  : 
    
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  :  
    
    _output_shapes
    :  : 
    
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  
    �
    {
        &__inference_conv2d_layer_call_fn_44317

            inputs
            unknown
            unknown_0
            identity��StatefulPartitionedCall�
            StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0*
            Tin
            2*
                Tout
            2*A
            _output_shapes/
            -: + ��������� ��������� ��������� *$
            _read_only_resource_inputs
            **
            config_proto
            
            CPU
            
            GPU 2J 8*J
            fERC
            A__inference_conv2d_layer_call_and_return_conditional_losses_443072
            StatefulPartitionedCall�
            IdentityIdentity StatefulPartitionedCall: output:0^StatefulPartitionedCall*
            T0*A
            _output_shapes/
            -: + ��������� ��������� ��������� 2

            Identity"
            identityIdentity: output: 0*H

            _input_shapes7
            5: + ��������� ��������� ���������: : 22
            StatefulPartitionedCallStatefulPartitionedCall: i e
            A
            _output_shapes/
            -: + ��������� ��������� ���������

            _user_specified_nameinputs: 
            
            _output_shapes
            : : 
            
            _output_shapes
            :  
            �
            �
            B__inference_dense_1_layer_call_and_return_conditional_losses_45005

            inputs"
            matmul_readvariableop_resource#
            biasadd_readvariableop_resource
            identity��
            MatMul/ReadVariableOpReadVariableOpmatmul_readvariableop_resource*
            _output_shapes
            : �`*
            dtype02
            MatMul/ReadVariableOps
            MatMulMatMulinputsMatMul/ReadVariableOp: value: 0*
            T0*'
            _output_shapes
            : ���������`2
            MatMul�
            BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
            _output_shapes
            : `*
                dtype02
            BiasAdd/ReadVariableOp�
            BiasAddBiasAddMatMul: product: 0BiasAdd/ReadVariableOp: value: 0*
            T0*'
            _output_shapes
            : ���������`2
            BiasAddX
            ReluReluBiasAdd: output: 0*
            T0*'
            _output_shapes
            : ���������`2
            Reluf
            IdentityIdentityRelu: activations: 0*
            T0*'
            _output_shapes
            :  ���������`2

            Identity"
            identityIdentity: output:0*/

            _input_shapes
            :  ����������: : : P L
            (
                _output_shapes
                :  ����������

            _user_specified_nameinputs: 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  
            �1
                �
                E__inference_sequential_layer_call_and_return_conditional_losses_44542
                conv2d_input
            conv2d_44423
            conv2d_44425
            conv2d_1_44429
            conv2d_1_44431
            conv2d_2_44435
            conv2d_2_44437
                conv2d_3_44440
            conv2d_3_44442
            dense_44482
            dense_44484

            dense_1_44509

            dense_1_44511

            dense_2_44536

            dense_2_44538
            identity��conv2d/StatefulPartitionedCall� conv2d_1/StatefulPartitionedCall� conv2d_2/StatefulPartitionedCall� conv2d_3/StatefulPartitionedCall�dense/StatefulPartitionedCall�dense_1/StatefulPartitionedCall�dense_2/StatefulPartitionedCall�
            conv2d/StatefulPartitionedCallStatefulPartitionedCall conv2d_input conv2d_44423 conv2d_44425*
            Tin
            2*
            Tout
            2*/
            _output_shapes
            :  ���������?? *$
            _read_only_resource_inputs
            **
            config_proto
            
                CPU
                
                GPU 2J 8*J
            fERC
            A__inference_conv2d_layer_call_and_return_conditional_losses_443072
            conv2d/StatefulPartitionedCall�
            max_pooling2d/PartitionedCallPartitionedCall'conv2d/StatefulPartitionedCall:output:0*
                Tin
            2*
            Tout
            2*/
            _output_shapes
            :  ��������� *
            _read_only_resource_inputs
             **
            config_proto
            
            CPU
            
            GPU 2J 8*Q
            fLRJ
            H__inference_max_pooling2d_layer_call_and_return_conditional_losses_443232
            max_pooling2d/PartitionedCall�
            conv2d_1/StatefulPartitionedCallStatefulPartitionedCall&max_pooling2d/PartitionedCall: output: 0conv2d_1_44429conv2d_1_44431*
            Tin
            2*
            Tout
            2*0
                _output_shapes
            :  ����������*$
            _read_only_resource_inputs
                **
            config_proto
            
            CPU
            
            GPU 2J 8*L
            fGRE
            C__inference_conv2d_1_layer_call_and_return_conditional_losses_443412"
            conv2d_1/StatefulPartitionedCall�
                max_pooling2d_1/PartitionedCallPartitionedCall)conv2d_1/StatefulPartitionedCall: output: 0*
                Tin
                2*
                Tout
                2*0
                _output_shapes
                :  ����������*
                _read_only_resource_inputs
                 **
                config_proto
                
                CPU
                
                GPU 2J 8*S
            fNRL
            J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_443572!
            max_pooling2d_1/PartitionedCall�
            conv2d_2/StatefulPartitionedCallStatefulPartitionedCall(max_pooling2d_1/PartitionedCall:output: 0conv2d_2_44435conv2d_2_44437*
            Tin
            2*
            Tout
            2*0
            _output_shapes
            :  ���������  �*$
            _read_only_resource_inputs
            **
            config_proto
            
                CPU
                
                GPU 2J 8*L
            fGRE
            C__inference_conv2d_2_layer_call_and_return_conditional_losses_443752"
            conv2d_2/StatefulPartitionedCall�
            conv2d_3/StatefulPartitionedCallStatefulPartitionedCall)conv2d_2/StatefulPartitionedCall: output: 0conv2d_3_44440conv2d_3_44442*
            Tin
            2*
            Tout
            2*0
            _output_shapes
            : ���������
            
            �*$
            _read_only_resource_inputs
            **
                config_proto
            
            CPU
            
            GPU 2J 8*L
            fGRE
                C__inference_conv2d_3_layer_call_and_return_conditional_losses_443972"
                conv2d_3/StatefulPartitionedCall�
            max_pooling2d_2/PartitionedCallPartitionedCall)conv2d_3/StatefulPartitionedCall: output: 0*
            Tin
            2*
            Tout
            2*0
            _output_shapes
            :  ����������*
            _read_only_resource_inputs
             **
            config_proto
            
            CPU
            
            GPU 2J 8*S
            fNRL
            J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_444132!
            max_pooling2d_2/PartitionedCall�
            flatten/PartitionedCallPartitionedCall(max_pooling2d_2/PartitionedCall: output: 0*
            Tin
            2*
            Tout
                2*(
                _output_shapes
                    :  ����������*
                _read_only_resource_inputs
                 **
                config_proto
                
                CPU
                
                GPU 2J 8*K
                fFRD
                B__inference_flatten_layer_call_and_return_conditional_losses_444522
                flatten/PartitionedCall�
                dense/StatefulPartitionedCallStatefulPartitionedCall flatten/PartitionedCall:output: 0 dense_44482 dense_44484*
                Tin
            2*
            Tout
                2*(
            _output_shapes
                :  ����������*$
            _read_only_resource_inputs
            **
                config_proto
            
            CPU
            
            GPU 2J 8*I
            fDRB
                @__inference_dense_layer_call_and_return_conditional_losses_444712
                dense/StatefulPartitionedCall�
            dense_1/StatefulPartitionedCallStatefulPartitionedCall&dense/StatefulPartitionedCall: output: 0
            dense_1_44509
            dense_1_44511*
            Tin
            2*
                Tout
            2*'
            _output_shapes
            :  ���������`*$
            _read_only_resource_inputs
            **
            config_proto
            
            CPU
            
            GPU 2J 8*K
            fFRD
            B__inference_dense_1_layer_call_and_return_conditional_losses_444982!
                dense_1/StatefulPartitionedCall�
                dense_2/StatefulPartitionedCallStatefulPartitionedCall(dense_1/StatefulPartitionedCall: output: 0
                dense_2_44536
                dense_2_44538*
                Tin
                2*
                Tout
                2*'
                _output_shapes
                :  ���������*$
                _read_only_resource_inputs
                **
                config_proto
                
                CPU
                
                GPU 2J 8*K
            fFRD
            B__inference_dense_2_layer_call_and_return_conditional_losses_445252!
            dense_2/StatefulPartitionedCall�
            IdentityIdentity(dense_2/StatefulPartitionedCall: output: 0^conv2d/StatefulPartitionedCall!^conv2d_1/StatefulPartitionedCall!^conv2d_2/StatefulPartitionedCall!^conv2d_3/StatefulPartitionedCall^dense/StatefulPartitionedCall ^dense_1/StatefulPartitionedCall ^dense_2/StatefulPartitionedCall*
            T0*'
                _output_shapes
            :  ���������2

            Identity"
            identityIdentity: output: 0*h

            _input_shapesW
            U:  �����������: : : : : : : : : : : : : : 2@
            conv2d/StatefulPartitionedCallconv2d/StatefulPartitionedCall2D
            conv2d_1/StatefulPartitionedCall conv2d_1/StatefulPartitionedCall2D
            conv2d_2/StatefulPartitionedCall conv2d_2/StatefulPartitionedCall2D
            conv2d_3/StatefulPartitionedCall conv2d_3/StatefulPartitionedCall2 >
            dense/StatefulPartitionedCalldense/StatefulPartitionedCall2B
            dense_1/StatefulPartitionedCalldense_1/StatefulPartitionedCall2B
            dense_2/StatefulPartitionedCalldense_2/StatefulPartitionedCall: _ [
            1
                _output_shapes
                :  �����������
            &
            _user_specified_name conv2d_input: 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  :
            
            _output_shapes
            :  : 
                
            _output_shapes
                :  : 
            
            _output_shapes
            : : 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  :
            
            _output_shapes
            :  :   
                
                _output_shapes
            :  : 
            
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  :  
            
            _output_shapes
            :  : 
            
            
                _output_shapes
                :  : 
            
            _output_shapes
            :  
            �
            |
            '__inference_dense_1_layer_call_fn_45014

            inputs
            unknown
            unknown_0
            identity��StatefulPartitionedCall�
            StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0*
            Tin
            2*
            Tout
                2*'
            _output_shapes
                :  ���������`*$
            _read_only_resource_inputs
            **
            config_proto
            
            CPU
            
            GPU 2J 8*K
            fFRD
            B__inference_dense_1_layer_call_and_return_conditional_losses_444982
            StatefulPartitionedCall�
            IdentityIdentity StatefulPartitionedCall: output:0^StatefulPartitionedCall*
            T0*'
            _output_shapes
            :  ���������`2

            Identity"
            identityIdentity: output:0*/

            _input_shapes
            :  ����������: : 22
            StatefulPartitionedCallStatefulPartitionedCall: P L
            (
            _output_shapes
            :  ����������

            _user_specified_nameinputs: 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  
            �

            �
            A__inference_conv2d_layer_call_and_return_conditional_losses_44307

            inputs"
            conv2d_readvariableop_resource#
            biasadd_readvariableop_resource
            identity��
            Conv2D/ReadVariableOpReadVariableOpconv2d_readvariableop_resource*&
            _output_shapes
            :  *
            dtype02
            Conv2D/ReadVariableOp�
                Conv2DConv2DinputsConv2D/ReadVariableOp: value: 0*
            T0*A
            _output_shapes/
            -:+ ��������� ��������� ��������� *
            paddingVALID*
            strides
            2
            Conv2D�
            BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
            _output_shapes
            :  *
            dtype02
            BiasAdd/ReadVariableOp�
                BiasAddBiasAddConv2D: output: 0BiasAdd/ReadVariableOp: value: 0*
            T0*A
                _output_shapes/
            -: + ��������� ��������� ��������� 2
            BiasAddr
            ReluReluBiasAdd: output:0*
            T0*A
            _output_shapes/
            -: + ��������� ��������� ��������� 2
            Relu�
            IdentityIdentityRelu: activations: 0*
            T0*A
            _output_shapes/
            -: + ��������� ��������� ��������� 2

            Identity"
            identityIdentity: output: 0*H

            _input_shapes7
            5: + ��������� ��������� ���������: : : i e
            A
            _output_shapes/
            -: + ��������� ��������� ���������

            _user_specified_nameinputs: 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  
            �
                �
                @__inference_dense_layer_call_and_return_conditional_losses_44985

                inputs"
            matmul_readvariableop_resource#
            biasadd_readvariableop_resource
            identity��
            MatMul/ReadVariableOpReadVariableOpmatmul_readvariableop_resource*
            _output_shapes
            :
            ��*
            dtype02
                MatMul/ReadVariableOpt
            MatMulMatMulinputsMatMul/ReadVariableOp: value:0*
            T0*(
            _output_shapes
            :  ����������2
            MatMul�
            BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
            _output_shapes
            : �*
            dtype02
            BiasAdd/ReadVariableOp�
            BiasAddBiasAddMatMul:product: 0BiasAdd/ReadVariableOp: value: 0*
            T0*(
            _output_shapes
            :  ����������2
            BiasAddY
            ReluReluBiasAdd:output: 0*
            T0*(
            _output_shapes
            :  ����������2
            Relug
            IdentityIdentityRelu: activations: 0*
            T0*(
            _output_shapes
            :  ����������2

            Identity"
                identityIdentity: output: 0*/

            _input_shapes
            :  ����������: : : P L
            (
            _output_shapes
            :  ����������

            _user_specified_nameinputs: 
            
            _output_shapes
                :  : 
            
            _output_shapes
            : 
            ��
            �
            !__inference__traced_restore_45379
            file_prefix"
                assignvariableop_conv2d_kernel"
            assignvariableop_1_conv2d_bias&
            "assignvariableop_2_conv2d_1_kernel$
            assignvariableop_3_conv2d_1_bias&
            "assignvariableop_4_conv2d_2_kernel$
            assignvariableop_5_conv2d_2_bias&
            "assignvariableop_6_conv2d_3_kernel$
            assignvariableop_7_conv2d_3_bias#
                assignvariableop_8_dense_kernel!
                assignvariableop_9_dense_bias&
                    "assignvariableop_10_dense_1_kernel$
                    assignvariableop_11_dense_1_bias&
                "assignvariableop_12_dense_2_kernel$
                assignvariableop_13_dense_2_bias!
                assignvariableop_14_adam_iter#
                assignvariableop_15_adam_beta_1#
                assignvariableop_16_adam_beta_2"
                assignvariableop_17_adam_decay*
                &assignvariableop_18_adam_learning_rate
                    assignvariableop_19_total
                assignvariableop_20_count
                assignvariableop_21_total_1
                assignvariableop_22_count_1,
                (assignvariableop_23_adam_conv2d_kernel_m*
                    &assignvariableop_24_adam_conv2d_bias_m.
                *assignvariableop_25_adam_conv2d_1_kernel_m,
                (assignvariableop_26_adam_conv2d_1_bias_m.
            *assignvariableop_27_adam_conv2d_2_kernel_m,
            (assignvariableop_28_adam_conv2d_2_bias_m.
                *assignvariableop_29_adam_conv2d_3_kernel_m,
            (assignvariableop_30_adam_conv2d_3_bias_m+
            'assignvariableop_31_adam_dense_kernel_m)
            %assignvariableop_32_adam_dense_bias_m-
            )assignvariableop_33_adam_dense_1_kernel_m+
            'assignvariableop_34_adam_dense_1_bias_m-
            )assignvariableop_35_adam_dense_2_kernel_m+
            'assignvariableop_36_adam_dense_2_bias_m,
            (assignvariableop_37_adam_conv2d_kernel_v*
            &assignvariableop_38_adam_conv2d_bias_v.
            *assignvariableop_39_adam_conv2d_1_kernel_v,
            (assignvariableop_40_adam_conv2d_1_bias_v.
            *assignvariableop_41_adam_conv2d_2_kernel_v,
            (assignvariableop_42_adam_conv2d_2_bias_v.
            *assignvariableop_43_adam_conv2d_3_kernel_v,
            (assignvariableop_44_adam_conv2d_3_bias_v+
            'assignvariableop_45_adam_dense_kernel_v)
            %assignvariableop_46_adam_dense_bias_v-
            )assignvariableop_47_adam_dense_1_kernel_v+
            'assignvariableop_48_adam_dense_1_bias_v-
                )assignvariableop_49_adam_dense_2_kernel_v+
                'assignvariableop_50_adam_dense_2_bias_v
                identity_52��AssignVariableOp�AssignVariableOp_1�AssignVariableOp_10�AssignVariableOp_11�AssignVariableOp_12�AssignVariableOp_13�AssignVariableOp_14�AssignVariableOp_15�AssignVariableOp_16�AssignVariableOp_17�AssignVariableOp_18�AssignVariableOp_19�AssignVariableOp_2�AssignVariableOp_20�AssignVariableOp_21�AssignVariableOp_22�AssignVariableOp_23�AssignVariableOp_24�AssignVariableOp_25�AssignVariableOp_26�AssignVariableOp_27�AssignVariableOp_28�AssignVariableOp_29�AssignVariableOp_3�AssignVariableOp_30�AssignVariableOp_31�AssignVariableOp_32�AssignVariableOp_33�AssignVariableOp_34�AssignVariableOp_35�AssignVariableOp_36�AssignVariableOp_37�AssignVariableOp_38�AssignVariableOp_39�AssignVariableOp_4�AssignVariableOp_40�AssignVariableOp_41�AssignVariableOp_42�AssignVariableOp_43�AssignVariableOp_44�AssignVariableOp_45�AssignVariableOp_46�AssignVariableOp_47�AssignVariableOp_48�AssignVariableOp_49�AssignVariableOp_5�AssignVariableOp_50�AssignVariableOp_6�AssignVariableOp_7�AssignVariableOp_8�AssignVariableOp_9�  RestoreV2� RestoreV2_1�
            RestoreV2/tensor_namesConst"
            /device: CPU:0*
            _output_shapes
            : 3*
            dtype0*�
            value�B�3B6layer_with_weights-0/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-0/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-1/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-1/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-2/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-2/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-3/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-3/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-4/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-4/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-5/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-5/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-6/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-6/bias/.ATTRIBUTES/VARIABLE_VALUEB)optimizer/iter/.ATTRIBUTES/VARIABLE_VALUEB+optimizer/beta_1/.ATTRIBUTES/VARIABLE_VALUEB+optimizer/beta_2/.ATTRIBUTES/VARIABLE_VALUEB*optimizer/decay/.ATTRIBUTES/VARIABLE_VALUEB2optimizer/learning_rate/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/0/total/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/0/count/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/1/total/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/1/count/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-0/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-0/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-1/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-1/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-2/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-2/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-3/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-3/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-4/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-4/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-5/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-5/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-6/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-6/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-0/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-0/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-1/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-1/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-2/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-2/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-3/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-3/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-4/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-4/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-5/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-5/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-6/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-6/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE2
            RestoreV2/tensor_names�
            RestoreV2/shape_and_slicesConst"
                /device: CPU: 0*
            _output_shapes
            : 3*
                dtype0*y
            valuepBn3B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B 2
            RestoreV2/shape_and_slices�
            RestoreV2  RestoreV2 file_prefixRestoreV2/tensor_names:output: 0#RestoreV2/shape_and_slices:output:0"
            /device: CPU: 0*�
            _output_shapes�
            �: : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : *A
            dtypes7
            523  2
            RestoreV2X
            IdentityIdentityRestoreV2: tensors: 0*
            T0*
            _output_shapes
            : 2

            Identity�
            AssignVariableOpAssignVariableOpassignvariableop_conv2d_kernelIdentity: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp\

            Identity_1IdentityRestoreV2: tensors: 1*
            T0*
                _output_shapes
                : 2

                Identity_1�
                AssignVariableOp_1AssignVariableOpassignvariableop_1_conv2d_biasIdentity_1: output: 0*
                _output_shapes
                 *
                dtype02
                AssignVariableOp_1\

                Identity_2IdentityRestoreV2: tensors: 2*
                T0*
                    _output_shapes
                : 2

                Identity_2�
                AssignVariableOp_2AssignVariableOp"assignvariableop_2_conv2d_1_kernelIdentity_2:output:0*
                _output_shapes
             *
            dtype02
            AssignVariableOp_2\

            Identity_3IdentityRestoreV2: tensors: 3*
            T0*
                _output_shapes
            : 2

            Identity_3�
            AssignVariableOp_3AssignVariableOp assignvariableop_3_conv2d_1_biasIdentity_3: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_3\

            Identity_4IdentityRestoreV2: tensors: 4*
            T0*
            _output_shapes
            : 2

            Identity_4�
            AssignVariableOp_4AssignVariableOp"assignvariableop_4_conv2d_2_kernelIdentity_4:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_4\

            Identity_5IdentityRestoreV2: tensors: 5*
            T0*
            _output_shapes
            : 2

            Identity_5�
            AssignVariableOp_5AssignVariableOp assignvariableop_5_conv2d_2_biasIdentity_5: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_5\

            Identity_6IdentityRestoreV2: tensors: 6*
            T0*
            _output_shapes
            : 2

                Identity_6�
            AssignVariableOp_6AssignVariableOp"assignvariableop_6_conv2d_3_kernelIdentity_6:output:0*
                _output_shapes
             *
            dtype02
            AssignVariableOp_6\

            Identity_7IdentityRestoreV2: tensors: 7*
            T0*
            _output_shapes
            : 2

                Identity_7�
                AssignVariableOp_7AssignVariableOp assignvariableop_7_conv2d_3_biasIdentity_7: output: 0*
                _output_shapes
                 *
                    dtype02
                AssignVariableOp_7\

                    Identity_8IdentityRestoreV2: tensors: 8*
                T0*
                _output_shapes
                : 2

                Identity_8�
                AssignVariableOp_8AssignVariableOpassignvariableop_8_dense_kernelIdentity_8: output: 0*
                _output_shapes
             *
                dtype02
                AssignVariableOp_8\

                    Identity_9IdentityRestoreV2: tensors: 9*
                T0*
                _output_shapes
                : 2

                Identity_9�
                AssignVariableOp_9AssignVariableOpassignvariableop_9_dense_biasIdentity_9: output: 0*
                _output_shapes
                 *
                    dtype02
                AssignVariableOp_9_
                    Identity_10IdentityRestoreV2: tensors: 10*
                T0*
            _output_shapes
            : 2
            Identity_10�
            AssignVariableOp_10AssignVariableOp"assignvariableop_10_dense_1_kernelIdentity_10:output:0*
            _output_shapes
             *
                dtype02
            AssignVariableOp_10_
                Identity_11IdentityRestoreV2: tensors: 11*
            T0*
            _output_shapes
            : 2
            Identity_11�
            AssignVariableOp_11AssignVariableOp assignvariableop_11_dense_1_biasIdentity_11:output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_11_
            Identity_12IdentityRestoreV2: tensors: 12*
            T0*
            _output_shapes
            :2
            Identity_12�
            AssignVariableOp_12AssignVariableOp"assignvariableop_12_dense_2_kernelIdentity_12:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_12_
            Identity_13IdentityRestoreV2: tensors: 13*
            T0*
            _output_shapes
            :2
            Identity_13�
            AssignVariableOp_13AssignVariableOp assignvariableop_13_dense_2_biasIdentity_13: output: 0*
            _output_shapes
             *
            dtype02
                AssignVariableOp_13_
            Identity_14IdentityRestoreV2:tensors: 14*
            T0  *
            _output_shapes
            : 2
            Identity_14�
            AssignVariableOp_14AssignVariableOpassignvariableop_14_adam_iterIdentity_14: output:0*
            _output_shapes
             *
            dtype0  2
            AssignVariableOp_14_
            Identity_15IdentityRestoreV2: tensors:15*
            T0*
            _output_shapes
            : 2
            Identity_15�
            AssignVariableOp_15AssignVariableOpassignvariableop_15_adam_beta_1Identity_15: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_15_
            Identity_16IdentityRestoreV2: tensors:16*
            T0*
            _output_shapes
            : 2
            Identity_16�
            AssignVariableOp_16AssignVariableOpassignvariableop_16_adam_beta_2Identity_16: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_16_
            Identity_17IdentityRestoreV2: tensors:17*
            T0*
            _output_shapes
            : 2
            Identity_17�
            AssignVariableOp_17AssignVariableOpassignvariableop_17_adam_decayIdentity_17: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_17_
            Identity_18IdentityRestoreV2: tensors:18*
            T0*
            _output_shapes
            : 2
            Identity_18�
            AssignVariableOp_18AssignVariableOp&assignvariableop_18_adam_learning_rateIdentity_18: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_18_
            Identity_19IdentityRestoreV2: tensors:19*
            T0*
            _output_shapes
            : 2
            Identity_19�
            AssignVariableOp_19AssignVariableOpassignvariableop_19_totalIdentity_19: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_19_
            Identity_20IdentityRestoreV2: tensors:20*
            T0*
            _output_shapes
            : 2
            Identity_20�
            AssignVariableOp_20AssignVariableOpassignvariableop_20_countIdentity_20: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_20_
            Identity_21IdentityRestoreV2: tensors:21*
            T0*
            _output_shapes
            : 2
            Identity_21�
            AssignVariableOp_21AssignVariableOpassignvariableop_21_total_1Identity_21: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_21_
            Identity_22IdentityRestoreV2: tensors:22*
                T0*
                _output_shapes
                : 2
                Identity_22�
                AssignVariableOp_22AssignVariableOpassignvariableop_22_count_1Identity_22: output: 0*
                _output_shapes
                 *
                dtype02
                AssignVariableOp_22_
                Identity_23IdentityRestoreV2: tensors:23*
                T0*
                _output_shapes
                : 2
            Identity_23�
            AssignVariableOp_23AssignVariableOp(assignvariableop_23_adam_conv2d_kernel_mIdentity_23: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_23_
            Identity_24IdentityRestoreV2: tensors:24*
            T0*
            _output_shapes
            : 2
            Identity_24�
            AssignVariableOp_24AssignVariableOp&assignvariableop_24_adam_conv2d_bias_mIdentity_24: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_24_
            Identity_25IdentityRestoreV2: tensors:25*
            T0*
            _output_shapes
            : 2
            Identity_25�
            AssignVariableOp_25AssignVariableOp*assignvariableop_25_adam_conv2d_1_kernel_mIdentity_25: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_25_
            Identity_26IdentityRestoreV2: tensors:26*
            T0*
            _output_shapes
            : 2
            Identity_26�
            AssignVariableOp_26AssignVariableOp(assignvariableop_26_adam_conv2d_1_bias_mIdentity_26: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_26_
            Identity_27IdentityRestoreV2: tensors:27*
            T0*
            _output_shapes
            : 2
            Identity_27�
            AssignVariableOp_27AssignVariableOp*assignvariableop_27_adam_conv2d_2_kernel_mIdentity_27: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_27_
            Identity_28IdentityRestoreV2: tensors:28*
            T0*
            _output_shapes
            : 2
            Identity_28�
            AssignVariableOp_28AssignVariableOp(assignvariableop_28_adam_conv2d_2_bias_mIdentity_28: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_28_
            Identity_29IdentityRestoreV2: tensors:29*
            T0*
            _output_shapes
            : 2
            Identity_29�
            AssignVariableOp_29AssignVariableOp*assignvariableop_29_adam_conv2d_3_kernel_mIdentity_29: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_29_
            Identity_30IdentityRestoreV2: tensors:30*
            T0*
            _output_shapes
            : 2
            Identity_30�
            AssignVariableOp_30AssignVariableOp(assignvariableop_30_adam_conv2d_3_bias_mIdentity_30: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_30_
            Identity_31IdentityRestoreV2: tensors:31*
            T0*
            _output_shapes
            : 2
            Identity_31�
            AssignVariableOp_31AssignVariableOp'assignvariableop_31_adam_dense_kernel_mIdentity_31:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_31_
            Identity_32IdentityRestoreV2: tensors:32*
            T0*
            _output_shapes
            : 2
            Identity_32�
            AssignVariableOp_32AssignVariableOp%assignvariableop_32_adam_dense_bias_mIdentity_32: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_32_
            Identity_33IdentityRestoreV2: tensors:33*
            T0*
            _output_shapes
            : 2
            Identity_33�
            AssignVariableOp_33AssignVariableOp)assignvariableop_33_adam_dense_1_kernel_mIdentity_33: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_33_
            Identity_34IdentityRestoreV2: tensors:34*
            T0*
            _output_shapes
            : 2
            Identity_34�
            AssignVariableOp_34AssignVariableOp'assignvariableop_34_adam_dense_1_bias_mIdentity_34:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_34_
            Identity_35IdentityRestoreV2: tensors:35*
            T0*
            _output_shapes
            : 2
            Identity_35�
            AssignVariableOp_35AssignVariableOp)assignvariableop_35_adam_dense_2_kernel_mIdentity_35: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_35_
            Identity_36IdentityRestoreV2: tensors:36*
            T0*
            _output_shapes
            : 2
            Identity_36�
            AssignVariableOp_36AssignVariableOp'assignvariableop_36_adam_dense_2_bias_mIdentity_36:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_36_
            Identity_37IdentityRestoreV2: tensors:37*
            T0*
            _output_shapes
            : 2
            Identity_37�
            AssignVariableOp_37AssignVariableOp(assignvariableop_37_adam_conv2d_kernel_vIdentity_37: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_37_
            Identity_38IdentityRestoreV2: tensors:38*
            T0*
            _output_shapes
            : 2
            Identity_38�
            AssignVariableOp_38AssignVariableOp&assignvariableop_38_adam_conv2d_bias_vIdentity_38: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_38_
            Identity_39IdentityRestoreV2: tensors:39*
            T0*
            _output_shapes
            : 2
            Identity_39�
            AssignVariableOp_39AssignVariableOp*assignvariableop_39_adam_conv2d_1_kernel_vIdentity_39: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_39_
            Identity_40IdentityRestoreV2: tensors:40*
            T0*
            _output_shapes
            : 2
            Identity_40�
            AssignVariableOp_40AssignVariableOp(assignvariableop_40_adam_conv2d_1_bias_vIdentity_40: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_40_
            Identity_41IdentityRestoreV2: tensors:41*
                T0*
                _output_shapes
                : 2
                Identity_41�
                AssignVariableOp_41AssignVariableOp*assignvariableop_41_adam_conv2d_2_kernel_vIdentity_41: output: 0*
                _output_shapes
                 *
                dtype02
                AssignVariableOp_41_
                Identity_42IdentityRestoreV2: tensors:42*
                T0*
                _output_shapes
                : 2
            Identity_42�
            AssignVariableOp_42AssignVariableOp(assignvariableop_42_adam_conv2d_2_bias_vIdentity_42: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_42_
            Identity_43IdentityRestoreV2: tensors:43*
            T0*
            _output_shapes
            : 2
            Identity_43�
            AssignVariableOp_43AssignVariableOp*assignvariableop_43_adam_conv2d_3_kernel_vIdentity_43: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_43_
            Identity_44IdentityRestoreV2: tensors:44*
            T0*
            _output_shapes
            : 2
            Identity_44�
            AssignVariableOp_44AssignVariableOp(assignvariableop_44_adam_conv2d_3_bias_vIdentity_44: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_44_
            Identity_45IdentityRestoreV2: tensors:45*
            T0*
            _output_shapes
            : 2
            Identity_45�
            AssignVariableOp_45AssignVariableOp'assignvariableop_45_adam_dense_kernel_vIdentity_45:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_45_
            Identity_46IdentityRestoreV2: tensors:46*
            T0*
            _output_shapes
            : 2
            Identity_46�
            AssignVariableOp_46AssignVariableOp%assignvariableop_46_adam_dense_bias_vIdentity_46: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_46_
            Identity_47IdentityRestoreV2: tensors:47*
            T0*
            _output_shapes
            : 2
            Identity_47�
            AssignVariableOp_47AssignVariableOp)assignvariableop_47_adam_dense_1_kernel_vIdentity_47: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_47_
            Identity_48IdentityRestoreV2: tensors:48*
            T0*
            _output_shapes
            : 2
            Identity_48�
            AssignVariableOp_48AssignVariableOp'assignvariableop_48_adam_dense_1_bias_vIdentity_48:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_48_
            Identity_49IdentityRestoreV2: tensors:49*
            T0*
            _output_shapes
            : 2
            Identity_49�
            AssignVariableOp_49AssignVariableOp)assignvariableop_49_adam_dense_2_kernel_vIdentity_49: output: 0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_49_
            Identity_50IdentityRestoreV2: tensors:50*
            T0*
            _output_shapes
            : 2
            Identity_50�
            AssignVariableOp_50AssignVariableOp'assignvariableop_50_adam_dense_2_bias_vIdentity_50:output:0*
            _output_shapes
             *
            dtype02
            AssignVariableOp_50�
            RestoreV2_1/tensor_namesConst"
            /device: CPU: 0*
            _output_shapes
            : *
            dtype0*1
            value(B&B_CHECKPOINTABLE_OBJECT_GRAPH2
            RestoreV2_1/tensor_names�
            RestoreV2_1/shape_and_slicesConst"
            /device: CPU: 0*
                _output_shapes
            : *
            dtype0*
                value B
            B 2
            RestoreV2_1/shape_and_slices�
            RestoreV2_1  RestoreV2 file_prefix!RestoreV2_1/tensor_names: output: 0%RestoreV2_1/shape_and_slices: output: 0
            ^RestoreV2"
            /device: CPU: 0*
            _output_shapes
            : *
                dtypes
            22
            RestoreV2_19
            NoOpNoOp"
            /device: CPU:0*
            _output_shapes
             2
            NoOp�
                Identity_51Identity file_prefix^AssignVariableOp^AssignVariableOp_1^AssignVariableOp_10^AssignVariableOp_11^AssignVariableOp_12^AssignVariableOp_13^AssignVariableOp_14^AssignVariableOp_15^AssignVariableOp_16^AssignVariableOp_17^AssignVariableOp_18^AssignVariableOp_19^AssignVariableOp_2^AssignVariableOp_20^AssignVariableOp_21^AssignVariableOp_22^AssignVariableOp_23^AssignVariableOp_24^AssignVariableOp_25^AssignVariableOp_26^AssignVariableOp_27^AssignVariableOp_28^AssignVariableOp_29^AssignVariableOp_3^AssignVariableOp_30^AssignVariableOp_31^AssignVariableOp_32^AssignVariableOp_33^AssignVariableOp_34^AssignVariableOp_35^AssignVariableOp_36^AssignVariableOp_37^AssignVariableOp_38^AssignVariableOp_39^AssignVariableOp_4^AssignVariableOp_40^AssignVariableOp_41^AssignVariableOp_42^AssignVariableOp_43^AssignVariableOp_44^AssignVariableOp_45^AssignVariableOp_46^AssignVariableOp_47^AssignVariableOp_48^AssignVariableOp_49^AssignVariableOp_5^AssignVariableOp_50^AssignVariableOp_6^AssignVariableOp_7^AssignVariableOp_8^AssignVariableOp_9^NoOp"
            /device: CPU: 0*
            T0*
            _output_shapes
            :  2
            Identity_51�
            Identity_52IdentityIdentity_51: output: 0^AssignVariableOp^AssignVariableOp_1^AssignVariableOp_10^AssignVariableOp_11^AssignVariableOp_12^AssignVariableOp_13^AssignVariableOp_14^AssignVariableOp_15^AssignVariableOp_16^AssignVariableOp_17^AssignVariableOp_18^AssignVariableOp_19^AssignVariableOp_2^AssignVariableOp_20^AssignVariableOp_21^AssignVariableOp_22^AssignVariableOp_23^AssignVariableOp_24^AssignVariableOp_25^AssignVariableOp_26^AssignVariableOp_27^AssignVariableOp_28^AssignVariableOp_29^AssignVariableOp_3^AssignVariableOp_30^AssignVariableOp_31^AssignVariableOp_32^AssignVariableOp_33^AssignVariableOp_34^AssignVariableOp_35^AssignVariableOp_36^AssignVariableOp_37^AssignVariableOp_38^AssignVariableOp_39^AssignVariableOp_4^AssignVariableOp_40^AssignVariableOp_41^AssignVariableOp_42^AssignVariableOp_43^AssignVariableOp_44^AssignVariableOp_45^AssignVariableOp_46^AssignVariableOp_47^AssignVariableOp_48^AssignVariableOp_49^AssignVariableOp_5^AssignVariableOp_50^AssignVariableOp_6^AssignVariableOp_7^AssignVariableOp_8^AssignVariableOp_9
            ^RestoreV2 ^RestoreV2_1*
            T0*
            _output_shapes
            :  2
            Identity_52"#
            identity_52Identity_52: output: 0*�

            _input_shapes�
            �:  : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : : 2$
        AssignVariableOpAssignVariableOp2(
        AssignVariableOp_1AssignVariableOp_12*
        AssignVariableOp_10AssignVariableOp_102*
        AssignVariableOp_11AssignVariableOp_112*
            AssignVariableOp_12AssignVariableOp_122*
            AssignVariableOp_13AssignVariableOp_132*
            AssignVariableOp_14AssignVariableOp_142*
            AssignVariableOp_15AssignVariableOp_152*
            AssignVariableOp_16AssignVariableOp_162*
                AssignVariableOp_17AssignVariableOp_172*
            AssignVariableOp_18AssignVariableOp_182*
            AssignVariableOp_19AssignVariableOp_192(
            AssignVariableOp_2AssignVariableOp_22*
            AssignVariableOp_20AssignVariableOp_202*
                AssignVariableOp_21AssignVariableOp_212*
            AssignVariableOp_22AssignVariableOp_222*
            AssignVariableOp_23AssignVariableOp_232*
            AssignVariableOp_24AssignVariableOp_242*
            AssignVariableOp_25AssignVariableOp_252*
                AssignVariableOp_26AssignVariableOp_262*
        AssignVariableOp_27AssignVariableOp_272*
        AssignVariableOp_28AssignVariableOp_282*
        AssignVariableOp_29AssignVariableOp_292(
        AssignVariableOp_3AssignVariableOp_32*
            AssignVariableOp_30AssignVariableOp_302*
        AssignVariableOp_31AssignVariableOp_312*
        AssignVariableOp_32AssignVariableOp_322*
        AssignVariableOp_33AssignVariableOp_332*
        AssignVariableOp_34AssignVariableOp_342*
            AssignVariableOp_35AssignVariableOp_352*
        AssignVariableOp_36AssignVariableOp_362*
        AssignVariableOp_37AssignVariableOp_372*
        AssignVariableOp_38AssignVariableOp_382*
        AssignVariableOp_39AssignVariableOp_392(
            AssignVariableOp_4AssignVariableOp_42*
        AssignVariableOp_40AssignVariableOp_402*
        AssignVariableOp_41AssignVariableOp_412*
        AssignVariableOp_42AssignVariableOp_422*
        AssignVariableOp_43AssignVariableOp_432*
            AssignVariableOp_44AssignVariableOp_442*
        AssignVariableOp_45AssignVariableOp_452*
        AssignVariableOp_46AssignVariableOp_462*
        AssignVariableOp_47AssignVariableOp_472*
        AssignVariableOp_48AssignVariableOp_482*
            AssignVariableOp_49AssignVariableOp_492(
        AssignVariableOp_5AssignVariableOp_52*
        AssignVariableOp_50AssignVariableOp_502(
        AssignVariableOp_6AssignVariableOp_62(
        AssignVariableOp_7AssignVariableOp_72(
            AssignVariableOp_8AssignVariableOp_82(
        AssignVariableOp_9AssignVariableOp_92
        RestoreV2  RestoreV22
        RestoreV2_1 RestoreV2_1: C ?
        
        _output_shapes
        :  
        %
        _user_specified_name
             file_prefix: 
        
        _output_shapes
        :  :
        
        _output_shapes
        :  : 
            
        _output_shapes
            :  : 
        
        _output_shapes
        : : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  :
        
        _output_shapes
        :  : 
            
        _output_shapes
            :  :   
        
        _output_shapes
        :  : 
        
        
        _output_shapes
        :  :  
            
            _output_shapes
        :  :  
        
        _output_shapes
        :  :
        
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  :  
        
        _output_shapes
            :  : !
        
        _output_shapes
        : : "
        
        _output_shapes
        :  : #
            
            _output_shapes
        :  : $
        
            _output_shapes
            :  : %
            
            _output_shapes
            :  : &
            
            _output_shapes
            :  : '
            
                _output_shapes
                :  : (
            
            _output_shapes
            :  : )
            
            _output_shapes
            :  : *
            
            _output_shapes
            :  : +
            
            _output_shapes
        :  : , 
        
            _output_shapes
            :  : -
        
        _output_shapes
        :  : .
        
        _output_shapes
        :  : /
        
        _output_shapes
        :  : 0
        
        _output_shapes
        :  : 1
        
            _output_shapes
            :  : 2
        
        _output_shapes
        :  : 3
        
        _output_shapes
        :  
        �
        f
        J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_44357

        inputs
        identity�
        MaxPoolMaxPoolinputs*J
        _output_shapes8
        6: 4 ��������� ��������� ��������� ���������*
        ksize
            *
            paddingVALID*
            strides
            2
            MaxPool�
            IdentityIdentityMaxPool: output:0*
            T0*J
            _output_shapes8
            6: 4 ��������� ��������� ��������� ���������2

        Identity"
        identityIdentity: output: 0*I

        _input_shapes8
        6: 4 ��������� ��������� ��������� ���������: r n
        J
        _output_shapes8
        6: 4 ��������� ��������� ��������� ���������

        _user_specified_nameinputs
        �
        �
        @__inference_dense_layer_call_and_return_conditional_losses_44471

        inputs"
        matmul_readvariableop_resource#
        biasadd_readvariableop_resource
        identity��
        MatMul/ReadVariableOpReadVariableOpmatmul_readvariableop_resource*
        _output_shapes
        :
        ��*
        dtype02
        MatMul/ReadVariableOpt
        MatMulMatMulinputsMatMul/ReadVariableOp: value: 0*
        T0*(
        _output_shapes
        :  ����������2
        MatMul�
        BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02
            BiasAdd/ReadVariableOp�
        BiasAddBiasAddMatMul: product: 0BiasAdd/ReadVariableOp: value: 0*
        T0*(
        _output_shapes
        :  ����������2
        BiasAddY
        ReluReluBiasAdd: output: 0*
        T0*(
        _output_shapes
        :  ����������2
        Relug
        IdentityIdentityRelu: activations: 0*
        T0*(
        _output_shapes
        :  ����������2

        Identity"
        identityIdentity: output: 0*/

        _input_shapes
        :  ����������: : :P L
        (
        _output_shapes
        :  ����������

        _user_specified_nameinputs: 
        
        _output_shapes
        : : 
        
        _output_shapes
        :  
        �?
        �
        E__inference_sequential_layer_call_and_return_conditional_losses_44839

        inputs)
        %conv2d_conv2d_readvariableop_resource*
        &conv2d_biasadd_readvariableop_resource+
        'conv2d_1_conv2d_readvariableop_resource,
        (conv2d_1_biasadd_readvariableop_resource+
            'conv2d_2_conv2d_readvariableop_resource,
            (conv2d_2_biasadd_readvariableop_resource+
        'conv2d_3_conv2d_readvariableop_resource,
        (conv2d_3_biasadd_readvariableop_resource(
        $dense_matmul_readvariableop_resource)
            %dense_biasadd_readvariableop_resource*
        &dense_1_matmul_readvariableop_resource+
        'dense_1_biasadd_readvariableop_resource*
        &dense_2_matmul_readvariableop_resource+
        'dense_2_biasadd_readvariableop_resource
        identity��
        conv2d/Conv2D/ReadVariableOpReadVariableOp%conv2d_conv2d_readvariableop_resource*&
        _output_shapes
            :  *
        dtype02
        conv2d/Conv2D/ReadVariableOp�

        conv2d/Conv2DConv2Dinputs$conv2d/Conv2D/ReadVariableOp: value: 0*
        T0*/
        _output_shapes
            :  ���������?? *
        paddingVALID*
        strides
        2

        conv2d/Conv2D�
        conv2d/BiasAdd/ReadVariableOpReadVariableOp&conv2d_biasadd_readvariableop_resource*
        _output_shapes
        : *
        dtype02
        conv2d/BiasAdd/ReadVariableOp�
        conv2d/BiasAddBiasAddconv2d/Conv2D: output: 0%conv2d/BiasAdd/ReadVariableOp: value: 0*
        T0*/
        _output_shapes
        :  ���������?? 2
        conv2d/BiasAddu
        conv2d/ReluReluconv2d/BiasAdd: output: 0*
        T0*/
        _output_shapes
        :  ���������?? 2
        conv2d/Relu�
        max_pooling2d/MaxPoolMaxPoolconv2d/Relu: activations: 0*/
        _output_shapes
        :  ��������� *
        ksize
        *
        paddingVALID*
        strides
        2
        max_pooling2d/MaxPool�
        conv2d_1/Conv2D/ReadVariableOpReadVariableOp'conv2d_1_conv2d_readvariableop_resource*'
        _output_shapes
        :  �*
        dtype02
        conv2d_1/Conv2D/ReadVariableOp�
        conv2d_1/Conv2DConv2Dmax_pooling2d/MaxPool: output: 0&conv2d_1/Conv2D/ReadVariableOp: value: 0*
        T0*0
        _output_shapes
        :  ����������*
        paddingVALID*
        strides
        2
        conv2d_1/Conv2D�
        conv2d_1/BiasAdd/ReadVariableOpReadVariableOp(conv2d_1_biasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02!
        conv2d_1/BiasAdd/ReadVariableOp�
        conv2d_1/BiasAddBiasAddconv2d_1/Conv2D: output:0'conv2d_1/BiasAdd/ReadVariableOp:value:0*
        T0*0
        _output_shapes
        :  ����������2
        conv2d_1/BiasAdd|

        conv2d_1/ReluReluconv2d_1/BiasAdd: output: 0*
        T0*0
        _output_shapes
        :  ����������2

        conv2d_1/Relu�
            max_pooling2d_1/MaxPoolMaxPoolconv2d_1/Relu: activations: 0*0
        _output_shapes
        : ����������*
        ksize
        *
        paddingVALID*
        strides
            2
        max_pooling2d_1/MaxPool�
        conv2d_2/Conv2D/ReadVariableOpReadVariableOp'conv2d_2_conv2d_readvariableop_resource*(
        _output_shapes
        : ��*
        dtype02
        conv2d_2/Conv2D/ReadVariableOp�
        conv2d_2/Conv2DConv2D max_pooling2d_1/MaxPool: output: 0&conv2d_2/Conv2D/ReadVariableOp: value: 0*
        T0*0
        _output_shapes
        :  ���������  �*
        paddingVALID*
        strides
            2
        conv2d_2/Conv2D�
        conv2d_2/BiasAdd/ReadVariableOpReadVariableOp(conv2d_2_biasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02!
        conv2d_2/BiasAdd/ReadVariableOp�
        conv2d_2/BiasAddBiasAddconv2d_2/Conv2D: output: 0'conv2d_2/BiasAdd/ReadVariableOp:value:0*
        T0*0
        _output_shapes
        :  ���������  �2
        conv2d_2/BiasAdd|

        conv2d_2/ReluReluconv2d_2/BiasAdd: output: 0*
        T0*0
        _output_shapes
        :  ���������  �2

        conv2d_2/Relu�
        conv2d_3/Conv2D/ReadVariableOpReadVariableOp'conv2d_3_conv2d_readvariableop_resource*(
        _output_shapes
        : ��*
        dtype02
        conv2d_3/Conv2D/ReadVariableOp�
        conv2d_3/Conv2DConv2Dconv2d_2/Relu: activations: 0&conv2d_3/Conv2D/ReadVariableOp: value: 0*
        T0*0
        _output_shapes
            :  ���������
        
        �*
        paddingVALID*
        strides
        2
        conv2d_3/Conv2D�
        conv2d_3/BiasAdd/ReadVariableOpReadVariableOp(conv2d_3_biasadd_readvariableop_resource*
        _output_shapes
        :�*
        dtype02!
        conv2d_3/BiasAdd/ReadVariableOp�
        conv2d_3/BiasAddBiasAddconv2d_3/Conv2D: output: 0'conv2d_3/BiasAdd/ReadVariableOp:value:0*
        T0*0
        _output_shapes
            :  ���������
        
        �2
        conv2d_3/BiasAdd|

        conv2d_3/ReluReluconv2d_3/BiasAdd:output: 0*
        T0*0
        _output_shapes
        :  ���������
        
        �2

        conv2d_3/Relu�
        max_pooling2d_2/MaxPoolMaxPoolconv2d_3/Relu: activations: 0*0
        _output_shapes
        :  ����������*
        ksize
        *
        paddingVALID*
        strides
        2
        max_pooling2d_2/MaxPoolo

        flatten/ConstConst*
        _output_shapes
        : *
        dtype0*
        valueB"����   2

        flatten/Const�
        flatten/ReshapeReshape max_pooling2d_2/MaxPool: output: 0flatten/Const: output: 0*
        T0*(
        _output_shapes
        :  ����������2
        flatten/Reshape�
        dense/MatMul/ReadVariableOpReadVariableOp$dense_matmul_readvariableop_resource*
        _output_shapes
        :
        ��*
        dtype02
        dense/MatMul/ReadVariableOp�
        dense/MatMulMatMulflatten/Reshape: output:0#dense/MatMul/ReadVariableOp:value:0*
        T0*(
        _output_shapes
            :  ����������2
        dense/MatMul�
        dense/BiasAdd/ReadVariableOpReadVariableOp%dense_biasadd_readvariableop_resource*
        _output_shapes
        : �*
        dtype02
        dense/BiasAdd/ReadVariableOp�

        dense/BiasAddBiasAdddense/MatMul: product: 0$dense/BiasAdd/ReadVariableOp: value:0*
        T0*(
        _output_shapes
        :  ����������2

        dense/BiasAddk

        dense/ReluReludense/BiasAdd: output: 0*
        T0*(
        _output_shapes
        :  ����������2

        dense/Relu�
        dense_1/MatMul/ReadVariableOpReadVariableOp&dense_1_matmul_readvariableop_resource*
        _output_shapes
        : �`*
        dtype02
        dense_1/MatMul/ReadVariableOp�
        dense_1/MatMulMatMuldense/Relu: activations: 0%dense_1/MatMul/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
            :  ���������`2
        dense_1/MatMul�
        dense_1/BiasAdd/ReadVariableOpReadVariableOp'dense_1_biasadd_readvariableop_resource*
        _output_shapes
        : `*
        dtype02
        dense_1/BiasAdd/ReadVariableOp�
        dense_1/BiasAddBiasAdddense_1/MatMul: product: 0&dense_1/BiasAdd/ReadVariableOp: value: 0*
        T0*'
            _output_shapes
        :  ���������`2
        dense_1/BiasAddp
        dense_1/ReluReludense_1/BiasAdd: output: 0*
        T0*'
        _output_shapes
        : ���������`2
        dense_1/Relu�
        dense_2/MatMul/ReadVariableOpReadVariableOp&dense_2_matmul_readvariableop_resource*
        _output_shapes

        : `*
        dtype02
        dense_2/MatMul/ReadVariableOp�
        dense_2/MatMulMatMuldense_1/Relu: activations: 0%dense_2/MatMul/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
        :  ���������2
        dense_2/MatMul�
        dense_2/BiasAdd/ReadVariableOpReadVariableOp'dense_2_biasadd_readvariableop_resource*
        _output_shapes
            : *
        dtype02
        dense_2/BiasAdd/ReadVariableOp�
        dense_2/BiasAddBiasAdddense_2/MatMul: product: 0&dense_2/BiasAdd/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
        :  ���������2
        dense_2/BiasAddy
            dense_2/SoftmaxSoftmaxdense_2/BiasAdd: output: 0*
        T0*'
        _output_shapes
        :  ���������2
        dense_2/Softmaxm
        IdentityIdentitydense_2/Softmax: softmax: 0*
        T0*'
        _output_shapes
        :  ���������2

        Identity"
        identityIdentity:output: 0*h

        _input_shapesW
        U: �����������: : : :: : : : :: : : : :: Y U
        1
        _output_shapes
            :  �����������

        _user_specified_nameinputs: 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  : 
            
            _output_shapes
            :  : 
            
                _output_shapes
                :  : 
            
            _output_shapes
            :  :   
            
            _output_shapes
        :  : 
        
        
            _output_shapes
            :  :  
        
        _output_shapes
        : :  
        
        _output_shapes
        :  : 
        
        
        _output_shapes
            :  : 
        
        _output_shapes
        : 
        �
        z
        %__inference_dense_layer_call_fn_44994

        inputs
        unknown
        unknown_0
        identity��StatefulPartitionedCall�
        StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0*
        Tin
            2*
        Tout
        2*(
        _output_shapes
        :  ����������*$
        _read_only_resource_inputs
        **
        config_proto
        
        CPU
        
        GPU 2J 8*I
        fDRB
        @__inference_dense_layer_call_and_return_conditional_losses_444712
        StatefulPartitionedCall�
        IdentityIdentity StatefulPartitionedCall: output: 0^StatefulPartitionedCall*
        T0*(
            _output_shapes
        :  ����������2

        Identity"
        identityIdentity: output: 0*/

        _input_shapes
        :  ����������: : 22
        StatefulPartitionedCallStatefulPartitionedCall: P L
        (
            _output_shapes
            :  ����������

        _user_specified_nameinputs: 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  
        �
            |
            '__inference_dense_2_layer_call_fn_45034

            inputs
            unknown
        unknown_0
        identity��StatefulPartitionedCall�
        StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0*
        Tin
        2*
        Tout
        2*'
        _output_shapes
        :  ���������*$
        _read_only_resource_inputs
        **
        config_proto
        
        CPU
            
            GPU 2J 8*K
        fFRD
        B__inference_dense_2_layer_call_and_return_conditional_losses_445252
        StatefulPartitionedCall�
        IdentityIdentity StatefulPartitionedCall: output: 0^StatefulPartitionedCall*
        T0*'
        _output_shapes
        :  ���������2

        Identity"
        identityIdentity: output: 0*.

        _input_shapes
        :  ���������`: : 22
        StatefulPartitionedCallStatefulPartitionedCall: O K
        '
        _output_shapes
        : ���������`

        _user_specified_nameinputs: 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  
        �
        }
    (__inference_conv2d_1_layer_call_fn_44351

        inputs
        unknown
    unknown_0
    identity��StatefulPartitionedCall�
    StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0*
    Tin
    2*
        Tout
    2*B
    _output_shapes0
    .:,  ��������� ��������� ����������*$
    _read_only_resource_inputs
    **
    config_proto
    
    CPU
    
    GPU 2J 8*L
    fGRE
    C__inference_conv2d_1_layer_call_and_return_conditional_losses_443412
    StatefulPartitionedCall�
    IdentityIdentity StatefulPartitionedCall:output: 0^StatefulPartitionedCall*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������2

    Identity"
    identityIdentity: output: 0*H

    _input_shapes7
        5: + ��������� ��������� ���������: :22
    StatefulPartitionedCallStatefulPartitionedCall:i e
    A
    _output_shapes/
    -: + ��������� ��������� ���������

    _user_specified_nameinputs:
    
    _output_shapes
    :  : 
        
    _output_shapes
        :  
        �
        I
    -__inference_max_pooling2d_layer_call_fn_44329

        inputs
        identity�
    PartitionedCallPartitionedCallinputs*
    Tin
    2*
    Tout
    2*J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������*
    _read_only_resource_inputs
     **
    config_proto
    
    CPU
    
    GPU 2J 8*Q
    fLRJ
    H__inference_max_pooling2d_layer_call_and_return_conditional_losses_443232
    PartitionedCall�
    IdentityIdentityPartitionedCall: output: 0*
    T0*J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������2

    Identity"
    identityIdentity: output: 0*I

    _input_shapes8
    6: 4 ��������� ��������� ��������� ���������: r n
    J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������

    _user_specified_nameinputs
    �

    �
    C__inference_conv2d_3_layer_call_and_return_conditional_losses_44397

    inputs"
        conv2d_readvariableop_resource#
    biasadd_readvariableop_resource
    identity��
    Conv2D/ReadVariableOpReadVariableOpconv2d_readvariableop_resource*(
    _output_shapes
    : ��*
    dtype02
    Conv2D/ReadVariableOp�
    Conv2DConv2DinputsConv2D/ReadVariableOp: value: 0*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������*
        paddingVALID*
        strides
        2
        Conv2D�
        BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
        _output_shapes
        :�*
        dtype02
    BiasAdd/ReadVariableOp�
    BiasAddBiasAddConv2D: output: 0BiasAdd/ReadVariableOp:value: 0*
    T0*B
    _output_shapes0
    .:, ��������� ��������� ����������2
    BiasAdds
    ReluReluBiasAdd: output: 0*
    T0*B
        _output_shapes0
    .:,  ��������� ��������� ����������2
    Relu�
    IdentityIdentityRelu: activations: 0*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������2

    Identity"
    identityIdentity: output: 0*I

    _input_shapes8
    6:,  ��������� ��������� ����������: :: j f
    B
    _output_shapes0
        .:,  ��������� ��������� ����������

    _user_specified_nameinputs: 
    
    _output_shapes
    :  :
    
    _output_shapes
    :  
        �
    }
    (__inference_conv2d_2_layer_call_fn_44385

        inputs
        unknown
    unknown_0
    identity��StatefulPartitionedCall�
    StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0*
    Tin
    2*
        Tout
    2*B
    _output_shapes0
    .:,  ��������� ��������� ����������*$
    _read_only_resource_inputs
    **
    config_proto
    
    CPU
    
    GPU 2J 8*L
    fGRE
    C__inference_conv2d_2_layer_call_and_return_conditional_losses_443752
    StatefulPartitionedCall�
    IdentityIdentity StatefulPartitionedCall:output: 0^StatefulPartitionedCall*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������2

    Identity"
    identityIdentity: output: 0*I

    _input_shapes8
        6:,  ��������� ��������� ����������: : 22
    StatefulPartitionedCallStatefulPartitionedCall: j f
    B
    _output_shapes0
    .:,  ��������� ��������� ����������

    _user_specified_nameinputs: 
    
    _output_shapes
        :  : 
    
    _output_shapes
    : 
    �
    �
    *__inference_sequential_layer_call_fn_44738
    conv2d_input
    unknown
    unknown_0
    unknown_1
    unknown_2
    unknown_3
    unknown_4
    unknown_5
    unknown_6
    unknown_7
    unknown_8
    unknown_9

    unknown_10

    unknown_11

    unknown_12
    identity��StatefulPartitionedCall�
    StatefulPartitionedCallStatefulPartitionedCall conv2d_inputunknown  unknown_0  unknown_1  unknown_2  unknown_3  unknown_4  unknown_5  unknown_6  unknown_7  unknown_8  unknown_9
    unknown_10
    unknown_11
    unknown_12*
    Tin
    2*
    Tout
    2*'
    _output_shapes
    :  ���������*0
    _read_only_resource_inputs
    

    **
    config_proto
        
        CPU
    
        GPU 2J 8*N
    fIRG
    E__inference_sequential_layer_call_and_return_conditional_losses_447072
    StatefulPartitionedCall�
    IdentityIdentity StatefulPartitionedCall: output: 0^StatefulPartitionedCall*
    T0*'
    _output_shapes
    :  ���������2

    Identity"
    identityIdentity: output: 0*h

    _input_shapesW
    U:  �����������:: : : : :: : : : :: : : 22
    StatefulPartitionedCallStatefulPartitionedCall: _ [
    1
    _output_shapes
    :  �����������
    &
    _user_specified_name conv2d_input: 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  :   
    
    _output_shapes
    :  : 
        
        
        _output_shapes
        :  :  
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  
        �1
        �
        E__inference_sequential_layer_call_and_return_conditional_losses_44585
        conv2d_input
        conv2d_44545
        conv2d_44547
        conv2d_1_44551
        conv2d_1_44553
    conv2d_2_44557
    conv2d_2_44559
    conv2d_3_44562
    conv2d_3_44564
    dense_44569
    dense_44571

    dense_1_44574

    dense_1_44576

    dense_2_44579

    dense_2_44581
    identity��conv2d/StatefulPartitionedCall� conv2d_1/StatefulPartitionedCall� conv2d_2/StatefulPartitionedCall� conv2d_3/StatefulPartitionedCall�dense/StatefulPartitionedCall�dense_1/StatefulPartitionedCall�dense_2/StatefulPartitionedCall�
    conv2d/StatefulPartitionedCallStatefulPartitionedCall conv2d_input conv2d_44545 conv2d_44547*
    Tin
        2*
    Tout
    2*/
    _output_shapes
    :  ���������?? *$
    _read_only_resource_inputs
    **
    config_proto
    
    CPU
        
        GPU 2J 8*J
    fERC
    A__inference_conv2d_layer_call_and_return_conditional_losses_443072
    conv2d/StatefulPartitionedCall�
    max_pooling2d/PartitionedCallPartitionedCall'conv2d/StatefulPartitionedCall:output:0*
    Tin
    2*
    Tout
    2*/
    _output_shapes
    :  ��������� *
    _read_only_resource_inputs
     **
    config_proto
    
        CPU
        
        GPU 2J 8*Q
    fLRJ
    H__inference_max_pooling2d_layer_call_and_return_conditional_losses_443232
    max_pooling2d/PartitionedCall�
    conv2d_1/StatefulPartitionedCallStatefulPartitionedCall&max_pooling2d/PartitionedCall: output: 0conv2d_1_44551conv2d_1_44553*
    Tin
    2*
    Tout
    2*0
    _output_shapes
    :  ����������*$
    _read_only_resource_inputs
    **
    config_proto
        
        CPU
    
        GPU 2J 8*L
    fGRE
    C__inference_conv2d_1_layer_call_and_return_conditional_losses_443412"
    conv2d_1/StatefulPartitionedCall�
    max_pooling2d_1/PartitionedCallPartitionedCall)conv2d_1/StatefulPartitionedCall: output:0*
    Tin
    2*
    Tout
    2*0
    _output_shapes
    :  ����������*
    _read_only_resource_inputs
     **
    config_proto
    
    CPU
    
    GPU 2J 8*S
    fNRL
    J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_443572!
    max_pooling2d_1/PartitionedCall�
        conv2d_2/StatefulPartitionedCallStatefulPartitionedCall(max_pooling2d_1/PartitionedCall: output: 0conv2d_2_44557conv2d_2_44559*
    Tin
        2*
    Tout
    2*0
    _output_shapes
    :  ���������  �*$
    _read_only_resource_inputs
    **
    config_proto
    
    CPU
        
        GPU 2J 8*L
    fGRE
    C__inference_conv2d_2_layer_call_and_return_conditional_losses_443752"
    conv2d_2/StatefulPartitionedCall�
    conv2d_3/StatefulPartitionedCallStatefulPartitionedCall)conv2d_2/StatefulPartitionedCall: output: 0conv2d_3_44562conv2d_3_44564*
    Tin
    2*
    Tout
    2*0
    _output_shapes
    :  ���������
    
    �*$
    _read_only_resource_inputs
    **
    config_proto
    
    CPU
    
    GPU 2J 8*L
    fGRE
    C__inference_conv2d_3_layer_call_and_return_conditional_losses_443972"
    conv2d_3/StatefulPartitionedCall�
    max_pooling2d_2/PartitionedCallPartitionedCall)conv2d_3/StatefulPartitionedCall: output: 0*
    Tin
    2*
    Tout
    2*0
    _output_shapes
    :  ����������*
    _read_only_resource_inputs
         **
        config_proto
    
    CPU
    
    GPU 2J 8*S
    fNRL
        J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_444132!
        max_pooling2d_2/PartitionedCall�
        flatten/PartitionedCallPartitionedCall(max_pooling2d_2/PartitionedCall: output: 0*
        Tin
            2*
            Tout
        2*(
        _output_shapes
        :  ����������*
        _read_only_resource_inputs
         **
        config_proto
        
        CPU
        
        GPU 2J 8*K
    fFRD
    B__inference_flatten_layer_call_and_return_conditional_losses_444522
    flatten/PartitionedCall�
        dense/StatefulPartitionedCallStatefulPartitionedCall flatten/PartitionedCall: output: 0 dense_44569 dense_44571*
    Tin
    2*
    Tout
    2*(
    _output_shapes
    :  ����������*$
    _read_only_resource_inputs
    **
    config_proto
    
    CPU
    
    GPU 2J 8*I
    fDRB
    @__inference_dense_layer_call_and_return_conditional_losses_444712
    dense/StatefulPartitionedCall�
    dense_1/StatefulPartitionedCallStatefulPartitionedCall&dense/StatefulPartitionedCall: output: 0
    dense_1_44574
        dense_1_44576*
        Tin
        2*
    Tout
    2*'
        _output_shapes
    :  ���������`*$
    _read_only_resource_inputs
    **
    config_proto
    
    CPU
    
    GPU 2J 8*K
    fFRD
    B__inference_dense_1_layer_call_and_return_conditional_losses_444982!
    dense_1/StatefulPartitionedCall�
    dense_2/StatefulPartitionedCallStatefulPartitionedCall(dense_1/StatefulPartitionedCall: output: 0
    dense_2_44579
    dense_2_44581*
    Tin
        2*
    Tout
    2*'
    _output_shapes
    :  ���������*$
    _read_only_resource_inputs
    **
        config_proto
    
    CPU
    
    GPU 2J 8*K
    fFRD
        B__inference_dense_2_layer_call_and_return_conditional_losses_445252!
        dense_2/StatefulPartitionedCall�
    IdentityIdentity(dense_2/StatefulPartitionedCall: output: 0^conv2d/StatefulPartitionedCall!^conv2d_1/StatefulPartitionedCall!^conv2d_2/StatefulPartitionedCall!^conv2d_3/StatefulPartitionedCall^dense/StatefulPartitionedCall ^dense_1/StatefulPartitionedCall ^dense_2/StatefulPartitionedCall*
    T0*'
    _output_shapes
    :  ���������2

    Identity"
    identityIdentity:output: 0*h

    _input_shapesW
    U: �����������: : : :: : : : :: : : : :2@
    conv2d/StatefulPartitionedCallconv2d/StatefulPartitionedCall2D
    conv2d_1/StatefulPartitionedCall conv2d_1/StatefulPartitionedCall2D
    conv2d_2/StatefulPartitionedCall conv2d_2/StatefulPartitionedCall2D
    conv2d_3/StatefulPartitionedCall conv2d_3/StatefulPartitionedCall2 >
    dense/StatefulPartitionedCalldense/StatefulPartitionedCall2B
    dense_1/StatefulPartitionedCalldense_1/StatefulPartitionedCall2B
    dense_2/StatefulPartitionedCalldense_2/StatefulPartitionedCall: _ [
    1
    _output_shapes
        :  �����������
    &
    _user_specified_name conv2d_input: 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  :   
    
    _output_shapes
    :  : 
    
    
        _output_shapes
        :  :  
    
    _output_shapes
    : :  
    
    _output_shapes
    :  : 
    
    
    _output_shapes
        :  : 
    
    _output_shapes
    : 
    �
    K
    /__inference_max_pooling2d_2_layer_call_fn_44419

    inputs
    identity�
    PartitionedCallPartitionedCallinputs*
    Tin
    2*
    Tout
    2*J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������*
    _read_only_resource_inputs
     **
    config_proto
        
        CPU
    
        GPU 2J 8*S
    fNRL
    J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_444132
    PartitionedCall�
    IdentityIdentityPartitionedCall: output: 0*
    T0*J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������2

    Identity"
    identityIdentity: output: 0*I

    _input_shapes8
    6: 4 ��������� ��������� ��������� ���������: r n
    J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������

    _user_specified_nameinputs
    �o
    �
    __inference__traced_save_45214
    file_prefix,
    (savev2_conv2d_kernel_read_readvariableop*
    &savev2_conv2d_bias_read_readvariableop.
    *savev2_conv2d_1_kernel_read_readvariableop,
    (savev2_conv2d_1_bias_read_readvariableop.
        *savev2_conv2d_2_kernel_read_readvariableop,
    (savev2_conv2d_2_bias_read_readvariableop.
    *savev2_conv2d_3_kernel_read_readvariableop,
    (savev2_conv2d_3_bias_read_readvariableop+
    'savev2_dense_kernel_read_readvariableop)
        %savev2_dense_bias_read_readvariableop-
        )savev2_dense_1_kernel_read_readvariableop+
    'savev2_dense_1_bias_read_readvariableop-
    )savev2_dense_2_kernel_read_readvariableop+
    'savev2_dense_2_bias_read_readvariableop(
        $savev2_adam_iter_read_readvariableop  *
        &savev2_adam_beta_1_read_readvariableop*
    &savev2_adam_beta_2_read_readvariableop)
    %savev2_adam_decay_read_readvariableop1
    -savev2_adam_learning_rate_read_readvariableop$
    savev2_total_read_readvariableop$
        savev2_count_read_readvariableop&
    "savev2_total_1_read_readvariableop&
        "savev2_count_1_read_readvariableop3
        /savev2_adam_conv2d_kernel_m_read_readvariableop1
        -savev2_adam_conv2d_bias_m_read_readvariableop5
    1savev2_adam_conv2d_1_kernel_m_read_readvariableop3
    /savev2_adam_conv2d_1_bias_m_read_readvariableop5
        1savev2_adam_conv2d_2_kernel_m_read_readvariableop3
    /savev2_adam_conv2d_2_bias_m_read_readvariableop5
    1savev2_adam_conv2d_3_kernel_m_read_readvariableop3
    /savev2_adam_conv2d_3_bias_m_read_readvariableop2
    .savev2_adam_dense_kernel_m_read_readvariableop0
    , savev2_adam_dense_bias_m_read_readvariableop4
    0savev2_adam_dense_1_kernel_m_read_readvariableop2
    .savev2_adam_dense_1_bias_m_read_readvariableop4
    0savev2_adam_dense_2_kernel_m_read_readvariableop2
    .savev2_adam_dense_2_bias_m_read_readvariableop3
        /savev2_adam_conv2d_kernel_v_read_readvariableop1
    -savev2_adam_conv2d_bias_v_read_readvariableop5
    1savev2_adam_conv2d_1_kernel_v_read_readvariableop3
    /savev2_adam_conv2d_1_bias_v_read_readvariableop5
    1savev2_adam_conv2d_2_kernel_v_read_readvariableop3
        /savev2_adam_conv2d_2_bias_v_read_readvariableop5
    1savev2_adam_conv2d_3_kernel_v_read_readvariableop3
    /savev2_adam_conv2d_3_bias_v_read_readvariableop2
    .savev2_adam_dense_kernel_v_read_readvariableop0
    , savev2_adam_dense_bias_v_read_readvariableop4
        0savev2_adam_dense_1_kernel_v_read_readvariableop2
    .savev2_adam_dense_1_bias_v_read_readvariableop4
    0savev2_adam_dense_2_kernel_v_read_readvariableop2
    .savev2_adam_dense_2_bias_v_read_readvariableop
    savev2_1_const

        identity_1��MergeV2Checkpoints�SaveV2�SaveV2_1�
        StaticRegexFullMatchStaticRegexFullMatch file_prefix"
        /device: CPU:**
        _output_shapes
        :  *
        pattern
        ^s3://.*2
        StaticRegexFullMatchc
        ConstConst"
            /device: CPU: **
        _output_shapes
        : *
        dtype0*
    value
        B  B.part2
        Const�
        Const_1Const"
        /device: CPU: **
        _output_shapes
        :  *
        dtype0*<
            value3B1 B+_temp_67eb62ae589d4af0919cee7446eb6a44/part2
        Const_1�
        SelectSelectStaticRegexFullMatch: output: 0Const: output: 0Const_1:output: 0"
        /device: CPU: **
    T0*
    _output_shapes
        :  2
        Selectt

    StringJoin
    StringJoin file_prefixSelect: output: 0"
    /device: CPU: **
    N*
    _output_shapes
    :  2

    StringJoinZ

        num_shardsConst*
        _output_shapes
    :  *
    dtype0*
    value  B : 2

    num_shards
    ShardedFilename/shardConst"
    /device: CPU: 0*
    _output_shapes
    :  *
    dtype0*
    value  B :  2
    ShardedFilename/shard�
    ShardedFilenameShardedFilenameStringJoin: output: 0ShardedFilename/shard: output: 0num_shards: output: 0"
    /device: CPU: 0*
    _output_shapes
    :  2
    ShardedFilename�
        SaveV2/tensor_namesConst"
        /device: CPU: 0*
        _output_shapes
        : 3*
        dtype0*�
        value�B�3B6layer_with_weights-0/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-0/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-1/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-1/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-2/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-2/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-3/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-3/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-4/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-4/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-5/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-5/bias/.ATTRIBUTES/VARIABLE_VALUEB6layer_with_weights-6/kernel/.ATTRIBUTES/VARIABLE_VALUEB4layer_with_weights-6/bias/.ATTRIBUTES/VARIABLE_VALUEB)optimizer/iter/.ATTRIBUTES/VARIABLE_VALUEB+optimizer/beta_1/.ATTRIBUTES/VARIABLE_VALUEB+optimizer/beta_2/.ATTRIBUTES/VARIABLE_VALUEB*optimizer/decay/.ATTRIBUTES/VARIABLE_VALUEB2optimizer/learning_rate/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/0/total/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/0/count/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/1/total/.ATTRIBUTES/VARIABLE_VALUEB4keras_api/metrics/1/count/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-0/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-0/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-1/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-1/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-2/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-2/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-3/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-3/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-4/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-4/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-5/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-5/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-6/kernel/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-6/bias/.OPTIMIZER_SLOT/optimizer/m/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-0/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-0/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-1/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-1/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-2/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-2/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-3/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-3/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-4/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-4/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-5/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-5/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBRlayer_with_weights-6/kernel/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUEBPlayer_with_weights-6/bias/.OPTIMIZER_SLOT/optimizer/v/.ATTRIBUTES/VARIABLE_VALUE2
    SaveV2/tensor_names�
    SaveV2/shape_and_slicesConst"
    /device: CPU: 0*
    _output_shapes
    :3*
    dtype0*y
    valuepBn3B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B B 2
    SaveV2/shape_and_slices�
        SaveV2SaveV2ShardedFilename: filename: 0SaveV2/tensor_names: output: 0 SaveV2/shape_and_slices: output: 0(savev2_conv2d_kernel_read_readvariableop&savev2_conv2d_bias_read_readvariableop*savev2_conv2d_1_kernel_read_readvariableop(savev2_conv2d_1_bias_read_readvariableop*savev2_conv2d_2_kernel_read_readvariableop(savev2_conv2d_2_bias_read_readvariableop*savev2_conv2d_3_kernel_read_readvariableop(savev2_conv2d_3_bias_read_readvariableop'savev2_dense_kernel_read_readvariableop%savev2_dense_bias_read_readvariableop)savev2_dense_1_kernel_read_readvariableop'savev2_dense_1_bias_read_readvariableop)savev2_dense_2_kernel_read_readvariableop'savev2_dense_2_bias_read_readvariableop$savev2_adam_iter_read_readvariableop&savev2_adam_beta_1_read_readvariableop&savev2_adam_beta_2_read_readvariableop%savev2_adam_decay_read_readvariableop-savev2_adam_learning_rate_read_readvariableop savev2_total_read_readvariableop savev2_count_read_readvariableop"savev2_total_1_read_readvariableop"savev2_count_1_read_readvariableop/savev2_adam_conv2d_kernel_m_read_readvariableop-savev2_adam_conv2d_bias_m_read_readvariableop1savev2_adam_conv2d_1_kernel_m_read_readvariableop/savev2_adam_conv2d_1_bias_m_read_readvariableop1savev2_adam_conv2d_2_kernel_m_read_readvariableop/savev2_adam_conv2d_2_bias_m_read_readvariableop1savev2_adam_conv2d_3_kernel_m_read_readvariableop/savev2_adam_conv2d_3_bias_m_read_readvariableop.savev2_adam_dense_kernel_m_read_readvariableop,savev2_adam_dense_bias_m_read_readvariableop0savev2_adam_dense_1_kernel_m_read_readvariableop.savev2_adam_dense_1_bias_m_read_readvariableop0savev2_adam_dense_2_kernel_m_read_readvariableop.savev2_adam_dense_2_bias_m_read_readvariableop/savev2_adam_conv2d_kernel_v_read_readvariableop-savev2_adam_conv2d_bias_v_read_readvariableop1savev2_adam_conv2d_1_kernel_v_read_readvariableop/savev2_adam_conv2d_1_bias_v_read_readvariableop1savev2_adam_conv2d_2_kernel_v_read_readvariableop/savev2_adam_conv2d_2_bias_v_read_readvariableop1savev2_adam_conv2d_3_kernel_v_read_readvariableop/savev2_adam_conv2d_3_bias_v_read_readvariableop.savev2_adam_dense_kernel_v_read_readvariableop,savev2_adam_dense_bias_v_read_readvariableop0savev2_adam_dense_1_kernel_v_read_readvariableop.savev2_adam_dense_1_bias_v_read_readvariableop0savev2_adam_dense_2_kernel_v_read_readvariableop.savev2_adam_dense_2_bias_v_read_readvariableop"
    /device: CPU: 0*
    _output_shapes
     *A
        dtypes7
        523  2
    SaveV2�
        ShardedFilename_1/shardConst"
    /device: CPU: 0*
    _output_shapes
    :  *
    dtype0*
    value  B : 2
    ShardedFilename_1/shard�
    ShardedFilename_1ShardedFilenameStringJoin: output: 0 ShardedFilename_1/shard: output: 0num_shards: output: 0"
    /device: CPU: 0*
    _output_shapes
    : 2
    ShardedFilename_1�
    SaveV2_1/tensor_namesConst"
    /device: CPU: 0*
    _output_shapes
    :*
    dtype0*1
    value(B&B_CHECKPOINTABLE_OBJECT_GRAPH2
    SaveV2_1/tensor_names�
    SaveV2_1/shape_and_slicesConst"
    /device:CPU: 0*
    _output_shapes
    : *
    dtype0*
    value B
    B 2
    SaveV2_1/shape_and_slices�
    SaveV2_1SaveV2ShardedFilename_1: filename: 0SaveV2_1/tensor_names: output: 0"SaveV2_1/shape_and_slices:output:0savev2_1_const^SaveV2"
    /device: CPU: 0*
    _output_shapes
     *
    dtypes
    22

        SaveV2_1�
    &MergeV2Checkpoints/checkpoint_prefixesPackShardedFilename: filename: 0ShardedFilename_1: filename:0^SaveV2  ^SaveV2_1"
        /device: CPU: 0*
        N*
        T0*
        _output_shapes
        : 2(
        &MergeV2Checkpoints/checkpoint_prefixes�
        MergeV2CheckpointsMergeV2Checkpoints/MergeV2Checkpoints/checkpoint_prefixes: output: 0 file_prefix  ^SaveV2_1"
        /device: CPU: 0*
    _output_shapes
     2
    MergeV2Checkpointsr
    IdentityIdentity file_prefix^MergeV2Checkpoints"
    /device: CPU: 0*
    T0*
    _output_shapes
        :  2

        Identity�

    Identity_1IdentityIdentity: output: 0^MergeV2Checkpoints^SaveV2  ^SaveV2_1*
    T0*
    _output_shapes
    :  2

    Identity_1"!

    identity_1Identity_1:output: 0*�

    _input_shapes�
    �:  : : :  �: �: ��: �: ��: �:
    ��: �: �`: `:`::  :  : :  :  :  :  : :  :  : :  �: �: ��: �: ��: �:
    ��: �: �`: `: `: : : :  �: �: ��: �: ��: �:
    ��: �: �`: `:`::  2(
    MergeV2CheckpointsMergeV2Checkpoints2
    SaveV2SaveV22
    SaveV2_1SaveV2_1: C ?
    
    _output_shapes
    :  
    %
        _user_specified_name
         file_prefix:, (
    &
    _output_shapes
    : : 
    
        _output_shapes
        : : -)
    '
    _output_shapes
    :  �: !
    
    _output_shapes
    : �:.*
    (
    _output_shapes
    : ��: !
    
    _output_shapes
    : �:.*
    (
    _output_shapes
    : ��: !
    
    _output_shapes
    : �:&  "

    _output_shapes
    :
    ��:!
    
    
    _output_shapes
    :�: % !
    
    _output_shapes
    : �`:  
    
    _output_shapes
        : `: $
    
    
    _output_shapes

    :`: 
    
    _output_shapes
    : : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  :, (
    &
    _output_shapes
    : : 
    
    _output_shapes
    : : -)
    '
    _output_shapes
    : �: !
    
    _output_shapes
    : �: .*
    (
    _output_shapes
    : ��: !
    
    _output_shapes
    : �: .*
    (
    _output_shapes
    : ��: !
    
    _output_shapes
    : �: & "

    _output_shapes
        :
        ��: !!
    
    _output_shapes
    : �: %"!
    
    _output_shapes
    : �`: #
    
    _output_shapes
    :`: $$
    
        _output_shapes

        : `: %
    
    _output_shapes
        : :, &(
    &
    _output_shapes
    :  : '
    
    _output_shapes
    :: -()
    '
        _output_shapes
        :  �: !)
        
            _output_shapes
            : �: .**
        (
        _output_shapes
        : ��: !+
        
            _output_shapes
            : �: ., *
        (
        _output_shapes
    : ��: !-
    
        _output_shapes
        : �: &."

    _output_shapes
    :
    ��: !/
    
    _output_shapes
        : �: %0!
    
    _output_shapes
    : �`: 1
    
    _output_shapes
    : `: $2
    
    _output_shapes

    : `: 3
    
    _output_shapes
    : : 4
    
        _output_shapes
        :  
        �
    �
        B__inference_dense_2_layer_call_and_return_conditional_losses_44525

        inputs"
    matmul_readvariableop_resource#
        biasadd_readvariableop_resource
    identity��
    MatMul/ReadVariableOpReadVariableOpmatmul_readvariableop_resource*
        _output_shapes

        : `*
        dtype02
        MatMul/ReadVariableOps
        MatMulMatMulinputsMatMul/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
        :  ���������2
        MatMul�
        BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
    _output_shapes
    : *
    dtype02
    BiasAdd/ReadVariableOp�
    BiasAddBiasAddMatMul: product: 0BiasAdd/ReadVariableOp: value: 0*
    T0*'
    _output_shapes
    :  ���������2
    BiasAdda
    SoftmaxSoftmaxBiasAdd: output: 0*
    T0*'
    _output_shapes
    :  ���������2
    Softmaxe
    IdentityIdentitySoftmax: softmax: 0*
    T0*'
    _output_shapes
    :  ���������2

    Identity"
    identityIdentity:output: 0*.

    _input_shapes
    : ���������`: : : O K
    '
    _output_shapes
    :  ���������`

    _user_specified_nameinputs: 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  
    �
        �
        B__inference_dense_1_layer_call_and_return_conditional_losses_44498

        inputs"
        matmul_readvariableop_resource#
        biasadd_readvariableop_resource
        identity��
        MatMul/ReadVariableOpReadVariableOpmatmul_readvariableop_resource*
        _output_shapes
        : �`*
        dtype02
        MatMul/ReadVariableOps
        MatMulMatMulinputsMatMul/ReadVariableOp: value: 0*
        T0*'
        _output_shapes
    :  ���������`2
    MatMul�
    BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
    _output_shapes
    : `*
    dtype02
    BiasAdd/ReadVariableOp�
    BiasAddBiasAddMatMul: product: 0BiasAdd/ReadVariableOp: value: 0*
    T0*'
    _output_shapes
    :  ���������`2
    BiasAddX
    ReluReluBiasAdd: output: 0*
    T0*'
    _output_shapes
    :  ���������`2
    Reluf
    IdentityIdentityRelu: activations: 0*
    T0*'
    _output_shapes
        :  ���������`2

        Identity"
        identityIdentity: output: 0*/

        _input_shapes
        :  ����������: : :P L
        (
        _output_shapes
        :  ����������

    _user_specified_nameinputs: 
        
        _output_shapes
        : : 
        
        _output_shapes
        :  
        �
        �
        #__inference_signature_wrapper_44781
        conv2d_input
        unknown
        unknown_0
        unknown_1
        unknown_2
        unknown_3
        unknown_4
        unknown_5
        unknown_6
        unknown_7
        unknown_8
        unknown_9

        unknown_10

        unknown_11

        unknown_12
        identity��StatefulPartitionedCall�
        StatefulPartitionedCallStatefulPartitionedCall conv2d_inputunknown  unknown_0  unknown_1  unknown_2  unknown_3  unknown_4  unknown_5  unknown_6  unknown_7  unknown_8  unknown_9
        unknown_10
        unknown_11
        unknown_12*
        Tin
        2*
        Tout
        2*'
        _output_shapes
        :  ���������*0
        _read_only_resource_inputs
        

        **
        config_proto
            
            CPU
        
            GPU 2J 8*)
        f$R"
        __inference__wrapped_model_442952
        StatefulPartitionedCall�
        IdentityIdentity StatefulPartitionedCall: output: 0^StatefulPartitionedCall*
        T0*'
        _output_shapes
        :  ���������2

        Identity"
        identityIdentity: output: 0*h

        _input_shapesW
        U:  �����������: : :: : : : :: : : : :: 22
        StatefulPartitionedCallStatefulPartitionedCall: _ [
        1
        _output_shapes
            :  �����������
        &
        _user_specified_name conv2d_input: 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
        _output_shapes
        :  : 
        
            _output_shapes
            :  : 
        
        _output_shapes
        :  :   
        
        _output_shapes
        :  : 
        
        
            _output_shapes
            :  :  
            
            _output_shapes
            : :  
            
            _output_shapes
            :  : 
            
            
            _output_shapes
                :  : 
            
            _output_shapes
            : 
            �
            �
            *__inference_sequential_layer_call_fn_44662
            conv2d_input
            unknown
            unknown_0
            unknown_1
            unknown_2
            unknown_3
            unknown_4
            unknown_5
        unknown_6
        unknown_7
        unknown_8
        unknown_9

        unknown_10

        unknown_11

        unknown_12
        identity��StatefulPartitionedCall�
        StatefulPartitionedCallStatefulPartitionedCall conv2d_inputunknown  unknown_0  unknown_1  unknown_2  unknown_3  unknown_4  unknown_5  unknown_6  unknown_7  unknown_8  unknown_9
        unknown_10
        unknown_11
        unknown_12*
        Tin
        2*
        Tout
        2*'
        _output_shapes
        :  ���������*0
        _read_only_resource_inputs
        

        **
        config_proto
            
            CPU
        
            GPU 2J 8*N
        fIRG
        E__inference_sequential_layer_call_and_return_conditional_losses_446312
        StatefulPartitionedCall�
        IdentityIdentity StatefulPartitionedCall: output: 0^StatefulPartitionedCall*
        T0*'
        _output_shapes
        :  ���������2

        Identity"
        identityIdentity: output: 0*h

        _input_shapesW
        U:  �����������:: : : : :: : : : :: : : 22
    StatefulPartitionedCallStatefulPartitionedCall: _ [
    1
    _output_shapes
    :  �����������
    &
    _user_specified_name conv2d_input: 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  :   
    
    _output_shapes
    :  : 
        
        
        _output_shapes
    :  :  
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  
    �
    �
        *__inference_sequential_layer_call_fn_44963

        inputs
    unknown
        unknown_0
        unknown_1
        unknown_2
        unknown_3
        unknown_4
        unknown_5
        unknown_6
        unknown_7
        unknown_8
        unknown_9

        unknown_10

        unknown_11

        unknown_12
        identity��StatefulPartitionedCall�
        StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0  unknown_1  unknown_2  unknown_3  unknown_4  unknown_5  unknown_6  unknown_7  unknown_8  unknown_9
    unknown_10
    unknown_11
    unknown_12*
        Tin
        2*
    Tout
    2*'
        _output_shapes
    :  ���������*0
    _read_only_resource_inputs
    

    **
    config_proto
    
    CPU
    
    GPU 2J 8*N
        fIRG
        E__inference_sequential_layer_call_and_return_conditional_losses_447072
        StatefulPartitionedCall�
        IdentityIdentity StatefulPartitionedCall: output: 0^StatefulPartitionedCall*
        T0*'
            _output_shapes
        :  ���������2

        Identity"
        identityIdentity: output: 0*h

        _input_shapesW
        U:  �����������: : : : : : : : : : : : : : 22
    StatefulPartitionedCallStatefulPartitionedCall: Y U
    1
    _output_shapes
    :  �����������

    _user_specified_nameinputs: 
    
    _output_shapes
    : : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  :
    
    _output_shapes
    :  : 
        
    _output_shapes
        :  : 
    
    _output_shapes
    : : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  :  
    
    _output_shapes
    :  : 
    
    
    _output_shapes
    :  :  
    
        _output_shapes
        :  :  
    
    _output_shapes
    : : 
    
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  
        �
        �
    *__inference_sequential_layer_call_fn_44930

    inputs
    unknown
    unknown_0
    unknown_1
    unknown_2
    unknown_3
    unknown_4
        unknown_5
        unknown_6
    unknown_7
    unknown_8
    unknown_9

        unknown_10

    unknown_11

        unknown_12
        identity��StatefulPartitionedCall�
    StatefulPartitionedCallStatefulPartitionedCallinputsunknown  unknown_0  unknown_1  unknown_2  unknown_3  unknown_4  unknown_5  unknown_6  unknown_7  unknown_8  unknown_9
    unknown_10
    unknown_11
    unknown_12*
    Tin
    2*
    Tout
    2*'
    _output_shapes
    :  ���������*0
    _read_only_resource_inputs
        

        **
        config_proto
        
        CPU
        
        GPU 2J 8*N
        fIRG
        E__inference_sequential_layer_call_and_return_conditional_losses_446312
        StatefulPartitionedCall�
        IdentityIdentity StatefulPartitionedCall:output: 0^StatefulPartitionedCall*
        T0*'
        _output_shapes
        :  ���������2

    Identity"
    identityIdentity:output: 0*h

    _input_shapesW
    U: �����������: : : :: : : : :: : : : :22
    StatefulPartitionedCallStatefulPartitionedCall:Y U
    1
    _output_shapes
    :  �����������

    _user_specified_nameinputs: 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  : 
    
        _output_shapes
        :  :   
    
    _output_shapes
    : : 
    
    
    _output_shapes
    :  :  
    
    _output_shapes
        :  :  
        
        _output_shapes
        :  : 
        
        
        _output_shapes
        :  : 
            
        _output_shapes
            :  
            �
            ^
        B__inference_flatten_layer_call_and_return_conditional_losses_44969

            inputs
            identity_
        ConstConst*
        _output_shapes
            : *
        dtype0*
        valueB"����   2
    Consth
    ReshapeReshapeinputsConst: output: 0*
    T0*(
    _output_shapes
    : ����������2
    Reshapee
    IdentityIdentityReshape: output: 0*
    T0*(
    _output_shapes
    :  ����������2

    Identity"
        identityIdentity: output: 0*/

    _input_shapes
    :  ����������: X T
    0
    _output_shapes
    :  ����������

    _user_specified_nameinputs
    �

    �
    C__inference_conv2d_2_layer_call_and_return_conditional_losses_44375

    inputs"
    conv2d_readvariableop_resource#
    biasadd_readvariableop_resource
    identity��
    Conv2D/ReadVariableOpReadVariableOpconv2d_readvariableop_resource*(
    _output_shapes
    :��*
    dtype02
        Conv2D/ReadVariableOp�
    Conv2DConv2DinputsConv2D/ReadVariableOp: value: 0*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������*
    paddingVALID*
    strides
    2
    Conv2D�
    BiasAdd/ReadVariableOpReadVariableOpbiasadd_readvariableop_resource*
    _output_shapes
    : �*
    dtype02
    BiasAdd/ReadVariableOp�
    BiasAddBiasAddConv2D: output: 0BiasAdd/ReadVariableOp: value: 0*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������2
    BiasAdds
    ReluReluBiasAdd: output: 0*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������2
    Relu�
    IdentityIdentityRelu: activations:0*
    T0*B
    _output_shapes0
    .:,  ��������� ��������� ����������2

    Identity"
        identityIdentity: output: 0*I

    _input_shapes8
    6:, ��������� ��������� ����������: : :j f
    B
    _output_shapes0
    .:,  ��������� ��������� ����������

    _user_specified_nameinputs: 
    
    _output_shapes
    :  : 
    
    _output_shapes
    :  
    �
    f
    J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_44413

    inputs
    identity�
    MaxPoolMaxPoolinputs*J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������*
    ksize
    *
    paddingVALID*
    strides
    2
    MaxPool�
    IdentityIdentityMaxPool: output: 0*
    T0*J
    _output_shapes8
    6:4 ��������� ��������� ��������� ���������2

    Identity"
        identityIdentity: output: 0*I

    _input_shapes8
    6: 4 ��������� ��������� ��������� ���������: r n
    J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������

    _user_specified_nameinputs
        �
        K
    /__inference_max_pooling2d_1_layer_call_fn_44363

        inputs
        identity�
    PartitionedCallPartitionedCallinputs*
    Tin
    2*
    Tout
    2*J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������*
    _read_only_resource_inputs
     **
    config_proto
    
    CPU
    
    GPU 2J 8*S
    fNRL
    J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_443572
    PartitionedCall�
    IdentityIdentityPartitionedCall: output: 0*
        T0*J
        _output_shapes8
        6: 4 ��������� ��������� ��������� ���������2

        Identity"
        identityIdentity: output: 0*I

        _input_shapes8
        6: 4 ��������� ��������� ��������� ���������: r n
    J
    _output_shapes8
    6: 4 ��������� ��������� ��������� ���������

    _user_specified_nameinputs"�L
    saver_filename: 0StatefulPartitionedCall_1: 0StatefulPartitionedCall_28"
    saved_model_main_op
        
        NoOp* >
        __saved_model_init_op%#
    __saved_model_init_op

    NoOp*�
    serving_default�
    O
    conv2d_input?
    serving_default_conv2d_input: 0 �����������;
    dense_20
    StatefulPartitionedCall: 0 ���������tensorflow/serving/predict: ��
    �[
        layer_with_weights-0
    layer-0
        layer-1
    layer_with_weights-1
    layer-2
    layer-3
    layer_with_weights-2
    layer-4
    layer_with_weights-3
    layer-5
    layer-6
    layer-7
      layer_with_weights-4
          layer-8
    
    layer_with_weights-5
    
        layer-9
         layer_with_weights-6
     layer-10

       optimizer
    
    regularization_losses

      variables
    trainable_variables

      keras_api
    
    signatures
    +�&call_and_return_all_conditional_losses

    �__call__
    �_default_save_signature"�W
    _tf_keras_sequential�W{ "class_name": "Sequential", "name": "sequential", "trainable": true, "expects_training_arg": true, "dtype": "float32", "batch_input_shape": null, "config": { "name": "sequential", "layers": [{ "class_name": "Conv2D", "config": { "name": "conv2d", "trainable": true, "batch_input_shape": { "class_name": "__tuple__", "items": [null, 256, 256, 1] }, "dtype": "float32", "filters": 32, "kernel_size": { "class_name": "__tuple__", "items": [5, 5] }, "strides": { "class_name": "__tuple__", "items": [4, 4] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "MaxPooling2D", "config": { "name": "max_pooling2d", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" } }, { "class_name": "Conv2D", "config": { "name": "conv2d_1", "trainable": true, "dtype": "float32", "filters": 128, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "MaxPooling2D", "config": { "name": "max_pooling2d_1", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" } }, { "class_name": "Conv2D", "config": { "name": "conv2d_2", "trainable": true, "dtype": "float32", "filters": 192, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "Conv2D", "config": { "name": "conv2d_3", "trainable": true, "dtype": "float32", "filters": 128, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "MaxPooling2D", "config": { "name": "max_pooling2d_2", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" } }, { "class_name": "Flatten", "config": { "name": "flatten", "trainable": true, "dtype": "float32", "data_format": "channels_last" } }, { "class_name": "Dense", "config": { "name": "dense", "trainable": true, "dtype": "float32", "units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "Dense", "config": { "name": "dense_1", "trainable": true, "dtype": "float32", "units": 96, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "Dense", "config": { "name": "dense_2", "trainable": true, "dtype": "float32", "units": 7, "activation": "softmax", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }], "build_input_shape": { "class_name": "TensorShape", "items": [null, 256, 256, 1] } }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { "-1": 1 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 256, 256, 1] }, "is_graph_network": true, "keras_version": "2.3.0-tf", "backend": "tensorflow", "model_config": { "class_name": "Sequential", "config": { "name": "sequential", "layers": [{ "class_name": "Conv2D", "config": { "name": "conv2d", "trainable": true, "batch_input_shape": { "class_name": "__tuple__", "items": [null, 256, 256, 1] }, "dtype": "float32", "filters": 32, "kernel_size": { "class_name": "__tuple__", "items": [5, 5] }, "strides": { "class_name": "__tuple__", "items": [4, 4] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "MaxPooling2D", "config": { "name": "max_pooling2d", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" } }, { "class_name": "Conv2D", "config": { "name": "conv2d_1", "trainable": true, "dtype": "float32", "filters": 128, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "MaxPooling2D", "config": { "name": "max_pooling2d_1", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" } }, { "class_name": "Conv2D", "config": { "name": "conv2d_2", "trainable": true, "dtype": "float32", "filters": 192, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "Conv2D", "config": { "name": "conv2d_3", "trainable": true, "dtype": "float32", "filters": 128, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "MaxPooling2D", "config": { "name": "max_pooling2d_2", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" } }, { "class_name": "Flatten", "config": { "name": "flatten", "trainable": true, "dtype": "float32", "data_format": "channels_last" } }, { "class_name": "Dense", "config": { "name": "dense", "trainable": true, "dtype": "float32", "units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "Dense", "config": { "name": "dense_1", "trainable": true, "dtype": "float32", "units": 96, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }, { "class_name": "Dense", "config": { "name": "dense_2", "trainable": true, "dtype": "float32", "units": 7, "activation": "softmax", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null } }], "build_input_shape": { "class_name": "TensorShape", "items": [null, 256, 256, 1] } } }, "training_config": { "loss": { "class_name": "CategoricalCrossentropy", "config": { "reduction": "auto", "name": "categorical_crossentropy", "from_logits": false, "label_smoothing": 0 } }, "metrics": ["accuracy"], "weighted_metrics": null, "loss_weights": null, "sample_weight_mode": null, "optimizer_config": { "class_name": "Adam", "config": { "name": "Adam", "learning_rate": 0.0010000000474974513, "decay": 0.0, "beta_1": 0.8999999761581421, "beta_2": 0.9990000128746033, "epsilon": 1e-07, "amsgrad": false } } } }
    �


        kernel
        bias
    regularization_losses

      variables
    trainable_variables

      keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
    _tf_keras_layer�  { "class_name": "Conv2D", "name": "conv2d", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": { "class_name": "__tuple__", "items": [null, 256, 256, 1] }, "stateful": false, "config": { "name": "conv2d", "trainable": true, "batch_input_shape": { "class_name": "__tuple__", "items": [null, 256, 256, 1] }, "dtype": "float32", "filters": 32, "kernel_size": { "class_name": "__tuple__", "items": [5, 5] }, "strides": { "class_name": "__tuple__", "items": [4, 4] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { "-1": 1 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 256, 256, 1] } }
    �
        regularization_losses

          variables
        trainable_variables

          keras_api
        +�&call_and_return_all_conditional_losses

        �__call__"�
        _tf_keras_layer�{ "class_name": "MaxPooling2D", "name": "max_pooling2d", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "max_pooling2d", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { } } } }
        �

            kernel
            bias
        regularization_losses

          variables
         trainable_variables

        !  keras_api
        +�&call_and_return_all_conditional_losses

        �__call__"�
    _tf_keras_layer�{ "class_name": "Conv2D", "name": "conv2d_1", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "conv2d_1", "trainable": true, "dtype": "float32", "filters": 128, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { "-1": 32 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 31, 31, 32] } }
    �
        "regularization_losses

        #	variables
        $trainable_variables

    %  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
    _tf_keras_layer�{ "class_name": "MaxPooling2D", "name": "max_pooling2d_1", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "max_pooling2d_1", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { } } } }
    �

        &kernel
        'bias
        (regularization_losses

    )  variables
    *trainable_variables

    +  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
    _tf_keras_layer�{ "class_name": "Conv2D", "name": "conv2d_2", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "conv2d_2", "trainable": true, "dtype": "float32", "filters": 192, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { "-1": 128 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 14, 14, 128] } }
    �

        , kernel
        -bias
    .regularization_losses

    /  variables
    0trainable_variables

    1  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
    _tf_keras_layer�{ "class_name": "Conv2D", "name": "conv2d_3", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "conv2d_3", "trainable": true, "dtype": "float32", "filters": 128, "kernel_size": { "class_name": "__tuple__", "items": [3, 3] }, "strides": { "class_name": "__tuple__", "items": [1, 1] }, "padding": "valid", "data_format": "channels_last", "dilation_rate": { "class_name": "__tuple__", "items": [1, 1] }, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { "-1": 192 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 12, 12, 192] } }
    �
        2regularization_losses

        3  variables
    4trainable_variables

    5  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
    _tf_keras_layer�{ "class_name": "MaxPooling2D", "name": "max_pooling2d_2", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "max_pooling2d_2", "trainable": true, "dtype": "float32", "pool_size": { "class_name": "__tuple__", "items": [3, 3] }, "padding": "valid", "strides": { "class_name": "__tuple__", "items": [2, 2] }, "data_format": "channels_last" }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": 4, "max_ndim": null, "min_ndim": null, "axes": { } } } }
    �
        6regularization_losses

        7  variables
    8trainable_variables

    9  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
    _tf_keras_layer�{ "class_name": "Flatten", "name": "flatten", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "flatten", "trainable": true, "dtype": "float32", "data_format": "channels_last" }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": null, "max_ndim": null, "min_ndim": 1, "axes": { } } } }
    �

        : kernel
        ;bias
    < regularization_losses

    =  variables
     >trainable_variables

    ?  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
        _tf_keras_layer�{ "class_name": "Dense", "name": "dense", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "dense", "trainable": true, "dtype": "float32", "units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": null, "max_ndim": null, "min_ndim": 2, "axes": { "-1": 2048 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 2048] } }
    �

        @kernel
        Abias
    Bregularization_losses

    C  variables
    Dtrainable_variables

    E  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
        _tf_keras_layer�{ "class_name": "Dense", "name": "dense_1", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "dense_1", "trainable": true, "dtype": "float32", "units": 96, "activation": "relu", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": null, "max_ndim": null, "min_ndim": 2, "axes": { "-1": 128 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 128] } }
    �

        Fkernel
        Gbias
    Hregularization_losses

    I  variables
    Jtrainable_variables

    K  keras_api
    +�&call_and_return_all_conditional_losses

    �__call__"�
        _tf_keras_layer�{ "class_name": "Dense", "name": "dense_2", "trainable": true, "expects_training_arg": false, "dtype": "float32", "batch_input_shape": null, "stateful": false, "config": { "name": "dense_2", "trainable": true, "dtype": "float32", "units": 7, "activation": "softmax", "use_bias": true, "kernel_initializer": { "class_name": "GlorotUniform", "config": { "seed": null } }, "bias_initializer": { "class_name": "Zeros", "config": { } }, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null }, "input_spec": { "class_name": "InputSpec", "config": { "dtype": null, "shape": null, "ndim": null, "max_ndim": null, "min_ndim": 2, "axes": { "-1": 96 } } }, "build_input_shape": { "class_name": "TensorShape", "items": [null, 96] } }
    �
        Liter

        Mbeta_1

    Nbeta_2
    Odecay
    P
    learning_ratem�m�m�m�&m�'m�,m�-m�:m�;m�@m�Am�Fm�Gm�v�v�v�v�&v�'v�, v�-v�: v�;v�@v�Av�Fv�Gv�"
    optimizer
    "
    trackable_list_wrapper
    �
    0
    1
    2
    3
    &4
    '5
    , 6
        -7
    : 8
    ;9
    @10
    A11
        F12
    G13"
    trackable_list_wrapper
    �
    0
    1
    2
    3
    &4
    '5
    , 6
    -7
    : 8
    ;9
        @10
    A11
    F12
    G13"
    trackable_list_wrapper
    �

    Qlayers
    
    regularization_losses
    Rmetrics
    S
    layer_metrics
        Tnon_trainable_variables

          variables
    trainable_variables
    Ulayer_regularization_losses

    �__call__
    �_default_save_signature
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
    _generic_user_object
    -
    �serving_default"

    signature_map
    ':% 2
    conv2d/kernel
    :  2 conv2d/bias
    "
    trackable_list_wrapper
    .
    0
    1"
    trackable_list_wrapper
    .
    0
        1"
    trackable_list_wrapper
    �

    Vlayers
    Wmetrics
    regularization_losses
    X
    layer_metrics
    Ynon_trainable_variables

      variables
        trainable_variables
    Zlayer_regularization_losses

        �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
    _generic_user_object
        "
        trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    �

    [layers
    \metrics
    regularization_losses
    ]
    layer_metrics
    ^non_trainable_variables

      variables
    trainable_variables
    _layer_regularization_losses

    �__call__
        +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
    _generic_user_object
    *: ( �2conv2d_1/kernel
    : �2
    conv2d_1/bias
    "
        trackable_list_wrapper
        .
        0
            1"
        trackable_list_wrapper
        .
        0
        1"
        trackable_list_wrapper
        �

        `layers
        ametrics
            regularization_losses
        b
        layer_metrics
        cnon_trainable_variables

          variables
         trainable_variables
        dlayer_regularization_losses

        �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
        _generic_user_object
        "
    trackable_list_wrapper
        "
        trackable_list_wrapper
    "
    trackable_list_wrapper
    �

    elayers
    fmetrics
    "regularization_losses
    g
    layer_metrics
    hnon_trainable_variables

    #	variables
    $trainable_variables
    ilayer_regularization_losses

    �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
    _generic_user_object
    +: )��2conv2d_2/kernel
    : �2
    conv2d_2/bias
    "
    trackable_list_wrapper
    .
    &0
    '1"
    trackable_list_wrapper
    .
    &0
    '1"
    trackable_list_wrapper
    �

    jlayers
    kmetrics
    (regularization_losses
    l
        layer_metrics
        mnon_trainable_variables

    )  variables
    *trainable_variables
    nlayer_regularization_losses

    �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
        _generic_user_object
        +: )��2conv2d_3/kernel
    : �2
    conv2d_3/bias
    "
    trackable_list_wrapper
        .
        , 0
    -1"
    trackable_list_wrapper
    .
        , 0
        -1"
    trackable_list_wrapper
    �

    olayers
    pmetrics
    .regularization_losses
    q
        layer_metrics
        rnon_trainable_variables

    /  variables
    0trainable_variables
    slayer_regularization_losses

    �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
        _generic_user_object
        "
    trackable_list_wrapper
        "
        trackable_list_wrapper
    "
    trackable_list_wrapper
    �

    tlayers
    umetrics
    2regularization_losses
    v
    layer_metrics
    wnon_trainable_variables

    3  variables
    4trainable_variables
    xlayer_regularization_losses

    �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
    _generic_user_object
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    �

        ylayers
        zmetrics
    6regularization_losses
    { 
    layer_metrics
    |non_trainable_variables

    7  variables
    8trainable_variables
     }layer_regularization_losses

    �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
    _generic_user_object
    : 
    ��2 dense/kernel
    : �2
    dense/bias
    "
    trackable_list_wrapper
    .
    : 0
    ;1"
        trackable_list_wrapper
    .
    : 0
    ;1"
    trackable_list_wrapper
    �

    ~layers
    metrics
    < regularization_losses
    �
    layer_metrics
        �non_trainable_variables

        =  variables
     >trainable_variables
    �layer_regularization_losses

    �__call__
    +�&call_and_return_all_conditional_losses
        '�"call_and_return_conditional_losses"
        _generic_user_object
    !:   �`2dense_1/kernel
    :`2 dense_1/bias
    "
    trackable_list_wrapper
    .
    @0
    A1"
    trackable_list_wrapper
    .
    @0
    A1"
    trackable_list_wrapper
        �
    �layers
        �metrics
    Bregularization_losses
    �
    layer_metrics
    �non_trainable_variables

    C  variables
    Dtrainable_variables
    �layer_regularization_losses

    �__call__
    +�&call_and_return_all_conditional_losses
    '�"call_and_return_conditional_losses"
        _generic_user_object
        :`2dense_2/kernel
    : 2 dense_2/bias
    "
    trackable_list_wrapper
    .
    F0
    G1"
    trackable_list_wrapper
        .
        F0
        G1"
        trackable_list_wrapper
        �
            �layers
            �metrics
        Hregularization_losses
        �
            layer_metrics
            �non_trainable_variables

        I  variables
        Jtrainable_variables
        �layer_regularization_losses

        �__call__
        +�&call_and_return_all_conditional_losses
        '�"call_and_return_conditional_losses"
        _generic_user_object
    :    (2  Adam/iter
    :  (2 Adam/beta_1
    :  (2 Adam/beta_2
    :  (2
    Adam/decay
    :  (2Adam/learning_rate
    n
    0
    1
    2
    3
    4
    5
    6
    7
      8
    
        9
         10"
    trackable_list_wrapper
    0
    �0
        �1"
    trackable_list_wrapper
    "
    trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
        trackable_list_wrapper
        "
    trackable_list_wrapper
        "
        trackable_dict_wrapper
        "
        trackable_list_wrapper
        "
        trackable_list_wrapper
        "
        trackable_list_wrapper
        "
        trackable_list_wrapper
        "
        trackable_dict_wrapper
        "
        trackable_list_wrapper
        "
        trackable_list_wrapper
        "
            trackable_list_wrapper
            "
        trackable_list_wrapper
            "
            trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
        trackable_list_wrapper
        "
    trackable_list_wrapper
        "
        trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
        trackable_list_wrapper
        "
    trackable_list_wrapper
        "
        trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
        trackable_list_wrapper
        "
    trackable_list_wrapper
        "
        trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
    trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    "
        trackable_list_wrapper
        "
    trackable_list_wrapper
        "
        trackable_dict_wrapper
    "
    trackable_list_wrapper
    "
    trackable_list_wrapper
    �

    �total

    �count
    �  variables
    �  keras_api"�
    _tf_keras_metricj{ "class_name": "Mean", "name": "loss", "dtype": "float32", "config": { "name": "loss", "dtype": "float32" } }
    �

        �total

        �count
    �
    _fn_kwargs
    �  variables
        �  keras_api"�
    _tf_keras_metric�{ "class_name": "MeanMetricWrapper", "name": "accuracy", "dtype": "float32", "config": { "name": "accuracy", "dtype": "float32", "fn": "categorical_accuracy" } }
    :   (2total
        :   (2count
        0
        �0
        �1"
        trackable_list_wrapper
        .
            �  variables"
            _generic_user_object
        :   (2total
        :   (2count
        "
        trackable_dict_wrapper
    0
    �0
    �1"
    trackable_list_wrapper
    .
    �  variables"
    _generic_user_object
, : * 2Adam/conv2d/kernel/m
    :  2Adam/conv2d/bias/m
    /: - �2Adam/conv2d_1/kernel/m
    !: �2Adam/conv2d_1/bias/m
    0: .��2Adam/conv2d_2/kernel/m
    !:�2Adam/conv2d_2/bias/m
    0: .��2Adam/conv2d_3/kernel/m
    !: �2Adam/conv2d_3/bias/m
    %: #
    ��2Adam/dense/kernel/m
    : �2Adam/dense/bias/m
    &: $  �`2Adam/dense_1/kernel/m
    : `2Adam/dense_1/bias/m
    %: #`2Adam/dense_2/kernel/m
        : 2Adam/dense_2/bias/m
, : * 2Adam/conv2d/kernel/v
    :  2Adam/conv2d/bias/v
    /: - �2Adam/conv2d_1/kernel/v
    !: �2Adam/conv2d_1/bias/v
    0: .��2Adam/conv2d_2/kernel/v
    !: �2Adam/conv2d_2/bias/v
    0: .��2Adam/conv2d_3/kernel/v
    !: �2Adam/conv2d_3/bias/v
    %: #
    ��2Adam/dense/kernel/v
    : �2Adam/dense/bias/v
    &: $  �`2Adam/dense_1/kernel/v
    : `2Adam/dense_1/bias/v
    %: #`2Adam/dense_2/kernel/v
    : 2Adam/dense_2/bias/v
    �2�
    E__inference_sequential_layer_call_and_return_conditional_losses_44897
    E__inference_sequential_layer_call_and_return_conditional_losses_44542
    E__inference_sequential_layer_call_and_return_conditional_losses_44585
        E__inference_sequential_layer_call_and_return_conditional_losses_44839�
        ���
    FullArgSpec1
    args)�&
    jself
    jinputs

    jtraining
    jmask
    varargs
     
    varkw
     
    defaults �
        p 
        
     

        kwonlyargs� 
    kwonlydefaults� 
    annotations� *
     
        �2�
        *__inference_sequential_layer_call_fn_44930
        *__inference_sequential_layer_call_fn_44963
    *__inference_sequential_layer_call_fn_44738
        *__inference_sequential_layer_call_fn_44662�
        ���
    FullArgSpec1
    args)�&
    jself
    jinputs

    jtraining
    jmask
    varargs
     
    varkw
     
    defaults �
    p 
    
     

    kwonlyargs� 
    kwonlydefaults� 
    annotations� *
     
    �2�
    __inference__wrapped_model_44295�
    ���
    FullArgSpec
    args� 
        varargsjargs
        varkw
         
        defaults
         

        kwonlyargs� 
        kwonlydefaults
         
        annotations� *5�2
        0�-
        conv2d_input �����������
        �2�
        A__inference_conv2d_layer_call_and_return_conditional_losses_44307�
    ���
    FullArgSpec
    args�
        jself
    jinputs
    varargs
     
        varkw
         
        defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *7�4
    2�/+ ��������� ��������� ���������
    �2�
    &__inference_conv2d_layer_call_fn_44317�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
         

        kwonlyargs� 
    kwonlydefaults
     
    annotations� *7�4
    2�/+ ��������� ��������� ���������
    �2�
    H__inference_max_pooling2d_layer_call_and_return_conditional_losses_44323�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
         

        kwonlyargs� 
        kwonlydefaults
         
        annotations� *@�=
    ;�84 ��������� ��������� ��������� ���������
        �2�
        -__inference_max_pooling2d_layer_call_fn_44329�
    ���
    FullArgSpec
    args�
        jself
    jinputs
    varargs
     
        varkw
         
        defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *@�=
;�84 ��������� ��������� ��������� ���������
    �2�
        C__inference_conv2d_1_layer_call_and_return_conditional_losses_44341�
        ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
         
        defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *7�4
    2�/+ ��������� ��������� ��������� 
    �2�
    (__inference_conv2d_1_layer_call_fn_44351�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
         
        defaults
         

        kwonlyargs� 
        kwonlydefaults
         
        annotations� *7�4
        2�/+ ��������� ��������� ��������� 
        �2�
        J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_44357�
        ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
     

        kwonlyargs� 
    kwonlydefaults
     
    annotations� *@�=
;�84 ��������� ��������� ��������� ���������
    �2�
    /__inference_max_pooling2d_1_layer_call_fn_44363�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
         
        annotations� *@�=
    ;�84 ��������� ��������� ��������� ���������
    �2�
    C__inference_conv2d_2_layer_call_and_return_conditional_losses_44375�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
         
        varkw
         
            defaults
             

        kwonlyargs� 
            kwonlydefaults
         
        annotations� *8�5
        3�0,  ��������� ��������� ����������
        �2�
    (__inference_conv2d_2_layer_call_fn_44385�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
         
        annotations� *8�5
    3�0,  ��������� ��������� ����������
    �2�
    C__inference_conv2d_3_layer_call_and_return_conditional_losses_44397�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
     

        kwonlyargs� 
    kwonlydefaults
     
    annotations� *8�5
    3�0,  ��������� ��������� ����������
    �2�
        (__inference_conv2d_3_layer_call_fn_44407�
    ���
        FullArgSpec
        args�
    jself
    jinputs
        varargs
         
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *8�5
    3�0,  ��������� ��������� ����������
    �2�
    J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_44413�
    ���
    FullArgSpec
    args�
        jself
    jinputs
    varargs
     
        varkw
         
        defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *@�=
;�84 ��������� ��������� ��������� ���������
    �2�
        /__inference_max_pooling2d_2_layer_call_fn_44419�
    ���
        FullArgSpec
        args�
    jself
    jinputs
        varargs
         
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *@�=
    ;�84 ��������� ��������� ��������� ���������
        �2�
        B__inference_flatten_layer_call_and_return_conditional_losses_44969�
        ���
        FullArgSpec
        args�
        jself
        jinputs
    varargs
     
    varkw
     
    defaults
     

        kwonlyargs� 
    kwonlydefaults
     
    annotations� *
     
    �2�
    '__inference_flatten_layer_call_fn_44974�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
         
        varkw
     
        defaults
         

    kwonlyargs� 
        kwonlydefaults
         
        annotations� *
         
        �2�
        @__inference_dense_layer_call_and_return_conditional_losses_44985�
        ���
        FullArgSpec
        args�
        jself
        jinputs
        varargs
         
        varkw
         
        defaults
         

        kwonlyargs� 
        kwonlydefaults
         
        annotations� *
     
    �2�
    %__inference_dense_layer_call_fn_44994�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
         
        annotations� *
     
    �2�
    B__inference_dense_1_layer_call_and_return_conditional_losses_45005�
    ���
    FullArgSpec
    args�
    jself
    jinputs
    varargs
     
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
         
        annotations� *
     
    �2�
    '__inference_dense_1_layer_call_fn_45014�
    ���
    FullArgSpec
        args�
    jself
        jinputs
        varargs
     
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *
     
    �2�
    B__inference_dense_2_layer_call_and_return_conditional_losses_45025�
        ���
    FullArgSpec
        args�
    jself
        jinputs
        varargs
     
    varkw
     
    defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *
     
    �2�
    '__inference_dense_2_layer_call_fn_45034�
        ���
        FullArgSpec
    args�
        jself
    jinputs
    varargs
     
        varkw
         
        defaults
     

    kwonlyargs� 
    kwonlydefaults
     
    annotations� *
     
    7B5
    #__inference_signature_wrapper_44781conv2d_input�
    __inference__wrapped_model_44295�&',-:;@AFG?�<
    5�2
    0�-
        conv2d_input �����������
    � "1�.
    ,
        dense_2!�
        dense_2 ����������
    C__inference_conv2d_1_layer_call_and_return_conditional_losses_44341�I�F
    ?�<
    : �7
        inputs+ ��������� ��������� ��������� 
    � "@�=
    6�3
    0,  ��������� ��������� ����������
    � �
    (__inference_conv2d_1_layer_call_fn_44351�I�F
    ?�<
    : �7
        inputs+ ��������� ��������� ��������� 
    � "3�0,�����������������������������
    C__inference_conv2d_2_layer_call_and_return_conditional_losses_44375�&'J�G
    @�=
;�8
    inputs,  ��������� ��������� ����������
    � "@�=
    6�3
    0, ��������� ��������� ����������
    � �
    (__inference_conv2d_2_layer_call_fn_44385�&'J�G
        @�=
;�8
    inputs,  ��������� ��������� ����������
    � "3�0,�����������������������������
    C__inference_conv2d_3_layer_call_and_return_conditional_losses_44397�, -J�G
    @�=
;�8
    inputs,  ��������� ��������� ����������
    � "@�=
    6�3
    0, ��������� ��������� ����������
    � �
    (__inference_conv2d_3_layer_call_fn_44407�, -J�G
    @�=
;�8
    inputs,  ��������� ��������� ����������
    � "3�0,�����������������������������
    A__inference_conv2d_layer_call_and_return_conditional_losses_44307�I�F
    ?�<
    : �7
    inputs+ ��������� ��������� ���������
    � "?�<
    5�2
    0+ ��������� ��������� ��������� 
    � �
    &__inference_conv2d_layer_call_fn_44317�I�F
    ?�<
    : �7
        inputs+ ��������� ��������� ���������
    � "2�/+��������������������������� �
        B__inference_dense_1_layer_call_and_return_conditional_losses_45005]@A0�-
    &�#
    !�
    inputs ����������
    � "%�"
    �
    0 ���������`
    � {
'__inference_dense_1_layer_call_fn_45014P@A0�-
    &�#
    !�
    inputs ����������
    � "����������`�
    B__inference_dense_2_layer_call_and_return_conditional_losses_45025\FG/�,
    %�"
    �
    inputs ���������`
    � "%�"
    �
    0 ���������
    � z
    '__inference_dense_2_layer_call_fn_45034OFG/�,
    %�"
    �
    inputs ���������`
    � "�����������
    @__inference_dense_layer_call_and_return_conditional_losses_44985^: ;0�-
    &�#
    !�
    inputs ����������
    � "&�#
    �
    0 ����������
        � z
        %__inference_dense_layer_call_fn_44994Q: ;0�-
        &�#
        !�
        inputs ����������
        � "������������
        B__inference_flatten_layer_call_and_return_conditional_losses_44969b8�5
        .�+
        )�&
        inputs ����������
    � "&�#
    �
    0 ����������
    � �
    '__inference_flatten_layer_call_fn_44974U8�5
    .�+
    )�&
    inputs ����������
    � "������������
    J__inference_max_pooling2d_1_layer_call_and_return_conditional_losses_44357�R�O
    H�E
    C�@
    inputs4 ��������� ��������� ��������� ���������
    � "H�E
    >�;
    04 ��������� ��������� ��������� ���������
    � �
    /__inference_max_pooling2d_1_layer_call_fn_44363�R�O
    H�E
    C�@
    inputs4 ��������� ��������� ��������� ���������
    � ";�84�������������������������������������
    J__inference_max_pooling2d_2_layer_call_and_return_conditional_losses_44413�R�O
    H�E
    C�@
    inputs4 ��������� ��������� ��������� ���������
    � "H�E
    >�;
    04 ��������� ��������� ��������� ���������
    � �
        /__inference_max_pooling2d_2_layer_call_fn_44419�R�O
    H�E
    C�@
    inputs4 ��������� ��������� ��������� ���������
    � ";�84�������������������������������������
    H__inference_max_pooling2d_layer_call_and_return_conditional_losses_44323�R�O
    H�E
    C�@
    inputs4 ��������� ��������� ��������� ���������
    � "H�E
    >�;
        04 ��������� ��������� ��������� ���������
    � �
    -__inference_max_pooling2d_layer_call_fn_44329�R�O
    H�E
    C�@
    inputs4 ��������� ��������� ��������� ���������
    � ";�84�������������������������������������
    E__inference_sequential_layer_call_and_return_conditional_losses_44542�&',-:;@AFGG�D
    =�:
    0�-
    conv2d_input �����������
    p
    
     
    � "%�"
    �
    0 ���������
    � �
    E__inference_sequential_layer_call_and_return_conditional_losses_44585�&',-:;@AFGG�D
    =�:
    0�-
    conv2d_input �����������
    p 
    
         
        � "%�"
        �
    0 ���������
    � �
    E__inference_sequential_layer_call_and_return_conditional_losses_44839z&',-:;@AFGA�>
    7�4
    *�'
    inputs �����������
    p
    
     
    � "%�"
    �
    0 ���������
    � �
    E__inference_sequential_layer_call_and_return_conditional_losses_44897z&',-:;@AFGA�>
    7�4
    *�'
    inputs �����������
    p 
    
     
� "%�"
    �
    0 ���������
    � �
    *__inference_sequential_layer_call_fn_44662s&',-:;@AFGG�D
    =�:
        0�-
        conv2d_input �����������
    p
    
     
    � "�����������
    *__inference_sequential_layer_call_fn_44738s&',-:;@AFGG�D
    =�:
    0�-
    conv2d_input �����������
    p 
        
         
        � "�����������
    *__inference_sequential_layer_call_fn_44930m&',-:;@AFGA�>
    7�4
    *�'
    inputs �����������
    p
    
     
    � "�����������
    *__inference_sequential_layer_call_fn_44963m&',-:;@AFGA�>
    7�4
    *�'
    inputs �����������
    p 
    
     
    � "�����������
    #__inference_signature_wrapper_44781�&',-:;@AFGO�L
    � 
    E�B
    @
    conv2d_input0�-
    conv2d_input �����������"1�.
,
    dense_2!�
    dense_2 ���������
