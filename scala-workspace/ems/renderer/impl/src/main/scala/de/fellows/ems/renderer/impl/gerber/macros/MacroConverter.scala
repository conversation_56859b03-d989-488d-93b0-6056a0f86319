package de.fellows.ems.renderer.impl.gerber.macros

import de.fellows.ems.pcb.model.graphics.{GPoint, Geometry, GerberApertureDefinition, Macro, PrimitiveUsage, Rectangle, VariableAssignment}
import de.fellows.ems.renderer.impl.gerber.GerberExceptions.ExOps
import de.fellows.ems.renderer.impl.gerber.builders.BoundsBuilder
import de.fellows.ems.renderer.impl.gerber.{Arith, Graphics}
import de.fellows.utils.logging.StackrateLogging
import org.apache.batik.ext.awt.geom.Polygon2D

import java.awt.geom.{Ellipse2D, Rectangle2D}
import scala.collection.mutable

sealed trait MacroPrimitive {
  def bounds(): Rectangle2D

}

case class CirclePrimitive(
    exposure: Int,
    ellipse: Ellipse2D,
    rotation: Option[Double]
) extends MacroPrimitive {
  override def bounds(): Rectangle2D = ellipse.getBounds2D
}
case class VectorLinePrimitive(
    exposure: Int,
    width: Double,
    start: GPoint,
    end: GPoint,
    rotation: Option[Double],
    edges: Rectangle
) extends MacroPrimitive {
  override def bounds(): Rectangle2D = {
    val r = new Rectangle2D.Double()
    r.setFrameFromDiagonal(edges.p1.x, edges.p1.y, edges.p3.x, edges.p3.y)
    r
  }

}
case class CenterLinePrimitive(
    exposure: Int,
    width: Double,
    height: Double,
    center: GPoint,
    rectangle: Rectangle2D,
    rotation: Option[Double]
) extends MacroPrimitive {
  override def bounds(): Rectangle2D =
    rectangle
}
case class OutlinePrimitive(exposure: Int, vertices: Int, polys: Polygon2D, rotation: Option[Double])
    extends MacroPrimitive {

  override def bounds(): Rectangle2D = polys.getBounds2D
}
case class PolygonPrimitive(
    exposure: Int,
    polygon: Polygon2D,
    center: GPoint,
    rotation: Option[Double]
) extends MacroPrimitive {
  override def bounds(): Rectangle2D = polygon.getBounds2D
}
case class ThermalPrimitive(
    outerDiameter: Ellipse2D,
    innerDiameter: Ellipse2D,
    verticalChannel: Rectangle2D,
    horizontalChannel: Rectangle2D,
    center: GPoint,
    outerDim: Double,
    innerDim: Double,
    gap: Double,
    rotation: Option[Double]
) extends MacroPrimitive {
  override def bounds(): Rectangle2D =
    outerDiameter.getBounds2D
}

case class MoireRing(outer: Ellipse2D, inner: Option[Ellipse2D])
case class MoirePrimitive(
    moireRings: Seq[MoireRing],
    verticalChannel: Rectangle2D,
    horizontalChannel: Rectangle2D,
    center: GPoint,
    outerDiameter: Double,
    thickness: Double,
    gap: Double,
    maxRings: Int,
    crosshairThickness: Double,
    crosshairLength: Double,
    rotation: Option[Double]
) extends MacroPrimitive {

  override def bounds(): Rectangle2D =
    new Rectangle2D.Double(
      center.x - (outerDiameter / 2),
      center.y - (outerDiameter / 2),
      outerDiameter,
      outerDiameter
    )
}

/** Converts a macro to a graphical element of type T
  */
abstract class MacroConverter[T](ad: GerberApertureDefinition, mac: Macro, scaling: Int) extends StackrateLogging {

  def resolve(vars: mutable.Map[Int, BigDecimal])(expression: String): BigDecimal =
    Arith.calculate(expression, vars.toMap)

  def result: T

  def draw(primitive: MacroPrimitive, target: GPoint)

  def buildPrimitive(
      primitive: Int,
      macroModifier: Seq[String],
      vars: mutable.Map[Int, BigDecimal]
  ): MacroPrimitive = {
    val params: Seq[BigDecimal] = macroModifier.map(resolve(vars))
    primitive match {
      case 1 =>
        if (macroModifier.size < 4) {
          s"Macro: Circle expects at least 4 modifiers and got $macroModifier" !!
        }

        val width  = params(1) * scaling
        val radius = width / 2

        val centerX = params(2) * scaling
        val centerY = params(3) * scaling

        val x = centerX - radius
        val y = centerY - radius

        val ellipse = new Ellipse2D.Double(x.toDouble, y.toDouble, width.toDouble, width.toDouble)
        CirclePrimitive(
          params.head.toInt,
          ellipse,
          params.lift(4).map(_.toDouble)
        )

      case 20 | 2 =>
        createVectorLine(macroModifier, params)

      case 21 =>
        if (macroModifier.size < 5) {
          s"Macro: Center Line expects at least 5 modifiers and got $macroModifier" !!
        }

        val w = params(1) * scaling
        val h = params(2) * scaling

        val cx   = params(3) * scaling
        val cy   = params(4) * scaling
        val x    = cx - (w / 2)
        val y    = cy - (h / 2)
        val edge = GPoint(x.toDouble, y.toDouble)
        CenterLinePrimitive(
          exposure = params.head.toInt,
          width = w.toDouble,
          height = h.toDouble,
          center = GPoint(cx.toDouble, cy.toDouble),
          new Rectangle2D.Double(edge.x, edge.y, w.toDouble, h.toDouble),
          rotation = params.lift(5).map(_.toDouble)
        )

      case 22 =>
        if (macroModifier.size < 5) {
          s"Macro: Center Line expects at least 5 modifiers and got $macroModifier" !!
        }

        val w = params(1) * scaling
        val h = params(2) * scaling

        val x = params(3) * scaling
        val y = params(4) * scaling

        val cx   = params(3) * scaling + (w / 2)
        val cy   = params(4) * scaling + (h / 2)
        val edge = GPoint(x.toDouble, y.toDouble)
        CenterLinePrimitive(
          exposure = params.head.toInt,
          width = w.toDouble,
          height = h.toDouble,
          center = GPoint(cx.toDouble, cy.toDouble),
          rectangle = new Rectangle2D.Double(edge.x, edge.y, w.toDouble, h.toDouble),
          rotation = params.lift(5).map(_.toDouble)
        )

      case 4 =>
        if (macroModifier.size < 6) {
          s"Macro: Outline expects at least 6 modifiers and got $macroModifier" !!
        }


        val numVertices = params(1)

        // according to the spec, the rest should be exactly `2 * numVertices + optional_rotation`.
        // however, this appears to not always be the case. So we need to be a bit more flexible here and lift the values,
        // so we draw as much as we can
        val rest = params.splitAt(2)._2.lift

        val poly = new Polygon2D()

        for (i <- 0.to(numVertices.toInt * 2, 2))
          for {
            i1 <- rest(i)
            i2 <- rest(i + 1)
          } yield poly.addPoint((i1 * scaling).toFloat, (i2 * scaling).toFloat)


        val rotation = rest((numVertices * 2 + 2).intValue)
        OutlinePrimitive(
          params.head.toInt,
          numVertices.toInt,
          poly,
          rotation.map(_.toDouble)
        )
      case 5 =>
        if (macroModifier.size < 5) {
          s"Macro: Polygon expects at least 5 modifiers and got $macroModifier" !!
        }

        val center = GPoint(params(2).toDouble * scaling, params(3).toDouble * scaling)
        PolygonPrimitive(
          exposure = params.head.toInt,
          polygon = Graphics.createNaturalPolygon(
            params(1),
            GPoint(params(2).toDouble * scaling, params(3).toDouble * scaling),
            params(4) * scaling / 2,
            params.lift(5).map(_.toDouble)
          ),
          center = center,
          rotation = None
        )

      case 6 =>
        if (macroModifier.size < 8) {
          s"Macro: Moire expects at least 8 modifiers and got $macroModifier" !!
        }

        val center    = GPoint(params(0).toDouble * scaling, params(1).toDouble * scaling)
        val diameter  = params(2).toDouble * scaling
        val radius    = diameter / 2
        val zero      = center - radius
        val thickness = params(3).toDouble * scaling
        val gap       = params(4).toDouble * scaling
        val maxRings  = params(5).toInt

        def addRing(diameter: Double, target: GPoint): MoireRing = {

          val outer = new Ellipse2D.Double(target.getX, target.getY, diameter, diameter)

          val inner =
            if (radius - thickness > 0) {
              val innerzero = target + thickness
              val inner = (new Ellipse2D.Double(
                innerzero.getX,
                innerzero.getY,
                (diameter - (thickness * 2)),
                (diameter - (thickness * 2))
              ))

              Some(inner)
            } else {
              None
            }

          MoireRing(outer, inner)
        }

        def addRings(diameter: Double, target: GPoint, ring: Int): Seq[MoireRing] = {

          val thisRing = addRing(diameter, target)

          val next    = diameter - (thickness * 2) - (gap * 2)
          val newZero = target + thickness + gap
          if (next > 0 && ring < maxRings - 1) {
            thisRing +: addRings(next, newZero, ring + 1)
          } else {
            Seq(thisRing)
          }
        }

        val moireRings = addRings(diameter, zero, 0)

        val chThickness = params(6).doubleValue * scaling
        val chLength    = params(7).doubleValue * scaling
        val chVertical = new Rectangle2D.Double(
          (center.getBX - (chThickness / 2)),
          (center.getBY - (chLength / 2)),
          chThickness,
          chLength
        )
        val chHorizontal = new Rectangle2D.Double(
          (center.getBX - (chLength / 2)),
          (center.getBY - (chThickness / 2)),
          chLength,
          chThickness
        )

        MoirePrimitive(
          moireRings = moireRings,
          verticalChannel = chVertical,
          horizontalChannel = chHorizontal,
          center = center,
          outerDiameter = diameter,
          thickness = thickness,
          gap = gap,
          maxRings = maxRings,
          crosshairThickness = chThickness,
          crosshairLength = chLength,
          params.lift(8).map(_.toDouble)
        )
      case 7 =>
        if (macroModifier.size < 5) {
          s"Macro: Thermal expects at least 5 modifiers and got $macroModifier" !!
        }

        val center   = GPoint(params(0).toDouble, params(1).toDouble) * scaling
        val outerDim = params(2).toDouble * scaling
        val innerDim = params(3).toDouble * scaling

        val outerZero = center - (outerDim / 2)
        val innerZero = center - (innerDim / 2)

        val gap      = params(4).toDouble * scaling
        val rotation = params.lift(5).map(_.toDouble)

        val outerDiameter = new Ellipse2D.Double(
          outerZero.getX,
          outerZero.getY,
          outerDim,
          outerDim
        )

        val innerDiameter = new Ellipse2D.Double(
          innerZero.getX,
          innerZero.getY,
          innerDim,
          innerDim
        )

        val chVertical = new Rectangle2D.Double(
          (center.getBX - (gap / 2)),
          (center.getBY - (outerDim / 2)),
          gap,
          outerDim
        )
        val chHorizontal = new Rectangle2D.Double(
          (center.getBX - (outerDim / 2)),
          (center.getBY - (gap / 2)),
          outerDim,
          gap
        )

        ThermalPrimitive(
          outerDiameter,
          innerDiameter,
          chVertical,
          chHorizontal,
          center,
          outerDim,
          innerDim,
          gap,
          rotation
        )

      case x =>
        s"Macro Primitive $x is unknown; Aperture: ${ad.id}, Macro: ${mac.name}" !!
    }

  }

  def flash(
      primitive: Int,
      macroModifier: Seq[String],
      vars: mutable.Map[Int, BigDecimal],
      target: GPoint
  ) = {
    val prim = buildPrimitive(primitive, macroModifier, vars)
    draw(prim, target)
  }

  private def createVectorLine(
      macroModifier: Seq[String],
      params: Seq[BigDecimal]
  ): VectorLinePrimitive = {
    if (macroModifier.size < 6) {
      s"Macro: Vector Line expects at least 6 modifiers and got $macroModifier" !!
    }

    val width = params(1).toDouble * scaling
    val rad   = width / 2

    val from = GPoint(params(2).toDouble, params(3).toDouble) * scaling
    val to   = GPoint(params(4).toDouble, params(5).toDouble) * scaling

    val radians = Geometry.calculateRadians(from, to, to.getX >= from.getX)

    val p1 = Geometry.rotate(from + GPoint(0, rad), from, radians)
    val p2 = Geometry.rotate(from - GPoint(0, rad), from, radians)
    val p3 = Geometry.rotate(to - GPoint(0, rad), to, radians)
    val p4 = Geometry.rotate(to + GPoint(0, rad), to, radians)

    VectorLinePrimitive(
      exposure = params.head.toInt,
      width = width,
      start = from,
      end = to,
      rotation = params.lift(6).map(_.toDouble),
      edges = Rectangle(p1, p2, p3, p4)
    )
  }

  def bounds(target: GPoint) = {
    val bounds = new BoundsBuilder()
    executeMacro { (a, b, c) =>
      val primitive = buildPrimitive(a, b, c)
      val d         = primitive.bounds()

      bounds.extend(new Rectangle2D.Double(
        d.getX + target.x.doubleValue,
        d.getY + target.y.doubleValue,
        d.getWidth,
        d.getHeight
      ))
    }

    bounds.result()
  }

  def executeMacro(proc: (Int, Seq[String], mutable.Map[Int, BigDecimal]) => Unit) = {
    val vars: mutable.Map[Int, BigDecimal] = mutable.Map()

    var i = 1
    for (arg <- ad.args) {
      vars.put(i, BigDecimal(arg))
      i += 1
    }

    mac.instructions.foreach {
      case VariableAssignment(variable, assignment) =>
        val res = Arith.calculate(assignment, vars.toMap)
        vars.put(variable, res)
      case PrimitiveUsage(primitive, modifier) =>
        if (ad.dCode == "D26") {
          logger.info(s"process primitive ${primitive} with vars ${vars}")
        }
        proc(primitive, modifier, vars)
    }
  }

  def flash(target: GPoint): T = {

    executeMacro(flash(_, _, _, target))

    result
  }
}
