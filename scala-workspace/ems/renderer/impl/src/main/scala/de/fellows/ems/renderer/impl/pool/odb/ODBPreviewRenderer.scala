package de.fellows.ems.renderer.impl.pool.odb

import de.fellows.ems.pcb.model.GraphicDefinition
import de.fellows.ems.pcb.model.graphics.GPoint
import de.fellows.ems.pcb.model.graphics.ops.Movement
import de.fellows.ems.renderer.impl.gerber.builders.BoundsBuilder
import de.fellows.ems.renderer.impl.gerber.{
  Clockwise => GClockwise,
  Counterclockwise => GCounterClockwise,
  InterpolationMode
}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.ems.renderer.impl.simple.SimpleGerberProcessor.{findArcParameters, getArcPositions}
import de.fellows.ems.renderer.impl.simple.SimpleGerberRenderer.setPrecision
import de.fellows.ems.renderer.impl.simple._
import de.fellows.utils.logging.StackrateLogging
import de.luminovo.odb.odbpp.model.constants.{Clockwise, CounterClockwise, Orientation0}
import de.luminovo.odb.odbpp.model.features.symbols.{BasicStandardSymbol, RoundSymbol, SquareSymbol, UserSymbol}
import de.luminovo.odb.odbpp.model.{ODBFeatures, SymbolUsage}

import scala.collection.mutable
import scala.util.Random

class ODBPreviewRenderer(
    layer: String,
    features: ODBFeatures,
    userSymbols: ODBUserSymbolPool,
    fontPool: ODBFontPool
) extends StackrateLogging {

  def renderToString(scale: Int) = {
    val g2d                                                                                   = Renderer.createGraphics
    val usedIds                                                                               = Set.newBuilder[Int]
    val lines: mutable.Map[Int, mutable.Builder[SVGPathInstruction, Seq[SVGPathInstruction]]] = mutable.Map()
    val featureLines                                                                          = Seq.newBuilder[String]

    val bounds = new BoundsBuilder()

    val symbols = features.symbols.map(x => x.symbolId -> x).toMap

    features.records.foreach { rec =>
      // collect symbol usage to know which defs we have to add
      rec match {
        case x: SymbolUsage => usedIds += x.symNum
        case _              =>
      }

      rec match {
        case x: ODBFeatures.LineRecord =>
          val linesForSymbol = lines.getOrElse(x.symNum, Seq.newBuilder[SVGPathInstruction])

          val instructions: Seq[SVGPathInstruction] = drawLine(scale, bounds, x)

          linesForSymbol ++= instructions
          lines.put(x.symNum, linesForSymbol)
        case x: ODBFeatures.PadRecord =>
          val svgLine: String = drawPad(scale, bounds, x)
          featureLines += svgLine

        case record: ODBFeatures.ArcRecord =>
          val linesForSymbol = lines.getOrElse(record.symNum, Seq.newBuilder[SVGPathInstruction])

          val instructions = drawArc(scale, bounds, record)

          linesForSymbol ++= instructions
          lines.put(record.symNum, linesForSymbol)

        case x: ODBFeatures.TextRecord =>
          ODBGraphicsFactory.renderTextPath(x, scale, fontPool, flip = true) match {
            case Left(value) => ???
            case Right(value) =>
              featureLines += s"""<path class="text" stroke="none"  d="${SimpleGerberRenderer.optimizePath(
                  PathTranslator.translate(value.getPathIterator(null))
                )}" />"""
          }

        case x: ODBFeatures.BarcodeRecord => ???
        case x: ODBFeatures.SurfaceRecord =>
          val polygonCurve = drawSurface(scale, x, bounds)
          featureLines += s"""<path stroke="none"  d="${SimpleGerberRenderer.optimizePath(polygonCurve)}" />"""
      }

    }

    val usedIdsSet = usedIds.result()
    val defs = features.symbols
      .filter(s => usedIdsSet.contains(s.symbolId))
      .flatMap { odbSymbol =>
        odbSymbol match {
          case x: BasicStandardSymbol =>
            val symbolDefId = s"${layer}def${odbSymbol.symbolId}"
            Some(SVGSymbolRenderer.createDef(
              x = x,
              id = symbolDefId,
              scale = scale,
              g2d = g2d.getShapeConverter,
              flip = true
            )
              .map(s => GraphicDefinition(symbolDefId, s)))

          case x: UserSymbol =>
            val symbolDefId = s"${layer}def${odbSymbol.symbolId}"
            Some(SVGSymbolRenderer.createDef(
              x,
              symbolDefId,
              scale,
              g2d.getShapeConverter,
              userSymbols,
              fontPool,
              flip = true
            )
              .map(s => GraphicDefinition(symbolDefId, s)))

          case x => throw new NotImplementedError(s"symbol type not implemented ${x}")
        }
      }.flatten

    lines.foreach { x =>
      val (sid, instructions) = x

      val instructionList = instructions.result()
      val svgString       = SimpleGerberRenderer.optimizePath(instructionList)

      val dia = symbols(sid) match {
        case symbol: RoundSymbol  => symbol.d * scale
        case symbol: SquareSymbol => symbol.dia * scale
        case _                    => throw new NotImplementedError("symbol type not implemented")
      }

      featureLines += s"""<path sym="${sid}" d="${svgString}" fill="none" stroke-width="${setPrecision(
          dia
        )}" stroke-linecap="round"/>"""
    }

    val finalBounds = bounds.grow(100).result()

    s"""<svg class="gerber-svg"  fill="black" stroke="black"  xmlns="http://www.w3.org/2000/svg" viewBox="${finalBounds.getMinX} ${finalBounds.getMinY} ${finalBounds.getWidth} ${finalBounds.getHeight}" xmlns:xlink="http://www.w3.org/1999/xlink" ><defs>${
        defs.map(_.path).mkString("\n")
      }</defs><g>${featureLines.result().mkString("\n")}</g></svg>"""
  }

  private def drawSurface(scale: Int, x: ODBFeatures.SurfaceRecord, bounds: BoundsBuilder) =
    x.polygons.flatMap { p =>
      var lastPoint = setPrecision(GPoint(p.xbs * scale, -p.ybs * scale))
      bounds.extend(lastPoint)

      M(lastPoint.x, lastPoint.y) +:
        p.curves.flatMap {
          case ODBFeatures.CurveLine(x, y, cw) =>
            lastPoint = setPrecision(GPoint(x * scale, -y * scale))
            bounds.extend(lastPoint)
            Seq(L(lastPoint.x, lastPoint.y))

          case ODBFeatures.CurveArc(xe, ye, xc, yc, cw) =>
            val end    = setPrecision(GPoint(xe, -ye) * scale)
            val center = setPrecision(GPoint(xc, -yc) * scale)
            val radius = end.distance(center)

            val from = lastPoint
            bounds.extend(from)
            bounds.extend(center)
            bounds.extend(center + radius)
            bounds.extend(center - radius)
            bounds.extend(end)
            val clw: InterpolationMode =
              if (cw == CounterClockwise) {
                GCounterClockwise()
              } else {
                GClockwise()
              }
            val arcpositions                                       = getArcPositions(from, end, center, clw)
            val (absSweep: Double, sweepFlag: Int, largeFlag: Int) = findArcParameters(arcpositions)

            if (end == lastPoint && absSweep != Math.PI * 2) {
              lastPoint = end.copy
              Seq(L(end.x, end.y))
            } else {
              val commands =
                (if (absSweep == Math.PI * 2) {
                   // full circle, we draw 2 arcs
                   val (mx, my) = (2 * center.x - end.x, (2 * center.y - end.y))
                   bounds.extend(GPoint(mx, my))
                   Seq(
                     A(
                       rx = radius,
                       ry = radius,
                       rotation = 0,
                       largeArcFlag = (largeFlag + 1) % 2,
                       sweepFlag = sweepFlag,
                       x = mx,
                       y = my
                     ),
                     A(
                       rx = radius,
                       ry = radius,
                       rotation = 0,
                       largeArcFlag = (largeFlag + 1) % 2,
                       sweepFlag = sweepFlag,
                       x = end.x,
                       y = end.y
                     )
                   )

                 } else {
                   Seq(
                     A(
                       rx = radius,
                       ry = radius,
                       rotation = 0,
                       largeArcFlag = (largeFlag + 1) % 2, // we flip the y axis, so we also flip the large flag
                       sweepFlag = sweepFlag,
                       x = end.x,
                       y = end.y
                     )
                   )
                 })
              lastPoint = end.copy

              commands
            }

        }
    }

  private def drawArc(scale: Int, bounds: BoundsBuilder, record: ODBFeatures.ArcRecord) = {
    val from   = setPrecision(GPoint(record.xs, -record.ys) * scale)
    val to     = setPrecision(GPoint(record.xe, -record.ye) * scale)
    val center = setPrecision(GPoint(record.xc, -record.yc) * scale)

    bounds.extend(from)
    bounds.extend(to)
    bounds.extend(center)

    val movement = Movement(from, to)
    val clkwse =
      if (record.direction == Clockwise) {
        GClockwise()
      } else {
        GCounterClockwise()
      }

    val arcPositions                                       = getArcPositions(from, to, center, clkwse)
    val (absSweep: Double, sweepFlag: Int, largeFlag: Int) = findArcParameters(arcPositions)

    val radius = from.distance(center)

    val instructions: Seq[SVGPathInstruction] =
      if (from == to && from == center) {
        Seq(
          M(from.x, from.y),
          L(to.x, to.y)
        )
      } else {
        if (from == to) {
          // full circle, we draw 2 arcs
          val (mx, my) = (2 * center.x - to.x, (2 * center.y - to.y))
          Seq(
            M(from.x, to.y),
            A(
              rx = radius,
              ry = radius,
              rotation = 0,
              largeArcFlag = (largeFlag + 1) % 2,
              sweepFlag = sweepFlag,
              x = mx,
              y = my
            ),
            A(
              rx = radius,
              ry = radius,
              rotation = 0,
              largeArcFlag = (largeFlag + 1) % 2,
              sweepFlag = sweepFlag,
              x = to.x,
              y = to.y
            )
          )
        } else {
          Seq(
            M(from.x, from.y),
            A(
              rx = radius,
              ry = radius,
              rotation = 0,
              largeArcFlag = (largeFlag + 1) % 2,
              sweepFlag = sweepFlag,
              x = to.x,
              y = to.y
            )
          )
        }
      }

    instructions
  }

  private def drawPad(scale: Int, bounds: BoundsBuilder, x: ODBFeatures.PadRecord) = {
    val flashPoint = setPrecision(GPoint(x.x, -x.y) * scale)
    bounds.extend(flashPoint)
    val transform =
      if (x.orientation != Orientation0(false)) {
        val mirror =
          if (x.orientation.mirrored) {
            -x.orientation.deg
          } else {
            x.orientation.deg
          }
        s"transform=\"rotate(${mirror} ${flashPoint.x} ${flashPoint.y}) \" "
      } else {
        s""
      }

    val svgLine =
      s"""<use style="stroke:none" xlink:href="#${layer}def${x.symNum}" x="${flashPoint.x}" y="${flashPoint.y}" $transform />"""
    svgLine
  }

  private def drawLine(scale: Int, bounds: BoundsBuilder, x: ODBFeatures.LineRecord) = {
    val start = setPrecision(GPoint(x.xs, -x.ys) * scale)
    val end   = setPrecision(GPoint(x.xe, -x.ye) * scale)

    val instructions = Seq(
      M(start.x, start.y),
      L(end.x, end.y)
    )

    bounds.extend(start)
    bounds.extend(end)
    instructions
  }
}
