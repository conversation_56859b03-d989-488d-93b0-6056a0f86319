package de.fellows.ems.renderer.impl.render

import akka.Done
import com.pump.geom.AreaX
import de.fellows.ems.gerber.parser.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}
import de.fellows.ems.pcb.model.graphics.lookup.GraphicLookup
import de.fellows.ems.pcb.model.graphics.parts.ComplexRegion
import de.fellows.ems.pcb.model.graphics.spatial.SpatialIndexLookup
import de.fellows.ems.pcb.model.graphics.{<PERSON><PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ChainedCollisionChecker, Clear, Dark, GPoint, Graphic}
import de.fellows.ems.pcb.model.{Dimension, Graphic => ApiGraphic, GraphicDefinition, GraphicElement}
import de.fellows.ems.renderer.impl.gerber._
import de.fellows.ems.renderer.impl.gerber.builders.{BasicFileListener, CopperLayerBuilder, GerberFileListener}
import de.fellows.ems.renderer.impl.progress.RenderEventListener
import de.fellows.utils.DebugUtils.timed
import de.fellows.utils.{FilePath, ThreadUtils}
import org.antlr.v4.runtime.tree.ParseTreeWalker
import org.antlr.v4.runtime.{Char<PERSON><PERSON><PERSON>, CommonTokenStream}
import org.apache.batik.anim.dom.SVGDOMImplementation
import org.apache.batik.constants.XMLConstants
import org.apache.batik.svggen.{SVGGeneratorContext, SVGGraphics2D}
import org.apache.batik.transcoder.image.{ImageTranscoder, JPEGTranscoder, PNGTranscoder}
import org.apache.batik.transcoder.{SVGAbstractTranscoder, TranscoderInput, TranscoderOutput}
import org.slf4j.{Logger, LoggerFactory}
import org.w3c.dom.{Document, Element}
import play.api.Logging
import play.api.libs.json._

import java.awt
import java.awt.Transparency
import java.awt.color.ColorSpace
import java.awt.geom.Area
import java.awt.image.{BufferedImage, ComponentColorModel, DataBuffer}
import java.io._
import java.util.concurrent.{Callable, ExecutorService}
import javax.xml.transform.dom.DOMSource
import javax.xml.transform.stream.StreamResult
import javax.xml.transform.{OutputKeys, TransformerFactory}
import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}

@deprecated
class Renderer(file: java.io.File, ec: ExecutionContext, eventListener: Option[RenderEventListener]) extends Logging {
  def shutdown(): Unit = {
    //    this.ex.shutdownNow()
    //    this.s.shutdown();
  }

  implicit val executionContext: ExecutionContext = ec

  def getMacroDefinitions() = glst.getMacroDefinitions()

  //  private val runnables = new LinkedBlockingQueue[Runnable]()
  //  val ex = new ThreadPoolExecutor(10, 10,
  //    0L, TimeUnit.MILLISECONDS,
  //    runnables, new DefaultThreadFactory(s"${file.getName}")) {
  //  };
  //
  //
  //  val s = Executors.newScheduledThreadPool(1);
  //  s.scheduleAtFixedRate(() => {
  //    logger.warn(s"[Render ${file.getName}] ${ex.getActiveCount} active,  ${runnables.size()} waiting runnables")
  //  }, 0, 5, TimeUnit.SECONDS)

  //  implicit val ctx = ExecutionContext.fromExecutor(ex)

  implicit val log: Logger = LoggerFactory.getLogger(classOf[Renderer])

  var glst: BasicFileListener = null

  import Renderer._
  val collisionChecker = new CachedCollisionChecker[Graphic]()

  def buildClearTree(): Future[Done] =
    Future {
      timed("Clear Tree") {
        val tree = builder.get.tree

        var checked  = 0
        var collided = 0
        val f = tree.processElements { e =>
          if (e.polarity == Dark()) {
            val toClear = tree.collideWithChecker(
              e,
              e.bounds,
              false,
              new ChainedCollisionChecker[Graphic, Graphic](collisionChecker) {
                override def doCollides(check: Graphic, element: Graphic): Boolean =
                  if (element.polarity == Clear() && element.index.x > check.index.x) {
                    checked += 1
                    true
                  } else {
                    false
                  }
              }
            )

            if (toClear.isEmpty) {
              None
            } else {
              collided += toClear.size
              val na = /*Future*/ {

                val newArea = new AreaX(e.shape)
                try {
                  toClear.map(_.shape).foreach(newArea.subtract)
                  newArea
                } catch {
                  case e: OutOfMemoryError =>
                    logger.error("TREE IS OUT OF MEMORY!", e)
                    throw GerberProblem(s"out of memory ${file.getName}", None)
                }
              }
              Some(ComplexRegion(na, Dark(), e.index, e.attributes, Some(toClear), Some(e)))
            }
          } else {
            None
          }
        //      e
        }

        tree.remove(_.polarity == Clear())
        logger.trace(s"Clear check: Checked $checked elements, $collided collided")
        Done
      }
    }.recover {
      case e: Throwable =>
        logger.warn("Clear Tree failed", e)
        throw e
    }

  def prepare(): Future[Renderer] =
    for {
      _ <- {
        println("[Renderer] build meta data")

        buildMetaData()
      }
      _ <- {
        println("[Renderer] build tree")
        buildTree()
      }
      _ <- {
        println("[Renderer] build clear tree")
        timed("Clear Tree") {
          buildClearTree()
        }
      }
      _ <- {
        println("[Renderer] build dimesions")
        Future.successful(buildDimensions())
      }
    } yield {
      this.shutdown();
      println("[Renderer] done")
      this
    }

  val p      = createGerberParser(file, eventListener)
  val walker = new ParseTreeWalker

  var meta: GerberMetadataExtractor       = _
  var builder: Option[CopperLayerBuilder] = None

  var dimensions: Dimension = _

  def buildDimensions(): Dimension = {
    val dims = meta.b.result()
    this.dimensions = Dimension.of(dims)
    dimensions
  }

  def renderPreview(target: FilePath) = {

    //    buildMetaData()
    //    buildTree()
    //    buildDimensions()

    val graphics = Renderer.createGraphics

    prepareGraphics(graphics)

    println(s"draw the tree")

    Renderer.draw(
      builder.get.tree,
      a =>
        graphics.fill(a)
    )

    println(s"done drawing the tree")

    Renderer.writeSVG(graphics, target)

    dimensions
  }

  def prepareGraphics(graphics: SVGGraphics2D) =
    if (dimensions != null) {
      Renderer.prepareGraphics(graphics, dimensions)
    }

  def buildTree(): Future[Option[CopperLayerBuilder]] =
    Future {
      builder = Some(new CopperLayerBuilder(meta.min, meta.max))
      glst = new GerberFileListener(builder.get)
      walker.walk(glst, p.gerber())
      p.reset()
      builder
    }

  def buildMetaData(): Future[Renderer] =
    Future {
      meta = new GerberMetadataExtractor()
      walker.walk(meta, p.gerber())
      p.reset()
      this
    }.recover {
      case e: Throwable =>
        logger.warn(s"Meta Data failed ${file}", e)
        throw e
    }

  def writeJson(dim: Dimension, gf: GerberFormat, paths: Seq[JsValue], m: FilePath): Unit = {
    val o: JsValue = JsObject(Seq(
      "viewbox" -> JsObject(Seq(
        "min" -> JsObject(Seq(
          "x" -> JsNumber(dim.min.x),
          "y" -> JsNumber(dim.min.y)
        )),
        "max" -> JsObject(Seq(
          "x" -> JsNumber(dim.max.x),
          "y" -> JsNumber(dim.max.y)
        ))
      )),
      "format" -> JsObject(Seq(
        "resolution" -> JsNumber(gf.resolution()),
        "unit" -> JsString(gf.getStardizedUnit match {
          case Inch()       => "in"
          case Millimetre() => "mm"
        })
      )),
      "count" -> JsNumber(1),
      "paths" -> JsArray(paths)
    ))

    val jsonfile = new java.io.File(m.toPath)
    jsonfile.getParentFile.mkdirs()

    val w = new PrintWriter(jsonfile)
    w.write(o.toString())
    w.close()
  }

}

object Renderer extends Logging {
  val svgNS = "http://www.w3.org/2000/svg"

  def createGerberParser(f: java.io.File, eventListener: Option[RenderEventListener]): GerberParser = {
    val stream         = CharStreams.fromStream(new FileInputStream(f))
    val l: GerberLexer = new GerberLexer(stream)
    l.removeErrorListeners()
    val ts: CommonTokenStream = new CommonTokenStream(l)
    val parser                = new GerberParser(ts)
    parser.removeErrorListeners()

    eventListener.foreach { lst =>
      parser.addErrorListener(lst)
      parser.addParseListener(lst)
    }

    parser
  }

  def prepareGraphics(graphics: SVGGraphics2D, dim: Dimension, margin: Double = 0) = {
    //    val format = meta.gContext.format.get
    //    val scale = Math.pow(10, Math.max(format.xDec, format.yDec) - 1)
    val scale = 1

    graphics.scale(scale, -scale)
    graphics.translate(-(dim.min.x.toDouble - margin), -(dim.max.y.toDouble + margin))

    val svgDims = new awt.Dimension(
      ((dim.max.x - dim.min.x) * scale + margin * 2).intValue,
      ((dim.max.y - dim.min.y) * scale + margin * 2).intValue
    )
    graphics.setSVGCanvasSize(svgDims)

  }

  def createGraphics: SVGGraphics2D = {
    val domImpl  = SVGDOMImplementation.getDOMImplementation
    val document = domImpl.createDocument(svgNS, "svg", null)
    val ctx      = SVGGeneratorContext.createDefault(document)
    new SVGGraphics2D(ctx, false)
  }

  def createGraphicsWithDoc: (SVGGraphics2D, Document) = {
    val domImpl  = SVGDOMImplementation.getDOMImplementation
    val document = domImpl.createDocument(svgNS, "svg", null)
    val ctx      = SVGGeneratorContext.createDefault(document)
    (new SVGGraphics2D(ctx, false), document)
  }

  def draw(tree: GraphicLookup[Graphic], draw: Area => Unit) =
    tree.walk(t => draw(t.area()))
  //    tree.walk(t => t.elements.map(s => new Area(s.shape)).foreach(draw))

  def drawGraphics(tree: GraphicLookup[Graphic], draw: Graphic => Unit): Unit =
    tree.walk(t => draw(t))

  def drawWithCollisionGroupsIter(
      t: GraphicLookup[Graphic],
      draw: mutable.ArrayBuffer[Graphic] => Unit,
      step: () => Unit = () => {}
  ): Unit = {
    val tree = t.copy()

    def collisionsFor(collisions: mutable.ArrayBuffer[Graphic]): Unit = {
      var idx = 0

      while (idx < collisions.size) {
        step()
        ThreadUtils.interruptCheck()

        val checker = collisions(idx)
        val cols    = tree.popCollide(checker)

        collisions.addAll(cols)

        idx += 1
      }
    }

    var head   = tree.head()
    val result = mutable.ArrayBuffer[Graphic]()

    while (head.isDefined) {
      step()
      ThreadUtils.interruptCheck()

      result.addOne(head.get)

      collisionsFor(result)

      draw(result)

      head = tree.head()
      result.clear()
    }
  }

  @deprecated
  def drawWithCollisionGroupsRec(t: GraphicLookup[Graphic], draw: Vector[Graphic] => Unit)(implicit
      c: ExecutionContext
  ) = {
    val tree = t.copy()

    t match {
      case x: SpatialIndexLookup[Graphic] =>
      //        x.actualCollisions = 0
      //        x.possibleCount = 0
      case _ =>
    }

    def collisionsFor(a: Graphic): Vector[Graphic] = {
      val start = System.currentTimeMillis()
      val s     = Vector.newBuilder[Graphic]
      val cols =
        tree.popCollide(a)

      s ++= cols

      s ++= cols.flatMap(collisionsFor)

      val end = System.currentTimeMillis() - start

      if (end > 20) {
        //        println(s"coll:\t${end}ms")
      }

      s.result()
      //      Seq()
    }

    var head = tree.head()
    while (head.isDefined) {

      val collisions = head.get +: collisionsFor(head.get)

      //      f += Future {
      draw(collisions)
      //        Done
      //      }

      head = tree.head()
    }

    t match {
      case x: SpatialIndexLookup[Graphic] =>
        println(s"tested ${x.possibleCount}, found ${x.actualCollisions}")
      case _ =>
    }
  }

  def drawWithCollisions(tree: GraphicLookup[Graphic], draw: AreaX => Unit)(implicit c: ExecutionContext) =
    drawWithCollisionGroupsIter(
      tree,
      v => {
        val a = new AreaX()
        //      v map { c => Future {  } } foreach { f => Done }
        val start = System.currentTimeMillis()
        v.foreach(c => a.add(c.area())) // TODO: This is slow AF
        val dur = System.currentTimeMillis() - start

        if (dur > 100) {}

        draw(a)
      }
    )

  private def addDefinition(
      allDefs: mutable.Map[String, Int],
      pref: String,
      doc: Document,
      defs: Element
  )(df: GraphicDefinition) = {
    val defname = s"$pref-${df.id}"
    allDefs.addOne(defname, 0)

    val p = doc.createElementNS(svgNS, "path")
    p.setAttribute("id", defname)
    p.setAttribute("d", df.path)
    defs.appendChild(p)
  }

  def addPath(
      allDefs: mutable.Map[String, Int],
      parent: Element,
      pref: String,
      doc: Document,
      transformer: Element => Element
  )(ps: Seq[GraphicElement]): Element = {
    val pathgroup = doc.createElementNS(svgNS, "g")

    ps.foreach {
      case x if x.path isDefined =>
        val pathnode = doc.createElementNS(svgNS, "path")
        pathnode.setAttribute("d", x.path.get)
        if (x.orientation.exists(_.degrees > 0)) {
          pathnode.setAttributeNS(null, "transform", s"rotate(${x.orientation.get.degrees} 0 0)")
        }
        pathgroup.appendChild(pathnode)

      case x if x.use isDefined =>
        val gu      = x.use.get
        val defname = s"$pref-${gu.reference}"

        allDefs.get(defname) match {
          case Some(count) =>
            allDefs.update(defname, count + 1)

            val e = doc.createElementNS(svgNS, "use")
            e.setAttributeNS(XMLConstants.XLINK_NAMESPACE_URI, XMLConstants.XLINK_HREF_QNAME, s"#$defname")
            e.setAttributeNS(null, "x", gu.location.x.floatValue.toString)
            e.setAttributeNS(null, "y", gu.location.y.floatValue.toString)
            if (x.orientation.exists(_.degrees > 0)) {
              e.setAttributeNS(
                null,
                "transform",
                s"rotate(${x.orientation.get.degrees} ${gu.location.x} ${gu.location.y})"
              )
            }
            pathgroup.appendChild(e)

          case None =>
            logger.error(s"missing def! '$defname'")
        }

      case x if x.tag.isDefined =>
        val e = doc.createElementNS(svgNS, x.tag.get)
        x.attributes.getOrElse(Map()).foreach { att =>
          e.setAttributeNS(null, att._1, att._2.head)
        }
        pathgroup.appendChild(e)

      case _ =>
    }

    parent.appendChild(pathgroup)
    transformer(pathgroup)
    pathgroup
  }

  def createSvg(
      graphic: Seq[ApiGraphic],
      graphictransformer: (ApiGraphic, Element) => Element = (a, b) => b,
      transformer: Element => Element = identity
  ): Document = {
    val svgNS = SVGDOMImplementation.SVG_NAMESPACE_URI
    val impl  = SVGDOMImplementation.getDOMImplementation

    val doc  = impl.createDocument(svgNS, "svg", null)
    val defs = doc.createElementNS(svgNS, "defs")

    val rootGroup = doc.createElementNS(svgNS, "g")
    val svgRoot   = doc.getDocumentElement
    svgRoot.setAttribute("xmlns:xlink", XMLConstants.XLINK_NAMESPACE_URI)
    svgRoot.appendChild(rootGroup)
    //    doc.appendChild(defs)

    val allDefs = mutable.Map[String, Int]()

    var i = 0;
    graphic.foreach { g =>
      val graphicsGroup = doc.createElementNS(svgNS, "g")

      graphictransformer(g, graphicsGroup)

      g.defs.foreach(addDefinition(allDefs, s"xx$i", doc, defs))
      g.paths.foreach(addPath(allDefs, graphicsGroup, s"xx$i", doc, transformer))

      rootGroup.appendChild(graphicsGroup)
      i += 1
    }

    svgRoot.appendChild(defs)

    doc
  }

  def createSvg(graphic: ApiGraphic): Document =
    createSvg(Seq(graphic))

  def printSVG(doc: Document, stream: OutputStream) = {
    val tf          = TransformerFactory.newInstance
    val transformer = tf.newTransformer
    transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "no")
    transformer.setOutputProperty(OutputKeys.METHOD, "xml")
    transformer.setOutputProperty(OutputKeys.INDENT, "yes")
    transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8")
    transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4")

    transformer.transform(new DOMSource(doc), new StreamResult(new OutputStreamWriter(stream, "UTF-8")))
  }

  def writePng(doc: Document, path: String, size: Float = 600f, gray: Boolean = false): Unit = {
    // Create a JPEGTranscoder and set its quality hint.
    val t: PNGTranscoder = new PNGTranscoder {
      override def createImage(width: Int, height: Int): BufferedImage =
        if (gray) {
          val cs         = ColorSpace.getInstance(ColorSpace.CS_GRAY) // ColorSpace.getInstance(ColorSpace.CS_GRAY)
          val nBits      = Array(1, 8)
          val colorModel = new ComponentColorModel(cs, nBits, true, false, Transparency.BITMASK, DataBuffer.TYPE_BYTE)
          val raster     = colorModel.createCompatibleWritableRaster(width, height)

          val b = new BufferedImage(colorModel, raster, true, null)
          b
        } else {
          super.createImage(width, height)
        }
    }
    //      t.addTranscodingHint(PNGTranscoder, new Float(.8))

    t.addTranscodingHint(SVGAbstractTranscoder.KEY_WIDTH, size)
    t.addTranscodingHint(SVGAbstractTranscoder.KEY_PIXEL_UNIT_TO_MILLIMETER, 100f)

    transcode(doc, path, t)

  }

  def writeJpg(doc: Document, path: String, size: Float = 600f) = {
    // Create a JPEGTranscoder and set its quality hint.
    val t: JPEGTranscoder = new JPEGTranscoder
    //      t.addTranscodingHint(PNGTranscoder, new Float(.8))
    //    t.addTranscodingHint(ImageTranscoder.KEY_BACKGROUND_COLOR, Color.YELLOW)
    t.addTranscodingHint(SVGAbstractTranscoder.KEY_WIDTH, size)
    t.addTranscodingHint(JPEGTranscoder.KEY_QUALITY, 0.8f);

    transcode(doc, path, t)

  }

  def transcode(doc: Document, ostream: OutputStream, t: ImageTranscoder): Unit = {
    val output: TranscoderOutput = new TranscoderOutput(ostream)
    val input: TranscoderInput   = new TranscoderInput(doc)
    // Perform the transcoding.
    t.transcode(input, output)
    ostream.flush()
  }

  def transcode(doc: Document, path: String, t: ImageTranscoder): Unit = {
    //  t.addTranscodingHint(SVGAbstractTranscoder.KEY_HEIGHT, 300f)
    // Set the transcoder input and output.

    new java.io.File(path).getParentFile.mkdirs()
    val ostream: OutputStream = new FileOutputStream(path)
    transcode(doc, ostream, t)
  }

  def writeSVG(graphics: SVGGraphics2D, m: FilePath): Unit =
    writeSVG(graphics, m.toPath)

  def writeSVG(graphics: SVGGraphics2D, path: String): Unit = {
    val file = new java.io.File(path)
    file.getParentFile.mkdirs()
    graphics.stream(path, false)
  }

}
