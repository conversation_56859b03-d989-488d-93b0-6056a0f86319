package de.fellows.ems.renderer.impl.worker

import de.fellows.utils.logging.StackrateLogging

import java.util.concurrent.{Semaphore, TimeUnit}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Try}

/** An alternative to [[scala.concurrent.Await]] that properly handles exceptions
  */
object SafeAwait extends StackrateLogging {

  def result[T](f: Future[T])(implicit ctx: ExecutionContext): Try[T] = {
    val sem            = new Semaphore(0)
    var result: Try[T] = null
    f.onComplete { x =>
      result = x
      sem.release()
    }

    try {
      while (!sem.tryAcquire(1, TimeUnit.MINUTES))
        logger.debug("waiting for future...")

      result
    } catch {
      case e: InterruptedException => Failure(e)
    }
  }
}
