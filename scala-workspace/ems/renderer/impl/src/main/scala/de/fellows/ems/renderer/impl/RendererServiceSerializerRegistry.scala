package de.fellows.ems.renderer.impl

import com.lightbend.lagom.scaladsl.playjson.{ JsonSerializer, JsonSerializerRegistry }
import de.fellows.ems.renderer.api.Render
import de.fellows.ems.renderer.impl.entity.annularring._
import de.fellows.ems.renderer.impl.entity.pcbreference.{ GetPCBReference, PCBReference, ReferenceSet, SetPCBReference }
import de.fellows.ems.renderer.impl.entity.render._
import de.fellows.ems.renderer.impl.entity.tracewidth._
import de.fellows.ems.renderer.impl.entity.{ distances, render }
import de.fellows.utils.FilePath
import de.fellows.utils.meta.MetaInfo

object RendererServiceSerializerRegistry extends JsonSerializerRegistry {
  override def serializers = List(
    JsonSerializer[Render],
    JsonSerializer[FilePath],
    JsonSerializer[GetRender.type],
    <PERSON>sonSerializer[SetPreview],
    <PERSON>sonSerializer[PreviewSet],
    <PERSON>sonSerializer[StartRender],
    JsonSerializer[StatusChanged],
    JsonSerializer[AddRender],
    JsonSerializer[RenderAdded],
    JsonSerializer[AddDrills],
    JsonSerializer[DrillsAdded],
    JsonSerializer[AddMeta],
    JsonSerializer[MetaInfo],
    JsonSerializer[MetaChanged],
    JsonSerializer[StartRender],
    JsonSerializer[StopRender],
    JsonSerializer[StopAnalysis],
    JsonSerializer[StartAnalysis],
    JsonSerializer[RenderError],
    JsonSerializer[StatusChanged],
    JsonSerializer[MessagesChanged],
    JsonSerializer[SetData],
    JsonSerializer[DataSet],
    JsonSerializer[distances.SetDistances],
    JsonSerializer[distances.Distances],
    JsonSerializer[distances.GetDistances],
    JsonSerializer[distances.DistancesSet],
    JsonSerializer[distances.AddDistances],
    JsonSerializer[distances.DistanceAdded],
    JsonSerializer[TraceWidths],
    JsonSerializer[SetTraceWidths],
    JsonSerializer[TraceWidthsSet],
    JsonSerializer[AddTraceWidths],
    JsonSerializer[TraceWidthsAdded],
    JsonSerializer[GetTraceWidths],
    JsonSerializer[AnnularRings],
    JsonSerializer[AnnularRing],
    JsonSerializer[SetAnnularRings],
    JsonSerializer[GetAnnularRings],
    JsonSerializer[AnnularRingsSet],
    JsonSerializer[AddAnnularRings],
    JsonSerializer[AnnularRingsAdded],
    JsonSerializer[render.SetDistances],
    JsonSerializer[render.DistancesSet],
    JsonSerializer[render.AddDistances],
    JsonSerializer[render.DistanceAdded],
    JsonSerializer[TreeSet],
    JsonSerializer[PCBReference],
    JsonSerializer[SetPCBReference],
    JsonSerializer[GetPCBReference.type],
    JsonSerializer[ReferenceSet],
    JsonSerializer[CloneRender],
    JsonSerializer[RenderSet],

  )

}
