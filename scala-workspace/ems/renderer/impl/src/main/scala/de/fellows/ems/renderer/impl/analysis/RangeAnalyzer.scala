package de.fellows.ems.renderer.impl.analysis

import de.fellows.ems.layerstack.api.LayerStack
import de.fellows.ems.pcb.model.{DrillSet, GerberFile}
import de.fellows.utils.logging.StackrateLogging

object RangeAnalyzer extends StackrateLogging {

  def getRangeFromDrillReport(
      holeList: HoleListWithFile,
      drillsets: Seq[DrillSet],
      copper: Seq[GerberFile]
  ): Option[ToolRange] =
    holeList.file.flatMap { gf =>
      drillsets.find { ds =>
        ds.filename.exists(s => gf.name.toLowerCase.endsWith(s.toLowerCase))
      }
    }.flatMap { ds =>
      val applicableLayers = copper.zipWithIndex.filter { layer =>
        ds.layers.exists(suffix => layer._1.name.toLowerCase.endsWith(suffix.toLowerCase))
      }.map(_._2)
      (applicableLayers.minOption, applicableLayers.maxOption) match {
        case (Some(f), Some(t)) if f < t => Some(ToolRange(f, t))
        case _                           => None
      }
    }

  def getRangeFromFile(
      layerstacks: LayerStack,
      holeList: HoleListWithFile,
      allFiles: Seq[DrillSet]
  ): Option[ToolRange] = {
    def cleanFileNameForMatching(n: String) = {
      val i = n.lastIndexOf('.')
      if (i > 0) {
        n.splitAt(i)._1
      } else {
        n
      }
    }

    val copper            = Reconciliation.getCopperLayers(layerstacks)
    val rangeFromFileType = getRangeFromFileType(holeList, copper.length)
    val rangeFromReport   = getRangeFromDrillReport(holeList, allFiles, copper)

    rangeFromReport orElse
      rangeFromFileType orElse
      holeList.file.flatMap { gf =>
        val name = gf.name
        getDrillRangeByLayerNames(name, copper.map(f => cleanFileNameForMatching(f.name)))
      }
  }

  private def getRangeFromFileType(holeList: HoleListWithFile, copperFiles: Int) =
    holeList.file.flatMap { f =>
      (f.fType.from, f.fType.to) match {
        case (Some(_f), Some(_t)) =>
          val f = _f - 1
          val t = _t - 1
          if (f >= 0 && t >= 0 && f < copperFiles && t < copperFiles) {
            Some(ToolRange(f, t))
          } else {
            logger
              .warn(s"[RECONCILIATION] FileType Range is too large: ${f} -> ${t}, with ${copperFiles} layers")
            None
          }
        case _ => None
      }
    }

  def getDrillRangeByLayerNames(name: String, copper: Seq[String]): Option[ToolRange] = {
    val indexToLayer = copper.zipWithIndex.map { s =>
      val (nameToCheck, layerIndex) = s
      (name.indexOf(nameToCheck), nameToCheck.length, layerIndex)
    }.filter(_._1 >= 0)

    val sortedIndex = indexToLayer.sortBy(_._1)

    val nestedMaps = sortedIndex.groupBy(_._1).map(x => x._1 -> x._2.groupBy(_._2))

    val fromTuple = nestedMaps.minByOption(_._1)
    val toTuple   = nestedMaps.maxByOption(_._1)

    if (fromTuple.map(_._1) != toTuple.map(_._1)) {
      // only check if the index is not the same (ie are at different positions in the name)
      val fromOption =
        fromTuple.flatMap(_._2.maxByOption(_._1).flatMap(x => x._2.minByOption(_._3).map(_._3)))

      val toOption =
        toTuple.flatMap(_._2.maxByOption(_._1).flatMap(x => x._2.maxByOption(_._3).map(_._3)))

      (fromOption, toOption) match {
        case (Some(from), Some(to)) => Some(ToolRange(from, to))
        case _                      => None
      }
    } else {
      None
    }
  }
}
