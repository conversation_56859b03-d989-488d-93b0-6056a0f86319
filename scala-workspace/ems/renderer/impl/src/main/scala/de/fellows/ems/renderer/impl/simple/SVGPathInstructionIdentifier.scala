package de.fellows.ems.renderer.impl.simple

sealed trait SVGPathInstructionIdentifier

object SVGPathInstructionIdentifier {
  def apply(id: String): SVGPathInstructionIdentifier =
    id match {
      case "M" => M
      case "L" => L
      case "H" => H
      case "V" => V
      case "Z" => Z
      case "S" => S
      case "C" => C
      case "Q" => Q
      case "A" => A
      case "T" => T

      case "m" => RelM
      case "l" => RelL
      case "h" => RelH
      case "v" => RelV
      case "z" => RelZ
      case "s" => RelS
      case "c" => RelC
      case "q" => RelQ
      case "a" => RelA
      case "t" => RelT
    }
}

case object M extends SVGPathInstructionIdentifier {
  def apply(x: Number, y: Number): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x, y))
}
case object L extends SVGPathInstructionIdentifier {
  def apply(x: Number, y: Number): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x, y))
}
case object H extends SVGPathInstructionIdentifier {
  def apply(x: Number): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x))
}
case object V extends SVGPathInstructionIdentifier {
  def apply(y: Number): SVGPathInstruction =
    SVGPathInstruction(this, Seq(y))
}
case object Z extends SVGPathInstructionIdentifier {
  def apply(): SVGPathInstruction =
    SVGPathInstruction(this, Seq())
}

case object S extends SVGPathInstructionIdentifier {
  def apply(
      x1: Number,
      y1: Number,
      x2: Number,
      y2: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x1, y1, x2, y2))
}
case object C extends SVGPathInstructionIdentifier {
  def apply(
      x1: Number,
      y1: Number,
      x2: Number,
      y2: Number,
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x1, y1, x2, y2, x, y))
}
case object Q extends SVGPathInstructionIdentifier {
  def apply(
      x1: Number,
      y1: Number,
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x1, y1, x, y))
}
case object A extends SVGPathInstructionIdentifier {
  def apply(
      rx: Number,
      ry: Number,
      rotation: Number,
      largeArcFlag: Number,
      sweepFlag: Number,
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(rx, ry, rotation, largeArcFlag, sweepFlag, x, y))
}
case object T extends SVGPathInstructionIdentifier {
  def apply(
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x, y))
}

// relative commands
case object RelM extends SVGPathInstructionIdentifier {
  def apply(x: Number, y: Number, target: (Number, Number)): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x, y), target)

  override def toString: String = "m"
}
case object RelL extends SVGPathInstructionIdentifier {
  def apply(x: Number, y: Number, target: (Number, Number)): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x, y), target)

  override def toString: String = "l"
}
case object RelH extends SVGPathInstructionIdentifier {
  def apply(x: Number, target: (Number, Number)): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x), target)

  override def toString: String = "h"
}
case object RelV extends SVGPathInstructionIdentifier {
  def apply(y: Number, target: (Number, Number)): SVGPathInstruction =
    SVGPathInstruction(this, Seq(y), target)

  override def toString: String = "v"
}
case object RelZ extends SVGPathInstructionIdentifier {
  def apply(): SVGPathInstruction =
    SVGPathInstruction(this, Seq())

  override def toString: String = "z"
}
case object RelS extends SVGPathInstructionIdentifier {
  def apply(
      x1: Number,
      y1: Number,
      x2: Number,
      y2: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x1, y1, x2, y2))

  override def toString: String = "s"
}
case object RelC extends SVGPathInstructionIdentifier {
  def apply(
      x1: Number,
      y1: Number,
      x2: Number,
      y2: Number,
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x1, y1, x2, y2, x, y))

  override def toString: String = "c"
}
case object RelQ extends SVGPathInstructionIdentifier {
  def apply(
      x1: Number,
      y1: Number,
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x1, y1, x, y))

  override def toString: String = "q"
}
case object RelA extends SVGPathInstructionIdentifier {
  def apply(
      rx: Number,
      ry: Number,
      rotation: Number,
      largeArcFlag: Number,
      sweepFlag: Number,
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(rx, ry, rotation, largeArcFlag, sweepFlag, x, y))

  override def toString: String = "a"
}
case object RelT extends SVGPathInstructionIdentifier {
  def apply(
      x: Number,
      y: Number
  ): SVGPathInstruction =
    SVGPathInstruction(this, Seq(x, y))

  override def toString: String = "t"
}
