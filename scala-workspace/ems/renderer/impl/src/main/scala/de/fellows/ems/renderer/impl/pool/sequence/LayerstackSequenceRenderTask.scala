package de.fellows.ems.renderer.impl.pool.sequence

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.layerstack.api.Material
import de.fellows.ems.layerstack.api.sequence.{LayerStackLeaf, LayerStackNode, LayerstackSequence, MaterialSequence, SequenceRoot}
import de.fellows.ems.renderer.impl.pool.layerstack.LAYER_COLORS
import de.fellows.ems.renderer.impl.pool.{AbstractTask, LowTaskPriority, TaskContext}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.logging.StackrateLogger
import org.apache.batik.svggen.SVGGraphics2D

import java.awt.geom.{AffineTransform, Line2D, Rectangle2D}
import java.awt.{BasicStroke, Color, Stroke}
import java.util.UUID
import scala.collection.mutable

case class Offsets(
    stackBaseOffset: Double,
    row: Int,
    column: Int,
    rowOffset: Double,
    columnOffset: Double,
    var maxCols: Int = 0
) {
  def +(o: Offsets) =
    Offsets(
      stackBaseOffset + o.stackBaseOffset,
      row + o.row,
      column + o.column,
      rowOffset + o.rowOffset,
      columnOffset + o.columnOffset,
      maxCols = maxCols
    )
}

object Offsets {
  val EMPTY = Offsets(0, 0, 0, 0, 0)
}

class SequenceMetaTracker {
  var maxLabeledColumn                                                     = 0
  val coordinates: mutable.HashMap[UUID, (Double, Double, Double, Double)] = mutable.HashMap()

  def addCoordinates(internalId: UUID, x: Double, y: Double, width: Double, height: Double): Unit =
    coordinates.put(internalId, (x, y, width, height))

  def trackMaxLabelColumn(i: Int) =
    maxLabeledColumn = Math.max(maxLabeledColumn, i)
}

class LayerstackSequenceRenderTask[C <: TaskContext[SVGGraphics2D]](
    val layerStack: SequenceRoot,
    val materials: Seq[Material],
    override val ctx: C
)(implicit logger: StackrateLogger)
    extends AbstractTask[SVGGraphics2D, C](ctx, System.currentTimeMillis(), LowTaskPriority) {

  override def getTeam(): Option[String] = Some(layerStack.team)

  override def getAssembly(): Option[AssemblyReference] = None

  override def getExtraInfo(): Seq[(String, Any)] = Seq()

  override def description: String = s"SequencedLayerStackDefinition ${layerStack.team}/${layerStack.name}"

  var metaTracker: Option[SequenceMetaTracker] = None

  override def doRun(): Unit = {
    import LayerstackSequenceRenderTask._
    val gr = Renderer.createGraphics

    val labeledColumns = layerStack.topSequence.depth()

    val BRACKETS = false

    this.metaTracker = Some(new SequenceMetaTracker())

    val dh = new DrawHelper(metaTracker, labeledColumns)
    dh.draw(layerStack.topSequence, new Offsets(0, 0, 0, 0, 0), BRACKETS)

    dh.getResult.foreach { op =>
      op(gr)
    }

    layerStack.children.zipWithIndex.foreach { d =>
      val link = d._1.sequenceLinks.find(x => x.mainStack == layerStack.id && x.subStack == d._1.id)

      link match {
        case Some(value) =>
        case None        =>
      }

      val dh = new DrawHelper(metaTracker, labeledColumns)
      dh.draw(d._1.topSequence, new Offsets(0, 0, 0, 0, 0), BRACKETS)

      val off =
        STACK_WIDTH + (metaTracker.get.maxLabeledColumn * COLUMN_NAME_GAP) + (metaTracker.get.maxLabeledColumn * COLUMN_NAME_GAP)

      val linkOffset = layerStack.sequenceLinks.find(x => x.subStack == d._1.id && x.mainStack == layerStack.id)
        .map { link =>
          val mainOff = metaTracker.get.coordinates(link.mainSequence)
          val subOff  = metaTracker.get.coordinates(link.subSequence)

          val mainY = mainOff._2
          val subY  = subOff._2

          mainY - subY
        }.getOrElse(0.0)

      gr.translate(off, linkOffset)

      dh.getResult.foreach { op =>
        op(gr)
      }

      gr.translate(-off, -linkOffset)

    }

    ctx.setResult(gr)
  }
}

object LayerstackSequenceRenderTask {
  val DEFAULT_COLOR   = Color.BLACK
  val DEFAULT_HEIGHT  = 100
  val DEFAULT_PAD     = 10
  val DEFAULT_MARGIN  = 5
  val STACK_WIDTH     = 700
  val COLUMN_NAME_GAP = 40
}

case class OperationDescriptor(color: Option[Color], stroke: Option[Stroke])

class DrawHelper(metaTracker: Option[SequenceMetaTracker], labeledColumns: Int) {

  import LayerstackSequenceRenderTask._

  val operations = Seq.newBuilder[SVGGraphics2D => Any]

  def getResult = operations.result()

  def startDraw(sequence: LayerstackSequence, offsets: Offsets): Offsets =
    Offsets.EMPTY

  def lighter(c: Color) = {
    val hsb: Array[Float] = Color.RGBtoHSB(c.getRed, c.getGreen, c.getBlue, null)
    hsb(2) = hsb(2) + 0.15f
    val r = new Color(Color.HSBtoRGB(hsb(0), hsb(1), hsb(2)))
    r
  }

  def endDraw(sequence: LayerstackSequence, offsets: Offsets, oldoff: Offsets, brackets: Boolean): Offsets = {

    if (brackets) {
      val column = labeledColumns - oldoff.column
      this.metaTracker.foreach(_.trackMaxLabelColumn(column))
      var color = new Color(10, 10, 10)

      Range(0, oldoff.column).foreach(_ => color = lighter(color))

      println(color)

      colorAndBorder(color, 3) { gr =>
        val xStart = offsets.stackBaseOffset + STACK_WIDTH + COLUMN_NAME_GAP + (column * COLUMN_NAME_GAP)
        val tx     = AffineTransform.getRotateInstance(0.5 * Math.PI, xStart, oldoff.rowOffset + 10)
        gr.setTransform(tx)
        val of = gr.getFont
        gr.setFont(of.deriveFont(5f))
        val name = sequence match {
          case x: LayerStackNode => x.joining.typeName
          case x                 => x.name
        }
        gr.drawString(name, xStart.floatValue(), oldoff.rowOffset.floatValue())
        gr.setTransform(AffineTransform.getRotateInstance(0))
        gr.setFont(of)

        val yStart = oldoff.rowOffset + 5
        val yStop  = offsets.rowOffset - 15
        val l      = new Line2D.Double(xStart, yStart, xStart, yStop)
        val tl     = new Line2D.Double(offsets.stackBaseOffset + STACK_WIDTH, yStart, xStart, yStart)
        val bl     = new Line2D.Double(offsets.stackBaseOffset + STACK_WIDTH, yStop, xStart, yStop)
        gr.draw(l)
        gr.draw(tl)
        gr.draw(bl)
      }
    }

    Offsets.EMPTY
  }

  def draw(s: LayerstackSequence, offsets: Offsets, brackets: Boolean): Offsets = {

    var delta = Offsets.EMPTY
    delta = delta + startDraw(s, offsets)

    s match {
      case x: LayerStackNode =>
        delta = delta + Offsets(0, 0, 1, 0, 20)
        x.children.foreach { s =>
          delta = delta + draw(s, offsets + delta, brackets)
        }
        delta = delta + Offsets(0, 0, -1, 0, 20)
      case leaf: LayerStackLeaf => delta = delta + drawLeaf(leaf, offsets + delta)
    }

    delta = delta + endDraw(s, offsets + delta, offsets, brackets)

    delta
  }

  def drawLeaf(leaf: LayerStackLeaf, offsets: Offsets): Offsets =
    leaf match {
      case mat: MaterialSequence =>
        val height = DEFAULT_HEIGHT
        metaTracker.foreach(_.addCoordinates(
          leaf.internalId.get,
          offsets.stackBaseOffset,
          offsets.rowOffset,
          STACK_WIDTH,
          height
        ))

        color(LAYER_COLORS.getOrElse(mat.layerType, DEFAULT_COLOR)) { gr =>
          val s = new Rectangle2D.Double(offsets.stackBaseOffset, offsets.rowOffset, STACK_WIDTH, height)
          gr.fill(s)
        }

        Offsets(0, 1, 0, height + 10, 0)
      case _ => Offsets.EMPTY
    }

  def colorAndBorder[T](c: Color, border: Float)(u: SVGGraphics2D => T): Unit =
    operations += (gr => {
      val os = gr.getStroke()
      gr.setStroke(new BasicStroke(border))
      val oc = gr.getColor
      gr.setColor(c)

      val r = u(gr)

      gr.setStroke(os)
      gr.setColor(oc)
      r
    })

  def color[T](c: Color)(u: SVGGraphics2D => T): Unit =
    operations += (gr => {
      val oc = gr.getColor
      gr.setColor(c)
      val r = u(gr)
      gr.setColor(oc)

      r
    })

}
