package de.fellows.ems.renderer.impl.pool

import de.fellows.ems.pcb.model.GerberFile
import de.fellows.utils.FutureUtils
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.telemetry.PropagatingExecutorService
import kamon.Kamon
import play.api.Logging

import java.util.concurrent.locks.ReentrantLock
import java.util.concurrent.{ExecutorService, Executors, TimeUnit}
import java.util.{Timer, TimerTask}
import scala.collection.mutable
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.util.Try

object RendererCoordinator extends Logging with Coordinator {
  def isShutdown(): Boolean =
    this.lock.synchronized {
      this._isShutdown
    }

  var _isShutdown = false

  val lock = new ReentrantLock()

  def time(ms: Long): String =
    s""

  val exs = new RendererExecutor(Runtime.getRuntime.availableProcessors)

  val previews: ExecutorService =
    new PropagatingExecutorService(Executors.newWorkStealingPool(5))

  val worker: ExecutorService =
    new PropagatingExecutorService(Executors.newWorkStealingPool(Runtime.getRuntime.availableProcessors))

  val analysisQueue: PropagatingExecutorService =
    new PropagatingExecutorService(Executors.newFixedThreadPool(Runtime.getRuntime.availableProcessors))

  val timer = new Timer()

  val running: mutable.HashSet[RenderTaskWrapper[_, _]] = mutable.HashSet[RenderTaskWrapper[_, _]]()
  val waiting: mutable.HashSet[RenderTaskWrapper[_, _]] = mutable.HashSet[RenderTaskWrapper[_, _]]()

  def waitForZero() =
    this.lock.synchronized {
      while (running.size + waiting.size != 0)
        this.lock.wait(1000)
    }

  def terminateAndWait() =
    this.lock.synchronized {
      if (!this._isShutdown) {
        this._isShutdown = true
      }
      while (running.size + waiting.size != 0)
        this.lock.wait(1000)
    }

  def shutdown(dur: FiniteDuration)(implicit c: ExecutionContext): Try[Boolean] =
    FutureUtils.timeout(dur) {
      this.terminateAndWait()
      if (!exs.isShutdown) {
        exs.shutdown()
      }
      if (!worker.isShutdown) {
        worker.shutdown()
      }
      if (!previews.isShutdown) {
        previews.shutdown()
      }
      if (!analysisQueue.isShutdown) {
        analysisQueue.shutdown()
      }

      exs.awaitTermination(dur.toMillis, TimeUnit.MILLISECONDS);
      worker.awaitTermination(dur.toMillis, TimeUnit.MILLISECONDS)
      previews.awaitTermination(dur.toMillis, TimeUnit.MILLISECONDS)
      analysisQueue.awaitTermination(dur.toMillis, TimeUnit.MILLISECONDS)
      logger.info("queues are shut down!")
      true
    }

  def shutdown()(implicit c: ExecutionContext): Any =
    shutdown(10 minutes)

  def submitTask[X, Y <: TaskContext[X]](task: AbstractTask[X, Y]): Unit = {
    val wrap: RenderTaskWrapper[X, Y] = new RenderTaskWrapper[X, Y](
      task.withWorker(this.worker),
      (w, start) =>
        this.lock.synchronized {
          waiting -= w
          running += w
          sendMetrics
        },
      (w, end) =>
        this.lock.synchronized {
          running -= w
          sendMetrics
          this.lock.synchronized {
            this.lock.notifyAll()
          }
        }
    )

    this.lock.synchronized {
      wrap.notifyScheduled()
      waiting += wrap

      sendMetrics
      exs.execute(wrap)
    }
  }

  timer.schedule(
    new TimerTask {
      override def run(): Unit =
        sendMetrics
    },
    0,
    10 * 1000
  )

  def sendMetrics =
    try
      sendMetricsUnsafe
    catch {
      case e: Throwable => logger.error(e.getMessage)
    }

  def sendMetricsUnsafe = {
    val msgBuilder = new StringBuilder()
    val now        = System.currentTimeMillis()
    //    if (running.nonEmpty || waiting.nonEmpty) {

    val (runningRender, runningAnalysis) =
      if (running.isEmpty) {
        (0, 0)
      } else {
        val (ren, an) = running.partition(x => x.rt.isInstanceOf[RenderTask[_]])
        (ren.size, an.size)
      }

    val (waitingRender, waitingAnalysis) =
      if (waiting.isEmpty) {
        (0, 0)
      } else {
        val (ren, an) = waiting.partition(x => x.rt.isInstanceOf[RenderTask[_]])
        (ren.size, an.size)
      }

    val groupedRunning = running.groupBy(_.rt.getClass.getSimpleName.toLowerCase).map { x =>
      s"${x._1}" -> x._2.size
    }
    val groupedWaiting = waiting.groupBy(_.rt.getClass.getSimpleName.toLowerCase).map { x =>
      s"${x._1}" -> x._2.size
    }

    val gr = groupedRunning.map(x => s"${x._1}: ${x._2}").mkString(",")
    val gw = groupedWaiting.map(x => s"${x._1}: ${x._2}").mkString(",")

    logger.warn(
      s"RUNNING: ${gr} ---- WAITING: ${gw}"
    )
  }

  def fileMetrics(gf: GerberFile): Seq[(String, Any)] =
    Seq(
      "id"       -> gf.id,
      "name"     -> gf.name,
      "path"     -> gf.path.toPath,
      "fileType" -> gf.fType.fileType
    )

}

class RenderTaskWrapper[X, Y <: TaskContext[X]](
    val rt: AbstractTask[X, Y],
    start: (RenderTaskWrapper[X, Y], Long) => Unit,
    end: (RenderTaskWrapper[X, Y], Long) => Unit
) extends Runnable with Comparable[Runnable] with StackrateLogging {
  private val context = Kamon.currentContext()

  var scheduledAt: Option[Long] = None;
  var startedAt: Option[Long]   = None;
  var stoppedAt: Option[Long]   = None;
  def notifyScheduled() = {
    scheduledAt = Some(System.currentTimeMillis())
    rt.notifyScheduled(scheduledAt.get)

  }

  override def run() =
    try
      Kamon.runWithContext(context) {
        val startTime = System.currentTimeMillis()
        startedAt = Some(startTime)
        start(this, startTime)
        rt.ctx.setTimeout(System.currentTimeMillis() + ((10 minutes).toMillis))
        rt.run()
      }
    finally {
      val endTime = System.currentTimeMillis()
      stoppedAt = Some(endTime)
      end(this, endTime)
    }

  override def compareTo(or: Runnable): Int =
    or match {
      case o: RenderTaskWrapper[X, Y] =>
        val aStart: Long = this.scheduledAt.getOrElse(0L)
        val bStart: Long = o.scheduledAt.getOrElse(0L)

        (this.rt.priority, o.rt.priority) match {
          case (x, y) if x == y =>
            this.rt.order.compareTo(o.rt.order) // inverse order for timestamps, the lower the higher the preview

          case (x, y) => x.compareTo(y)
        }

      case _ => -100
    }

}
