package de.fellows.ems.renderer.impl.outline

import de.fellows.ems.pcb.model.Density

object OutlineCandidateHeuristic {

  /** calculates a normalized score between 0 and 100.
    *
    * the higher the score, the more suitable the given density is for an outline
    */
  def score(d: Density): Double = {
    val weightForCopperOutside = 1.0
    val weightForCopperPortion = 1.0

    if (d.portionOfCandidateIsCopper.isNaN || d.portionOfCopperIsOutsideCandidate.isNaN) {
      Double.MinValue
    } else {
      // the higher the value is, the worse the score should be. inverse the percentage.
      val inverseCopperOutside = 100 - d.portionOfCopperIsOutsideCandidate

      val weightedScore =
        (weightForCopperOutside * inverseCopperOutside) + (weightForCopperPortion * d.portionOfCandidateIsCopper)

      // The normalized score should range from 0 to 100
      val normalizedScore = weightedScore / (weightForCopperOutside + weightForCopperPortion)

      normalizedScore
    }

  }

  implicit val ordering: Ordering[Density] = Ordering.by[Density, Double] { d =>
    score(d)
  }

  implicit val transientDensityOrdering: Ordering[TransientOutlineCandidateWithDensity] =
    Ordering.by[TransientOutlineCandidateWithDensity, Density] { d =>
      d.density
    }
}
