package de.fellows.ems.renderer.impl.outline

import akka.Done
import com.typesafe.config.Config
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, AssemblyService}
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.model.graphics.tree.PCBLayerInternalData
import de.fellows.ems.pcb.model.{GerberFile, LayerConstants, Outline, PCBVersion}
import de.fellows.utils.internal.{FileWriter, LifecycleStageStatus}
import de.fellows.utils.logging.StackrateLogger
import de.fellows.utils.meta.MetaInfo
import de.fellows.utils.{DebugUtils, FilePath}

import java.util.UUID
import java.util.concurrent.Callable
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

class OutlineTask(
    pcbService: PCBService,
    assemblyService: AssemblyService,
    assRef: AssemblyReference,
    renderings: Seq[FilePath],
    pcb: PCBVersion
)(implicit ec: ExecutionContext, logger: StackrateLogger, config: Config) extends Callable[Future[Done]] {

  private def setResults(outlines: Seq[Outline]): Future[Done] =
    if (outlines.nonEmpty) {
      (for {
        _ <- pcbService._setOutlineCandidates(assRef.team, assRef.id, assRef.version).invoke(outlines)
        _ <- pcbService._selectBestOutlineCandidate(assRef.team, assRef.id, assRef.version).invoke()
        _ <- setLifecycle(assRef, LifecycleStageStatus.emptySuccess)
      } yield Done).recoverWith {
        case NonFatal(e) =>
          logger.error(s"Error saving outlines for pcb ${assRef.team}/${assRef.id}/${assRef.gid}: $e")
          setLifecycle(assRef, LifecycleStageStatus.emptyError)
      }
    } else {
      logger.error("No outline candidates found")
      setLifecycle(assRef, LifecycleStageStatus.emptyError)
    }

  private def setLifecycle(assRef: AssemblyReference, status: LifecycleStageStatus): Future[Done] =
    assemblyService._updateVersionLifecycle(
      assRef.team,
      assRef.id,
      Some(assRef.version),
      AssemblyLifecycleStageName.Outline.value,
      Some(System.currentTimeMillis())
    )
      .invoke(status)

  override def call(): Future[Done] = {
    val copperdata = DebugUtils.timed("read copper traces") {
      renderings.view.flatMap(PCBLayerInternalData.load)
    }(logger.logger)

    val mechanicalFiles = OutlineTask.getPossibleOutlineFiles(pcb.files)
    val copperFiles     = pcb.files.filter(f => LayerConstants.COPPER.contains(f.fType.fileType))

    for {
      allCandidates <- DebugUtils.timed("build outlines") {
        new OutlineCandidateBuilder().build(mechanicalFiles, copperFiles, copperdata, assRef)
      }(logger.logger)

      filteredCandidates = {
        import de.fellows.ems.renderer.impl.outline.OutlineCandidateHeuristic._

        allCandidates
          .sorted(implicitly[Ordering[TransientOutlineCandidateWithDensity]].reverse)
          .slice(0, 10)
          .map { c =>
            val id   = UUID.randomUUID()
            val path = Outline.graphicPath(assRef, id)

            FileWriter.writeCompressedObject(path.toJavaPath, c.candidate.graphic)

            Outline(
              id = id,
              file = c.candidate.file,
              path = path,
              userChoice = false,
              metaInfo = MetaInfo(c.properties().groupBy(_.name).map(x => x._1 -> x._2.head)),
              density = c.density,
              score = OutlineCandidateHeuristic.score(c.density)
            )
          }
      }

      _ <- setResults(filteredCandidates)
    } yield Done
  }

}

object OutlineTask {
  def getPossibleOutlineFiles(files: Seq[GerberFile]): Seq[GerberFile] =
    files.filter { f =>
      Seq(LayerConstants.OUTLINE, LayerConstants.MECHANICAL, LayerConstants.KEEP_OUT).contains(f.fType.fileType)
    }
}
