package de.fellows.ems.renderer.impl.gerber.builders

import de.fellows.ems.pcb.model.graphics.ops.{Interpolation, Movement}
import de.fellows.ems.pcb.model.graphics.{GPoint, GerberApertureDefinition, Rectangle}
import de.fellows.ems.renderer.impl.gerber.graphic.ApertureUtils
import de.fellows.ems.renderer.impl.gerber.interpolation.awt.InterpolationBoundsCreator

class GerberInterpolationBoundsCreator extends InterpolationBoundsCreator[GerberApertureDefinition] {

  override def rec(
      from: GPoint,
      to: GPoint,
      radians: (Double, Double),
      ad: GerberApertureDefinition,
      movement: Movement,
      scaling: Int
  ): Rectangle = {

    val dia =
      ApertureUtils.getNonZeroDecimal(
        ad.diameter(scaling).get
      ) // (ApertureUtils.getNonZeroDecimal(ad.args.head).get * scaling).doubleValue

    ad.template match {
      case "C" =>
        doCircle(from, to, radians, dia)
      case "R" =>
        Interpolation.createRectForR(from, to, ad.bounds.get, movement, scaling)

      case x =>
        // TODO: s"Circular Interpolation for aperture $x unsupported"

        doCircle(from, to, radians, dia)
    }
  }

}
