package de.fellows.ems.renderer.impl.entity.annularring

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventShards, AggregateEventTag }
import de.fellows.ems.pcb.model.graphics.tree.Distance
import de.fellows.ems.pcb.model.{ BigPoint, GerberFile, Tool }
import de.fellows.ems.renderer.api.AnnularRingDescription
import de.fellows.ems.renderer.impl.RendererServiceImpl
import play.api.libs.json
import play.api.libs.json.Json

import java.util.UUID

case class AnnularRing(
    t: Tool,
    layer: Int,
    ringType: String = AnnularRingDescription.INSIDE,
    hit: BigPoint,
    size: Double = -1,
    line: Distance,
    file: Option[GerberFile]
)

case class AnnularRings(rings: Seq[AnnularRing])

object AnnularRing {

  implicit val format: json.Format[AnnularRing] = Json.using[Json.WithDefaultValues].format
}

object AnnularRings {
  def id(version: UUID) =
    RendererServiceImpl.id(version, "annularrings")

  implicit val format: json.Format[AnnularRings] = Json.format
}

sealed trait AnnularRingsCommand

case class AddAnnularRings(version: UUID, rings: Seq[AnnularRing]) extends AnnularRingsCommand with ReplyType[Done]

case class SetAnnularRings(version: UUID, rings: Seq[AnnularRing]) extends AnnularRingsCommand with ReplyType[Done]

case class GetAnnularRings(version: UUID) extends AnnularRingsCommand with ReplyType[AnnularRings]

object SetAnnularRings {
  implicit val format: json.Format[SetAnnularRings] = Json.format
}

object GetAnnularRings {
  implicit val format: json.Format[GetAnnularRings] = Json.format
}

object AddAnnularRings {
  implicit val format: json.Format[AddAnnularRings] = Json.format
}

sealed trait AnnularRingsEvent extends AggregateEvent[AnnularRingsEvent] {
  override def aggregateTag: AggregateEventShards[AnnularRingsEvent] = AnnularRingsEvent.Tag
}

object AnnularRingsEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[AnnularRingsEvent](NumShards)
}

case class AnnularRingsAdded(version: UUID, rings: Seq[AnnularRing]) extends AnnularRingsEvent

case class AnnularRingsSet(version: UUID, rings: Seq[AnnularRing]) extends AnnularRingsEvent

object AnnularRingsAdded {
  implicit val format: json.Format[AnnularRingsAdded] = Json.format
}

object AnnularRingsSet {
  implicit val format: json.Format[AnnularRingsSet] = Json.format
}
