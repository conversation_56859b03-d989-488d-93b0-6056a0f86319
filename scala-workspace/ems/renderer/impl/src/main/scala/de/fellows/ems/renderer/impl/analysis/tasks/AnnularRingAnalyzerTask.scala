package de.fellows.ems.renderer.impl.analysis.tasks

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.graphics.Graphic
import de.fellows.ems.pcb.model.graphics.tree.QuadTree
import de.fellows.ems.pcb.model.{BigPoint, GerberFile, HoleList, Tool}
import de.fellows.ems.renderer.impl.analysis.AnnularRingContext
import de.fellows.ems.renderer.impl.entity.annularring.AnnularRing
import de.fellows.ems.renderer.impl.pool.{AbstractTask, MediumTaskPriority, RendererCoordinator}
import de.fellows.utils.logging.StackrateLogger

import java.awt.Shape
import java.awt.geom.{Area, Rectangle2D}
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}

/** @param assRef
  * @param ctx
  * @param copperFile
  * @param layer
  *   The Layer number, 1-based (i.e. the i-th copper layer)
  * @param holes
  * @param qt
  */
class AnnularRingAnalyzerTask(
    assRef: AssemblyReference,
    ctx: AnnularRingContext,
    copperFile: Option[GerberFile],
    layer: Int,
    holes: Seq[HoleList],
    qt: QuadTree[Graphic]
)(implicit logger: StackrateLogger)
    extends AbstractTask[Seq[AnnularRing], AnnularRingContext](ctx, System.currentTimeMillis(), MediumTaskPriority)
    with Analyzer {

  var bounds: Rectangle2D = _
  var area: Area          = _

  override def getTeam(): Option[String] = Some(assRef.team)

  override def getAssembly(): Option[AssemblyReference] = Some(assRef)

  def getExtraInfo(): Seq[(String, Any)] = copperFile.map { gf =>
    RendererCoordinator.fileMetrics(gf)
  }.getOrElse(Seq()) :+ ("layerIndex" -> layer)

  private def collision(element: Shape, check: Graphic): Boolean =
    if (bounds.intersects(check.bounds)) {
      Graphic.intersects(area, check.area())
    } else {
      false
    }

  private def getAnnularRing(t: Tool, hit: BigPoint, qt: QuadTree[Graphic], scaling: Double): Option[AnnularRing] =
    None // TODO

  private def analyze(): Seq[AnnularRing] = {
    val relevantHoles = holes.filter { hl =>
      (hl.from.map(_ <= layer).forall(_ == true) && hl.to.map(_ >= layer).forall(_ == true))
    }
    implicit val exctx = ExecutionContext.fromExecutor(this.worker.get)

    val f = Future.sequence(relevantHoles.map { hl =>
      Future.sequence(hl.tools.filter(_.drillType == Tool.PLATED).flatMap { t =>
        t.drills.map { hit =>
          Future.successful(
            getAnnularRing(t, hit.asPoint, qt, hl.scaling.map(_.doubleValue).getOrElse(100.0))
          )
        }
      })
    }).map(_.flatten.flatten)

    Await.result(f, 10 minutes)
  }

  override def doRun(): Unit =
    try
//      var result = Some(analyze())
      ctx._result = Some(Seq())
    catch {
      case e: Throwable =>
        e.printStackTrace()
        ctx.error(assRef, None, e)
    }

  override def description: String =
    s"${assRef.gid.getOrElse("")} | ${copperFile.map(_.name).getOrElse("?")} | ${getClass.getSimpleName}"
}
