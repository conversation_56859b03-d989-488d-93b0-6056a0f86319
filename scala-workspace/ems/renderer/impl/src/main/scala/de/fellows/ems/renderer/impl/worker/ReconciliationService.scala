package de.fellows.ems.renderer.impl.worker

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.Config
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.AssemblyLifecycleStageName.Reconciliation
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, AssemblyService, AssemblyUtils}
import de.fellows.ems.layerstack.api.{LayerStacks, LayerstackService}
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.model.{PCBVersion, RenderConstants}
import de.fellows.ems.renderer.api.Render
import de.fellows.ems.renderer.api.job.ReconciliationJobEntry
import de.fellows.ems.renderer.impl.analysis.GerberBoardReconciler
import de.fellows.ems.renderer.impl.entity.render.{GetRender, RenderEntity}
import de.fellows.ems.renderer.impl.pool.RendererCoordinator
import de.fellows.ems.renderer.impl.supp.SupplementaryImageBuilderTask
import de.fellows.utils.FilePath
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.jobs.QueuedJob
import de.fellows.utils.telemetry.KamonUtils
import kamon.Kamon

import java.util
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class ReconciliationService(
    assemblyService: AssemblyService,
    pcbService: PCBService,
    layerstackService: LayerstackService,
    registry: PersistentEntityRegistry,
    config: Config
) extends StackrateLogging {

  def handleReconciliation(jobInfo: ReconciliationJobEntry, job: QueuedJob)(implicit
      ec: ExecutionContext
  ): Try[Done] =
    SafeAwait.result(
      Kamon.span("handleReconciliation", getClass.getSimpleName) {
        AssemblyUtils.lifecycle(jobInfo.assembly, assemblyService, AssemblyLifecycleStageName.Reconciliation) {
          for {
            _ <- Future.unit
            assRef = jobInfo.assembly
            pcbv   <- pcbService._getPCBVersion(assRef.team, assRef.id, assRef.version).invoke()
            stacks <- layerstackService._getPCBLayerstack(assRef.team, assRef.version, Some(true)).invoke()
            renders <- Future.sequence(pcbv.files.map { lf =>
              val render = registry.refFor[RenderEntity](Render.fileID(assRef.version, lf.name))
              render.ask(GetRender)
                .map(r =>
                  (r.graphics.get(RenderConstants.COPPER_JSON) orElse r.graphics.get(RenderConstants.LAYER_JSON)).map(
                    lf.name -> _
                  )
                )
            }).map(_.flatten.toMap)
            _ <- reconcileBoard(assRef.team, assRef.id, assRef.version, pcbv, stacks, renders)
          } yield Done
        }
      }
    )

  private def reconcileBoard(
      team: String,
      assembly: UUID,
      version: UUID,
      pcbv: PCBVersion,
      stacks: LayerStacks,
      renders: Map[String, FilePath]
  )(implicit ec: ExecutionContext): Future[Done] = {
    val assRef = AssemblyReference(team, assembly, None, version)

    Kamon.span("reconcileBoard", getClass.getSimpleName) {
      KamonUtils.safe(
        AssemblyUtils.lifecycle(team, assembly, version, assemblyService, Reconciliation) {
          if (stacks.selected.isDefined) {
            val callable: util.concurrent.Future[Future[Done]] =
              RendererCoordinator.analysisQueue.submit(new GerberBoardReconciler(
                assRef = assRef,
                pcbservice = pcbService,
                assService = assemblyService,
                layerstacks = stacks,
                reg = registry,
                pcb = pcbv,
                conf = config
              ))

            val task = new SupplementaryImageBuilderTask(pcbv = pcbv, graphics = renders)(logger, config)

            val supp = RendererCoordinator.analysisQueue.submit(task)

            Future {
              Future.sequence(Seq(callable.get, Future.successful(supp.get))).map(_ => Done)
            }.flatten

          } else {
            logger.error("=== no selected stack")
            Future.successful(Done)
          }
        }
      ).map {
        case Failure(exception) =>
          exception.printStackTrace()
          logger.error(s"exception: ${exception.getMessage}")
          Done
        case Success(_) =>
          Done
      }
    }
  }
}
