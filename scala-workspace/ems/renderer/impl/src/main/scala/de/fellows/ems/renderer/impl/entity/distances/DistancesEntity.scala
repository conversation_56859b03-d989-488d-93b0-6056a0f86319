package de.fellows.ems.renderer.impl.entity.distances

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity

class DistancesEntity extends PersistentEntity {
  override type Command = DistancesCommand
  override type Event   = DistancesEvent
  override type State   = Distances

  override def initialState: Distances = Distances(Seq())

  override def behavior: Behavior =
    Actions()
      .onReadOnlyCommand[GetDistances, Distances] {
        case (x: GetDistances, ctx, s) =>
          ctx.reply(s)
      }
      .onCommand[AddDistances, Done] {
        case (AddDistances(ref, file, dst), ctx, s) =>
          ctx.thenPersist(
            DistanceAdded(ref, file, dst)
          )(_ => ctx.reply(Done))
      }
      .onCommand[SetDistances, Done] {
        case (SetDistances(ref, file, dst), ctx, s) =>
          val window = dst.sliding(10, 10).toSeq
          if (window.size > 1) {
            val h      = window.splitAt(1)
            val events = Seq(h._1.map(DistancesSet(ref, file, _)), h._2.map(DistanceAdded(ref, file, _))).flatten
            ctx.thenPersistAll(events: _*)(() => ctx.reply(Done))
          } else if (window.size == 1) {
            ctx.thenPersist(DistancesSet(ref, file, window.head))(_ => ctx.reply(Done))
          } else {
            ctx.thenPersist(DistancesSet(ref, file, Seq()))(_ => ctx.reply(Done))
          }
      }
      .onEvent {
        case (DistanceAdded(version, file, distance), s) =>
          s.copy(distances = s.distances ++ distance)
        case (DistancesSet(version, file, distances), s) =>
          s.copy(distances = distances)
      }
}
