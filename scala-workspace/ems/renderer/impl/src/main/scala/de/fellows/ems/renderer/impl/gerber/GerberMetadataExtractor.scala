package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.gerber.parser.GerberParser
import de.fellows.ems.pcb.model.graphics.ops.Movement
import de.fellows.ems.pcb.model.graphics.{GPoint, Geometry, Polarity}
import de.fellows.ems.renderer.impl.gerber.GerberExceptions._
import de.fellows.ems.renderer.impl.gerber.builders.{BasicFileListener, BoundsBuilder}
import de.fellows.ems.renderer.impl.gerber.graphic.ApertureUtils
import de.fellows.ems.renderer.impl.gerber.interpolation.awt.Interpolator
import de.fellows.ems.renderer.impl.simple.SimpleApertureStore
import org.antlr.v4.runtime.ParserRuleContext

import java.awt.geom.Rectangle2D

class GerberMetadataExtractor extends BasicFileListener {

  override def polarityChanged(from: Polarity, to: Polarity): Unit = {}

  val currentPoint: Option[GPoint] = None

  var b = new BoundsBuilder()

  def bounds: Rectangle2D = b.result()

  def min: GPoint = GPoint(b.result().getMinX, b.result().getMinY)

  def max: GPoint = GPoint(b.result().getMaxX, b.result().getMaxY)

  val apertureStore = new SimpleApertureStore()

  override def apReg: ApertureRegistry = apertureStore

  override def closeStepRepeat(implicit ctx: ParserRuleContext): Unit = {}

  override def endRegion(): Unit =
    this.gContext.region.foreach(_.foreach { p =>
      val bnds = Geometry.bounds(p.path)
      bnds.foreach(this.b.extend)
    })

  override def flash(target: GPoint)(implicit v: ParserRuleContext): Unit = {
    val area = ApertureUtils.getArea(target, this.gContext.aperture.get, scaling, macroDefinitions.toMap)
    val bnds = Geometry.bounds(area)
    bnds.foreach(this.b.extend)
  }

  override def doInterpolate(point: GPoint, i: Option[Double], j: Option[Double])(implicit
      v: GerberParser.OpContext
  ): Unit = {

    val from: GPoint        = (gContext.point.getOrElse("No current point exist" !))
    val target: GPoint      = (createFrom.getOrElse("Point is missing" !))
    var rel: Option[GPoint] = None
    if (v.i != null || v.j != null) {
      val i: Double = Option(v.i).map(x => gContext.format.get.toX(x.getText)).getOrElse(0.0)
      val j: Double = Option(v.j).map(y => gContext.format.get.toY(y.getText)).getOrElse(0.0)
      rel = Some((GPoint(i, j)))
    }

    val movingXUp = target.x - from.x
    val movingYUp = target.y - from.y

    val line = Interpolator(
      quad = gContext.quadrant,
      interpolate = gContext.interpolation,
      from = from,
      to = target,
      relativePoint = rel,
      ad = null,
      movement = Movement(movingXUp, movingYUp),
      scaling = scaling
    ).getLine(false)

    val bnds = Geometry.bounds(line)
    bnds.foreach(this.b.extend)
  }
}
