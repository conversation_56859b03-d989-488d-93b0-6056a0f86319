package de.fellows.ems.renderer.impl

import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assembly.commons.AssemblyFiles
import de.fellows.ems.layerstack.api.{LayerStack, LayerstackService}
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.pcb.model.{Outline, PCBSpecification, PCBVersion, RenderedPreviewFile}
import de.fellows.ems.renderer.api.Render
import de.fellows.ems.renderer.impl.RendererServiceImpl.fileID
import de.fellows.ems.renderer.impl.entity.render.{GetRender, RenderEntity}
import de.fellows.utils.FilePath
import de.fellows.utils.FutureUtils.option
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogger
import org.w3c.dom.Document

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

case class RenderedPreview(specification: UUID, front: Option[Document], rear: Option[Document])

case class StackConfig(front: Boolean, rear: Boolean) {
  def getFirst(rp: RenderedPreview): Document =
    Seq(rp.front, rp.rear).flatten.head
}

/** Renders the given specification
  */
class StatelessPreviewRenderer(
    spec: PCBSpecification,
    pcbv: PCBVersion,
    outline: Outline,
    stack: LayerStack,
    renderStates: Seq[Render],
    conf: StackConfig
)(implicit logger: StackrateLogger) {

  def renderPreview(): RenderedPreview = {
    val specId =
      spec.id

    val outlinePath = outline.path

    logger.warn(s"render preview for spec $spec")
    val rear = conf.rear match {
      case false => None
      case true => Some(new SVGStackup(
          stack.stacks.head.layers,
          renderStates,
          outlinePath,
          stack.unmatchedFiles,
          Some(spec.settings),
          pcbv.reconciledHoles,
          pcbv.holes
        ).build(true))
    }

    val front = conf.front match {
      case false => None
      case true => Some(new SVGStackup(
          stack.stacks.head.layers,
          renderStates,
          outlinePath,
          stack.unmatchedFiles,
          Some(spec.settings),
          pcbv.reconciledHoles,
          pcbv.holes
        ).build(false))
    }

    RenderedPreview(specId, front, rear)
  }
}

/** Retrieves the given ids from the various given services, and renders them using StatelessPreviewRenderer
  */
class PreviewRenderer(
    pcb: PCBService,
    layerstack: LayerstackService,
    registry: PersistentEntityRegistry,
    pcbv: PCBVersion,
    outline: Outline
)(implicit logger: StackrateLogger) {

  val config = ConfigFactory.load()

  def stack(assembly: UUID, version: UUID, specification: Option[UUID], team: String, conf: StackConfig)(implicit
      ec: ExecutionContext
  ): Future[RenderedPreview] =
    (for {
      spec <- option(specification.orElse(pcbv.defaultSpecification).map(sid =>
        pcb._getSpecification(team, assembly, version, sid).invoke()
      ))
      stack <- layerstack._getPCBLayerstack(team, version, None).invoke().map(_.selected)
      render: Seq[Render] <-
        Future.sequence(pcbv.files.map(f => registry.refFor[RenderEntity](fileID(version, f.name)).ask(GetRender)))

    } yield new StatelessPreviewRenderer(
      spec = spec.flatMap(_.headOption).getOrElse(throw new TransportException(
        TransportErrorCode.NotFound,
        "specification not found"
      )),
      pcbv = pcbv,
      outline = outline,
      stack = stack.getOrElse(throw new TransportException(
        TransportErrorCode.NotFound,
        "layerstack not found"
      )),
      renderStates = render,
      conf = conf
    ).renderPreview()).recover {
      case e: TransportException => throw e
      case e: Throwable          => throw new TransportException(TransportErrorCode.InternalServerError, e)
    }

  def _doPreview(
      assembly: UUID,
      version: UUID,
      specification: Option[UUID],
      team: String,
      size: Float,
      targetPath: Option[FilePath] = None,
      conf: StackConfig
  )(implicit ec: ExecutionContext): Future[RenderedPreviewFile] =
    stack(assembly, version, specification, team, conf).map { rprev =>
      implicit val _c: Config = config

      val target    = targetPath.getOrElse(AssemblyFiles.createAssemblyResourceFolder(team, assembly, version))
      val rearFile  = rprev.rear.map(PreviewRenderer.save(true, _, target, rprev.specification, size))
      val frontFile = rprev.front.map(PreviewRenderer.save(false, _, target, rprev.specification, size))

      RenderedPreviewFile(rprev.specification, front = frontFile, rear = rearFile)
    }

  def _doHighQualityRenderPreview(
      assembly: UUID,
      version: UUID,
      specification: Option[UUID],
      team: String,
      conf: StackConfig
  )(implicit ec: ExecutionContext, sd: ServiceDefinition): Future[RenderedPreview] =
    stack(assembly, version, specification, team, conf)

  def _doRenderPreview(assembly: UUID, version: UUID, specification: Option[UUID], team: String, conf: StackConfig)(
      implicit ec: ExecutionContext
  ): Future[RenderedPreviewFile] =
    _doPreview(assembly, version, specification, team, PreviewRenderer.DEFAULT_SIZE, None, conf)
}

object PreviewRenderer {
  val DEFAULT_SIZE = 600f

  def save(rear: Boolean, doc: Document, targetPath: FilePath, specification: UUID, size: Float) = {
    val side = rear match {
      case true  => "rear"
      case false => "front"
    }

    val path = targetPath
      .copy(base = s"specifications/${specification}", filename = s"specification-preview-${side}.webp")

    val svgpath = targetPath
      .copy(base = s"specifications/${specification}", filename = s"specification-preview-${side}.svg")

    path.createParentDir()

    SVGStackup.writeWEBP(doc, path.toPath, size)

    SVGStackup.writeSVG(doc, svgpath.toPath)

    path
  }
}
