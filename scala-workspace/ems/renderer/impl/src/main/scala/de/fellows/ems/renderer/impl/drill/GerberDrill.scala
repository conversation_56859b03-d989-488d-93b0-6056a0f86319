package de.fellows.ems.renderer.impl.drill

import de.fellows.ems.gerber.parser.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, G<PERSON>berParser}
import de.fellows.ems.pcb.model._
import de.fellows.utils.UUIDUtils
import de.fellows.utils.internal.FileType
import org.antlr.v4.runtime.tree.{ParseTree, ParseTreeWalker}
import org.antlr.v4.runtime.{CharStreams, CommonTokenStream, Parser}

import java.io.FileInputStream

case class DrillDefinition(
    tool: BigDecimal,
    diameter: BigDecimal,
    drillType: Option[String]
)

case class DrillFile(
    startLayer: Option[BigDecimal] = None,
    endLayer: Option[BigDecimal] = None,
    drills: Seq[DrillDefinition],
    holes: Seq[Hole]
)

case class Hole(
    x: BigDecimal,
    y: BigDecimal,
    drill: BigDecimal
)

case class Slot(
    startX: BigDecimal,
    startY: BigDecimal,
    endX: BigDecimal,
    endY: BigDecimal,
    drill: BigDecimal
)

case class ParserWrapper[T <: Parser](p: T, listener: (String) => DrillListener, root: (T) => ParseTree) {
  def getRoot(): ParseTree = root(p)
}

class GerberDrill(file: java.io.File, fileId: String, filetype: Option[FileType]) {

  val antlr = GerberDrill.createGerberParser(file)

  val walker = new ParseTreeWalker

  val defTool =
    filetype.map { t =>
      if (t.fileType == LayerConstants.PH_DRILL) {
        Tool.PLATED
      } else if (t.fileType == LayerConstants.NPH_DRILL) {
        Tool.NON_PLATED
      } else {
        Tool.UNKNOWN
      }
    }.getOrElse(Tool.UNKNOWN)

  val listener = antlr.listener(defTool) // new ExcellonDrillListener(defTool)

  def build(): DrillFile = {
    walker.walk(listener, antlr.getRoot())
    val range = listener.getRange

    DrillFile(
      startLayer = range._1,
      endLayer = range._2,
      drills = listener.getTools.values.toSeq,
      holes = listener.getHoles
    )
  }

  def dimension(): Option[Dimension] =
    listener.getDimension

  /** Build the drill model.
    *
    * This builds an [[UnreconciledHoleList]], even though it doesn't really need reconciliation. But using an unreconciled list
    * makes the gerber drill model fit into the rest of the drill reconciliation/analysis without keeping extra cases
    * for gerber drills each time.
    *
    * all numbers in this model are [[NumberWithScale with scale]], so reconciliation will only yield one result
    * and doesnt effect the efficiency of the reconciliation.
    *
    * It also has the advantage of automatically unifying the unit to metric later.
    *
    * @return
    */
  def buildModel: UnreconciledHoleList = {
    val drillfile = build()
    val holes     = drillfile.holes.groupBy(_.drill)

    UnreconciledHoleList(
      tools = drillfile.drills.map { ddef =>
        UnreconciledTool(
          name = s"T${ddef.tool}-${fileId}",
          diameter = NumberWithScale(ddef.diameter / listener.getMMScaling),
          drills = holes.get(ddef.tool) match {
            case Some(drillhits) => drillhits.map { hole =>
                UnreconciledDrillHit(
                  UnreconciledPoint.absolute(
                    NumberWithScale(hole.x / listener.getMMScaling),
                    NumberWithScale(hole.y / listener.getMMScaling)
                  ),
                  UUIDUtils.createTiny()
                )

              }
            case None => Seq()
          },
          slots = Seq(),
          drillType = ddef.drillType
        )
      },
      formatHints = UnreconciledFormatHints(
        from = drillfile.startLayer.map(_.toString()),
        to = drillfile.endLayer.map(_.toString()),
        fileUnit = UnknownDrillUnit,
        intPlaces = None,
        decimalPlaces = None,
        includesLeadingZeroes = None
      )
    )
  }
}

object GerberDrill {
  def createGerberParser(f: java.io.File): ParserWrapper[GerberParser] = {
    val stream                = CharStreams.fromStream(new FileInputStream(f))
    val l                     = new GerberLexer(stream)
    val ts: CommonTokenStream = new CommonTokenStream(l)
    ParserWrapper(new GerberParser(ts), (dflt: String) => new GerberDrillListener(dflt), (p) => p.gerber())
  }
}
