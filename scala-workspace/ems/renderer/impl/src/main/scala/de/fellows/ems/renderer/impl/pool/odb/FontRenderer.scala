package de.fellows.ems.renderer.impl.pool.odb

import de.fellows.ems.pcb.model.graphics.ops.{Interpolation, Movement}
import de.fellows.ems.pcb.model.graphics.{GPoint, Geometry, Rectangle}
import de.fellows.ems.renderer.impl.gerber.interpolation.awt.Interpolator
import de.luminovo.odb.odbpp.model.ODBFeatures.TextRecord
import de.luminovo.odb.odbpp.model.constants.RoundedChar
import de.luminovo.odb.odbpp.model.font.ODBFont

import java.awt.geom.{Area, Ellipse2D}

object FontRenderer {
  def renderChar(
      char: String,
      font: ODBFont,
      scaledX: Double,
      scaledY: Double,
      internalScale: Int,
      textRecord: TextRecord,
      xScale: Double,
      yScale: Double,
      flip: Boolean = false
  ): Either[String, Seq[Area]] =
    font.get(char) match {
      case Some(charInFont) =>
        Right(charInFont.lines.map { l =>

          val off = font.offset * internalScale
          val xs             = l.xs * internalScale
          val ys             = l.ys * internalScale
          val xe             = l.xe * internalScale
          val ye             = l.ye * internalScale
          val from           = (GPoint((xs * xScale) + scaledX, (ys * yScale) + scaledY)).flipY(flip)
          val to             = (GPoint((xe * xScale) + scaledX, (ye * yScale) + scaledY)).flipY(flip)
          val diameter       = (l.width * xScale * internalScale)
          val radius         = diameter / 2
          val movement       = Movement(from, to)
          val radians        = Geometry.calculateRadians(from, to, movement.moveXUp)
          val rec: Rectangle = Interpolation.createRectForC(from, to, radians, diameter)

          val path = new Area()
          if (l.shape == RoundedChar) {

            val startFlash = new Ellipse2D.Double()
            startFlash.setFrameFromCenter(from.getBX, from.getBY, from.getBX + radius, from.getBY + radius)
            path.add(
              new Area(
                startFlash
              )
            )
            val endFlash = new Ellipse2D.Double()
            endFlash.setFrameFromCenter(to.getBX, to.getBY, to.getBX + radius, to.getBY + radius)
            path.add(
              new Area(
                endFlash
              )
            )

          } else {
            Seq()
          }

          path.add(Interpolator.createRecArea(rec))

          path
        })

      case None => Left("char not supported")
    }
}
