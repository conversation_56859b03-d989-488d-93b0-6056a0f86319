package de.fellows.ems.renderer.impl.pool.layerstack

import de.fellows.ems.layerstack.api
import de.fellows.ems.pcb.model.{HoleList, Tool}
import org.apache.batik.svggen.SVGGraphics2D

import java.awt.Color
import java.awt.geom.{Path2D, Rectangle2D}
import scala.collection.mutable

class MutableSubStackRenderState(substack: api.SubStackDefinition) {

  def sortDrills(max: Int): DrawnDrills => (Int, Int) = { d =>
    val f = d.from.getOrElse(0)
    val t = d.to.getOrElse(max)

    (max - f, t - f)
  }

  def draw(_gr: SVGGraphics2D, params: RenderParameters): Rectangle2D = {

    val copperIndex = mutable.HashMap.newBuilder[Int, (Double, Double)]

    var currentCopperLayer = 0

    val layersResult = layers.result()
    val drillresult  = drills.result()

    val drillwidth = drillresult.length match {
      case 0 => 0
      case x => (x * (params.drillwidth + params.drillgap)) + (params.drillpadding * 2)
    }

    val width = Math.max(drillwidth, params.minimumWidth)

    val layerGraphics = _gr.create().asInstanceOf[SVGGraphics2D]

    var wasPadded              = true
    var currentStartCoordinate = 0.0

    (layersResult.foreach { layer =>
      println(s"draw layer ${layer}")
      val colorBuffer = layerGraphics.getColor
      layerGraphics.setColor(params.color(layer.material))

      val preOffset =
        if (layer.padding && !wasPadded) {
          params.layerpadding
        } else {
          0.0
        }

      layerGraphics.translate(0, preOffset)

      layerGraphics.fill(new Rectangle2D.Double(0, 0, width, layer.height))

      val heightWithPadding = (layer.height +
        (layer.padding match {
          case true  => params.layerpadding
          case false => 0
        }))

      layerGraphics.translate(0, heightWithPadding)
      layerGraphics.setColor(colorBuffer)

      if (layer.copper) {
        copperIndex += currentCopperLayer -> (currentStartCoordinate, currentStartCoordinate + layer.height)
        currentCopperLayer += 1
      }
      currentStartCoordinate += preOffset + heightWithPadding
      wasPadded = layer.padding
    })

    val lastCopper = layersResult.count(_.copper) - 1

    val copperIndexResult = copperIndex.result()

    val holesGraphics = _gr.create().asInstanceOf[SVGGraphics2D]

    drillresult.sortBy(sortDrills(lastCopper)).reverse.zipWithIndex.foreach { zipped =>
      val (h, idx) = zipped
      val from     = h.from.getOrElse(0)
      val to       = h.to.getOrElse(lastCopper)

      if (lastCopper >= to) {
        drawDrillLine(h, params, copperIndexResult(from)._1, copperIndexResult(to)._2, holesGraphics, idx)
      }
    }

    new Rectangle2D.Double(0, 0, width, currentStartCoordinate)
  }

  def drawGradientHole(
      g2d: SVGGraphics2D,
      startX: Double,
      startY: Double,
      width: Double,
      height: Double,
      color: Color,
      taperAmount: Option[Double]
  ) = {
    import java.awt.{Color, GradientPaint}
    val startColor = Color.BLACK
    val endColor   = color

    val endX = startX + (width / 2)
    val endY = startY

    val stretch = 20

    val gradient = new GradientPaint(
      (startX - stretch).floatValue(),
      startY.floatValue(),
      startColor,
      (endX).floatValue(),
      endY.floatValue(),
      endColor,
      true
    )
    g2d.setPaint(gradient)

    g2d.fill(createBorderPath(startX, startY, width, height, taperAmount.map(-_), true))
  }

  private def drawDrillLine(
      h: DrawnDrills,
      params: RenderParameters,
      fromCoords: Double,
      toCoords: Double,
      holesGraphics: SVGGraphics2D,
      idx: Int
  ): Unit = {
    val startX = params.drillpadding + (idx * (params.drillwidth + params.drillgap))
    val height = toCoords - fromCoords
    val border = params.drillwidth * 0.1
    val center = params.drillwidth * 0.8

    if (h.plated) {
      if (h.micro) {
        drawGenericHole(params, fromCoords, holesGraphics, startX, height, border, center, true, true)

      } else {
        drawGenericHole(params, fromCoords, holesGraphics, startX, height, border, center, true, false)
      }
    } else {
      drawGenericHole(params, fromCoords, holesGraphics, startX, height, border, center, false, false)
    }
  }

  private def drawGenericHole(
      params: RenderParameters,
      fromY: Double,
      holesGraphics: SVGGraphics2D,
      startX: Double,
      height: Double,
      border: Double,
      center: Double,
      plated: Boolean,
      tapered: Boolean
  ): Unit = {

    val taperAmount = tapered match {
      case false => None
      case true  => Some(border * 2)
    }

    val border1 =
      createBorderPath(startX, fromY, border, height, taperAmount, false)

    val r3 =
      createBorderPath(startX + border + center, fromY, border, height, taperAmount.map(-_), false)

    if (plated) {
      holesGraphics.setColor(params.viaBorder)
    } else {
      holesGraphics.setColor(params.holeBorder)
    }
    holesGraphics.fill(border1)

    val centerColor =
      if (plated) {
        (params.viaCenter)
      } else {
        (params.holeCenter)
      }

    drawGradientHole(holesGraphics, startX + border, fromY, center, height, centerColor, taperAmount)

    if (plated) {
      holesGraphics.setColor(params.viaBorder)
    } else {
      holesGraphics.setColor(params.holeBorder)

    }
    holesGraphics.fill(r3)
  }

  private def createBorderPath(
      startX: Double,
      fromY: Double,
      width: Double,
      height: Double,
      taper: Option[Double],
      symmetricTaper: Boolean
  ): Path2D = {
    val path = new Path2D.Double()
    path.moveTo(startX, fromY)
    path.lineTo(startX + width, fromY)
    path.lineTo(startX + width + taper.getOrElse(0.0), fromY + height)
    if (symmetricTaper) {
      path.lineTo(startX - taper.getOrElse(0.0), fromY + height)
    } else {
      path.lineTo(startX + taper.getOrElse(0.0), fromY + height)
    }
    path.closePath()
    path
  }

  val layers = Seq.newBuilder[DrawnLayer]
  val drills = Seq.newBuilder[DrawnDrills]

  def addMaterial(height: Double, material: Option[String], padding: Boolean, copper: Boolean) =
    layers += DrawnLayer(
      height,
      material,
      padding,
      copper
    )

  def addDrills(hl: HoleList): Unit = {
    val microvia = hl.tools.exists(t => t.diameter < 0.1 && t.drillType == Tool.PLATED)
    val via      = hl.tools.exists(t => t.diameter >= 0.1 && t.drillType == Tool.PLATED)
    val hole     = hl.tools.exists(t => t.drillType == Tool.NON_PLATED)

    if (microvia) {
      drills += DrawnDrills(
        hl.from.map(_.intValue),
        hl.to.map(_.intValue),
        micro = true,
        plated = true
      )
    }
    if (via) {
      drills += DrawnDrills(
        hl.from.map(_.intValue),
        hl.to.map(_.intValue),
        micro = false,
        plated = true
      )
    }
    if (hole) {
      drills += DrawnDrills(
        hl.from.map(_.intValue),
        hl.to.map(_.intValue),
        micro = false,
        plated = false
      )
    }
  }
}
