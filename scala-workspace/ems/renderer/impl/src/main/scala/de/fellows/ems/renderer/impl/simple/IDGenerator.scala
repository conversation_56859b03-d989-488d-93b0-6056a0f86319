package de.fellows.ems.renderer.impl.simple

/** Generates IDs for SVG files.
  * focuses on generating as short ids as possible
  */
class IDGenerator {
  var current = 0
  // generate a unique single character id
  val chars = "abcdefghijklmnopqrstuvwxyz"
  def generateId(): String = {
    val sb = new StringBuilder

    def getChars(i: Int): String =
      if (i >= chars.length) {
        getChars((i / chars.length) - 1) + chars.charAt(i % chars.length)
      } else {
        s"${chars.charAt(i)}"
      }

    val r = getChars(current)
    current += 1
    r
  }
}
