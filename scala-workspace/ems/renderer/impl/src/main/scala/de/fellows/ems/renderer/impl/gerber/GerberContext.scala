package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.gerber.parser.GerberParser.OpContext
import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, Dark, GPoint, GerberApertureDefinition, Polarity}
import org.apache.batik.ext.awt.geom.ExtendedGeneralPath

/** Describes a contour in a gerber file.
  * @param flash
  *   whether this contour can be seen as a flash
  */
case class GerberContour(path: ExtendedGeneralPath, flash: Boolean = true)

case class GerberContext(
    var polarity: Polarity = Dark(),
    var aperture: Option[GerberApertureDefinition] = None,
    var explicitlySelected: Option[Boolean] = None,
    var region: Option[Seq[GerberContour]] = None,
    var contour: Option[GerberContour] = None,
    var format: Option[GerberFormat] = None,
    var point: Option[GPoint] = Some(GPoint(0, 0)),
    var steprepeat: Option[StepRepeat] = None,
    var interpolation: Option[InterpolationMode] = None,
    var deprecatedOperation: Option[String] = None,
    var quadrant: Option[Quadrant] = None
) {

  private var index = 0

  def getAndIncrementIndex: Int =
    this.synchronized {
      val i = index
      index += 1
      i
    }

  def setFormat(newFormat: GerberFormat): Unit =
    format = Some(
      newFormat.copy(
        gerberUnit = format.getOrElse(newFormat).gerberUnit,
        gerberScaling = format.getOrElse(newFormat).gerberScaling
      )
    )

  private def getFormatOrDefault: GerberFormat =
    format.getOrElse(GerberFormat.DEFAULT_FORMAT)

  def setMeasurementUnit(u: MeasurementUnit): GerberFormat = {
    val baseScale = 100
    val scale = u match {
      case Millimetre() => baseScale
      case Inch()       => baseScale * 25.4
    }
    val format = getFormatOrDefault.copy(
      gerberUnit = Some(u),
      gerberScaling = Some(scale.intValue())
    )

    this.format = Some(format)

    format
  }

  def setGerberCoordinate(coordinate: GerberCoordinate): Unit = {
    val format = getFormatOrDefault.copy(
      coordinate = coordinate
    )

    this.format = Some(format)
  }
}

object GerberContext {}

sealed trait InterpolationMode

case class Linear() extends InterpolationMode

case class Clockwise() extends InterpolationMode

case class Counterclockwise() extends InterpolationMode

sealed trait Quadrant

case class MultiQuadrant() extends Quadrant

case class SingleQuadrant() extends Quadrant
