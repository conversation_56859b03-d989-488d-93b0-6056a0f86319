package de.fellows.ems.renderer.impl.analysis

import de.fellows.ems.layerstack.api.{LayerStack, LayerStacks, MaterialTypes}
import de.fellows.ems.pcb.model.graphics.parts.{ComplexRegion, Flash, Line, Polygon}
import de.fellows.ems.pcb.model.graphics.tree.{PCBLayerInternalData, Trace, TraceIndex}
import de.fellows.ems.pcb.model.graphics.{Graphic => GGraphic}
import de.fellows.ems.pcb.model.{
  graphics,
  BigPoint,
  DrillFormat,
  DrillHit,
  DrillSet,
  Format,
  GerberFile,
  Graphic,
  GraphicElement,
  HoleList,
  LayerConstants,
  Net,
  NetList,
  NetTrace,
  NumberWithScale,
  NumberWithoutScale,
  Tool,
  UnreconciledHoleList
}
import de.fellows.ems.renderer.impl.gerber.builders.BoundsBuilder
import de.fellows.ems.renderer.impl.outline.DensityCalculator
import de.fellows.utils.internal.FileReader
import de.fellows.utils.{DebugUtils, FilePath}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.telemetry.KamonUtils
import kamon.Kamon
import play.api.Logging

import java.awt.geom.{Area, Ellipse2D, Path2D, Rectangle2D}
import java.util.UUID
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import scala.math.BigDecimal.RoundingMode

case class ToolRange(from: Int, to: Int) {
  def includes(index: Int): Boolean =
    index >= from && index <= to
}

case class ToolInfo(
    toolType: String,
    range: ToolRange,
    netlists: Map[DrillHit, Map[Int, Seq[(Trace, GerberFile)]]]
)

object ToolInfo {
  def fromHoleListFile(holeListWithFile: HoleListWithFile, copperFiles: Seq[GerberFile]): Option[ToolInfo] =
    holeListWithFile.file.flatMap { file =>
      (file.fType.from, file.fType.to) match {
        case (Some(from), Some(to)) if from < to =>
          val toolType = file.fType.fileType match {
            case LayerConstants.NPH_DRILL => Tool.NON_PLATED
            case LayerConstants.PH_DRILL  => Tool.PLATED
            case _                        => Tool.UNKNOWN
          }

          Some(
            ToolInfo(
              toolType = toolType,
              range = ToolRange(
                from = Math.max(from - 1, 0),
                to = Math.min(to - 1, copperFiles.size - 1)
              ),
              netlists = Map()
            )
          )

        case _ => None
      }
    }
}

private case class DistinctDrillId(
    from: Option[BigDecimal],
    to: Option[BigDecimal],
    diameter: BigDecimal,
    x: BigDecimal,
    y: BigDecimal
)

object Reconciliation extends StackrateLogging {
  val DEBUG_IMAGE = false

  def getCopperLayers(layerstacks: LayerStacks): Seq[GerberFile] =
    layerstacks
      .selected
      .toSeq
      .flatMap(ls => getCopperLayers(ls))

  def getCopperLayers(ls: LayerStack): Seq[GerberFile] =
    ls.stacks.head.layers
      .filter {
        _.definition.layerType match {
          case Some(layerType) => MaterialTypes.COPPER.contains(layerType)
          case None            => false
        }
      }
      .flatMap(_.files.getOrElse(Seq()))

  private def isPad(x: graphics.Graphic, t: Tool, format: Option[Format]): Boolean =
    x match {
      case l: Flash =>
        val scale = format.flatMap(_.gerberscale).getOrElse(1.0)
        l.aperture.diameter(scale).forall { flashDiameter =>
          // if we dont know the size of the flash (for instance macros do not necessarily have a diameter)
          // just assume that its large enough

          (flashDiameter) > t.diameter
        }
      case x: Polygon if x.isPad => true
      case x: ComplexRegion      => false
      case _                     => false
    }

  final case class HoleListAnalysis(
      holeList: HoleListWithFile,
      toolRange: ToolRange,
      toolInfos: ListBuffer[(Tool, ToolInfo)],
      toolAnalysis: ListBuffer[ToolAnalysis]
  )

  final case class ToolAnalysis(
      tool: Tool,
      drillHits: Seq[(DrillHit, Ellipse2D.Double)],
      platedOnLayers: mutable.BitSet,
      mutNetLists: mutable.Map[DrillHit, mutable.Map[Int, Seq[(Trace, GerberFile)]]]
  )

  private def getToolRangesAndTypes(
      renders: Seq[(GerberFile, FilePath)],
      holeLists: Seq[HoleListWithFile],
      layerstacks: LayerStack,
      allFiles: Seq[DrillSet],
      fileTraces: mutable.Map[GerberFile, Seq[Trace]]
  ): Vector[(HoleListWithFile, Vector[(Tool, ToolInfo)])] = {
    val coppers = getCopperLayers(layerstacks)
    val Empty   = Map.empty[DrillHit, Map[Int, Seq[(Trace, GerberFile)]]]

    val withRanges = holeLists.toVector.map { holeList =>
      val origFrom = holeList.holeList.from.map(_.intValue)
      val origTo   = holeList.holeList.to.map(_.intValue)

      val (preparedFrom, preparedTo) =
        if (holeList.holeList.from.isEmpty || holeList.holeList.to.isEmpty) {
          lazy val range = RangeAnalyzer.getRangeFromFile(layerstacks, holeList, allFiles)
          val from       = origFrom orElse range.map(_.from)
          val to         = origTo orElse range.map(_.to)

          (from, to)
        } else {
          (origFrom, origTo)
        }

      val toolInfoFromFile = ToolInfo.fromHoleListFile(holeList, coppers)
      val range            = toolRange(preparedFrom, preparedTo, toolInfoFromFile.map(_.range), renders.size)

      val nonPlated = mutable.ListBuffer[(Tool, ToolInfo)]()
      val rest      = mutable.ListBuffer[ToolAnalysis]()

      holeList
        .holeList
        .tools
        .foreach { tool =>
          if (tool.drillType == Tool.NON_PLATED) {
            nonPlated.addOne(
              tool ->
                ToolInfo(
                  Tool.NON_PLATED,
                  range = ToolRange(
                    from = 0,
                    to = coppers.length - 1
                  ),
                  netlists = Empty
                )
            )
          } else {
            rest.addOne(ToolAnalysis(
              tool,
              tool.drills.map(drill => drill -> tool.graphic(drill)),
              new mutable.BitSet(renders.size),
              mutable.Map[DrillHit, mutable.Map[Int, Seq[(Trace, GerberFile)]]]()
            ))
          }
        }

      HoleListAnalysis(holeList, range, nonPlated, rest)
    }

    renders.zipWithIndex.foreach {
      case ((file, path), index) =>
        FileReader.json[PCBLayerInternalData](path.toJavaPath).toOption.flatMap(_.asOpt) match {
          case None =>

          case Some(internalData) =>
            withRanges.foreach { ha =>
              if (ha.toolRange.includes(index)) {
                ha.toolAnalysis.foreach { ta =>
                  val isPlated = isToolPlated(
                    internalData,
                    file,
                    index,
                    ta.tool,
                    ta.drillHits,
                    ta.mutNetLists
                  )

                  if (isPlated) {
                    ta.platedOnLayers.add(index)
                  }
                }
              }
            }
            internalData.traces.foreach { traces =>
              fileTraces.put(file, traces)
            }
        }
    }

    val layerRange = Range(0, renders.length).toVector

    withRanges.map { ha =>
      val restToolInfos = ha.toolAnalysis.toSeq.map { ta =>
        logger.info(s"[RECONCILIATION] TOOL ${ta.tool.name} is plated on: [${
            layerRange.map { idx =>
              if (ta.platedOnLayers(idx)) {
                "X"
              } else {
                "-"
              }
            }.mkString(",")
          }]")

        val netLists         = ta.mutNetLists.map { case (d, m) => d -> m.toMap }.toMap
        val toolInfoFromFile = ToolInfo.fromHoleListFile(ha.holeList, coppers)
        val toolInfo = toolInfoFromFile match {
          case Some(toolInfo) =>
            if (toolInfo.toolType == Tool.UNKNOWN) {
              val toolType =
                if (toolInfo.range.from == 0 && toolInfo.range.to == renders.length - 1) {
                  Tool.NON_PLATED
                } else {
                  Tool.PLATED
                }

              logger.warn(
                s"tool type from file is unknown, computing based on defined layers. new toolType=${toolType}"
              )

              toolInfo.copy(
                toolType = toolType,
                netlists = netLists
              )
            } else {
              toolInfo.copy(netlists = netLists)
            }

          case None =>
            /* If there's at least 1 layer where the drill is found to be plated (see above),
             * then the range depends on which layers were marked as plated.
             *
             * Otherwise, if no layer was found with this condition, we check if the user set any
             * drill ranges. If he did, then we need to respect that.
             *
             * Otherwise, we assume it's a hole that goes through all layers
             */
            val (from, to, toolType) =
              if (ta.tool.drillType != Tool.NON_PLATED) {
                // it's either plated or unknown, so we need to check which layers are plated
                val layersWithPads = layerRange.filter(idx => ta.platedOnLayers(idx))

                // if only one entry exists, then the range for this tool
                // is a single layer which is not valid
                val (from, to, calculatedType) =
                  if (layersWithPads.size > 1) {
                    (layersWithPads.min, layersWithPads.max, Tool.PLATED)
                  } else {
                    logger.warn("via cannot start and end on the same layer, assuming non plated")
                    (0, renders.length - 1, Tool.NON_PLATED)
                  }

                // keep the user's choice if he set it
                val drillType = ta.tool.drillType match {
                  case Tool.UNKNOWN => calculatedType
                  case _            => ta.tool.drillType
                }

                (from, to, drillType)
              } else {
                (0, renders.length - 1, Tool.NON_PLATED)
              }

            ToolInfo(toolType, ToolRange(from, to), netLists)
        }

        ta.tool -> toolInfo
      }

      ha.holeList -> (ha.toolInfos.toVector ++ restToolInfos)
    }
  }

  private def toolRange(
      preparedFrom: Option[Int],
      preparedTo: Option[Int],
      rangeFromFileType: Option[ToolRange],
      numberCopperLayers: Int
  ): ToolRange =
    rangeFromFileType match {
      case Some(range) => ToolRange(range.from, range.to)
      case None =>
        ToolRange(
          preparedFrom.getOrElse(0),
          preparedTo.getOrElse(numberCopperLayers)
        )
    }

  private def isToolPlated(
      internalData: PCBLayerInternalData,
      gerberFile: GerberFile,
      index: Int,
      t: Tool,
      holes: Seq[(DrillHit, Ellipse2D)],
      mutNetLists: mutable.Map[DrillHit, mutable.Map[Int, Seq[(Trace, GerberFile)]]]
  ): Boolean = {
    val tree     = internalData.tree
    val traces   = internalData.traceIndex
    val inverted = gerberFile.inverted.getOrElse(false)
    val format   = gerberFile.format
    val scale    = format.flatMap(_.gerberscale).getOrElse(1.0)

    var onThisLayerCount = 0
    var totalCount       = 0

    holes.foreach {
      case (drillhit, shape) =>
        val collisions = tree.collideWithCheckerOrBoundsIter[GraphicHelper](
          GraphicHelper(shape),
          shape.getBounds2D,
          tree.getRoot(),
          pop = false,
          (one: GraphicHelper, two: GGraphic) => {
            val possible = (one, two) match {
              case (_, x: Polygon) if !x.flash => false
              case (_, _: ComplexRegion)       => false
              case _                           => true
            }

            if (possible && one.fastIntersects(two)) {
              one.intersects(two)
            } else {
              false
            }
          }
        )

        val plated =
          if (inverted) {
            collisions.isEmpty
          } else {
            val validCollisions = collisions.count { x =>
              isPad(x, t, format) || (x match {
                case l: Line =>
                  l.aperture.diameter(scale) match {
                    case Some(lineDiameter) => (lineDiameter) > t.diameter
                    case None               => false // if we dont know the line diameter assume that it is not a pad
                  }

                case _ => false
              })
            }

            if (validCollisions > 0) {
              true
            } else {
              // If there are no valid collisions, we need to check if the shape is completely inside the collision area
              // This is because the shapes used for the collision detection might not include a pad.
              // It is an edge case but it can happen when the project has issues
              val path = new Path2D.Double()
              collisions.foreach(c => path.append(c.shape, false))

              val pathArea  = new Area(path)
              val shapeArea = new Area(shape)
              val finalArea = new Area(shape)

              finalArea.intersect(pathArea)
              val equals = finalArea.equals(shapeArea)

              equals
            }
          }

        if (plated) {
          val tracesForThisHole =
            collisions.flatMap(gr => traces(gr.index.x)).distinctBy(_.traceID).map(t => t -> gerberFile)

          mutNetLists
            .getOrElseUpdate(drillhit, mutable.Map())
            .put(index, tracesForThisHole)

          onThisLayerCount += 1
        }

        totalCount += 1
    }

    // at least 90% of holes have to be plated on this layer to count
    onThisLayerCount > (totalCount * 0.9)
  }

  private def consolidatedToolRange(tools: Map[ToolInfo, Seq[Tool]]): ToolRange =
    tools.keys.map(_.range).reduce { (x, y) =>
      ToolRange(Math.max(x.from, y.from), Math.min(x.to, y.to))
    }

  private def hasDrills(holeList: HoleListWithFile): Boolean =
    holeList.holeList.tools.exists(_.drills.nonEmpty)

  def reorder(
      holeLists: Seq[HoleListWithFile],
      x: Seq[(GerberFile, FilePath)],
      layerstacks: LayerStack,
      drillsets: Seq[DrillSet]
  ): (Seq[HoleListWithFile], NetList) =
    Kamon.span("Reconciliation.reorder") {
      val coppers        = getCopperLayers(layerstacks)
      val netlistBuilder = new NetListBuilder
      val fileTraces     = mutable.Map[GerberFile, Seq[Trace]]()
      val withToolInfos  = getToolRangesAndTypes(x, holeLists, layerstacks, drillsets, fileTraces)

      val holelists = withToolInfos.map {
        case (namedHl, toolInfos) if toolInfos.isEmpty =>
          val from = namedHl.holeList.from.map(_.intValue)
            .orElse(namedHl.file.flatMap(_.fType.from))
            .map(from => BigDecimal(Math.max(from - 1, 0)))

          val to = namedHl.holeList.to.map(_.intValue)
            .orElse(namedHl.file.flatMap(_.fType.to))
            .map(to => BigDecimal(Math.min(to - 1, coppers.size - 1)))

          namedHl.copy(
            namedHl.holeList.copy(
              from = from,
              to = to
            )
          )

        case (namedHl, toolInfos) =>
          val hl                  = namedHl.holeList
          val info                = toolInfos.groupMap(_._2)(_._1)
          val (plated, nonPlated) = info.partition(_._1.toolType == Tool.PLATED)

          val consolidatedRange: ToolRange =
            if (plated.nonEmpty) {
              consolidatedToolRange(plated)
            } else {
              consolidatedToolRange(nonPlated)
            }

          logger.info(
            s"[RECONCILIATION] Holelist ${namedHl.file} range: ${consolidatedRange}: [${
                Seq(
                  Range.inclusive(0, consolidatedRange.from - 1).map(_ => "-"),
                  Range.inclusive(consolidatedRange.from, consolidatedRange.to).map(_ => "X"),
                  Range.inclusive(consolidatedRange.to + 1, x.length - 1).map(_ => "-")
                ).flatten.mkString(",")
              }]"
          )
          logger.info("\n")

          addNetlist(fileTraces, plated, consolidatedRange, netlistBuilder)

          HoleListWithFile(
            hl.copy(
              from = Some(consolidatedRange.from),
              to = Some(consolidatedRange.to),
              tools = info.flatMap(x =>
                x._2.map { tool =>
                  // only overwrite the actual drill type if we dont know it from the file already
                  if (tool.drillType == Tool.UNKNOWN) {
                    tool.copy(drillType = x._1.toolType)
                  } else {
                    tool
                  }
                }
              ).toSeq
            ),
            file = namedHl.file
          )
      }

      logger.info("[RECONCILIATION] all files done")

      (holelists, NetList(netlistBuilder.result))
    }

  private def addNetlist(
      fileTraces: mutable.Map[GerberFile, Seq[Trace]],
      plated: Map[ToolInfo, Seq[Tool]],
      range: ToolRange,
      builder: NetListBuilder
  ): Unit = {
    val addedTraces = mutable.HashSet[Trace]()
    plated.foreach { platedTool =>
      val (toolInfo, tools) = platedTool
      val toolNetlists      = toolInfo.netlists

      tools.foreach { t =>
        t.drills.foreach { drillhit =>
          val netlistsOfThisDrill = toolNetlists.getOrElse(drillhit, Map())
          val traces = (range.from to range.to).flatMap { index =>
            netlistsOfThisDrill.getOrElse(index, Seq())
          }.distinctBy(_._1.traceID) // TODO Why aren't they distinct already?

          val net = Net(
            traces = traces.map { t =>
              NetTrace(
                t._1.traceID,
                t._1.elements.toSeq.map(id => GraphicElement.elementID(t._2, id.x))
              )
            },
            drills = Seq(
              drillhit.id
            )
          )

          builder.addNet(net)
          traces.foreach(t => addedTraces.add(t._1))
        }
      }
    }

    fileTraces.foreach {
      case (gf, traces) =>
        traces.foreach { trace =>
          if (!addedTraces.contains(trace)) {
            builder.addNet(Net(
              Seq(NetTrace(
                id = trace.traceID,
                elements = trace.elements.map(id => GraphicElement.elementID(gf, id.x)).toSeq
              ))
            ))
          }
        }
    }
    fileTraces.clear()
  }

  /** calculate an identifier for a drill hit.
    * If the identifier is the same, the drills are the same
    */
  private def getDrillIdentifier(t: Tool, point: BigPoint, hl: HoleList): DistinctDrillId =
    DistinctDrillId(
      hl.from,
      hl.to,
      // set the scale to something sensible to remove rounding issues (4 is 0.1 micrometer accuracy)
      (t.diameter / hl.scaling.getOrElse(1)).setScale(4, RoundingMode.HALF_UP),
      (point.x / hl.scaling.getOrElse(1)).setScale(4, RoundingMode.HALF_UP),
      (point.y / hl.scaling.getOrElse(1)).setScale(4, RoundingMode.HALF_UP)
    )

  /** Remove all duplicates from the given hole list, remove empty hole lists
    */
  def distinct(holes: Seq[HoleListWithFile]): Seq[HoleListWithFile] = {
    val runningHoleLists = Seq.newBuilder[HoleListWithFile]
    // keep a running list of points
    val points = mutable.HashSet[String]()

    holes.foreach { namedHolelist =>
      val distinctTools = namedHolelist.holeList.tools.flatMap { t =>
        // keep only drills that do not already exist
        val distinctDrills = t.drills.filter { point =>
          !points.contains(point.id)
        }
        points ++= distinctDrills.map(_.id)

        if (distinctDrills.nonEmpty) {
          Some(t.copy(
            drills = distinctDrills
          ))
        } else {
          None
        }
      }

      val res = namedHolelist.copy(
        holeList = namedHolelist.holeList.copy(
          tools = distinctTools
        )
      )
      runningHoleLists += res
    }

    runningHoleLists.result()
  }

  private def calculateOverlap(outlineBounds: Rectangle2D, holeBounds: Rectangle2D): Double = {
    def area(r: Rectangle2D) =
      r.getWidth * r.getHeight

    if (!outlineBounds.intersects(holeBounds)) {
      0.0
    } else {
      val intersection = area(outlineBounds.createIntersection(holeBounds))

      val unionArea = area(outlineBounds) + area(holeBounds) - intersection
      if (unionArea == 0.0) {
        100.0
      } else {
        (intersection / unionArea) * 100
      }
    }
  }

  /** calculate a score for the given outline and drills
    */
  def scoreFormat(
      outlineGraphic: Graphic,
      reconciledDrills: Map[UnreconciledHoleList, HoleListWithFile],
      debug: Option[(String, UUID, String)] = None
  ): FormatScore = {

    val holeBoundsBuilder = new BoundsBuilder()

    val shapes = reconciledDrills.flatMap { drillinfo =>
      val holelist = drillinfo._2.holeList
      holelist.tools.flatMap { tool =>
        tool.drills.map { point =>
          val g = tool.graphic(point)
          holeBoundsBuilder.extend(g.getBounds2D)
          g
        }
      }
    }.toSeq

    val outlineBounds = outlineGraphic.viewbox.rectangle

    val overlap = calculateOverlap(outlineBounds, holeBoundsBuilder.result())

    val (inOutline, outsideOfOutline) = shapes.partition { s =>
      outlineBounds.intersects(s.getBounds2D)
    }

    if (outsideOfOutline.nonEmpty) {
      // shortcut. if even the bounds of some drills do not intersect with the outline bounds, calculate the
      // drill density with their bounds only. significantly faster, but less accurate.
      FormatScore(100.0 - (outsideOfOutline.size.doubleValue / shapes.size.doubleValue) * 100, 0.0, overlap)
    } else {
      // all drill bounds are at least inside the outline bounds. In this case we draw both and count the overlapping pixels.
      val density =
        DensityCalculator.getDensityByShapes(shapes, outlineGraphic, debug)

      if (density.portionOfCandidateIsCopper.isNaN || density.portionOfCopperIsOutsideCandidate.isNaN) {
        // there was an illegal operation. score is zero
        FormatScore(0.0, 0.0, overlap)
      } else {

        // the score is twofold:
        // 1. how much of the drills are outside of the outline. Higher is worse, so its inverted
        // 2. hom much of the outline is covered in drills. higher is better.
        FormatScore(100.0 - density.portionOfCopperIsOutsideCandidate, density.portionOfCandidateIsCopper, overlap)
      }
    }
  }

  /** Helper function to generate the *all* possible formats for the given lenght of numbers.
    *
    * @return
    */
  def generateAllPossibleFormats(maxLengthNumber: String): Seq[DrillFormat] =
    Range.inclusive(1, maxLengthNumber.length).flatMap { i =>
      val lzOptions     = Seq(true, false)
      val metricOptions = Seq(true, false)
      lzOptions.flatMap { lz =>
        metricOptions.map { metric =>
          DrillFormat(
            intPlaces = i,
            decimalPlaces = maxLengthNumber.length - i,
            includesLeadingZeroes = lz,
            metric = metric
          )
        }
      }
    }

  /** create the eligible formats for the given drills
    */
  def createEligibleFormats(
      drills: Iterable[UnreconciledHoleList],
      names: UnreconciledHoleList => Option[GerberFile] = _ => None
  ): Map[DrillFormat, Map[UnreconciledHoleList, HoleListWithFile]] = {

    val all = drills.flatMap { holes =>
      val allNumbers = holes.tools.flatMap(_.drills.flatMap(h => Seq(h.x, h.y)))

      val allUnscaledNumbers = allNumbers.flatMap(_.operations.flatMap {
        case NumberWithoutScale(number, negative, unit) => Some(number)
        case NumberWithScale(number, unit)              => None
      })

      if (allUnscaledNumbers.nonEmpty) {
        allUnscaledNumbers
      } else {
        // no unscaled numbers. fix length  to 1 since it doesn't really matter.
        // We still want to reconcile the drills however, so we need a format
        Seq("-")
      }
    }.maxByOption(_.length).map { maxLengthNumber =>
      val maxByHints: Int =
        drills.map(h => h.formatHints.intPlaces.getOrElse(0) + h.formatHints.decimalPlaces.getOrElse(0)).max

      // sometimes the hints given in the files are not actually correct, and no number actually fits in the hinted format.
      // sometimes the drills fit the hinted format, but no coordinate in the file "exhausts" the format (i.e. everything is padded in some way),
      // so our generated format is wrong.
      //
      // for that reason we generate formats for both the format given in the hints and the format we found.
      // Ideally (and usually) they are the same.
      Reconciliation.generateAllPossibleFormats(maxLengthNumber) ++
        (if (maxLengthNumber.length != maxByHints) {
           Reconciliation.generateAllPossibleFormats("0" * maxByHints)
         } else {
           Seq()
         })
    }.getOrElse(Seq())

    drills.map(_.formatHints).reduceOption { (a, b) =>
      a merge b
    }.map { hints =>
      all.filter { df =>
        hints.valid(df)
      }
    }.getOrElse(all)
      .map { df =>
        df -> {
          drills.map { unreconciledHoles =>
            unreconciledHoles -> HoleListWithFile(unreconciledHoles.reconcile(df), names(unreconciledHoles))
          }.toMap
        }
      }.toMap
  }
}
