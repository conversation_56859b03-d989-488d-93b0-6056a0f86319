package de.fellows.ems.renderer.impl.pool.odb

import de.fellows.ems.pcb.model.graphics.{Clear, Dark, Polarity}
import de.luminovo.odb.odbpp.model.{ODBLayer, ODBUnit}
import de.luminovo.odb.odbpp.model.constants.{Clockwise, CounterClockwise, Direction, NegativePolarity, PositivePolarity}

import java.nio.file.Path

object ODBRenderUtils {

  import de.luminovo.odb.odbpp

  def convertPolarity(polarity: odbpp.model.constants.Polarity): Polarity =
    polarity match {
      case NegativePolarity => Clear()
      case PositivePolarity => Dark()
    }

  def convertDirection(direction: Direction): Boolean =
    direction match {
      case Clockwise        => true
      case CounterClockwise => false
    }

  def buildUserSymbols(feature: Path, desiredUnit: Option[ODBUnit]): ODBUserSymbolPool = {
    val root = ODBLayer.getRootFromLayer(feature)
    val pool = new ODBUserSymbolPool
    new UserSymbolParser(Option(root.resolve("symbols").toFile.listFiles()).map(_.toSeq).getOrElse(Seq()), pool).parse(desiredUnit)
    pool
  }

  def buildFontPool(feature: Path, desiredUnit: Option[ODBUnit]): ODBFontPool = {
    val root = ODBLayer.getRootFromLayer(feature)
    val pool = new ODBFontPool
    new FontParser(Option(root.resolve("fonts").toFile.listFiles()).map(_.toSeq).getOrElse(Seq()), pool).parse(desiredUnit)
    pool
  }

}
