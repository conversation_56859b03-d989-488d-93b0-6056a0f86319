package de.fellows.ems.renderer.impl.read

import akka.Done
import com.datastax.driver.core.{ BoundStatement, PreparedStatement, Row }
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.cassandra.{ CassandraReadSide, CassandraSession }
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEventTag, PersistentEntityRegistry, ReadSideProcessor }
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.ems.pcb.model.graphics.GPoint
import de.fellows.ems.pcb.model.graphics.tree.Distance
import de.fellows.ems.renderer.api.DistanceDescription
import de.fellows.ems.renderer.impl.entity.distances.{ DistanceAdded, DistancesEvent, DistancesSet }
import de.fellows.utils.communication.ServiceDefinition

import java.util.UUID
import scala.concurrent.{ ExecutionContext, Future }

class GerberInformationRepository(session: CassandraSession)(implicit ec: ExecutionContext, sd: ServiceDefinition) {
  def getDistance(version: UUID, file: String, from: Int, to: Int): Future[Option[DistanceDescription]] =
    session.selectOne(
      "SELECT * FROM distances WHERE version = ? AND file = ? AND fromId = ? AND toId = ? ",
      version,
      file,
      from.asInstanceOf[Integer],
      to.asInstanceOf[Integer]
    ).map(_.map(toDistanceDesc))

  private def toDistance(r: Row): Distance =
    Distance(
      start = GPoint(r.getDouble("fromCoordX"), r.getDouble("fromCoordY")),
      end = GPoint(r.getDouble("toCoordX"), r.getDouble("toCoordY"))
    )

  def optionNeg(i: Int) =
    if (i < 0) {
      None
    } else {
      Some(i)
    }

  private def toDistanceDesc(r: Row): DistanceDescription =
    DistanceDescription(
      optionNeg(r.getInt("fromId")),
      optionNeg(r.getInt("toId")),
      toDistance(r),
      r.getDouble("distance"),
      Option(r.getString("fromTrace")),
      Option(r.getString("toTrace"))
    )

  def getDistances(version: UUID, file: String, from: Int): Future[Seq[DistanceDescription]] =
    session.selectAll(
      "SELECT * FROM distances WHERE version = ? AND file = ? AND fromId = ? ",
      version,
      file,
      from.asInstanceOf[Integer]
    )
      .map(_.map(r => toDistanceDesc(r)))

  def getDistances(version: UUID, file: String): Future[Seq[DistanceDescription]] =
    session.selectAll("SELECT * FROM distances WHERE version = ? AND file = ?", version, file)
      .map(_.map(r => toDistanceDesc(r)))

  def getDistances(version: UUID): Future[Seq[DistanceDescription]] =
    session.selectAll("SELECT * FROM distances WHERE version = ?", version)
      .map(_.map(r => toDistanceDesc(r)))

  def getDistanceLessThan(version: UUID, file: String, limit: Double): Future[Seq[DistanceDescription]] =
    session.selectAll(
      "SELECT * FROM distancesByDistance WHERE version = ? AND file = ? AND distance <= ?",
      version,
      file,
      limit.asInstanceOf[java.lang.Double]
    )
      .map(_.map(r => toDistanceDesc(r)))
}

class GerberInformationProcessor(
    session: CassandraSession,
    readSide: CassandraReadSide,
    eReg: PersistentEntityRegistry
)(implicit ec: ExecutionContext, sd: ServiceDefinition)
    extends ReadSideProcessor[DistancesEvent] {

  var stmtSetDistance: PreparedStatement           = _
  var stmtSetDistanceByDistance: PreparedStatement = _

  def setDistances(event: DistancesSet): Future[Seq[BoundStatement]] =
    Future.successful(event.distance.flatMap { dd =>
      Seq(
        bind(stmtSetDistance, event.version, event.file, dd),
        bind(stmtSetDistanceByDistance, event.version, event.file, dd)
      )
    })

  def addDistance(event: DistanceAdded): Future[Seq[BoundStatement]] = {
    val ddl = event.distance
    Future.successful(ddl.flatMap { dd =>
      Seq(
        bind(stmtSetDistance, event.version, event.file, dd),
        bind(stmtSetDistanceByDistance, event.version, event.file, dd)
      )
    })
  }

  private def bind(preparedStatement: PreparedStatement, version: UUID, file: String, dd: DistanceDescription) =
    preparedStatement.bind()
      .setUUID("version", version)
      .setString("file", file)
      .setInt("fromId", dd.from.getOrElse(-1))
      .setInt("toId", dd.to.getOrElse(-1))
      .setDouble("fromCoordX", dd.distance.start.x)
      .setDouble("fromCoordY", dd.distance.start.y)
      .setDouble("toCoordX", dd.distance.end.x)
      .setDouble("toCoordY", dd.distance.end.y)
      .setDouble("distance", dd.distance.distance)
      .setString("fromTrace", dd.fromTrace.orNull)
      .setString("toTrace", dd.toTrace.orNull)

  def createTables() =
    // language=SQL
    for {
      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS distances (
          |           version uuid,
          |           file text,
          |           fromId int,
          |           toId int,
          |           fromCoordX double,
          |           fromCoordY double,
          |           toCoordX double,
          |           toCoordY double,
          |           fromTrace text,
          |           toTrace text,
          |           distance double,
          |           PRIMARY KEY(version, file, fromId, toId)
          |);
        """.stripMargin
      )
      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS distancesByDistance (
          |           version uuid,
          |           file text,
          |           fromId int,
          |           toId int,
          |           fromCoordX double,
          |           fromCoordY double,
          |           toCoordX double,
          |           toCoordY double,
          |           fromTrace text,
          |           toTrace text,
          |           distance double,
          |           PRIMARY KEY(version, file, distance, fromId, toId)
          |);
        """.stripMargin
      )

    } yield {
      PCBCodecHelper.registerPCBCodecs(session)

      Done
    }

  def prepareStatements(): Future[Done] =
    // language=SQL
    for {

      setDistance <- session.prepare(
        """
          | UPDATE distances SET fromCoordX = :fromCoordX, fromCoordY = :fromCoordY, toCoordX = :toCoordX, toCoordY = :toCoordY, distance = :distance, fromTrace = :fromTrace, toTrace = :toTrace WHERE version = :version AND file = :file AND fromId = :fromId AND toId = :toId
        """.stripMargin
      )
      setDistanceByDistance <- session.prepare(
        """
          | UPDATE distancesByDistance SET fromCoordX = :fromCoordX, fromCoordY = :fromCoordY, toCoordX = :toCoordX, toCoordY = :toCoordY , fromTrace = :fromTrace, toTrace = :toTrace WHERE version = :version AND file = :file AND fromId = :fromId AND toId = :toId AND distance = :distance
        """.stripMargin
      )

    } yield {
      stmtSetDistance = setDistance
      stmtSetDistanceByDistance = setDistanceByDistance
      Done
    }

  override def buildHandler(): ReadSideHandler[DistancesEvent] =
    readSide.builder[DistancesEvent]("renderer-information-v1.1")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[DistancesSet](e => setDistances(e.event))
      .setEventHandler[DistanceAdded](e => addDistance(e.event))
      .build()

  //
  override def aggregateTags: Set[AggregateEventTag[DistancesEvent]] =
    DistancesEvent.Tag.allTags

}
