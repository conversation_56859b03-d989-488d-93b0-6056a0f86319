package de.fellows.ems.renderer.impl.simple

import de.fellows.utils.graphics.PathPrinter

import java.awt.geom.PathIterator

object PathTranslator {

  def translate(pathIterator: PathIterator): Seq[SVGPathInstruction] = {
    val sb = Seq.newBuilder[SVGPathInstruction]
    PathPrinter.walk(
      pathIterator,
      (inst, params) => sb += SVGPathInstruction(inst, params),
      invertY = false
    )

    val result = sb.result()
    result
  }

}
