package de.fellows.ems.renderer.impl.gerber.builders

import de.fellows.ems.pcb.model.graphics.ops.Interpolation
import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, GPoint, GerberApertureDefinition}

import java.awt.Shape
import java.awt.geom._

object Java2DUtils {
  def rectangly(
      ad: GerberApertureDefinition,
      target: GPoint,
      scaling: Int,
      r: (Double, Double, Double, Double) => Shape
  ): Shape = {

    val (xSize: Double, ySize: Double, x: Double, y: Double) = Interpolation.getRectParams(ad.bounds.get, target, scaling)

    val a = r(x, y, xSize, ySize)

    if (ad.args.length > 2) {
      val ar = new Area(a)
      addHole(target, ad.args(2), ar)
      ar
    } else {
      a
    }
  }

  def addHole(target: GPoint, r: String, area: Area): Unit = {
    val radius = BigDecimal(r).doubleValue
    val circle = new Ellipse2D.Double(
      (target.getBX - (radius / 2)).toDouble,
      (target.getBY - (radius / 2)).toDouble,
      radius.doubleValue,
      radius.doubleValue
    )
    area.exclusiveOr(new Area(circle))
  }

}
