package de.fellows.ems.renderer.impl.gerber.macros

import de.fellows.ems.gerber.parser.{ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GerberParserBaseListener }
import de.fellows.ems.pcb.model.graphics.{ Macro, MacroInstruction, PrimitiveUsage, VariableAssignment }

import scala.jdk.CollectionConverters._
import scala.collection.mutable

class MacroWalker extends GerberParserBaseListener {
  var name: Option[String] = None

  val instructions: mutable.Buffer[MacroInstruction] = mutable.Buffer()

  override def enterAm(ctx: GerberParser.AmContext): Unit =
    name = Some(ctx.name.getText)

  override def enterPrim(ctx: GerberParser.PrimContext): Unit =
    instructions += PrimitiveUsage(ctx.primitive.getText.toInt, ctx.primdef().asScala.toSeq.map(_.getText))

  override def enterVardef(ctx: GerberParser.VardefContext): Unit =
    instructions += VariableAssignment(ctx.number().getText.toInt, ctx.arith().getText)

  def getMacro(): Macro =
    Macro(name.get, instructions.toSeq)
}
