package de.fellows.ems.renderer.impl.entity.render

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity
import de.fellows.app.assembly.commons.AssemblyFiles.getLocalResourceFolder
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.GerberFile
import de.fellows.ems.renderer.api.{Render, RenderStatus}
import de.fellows.utils.FilePath
import de.fellows.utils.meta._
import play.api.Logging

import java.time.Instant

class RenderEntity extends PersistentEntity with Logging {
  override type Command = RenderCommand
  override type Event   = RenderEvent
  override type State   = Render

  override def initialState: Render = Render(
    fileID = None,
    graphics = Map(),
    preview = None,
    format = None
  )

  override def behavior: Behavior = {
    Actions()
      .onCommand[AddRender, Done] {
        case (AddRender(ref, f, renders, tree, format), ctx, s) =>
          logger.warn(s"[RENDER ENTITY] add render! ${f.id} ${f.name} ${s.status}")
          val events = Seq[RenderEvent](
            RenderAdded(ref, f, renders, s.graphics, format)
          ) ++ (tree match {
            case Some(t) => Seq(TreeSet(ref, f, t, format))
            case None    => Seq()
          })

          ctx.thenPersistAll(
            events: _*
          )(() => ctx.reply(Done))
      }
      .onCommand[SetPreview, Done] {
        case (SetPreview(ref, f, prev), ctx, s) => ctx.thenPersist(
            PreviewSet(ref, f, prev)
          )(_ => ctx.reply(Done))
      }
      .onCommand[AddDistances, Done] {
        case (AddDistances(ref, file, dst), ctx, s) =>
          ctx.thenPersist(
            DistanceAdded(ref, file, dst)
          )(_ => ctx.reply(Done))
      }
      .onCommand[SetDistances, Done] {
        case (SetDistances(ref, file, dst), ctx, s) =>
          val h      = dst.sliding(10, 10).splitAt(1)
          val events = Seq(h._1.map(DistancesSet(ref, file, _)), h._2.map(DistanceAdded(ref, file, _))).flatten
          ctx.thenPersistAll(events: _*)(() => ctx.reply(Done))
      }
      .onCommand[AddDrills, FilePath] {
        case (AddDrills(ref, f, newDrills, info), ctx, s) =>
          ctx.thenPersist(
            DrillsAdded(ref, f, newDrills, s.drills, info)
          )(_ => ctx.reply(newDrills))
      }
      .onCommand[StartRender, Done] {
        case (StartRender(ref, file), ctx, s) =>
          logger.warn(s"[RENDER ENTITY] start render! ${file.id} ${file.name} ${s.status}")
          if (status(s.status, Seq(RenderStatus.NAME_ANALYZING, RenderStatus.NAME_RENDERING))) {
            ctx.commandFailed(new RenderProgressException(s"already rendering: ${s.status}"))
            ctx.done
          } else {
            ctx.thenPersist(
              StatusChanged(ref, file, RenderStatus.STATUS_RENDERING(Instant.now()))
            )(_ => ctx.reply(Done))
          }
      }
      .onCommand[StopRender, Done] {
        case (StopRender(ref, file, msgs), ctx, s) =>
          logger.warn(s"[RENDER ENTITY] stop render! ${file.id} ${file.name} ${msgs} ${s.status}")
          if (!status(s.status, RenderStatus.NAME_RENDERING) && !status(s.status, RenderStatus.NAME_FAILED)) {
            ctx.commandFailed(new RenderProgressException(s"not rendering: ${s.status}"))
            ctx.done
          } else {
            ctx.thenPersistAll(
              MessagesChanged(ref, file, msgs),
              StatusChanged(ref, file, RenderStatus.STATUS_RENDERED(Instant.now()))
            )(() => ctx.reply(Done))
          }
      }
      .onCommand[RenderError, Done] {
        case (RenderError(ref, file, msgs), ctx, s) =>
          logger.warn(s"[RENDER ENTITY] render error! ${file.id} ${file.name} ${msgs} ${s.status}")
          ctx.thenPersistAll(
            MessagesChanged(ref, file, msgs),
            StatusChanged(ref, file, RenderStatus.STATUS_FAILED(Instant.now()))
          )(() => ctx.reply(Done))
      }
      .onCommand[StartAnalysis, Done] {
        case (StartAnalysis(ref, file), ctx, s) =>
          logger.warn(s"[RENDER ENTITY] start analysis! ${file} ${s.status}")
          if (status(s.status, RenderStatus.NAME_ANALYZING)) {
            ctx.invalidCommand("Already analyzing")
            ctx.done
          } else if (!status(s.status, RenderStatus.NAME_RENDERED)) {
            ctx.invalidCommand(s"Layer is not rendered: ${s.status} ")
            ctx.done
          } else {
            ctx.thenPersist(
              StatusChanged(ref, s.fileID.get, RenderStatus.STATUS_ANALYZING(Instant.now()))
            )(_ => ctx.reply(Done))
          }
      }
      .onCommand[StopAnalysis, Done] {
        case (StopAnalysis(ref, file), ctx, s) =>
          logger.warn(s"[RENDER ENTITY] stopped analysis! ${file} ${s.status}")
          if (!status(s.status, RenderStatus.NAME_ANALYZING)) {
            ctx.invalidCommand(s"not analyzing: ${s.status}")
            ctx.done
          } else {
            ctx.thenPersist(
              StatusChanged(ref, s.fileID.get, RenderStatus.STATUS_READY(Instant.now()))
            )(_ => ctx.reply(Done))
          }
      }
      .onCommand[AddMeta, MetaInfo] {

        case (AddMeta(ref, file, newMeta), ctx, s) =>
          logger.warn(s"[RENDER ENTITY] add meta! ${file.id} ${file.name} ${s.status}")
          val updated = s.meta.getOrElse(newMeta) ++ newMeta
          ctx.thenPersist(
            MetaChanged(ref, s.fileID.get, newMeta, updated)
          )(_ => ctx.reply(updated))
      }
      .onCommand[CloneRender, Done] {
        case (evt: CloneRender, ctx, state) =>
          val newRender = RenderEntity.cloneFromExistingRender(
            state = state,
            assRef = evt.assRef,
            gf = Some(evt.file),
            original = evt.render
          )

          ctx.thenPersist(
            RenderSet(evt.assRef, evt.file, newRender)
          )(_ => ctx.reply(Done))
      }
      .onReadOnlyCommand[GetRender.type, Render] {
        case (GetRender, ctx, s) =>
          ctx.reply(s)
      }
      .onEvent {
        case (RenderAdded(ref, f, n, o, format), s) =>
          s.copy(fileID = Some(f), graphics = n ++ o, format = Some(format))
        case (evt: RenderSet, s)                           => evt.render
        case (StatusChanged(ref, file, status), s)         => s.copy(status = Some(status))
        case (MetaChanged(ref, file, newmeta, updated), s) => s.copy(meta = Some(updated))
        case (MessagesChanged(ref, file, msgs), s)         => s.copy(renderMessages = Some(msgs))
        case (PreviewSet(ref, f, p), s)                    => s.copy(preview = Some(p))
        case (DrillsAdded(ref, f, nr, ar, info), s)        =>
          // TODO mutliple drillsets
          //          val idx = s.drills.indexWhere(_.toPath == nr.toPath)
          //          val updated =
          //            if (idx >= 0) {
          //              s.drills.updated(idx, nr)
          //            } else {
          //              s.drills :+ nr
          //            }

          s.copy(fileID = Some(f), drills = Seq(nr))
        case (TreeSet(ref, f, td, ar), s) => s.copy(tree = Option(td))
        case (DistanceAdded(version, file, distance), s) =>
          s.copy(distances = Some(s.distances.getOrElse(Seq()) ++ distance))
        case (DistancesSet(version, file, distances), s) =>
          s.copy(distances = Some(distances))
      }
  }

  def status(s: Option[RenderStatus], n: String): Boolean =
    status(s, Seq(n))

  def status(s: Option[RenderStatus], col: Seq[String]): Boolean =
    s.map(_.name).forall(col.contains(_))
}

object RenderEntity extends Logging {

  private def cloneFromExistingRender(
      state: Render,
      assRef: AssemblyReference,
      gf: Option[GerberFile],
      original: Render
  ): Render = {
    val resource = getLocalResourceFolder(assRef)

    val newGraphics = original.graphics.map {
      case (str, path) =>
        str -> path.copy(resource = resource)
    }

    val newPreview = original.preview.map(_.copy(resource = resource))
    val newDrills  = original.drills.map(_.copy(resource = resource))
    val newTree    = original.tree.map(_.copy(resource = resource))

    state.copy(
      fileID = gf,
      graphics = newGraphics,
      preview = newPreview,
      format = original.format,
      drills = newDrills,
      tree = newTree,
      distances = original.distances,
      status = original.status,
      renderMessages = None,
      meta = original.meta
    )
  }

}
