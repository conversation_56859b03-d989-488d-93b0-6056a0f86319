package de.fellows.ems.renderer.impl.outline

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.Config
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.graphics.tree.PCBLayerInternalData
import de.fellows.ems.pcb.model.{Dimension, LayerConstants, PCBVersion}
import de.fellows.ems.renderer.api.Render
import de.fellows.ems.renderer.impl.entity.render.{GetRender, RenderEntity}
import de.fellows.ems.renderer.impl.pool.{AbstractTask, HighTaskPriority, TaskContext}
import de.fellows.utils.logging.{StackrateLogger, StackrateLogging}
import de.fellows.utils.telemetry.PropagatingExecutorService

import java.util.concurrent.Executors
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}

/** creates a new outline candidate by using the given dimensions
  */
class OutlineBoxCreatorTask[C <: TaskContext[TransientOutlineCandidateWithDensity]](
    ctx: C,
    newOutlineBox: Dimension,
    pcbv: PCBVersion,
    reg: PersistentEntityRegistry
)(
    implicit val l: StackrateLogger,
    implicit val conf: Config
) extends AbstractTask[TransientOutlineCandidateWithDensity, C](ctx, System.currentTimeMillis(), HighTaskPriority)
    with StackrateLogging {
  lazy val basicExecutor              = new PropagatingExecutorService(Executors.newCachedThreadPool())
  implicit val ectx: ExecutionContext = ExecutionContext.fromExecutor(basicExecutor)

  override def getTeam(): Option[String] = pcbv.assembly.map(_.team)

  override def getAssembly(): Option[AssemblyReference] = pcbv.assembly

  override def getExtraInfo(): Seq[(String, Any)] = Seq()

  override def description: String = ""

  override def doRun(): Unit = {
    val result = Future.sequence(pcbv.files
      .filter(gf => LayerConstants.COPPER.contains(gf.fType.fileType))
      .map(gf =>
        reg.refFor[RenderEntity](Render.fileID(pcbv.assembly.get, gf)).ask(GetRender)
          .map(_.tree)
      )).map(_.flatten)
      .map { treesPaths =>
        val trees =
          treesPaths.flatMap { tree =>
            PCBLayerInternalData.load(tree).toSeq
          }

        trees.flatMap(_.format).headOption.map { format =>
          val scaledBox = newOutlineBox * format.scaling.getOrElse(1.0)
          val candidate = OutlineCandidateBuilder.createOutlineCandidateForBounds(format, scaledBox.rectangle)
          val graphics  = trees.flatMap(_.tree.collect(g => Seq(g)))
          val density   = DensityCalculator.getDensityByGraphics(graphics, candidate.graphic, None)

          TransientOutlineCandidateWithDensity(candidate, density)
        }

      }

    val outline = Await.result(result, 1 minute)

    outline.foreach(ctx.setResult)
  }
}
