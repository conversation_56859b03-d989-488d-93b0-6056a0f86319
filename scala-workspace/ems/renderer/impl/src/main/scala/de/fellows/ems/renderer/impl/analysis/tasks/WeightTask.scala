package de.fellows.ems.renderer.impl.analysis.tasks

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.layerstack.api._
import de.fellows.ems.pcb.api.specification.units.{AreaWithUnit, DensityWithUnit, LengthWithUnit, VolumeWithUnit, WeightUnit, WeightWithUnit}
import de.fellows.ems.pcb.model.DFM.Properties.{DFM, Settings}
import de.fellows.ems.pcb.model._
import de.fellows.ems.renderer.impl.analysis.StepResult
import de.fellows.ems.renderer.impl.pool.{AbstractTask, MediumTaskPriority, SimpleTaskContext}
import de.fellows.utils.logging.StackrateLogger
import de.fellows.utils.meta._

import java.util.UUID
import scala.concurrent.Future

class WeightTaskContext extends SimpleTaskContext[StepResult]

class WeightTask(
    assRef: AssemblyReference,
    ctx: WeightTaskContext,
    stack: SubStack,
    outlineMeta: () => MetaInfo,
    pcbMeta: () => MetaInfo,
    layerMetas: Map[UUID, MetaInfo],
)(implicit logger: StackrateLogger)
    extends AbstractTask[StepResult, WeightTaskContext](ctx, System.currentTimeMillis(), MediumTaskPriority)
    with Analyzer {
  override def getTeam(): Option[String] = Option(assRef.team)

  override def getAssembly(): Option[AssemblyReference] = Some(assRef)

  override def getExtraInfo(): Seq[(String, Any)] = Seq()

  override def description: String = "Calculate weight"

  def decimalProp(meta: Option[MetaInfo], p: String) =
    (meta.flatMap(_ \ p): Option[Property]).flatMap(_.decimalValue)

  override def doRun(): Unit = {

    val outlineArea = outlineMeta().get[DecimalProperty](DFM.AREA).map(_.value).map(AreaWithUnit.sqmm)

    val outerCopperThickness =
      pcbMeta().get[DecimalProperty](DFM.OUTER_COPPER_HEIGHT).map(_.value).map(LengthWithUnit.micrometer)
    val innerCopperThickness =
      pcbMeta().get[DecimalProperty](DFM.INNER_COPPER_HEIGHT).map(_.value).map(LengthWithUnit.micrometer)
    val fallbackSolderMaskThickness = LengthWithUnit.micrometer(BigDecimal(18))

    logger.info(s"[WEIGHT] analyze layers ${stack.layers.map(_.definition.layerType)}")

    val innerLayers = stack.layers.filter { l =>
      Seq(MaterialTypes.CORE, MaterialTypes.FLEXCORE, MaterialTypes.FOIL).exists(t =>
        l.definition.layerType.contains(t)
      )
    }.flatMap { l =>
      if (Seq(MaterialTypes.CORE, MaterialTypes.FLEXCORE).exists(t => l.definition.layerType.contains(t))) {
        // duplicate core layers because there are 2 copper layers. this is just for ease of use in the next step
        Seq(l, l)
      } else {
        Seq(l)
      }
    }
      .drop(1)      //
      .dropRight(1) // since we duplicated the cores, we can just drop one on either end and retain the inner layers
      .toSet        // dedup

    val finalThickness = pcbMeta().get[DecimalProperty](Settings.FINAL_THICKNESS).map(_.value).map(LengthWithUnit.mm)
    val allCopperThickess = stack.layers.flatMap(l =>
      l.definition.layerType match {
        case Some(MaterialTypes.CORE) | Some(MaterialTypes.FLEXCORE) =>
          if (innerLayers.contains(l)) {
            innerCopperThickness.map(_ * 2)
          } else {
            outerCopperThickness.map(_ * 2)
          }
        case Some(MaterialTypes.FOIL) =>
          if (innerLayers.contains(l)) {
            innerCopperThickness
          } else {
            outerCopperThickness
          }
        case _ => None
      }
    ).reduce(_ + _)

    val soldermaskThickness = stack.layers.flatMap { l =>
      l.definition.layerType match {
        case Some(MaterialTypes.SOLDERMASK) =>
          decimalProp(l.definition.material.flatMap(_.meta), MaterialProperties.MaskThickness).map(
            LengthWithUnit.micrometer
          ).orElse(Some(fallbackSolderMaskThickness))
        case _ => None
      }
    }.reduce(_ + _)

    val allPrepregThickness =
      finalThickness.map(_ - allCopperThickess - soldermaskThickness)

    val copperWeights: Seq[WeightWithUnit] = stack.layers.flatMap { layer =>
      val y = layer.definition.material.toSeq.flatMap { material =>
        material.materialType match {

          case Some(MaterialTypes.FOIL) =>
            val w = for {
              thickness <- decimalProp(material.meta, MaterialProperties.CuThickness)
                .map(LengthWithUnit.micrometer)
                .orElse({
                  if (innerLayers.contains(layer)) {
                    innerCopperThickness
                  } else {
                    outerCopperThickness
                  }
                })
              area <- layer.definition.id.flatMap(id =>
                layerMetas.get(id).flatMap(_.get[DecimalProperty](LayerProperties.CUArea).map(_.value)).map(
                  AreaWithUnit.sqmm
                )
              )
                .orElse(outlineArea)
            } yield materialWeight(thickness, DensityWithUnit.kgPerCubicMeter(8960), area)

            w.toSeq

          case Some(MaterialTypes.CORE) | Some(MaterialTypes.FLEXCORE) =>
            val copperThickness =
              if (innerLayers.contains(layer)) {
                innerCopperThickness
              } else {
                outerCopperThickness
              }

            val w = for {
              copperThicknessTop <-
                decimalProp(material.meta, MaterialProperties.UpperCuThickness).map(LengthWithUnit.micrometer).orElse(
                  copperThickness
                )

              copperThicknessBottom <-
                decimalProp(material.meta, MaterialProperties.LowerCuThickness).map(LengthWithUnit.micrometer).orElse(
                  copperThickness
                )

              copperAreaTop <- layer.definition.id.flatMap(id =>
                layerMetas.get(id).flatMap(_.get[DecimalProperty](LayerProperties.UpperCUArea).map(_.value)).map(
                  AreaWithUnit.sqmm
                )
              )
              copperAreaBottom <- layer.definition.id.flatMap(id =>
                layerMetas.get(id).flatMap(_.get[DecimalProperty](LayerProperties.LowerCUArea).map(_.value)).map(
                  AreaWithUnit.sqmm
                )
              )
            } yield {
              val topCopperWeight =
                materialWeight(copperThicknessTop, DensityWithUnit.kgPerCubicMeter(8960), copperAreaTop)
              val bottomCopperWeight =
                materialWeight(copperThicknessBottom, DensityWithUnit.kgPerCubicMeter(8960), copperAreaBottom)

              logger.info(s"[WEIGHT] core weight: ${topCopperWeight}, ${bottomCopperWeight}")
              Seq(topCopperWeight, bottomCopperWeight)
            }

            w.getOrElse(Seq())

          case _ => Seq()
        }
      }

      y
    }

    val prepregWeight = for {
      area      <- outlineArea
      thickness <- allPrepregThickness
    } yield materialWeight(thickness, DensityWithUnit.kgPerCubicMeter(1850), area)

    val soldermaskWeight =
      outlineArea.map { area =>
        materialWeight(soldermaskThickness, DensityWithUnit.kgPerCubicMeter(1900), area)
      }

    val weights = copperWeights ++ prepregWeight ++ soldermaskWeight

    var stackMetaMap = Map(): Map[String, Property]

    if (weights.nonEmpty) {
      val stackWeight = weights.reduce((a, b) => a + b)
      stackMetaMap = stackMetaMap + DecimalProperty.e(LayerstackDFM.WEIGHT, stackWeight.to(WeightUnit.Gram))

      val stackWeightWithoutCU = (prepregWeight ++ soldermaskWeight).reduce((a, b) => a + b)
      stackMetaMap =
        stackMetaMap + DecimalProperty.e(LayerstackDFM.WEIGHT_WITHOUT_CU, stackWeightWithoutCU.to(WeightUnit.Gram))

      val stackWeightCU = copperWeights.reduce((a, b) => a + b)
      stackMetaMap = stackMetaMap + DecimalProperty.e(LayerstackDFM.WEIGHT_CU, stackWeightCU.to(WeightUnit.Gram))
    }

    ctx.setResult(
      StepResult(
        pcbMeta = MetaInfo(stackMetaMap), // put stackmeta to pcbmeta as well
        stackMeta = MetaInfo(stackMetaMap)
      )
    )
  }

  private def materialWeight(
      thickness: LengthWithUnit,
      density: DensityWithUnit,
      area: AreaWithUnit
  ): WeightWithUnit = {

    val volume: VolumeWithUnit = area ** thickness
    volume ** density
  }
}
