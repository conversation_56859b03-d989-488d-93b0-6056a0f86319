package de.fellows.ems.renderer.impl.pool

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.graphics.tree.PCBLayerInternalData
import de.fellows.ems.pcb.model.{GerberFile, Graphic}
import de.fellows.ems.renderer.api.Render
import de.fellows.ems.renderer.impl.entity.distances.Distances
import de.fellows.ems.renderer.impl.entity.tracewidth.TraceWidths
import de.fellows.utils.internal.FileReader
import de.fellows.utils.logging.StackrateLogger
import play.api.libs.json.JsSuccess

import scala.util.Success

class TemplatedRenderTask[C <: RenderContext](
    val assRef: AssemblyReference,
    val template: AssemblyReference,
    val gf: GerberFile,
    val templategf: Render,
    val templatedist: Distances,
    val templatetrace: TraceWidths,
    override val ctx: C
)(implicit logger: StackrateLogger) extends AbstractTask[Any, C](ctx, System.currentTimeMillis(), HighTaskPriority) {

  override def getTeam(): Option[String] = Some(assRef.team)

  override def getAssembly(): Option[AssemblyReference] = Some(assRef)

  def getExtraInfo(): Seq[(String, Any)] =
    RendererCoordinator.fileMetrics(gf)

  override def description: String =
    s"${assRef.gid.getOrElse("")} | ${gf.name} | ${getClass.getSimpleName}"

  override def doRun(): Unit = {
    // copy renders
    val tree = PCBLayerInternalData.load(templategf.tree.get)
    ctx.start(assRef, Some(gf))

    templategf.graphics.foreach { r =>
      FileReader.json[Graphic](r._2) match {
        case Success(JsSuccess(value, path)) => ctx.persist(assRef, gf, value.viewbox, tree.get, r._1, value, None)
        case _                               =>
      }
    }

    templategf.meta.foreach { meta =>
      ctx.persistMeta(assRef, gf, meta)
    }

    ctx.persistDistances(assRef, gf, templatedist.distances.toList)

    ctx.persistTraceWidth(assRef, gf, templatetrace.widths.toList)

    ctx.stop(assRef, Some(gf))
  }
}
