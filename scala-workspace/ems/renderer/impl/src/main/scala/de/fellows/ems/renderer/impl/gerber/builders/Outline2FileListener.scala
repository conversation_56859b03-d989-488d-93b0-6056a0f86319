package de.fellows.ems.renderer.impl.gerber.builders

import de.fellows.ems.gerber.parser.GerberParser
import de.fellows.ems.pcb.model.Dimension
import de.fellows.ems.pcb.model.graphics.Paths.FunctionalPathIterator
import de.fellows.ems.pcb.model.graphics.ops.Movement
import de.fellows.ems.pcb.model.graphics.tree.QuadTree
import de.fellows.ems.pcb.model.graphics.{
  ChainedCollision<PERSON>hecker,
  Close,
  <PERSON>lision<PERSON>hecker,
  CubicTo,
  GPoint,
  LineTo,
  MoveTo,
  PathSegment,
  Polarity,
  QuadTo
}
import de.fellows.ems.renderer.impl.gerber.GerberExceptions._
import de.fellows.ems.renderer.impl.gerber.graphic.ApertureUtils
import de.fellows.ems.renderer.impl.gerber.interpolation.awt.Interpolator
import de.fellows.ems.renderer.impl.gerber.{ApertureRegistry, Linear}
import de.fellows.ems.renderer.impl.simple.SimpleApertureStore
import de.fellows.utils.DebugUtils
import org.antlr.v4.runtime.ParserRuleContext
import play.api.Logging

import java.awt.geom.{Area, Path2D}
import scala.collection.mutable.ListBuffer
import scala.util.control.NonFatal

class Outline2FileListener(dim: Dimension) extends BasicFileListener with Outliner with Logging {

  override def polarityChanged(from: Polarity, to: Polarity): Unit = {}

  var complexity = 0

  val lineTree = new QuadTree[LineDescription](
    min = GPoint(dim.min.x.doubleValue - 1000, dim.min.y.doubleValue - 1000),
    max = GPoint(dim.max.x.doubleValue + 1000, dim.max.y.doubleValue + 1000),
    maxPerBox = 5,
    maxDepth = Some(8)
  )

  val apertureStore = new SimpleApertureStore()

  override def apReg: ApertureRegistry = apertureStore

  override def enterEveryRule(ctx: ParserRuleContext): Unit = {
    super.enterEveryRule(ctx)
    complexity += 1
  }

  override def closeStepRepeat(implicit ctx: ParserRuleContext): Unit = {}

  override def endRegion(): Unit = {}

  override def flash(target: GPoint)(implicit v: ParserRuleContext): Unit = {}

  override def doInterpolate(point: GPoint, i: Option[Double], j: Option[Double])(implicit
      v: GerberParser.OpContext
  ): Unit = {
    if (gContext.point.isEmpty) {
      "no current point selected" !
    }

    val from: GPoint        = gContext.point.get
    val target: GPoint      = createFrom.getOrElse("Point is missing" !)
    var rel: Option[GPoint] = None
    if (v.i != null || v.j != null) {
      val i: Double = Option(v.i).map(x => gContext.format.get.toX(x.getText)).getOrElse(0)
      val j: Double = Option(v.j).map(y => gContext.format.get.toY(y.getText)).getOrElse(0)
      rel = Some(GPoint(i, j))
    }

    feather(from, target, rel)
  }

  private def feather(from: GPoint, to: GPoint, rel: Option[GPoint])(implicit ctx: ParserRuleContext): Unit =
    gContext.aperture match {
      case None => throw "Aperture needed" !
      case Some(a) =>
        val diameter  = ApertureUtils.getNonZeroDecimal(a.args.head, scaling).get.doubleValue
        val movingXUp = to.x - from.x
        val movingYUp = to.y - from.y

        this.lineTree.insert(
          LineDescription(
            idx = gContext.getAndIncrementIndex,
            from = from,
            to = to,
            rel = rel,
            diameter = diameter / 2,
            aperture = gContext.aperture.get,
            quadrant = this.gContext.quadrant,
            interpolation = this.gContext.interpolation,
            movement = Movement(movingXUp, movingYUp)
          )
        )
    }

  private val collisionChecker = new CollisionChecker[LineDescription, LineDescription] {
    override def collides(one: LineDescription, two: LineDescription): Boolean =
      one.intersects(two)
  }

  private val collisionCheckerSameAperture = new ChainedCollisionChecker(collisionChecker) {
    override def doCollides(one: LineDescription, two: LineDescription): Boolean =
      one.aperture.dCode == two.aperture.dCode
  }

  private def createOutlineArea(
      tree: QuadTree[LineDescription],
      collisionChecker: CollisionChecker[LineDescription, LineDescription]
  ): Seq[OutlineDescription] = {
    implicit val ctx: ParserRuleContext = new ParserRuleContext()

    var startPoint: GPoint = null
    var endPoint: GPoint   = null

    def isSameSlope(slopeA: Double, slopeB: Double): Boolean =
      if (slopeA.isInfinite && slopeB.isInfinite) {
        true
      } else if (slopeA.isInfinite || slopeB.isInfinite) {
        false
      } else {
        slopeA == slopeB
      }

    def mergePathSegments(path: Path2D): (Vector[PathSegment], Boolean) =
      mergeSegments(path.getPathIterator(null).toSeq())

    def mergeSegments(segments: Seq[PathSegment]): (Vector[PathSegment], Boolean) = {
      val newLine = segments
        .foldLeft(List.empty[PathSegment]) {
          case (Nil, segment) => segment :: Nil

          case ((head: LineTo) :: tail, line: LineTo) =>
            mergeTwo(head, line) match {
              case Some(merged) => merged :: tail
              case None         => line :: head :: tail
            }

          case (acc, segment) => segment :: acc
        }
        .reverse
        .toVector

      val changed = segments.size != newLine.size

      (newLine, changed)
    }

    def mergeTwo(line1: LineTo, line2: LineTo): Option[LineTo] = {
      val line1Slope = (line1.toY - line1.fromY) / (line1.toX - line1.fromX)
      val lineSlope  = (line2.toY - line2.fromY) / (line2.toX - line2.fromX)

      // If the slope is infinite, we have a straight vertical line
      // which is only mergeable if the x values are the same and
      // if there's some overlap in the y values (even if just one point)
      if (line1Slope.isInfinite && lineSlope.isInfinite) {
        val sameXCoordinate = line1.toX == line2.toX

        val minLine1Y = Math.min(line1.fromY, line1.toY)
        val maxLine1Y = Math.max(line1.fromY, line1.toY)

        val minLine2Y = Math.min(line2.fromY, line2.toY)
        val maxLine2Y = Math.max(line2.fromY, line2.toY)

        // check if it starts between the first line or at the end of it
        val overlapMin = minLine2Y >= minLine1Y && minLine2Y <= maxLine1Y
        val overlapMax = maxLine2Y >= minLine1Y && maxLine2Y <= maxLine1Y

        if (sameXCoordinate) {
          if (overlapMin && overlapMax) {
            Some(line1)
          } else if (overlapMin) {
            Some(line1.copy(toY = maxLine2Y))
          } else if (overlapMax) {
            Some(line1.copy(toY = minLine2Y))
          } else {
            None
          }
        } else {
          None
        }
      } else if (line1Slope.isInfinite || lineSlope.isInfinite) {
        // can't merge if lines are different slopes
        None
      } else {
        None
      }
    }

    def createNewPathFrom(segments: Vector[PathSegment]): Path2D = {
      val newPath = new Path2D.Double()

      segments.foreach {
        case m: MoveTo  => newPath.moveTo(m.x, m.y)
        case l: LineTo  => newPath.lineTo(l.toX, l.toY)
        case s: CubicTo => newPath.curveTo(s.cp1X, s.cp1Y, s.cp2X, s.cp2Y, s.cp3X, s.cp3Y)
        case s: QuadTo  => newPath.quadTo(s.cp1X, s.cp1Y, s.cp2X, s.cp2Y)
        case s: Close   => newPath.closePath()
      }

      newPath
    }

    def collisionsFor(
        tree: QuadTree[LineDescription],
        path: OutlineDescription,
        line: LineDescription
    ): Unit = {
      val collisions = tree.collideWithCheckerOrBoundsIter(
        check = line,
        envelope = line.bounds,
        startNode = tree.getRoot(),
        pop = true,
        collisionChecker = collisionChecker
      )

      collisions
        .distinct
        .foreach { nextSegment =>
          val checkDia = nextSegment.diameter.max(line.diameter) / 2
          val short =
            (startPoint.distance(nextSegment.from) <= checkDia && startPoint.distance(nextSegment.to) <= checkDia) ||
              (endPoint.distance(nextSegment.from) <= checkDia && endPoint.distance(nextSegment.to) <= checkDia)

          val same = nextSegment.same(line)

          // This check is supposed to catch some simpler cases of lines that are drawn inside other lines
          // and can be skipped. This should also catch cases where the next segment extends just a little bit.
          // These cases can be dropped in the "normal" path extending logic below, but they are still relevant
          // in some cases if for some reason we have a lot of small segments that overlap but still extend the
          // outline path
          val (isContained, extended) =
            if (nextSegment.interpolation.contains(Linear())) {

              val (mergedSegments, _) = mergePathSegments(path.path)
              val nextSegmentSlope = (nextSegment.to.y - nextSegment.from.y) / (nextSegment.to.x - nextSegment.from.x)

              // if the next segment is contained in the path we already have, we don't need to add it
              // but it can be the case that the next segment is not fully contained in a single line, but rather
              // two (or more) lines. To try and get around that, we "merge" lines that are smaller segments to
              // avoid keeping too much state around
              val isContained = mergedSegments.exists {
                case l: LineTo =>
                  val lineSlope = (l.toY - l.fromY) / (l.toX - l.fromX)
                  val sameSlope = isSameSlope(nextSegmentSlope, lineSlope)

                  if (sameSlope) {
                    val minX = Math.min(nextSegment.from.x, nextSegment.to.x)
                    val minY = Math.min(nextSegment.from.y, nextSegment.to.y)
                    val maxX = Math.max(nextSegment.from.x, nextSegment.to.x)
                    val maxY = Math.max(nextSegment.from.y, nextSegment.to.y)

                    val lineMinX = Math.min(l.fromX, l.toX)
                    val lineMinY = Math.min(l.fromY, l.toY)
                    val lineMaxX = Math.max(l.fromX, l.toX)
                    val lineMaxY = Math.max(l.fromY, l.toY)

                    lineMinX <= minX && lineMinY <= minY && lineMaxX >= maxX && lineMaxY >= maxY
                  } else {
                    false
                  }

                case _ => false
              }

              if (isContained) {
                (isContained, None)
              } else {
                // Otherwise, the next segment is not contained, but it can still overlap with the current path,
                // while extending it a little bit. We can try to merge the next segment with the first or last
                // line of the current path, if it's a LineTo command. Other cases are more complex
                // and potentially handled by the "normal" merging logic

                // find the first line that's not a MoveTo command, if it's a LineTo
                val head = mergedSegments.find(s => !s.isInstanceOf[MoveTo]).collect { case s: LineTo => s }
                // and the last command, if it's a LineTo
                val tail = mergedSegments.lastOption.collect { case s: LineTo if !head.contains(s) => s }

                val segmentLine = LineTo(nextSegment.from.x, nextSegment.from.y, nextSegment.to.x, nextSegment.to.y, 1)

                val merged = head.flatMap { h =>
                  mergeTwo(segmentLine, h) match {
                    case Some(merged) =>
                      val newSegments     = Vector(MoveTo(merged.fromX, merged.fromY, 1), merged)
                      val updatedSegments = newSegments ++ mergedSegments.dropWhile(_ != h).drop(1)
                      val newStartPoint   = GPoint(merged.fromX, merged.fromY)

                      Some((updatedSegments, Some(newStartPoint), None))

                    case None => None
                  }
                }.orElse {
                  tail.flatMap { t =>
                    mergeTwo(t, segmentLine) match {
                      case Some(merged) =>
                        val updatedSegments = mergedSegments.dropRight(1) :+ merged
                        val newEndPoint     = GPoint(merged.toX, merged.toY)

                        Some((updatedSegments, None, Some(newEndPoint)))

                      case None => None
                    }
                  }
                }

                (isContained, merged)
              }
            } else {
              (false, None)
            }

          if (short || same || isContained) {
            // skipping
          } else {
            extended match {
              case Some((extendedSegments, newStartPoint, newEndPoint)) =>
                val newPath = createNewPathFrom(extendedSegments)
                path.path = newPath

                newStartPoint.foreach { p =>
                  startPoint = p
                }

                newEndPoint.foreach { p =>
                  endPoint = p
                }

              case _ =>
                // check distance with double the diameter to catch border cases where a lot of small sgments intersect
                if (endPoint.distance(nextSegment.from) > line.diameter * 2) {
                  // next segments does not start at current point
                  if (endPoint.distance(nextSegment.to) > line.diameter * 2) {
                    // next segment does not end at current point
                    if (startPoint.distance(nextSegment.from) > line.diameter * 2) {
                      // next Segment does not start from the current start point
                      if (startPoint.distance(nextSegment.to) > line.diameter * 2) {
                        // next segment does not end at current start point, all options are exhausted
                      } else {
                        // next segment ends at current start point: prepend, set current start to start of next segment
                        val newPath = Interpolator(
                          quad = nextSegment.quadrant,
                          interpolate = nextSegment.interpolation,
                          from = nextSegment.from,
                          to = nextSegment.to,
                          relativePoint = nextSegment.rel,
                          ad = null,
                          movement = nextSegment.movement,
                          scaling = scaling
                        ).getLine(false)

                        startPoint = nextSegment.from
                        path.prepend(newPath)
                      }

                    } else {
                      // next segment starts from current start: prepend reversed, set current start to the end of next segment (because it is reversed)
                      val newPath = Interpolator(
                        quad = nextSegment.quadrant,
                        interpolate = nextSegment.interpolation,
                        from = nextSegment.from,
                        to = nextSegment.to,
                        relativePoint = nextSegment.rel,
                        ad = null,
                        movement = nextSegment.movement,
                        scaling = scaling
                      ).getLine(true)

                      startPoint = nextSegment.to
                      path.prepend(newPath)
                    }
                  } else {
                    // next segment ends at current point: append reversed
                    val newPath = Interpolator(
                      quad = nextSegment.quadrant,
                      interpolate = nextSegment.interpolation,
                      from = nextSegment.from,
                      to = nextSegment.to,
                      relativePoint = nextSegment.rel,
                      ad = null,
                      movement = nextSegment.movement,
                      scaling = scaling
                    ).getLine(true)

                    path.append(newPath)
                    endPoint = nextSegment.from
                  }
                } else {
                  // next segment starts at current point: append, no reverse
                  val newPath = Interpolator(
                    quad = nextSegment.quadrant,
                    interpolate = nextSegment.interpolation,
                    from = nextSegment.from,
                    to = nextSegment.to,
                    relativePoint = nextSegment.rel,
                    ad = null,
                    movement = nextSegment.movement,
                    scaling = scaling
                  ).getLine(false)

                  path.append(newPath)
                  endPoint = nextSegment.to
                }
            }

            val (mergedSegments, changed) = mergePathSegments(path.path)
            if (changed) {
              val newPath = createNewPathFrom(mergedSegments)
              path.path = newPath
            }

            collisionsFor(
              tree = tree,
              path = path,
              line = nextSegment
            )
          }
        }
    }

    val res = tree
      .head()
      .toSeq
      .map { line =>
        val interpolator = Interpolator(
          quad = line.quadrant,
          interpolate = line.interpolation,
          from = line.from,
          to = line.to,
          relativePoint = line.rel,
          ad = null,
          movement = line.movement,
          scaling = scaling
        )
        val newPath       = interpolator.getLine(false)
        val boundsBuilder = new BoundsBuilder().extend(newPath.getBounds2D)

        val od = OutlineDescription(
          path = newPath,
          segmentCount = 1,
          boundsBuilder = boundsBuilder,
          scaling = gContext.format.get.getImageScaling.intValue,
          intialAperture = line.aperture
        )

        startPoint = line.from
        endPoint = line.to

        collisionsFor(tree, od, line)

        od
      }

    res
  }

  def getOutlines(): Seq[OutlineDescription] = {
    val outlines = ListBuffer[OutlineDescription]()

    DebugUtils.timed("create outlines") {
      try {
        val tree             = this.lineTree
        val treeSameAperture = tree.copy()

        // TODO this will duplicate most outlines
        while (tree.nonEmpty)
          outlines.addAll(createOutlineArea(tree, collisionChecker))

        while (treeSameAperture.nonEmpty)
          outlines.addAll(createOutlineArea(treeSameAperture, collisionCheckerSameAperture))

      } catch {
        case e: StackOverflowError => logger.error("StackOverflow while drawing outline", e)
        case NonFatal(e)           => logger.error("error", e)
      }
    }(logger.logger)

    outlines.toSeq
  }

}
