package de.fellows.ems.renderer.impl.gerber.interpolation.awt

import de.fellows.ems.pcb.model.graphics.ops.{Interpolation, Movement}
import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, GPoint, Geometry, GerberApertureDefinition, Rectangle}
import de.fellows.ems.renderer.impl.gerber.GerberExceptions._
import de.fellows.ems.renderer.impl.gerber.graphic.ApertureUtils
import org.antlr.v4.runtime.ParserRuleContext

import java.awt.Shape
import java.awt.geom.Path2D

case class LinearInterpolator[X <: ApertureDefinition](from: GPoint, to: GPoint, ad: X, movement: Movement, scaling: Int)(
    implicit ctx: ParserRuleContext
) extends Interpolator[X] {

  override def getArea(implicit creator: InterpolationBoundsCreator[X]): Shape = {

    val radians = Geometry.calculateRadians(from, to, movement.moveXUp)

    val rec = creator.rec(from, to, (radians, radians), ad, movement, scaling)
    Interpolator.createRecArea(rec)

  }



  override def getLine(reverse: Boolean): Path2D =
    if (from == to) {
      val path = new Path2D.Double()
      path.moveTo(from.x, from.y)
      path
    } else {
      val path = new Path2D.Double()
      if (reverse) {
        path.moveTo(to.getX, to.getY)
        path.lineTo(from.getX, from.getY)
      } else {
        path.moveTo(from.getX, from.getY)
        path.lineTo(to.getX, to.getY)
      }

      path
    }

}
