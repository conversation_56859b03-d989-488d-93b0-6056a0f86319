package de.fellows.ems.renderer.impl.analysis.tasks

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.LayerConstants.{COPPER_BOTTOM, COPPER_MID, COPPER_TOP, PLANE_MID, SOLDERMASK_BOTTOM, SOLDERMASK_TOP}
import de.fellows.ems.pcb.model.graphics.tree.PCBLayerInternalData
import de.fellows.ems.pcb.model.{Format, GerberFile, LayerConstants}
import de.fellows.ems.renderer.impl.analysis.GerberLayerAnalyzer
import de.fellows.ems.renderer.impl.pool.{AbstractTask, MediumTaskPriority, RenderContext, RendererCoordinator}
import de.fellows.utils.logging.StackrateLogger

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext}

class ClearanceAnalysisTask(
    val assRef: AssemblyReference,
    val gf: GerberFile,
    override val ctx: RenderContext,
    val ld: () => PCBLayerInternalData,
    val format: Format
)(implicit logger: StackrateLogger)
    extends AbstractTask[Any, RenderContext](ctx, System.currentTimeMillis(), MediumTaskPriority)
    with Analyzer {
  override def getTeam(): Option[String] = Some(assRef.team)

  override def getAssembly(): Option[AssemblyReference] = Some(assRef)

  def getExtraInfo(): Seq[(String, Any)] =
    RendererCoordinator.fileMetrics(gf)

  override def description: String =
    s"${assRef.gid.getOrElse("")} | ${gf.name} | ${getClass.getSimpleName}"

  override def doRun(): Unit =
    try
      _doRun
    catch {
      case e: Throwable =>
        ctx.sink.error(None, e.getLocalizedMessage, e)
        throw e
    }

  private def _doRun = {
    if (shouldExtractClearance(gf)) {
      state = "analyzing distances"
      val res = {
        // val ectx = ExecutionContext.fromExecutor(this.worker.get)

        Await.result(
          new GerberLayerAnalyzer(
            ld(),
            format,
            ExecutionContext.fromExecutor(worker.get),
            ctx.timeout,
            ignoreTraces = LayerConstants.isSolderMask(gf)
          ).analyzeDistancesParallel(),
          10 minutes
        )
      }

      val sortedDistances = res.map(_._3).sortBy(_.length)
      state = "persisting distances"
      ctx.persistDistances(assRef, gf, sortedDistances.slice(0, 100).toList)
    }

    state = "done"
  }


  private def shouldExtractClearance(gfile: GerberFile) =
    Seq(COPPER_MID, COPPER_TOP, COPPER_BOTTOM, PLANE_MID, SOLDERMASK_BOTTOM, SOLDERMASK_TOP).contains(gfile.fType.fileType)
}
