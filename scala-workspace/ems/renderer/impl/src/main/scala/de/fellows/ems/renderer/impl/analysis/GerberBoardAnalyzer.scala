package de.fellows.ems.renderer.impl.analysis

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import copper.DebugImageDescriptor
import copper.ExposedCopperCalculator.getExposedCopper
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, AssemblyService}
import de.fellows.ems.layerstack.api._
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.specification.PCBV2SpecificationApi.PCBV2Properties
import de.fellows.ems.pcb.api.specification.units.LengthUnit.Millimeter
import de.fellows.ems.pcb.api.specification.{PCBV2SpecificationApi, Side, SurfaceFinish}
import de.fellows.ems.pcb.api.{PCBService, PCBV2Layer}
import de.fellows.ems.pcb.model.DFM.Properties.{DFM, Settings}
import de.fellows.ems.pcb.model._
import de.fellows.ems.pcb.model.graphics.tree.PCBLayerInternalData
import de.fellows.ems.pcb.model.graphics.{Close, CubicTo, Geometry, LineTo, MoveTo, QuadTo}
import de.fellows.ems.renderer.api.Render
import de.fellows.ems.renderer.impl.analysis.tasks.{
  CopperDensityAnalyzerTask,
  CopperDensityContext,
  WeightTask,
  WeightTaskContext
}
import de.fellows.ems.renderer.impl.entity.annularring._
import de.fellows.ems.renderer.impl.entity.distances.{Distances, DistancesEntity, GetDistances}
import de.fellows.ems.renderer.impl.entity.render.{GetRender, RenderEntity}
import de.fellows.ems.renderer.impl.pool.RendererCoordinator
import de.fellows.ems.renderer.impl.progress.RenderProgressSink
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.ems.renderer.impl.{PathGenerator, RendererHelper}
import de.fellows.utils.internal.{FileReader, LifecycleStageStatus, StageStatusName}
import de.fellows.utils.logging.StackrateLogger
import de.fellows.utils.meta._
import de.fellows.utils.telemetry.{KamonUtils, PropagatingExecutorService, Telemetry}
import de.fellows.utils.{FilePath, FutureUtils, SerializedCompressedFile}
import kamon.Kamon
import play.api.libs.json.JsSuccess

import java.awt.Color
import java.awt.image.BufferedImage
import java.nio.file.Paths
import java.util.UUID
import java.util.concurrent.Executors
import javax.imageio.ImageIO
import scala.collection.mutable
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor, Future}
import scala.math.BigDecimal.RoundingMode
import scala.util.Success
import de.fellows.ems.pcb.api.specification.units.AreaWithUnit

class FileCache {

  private val graphics = mutable.HashMap[FilePath, Graphic]()
  def graphic(path: FilePath): Graphic =
    graphics.getOrElseUpdate(path, GraphicUtils.readFile(path).get)
}

case class DrillMetaData(
    phmin: Option[BigDecimal],
    phmax: Option[BigDecimal],
    nphmin: Option[BigDecimal],
    nphmax: Option[BigDecimal],
    phtoolcount: BigDecimal,
    nphtoolcount: BigDecimal,
    phcount: BigDecimal,
    nphcount: BigDecimal
)

/** @param pcbMeta
  * general meta info for the pcb
  * @param fileMetas
  * meta info for single files (key is the file id)
  * @param layerMetas
  * meta info for layers (key is the layerdef id)
  * @param stackMeta
  * meta info for stacks
  */
case class StepResult(
    pcbMeta: MetaInfo = MetaInfo(),
    fileMetas: Map[UUID, MetaInfo] = Map(),
    layerMetas: Map[UUID, MetaInfo] = Map(),
    stackMeta: MetaInfo = MetaInfo()
) {
  def merge[T](m1: Map[T, MetaInfo], m2: Map[T, MetaInfo]): Map[T, MetaInfo] = {
    val merged  = m1.toSeq ++ m2.toSeq
    val grouped = merged.groupBy(_._1)
    grouped.map { x =>
      (x._1 -> MetaInfo(x._2.map(_._2.properties).reduceOption(_ ++ _).getOrElse(Map())))
    }
  }

  def ++(other: StepResult): StepResult = {
    val mergedFileMeta  = merge(fileMetas, other.fileMetas)
    val mergedLayerMeta = merge(layerMetas, other.layerMetas)

    StepResult(
      pcbMeta = pcbMeta ++ other.pcbMeta,
      fileMetas = mergedFileMeta,
      layerMetas = mergedLayerMeta,
      stackMeta = stackMeta ++ other.stackMeta
    )
  }
}

class GerberBoardAnalyzer(
    assRef: AssemblyReference,
    pcbservice: PCBService,
    assService: AssemblyService,
    stackService: LayerstackService,
    layerstacks: LayerStacks,
    reg: PersistentEntityRegistry,
    pcb: PCBVersion
)(implicit logger: StackrateLogger) extends Runnable {
  val worker = new PropagatingExecutorService(Executors.newWorkStealingPool(Runtime.getRuntime.availableProcessors))

  val files = new FileCache

  implicit val analysisContext: ExecutionContextExecutor = ExecutionContext.fromExecutor(worker)

  def copyMetaInfo(from: PCBVersion, tov: PCBVersion, toa: AssemblyReference): Future[Done] = {
    val f1 = FutureUtils.option(from.meta.map(tmeta =>
      pcbservice._setMetaInfoProperty(toa.team, toa.id, toa.version).invoke(tmeta.properties.values.toSeq)
    )).map(_ => Done)

    val f2 = Future.sequence(from.files.flatMap(f =>
      f.metaInfo.map { fmeta =>
        pcbservice._setFileMetaInfoProperty(toa.team, toa.id, toa.version, f.id).invoke(fmeta.properties.values.toSeq)
      }
    ))

    Future.sequence(Seq(f1, f2)).map(_ => Done)
  }

  override def run(): Unit =
    Kamon.span(s"Analyze Board ${assRef.id}${assRef.gid.map(s => s" (${s})").getOrElse("")}", getClass.getSimpleName) {
      Await.result(doRun(), 30 minutes)
    }

  private def doRun() = {
    println(s"ANALYSIS STARTING FOR ${assRef.version}")

    (for {
      _ass <- assService._getAssembly(assRef.team, assRef.id).invoke().map(_.assembly)
    } yield {
      logger.info(s"ANALYSIS STARTED FOR ${assRef.version}")
      if (_ass.template.isEmpty) {
        Kamon.span("analyzeBoards") {
          try
            pcbservice._getSpecification(assRef.team, assRef.id, assRef.version, pcb.specifications.head).invoke()
              .flatMap { spec =>
                analyzeBoards(spec.headOption)
              }
          catch {
            case e: Throwable => Future.failed(e)
          }
        }
      } else {
        val tmplt = _ass.template.get
        analyzeBoardByTemplate(tmplt)
      }
    }).flatten
      .flatMap { d =>
        assService._updateVersionLifecycle(
          assRef.team,
          assRef.id,
          Some(assRef.version),
          AssemblyLifecycleStageName.Analysis.value,
          Some(System.currentTimeMillis())
        ).invoke(LifecycleStageStatus.emptySuccess).map { _ =>
          Telemetry.trackAnonymous(
            "pcb_analysis_done",
            Map(
              "pcb_id" -> assRef.version,
              "tenant" -> assRef.team
            )
          )
          logger.info(s"ANALYSIS DONE (REALLY) FOR ${assRef.version}")

          d
        }

      }
      .recoverWith {
        case e: Throwable =>
          logger.error(e.getMessage, e)
          assService._updateVersionLifecycle(
            assRef.team,
            assRef.id,
            Some(assRef.version),
            AssemblyLifecycleStageName.Analysis.value,
            Some(System.currentTimeMillis())
          ).invoke(LifecycleStageStatus(StageStatusName.Error, Seq(e.getLocalizedMessage), None)).map(_ =>
            Done
          )
      }

  }

  private def analyzeBoardByTemplate(tmplt: AssemblyReference) =
    for {
      tmpv <- pcbservice._getPCBVersion(tmplt.team, tmplt.id, tmplt.version).invoke()
      spec <- pcbservice._getSpecifications(tmplt.team, tmplt.id, tmplt.version).invoke()
      ls   <- doPressLayerstack(MetaInfo(), spec.headOption)
    } yield Future.sequence(Seq(
      FutureUtils.option(ls.flatMap { sr =>
        mergeStackprops(sr)
      }.map(s =>
        stackService._updateStack(assRef.team, assRef.version).invoke(s)
      )).map(_ => Done),
      copyMetaInfo(tmpv, this.pcb, assRef)
    ))

  private def getHoles(): Seq[HoleList] =
    pcb.holes.flatMap { df =>
      FileReader.json[HoleList](df.holes) match {
        case Success(JsSuccess(x, _)) => Some(x)
        case _                        => None
      }
    } ++ readReconciledHoles.map(_._2)

  private lazy val readReconciledHoles: Seq[(SerializedCompressedFile[HoleList], HoleList)] =
    pcb
      .reconciledHoles
      .flatMap(r => r.holes.read().toOption.map(c => (r.holes, c)))

  def analyzeDrills(): Seq[Property] =
    Kamon.span("analyzeDrills") {
      val holes = getHoles()
      GerberBoardAnalyzer.getHoleProperties(holes)
    }

  private def pressLayerStack(
      defaultMetaInfo: MetaInfo,
      specification: Option[PCBSpecification]
  ): Future[Option[StepResult]] = {
    val pressedResult = layerstacks.selected match {
      case Some(ls) =>
        ls.stacks.headOption match {
          case Some(stack) =>
            pcb.outline match {
              case Some(outline) =>
                val ctx = new CopperDensityContext()
                val cdt = new CopperDensityAnalyzerTask(
                  assRef,
                  ctx,
                  stack,
                  () => files.graphic(outline.path),
                  () => outline.metaInfo,
                  (gf, tps) => {

                    val render = reg.refFor[RenderEntity](Render.fileID(assRef, gf))
                    render.ask(GetRender).map { re =>
                      tps.find(re.graphics.contains).flatMap { tp =>
                        re.graphics.get(tp).flatMap(GraphicUtils.readFile)
                      }
                    }
                  }
                )

                RendererCoordinator.submitTask(cdt)
                cdt.result.recover {
                  case e: NullPointerException =>
                    e.printStackTrace()
                    None
                }
              case _ => Future.successful(None)
            }
          case None => Future.successful(None)
        }
      case None => Future.successful(None)
    }

    for {
      pressed <- pressedResult
      weight  <- calculateWeight(pressed.getOrElse(StepResult()), defaultMetaInfo, specification)
    } yield Some(pressed.getOrElse(StepResult()) ++ weight.getOrElse(StepResult()))
  }

  def calculateWeight(
      pressedStepResult: StepResult,
      defaultMetaInfo: MetaInfo,
      specification: Option[PCBSpecification]
  ): Future[Option[StepResult]] =
    layerstacks.selected match {
      case Some(ls) =>
        ls.stacks.headOption match {
          case Some(stack) =>
            pcb.outline match {
              case Some(outline) =>
                val ctx = new WeightTaskContext()
                val cdt = new WeightTask(
                  assRef,
                  ctx,
                  stack,
                  () => outline.metaInfo,
                  () =>
                    defaultMetaInfo ++ // do not overwrite the existing things with the defaults
                      pcb.meta.getOrElse(MetaInfo()) ++
                      specification.map(_.settings).getOrElse(MetaInfo()),
                  pressedStepResult.layerMetas
                )

                RendererCoordinator.submitTask(cdt)
                cdt.result.recover {
                  case e: NullPointerException =>
                    e.printStackTrace()
                    None
                }
              case _ => Future.successful(None)
            }
          case None => Future.successful(None)
        }
      case None => Future.successful(None)
    }

  def annularRing(): Future[Option[StepResult]] =
    layerstacks.selected match {
      case Some(ls) =>
        ls.stacks.headOption match {
          case Some(stack) =>
            val sink = new RenderProgressSink(assRef, "")

            val tasks = new AnnularRingStackAnalyzer(
              assRef,
              stack,
              (gf) =>
                Await.result(
                  reg.refFor[RenderEntity](Render.fileID(assRef, gf)).ask(GetRender).map { r =>
                    PCBLayerInternalData.load(r.tree.getOrElse(RendererHelper.quadTreePath(gf))).get
                  },
                  10 minutes
                ),
              RendererCoordinator
            ).analyzeHoles { () =>
              getHoles()
            }

            Future.sequence(
              tasks.map { t =>
                Future {
                  t.waitFor()
                  val rings = t.ctx.asInstanceOf[AnnularRingContext].result

                  rings.map { r =>
                    val fileMeta: Map[UUID, MetaInfo] =
                      GerberBoardAnalyzer.getAnnularRingsMetaData(r)

                    persistRings(r).map(_ => fileMeta)
                  }.getOrElse(Future.successful(Map()))

                }.flatten
              }
            ).map(_.flatten)
              .map { listOfMetaDataPerFile =>
                GerberBoardAnalyzer.getAnnularRingsResult(listOfMetaDataPerFile)
              }
          case None => Future.successful(None)
        }
      case None => Future.successful(None)
    }

  private def persistRings(rings: Seq[AnnularRing]): Future[Done] = {
    val entity = reg.refFor[AnnularRingsEntity](AnnularRings.id(assRef.version))
    val ref    = assRef.version
    val cmds = (rings.sliding(10, 10).toSeq) match {
      case x if x.size > 1 =>
        val h = x.splitAt(1)
        Seq(h._1.map(SetAnnularRings(ref, _)), h._2.map(AddAnnularRings(ref, _))).flatten
      case x if x.size == 1 =>
        Seq(SetAnnularRings(ref, x.head))
      case _ =>
        Seq()
    }

    Future.sequence(cmds.map(c => entity.ask(c))).map(_ => Done)
  }

  private def defaultProperties(
      capabilities: PCBV2SpecificationApi.PCBV2SpecificationCapabilities,
      layerCount: Option[BigDecimal]
  ): PCBV2Properties = {
    val props: PCBV2Properties = capabilities.defaultProperties
    props.copy(layerStack =
      props.layerStack.copy(
        layercount = layerCount
      )
    )
  }

  def analyzeBoards(specification: Option[PCBSpecification]): Future[Done] = {

    val copperFilesCount = pcb.files.count(f => LayerConstants.COPPER.contains(f.fType.fileType))
    val copperCountProp  = DecimalProperty(DFM.COPPER_FILES_COUNT, copperFilesCount)
    val syncProps        = analyzeDrills() :+ copperCountProp

    val boardArea     = pcb.meta.flatMap(_.get[DecimalProperty](DFM.AREA)).map(sqmm => AreaWithUnit.sqmm(sqmm.value))
    val layercount    = pcb.meta.flatMap(_.get[DecimalProperty](DFM.LAYERCOUNT)).map(_.value)
    val surfaceFinish = pcb.meta.flatMap(_.resolve(Settings.FINISH, SurfaceFinish.res))
    val defaultProps = defaultProperties(
      PCBV2Layer.defaultCapabilities(
        numberOfLayers = layercount.map(_.intValue),
        surfaceFinish = surfaceFinish
      ),
      layercount
    )
    val defaultMetaInfo = PCBV2Layer.from(defaultProps, layercount)

    val t = Future.sequence(Seq(
      doDrillTypes.recover { x =>
        logger.error("doDrillTypes Error: " + x.getMessage)
        None
      },
      doCalculateSpecificationFromFiles.recover { x =>
        logger.error("doCalculateSpecificationFromFiles Error: " + x.getMessage)
        None
      },
      doAnnularRing.recover { x =>
        logger.error("doAnnularRing Error: " + x.getMessage)
        None
      },
      doPressLayerstack(defaultMetaInfo, specification).recover { x =>
        logger.error("doPressLayerstack Error: " + x.getMessage)
        None
      },
      doCalculateOutlineMillingLength.recover { x =>
        logger.error("doCalculateOutlineMillingLength Error: " + x.getMessage)
        None
      },
      doClearance.recover { x =>
        logger.error("Clearance Error: " + x.getMessage)
        None
      },
      doSoldermaskDam.recover { x =>
        logger.error("Soldermask Dam Error: " + x.getMessage)
        None
      },
      boardArea.map(x =>
        doCalculateExposedCopper(x).recover { x =>
          logger.error("doCalculateExposedCopper Error: " + x.getMessage)
          None
        }
      ).getOrElse(Future.successful(None)),
      doCalculateHoleSurfaceArea(defaultProps).recover { x =>
        logger.error("doCalculateHoleSurfaceArea Error: " + x.getMessage)
        None
      }
    )).map(_.flatten)

    t.flatMap { asyncProps =>
      val asyncMetaResult = asyncProps.reduce(_ ++ _)
      val unconsolidated =
        asyncMetaResult.copy(pcbMeta = MetaInfo(asyncMetaResult.pcbMeta.properties ++ syncProps.map(_.e)))

      val metaresult = doConsolidateMetaData(unconsolidated)
      val stack      = mergeStackprops(metaresult)
      KamonUtils.span("set result metadata") {
        for {
          _ <-
            pcbservice._setMetaInfoProperty(assRef.team, assRef.id, assRef.version, overwrite = Some(false)).invoke(
              defaultMetaInfo.properties.values.toSeq
            )
          a <- pcbservice._setMetaInfoProperty(assRef.team, assRef.id, assRef.version).invoke(
            metaresult.pcbMeta.properties.values.toSeq ++ metaresult.stackMeta.properties.values.toSeq
          )
          b <- Future.sequence(metaresult.fileMetas.map { x =>
            pcbservice._setFileMetaInfoProperty(assRef.team, assRef.id, assRef.version, x._1).invoke(
              x._2.properties.values.toSeq
            )
          })
          c <- FutureUtils.option(stack.map(s =>
            stackService._updateStack(assRef.team, assRef.version).invoke(s)
          ))

        } yield {

          logger.info(s" done updating metas $a $b $c")

          Done
        }
      }

    }
  }

  private def mergeStackprops(metaresult: StepResult): Option[LayerStack] =
    layerstacks.selected.map(ls =>
      ls.copy(
        stacks = ls.stacks.map { ss =>
          ss.copy(
            layers = ss.layers.map(l =>
              l.copy(
                metaInfo = Some(l.metaInfo.getOrElse(MetaInfo()) ++ metaresult.layerMetas.getOrElse(
                  l.definition.id.get,
                  MetaInfo()
                ))
              )
            ),
            metaInfo = Some(ss.metaInfo.getOrElse(MetaInfo()) ++ metaresult.stackMeta)
          )
        }
      )
    )

  def doDrillTypes: Future[Option[StepResult]] =
    Kamon.span("doDrillTypes") {
      val layercount =
        pcb.meta.flatMap(_.get[DecimalProperty](DFM.LAYERCOUNT)).map(_.value.intValue).getOrElse(0)

      val holeLists      = readReconciledHoles.map(_._2)
      val holeTypeResult = getHoleTypeProperties(layercount, holeLists)

      getHoleCoverage(readReconciledHoles).map { r =>
        Some(r.getOrElse(StepResult()) ++ holeTypeResult)
      }
    }

  private def getHoleCoverage(holes: Seq[(SerializedCompressedFile[HoleList], HoleList)]) =
    FutureUtils.option(layerstacks.selected.map { ls =>
      val layers               = ls.stacks.head.layers
      val soldermasks          = layers.filter(l => l.definition.layerType.contains(MaterialTypes.SOLDERMASK))
      val soldermaskTop        = soldermasks.headOption
      val soldermaskBottom     = if (soldermasks.length > 1) soldermasks.lastOption else None
      val soldermaskTopFile    = soldermaskTop.flatMap(sm => sm.files).flatMap(_.headOption)
      val soldermaskBottomFile = soldermaskBottom.flatMap(sm => sm.files).flatMap(_.headOption)

      for {
        soldermaskTopData <- FutureUtils.option(soldermaskTopFile.map { gf =>
          reg.refFor[RenderEntity](Render.fileID(assRef, gf)).ask(GetRender).map { r =>
            PCBLayerInternalData.load(r.tree.getOrElse(RendererHelper.quadTreePath(gf))).get
          }
        })
        soldermaskBotData <- FutureUtils.option(soldermaskBottomFile.map { gf =>
          reg.refFor[RenderEntity](Render.fileID(assRef, gf)).ask(GetRender).map { r =>
            PCBLayerInternalData.load(r.tree.getOrElse(RendererHelper.quadTreePath(gf))).get
          }
        })
      } yield {
        val analyzedHoleList = holes.map { h =>
          val analyzedHoles = GerberBoardAnalyzer.calculateHoleCoverage(h._2, soldermaskTopData, soldermaskBotData)
          h._1.write(analyzedHoles)
          analyzedHoles
        }

        val allCoveragesByToolType: Map[String, Seq[Side]] = analyzedHoleList.flatMap { list =>
          val groupedDrills   = list.tools.groupBy(_.drillType)
          val platedDrills    = groupedDrills.getOrElse(Tool.PLATED, Seq.empty)
          val nonPlatedDrills = groupedDrills.getOrElse(Tool.NON_PLATED, Seq.empty)

          Map(
            Tool.PLATED     -> coverageToSide(platedDrills),
            Tool.NON_PLATED -> coverageToSide(nonPlatedDrills)
          )
        }.groupBy(_._1).map(x => x._1 -> x._2.flatMap(_._2))

        val props = allCoveragesByToolType.flatMap { x =>
          val (drillType, sides)     = x
          val groups: Map[Side, Int] = sides.groupBy(identity).map(x => x._1 -> x._2.length)

          drillType match {
            case Tool.PLATED =>
              Seq(
                DecimalProperty.e(DFM.PH_UNCOVERED, groups.getOrElse(Side.None, 0): Int),
                DecimalProperty.e(DFM.PH_COVERED_BOTH_SIDES, groups.getOrElse(Side.Both, 0): Int),
                DecimalProperty.e(
                  DFM.PH_COVERED_ONE_SIDE,
                  groups.getOrElse(Side.Top, 0) + groups.getOrElse(Side.Bottom, 0)
                )
              )

            case Tool.NON_PLATED =>
              Seq(
                DecimalProperty.e(DFM.NPH_UNCOVERED, groups.getOrElse(Side.None, 0): Int),
                DecimalProperty.e(DFM.NPH_COVERED_BOTH_SIDES, groups.getOrElse(Side.Both, 0): Int),
                DecimalProperty.e(
                  DFM.NPH_COVERED_ONE_SIDE,
                  groups.getOrElse(Side.Top, 0) + groups.getOrElse(Side.Bottom, 0)
                )
              )
            case _ => Seq()
          }

        }

        StepResult(pcbMeta = MetaInfo(props))
      }
    })

  private def coverageToSide(platedDrills: Seq[Tool]): Seq[Side] =
    platedDrills.flatMap(_.drills.flatMap(_.coverage.map { c =>
      (c.soldermaskBottom.getOrElse(false), c.soldermaskTop.getOrElse(false)) match {
        case (true, true)   => Side.Both
        case (true, false)  => Side.Top
        case (false, true)  => Side.Bottom
        case (false, false) => Side.None
      }
    }))

  private def getHoleTypeProperties(layercount: Int, holes: Seq[HoleList]): StepResult = {
    def countHoles(hl: Seq[HoleList]): Int = hl.map(_.tools.flatMap(_.drills).length).sum

    // its a through hole if it appears on both the top and bottom layer
    val through = countHoles(holes.filter(_.isThroughHole(layercount)))
    // its a blind hole if it appears on only the top or the bottom layer (xor)
    val blind = countHoles(holes.filter(_.isBlind(layercount)))
    // its buried if it appears neither on the top or bottom layer
    val buried = countHoles(holes.filter(_.isBuried(layercount)))

    val allHoleCount = holes.flatMap(_.tools.flatMap(_.drills)).length
    val drillDensity = pcb.meta.flatMap(_.get[DecimalProperty](DFM.AREA)).map { x =>
      val qdm = x.value / 10000
      DecimalProperty.e(DFM.DRILL_DENSITY, allHoleCount / qdm)
    }

    StepResult(
      pcbMeta = MetaInfo(Seq(
        Some(DecimalProperty.e(DFM.BURIED_VIA_COUNT, buried)),
        Some(DecimalProperty.e(DFM.BLIND_VIA_COUNT, blind)),
        Some(DecimalProperty.e(DFM.THROUGH_HOLE_COUNT, blind)),
        Some(BooleanProperty.e(DFM.BURIED_VIAS, buried > 0)),
        Some(BooleanProperty.e(DFM.BLIND_VIAS, blind > 0)),
        Some(BooleanProperty.e(DFM.THROUGH_HOLES, through > 0)),
        drillDensity
      ).flatten.toMap)
    )
  }

  /** calculate some specification settings from the given filetypes
    *
    * @return
    */
  private def doCalculateSpecificationFromFiles: Future[Option[StepResult]] = {

    val maskTop    = pcb.files.exists(_.fType.fileType == LayerConstants.SOLDERMASK_TOP)
    val maskBottom = pcb.files.exists(_.fType.fileType == LayerConstants.SOLDERMASK_BOTTOM)

    val pasteTop    = pcb.files.exists(_.fType.fileType == LayerConstants.PASTE_TOP)
    val pasteBottom = pcb.files.exists(_.fType.fileType == LayerConstants.PASTE_BOTTOM)

    val screenTop    = pcb.files.exists(_.fType.fileType == LayerConstants.SILKSCREEN_TOP)
    val screenBottom = pcb.files.exists(_.fType.fileType == LayerConstants.SILKSCREEN_BOTTOM)

    def getSides(top: Boolean, bottom: Boolean, fallback: Option[Option[Side]] = None): Option[Side] =
      (top, bottom) match {
        case (true, true)   => Some(Side.Both)
        case (false, true)  => Some(Side.Bottom)
        case (true, false)  => Some(Side.Top)
        case (false, false) => fallback.getOrElse(Some(Side.None))
      }

    val props = Seq(
      getSides(maskTop, maskBottom).flatMap(Side.res.to).map(StringProperty.e(Settings.SOLDERMASK_SIDES, _)),
      getSides(pasteTop, pasteBottom).flatMap(Side.res.to).map(StringProperty.e(Settings.PASTE_SIDES, _)),
      getSides(pasteTop, pasteBottom, Some(None)).flatMap(Side.res.to).map(StringProperty.e(
        Settings.PLACEMENT_SIDES,
        _
      )),
      getSides(screenTop, screenBottom).flatMap(Side.res.to).map(StringProperty.e(Settings.SILKSCREEN_SIDES, _))
    ).flatten.toMap

    Future.successful(Some(StepResult(
      pcbMeta = MetaInfo(
        props
      )
    )))

  }

  private def doAnnularRing: Future[Option[StepResult]] =
    Kamon.span("doAnnularRing") {
      try
        annularRing()
      catch {
        case e: Throwable =>
          logger.error(e.getMessage, e)
          Future.successful(None)
      }
    }

  private def doSoldermaskDam: Future[Option[StepResult]] =
    Kamon.span("doSoldermaskDam") {
      combineDistances(LayerConstants.isSolderMask, DFM.SOLDERMASK_DAM, persistPerFile = false)
    }

  private def doClearance: Future[Option[StepResult]] =
    Kamon.span("doClearance") {
      combineDistances(LayerConstants.isCopper, DFM.CLEARANCE, persistPerFile = true)
    }

  private def combineDistances(fileFilter: GerberFile => Boolean, propertyToWrite: String, persistPerFile: Boolean) =
    for {
      alldistances <-
        Future.sequence(pcb.files
          .filter(fileFilter)
          .map { gf =>
            reg.refFor[DistancesEntity](Distances.id(assRef.version, gf.name)).ask(GetDistances(
              assRef.version,
              gf.name
            ))
              .map { d =>
                (gf -> d)
              }
          })
      netlist <- Future.successful(pcb.netlist.flatMap(_.read().toOption))
    } yield {
      val mindistancesWithFile = alldistances.map { d =>
        val notSameTrace = d._2.distances.filter { dd =>
          (dd.fromTrace, dd.toTrace, netlist) match {
            case (Some(a), Some(b), Some(netlist)) =>
              !netlist.findNet(a).toSeq.flatMap(_.traces.map(_.id)).contains(b)
            case _ => true
          }
        }

        d._1 -> notSameTrace.minByOption(_.distance.distance)
      }

      val perFile = mindistancesWithFile.filter(_._2.isDefined).map { x =>
        val gf       = x._1
        val distance = x._2.get

        val scale = gf.format.flatMap(_.scaling).getOrElse(100.0)
        gf -> DecimalProperty(
          propertyToWrite,
          BigDecimal(distance.distance.distance / scale).setScale(4, RoundingMode.HALF_UP)
        )
      }

      val pcbMeta =
        MetaInfo(
          perFile.map(_._2).minByOption(_.value).toSeq.map(x => x.name -> x).toMap
        )

      val fileMeta = perFile.map { x =>
        x._1.id -> MetaInfo(Map(
          x._2.name -> x._2
        ))
      }.toMap

      Some(
        StepResult(
          pcbMeta = pcbMeta,
          fileMetas =
            if (persistPerFile) {
              fileMeta
            } else {
              Map()
            },
          layerMetas = Map(),
          stackMeta = MetaInfo()
        )
      )
    }

  private def doCalculateOutlineMillingLength: Future[Option[StepResult]] =
    Kamon.span("doCalculateOutlineMillingLength") {
      import de.fellows.ems.pcb.model.graphics.Paths._
      val length = this.pcb.outline.map(o => files.graphic(o.path)).map { olgr =>
        val scaling = BigDecimal(olgr.format.scaling.getOrElse(1.0))
        olgr.paths.map(groups =>
          groups
            .flatMap { element =>
              element.path.toSeq.flatMap { x =>
                PathGenerator
                  .parse(x)
                  .getPathIterator(null, 100)
                  .map {
                    case x: LineTo  => x.length
                    case x: QuadTo  => 0.0 // a flattened path has no curves.
                    case x: CubicTo => 0.0 // ^
                    case x: Close   => x.length
                    case x: MoveTo  => 0.0

                  }
              }
            }.sum / scaling
        ).max
      }

      Future.successful(
        length.map { l =>
          StepResult(
            pcbMeta = MetaInfo(Map(
              DecimalProperty.e(DFM.OUTLINE_LENGTH, l) // DFM.OUTLINE_MILLING_LENGTH ->
            ))
          )
        }
      )
    }

  private def doPressLayerstack(
      defaultMetaInfo: MetaInfo,
      specification: Option[PCBSpecification]
  ): Future[Option[StepResult]] =
    Kamon.span("pressLayerstack") {
      try
        pressLayerStack(defaultMetaInfo, specification)
      catch {
        case e: Throwable =>
          logger.error(e.getMessage, e)
          Future.successful(None)
      }
    }

  private def min(a: Option[BigDecimal], b: Option[BigDecimal]): Option[BigDecimal] =
    (a, b) match {
      case (Some(_a), Some(_b)) => Some(_a.min(_b))
      case (None, Some(_b))     => Some(_b)
      case (Some(_a), None)     => Some(_a)
      case _                    => None
    }

  private def doConsolidateMetaData(analysisResult: StepResult): StepResult =
    Kamon.span("consolidateMetaData") {

      analysisResult ++
        layerstacks.selected.flatMap(s => s.stacks.headOption)
          .map { s =>
            GerberBoardAnalyzer.consolidateMetaDataByStack(analysisResult, s, pcb.files)
          }
          .getOrElse(StepResult())

    }

  private def doCalculateHoleSurfaceArea(default: PCBV2Properties): Future[Option[StepResult]] =
    Kamon.span("calculateHoleSurfaceArea") {
      val holes = pcb.reconciledHoles.flatMap(_.holes.read().toOption)

      val platedDrills = holes.flatMap { list =>
        list.tools.filter(_.drillType == Tool.PLATED)
      }

      val analysis = PCBV2Layer.to(pcb.meta.getOrElse(MetaInfo()))
      logger.info(s"calculateHoleSurfaceArea start")

      pcb.defaultSpecification.map { specId =>
        logger.info(s"calculateHoleSurfaceArea for spec: ${specId}")
        pcbservice._getPCBV2Specification(assRef.team, assRef.version, specId.toString).invoke().map { spec =>
          val finalThickness = analysis.layerStack.finalThickness
            .orElse(spec.settings.layerStack.finalThickness)
            .orElse(default.layerStack.finalThickness)
          logger.info(s"calculateHoleSurfaceArea for final thickness: ${finalThickness}")
          finalThickness.map { finalThickness =>
            val summedSurfaceArea = platedDrills.map { tool =>
              val diameterUm = tool.diameter
              val diameterMm = diameterUm / 100
              logger.info(
                s"calculateHoleSurfaceArea hole surface for tool ${tool.name} with diameter $diameterMm and thickness ${finalThickness}"
              )
              val surfaceArea = Geometry.surfaceAreaOfHole(diameterMm, finalThickness.to(Millimeter))
              tool.drills.size * surfaceArea
            }.sum

            StepResult(
              pcbMeta = MetaInfo(Map(
                DecimalProperty.e(DFM.PH_SURFACE_AREA, summedSurfaceArea)
              ))
            )
          }
        }
      }.getOrElse(Future.successful(None))

    }

  private def doCalculateExposedCopper(boardArea: AreaWithUnit): Future[Option[StepResult]] =
    Kamon.span("calculateExposedCopper") {
      try {
        val copperFiles = pcb.files.filter(f => LayerConstants.COPPER.contains(f.fType.fileType))
        val soldermask = pcb.files.filter(f =>
          f.fType.fileType == LayerConstants.SOLDERMASK_TOP || f.fType.fileType == LayerConstants.SOLDERMASK_BOTTOM
        )

        val copperFilesWithGraphics =
          Future.traverse(copperFiles) { f =>
            val render = reg.refFor[RenderEntity](Render.fileID(assRef, f))
            render.ask(GetRender).map { reply =>
              val graphic = Seq(RenderConstants.COPPER_JSON, RenderConstants.LAYER_JSON)
                .find(reply.graphics.contains)
                .flatMap { tp =>
                  reply.graphics.get(tp).flatMap(GraphicUtils.readFile)
                }

              f -> graphic
            }
          }

        val soldermaskGraphics = Future.traverse(soldermask) { f =>
          val render = reg.refFor[RenderEntity](Render.fileID(assRef, f))
          render.ask(GetRender).map { reply =>
            val graphic = Seq(RenderConstants.COPPER_JSON, RenderConstants.LAYER_JSON)
              .find(reply.graphics.contains)
              .flatMap { tp =>
                reply.graphics.get(tp).flatMap(GraphicUtils.readFile)
              }

            f -> graphic
          }
        }

        for {
          copperFilesWithGraphics <- copperFilesWithGraphics
          soldermaskGraphics      <- soldermaskGraphics
        } yield {

          val exposedCopperTop =
            soldermaskGraphics
              .find(_._1.fType.fileType == LayerConstants.SOLDERMASK_TOP)
              .flatMap {
                case (_, Some(soldermaskGraphic)) =>
                  val copperTop =
                    copperFilesWithGraphics
                      .filter(f => f._1.fType.fileType == LayerConstants.COPPER_TOP)
                      .flatMap(_._2)

                  val exposedCopperTop = getExposedCopper(
                    name = "TOP",
                    soldermask = soldermaskGraphic,
                    copper = copperTop,
                    boardArea = boardArea,
                    debug = Some(DebugImageDescriptor(
                      team = assRef.team,
                      assembly = assRef.id,
                      debugName = "exposed_copper_top",
                      version = assRef.version
                    ))
                  )

                  Some(exposedCopperTop)

                case _ => None
              }

          val exposedCopperBottom =
            soldermaskGraphics
              .find(_._1.fType.fileType == LayerConstants.SOLDERMASK_BOTTOM)
              .flatMap {
                case (_, Some(soldermaskGraphic)) =>
                  val copperBottom =
                    copperFilesWithGraphics
                      .filter(f => f._1.fType.fileType == LayerConstants.COPPER_BOTTOM)
                      .flatMap(_._2)

                  val exposedCopperBottom = getExposedCopper(
                    name = "BOTTOM",
                    soldermask = soldermaskGraphic,
                    copper = copperBottom,
                    boardArea = boardArea,
                    debug = Some(DebugImageDescriptor(
                      team = assRef.team,
                      assembly = assRef.id,
                      debugName = "exposed_copper_bottom",
                      version = assRef.version
                    ))
                  )

                  Some(exposedCopperBottom)

                case _ => None
              }

          val exposedCopperProps = exposedCopperTop.map(value =>
            Seq(
              DecimalProperty.e(DFM.EXPOSED_COPPER_PERCENTAGE_TOP, value.exposedCopperPercentage),
              DecimalProperty.e(DFM.EXPOSED_COPPER_AREA_TOP, value.exposedCopperArea),
              DecimalProperty.e(DFM.COPPER_AREA_TOP, value.copperArea)
            )
          ).getOrElse(Seq.empty) ++ exposedCopperBottom.map(value =>
            Seq(
              DecimalProperty.e(DFM.EXPOSED_COPPER_PERCENTAGE_BOTTOM, value.exposedCopperPercentage),
              DecimalProperty.e(DFM.EXPOSED_COPPER_AREA_BOTTOM, value.exposedCopperArea),
              DecimalProperty.e(DFM.COPPER_AREA_BOTTOM, value.copperArea)
            )
          ).getOrElse(Seq.empty)

          Some(
            StepResult(
              pcbMeta = MetaInfo(exposedCopperProps.toMap)
            )
          )
        }
      } catch {
        case e: Throwable =>
          logger.error(e.getMessage, e)
          Future.successful(None)
      }
    }
}

object GerberBoardAnalyzer {
  private def getPropertyMinimum(
      listOfMetaDataPerFile: Seq[(UUID, MetaInfo)],
      property: String
  ): Option[(String, DecimalProperty)] =
    listOfMetaDataPerFile.flatMap(_._2 \ [DecimalProperty] property).map(_.value).minOption
      .map(value => DecimalProperty.e(property, value))

  def holeInfo(holes: Seq[HoleList]) /*(implicit ctx: ExecutionContext)*/: DrillMetaData = {
    var phmin: Option[BigDecimal]  = None
    var phmax: Option[BigDecimal]  = None
    var nphmin: Option[BigDecimal] = None
    var nphmax: Option[BigDecimal] = None
    var nphtoolcount               = 0
    var phtoolcount                = 0
    var nphcount                   = 0
    var phcount                    = 0

    holes.foreach { hl =>
      hl.tools.foreach { t =>
        val scaledDiameter = t.diameter / hl.scaling.getOrElse(1)

        if (t.drills.nonEmpty) {
          if (t.drillType == Tool.PLATED) {
            phmin = Some(phmin.getOrElse(scaledDiameter).min(scaledDiameter))
            phmax = Some(phmax.getOrElse(scaledDiameter).max(scaledDiameter))
            phtoolcount += 1
            phcount += t.drills.size
          }
          if (t.drillType == Tool.NON_PLATED) {
            nphmin = Some(nphmin.getOrElse(scaledDiameter).min(scaledDiameter))
            nphmax = Some(nphmax.getOrElse(scaledDiameter).max(scaledDiameter))
            nphtoolcount += 1
            nphcount += t.drills.size
          }
        }
      }
    }

    DrillMetaData(phmin, phmax, nphmin, nphmax, phtoolcount, nphtoolcount, phcount, nphcount)
  }

  private def min(a: Option[BigDecimal], b: Option[BigDecimal]): Option[BigDecimal] =
    (a, b) match {
      case (Some(_a), Some(_b)) => Some(_a.min(_b))
      case (None, Some(_b))     => Some(_b)
      case (Some(_a), None)     => Some(_a)
      case _                    => None
    }

  def consolidateMetaDataByStack(base: StepResult, s: SubStack, files: Seq[GerberFile]) = {
    val copperlayers =
      s.layers.filter(l => l.definition.layerType.exists(MaterialTypes.COPPER.contains)).flatMap(_.files).flatten

    val copperMetas: Seq[(GerberFile, MetaInfo)] = copperlayers.map { f =>
      val metas = f.metaInfo.getOrElse(MetaInfo.empty) ++
        files.find(_.id == f.id).flatMap(_.metaInfo).getOrElse(MetaInfo.empty) ++
        base.fileMetas.getOrElse(f.id, MetaInfo.empty) ++
        base.layerMetas.getOrElse(f.id, MetaInfo.empty)

      f -> metas
    }

    val outerfiles = Seq(copperMetas.head, copperMetas.last)
    val innerfileso =
      if (copperMetas.length > 2) {
        Some(copperMetas.slice(1, copperMetas.length - 1))
      } else {
        None
      }

    val outermetas  = outerfiles.map(_._2)
    val innermetaso = innerfileso.map(_.map(_._2))
    val allmetas    = outermetas ++ innermetaso.toSeq.flatten

    val traceWidth       = allmetas.flatMap(_ \ [DecimalProperty] DFM.TRACE_WIDTH).map(_.value).minOption
    val sameNetClearance = allmetas.flatMap(_ \ [DecimalProperty] DFM.SAME_NET_CLEARANCE).map(_.value).minOption

    val minOuterTraceWidth = outermetas.flatMap(_ \ [DecimalProperty] DFM.TRACE_WIDTH).map(_.value).minOption
    val minOuterClearance  = outermetas.flatMap(_ \ [DecimalProperty] DFM.CLEARANCE).map(_.value).minOption

    val outerStructure = min(minOuterTraceWidth, minOuterClearance)

    val innerProperties = innermetaso.map { i =>
      val tr = i.flatMap(_ \ [DecimalProperty] DFM.TRACE_WIDTH).map(_.value).minOption
      val cl = i.flatMap(_ \ [DecimalProperty] DFM.CLEARANCE).map(_.value).minOption

      Seq(
        min(tr, cl).map(_m => Property.of(DFM.MIN_INNER_STRUCTURE, _m).e),
        cl.map(_cl => Property.of(DFM.INNER_CLEARANCE, _cl).e),
        tr.map(_tr => Property.of(DFM.INNER_TRACE_WIDTH, tr).e)
      ).flatten
    } match {
      case Some(value) => value.map(Some(_))
      case None        => Seq()
    }

    StepResult(
      pcbMeta = MetaInfo((
        Seq(
          outerStructure.map(os => Property.of(DFM.MIN_OUTER_STRUCTURE, os).e),
          minOuterClearance.map(moc => Property.of(DFM.OUTER_CLEARANCE, moc).e),
          minOuterTraceWidth.map(mot => Property.of(DFM.OUTER_TRACE_WIDTH, mot).e),
          traceWidth.map(mot => Property.of(DFM.TRACE_WIDTH, mot).e),
          sameNetClearance.map(mot => Property.of(DFM.SAME_NET_CLEARANCE, mot).e)
        ) ++ innerProperties
      ).flatten.toMap)
    )
  }

  def getAnnularRingsMetaData(rings: Seq[AnnularRing]) = {
    val fileMeta = rings.groupBy(_.file.map(_.id)) // all rings grouped by file
      .flatMap { ox =>
        if (ox._1.isDefined) {
          Some(ox._1.get -> MetaInfo(ox._2.groupBy(_.t.drillType).flatMap {
            case (Tool.PLATED, rings) => Some(rings.minByOption(_.size).map(r =>
                DecimalProperty.e(DFM.PH_ANNULAR_RING, r.size)
              ).getOrElse(DecimalProperty.e(DFM.PH_ANNULAR_RING, 0.0)))
            case _ => None
          }))
        } else {
          None
        }
      }
    fileMeta
  }

  def getAnnularRingsResult(listOfMetaDataPerFile: Seq[(UUID, MetaInfo)]) = {
    val phAnnular =
      getPropertyMinimum(listOfMetaDataPerFile, DFM.PH_ANNULAR_RING)
    val buriedAnnular =
      getPropertyMinimum(listOfMetaDataPerFile, DFM.BURIED_VIA_ANNULAR_RING)
    val blindAnnular =
      getPropertyMinimum(listOfMetaDataPerFile, DFM.BLIND_VIA_ANNULAR_RING)

    val pcbMeta = MetaInfo(Seq(phAnnular, buriedAnnular, blindAnnular).flatten.toMap)

    val value = listOfMetaDataPerFile.map(y =>
      StepResult(
        pcbMeta = pcbMeta,
        fileMetas = Map(y)
      )
    )
    if (value.isEmpty) {
      None
    } else {
      Some(
        value.reduce(_ ++ _)
      )
    }
  }

  def getHoleProperties(holes: Seq[HoleList]) = {
    val holeInfo = GerberBoardAnalyzer.holeInfo(holes)

    val scale = 4
    Seq(
      holeInfo.phmin.map(value => DecimalProperty(DFM.PH_MIN_SIZE, value.setScale(scale, RoundingMode.HALF_UP))),
      holeInfo.phmin.map(value => DecimalProperty(DFM.VIA_MIN_SIZE, value.setScale(scale, RoundingMode.HALF_UP))),
      holeInfo.phmax.map(value => DecimalProperty(DFM.PH_MAX_SIZE, value.setScale(scale, RoundingMode.HALF_UP))),
      holeInfo.nphmin.map(value => DecimalProperty(DFM.NPH_MIN_SIZE, value.setScale(scale, RoundingMode.HALF_UP))),
      holeInfo.nphmax.map(value => DecimalProperty(DFM.NPH_MAX_SIZE, value.setScale(scale, RoundingMode.HALF_UP))),
      Some(DecimalProperty(DFM.PH_TOOL_COUNT, holeInfo.phtoolcount)),
      Some(DecimalProperty(DFM.NPH_TOOL_COUNT, holeInfo.nphtoolcount)),
      Some(DecimalProperty(DFM.PH_COUNT, holeInfo.phcount)),
      Some(DecimalProperty(DFM.NPH_COUNT, holeInfo.nphcount)),
      Some(DecimalProperty(DFM.TOTAL_DRILL_COUNT, holeInfo.nphcount + holeInfo.phcount))
    ).flatten
  }

  def calculateHoleCoverage(
      holes: HoleList,
      soldermaskTopData: Option[PCBLayerInternalData],
      soldermaskBotData: Option[PCBLayerInternalData],
      debugName: Option[String] = None,
      debugPath: Option[String] = None
  ): HoleList = {

    val MARGIN    = 10
    val mainScale = 1

    val topTree = soldermaskTopData.map(_.tree)
    val botTree = soldermaskBotData.map(_.tree)

    val debugAwt = debugPath.map { _ =>
      val dimensions = soldermaskTopData.get.format.get.dimension.get union
        soldermaskBotData.get.format.get.dimension.get
      //
      val awtImageTop = new BufferedImage(
        Math.abs((dimensions.size.x.intValue + (MARGIN * 2)) * mainScale).intValue,
        Math.abs((dimensions.size.y.intValue + (MARGIN * 2)) * mainScale).intValue,
        BufferedImage.TYPE_INT_RGB
      )
      val awtImageBot = new BufferedImage(
        Math.abs((dimensions.size.x.intValue + (MARGIN * 2)) * mainScale).intValue,
        Math.abs((dimensions.size.y.intValue + (MARGIN * 2)) * mainScale).intValue,
        BufferedImage.TYPE_INT_RGB
      )
      (awtImageTop, awtImageBot, awtImageTop.createGraphics(), awtImageBot.createGraphics())
    }

    debugAwt.foreach { awt =>
      val g2dTop = awt._3
      val g2dBot = awt._4
      topTree.foreach { t =>
        g2dTop.setColor(new Color(0, 0, 255, 50))
        Renderer.draw(
          t,
          a =>
            g2dTop.fill(a)
        )
      }
      botTree.foreach { t =>
        g2dBot.setColor(new Color(255, 0, 255, 50))
        Renderer.draw(
          t,
          a =>
            g2dBot.fill(a)
        )
      }
    }

    val tools = holes.tools.map { tool =>
      val hits = tool.drills.map { hit =>
        val g          = tool.graphic(hit)
        val holeBounds = g.getBounds2D
        val topCollisions = topTree.toSeq.flatMap { t =>
          t.collide(GraphicHelper(g)).filter { g =>
            val featureBounds = g.bounds
            featureBounds.contains(holeBounds)
          }
        }
        val botCollisions = botTree.toSeq.flatMap { t =>
          t.collide(GraphicHelper(g)).filter { g =>
            val featureBounds = g.bounds
            featureBounds.contains(holeBounds)
          }
        }

        debugAwt.foreach { awt =>
          val g2dTop = awt._3
          val g2dBot = awt._4

          val colorUncovered        = Color.GREEN
          val colorCovered          = Color.RED
          val colorCoveredOtherSide = Color.YELLOW

          (topCollisions.isEmpty, botCollisions.isEmpty) match {
            case (true, true) =>
              g2dTop.setColor(colorCovered)
              g2dBot.setColor(colorCovered)
            case (true, false) =>
              g2dTop.setColor(colorCovered)
              g2dBot.setColor(colorCoveredOtherSide)
            case (false, true) =>
              g2dTop.setColor(colorCoveredOtherSide)
              g2dBot.setColor(colorCovered)
            case (false, false) =>
              g2dTop.setColor(colorUncovered)
              g2dBot.setColor(colorUncovered)
          }

          g2dTop.fill(g)
          g2dBot.fill(g)
        }

        hit.copy(coverage =
          Some(
            DrillCoverage(
              soldermaskTop = Some(topCollisions.isEmpty),
              soldermaskBottom = Some(botCollisions.isEmpty)
            )
          )
        )
      }

      tool.copy(drills = hits)
    }

    debugAwt.foreach(awt =>
      debugPath.foreach { di =>
        val file = Paths.get(s"$di/top${debugName.getOrElse("")}.png").toFile
        println(file.getAbsolutePath)
        ImageIO.write(awt._1, "png", file)
        ImageIO.write(awt._2, "png", Paths.get(s"$di/bot${debugName.getOrElse("")}.png").toFile)
      }
    )
    holes.copy(tools = tools)
  }
}
