package de.fellows.ems.renderer.impl

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.api.broker.Message
import com.lightbend.lagom.scaladsl.broker.kafka.KafkaMetadataKeys
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.layerstack.api.Streams.LayerStackDefinitionChanged
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.renderer.impl.pool.layerstack.LayerStackDefinitionRenderTask
import de.fellows.ems.renderer.impl.pool.{RendererCoordinator, SimpleTaskContext}
import de.fellows.utils.{FilePath, TopicUtils}
import de.fellows.utils.internal.FileReader._
import de.fellows.utils.logging.StackrateLogging
import org.apache.batik.svggen.SVGGraphics2D

import java.io.{FileWriter => JavaFileWriter}
import java.nio.file.Paths
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class LayerstackDefinitionListener(
    stacks: LayerstackService,
    ass: AssemblyService,
    pcbService: PCBService,
    registry: PersistentEntityRegistry
)(
    implicit ectx: ExecutionContext
) extends StackrateLogging {
  val started = System.currentTimeMillis()

  implicit val conf: Config   = ConfigFactory.load()
  var app: RendererServiceApp = _

  def withApp(app: RendererServiceApp) =
    this.app = app

  import TopicUtils.defaultNaming
  TopicUtils.subscribeLatest(stacks.layerstackDefinitionTopic(), started) {
    _.payload match {
      case msg =>
        Future {
          val stack = msg.stack
          stack.team.foreach { team =>
            val ctx = new SimpleTaskContext[SVGGraphics2D]()
            val rt  = new LayerStackDefinitionRenderTask(stack, ctx)
            rt.onSuccess { (ctx, t) =>
              val tmp = FilePath.createTempDirectory("layerstack").resolve(UUID.randomUUID().toString + ".svg")
              withResource(new JavaFileWriter(tmp.toFile)) { wr =>
                ctx._r.foreach(_.stream(wr))
              }

              val base = Paths.get(conf.getString("fellows.storage.base"))

              val rel = base.relativize(tmp)
              stacks._setPreview(team, stack.name).invoke(rel.toString)
            }

            RendererCoordinator.submitTask(rt)
          }
          Done
        }
    }
  }

  TopicUtils.subscribeLatest(stacks.layerstackDefinitionTopic(), started) {
    _.payload match {
      case msg =>
        Future {
          val stack = msg.stack
          stack.team.foreach { team =>
            val ctx = new SimpleTaskContext[SVGGraphics2D]()
            val rt  = new LayerStackDefinitionRenderTask(stack, ctx)
            rt.onSuccess { (ctx, t) =>
              val tmp = FilePath.createTempDirectory("layerstack").resolve(UUID.randomUUID().toString + ".svg")
              withResource(new JavaFileWriter(tmp.toFile)) { wr =>
                ctx._r.foreach(_.stream(wr))
              }

              val base = Paths.get(conf.getString("fellows.storage.base"))

              val rel = base.relativize(tmp)
              stacks._setPreview(team, stack.name).invoke(rel.toString)
            }

            RendererCoordinator.submitTask(rt)
          }
          Done
        }
    }
  }
}
