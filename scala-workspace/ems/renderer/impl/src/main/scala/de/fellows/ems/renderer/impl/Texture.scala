package de.fellows.ems.renderer.impl

object Texture {
  //  def render(c: Graphic): Array[Byte] = {
  //    val doc = Renderer.createSvg(c)
  //
  //    val domSource = new DOMSource(doc)
  //    val writer = new StringWriter
  //    val result = new StreamResult(writer)
  //    val tf = TransformerFactory.newInstance
  //    val transformer = tf.newTransformer
  //    transformer.setOutputProperty(OutputKeys.INDENT, "yes")
  //    transformer.transform(domSource, result)
  //    println(writer.toString)
  //
  //    println(s"$doc")
  //    // Create a JPEGTranscoder and set its quality hint.
  //    val t: PNGTranscoder = new PNGTranscoder
  //    //      t.addTranscodingHint(PNGTranscoder, new Float(.8))
  //    t.addTranscodingHint(ImageTranscoder.KEY_BACKGROUND_COLOR, Color.WHITE)
  //    //    t.addTranscodingHint(SVGAbstractTranscoder.KEY_WIDTH, size)
  //    t.addTranscodingHint(SVGAbstractTranscoder.KEY_HEIGHT, 300f)
  //
  //    val input: TranscoderInput = new TranscoderInput(doc)
  //    val ostream: ByteArrayOutputStream = new ByteArrayOutputStream()
  //    val output: TranscoderOutput = new TranscoderOutput(ostream)
  //
  //    // Perform the transcoding.
  //    t.transcode(input, output)
  //    ostream.flush()
  //
  //    ostream.toByteArray
  //  }

}
