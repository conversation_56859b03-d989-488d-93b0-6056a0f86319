package de.fellows.ems.renderer.impl.analysis

import de.fellows.ems.pcb.model.Format
import de.fellows.ems.pcb.model.graphics.parts.{ComplexRegion, Curve, Flash, Line, Polygon}
import de.fellows.ems.pcb.model.graphics.tree._
import de.fellows.ems.pcb.model.graphics.{CollisionChecker, GPoint, GerberApertureDefinition, Graphic, Macro}
import de.fellows.ems.renderer.api.DistanceDescription
import de.fellows.ems.renderer.impl.gerber.GerberFormat
import de.fellows.ems.renderer.impl.gerber.graphic.ApertureUtils
import de.fellows.ems.renderer.impl.gerber.macros.MacroFlasher
import de.fellows.utils.{DebugUtils, ThreadUtils}
import org.slf4j.Logger
import play.api.Logging

import scala.concurrent.{ExecutionContext, Future}

class GerberLayerAnalyzer(
    data: PCBLayerInternalData,
    format: Format,
    worker: ExecutionContext,
    timeout: Option[Long],
    ignoreTraces: Boolean = false
) extends Logging {
  implicit val l: Logger = logger.logger

  import GerberLayerAnalyzer._
  val macros: Map[String, Macro] = data.macros.getOrElse(Seq()).map(x => x.name -> x).toMap

  def liesIn(small: Graphic, large: Graphic): Boolean = {
    val largeArea = large.areaX()

    small match {
      case Line(aperture, s, pol, i, from, to, a) =>
        val f = ApertureUtils.getArea(from, aperture, format.scaling.getOrElse(100.0).intValue(), macros)
        val t = ApertureUtils.getArea(to, aperture, format.scaling.getOrElse(100.0).intValue(), macros)

        largeArea.intersects(f) && largeArea.intersects(t)
      case Curve(aperture, s, pol, i, multiquadrant, clockwise, from, to, rel, a) =>
        largeArea.contains(from) && largeArea.contains(to)
      case _ => false
    }

    //    false
  }

  private val qtree: QuadTree[Graphic] = data.tree

  def getWindow(index: Int, lines: Vector[Line], size: Int = 10): Vector[Line] = {
    val start = Math.max(index - (size / 2), 0)
    val end   = Math.min(start + size, lines.size - 1)

    lines.slice(start, end)
  }

  private def getCoordinates(x: Line): (GPoint, GPoint) =
    (x.from, x.to)

  def analyzeThicknesses(): Vector[TraceWidthDescription] =
    DebugUtils.timed(s"Analyzing Thicknesses with scaling ${format.scaling} gerberscale is ${format.gerberscale}") {
      val idx: TraceIndex = data.traceIndex

      val widths = Vector.newBuilder[TraceWidthDescription]
      var total  = 0

      qtree.walk { g =>
        if (g.isInstanceOf[Line] || g.isInstanceOf[Curve]) {
          idx(g.index.x) match {
            case Some(trace) if trace.containsPads.getOrElse(true) =>
              g.thinness(Some(THINNESS_MARGIN_MM), format.gerberscale.orElse(format.scaling).get).minByOption(
                _.width
              ).foreach { dst =>
                val colls = qtree.collide(g)

                val parallel = g match {
                  case line: Line =>
                    val lines = colls.filter(x => x.isInstanceOf[Line]).asInstanceOf[Vector[Line]]
                    hasTooCloseLines(lines)
                  case _ => false
                }

                if (!parallel && !colls.exists(isLargeAndLinesIn(g, _))) {
                  widths.addOne(dst)
                  total += 1
                }
              }

            case _ =>
          }
        }
      }

      widths.result()
    }(logger.logger)

  private def isLargeAndLinesIn(graphic: Graphic, other: Graphic): Boolean =
    other match {
      case _: ComplexRegion | _: Polygon | _: Flash => liesIn(graphic, other)
      case _                                        => false
    }

  private val CLOSE_LINES_THRESHOLD = 10

  /** lines where the start AND end position are extremely close, usually indicate that there is some dirty gerber
    * lattice-work going on. we want to ignore these lines for the trace width, since it is often very misleading
    *
    * @param alllines
    *   should only contain Lines and Curves
    * @return
    */
  private def hasTooCloseLines(lines: Vector[Line]): Boolean = {
    val sortedLines = lines.sortBy(getCoordinates)

    sortedLines
      .zipWithIndex
      .exists {
        case (graphic, idx) =>
          // get the coordinates for this item
          val check = getCoordinates(graphic)

          // get the closest lines around this one
          val window = getWindow(idx, sortedLines)
          window
            .exists {
              // dont compare with itself
              case other if other.index == graphic.index => false
              case other =>
                val (otherX, otherY) = getCoordinates(other)

                // get the distances between start and end of this and the other item
                val sameDirection = (check._1.distance(otherX), check._2.distance(otherY))
                // in case its reversed
                val mirroredDirection = (check._2.distance(otherX), check._1.distance(otherY))

                val direction =
                  if (sameDirection._1 + sameDirection._2 < mirroredDirection._1 + mirroredDirection._2) {
                    sameDirection
                  } else {
                    mirroredDirection
                  }

                // if both distances are below threshold -> true
                direction._1 < CLOSE_LINES_THRESHOLD && direction._2 < CLOSE_LINES_THRESHOLD
            }
      }
  }

  def analyzeDistancesParallel(): Future[Vector[(ElementId[Int], ElementId[Int], DistanceDescription)]] = {
    val thread = Thread.currentThread()

    val margin = NEIGHBOR_MARGIN_MM * format.scaling.getOrElse(GerberFormat.DEFAULT_IMAGE_SCALING)

    DebugUtils.async(s"Analyzing Distances with margin $margin (format $format)") {
      try {
        val traceIndex                          = data.traceIndex
        implicit val executor: ExecutionContext = worker

        Future.sequence(
          qtree.collect[Future[Vector[(ElementId[Int], ElementId[Int], DistanceDescription)]]] { n =>
            ThreadUtils.interruptCheck(thread)
            timeout.foreach(ThreadUtils.interruptCheck)

            val traceIndexOption =
              if (ignoreTraces) {
                None
              } else {
                Some(traceIndex)
              }
            List(
              Future {
                collectDistances(qtree, traceIndexOption, margin, n, thread, macros).map { x =>
                  val (el1, el2, distance) = x
                  val (ft, tt) =
                    if (ignoreTraces) {
                      (None, None)
                    } else {
                      (
                        traceIndex(el1.x),
                        traceIndex(el2.x)
                      )
                    }

                  (
                    el1,
                    el2,
                    DistanceDescription(
                      Some(el1.x),
                      Some(el2.x),
                      distance,
                      distance.distance,
                      ft.map(_.traceID),
                      tt.map(_.traceID)
                    )
                  )
                }
              }
            )
          }
        ).map { distances =>
          distances
            .flatten
            .groupBy(f => (f._1.x, f._2.x)) // group by id to get all distances between the same elements
            .values
            .flatMap(vl =>
              vl.minByOption(_._3.distance.distance)
            ) // get the minimum for the particular set of two features
            .filter(d => d._3.distance.distance >= 1)
            .toVector
        }
      } catch {
        case e: Throwable =>
          e.printStackTrace()
          throw e
      }
    }(logger.logger, worker)
  }

  /** @param tree
    * @param traces if not empty, only get distances between items that are not in the same trace, and whose traces contain pads
    * @param neighbormargin
    * @param graphic the graphic to get distances for
    * @param parentThread
    * @param macros
    * @return
    */
  private def collectDistances(
      tree: QuadTree[Graphic],
      traces: Option[TraceIndex],
      neighbormargin: Double,
      graphic: Graphic,
      parentThread: Thread,
      macros: Map[String, Macro]
  ): Vector[(ElementId[Int], ElementId[Int], Distance)] = {

    val neighborChecker = new CollisionChecker[Graphic, Graphic] {
      override def collides(check: Graphic, element: Graphic): Boolean = {

        val traceCheck = traces match {
          case Some(traces) =>
            val e1 = traces(check.index.x)
            val e2 = traces(element.index.x)
            e1.flatMap(_.containsPads).getOrElse(true) &&
            e2.flatMap(_.containsPads).getOrElse(true) &&
            ((e1.map(_.traceID).orNull != e2.map(_.traceID).orNull))
          case None => true
        }

        traceCheck &&
        element.isNeighbor(check, neighbormargin) &&
        !element.intersects(check) // if they intersect, dont consider them as neighbors
      }

    }

    val fres = {
      val relevantNeighbors = tree.neighbor(graphic, neighbormargin, neighborChecker)

      val res =
        relevantNeighbors.flatMap { neigh =>
          ThreadUtils.interruptCheck(parentThread)

          graphic.distance(neigh, Some(neighbormargin), format.scaling.get)
            .minByOption(_.distance)
            .filter(_.distance <= neighbormargin)
            .filter(_.distance > 0)
            .map { d =>
              (graphic.index, neigh.index, d)
            }
        }

      res
    }

    val flashClearance: Option[(ElementId[Int], ElementId[Int], Distance)] = graphic match {
      case x: Flash =>
        x.aperture match {
          case ga: GerberApertureDefinition if (macros.contains(ga.template)) =>
            val mac = macros(ga.template)
            MacroFlasher.clearance(x, mac, format.gerberscale.get).map(d => (graphic.index, graphic.index, d))

          case _ => None
        }
      case _ => None
    }

    fres ++ flashClearance
  }
}

object GerberLayerAnalyzer {
  val NEIGHBOR_MARGIN_MM = 0.5
  val THINNESS_MARGIN_MM = 1.5
}
