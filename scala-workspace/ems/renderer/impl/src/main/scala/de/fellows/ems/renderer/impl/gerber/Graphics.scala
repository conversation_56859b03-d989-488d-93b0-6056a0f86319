package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.pcb.model.graphics.{GPoint, Geometry}
import org.apache.batik.ext.awt.geom.Polygon2D

import java.awt.Shape
import java.awt.geom.{Area, FlatteningPathIterator, PathIterator}

object Graphics {

  implicit class AreaExtensions(a: Area) extends Area {
    def subtract(s: Shape): Unit =
      a.subtract(new Area(s))

    def add(s: Shape): Unit =
      a.add(new Area(s))
  }

  def createNaturalPolygonArea(
      vertices: BigDecimal,
      center: GPoint,
      radius: BigDecimal,
      rotation: Option[Double]
  ): Area = {
    val poly: Polygon2D = createNaturalPolygon(vertices, center, radius, rotation)

    new Area(poly)
  }

  def createNaturalPolygon(vertices: BigDecimal, center: GPoint, radius: BigDecimal, rotation: Option[Double]) = {
    val poly = new Polygon2D()
    for (i <- 0 until vertices.intValue) {
      val yP = center.getBY + (radius * BigDecimal(Math.cos(
        (i * 2 * Math.PI / vertices.intValue + Math.PI / 2) + Math.toRadians(rotation.getOrElse(0.0))
      )))
      val xP = center.getBX + (radius * BigDecimal(Math.sin(
        (i * 2 * Math.PI / vertices.intValue + Math.PI / 2) + Math.toRadians(rotation.getOrElse(0.0))
      )))

      poly.addPoint(xP.floatValue, -yP.floatValue)
    }
    poly
  }

  def fliterate(i: PathIterator, flatness: Float = 0.01f)(h: (GPoint, GPoint) => Unit): Unit = {
    val check                    = new Array[Double](6) //    float[] coords = new float[6];
    val iter                     = new FlatteningPathIterator(i, flatness)
    var current: Option[GPoint]  = None
    var lastMove: Option[GPoint] = None
    while (!iter.isDone()) {
      val seg = iter.currentSegment(check);

      seg match {

        case PathIterator.SEG_MOVETO =>
          val newPoint = GPoint(check(0), check(1))
          current = Some(newPoint)
          lastMove = Some(newPoint)
        case PathIterator.SEG_LINETO =>
          val newPoint = GPoint(check(0), check(1))
          h(current.get, newPoint)
          current = Some(newPoint)
        case PathIterator.SEG_CLOSE =>
          h(current.get, lastMove.get)
          current = Some(lastMove.get)
      }
      iter.next()
    }
  }

}
