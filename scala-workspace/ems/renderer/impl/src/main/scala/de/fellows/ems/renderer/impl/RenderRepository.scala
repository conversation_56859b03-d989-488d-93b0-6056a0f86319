package de.fellows.ems.renderer.impl

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{ BoundStatement, PreparedStatement }
import com.lightbend.lagom.scaladsl.persistence.ReadSideProcessor.ReadSideHandler
import com.lightbend.lagom.scaladsl.persistence.cassandra.{ CassandraReadSide, CassandraSession }
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEventTag, ReadSideProcessor }
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.ems.renderer.impl.entity.render.{ RenderAdded, RenderEvent }
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition

import java.time.Instant
import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ ExecutionContext, Future }

class RenderRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  PCBCodecHelper.registerPCBCodecs(session)

  def getRendersFor(file: UUID): Future[Map[String, FilePath]] =
    session.selectAll(
      """
        |SELECT * FROM renders WHERE file = ?
      """.stripMargin,
      file
    ).map(_.map { r =>
      (r.getString("rType"), r.get("path", classOf[FilePath]))
    }.toMap)

  def getAllRendersFor(files: Seq[UUID]) =
    Future.sequence(files.map { f =>
      session.selectAll("SELECT * FROM renders WHERE file = ?", f)
        .map(r => f -> r.map(row => (row.getString("rType"), row.get("path", classOf[FilePath]))).toMap)
    }).map(_.toMap)

  //      .map(_.flatten.toMap)
}

private[impl] class RenderEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[RenderEvent] {

  var stmtSetRender: PreparedStatement = _

  def createTables() =
    for {
      _ <- PCBCodecHelper.loadTypes(session)
      _ <- session.executeCreateTable(
        """
            CREATE TABLE IF NOT EXISTS renders (
              file uuid,
              rType text,
              created timestamp,
              path filepath,

              PRIMARY KEY (file, rType)
            )
          """
      )

    } yield {
      PCBCodecHelper.registerPCBCodecs(session)
      Done
    }

  def prepareStatements(): Future[Done] =
    for {
      setRender <- session.prepare(
        """
          | UPDATE renders SET created = ?, path = ? WHERE file = ? AND rType = ?
        """.stripMargin
      )

    } yield {
      PCBCodecHelper.registerPCBCodecs(session)

      stmtSetRender = setRender
      Done
    }

  def addRender(event: RenderAdded): Future[immutable.Seq[BoundStatement]] =
    Future.successful(event.newRender.map { x =>
      stmtSetRender.bind(Instant.now, x._2, event.file.id, x._1)
    }.toList)

  override def buildHandler(): ReadSideHandler[RenderEvent] = readSide.builder[RenderEvent]("renderrepo-v1.1")
    .setGlobalPrepare(createTables)
    .setPrepare(_ => prepareStatements())
    .setEventHandler[RenderAdded](e => addRender(e.event))
    .build()

  override def aggregateTags: Set[AggregateEventTag[RenderEvent]] = RenderEvent.Tag.allTags
}

object RenderEventProcessor {}
