package de.fellows.ems.renderer.impl.entity.pcbreference

import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.GerberFile
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity
import play.api.libs.json.{Format, Json}

case class PCBReference(reference: AssemblyReference, files: Seq[GerberFile])

object PCBReference {
  implicit val f: Format[PCBReference] = Json.format[PCBReference]
}

class PCBReferenceEntity(implicit override val service: ServiceDefinition)
    extends SecureTeamEntity[Option[PCBReference]] {
  override type Command = PCBReferenceCommand
  override type Event   = PCBReferenceEvent

  override def initialState: Option[PCBReference] = None

  override def entityBehavior(state: Option[PCBReference]): Actions =
    Actions()
      .onCommand[SetPCBReference, PCBReference] {
        case (x: SetPCBReference, ctx, s) =>
          val ref = PCBReference(x.reference, x.files)
          ctx.thenPersist(ReferenceSet(ref))(_ => ctx.reply(ref))
      }
      .onReadOnlyCommand[GetPCBReference.type, PCBReference] {
        case (x: GetPCBReference.type, ctx, s) =>
          ctx.reply(s.get)
      }

  override def isAllowed(a: PCBReferenceCommand, s: Option[PCBReference]): Boolean = a match {
    case SetPCBReference(reference, files) => true
    case GetPCBReference                   => true
  }

}

sealed trait PCBReferenceCommand

case class SetPCBReference(reference: AssemblyReference, files: Seq[GerberFile]) extends PCBReferenceCommand
    with ReplyType[PCBReference]

object SetPCBReference {
  implicit val f: Format[SetPCBReference] = Json.format[SetPCBReference]
}

case object GetPCBReference extends PCBReferenceCommand with ReplyType[PCBReference] {
  implicit val f: Format[GetPCBReference.type] = Json.format[GetPCBReference.type]
}

sealed trait PCBReferenceEvent extends AggregateEvent[PCBReferenceEvent] {
  override def aggregateTag: AggregateEventShards[PCBReferenceEvent] = PCBReferenceEvent.Tag
}

object PCBReferenceEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[PCBReferenceEvent](NumShards)
}

case class ReferenceSet(reference: PCBReference) extends PCBReferenceEvent

object ReferenceSet {
  implicit val f: Format[ReferenceSet] = Json.format[ReferenceSet]
}
