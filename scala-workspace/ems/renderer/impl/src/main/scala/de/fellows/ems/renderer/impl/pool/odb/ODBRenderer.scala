package de.fellows.ems.renderer.impl.pool.odb

import de.fellows.ems.pcb.model._
import de.fellows.ems.pcb.model.graphics.tree.QuadTree
import de.fellows.ems.pcb.model.graphics.{GPoint, Graphic}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.{DebugUtils, UUIDUtils}
import de.luminovo.odb.odbpp.model.ODBFeatures.ODBFeatureRecord
import de.luminovo.odb.odbpp.model._
import de.luminovo.odb.odbpp.model.features.symbols.{HoleSymbol, RoundSymbol}
import de.luminovo.odb.odbpp.model.validation.SemanticError

import java.awt.geom.Rectangle2D
import scala.io.BufferedSource
import scala.util.Using

class ODBRenderer(featuresFile: java.io.File, val internalScale: Int = 100) extends StackrateLogging {

  var bounds: Rectangle2D = new Rectangle2D.Double(0, 0, 0, 0)

  def build(
      userSymbols: ODBUserSymbolPool,
      fontPool: ODBFontPool,
      desiredUnit: Option[ODBUnit]
  ): Either[SemanticError, ODBRender] =
    Using.resource(ODBUtils.fromFile(featuresFile)) { src =>
      val featuresOption = buildFeatures(src, desiredUnit)
      featuresOption.map { features =>
        ODBRender(features, renderFeaturesToTree(features, userSymbols, fontPool))
      }
    }

  def buildHoleList(
      features: ODBFeatures,
      toolsFile: Option[ODBStructuredText],
      gf: GerberFile
  ): Seq[UnreconciledTool] = {

    val toolMap = toolsFile.map { st =>
      st.obj("TOOLS")
    }.getOrElse(Seq())

    val symbolMap = features.symbols.map(x => x.symbolId -> x).toMap

    val tools = features.records.collect {
      case x: SymbolUsage => x
    }.groupBy(_.symNum).flatMap {
      case (symNum, usages) =>
        val symbol = symbolMap.get(symNum)

        val drillHits = usages.flatMap {
          case ODBFeatures.PadRecord(_, x, y, symNum, polarity, dcode, orientation) =>
            val symbol = symbolMap.get(symNum)
            val scaledHit = UnreconciledPoint.absolute(
              NumberWithScale(x, MetricDrills),
              NumberWithScale(y, MetricDrills)
            )

            symbol match {
              case Some(rs: RoundSymbol) =>
                Some(UnreconciledDrillHit(scaledHit, UUIDUtils.createTiny()))
              case Some(hs: HoleSymbol) =>
                Some(UnreconciledDrillHit(scaledHit, UUIDUtils.createTiny()))
              case _ => None
            }

          case _ => None // only pads in drill files
        }

        (symbol match {
          case Some(rs: RoundSymbol) =>
            Some(rs.d)
          case Some(hs: HoleSymbol) =>
            Some(hs.d)
          case _ => None
        }).map { dia =>
          // get the tool type. Its either a HoleSymbol that will define it, or we try to find it in the toolmap (built from the matrix)
          val toolType = symbol match {
            case Some(hs: HoleSymbol) =>
              if (hs.p == "v" || hs.p == "p") {
                Tool.PLATED
              } else {
                Tool.NON_PLATED
              }
            case _ =>
              val tool = toolMap.find(a => a.assignment("NUM").contains((symNum + 1).toString))
              tool.flatMap(_.assignment("TYPE")) match {
                case Some("VIA")        => Tool.PLATED
                case Some("PLATED")     => Tool.PLATED
                case Some("NON_PLATED") => Tool.NON_PLATED
                case _                  => Tool.UNKNOWN
              }
          }

          val tool = toolMap.find(a => a.assignment("NUM").contains((symNum + 1).toString))
          logger.info(s"[DRILLS] found drill in ${gf.name}: no. ${symNum}: $dia $toolType. toolmap had ${tool}")

          UnreconciledTool(
            name = s"T-${UUIDUtils.createTiny()}",
            diameter = NumberWithScale(dia, MetricDrills),
            drills = drillHits,
            slots = Seq(),
            drillType = Some(toolType)
          )
        }

    }

    tools.toSeq
  }

  private def renderFeaturesToTree(
      features: ODBFeatures,
      userSymbols: ODBUserSymbolPool,
      fontPool: ODBFontPool
  ): QuadTree[Graphic] = {
    val unscaledBounds = features.getBounds()
    this.bounds = new Rectangle2D.Double(
      unscaledBounds.getMinX * internalScale,
      unscaledBounds.getMinY * internalScale,
      unscaledBounds.getWidth * internalScale,
      unscaledBounds.getHeight * internalScale
    )
    val tree = new QuadTree[Graphic](
      min = GPoint(bounds.getMinX, bounds.getMinY),
      max = GPoint(bounds.getMaxX, bounds.getMaxY),
      maxPerBox = 4,
      maxDepth = Some(8)
    )

    ODBRenderer.renderFeatures(features, userSymbols, fontPool, internalScale) { (featureRecord, graphic) =>
      tree.insert(graphic)
    }

    tree
  }

  def buildFeatures(desiredUnit: Option[ODBUnit]): Either[SemanticError, ODBFeatures] = {
    val result = Using.resource(ODBUtils.fromFile(featuresFile)) { src =>
      buildFeatures(src, desiredUnit)
    }
    result
  }

  def buildFeatures(src: BufferedSource, desiredUnit: Option[ODBUnit]): Either[SemanticError, ODBFeatures] =
    ODBLineRecord(src).flatMap { lr =>
      ODBFeatures(lr, desiredUnit) match {
        case x: Left[SemanticError, ODBFeatures] =>
          logger.info(s"error ODBLineRecord features from file: ${featuresFile.getName}: ${x.value}")
          x
        case x: Right[SemanticError, ODBFeatures] =>
          x
      }
    }
}

object ODBRenderer extends StackrateLogging {
  def renderFeatures(
      features: ODBFeatures,
      userSymbols: ODBUserSymbolPool,
      fontPool: ODBFontPool,
      internalScale: Int,
      embedded: Boolean = false
  )(b: (ODBFeatureRecord, Graphic) => Unit): Unit = {
    val symbols = features.symbols.map { sym =>
      sym.symbolId -> sym
    }.toMap

    val recs = features.records.zipWithIndex.map {
      case (withSymbol: SymbolUsage, idx: Int) =>
        ODBGraphicsFactory.render(
          symbols(withSymbol.symNum),
          withSymbol,
          idx,
          internalScale,
          userSymbols,
          fontPool
        ).map(
          withSymbol -> _
        )

      case (x: ODBFeatures.SurfaceRecord, idx: Int) =>
        ODBGraphicsFactory.render(x.polygons, x, idx, internalScale).map(x -> _)
      case (x: ODBFeatures.TextRecord, idx: Int) =>
        ODBGraphicsFactory.renderText(x, idx, internalScale, fontPool).map(x -> _)

      case (x: ODBFeatures.BarcodeRecord, idx: Int) =>
        Left("missing implementation for BarcodeRecord")
    }
    recs.foreach {
      case Left(value: Throwable) =>
        logger.error(s"error in file", value)
      case Left(value) =>
        logger.error(s"error in file $value")
      case Right(value) =>
        b(value._1, value._2)
    }
  }

  def buildFontPool(gf: GerberFile): ODBFontPool = {
    val fontPool = new ODBFontPool
    ODBUtils.findODBRoot(gf.path.toJavaPath).foreach { root =>
      new FontParser(Option(root.resolve("fonts").toFile.listFiles()).map(_.toSeq).getOrElse(Seq()), fontPool).parse(
        Some(MMUnit)
      )
    }
    fontPool
  }

  def buildSymbolPool(gf: GerberFile): ODBUserSymbolPool = {
    val pool = new ODBUserSymbolPool
    ODBUtils.findODBRoot(gf.path.toJavaPath).foreach { root =>
      new UserSymbolParser(
        Option(root.resolve("symbols").toFile.listFiles()).map(_.toSeq).getOrElse(Seq()),
        pool
      ).parse(Some(MMUnit))
    }
    pool
  }
}
