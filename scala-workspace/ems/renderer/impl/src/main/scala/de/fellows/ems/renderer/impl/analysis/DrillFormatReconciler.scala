package de.fellows.ems.renderer.impl.analysis

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.ems.layerstack.api.{LayerStack, MaterialTypes}
import de.fellows.ems.pcb.model.graphics.tree.{ElementId, PCBLayerInternalData, QuadTree}
import de.fellows.ems.pcb.model.graphics.{Dark, Graphic}
import de.fellows.ems.pcb.model.{DrillHit, HoleList, PCBVersion, Scaling, Score}
import de.fellows.ems.renderer.api.Render
import de.fellows.ems.renderer.impl.analysis.DrillFormatReconciler.DEFAULT_SCALING
import de.fellows.ems.renderer.impl.entity.render.{GetRender, RenderEntity}
import de.fellows.utils.internal.FileReader
import play.api.libs.json.JsSuccess

import java.awt.Shape
import java.util.UUID
import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Success

class DrillFormatReconciler(
    version: UUID,
    holes: HoleList,
    pcb: PCBVersion,
    stack: LayerStack,
    registry: PersistentEntityRegistry
)(implicit exc: ExecutionContext) {

  def plated(holes: HoleList, tree: QuadTree[Graphic]): (Scaling, Score) =
    DEFAULT_SCALING.map { d =>
      d -> DrillFormatReconciler.score(holes, tree, scale = d)
    }.sortBy(_._2.isPlated).reverse.head

  def reconcileScale(): Future[(Scaling, Score)] =
    Future.sequence(stack.stacks.head.layers
      .filter { l =>
        l.definition.layerType.contains(MaterialTypes.CORE) ||
        l.definition.layerType.contains(MaterialTypes.FLEXCORE) ||
        l.definition.layerType.contains(MaterialTypes.FOIL)
      }
      .flatMap { layer =>
        layer.files.toSeq.flatten
      }
      .zipWithIndex
      .map { x =>
        registry.refFor[RenderEntity](Render.fileID(version, x._1.name)).ask(GetRender)
          .map { render =>
            render.tree
              .map(t => FileReader.json[PCBLayerInternalData](t.toJavaPath))
              .flatMap {
                case Success(JsSuccess(value, path)) =>
                  Some(plated(holes, value.tree))
                case _ =>
                  None
              }
          }
      })
      .map(_.flatten.sortBy(_._2.isPlated).reverse.headOption.getOrElse((Scaling(1, 1), Score(1, 1))))
}

case class GraphicHelper(s: Shape) extends Graphic(Dark(), ElementId(0), None) {
  override def shape: Shape = s
}

object DrillFormatReconciler {
  lazy val DEFAULT_SCALING: Seq[Scaling] =
    for {
      fscale <- Seq(0.01, 0.1, 1.0, 10.0, 100.0)
      uscale <- Seq(1.0)
    } yield Scaling(fscale, uscale)

  def score(holes: HoleList, tree: QuadTree[Graphic], scale: Scaling): Score = {
    val countbuilder     = mutable.Map[DrillHit, Int]()
    val collidedGraphics = mutable.Map[Int, Int]()

    val maxGraphics = tree.collect(g => Seq(1)).sum
    val maxDrills   = holes.tools.map(_.drills.length).sum
    holes.tools.foreach { tool =>
      tool.drills.foreach { hit =>
        val gr    = tool.graphic(hit, scale)
        val colls = tree.collide(GraphicHelper(gr))

        countbuilder.put(hit, countbuilder.getOrElse(hit, 0) + colls.length)

        colls.foreach { g =>
          collidedGraphics.put(g.index.x, collidedGraphics.getOrElse(g.index.x, 0) + 1)
        }
      }
    }

    //    println(collidedGraphics.filter(_._2 > 1))

    val collidedElementsCount = collidedGraphics.keys.toSeq.length
    val platedScore           = collidedElementsCount.doubleValue() / maxGraphics.doubleValue()

    val presentScore = countbuilder.count(_._2 > 0) / maxDrills.doubleValue()

    Score(platedScore, presentScore)
  }
}
