package de.fellows.ems.renderer.impl.entity.distances

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventShards, AggregateEventTag }
import de.fellows.ems.renderer.api.DistanceDescription
import de.fellows.ems.renderer.impl.RendererServiceImpl
import play.api.libs.json
import play.api.libs.json.Json

import java.util.UUID

case class Distances(distances: Seq[DistanceDescription])

object Distances {
  def id(version: UUID, file: String) =
    RendererServiceImpl.id(version, file, "distances")

  implicit val format: json.Format[Distances] = Json.format
}

sealed trait DistancesCommand

case class AddDistances(version: UUID, file: String, distances: Seq[DistanceDescription]) extends DistancesCommand
    with ReplyType[Done]

case class SetDistances(version: UUID, file: String, distances: Seq[DistanceDescription]) extends DistancesCommand
    with ReplyType[Done]

case class GetDistances(version: UUID, file: String) extends DistancesCommand with ReplyType[Distances]

object SetDistances {
  implicit val format: json.Format[SetDistances] = Json.format
}

object GetDistances {
  implicit val format: json.Format[GetDistances] = Json.format
}

object AddDistances {
  implicit val format: json.Format[AddDistances] = Json.format
}

sealed trait DistancesEvent extends AggregateEvent[DistancesEvent] {
  override def aggregateTag: AggregateEventShards[DistancesEvent] = DistancesEvent.Tag
}

object DistancesEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[DistancesEvent](NumShards)
}

case class DistanceAdded(version: UUID, file: String, distance: Seq[DistanceDescription]) extends DistancesEvent

case class DistancesSet(version: UUID, file: String, distance: Seq[DistanceDescription]) extends DistancesEvent

object DistanceAdded {
  implicit val format: json.Format[DistanceAdded] = Json.format
}

object DistancesSet {
  implicit val format: json.Format[DistancesSet] = Json.format
}
