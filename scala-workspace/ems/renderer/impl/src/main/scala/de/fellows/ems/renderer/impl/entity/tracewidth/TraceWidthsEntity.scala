package de.fellows.ems.renderer.impl.entity.tracewidth

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity

class TraceWidthsEntity extends PersistentEntity {
  override type Command = TraceWidthsCommand
  override type Event   = TraceWidthsEvent
  override type State   = TraceWidths

  override def initialState: TraceWidths = TraceWidths(Seq())

  override def behavior: Behavior =
    Actions()
      .onReadOnlyCommand[GetTraceWidths, TraceWidths] {
        case (GetTraceWidths(v, f), ctx, s) =>
          ctx.reply(s)
      }
      .onCommand[AddTraceWidths, Done] {
        case (AddTraceWidths(ref, file, dst), ctx, s) =>
          ctx.thenPersist(
            TraceWidthsAdded(ref, file, dst)
          )(_ => ctx.reply(Done))
      }
      .onCommand[SetTraceWidths, Done] {
        case (SetTraceWidths(ref, file, dst), ctx, s) =>
          (dst.sliding(10, 10).toSeq) match {
            case x if x.size > 1 =>
              val h      = x.splitAt(1)
              val events = Seq(h._1.map(TraceWidthsSet(ref, file, _)), h._2.map(TraceWidthsAdded(ref, file, _))).flatten
              ctx.thenPersistAll(events: _*)(() => ctx.reply(Done))
            case x if x.size == 1 =>
              ctx.thenPersist(TraceWidthsSet(ref, file, x.head))(_ => ctx.reply(Done))
            case _ =>
              ctx.thenPersist(TraceWidthsSet(ref, file, Seq()))(_ => ctx.reply(Done))
          }
      }
      .onEvent {
        case (TraceWidthsAdded(version, file, widths), s) =>
          s.copy(widths = s.widths ++ widths)
        case (TraceWidthsSet(version, file, widths), s) =>
          s.copy(widths = widths)
      }
}
