// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.ems.renderer.impl

import akka.actor.ActorSystem
import akka.dispatch.MessageDispatcher
import akka.stream.scaladsl.StreamConverters
import com.lightbend.lagom.scaladsl.api.transport.TransportException
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.Config
import de.fellows.app.assembly.commons.{AssemblyFiles, AssemblyReference}
import de.fellows.app.assemby.api.Version
import de.fellows.app.security.SecurityBodyParser
import de.fellows.ems.pcb.model.{GerberFile, Graphic, GraphicUtils, RenderConstants}
import de.fellows.ems.renderer.impl.PCBListener.{getSvgPreviewPath, persistPreview, setAsyncLifecycle}
import de.fellows.ems.renderer.impl.RendererServiceImpl.fileID
import de.fellows.ems.renderer.impl.entity.render.{GetRender, RenderEntity}
import de.fellows.ems.renderer.impl.pool.layerstack.LayerstackRenderer
import de.fellows.ems.renderer.impl.pool.{RendererCoordinator, SimplePreviewRenderTask, SimpleRenderContext}
import de.fellows.ems.renderer.impl.simple.SimpleGerberRenderer
import de.fellows.ems.renderer.impl.supp.SupplementaryImageBuilderTask
import de.fellows.ems.renderer.impl.SVGUtils.format
import de.fellows.utils.{FilePath, UUIDUtils}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.internal.{FileLifecycleStageName, LifecycleDeadline, StageStatusName}
import de.fellows.utils.internal.FileReader._
import de.fellows.utils.internal.StageStatusName.Progress
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.security.{Auth0Token, Auth0TokenContent, GenericTokenContent}
import org.apache.batik.anim.dom.SVGDOMImplementation
import org.apache.batik.constants.XMLConstants
import org.apache.batik.transcoder.image.{JPEGTranscoder, PNGTranscoder}
import org.apache.batik.transcoder.{SVGAbstractTranscoder, TranscoderInput, TranscoderOutput}
import org.w3c.dom.{Document, Element}
import play.api.http.{FileMimeTypes, Writeable}
import play.api.mvc._
import play.api.routing.Router
import play.api.routing.sird._

import java.awt.Color
import java.io.{ByteArrayInputStream, ByteArrayOutputStream, File, FileInputStream, StringWriter}
import java.net.URLDecoder
import java.nio.file.Files
import java.util.UUID
import java.util.concurrent.Callable
import javax.xml.transform.TransformerFactory
import javax.xml.transform.dom.DOMSource
import javax.xml.transform.stream.StreamResult
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try, Using}

class Converter(
    action: DefaultActionBuilder,
    parser: PlayBodyParsers,
    actorSystem: ActorSystem,
    mime: FileMimeTypes,
    registry: PersistentEntityRegistry,
    conf: Config
)(implicit ec: ExecutionContext, sd: ServiceDefinition) extends StackrateLogging {

  var app: RendererServiceApp = _

  def withApp(s: RendererServiceApp): Converter = {
    this.app = s
    this
  }

  def syncPreviewRender(size: Option[Float]): Action[_] =
    action(
      SecurityBodyParser(token => s"renderer-api:${token.getTeam}:${token.getTeam}:renderer:*:write")(token =>
        parser.multipartFormData
      )
    ) {
      request =>
        val files = request.body.files
        // TODO: multiple files

        files.headOption.map { f =>
          val path = f.ref.path
          val ctx  = new SimpleRenderContext()
          val rt   = new SimplePreviewRenderTask(ctx, path)
          Some(RendererCoordinator.submitTask(rt))
          rt.waitFor()

          val ostream = new ByteArrayOutputStream()
          ctx.result.foreach(SimplePreviewRenderTask.writeStream(_, ostream, size))

          ostream.toByteArray
        } match {

          case Some(value) =>
            val io = StreamConverters.fromInputStream(() => new ByteArrayInputStream(value))
            Results.Ok.streamed(io, Some(value.length), Some("image/png"))
          case None => Results.BadRequest
        }
    }

  def convert(format: Option[String], width: Option[Float], height: Option[Float]): Action[_] =
    action(
      SecurityBodyParser(token => s"renderer:${token.getTeam}:${token.getTeam}:converter:*:write")(token =>
        parser.multipartFormData
      )
    ) {

      request =>
        val files = request.body.files

        files.find(_.contentType.contains("image/svg+xml")).map { part =>
          val t = format.map(_.toLowerCase) match {
            case Some("jpg") | Some("jpeg") =>
              val t = new JPEGTranscoder()
              t.addTranscodingHint(JPEGTranscoder.KEY_QUALITY, 0.5f);
              t
            case _ => new PNGTranscoder()
          }

          width.foreach(w => t.addTranscodingHint(SVGAbstractTranscoder.KEY_WIDTH, w))
          height.foreach(h => t.addTranscodingHint(SVGAbstractTranscoder.KEY_HEIGHT, h))

          val input   = new TranscoderInput(part.ref.toURI.toString)
          val ostream = new ByteArrayOutputStream()
          val output  = new TranscoderOutput(ostream)

          t.transcode(input, output)

          // Flush and close the stream.
          ostream.flush()
          ostream.close()

          ostream.toByteArray
        } match {
          case Some(ba) =>
            val is = new ByteArrayInputStream(ba)
            Results.Ok.streamed(
              StreamConverters.fromInputStream(() => is),
              Some(ba.length),
              (format.map(_.toLowerCase) match {
                case Some("jpg") | Some("jpeg") => Some("image/jpeg")
                case _                          => Some("image/png")
              })
            )
          case None => Results.NotFound
        }
    }

  def getLayerstackForPCBByVersion(
      version: String
  ): Action[_] = {
    var token: GenericTokenContent = null
    action.async(
      SecurityBodyParser { token =>
        token match {
          case _: Auth0Token => s"view:pcb"
          case t             => s"pcb:${t.getTeam}:${t.getTeam}:$version:specification:read"
        }

      } { t =>
        token = t
        parser.anyContent
      }
    ) {
      request =>
        val versionId = UUID.fromString(version)
        app.assService._getAssemblyIdByVersion(token.getTeam, versionId).invoke()
          .map { id =>
            _doGetLayerstackImage(id, versionId, token)
          }
    }
  }

  def getLayerstackForPCB(
      assembly: String,
      version: String
  ): Action[_] = {
    var token: GenericTokenContent = null
    action(
      SecurityBodyParser(token => s"pcb:${token.getTeam}:${token.getTeam}:$version:specification:read") { t =>
        token = t
        parser.anyContent
      }
    ) {
      request =>
        _doGetLayerstackImage(UUID.fromString(assembly), UUID.fromString(version), token)
    }
  }

  private def _doGetLayerstackImage(assembly: UUID, version: UUID, token: GenericTokenContent) = {
    val path = LayerstackRenderer.getLayerstackImagePath(
      token.getTeam,
      assembly,
      version
    )(conf)

    val jfile = path.toJavaFile
    if (jfile.exists() && jfile.isFile) {
      Results.Ok.sendFile(
        jfile
      )(ec, mime)
    } else {
      Results.NotFound
    }
  }

  def getHighQualityStackup(
      assembly: String,
      version: String,
      specification: Option[String],
      back: Option[Boolean],
      height: Option[Float],
      format: Option[String]
  ): Action[_] = {
    var token: GenericTokenContent = null
    action.async(
      SecurityBodyParser(token => s"pcb:${token.getTeam}:${token.getTeam}:$version:specification:read") { t =>
        token = t
        parser.anyContent
      }
    ) {
      request =>
        try {
          val assemblyID = UUID.fromString(assembly)
          val versionID  = UUID.fromString(version)
          logger.withAssembly(token.getTeam, assemblyID, Some(versionID), None)
          val specificationID = specification.flatMap(UUIDUtils.fromString)

          val conf = back match {
            case Some(true) => StackConfig(false, true)
            case _          => StackConfig(true, false)
          }

          val rpf = app.pcbService._getPCBVersion(token.getTeam, assemblyID, versionID).invoke().flatMap { pcbv =>
            new PreviewRenderer(
              app.pcbService,
              app.layerstackService,
              registry,
              pcbv,
              pcbv.outline.get
            )._doHighQualityRenderPreview(assemblyID, versionID, specificationID, token.getTeam, conf)
          }

          format match {
            case Some("png") =>
              deliverAsPNG(rpf.map(conf.getFirst), height)
            case Some("webp") =>
              deliverAsWEBP(rpf.map(conf.getFirst), height)
            case _ =>
              deliverAsSVG(rpf.map(conf.getFirst))

          }

        } catch {
          case e: TransportException => Future.successful({
              val w = implicitly[Writeable[String]].toEntity(s"${e.exceptionMessage}: ${e.exceptionMessage.detail}")
              new Results.Status(e.errorCode.http).sendEntity(w)
            })
          case e: IllegalArgumentException => Future.successful({
              Results.BadRequest(e.getMessage)
            })
        }

    }
  }

  private def deliverAsSVG(rpf: Future[Document]) =
    rpf.map { rp =>
      withResource(new StringWriter()) { writer =>
        //        Result result = new StreamResult(file or  output stream or writer);
        //        Transformer xformer = TransformerFactory.newInstance().newTransformer();
        //        xformer.transform(source, result);
        val s = new DOMSource(rp)
        //        rp.doc.

        val result      = new StreamResult(writer)
        val tf          = TransformerFactory.newInstance
        val transformer = tf.newTransformer()
        transformer.transform(s, result)

        val string = writer.toString.getBytes()
        withResource(new ByteArrayInputStream(string)) { is =>
          Results.Ok.streamed(StreamConverters.fromInputStream(() => is), Some(string.length), Some("image/svg"))
        }
      }
    }

  private def deliverAsWEBP(rpf: Future[Document], height: Option[Float]) =
    rpf.map { rp =>
      val temp = Files.createTempFile("webpconvert", ".webp")
      if (SVGStackup.writeWEBP(rp, temp.toString, height.getOrElse(1440f))) {
        val bytes = Files.readAllBytes(temp)
        temp.toFile.delete()
        withResource(new ByteArrayInputStream(bytes)) { is =>
          Results.Ok.streamed(StreamConverters.fromInputStream(() => is), Some(bytes.length), Some("image/webp"))
        }
      } else {
        Results.InternalServerError
      }
    }

  private def deliverAsPNG(rpf: Future[Document], height: Option[Float]) =
    rpf.map { rp =>
      val trans = new PNGTranscoder()
      trans.addTranscodingHint(SVGAbstractTranscoder.KEY_HEIGHT, height.getOrElse(1440f))
      val input: TranscoderInput = new TranscoderInput(rp)

      withResource(new ByteArrayOutputStream()) { ostream =>
        val output = new TranscoderOutput(ostream)
        trans.transcode(input, output)

        // Flush and close the stream.
        ostream.flush()
        val ba = ostream.toByteArray

        withResource(new ByteArrayInputStream(ba)) { is =>
          Results.Ok.streamed(StreamConverters.fromInputStream(() => is), Some(ba.length), Some("image/png"))
        }
      }
    }
  def getFileLifecycle(version: Version, lc: FileLifecycleStageName, gf: GerberFile) =
    version.files.flatMap(_.find(_.name == gf.name)).flatMap(_.lifecycles.find(_.name == lc))

  /** rerender the preview if the last modified date is older than the threshold
    * this is the case for older projects that need to have previews with the new simple renderer
    *
    * This is a hack to ensure backwards compatibility
    * @return
    */
  def checkForRerender(
      assRef: AssemblyReference,
      format: Option[String],
      file: File,
      gfo: Option[GerberFile]
  ): Future[Option[File]] = {
    val LAST_MODIFIED_PREVIEW_THRESHOLD =
      1695053752383L // Timestamp when the new renderer was introduced
    gfo.map { gf =>
      val previewPath  = getSvgPreviewPath(gf)
      val lastModified = previewPath.toJavaFile.lastModified()
      if (format.contains("svg") && lastModified < LAST_MODIFIED_PREVIEW_THRESHOLD && lastModified > 0L) {
        logger.warn(s"Rerendering preview for ${gfo.map(_.name)}")
        implicit val rctx: MessageDispatcher = actorSystem.dispatchers.lookup("rendering-dispatcher")

        RendererCoordinator.previews.submit(new Callable[Either[Throwable, FilePath]] {

          override def call(): Either[Throwable, FilePath] = {
            val start = System.currentTimeMillis()

            val f = app.assService._getVersion(assRef.team, assRef.id, assRef.version).invoke().flatMap { version =>
              val lc = getFileLifecycle(version, FileLifecycleStageName.Preview, gf).map(_.status.name)

              if (lc.contains(Progress)) {
                Future.successful(Right(previewPath))
              } else {
                for {

                  _ <-
                    setAsyncLifecycle(
                      assService = app.assService,
                      Seq(),
                      assRef,
                      gf.name,
                      FileLifecycleStageName.Preview,
                      StageStatusName.Progress,
                      System.currentTimeMillis(),
                      Some(LifecycleDeadline.in(10 minutes))
                    )

                  svgString <- Future.successful(Try {
                    Using.resource(new FileInputStream(new java.io.File(gf.path.toPath))) { is =>
                      new SimpleGerberRenderer(is).render()
                    }
                  })

                  path <- persistPreview(assRef, gf, svgString, start, app.assService, app.persistentEntityRegistry)
                } yield path
              }

            }

            f.onComplete {
              case Failure(exception) => logger.error(s"Failed to rerender preview for ${gfo.map(_.name)}", exception)
              case Success(value)     => logger.info(s"sucessfully rerendered preview for ${gfo.map(_.name)}")
            }

            Await.result(f, 5 minute)
          }

        })
        Future.successful(None)
      } else {
        Future.successful(Some(file))
      }
    }.getOrElse(Future.successful(Some(file)))
  }

  def getPreview(assembly: String, version: String, encodedfile: String, format: Option[String]): Handler = {
    var token: GenericTokenContent = null
    action.async(
      SecurityBodyParser {
        case _: Auth0TokenContent =>
          s"view:pcb"
        case t =>
          s"pcb:${t.getTeam}:${t.getTeam}:$version:specification:read"
      } { t =>
        token = t
        parser.anyContent
      }
    ) { _ =>
      val file       = URLDecoder.decode(encodedfile, "UTF-8")
      val assemblyID = UUID.fromString(assembly)
      val versionID  = UUID.fromString(version)
      app.pcbService._getPCBVersion(token.getTeam, assemblyID, versionID).invoke().flatMap { pcb =>
        registry.refFor[RenderEntity](fileID(UUID.fromString(version), file)).ask(GetRender).flatMap { render =>
          val gfo = pcb.files.find(f => f.name == file || f.id.toString == file)
          val path = gfo.map { gf =>
            format match {
              case Some("svg") => gf.path.copy(base = "previews", filename = s"${gf.path.filename}.svg")
              case _           => gf.path.copy(base = "previews", filename = s"${gf.path.filename}.png")
            }
          }

          path match {
            case Some(p) =>
              val file = p.toJavaFile
              if (file.exists() && file.isFile) {

                checkForRerender(pcb.assembly.get, format, file, gfo)
                  .map {
                    case Some(value) =>
                      Results.Ok.sendFile(
                        content = value
                      )(ec, mime)
                    case None =>
                      Results.NotFound
                  }
              } else {
                Future.successful(Results.NotFound)
              }
            case _ =>
              Future.successful(Results.NotFound)
          }
        }
      }
    }
  }

  def getHighQualityPreview(assembly: String, version: String, encodedfile: String, size: Option[Float]): Handler = {
    var token: GenericTokenContent = null
    action.async(
      SecurityBodyParser(token => s"pcb:${token.getTeam}:${token.getTeam}:$version:specification:read") { t =>
        token = t
        parser.anyContent
      }
    ) {
      _ =>
        val file       = URLDecoder.decode(encodedfile, "UTF-8")
        val assemblyID = UUID.fromString(assembly)
        val versionID  = UUID.fromString(version)

        val maskfile = AssemblyFiles.createAssemblyResourceFolder(token.getTeam, assemblyID, versionID)(this.conf)
          .copy(base = "mask", filename = s"${file}.png").toJavaFile

        if (maskfile.exists() && maskfile.isFile) {
          Future.successful(Results.Ok.sendFile(maskfile)(ec, mime))
        } else {

          for {
            pcbv   <- app.pcbService._getPCBVersion(token.getTeam, assemblyID, versionID).invoke()
            render <- registry.refFor[RenderEntity](fileID(UUID.fromString(version), file)).ask(GetRender)
          } yield GraphicUtils.readFile(pcbv.outline.get.path).map { outline =>
            val json =
              (render.graphics.get(RenderConstants.COPPER_JSON) orElse render.graphics.get(RenderConstants.LAYER_JSON))
                .flatMap(GraphicUtils.readFile).get

            Converter.renderMask(outline, json, maskfile, SupplementaryImageBuilderTask.getOutlineSize(outline))

            Results.Ok.sendFile(
              maskfile
            )(ec, mime)
          }.getOrElse(Results.InternalServerError)
        }
    }
  }

  def router: Router =
    Router.from {
      case POST(
            p"/api/ems/renderer/convert" ? q_o"format=$format" & q_o"width=${float(width)}" & q_o"height=${float(height)}"
          ) =>
        convert(format, width, height)

      case GET(
            p"/api/ems/renderer/layerstacks/$assembly/versions/$versions"
          ) =>
        getLayerstackForPCB(assembly, versions)
      case GET(
            p"/api/ems/renderer/layerstacks/versions/$versions"
          ) =>
        getLayerstackForPCBByVersion(versions)
      case GET(
            p"/api/ems/renderer/stackups/$assembly/versions/$versions/specifications/$specification" & q_o"back=${bool(
                back
              )}" & q_o"height=${float(height)}" & q_o"format=${format}"
          ) =>
        getHighQualityStackup(assembly, versions, Some(specification), back, height, format)
      case GET(
            p"/api/ems/renderer/assemblies/$assembly/versions/$versions/files/mask/${file}*" & q_o"size=${float(
                size
              )}"
          ) =>
        getHighQualityPreview(assembly, versions, file, size)
      case GET(
            p"/api/ems/renderer/assemblies/$assembly/versions/$versions/files/$file" & q_o"format=${format}"
          ) =>
        getPreview(assembly, versions, file, format)
      case GET(p"/api/ems/renderer/stackups/$assembly/versions/$versions" & q_o"back=${bool(
              back
            )}" & q_o"specification=${specification}" & q_o"height=${float(height)}" & q_o"format=${format}") =>
        getHighQualityStackup(assembly, versions, specification, back, height, format)

      case POST(p"/api/ems/renderer/doRender" & q_o"size=${float(size)}") =>
        syncPreviewRender(size)
      //      case GET(p"/files/ems/renderer/$assembly/versions/$version/textures/${filename}") =>
      //        downloadTexture(assembly, version, s"$filename")
    }
}

object Converter {
  val svgNS = SVGDOMImplementation.SVG_NAMESPACE_URI
  val impl  = SVGDOMImplementation.getDOMImplementation

  def renderMask(outline: Graphic, data: Graphic, file: File, size: Float = 800f): Unit = {

    val doc       = impl.createDocument(svgNS, "svg", null)
    val rootGroup = doc.createElementNS(svgNS, "g")
    val defs      = doc.createElementNS(svgNS, "defs")
    val svgRoot   = doc.getDocumentElement
    rootGroup.appendChild(defs)
    svgRoot.appendChild(rootGroup)
    val viewbox = outline.format.dimension.get

    rootGroup.setAttributeNS(
      null,
      "transform",
      s"translate(${format(-viewbox.min.x)}, ${format(viewbox.max.y)}), matrix(1,0,0,-1,0,0)"
    )

    def draw(g: Graphic, fill: String): (Seq[Element], Seq[Element]) = {
      val defs = g.defs.map { d =>
        val id   = d.id + UUIDUtils.createTiny()
        val path = doc.createElementNS(svgNS, "path")
        path.setAttribute("id", s"${id}")
        path.setAttributeNS(null, "d", d.path)
        d.id -> (path, id)
      }.toMap

      val elems = g.paths.flatMap(gg =>
        gg.flatMap { element =>
          if (element.use.isDefined) {
            val use  = element.use.get
            val path = doc.createElementNS(svgNS, "use")
            path.setAttributeNS(
              XMLConstants.XLINK_NAMESPACE_URI,
              XMLConstants.XLINK_HREF_QNAME,
              s"#${defs(use.reference)._2}"
            )
            path.setAttributeNS(null, "x", format(use.location.x))
            path.setAttributeNS(null, "y", format(use.location.y))
            if (element.orientation.exists(_.degrees > 0)) {
              path.setAttributeNS(
                null,
                "transform",
                s"rotate(${format(element.orientation.get.degrees)} ${format(use.location.x)} ${format(use.location.y)})"
              )
            }
            Some(path)
          } else if (element.path.isDefined) {
            val path = doc.createElementNS(svgNS, "path")
            path.setAttributeNS(null, "fill", fill)
            path.setAttributeNS(null, "d", element.path.get)
            if (element.orientation.exists(_.degrees > 0)) {
              path.setAttributeNS(null, "transform", s"rotate(${format(element.orientation.get.degrees)} 0 0)")
            }
            Some(path)
          } else {
            None
          }
        }
      )

      (elems, defs.values.map(_._1).toSeq)
    }

    svgRoot.setAttribute("width", format(viewbox.size.x.intValue))
    svgRoot.setAttribute("height", format(viewbox.size.y.intValue))

    val (elems1, defs1) = draw(outline, "white")
    val (elems2, defs2) = draw(data, "black")

    defs1.foreach(d => defs.appendChild(d))
    defs2.foreach(d => defs.appendChild(d))

    val outlinegroup = doc.createElementNS(svgNS, "g")
    elems1.foreach(d => outlinegroup.appendChild(d))
    rootGroup.appendChild(outlinegroup)

    val layerdata = doc.createElementNS(svgNS, "g")
    elems2.foreach(d => layerdata.appendChild(d))
    rootGroup.appendChild(layerdata)

    SVGStackup.writePNG(doc, file.getPath, bg = Some(Color.black), size = size)

  }
}
