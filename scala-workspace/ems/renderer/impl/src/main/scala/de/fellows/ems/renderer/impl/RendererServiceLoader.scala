package de.fellows.ems.renderer.impl

import akka.Done
import akka.actor.CoordinatedShutdown
import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.camunda.bridge.api.CamundaBridgeService
import de.fellows.app.converter.kicad.api.KicadConverterService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.renderer.api.RendererService
import de.fellows.ems.renderer.impl.entity.annularring.AnnularRingsEntity
import de.fellows.ems.renderer.impl.entity.distances.DistancesEntity
import de.fellows.ems.renderer.impl.entity.pcbreference.PCBReferenceEntity
import de.fellows.ems.renderer.impl.entity.render.RenderEntity
import de.fellows.ems.renderer.impl.entity.tracewidth.TraceWidthsEntity
import de.fellows.ems.renderer.impl.read.{GerberInformationProcessor, GerberInformationRepository}
import de.fellows.ems.renderer.impl.worker.{
  BoardAnalysisService,
  FileRenderService,
  OutlineService,
  PostProcessingService,
  ProductionAnalysisService,
  ReconciliationService,
  RedisWorker,
  SpecificationRenderService
}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.redislog.RedisComponents
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.{ExecutionContext, Future}
import de.fellows.utils.health.HealthCheckComponents

class RendererServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext): LagomApplication =
    new RendererServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new RendererServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService =
    Some(readDescriptor[RendererService])
}

abstract class RendererServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with RendererServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with RedisComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)
  lazy val pcbService                            = serviceClient.implement[PCBService]
  lazy val assService                            = serviceClient.implement[AssemblyService]
  lazy val layerstackService                     = serviceClient.implement[LayerstackService]
  lazy val camunda                               = serviceClient.implement[CamundaBridgeService]
  lazy val panelService                          = serviceClient.implement[PanelService]
  lazy val kicadConverterService                 = serviceClient.implement[KicadConverterService]

  lazy val pcbListener: PCBListener = wire[PCBListener].withApp(this)
  val ldefListener                  = wire[LayerstackDefinitionListener].withApp(this)

  lazy val redisWorker: RedisWorker = {
    val fileRenderService          = wire[FileRenderService]
    val postProcessingService      = wire[PostProcessingService]
    val reconciliationService      = wire[ReconciliationService]
    val boardAnalysisService       = wire[BoardAnalysisService]
    val productionAnalysisService  = wire[ProductionAnalysisService]
    val outlineService             = wire[OutlineService]
    val specificationRenderService = wire[SpecificationRenderService]

    wire[RedisWorker].startup()
  }

  lazy val pcbRep = wire[RenderRepository]

  lazy val converter: Converter =
    wire[Converter]
      .withApp(this)

  //  lazy val renderFiles: RenderFileService = wire[RenderFileService]
  lazy val srv: RendererServiceImpl = wire[RendererServiceImpl]
  override lazy val lagomServer = serverFor[RendererService](srv)
    .additionalRouter(converter.router)

  //    .additionalRouter(renderFiles.router)
}

trait RendererServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("renderer")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = RendererServiceSerializerRegistry

  persistentEntityRegistry.register(wire[RenderEntity])
  persistentEntityRegistry.register(wire[DistancesEntity])
  persistentEntityRegistry.register(wire[TraceWidthsEntity])
  persistentEntityRegistry.register(wire[AnnularRingsEntity])
  persistentEntityRegistry.register(wire[PCBReferenceEntity])

  implicit val pcbEventProcessor: RenderEventProcessor = wire[RenderEventProcessor]
  readSide.register(pcbEventProcessor)
  //  implicit val layerstackEventProcessor = wire[LayerstackEventProcessor]
  //  readSide.register(layerstackEventProcessor)
  implicit val informationEventProcessor: GerberInformationProcessor = wire[GerberInformationProcessor]
  readSide.register(informationEventProcessor)

  lazy val infoRep = wire[GerberInformationRepository]
}
