package de.fellows.ems.renderer.impl.progress

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.utils.logging.StackrateLogger

import java.util.UUID
import scala.collection.mutable

object RendererProgress {

  val progresses = mutable.HashSet[RenderProgressSink]()

  //  def startRender(sink: RenderProgressSink) = {
  //    progresses.update(sink, true)
  //  }

  def get(assRef: AssemblyReference, file: String)(implicit logger: StackrateLogger): RenderProgressSink =
    progresses.find(x => x.assRef == assRef && x.file == file) match {
      case Some(x) => x
      case None =>
        val rps = new RenderProgressSink(assRef, file)
        progresses.update(rps, true)
        rps
    }

  private[progress] def done(sink: RenderProgressSink) =
    progresses.update(sink, false)

  def getProgressPublisher(version: UUID): Seq[RenderProgressSink] =
    progresses.filter(f => f.assRef.version == version).toSeq

  def getProgressPublisher(version: UUID, file: String): Seq[RenderProgressSink] =
    progresses.filter(f => f.assRef.version == version && f.file == file).toSeq

  case class RenderMessage(
      assRef: AssemblyReference,
      lvl: String,
      tag: String,
      message: String,
      e: Option[Throwable] = None
  )

  object RenderMessage {
    val LVL_DEBUG = "debug"
    val LVL_INFO  = "info"
    val LVL_ERROR = "error"
  }

  //  case class ParsingStarted(override val assRef: AssemblyReference, work: Int) extends RenderMessage
  //
  //  case class ParsingFinished(override val assRef: AssemblyReference) extends RenderMessage
  //
  //  case class RenderStarted(override val assRef: AssemblyReference, renderType: String, work: Int) extends RenderMessage
  //
  //  case class RenderProgressed(override val assRef: AssemblyReference, renderType: String, work: Int, progress: Int, max: Int) extends RenderMessage
  //
  //  case class RenderFinished(override val assRef: AssemblyReference, renderType: String) extends RenderMessage
  //
  //  case class RenderError(override val assRef: AssemblyReference, renderType: Option[String], msg: String) extends RenderMessage

}
