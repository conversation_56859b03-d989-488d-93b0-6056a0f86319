package de.fellows.ems.renderer.impl.gerber

import org.antlr.v4.runtime.ParserRuleContext

object GerberExceptions {

  implicit class ExOps(private val s: String) extends AnyVal {
    def !(implicit v: ParserRuleContext): Nothing = throw GerberProblem(s, Some(v))

    def ?(implicit ctx: ParserRuleContext): Unit =
      println(s"$s @ ${ctx.getStart.getLine}:${ctx.getStart.getCharPositionInLine} ")
    // TODO

    def !! : Nothing = throw GerberProblem(s, None)

    def ~(implicit ctx: ParserRuleContext): Unit =
      println(s"$s @ ${ctx.getStart.getLine}:${ctx.getStart.getCharPositionInLine} ")

  }

  def ??(implicit v: ParserRuleContext): Nothing = throw GerberProblem("Implementation Missing", Some(v))

  def ???? : Nothing = throw new IllegalStateException

}

case class GerberProblem(msg: String, ctx: Option[ParserRuleContext])
    extends IllegalStateException(s"$msg on line ${ctx.map(_.getStart.getLine).getOrElse(0)}:${ctx.map(
        _.getStart.getCharPositionInLine
      ).getOrElse(0)} ${ctx.map(x => s"(Gerber Command ${x.getText})").getOrElse("")}")
