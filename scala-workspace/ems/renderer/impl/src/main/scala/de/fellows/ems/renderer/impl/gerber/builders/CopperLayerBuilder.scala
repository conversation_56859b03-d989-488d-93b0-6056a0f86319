package de.fellows.ems.renderer.impl.gerber.builders

import com.pump.geom.AreaX
import de.fellows.ems.pcb.model.BigPoint
import de.fellows.ems.pcb.model.graphics._
import de.fellows.ems.pcb.model.graphics.parts._
import de.fellows.ems.pcb.model.graphics.tree.{ElementId, QuadTree}
import de.fellows.ems.renderer.impl.gerber._
import de.fellows.ems.renderer.impl.gerber.builders.CopperLayerBuilder.splitRectangle
import org.slf4j.{Logger, LoggerFactory}

import java.awt.Shape
import java.awt.geom.{Area, Rectangle2D}
import scala.collection.mutable

class CopperLayerBuilder(min: GPoint, max: GPoint) extends GerberBuilder {
  implicit val log: Logger = LoggerFactory.getLogger(classOf[CopperLayerBuilder])

  val tree = new QuadTree[Graphic](min, max, 4, maxDepth = Some(8))

  private val rootBounds = this.tree.getRoot().bounds

  def containsClearElements: Boolean = _containsClearElements

  private var _containsClearElements = false
  private val clear                  = Clear()

  def insert(graphic: Graphic): Unit = {
    tree.insert(graphic)
    _containsClearElements ||= graphic.polarity == clear
  }

  override def addFlash(
      aperture: ApertureDefinition,
      shape: Shape,
      polarity: Polarity,
      index: Int,
      target: GPoint,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit =
    insert(Flash(aperture, shape, polarity, ElementId(index), target, attributes, None))

  override def addLine(
      aperture: ApertureDefinition,
      shape: Shape,
      polarity: Polarity,
      index: Int,
      from: GPoint,
      to: GPoint,
      rel: Option[GPoint],
      quad: Option[Quadrant],
      interpolationMode: Option[InterpolationMode],
      attributes: Option[Map[String, Seq[String]]]
  ): Unit = {
    val q = quad match {
      case Some(MultiQuadrant()) => true
      case x                     => false
    }

    interpolationMode match {
      case Some(Linear()) | None =>
        insert(Line(aperture, shape, polarity, ElementId(index), from, to, attributes))
      case Some(Counterclockwise()) =>
        insert(Curve(aperture, shape, polarity, ElementId(index), q, false, from, to, rel, attributes))
      case Some(Clockwise()) =>
        insert(Curve(aperture, shape, polarity, ElementId(index), q, true, from, to, rel, attributes))
    }

  }

  override def addGeneric(
      shape: Shape,
      polarity: Polarity,
      index: Int,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit =
    insert(ComplexRegion(shape, polarity, ElementId(index), attributes, None, None))

  override def addRegion(
      path: Seq[GerberContour],
      polarity: Polarity,
      index: Int,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit =
    path.foreach { p =>
      val polygon = Polygon(p.path, polarity, ElementId(index), attributes, p.flash)
      if (polygon.complexity > 50) {
        val shapeBounds      = polygon.bounds
        val widthPercentage  = shapeBounds.getWidth / rootBounds.getWidth
        val heightPercentage = shapeBounds.getHeight / rootBounds.getHeight

        if (widthPercentage > 0.3 || heightPercentage > 0.3) {
          val area       = new AreaX(p.path)
          val rectangles = splitRectangle(shapeBounds, 3)
          val intersected = rectangles.map { r =>
            val a = new AreaX(r)
            a.intersect(area)
            a
          }

          intersected.foreach { i =>
            insert(ComplexRegion(i, polarity, ElementId(index), attributes, None, None))
          }
        } else {
          insert(polygon)
        }
      } else {
        insert(polygon)
      }
    }

  override def finished(): Unit =
    this.tree.finish()
}

object CopperLayerBuilder {
  def splitRectangle(rect: Rectangle2D, iterations: Int): Seq[Rectangle2D] = {
    def split(
        toSplit: mutable.Queue[Rectangle2D],
        acc: mutable.Queue[Rectangle2D]
    ): Unit =
      while (toSplit.nonEmpty) {
        val rect = toSplit.dequeue()

        val halfWidth  = rect.getWidth / 2
        val halfHeight = rect.getHeight / 2
        val x1         = rect.getMinX
        val y1         = rect.getMinY

        val half1 = new Rectangle2D.Double(x1, y1, halfWidth, halfHeight)
        val half2 = new Rectangle2D.Double(x1 + halfWidth, y1, halfWidth, halfHeight)
        val half3 = new Rectangle2D.Double(x1, y1 + halfHeight, halfWidth, halfHeight)
        val half4 = new Rectangle2D.Double(x1 + halfWidth, y1 + halfHeight, halfWidth, halfHeight)

        acc.addOne(half1)
        acc.addOne(half2)
        acc.addOne(half3)
        acc.addOne(half4)
      }

    val toSplit = mutable.Queue(rect)
    val res     = new mutable.Queue[Rectangle2D](64)

    var idx = 0
    while (idx < iterations) {
      toSplit.addAll(res)
      res.clear()

      split(toSplit, res)

      idx += 1
    }

    res.toVector
  }
}
