package de.fellows.ems.renderer.impl.simple

import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, GPoint, GerberApertureDefinition, Macro}
import de.fellows.ems.renderer.impl.gerber.{Clockwise, Counterclockwise}
import de.fellows.ems.renderer.impl.gerber.macros.{CenterLinePrimitive, CirclePrimitive, MacroConverter, MacroPrimitive, MoirePrimitive, OutlinePrimitive, PolygonPrimitive, ThermalPrimitive, VectorLinePrimitive}
import de.fellows.ems.renderer.impl.simple.SimpleGerberProcessor.findArcParameters
import de.fellows.utils.graphics.PathPrinter
import org.apache.batik.ext.awt.geom.Polygon2D

class SimpleMacroConverter(ad: GerberApertureDefinition, mac: Macro, scaling: Int)
    extends MacroConverter[String](ad, mac, scaling) {

  val workingString = new StringBuilder
  override def result: String =
    workingString.result()

  def toSvg(polys: Polygon2D): String = {
    val b = new StringBuffer()
    PathPrinter.print(
      i = polys.getPathIterator(null),
      s = b,
      invertY = true
    )
    b.toString
  }

  def rotate(rotation: Option[Double]) =
    rotation match {
      case Some(0.0)   => ""
      case Some(value) => s"""transform="rotate(${value})""""
      case None        => ""
    }

  def rotateAndShift(center: GPoint, target: GPoint, degrees: Option[Double]) = {
    val rotation = Math.toRadians(degrees.getOrElse(0.0))
    val sin      = Math.sin(rotation)
    val cos      = Math.cos(rotation)

    GPoint(
      SimpleGerberRenderer.setPrecision(center.x * cos - center.y * sin + target.x),
      SimpleGerberRenderer.setPrecision(center.x * sin + center.y * cos + target.y)
    )
  }

  def drawThermal(x: ThermalPrimitive, target: GPoint): String = {

    val center      = rotateAndShift(x.center, target, x.rotation)
    val outerRadius = x.outerDim / 2
    val innerRadius = x.innerDim / 2
    val halfGap     = x.gap / 2
    val outerIntSq  = (outerRadius * outerRadius) - (halfGap * halfGap)
    val innerIntSq  = (innerRadius * innerRadius) - (halfGap * halfGap)
    val outerInt    = Math.sqrt(outerIntSq)
    val innerInt =
      if (innerIntSq > 0) {
        Math.sqrt(innerIntSq)
      } else {
        halfGap
      }

    val positions = Seq(0, 90, 180, 270)

    val points = Seq(
      GPoint(innerInt, halfGap),
      GPoint(outerInt, halfGap),
      GPoint(halfGap, outerInt),
      GPoint(halfGap, innerInt)
    )

    val svgPath = positions.flatMap { rot =>
      val rotatedPoints = points
        .map { p =>
          rotateAndShift(p, center, Some(rot))
        }
        .map { p =>
          rotateAndShift(p, target, x.rotation)
        }

      val arc = SimpleGerberProcessor.getArcPositions(
        rotatedPoints(1),
        rotatedPoints(2),
        center,
        Counterclockwise()
      )

      val (_, sweepFlag: Int, largeFlag: Int) = findArcParameters(arc)
      Seq(
        M(rotatedPoints(0).x, -rotatedPoints(0).y),
        L(rotatedPoints(1).x, -rotatedPoints(1).y),
        A(
          rx = outerRadius,
          ry = outerRadius,
          rotation = 0,
          largeArcFlag = largeFlag,
          sweepFlag = sweepFlag,
          x = arc.end.x,
          y = -arc.end.y
        ),
        M(rotatedPoints(2).x, -rotatedPoints(2).y),
        L(rotatedPoints(3).x, -rotatedPoints(3).y)
      ) ++ (
        if (points(0) != points(3)) {
          val innerArc = SimpleGerberProcessor.getArcPositions(
            rotatedPoints(3),
            rotatedPoints(0),
            center,
            Clockwise()
          )

          val (_, sweepFlag: Int, largeFlag: Int) = findArcParameters(innerArc)

          Seq(
            A(
              rx = innerRadius,
              ry = innerRadius,
              rotation = 0,
              largeArcFlag = largeFlag,
              sweepFlag = sweepFlag,
              x = innerArc.end.x,
              y = -innerArc.end.y
            )
          )
        } else {
          Seq()
        }
      )

    }
    SimpleGerberRenderer.optimizePath(svgPath)
  }

  def drawMoire(x: MoirePrimitive, target: GPoint): String = {
    def rot(p: GPoint) = rotateAndShift(p, target, x.rotation)

    val rotatedCenter          = rot(x.center)
    val halfCrosshairThickness = x.crosshairThickness / 2
    val halfCrosshairLength    = x.crosshairLength / 2

    var count             = 0
    var remainingDiameter = x.outerDiameter
    val radii             = Seq.newBuilder[Double]
    while (remainingDiameter >= 0 && count < x.maxRings) {

      val rad   = remainingDiameter / 2
      val rHole = rad - x.thickness

      radii += rad

      count += 1
      remainingDiameter = 2 * (rHole - x.gap)
    }

    val vertical = Seq(
      GPoint(x.center.x - halfCrosshairThickness, x.center.y - halfCrosshairLength),
      GPoint(x.center.x + halfCrosshairThickness, x.center.y - halfCrosshairLength),
      GPoint(x.center.x + halfCrosshairThickness, x.center.y + halfCrosshairLength),
      GPoint(x.center.x - halfCrosshairThickness, x.center.y + halfCrosshairLength)
    )
      .map(rot)
      .map(p => L(p.x, -p.y))

    val horizontal =
      Seq(
        GPoint(x.center.x - halfCrosshairLength, x.center.y - halfCrosshairThickness),
        GPoint(x.center.x + halfCrosshairLength, x.center.y - halfCrosshairThickness),
        GPoint(x.center.x + halfCrosshairLength, x.center.y + halfCrosshairThickness),
        GPoint(x.center.x - halfCrosshairLength, x.center.y + halfCrosshairThickness)
      )
        .map(rot)
        .map(p => L(p.x, -p.y))

    val horizontalWithMoves = M(horizontal.head.params(0), horizontal.head.params(1)) +: horizontal :+ Z()
    val verticalWithMoves   = M(vertical.head.params(0), vertical.head.params(1)) +: vertical :+ Z()

    val svgs = radii.result().map { rad =>
      s"""<circle cx="${rotatedCenter.x}" cy="${-rotatedCenter.y}" r="${rad - x.thickness / 2}" stroke-width="${SimpleGerberRenderer.setPrecision(
          x.thickness
        )}" fill="none"> </circle>"""
    } ++ Seq(
      s"""<path d="${SimpleGerberRenderer.optimizePath(verticalWithMoves)}"> </path>""",
      s"""<path d="${SimpleGerberRenderer.optimizePath(horizontalWithMoves)}"> </path>"""
    )

    svgs.mkString("")
  }

  def fill(exposure: Int) =
    if (exposure == 0) {
      """fill="#fff""""
    } else {
      """fill="inherit""""
    }

  override def draw(primitive: MacroPrimitive, target: GPoint): Unit = {

    primitive match {
      case CirclePrimitive(exposure, ellipse, rotation) =>
        workingString append s"""<circle  class="macro circle" cx="${ellipse.getCenterX}" cy="${-ellipse.getCenterY}" r="${ellipse.getWidth / 2}" ${rotate(
            rotation
          )} ${fill(exposure)}></circle>"""
      case VectorLinePrimitive(exposure, width, start, end, rotation, edges) =>
        workingString append s"""<line class="macro vline" x1="${start.x}" y1="${-start.y}" x2="${end.x}" y2="${-end.y}"  stroke-width="${width}" ${rotate(
            rotation
          )} ${fill(exposure)}></line>"""

      case CenterLinePrimitive(exposure, width, height, center, rect, rotation) =>
        val invertedStart = GPoint(center.x - width / 2, -center.y - height / 2)
        workingString append s"""<rect class="macro cline" x="${invertedStart.x}" y="${invertedStart.y}" width="${width}" height="${height}" ${rotate(
            rotation
          )} ${fill(exposure)}></rect>"""
      case OutlinePrimitive(exposure, vertices, polys, rotation) =>
        workingString append s"""<path class="macro outline" d="${toSvg(polys)}" ${rotate(rotation)} ${fill(
            exposure
          )}></path>"""
      case PolygonPrimitive(exposure, polygon, center, rotation) =>
        workingString append s"""<path class="macro polygon" d="${toSvg(polygon)}" ${rotate(rotation)} ${fill(
            exposure
          )}></path>"""
      case x: ThermalPrimitive =>
        workingString append s"""<path class="macro thermal" d="${drawThermal(x, target)}"></path>"""
      case x: MoirePrimitive => workingString append s"""<g class="macro moire">${drawMoire(x, target)}</g>"""
    }
  }
}
