package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.pcb.model.graphics._
import org.apache.batik.ext.awt.geom.ExtendedGeneralPath

import java.awt.Shape
import java.awt.geom.AffineTransform

class GerberBlock(var areas: Seq[BlockPart], xRep: Int, yRep: Int, iDist: BigDecimal, jDist: BigDecimal)
    extends GerberBuilder {

  override def finished(): Unit = {}

  def repeat(builder: GerberBuilder, t: AffineTransform, ctx: GerberContext, tx: Double, ty: Double): Unit =
    for (a <- areas)
      a.repeat(builder, t, ctx, tx, ty)

  //
  //  def repeat(area: Graphic): Area = {
  //    for ((a, p) <- areas) {
  //      import de.fellows.ems.pcb.impl2.Graphics._
  //      p match {
  //        case Dark() => area.add(a)
  //        case Clear() => area.subtract(a)
  //      }
  //    }
  //
  //    area
  //  }
  //
  //  def add(a: Shape, polarity: Polarity) = {
  //    areas = areas :+ (a, polarity)
  //  }

  override def addFlash(
      aperture: ApertureDefinition,
      shape: Shape,
      polarity: Polarity,
      index: Int,
      target: GPoint,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit =
    areas = areas :+ GenericBlock(shape, polarity, attributes) // TODO: use flash

  override def addGeneric(
      shape: Shape,
      polarity: Polarity,
      index: Int,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit = {}

  override def addLine(
      aperture: ApertureDefinition,
      shape: Shape,
      polarity: Polarity,
      index: Int,
      from: GPoint,
      to: GPoint,
      rel: Option[GPoint],
      quad: Option[Quadrant],
      interpolationMode: Option[InterpolationMode],
      attributes: Option[Map[String, Seq[String]]]
  ): Unit =
    areas = areas :+ LineBlock(aperture, shape, polarity, from, to, rel, quad, interpolationMode, attributes)

  override def addRegion(
      path: Seq[GerberContour],
      polarity: Polarity,
      index: Int,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit =
    areas = areas :+ RegionBlock(path, polarity, attributes)
}

sealed trait BlockPart {
  def repeat(b: GerberBuilder, at: AffineTransform, ctx: GerberContext, tx: Double, ty: Double)
}

case class FlashBlock(
    a: ApertureDefinition,
    shape: Shape,
    polarity: Polarity,
    attributes: Option[Map[String, Seq[String]]]
) extends BlockPart {
  override def repeat(b: GerberBuilder, at: AffineTransform, ctx: GerberContext, tx: Double, ty: Double): Unit = {

    val trans = new ExtendedGeneralPath(at.createTransformedShape(shape))

    b.addGeneric(trans, polarity, ctx.getAndIncrementIndex, attributes) // TODO use flash
  }
}

case class LineBlock(
    a: ApertureDefinition,
    shape: Shape,
    polarity: Polarity,
    from: GPoint,
    to: GPoint,
    rel: Option[GPoint],
    quad: Option[Quadrant],
    interpolationMode: Option[InterpolationMode],
    attributes: Option[Map[String, Seq[String]]]
) extends BlockPart {
  override def repeat(b: GerberBuilder, at: AffineTransform, ctx: GerberContext, tx: Double, ty: Double): Unit = {
    val off   = GPoint(tx, ty)
    val trans = new ExtendedGeneralPath(at.createTransformedShape(shape))

    b.addLine(
      a,
      trans,
      polarity,
      ctx.getAndIncrementIndex,
      from + off,
      to + off,
      rel.map(_ + off),
      quad,
      interpolationMode,
      attributes
    )
  }
}

case class RegionBlock(path: Seq[GerberContour], polarity: Polarity, attributes: Option[Map[String, Seq[String]]])
    extends BlockPart {
  override def repeat(b: GerberBuilder, at: AffineTransform, ctx: GerberContext, tx: Double, ty: Double): Unit = {
    val trans = path
      .map { x =>
        GerberContour(new ExtendedGeneralPath(x.path.createTransformedShape(at)), x.flash)
      }

    b.addRegion(trans, polarity, ctx.getAndIncrementIndex, attributes)
  }
}

case class GenericBlock(shape: Shape, polarity: Polarity, attributes: Option[Map[String, Seq[String]]])
    extends BlockPart {
  override def repeat(b: GerberBuilder, at: AffineTransform, ctx: GerberContext, tx: Double, ty: Double): Unit = {
    val trans = new ExtendedGeneralPath(at.createTransformedShape(shape))
    println(s"add transformed shape ${trans.getClass}")
    b.addGeneric(trans, polarity, ctx.getAndIncrementIndex, attributes)
  }
}
