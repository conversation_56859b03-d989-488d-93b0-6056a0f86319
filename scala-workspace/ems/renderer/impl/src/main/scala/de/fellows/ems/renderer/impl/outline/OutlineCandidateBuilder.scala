package de.fellows.ems.renderer.impl.outline

import com.typesafe.config.Config
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.ems.pcb.model.DFM.Properties
import de.fellows.ems.pcb.model.DFM.Properties.DFM
import de.fellows.ems.pcb.model.graphics.tree.{PCBLayerInternalData, TraceIndex}
import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, Geometry, Graphic}
import de.fellows.ems.pcb.model.{
  graphics,
  Density,
  Dimension,
  Format,
  GerberFile,
  Graphic => ModelGraphic,
  GraphicElement,
  LayerConstants,
  Outline
}
import de.fellows.ems.renderer.impl.gerber.builders.OutlineBuilder
import de.fellows.ems.renderer.impl.pool.odb.{ODBGraphicsFactory, ODBRenderer}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.internal.FileWriter
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.meta.{DecimalProperty, MetaInfo, Property, StringProperty}
import de.fellows.utils.{DebugUtils, Units}
import de.luminovo.odb.odbpp.model.{MMUnit, ODBFeatures}
import org.antlr.v4.runtime.tree.ParseTreeWalker
import org.apache.batik.svggen.SVGGraphics2D

import java.awt.geom.{Path2D, Rectangle2D}
import java.io.File
import java.text.DecimalFormat
import java.nio.file.Path
import java.util.UUID
import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}
import scala.math.BigDecimal.RoundingMode
import scala.util.{Failure, Success, Try}

case class TransientOutlineCandidate(
    graphic: ModelGraphic,
    path: Path2D,
    bounds: Rectangle2D,
    aperture: Option[ApertureDefinition],
    file: Option[UUID]
)

case class TransientOutlineCandidateWithDensity(candidate: TransientOutlineCandidate, density: Density) {
  def properties(scale: Int = 3): Seq[Property] = {
    val w  = candidate.bounds.getWidth / candidate.graphic.format.scaling.getOrElse(1.0)
    val h  = candidate.bounds.getHeight / candidate.graphic.format.scaling.getOrElse(1.0)
    val wr = BigDecimal(w).setScale(scale, RoundingMode.HALF_UP).doubleValue
    val hr = BigDecimal(h).setScale(scale, RoundingMode.HALF_UP).doubleValue
    val props: Seq[Property] = Seq(
      DecimalProperty(DFM.WIDTH, wr),
      DecimalProperty(DFM.HEIGHT, hr),
      StringProperty(Properties.DFM.UNIT, Units.MILLIMETER),
      DecimalProperty(
        Properties.DFM.AREA,
        BigDecimal(wr * hr).setScale(scale, RoundingMode.HALF_UP).doubleValue
      )
    )
    props
  }

}

class OutlineCandidateBuilder extends StackrateLogging {

  private def createFallbackOutlines(
      traces: Seq[Graphic],
      copperFiles: Iterable[PCBLayerInternalData],
      format: Format
  ): Seq[TransientOutlineCandidate] =
    Seq(
      traces.map(_.bounds)
        .filter(b => b.getWidth > 0 && b.getHeight > 0)
        .reduceOption(_ createUnion _),
      copperFiles.flatMap(_.tree.collect(Seq(_))).map(_.bounds)
        .filter(b => b.getWidth > 0 && b.getHeight > 0)
        .reduceOption(_ createUnion _)
    ).flatten.map { ol =>
      OutlineCandidateBuilder.createOutlineCandidateForBounds(format, ol)
    }

  // TODO: add a second pass for more complicated possible files
  def build(
      mechanicalFiles: Seq[GerberFile],
      copperFiles: Seq[GerberFile],
      copperData: Iterable[PCBLayerInternalData],
      assemblyReference: AssemblyReference
  )(implicit
      rctx: ExecutionContext
  ): Future[Seq[TransientOutlineCandidateWithDensity]] = {
    val traces: Seq[Graphic] =
      copperData.flatMap(OutlineCandidateBuilder.getTracesOnly).toSeq

    val traceBoundsOption = traces.map(_.bounds)
      .filter(b => b.getWidth > 0 && b.getHeight > 0)
      .reduceOption(_.createUnion(_))

    val possibleOutlineFiles =
      if (mechanicalFiles.nonEmpty) {
        mechanicalFiles
      } else {
        copperFiles
      }

    copperData.find(_.format.isDefined).flatMap(_.format) match {
      case Some(format) =>
        Future.traverse(possibleOutlineFiles)(createOutlineGraphics)
          .map { nestedOutlines =>
            val filteredOutlines = filterOutlines(nestedOutlines.flatten, traceBoundsOption)

            val filteredOrFallbackOutlines =
              if (filteredOutlines.isEmpty) {
                val fallbackOutlines = createFallbackOutlines(traces, copperData, format)
                filterOutlines(fallbackOutlines, traceBoundsOption)
              } else {
                filteredOutlines
              }

            DebugUtils.timed(s"create outline densities for ${filteredOrFallbackOutlines.size} outlines") {
              val densities =
                DensityCalculator.getDensityByGraphics(
                  copperGraphics = traces,
                  outlineRender = filteredOrFallbackOutlines.map(_.graphic).zipWithIndex,
                  debug = Some((assemblyReference.team, assemblyReference.id, "density"))
                ).toMap

              filteredOrFallbackOutlines.zipWithIndex.map {
                case (candidate, idx) =>
                  TransientOutlineCandidateWithDensity(
                    candidate,
                    densities(idx)
                  )
              }
            }(logger.logger)
          }

      case None =>
        logger.error("No format found for copper files")
        Future.successful(Seq.empty)
    }

  }

  private def filterOutlines(
      outlines: Seq[TransientOutlineCandidate],
      traceBounds: Option[Rectangle2D]
  ): Seq[TransientOutlineCandidate] = {
    def outlineFilter(traceBounds: Rectangle2D)(candidate: TransientOutlineCandidate): Boolean = {
      // remove all candidates that are not even intersecting with the copper bounds
      // most useful for layers with documentation somewhere
      val intersects = traceBounds.intersects(candidate.bounds)
      // if the candidate is too small compared to the copper bounds, skip
      val sizeRatio = Geometry.area(candidate.bounds) / Geometry.area(traceBounds)

      // this is a magic number specifying the cutoff ratio between the sizes of the outline and the copper.
      // the smaller it is, the more invalid outlines we will have to check.
      // The higher it is the likelier it is that we miss a valid outline.
      val magicCutoff = 0.3

      intersects && !sizeRatio.isNaN && sizeRatio > magicCutoff
    }

    traceBounds match {
      case Some(traceBounds) => outlines.filter(outlineFilter(traceBounds))
      case None              => outlines
    }
  }

  def createOutlineGraphics(f: GerberFile)(implicit
      rctx: ExecutionContext
  ): Future[Seq[TransientOutlineCandidate]] = {
    val graphics = Renderer.createGraphics

    val r: Future[Seq[TransientOutlineCandidate]] =
      if (f.fType.mimeType == LayerConstants.Mime.gerber) {
        renderGerberOutlines(f, graphics)
      } else if (f.fType.mimeType == LayerConstants.Mime.odblinerecord && f.fType.fileType == LayerConstants.OUTLINE) {
        renderProfile(f, f.path.toJavaPath, graphics)
      } else if (f.fType.mimeType == LayerConstants.Mime.odblayer) {
        renderODBOutlines(f, rctx, graphics)
      } else {
        Future.successful(Seq())
      }

    r.recover { exc =>
      logger.warn(s"failed to build outline for ${f.path}", exc)
      Seq()
    }
  }

  def renderODBOutlines(
      f: GerberFile,
      rctx: ExecutionContext,
      graphics: SVGGraphics2D
  ): Future[Seq[TransientOutlineCandidate]] = {

    val profileForThisLayer = f.path.toJavaPath.resolve("profile")

    if (profileForThisLayer.toFile.exists()) {
      logger.info(s"creating odb outline candidate: ${profileForThisLayer}")

      renderProfile(f, profileForThisLayer, graphics)
    } else {
      logger.info(s"no profile file found for layer: ${f.name}")
      Future.successful(Seq())
    }

  }

  private def renderProfile(f: GerberFile, profileForThisLayer: Path, graphics: SVGGraphics2D) = {
    val renderer = new ODBRenderer(profileForThisLayer.toFile)
    val candidates = renderer.buildFeatures(Some(MMUnit)) match {
      case Left(value) =>
        Seq()
      case Right(features) =>
        // according to the spec, profile files should only have one record, which is surface. just in case we'll render all surface records as candidates
        features.records.collect {
          case x @ ODBFeatures.SurfaceRecord(_, polygons, polarity, dcode, attributes) =>
            ODBGraphicsFactory.render(polygons, x, 1, renderer.internalScale).map { outlineGraphic =>
              val svgPath = graphics.getShapeConverter.toSVG(outlineGraphic.s).getAttribute("d")
              val path2D  = new Path2D.Double()
              path2D.append(outlineGraphic.s, false)

              TransientOutlineCandidate(
                graphic = ModelGraphic(
                  viewbox = Dimension.of(outlineGraphic.bounds),
                  format = Format(
                    dimension = Some(Dimension.of(outlineGraphic.bounds)),
                    unit = "mm",
                    resolution = 0,
                    complexity = Some(1),
                    scaling = Some(renderer.internalScale)
                  ),
                  paths = Seq(Seq(GraphicElement(path = Some(svgPath), tag = None, use = None, None))),
                  count = 1,
                  defs = Seq()
                ),
                path = path2D,
                bounds = outlineGraphic.bounds,
                aperture = None,
                file = Some(f.id)
              )
            } match {
              case Left(value) =>
                logger.error(s"failed to render outline ${f.path.filename}: ${value}")
                None
              case Right(value) => Some(value)
            }

        }.flatten
    }

    logger.info(s"built ${candidates.size} odb outline candidates for ${f.path}")
    Future.successful(candidates)
  }

  private def renderGerberOutlines(
      f: GerberFile,
      graphics: SVGGraphics2D
  )(implicit ec: ExecutionContext): Future[Seq[TransientOutlineCandidate]] =
    new Renderer(new File(f.path.toPath), ec, None).buildMetaData().map { renderer =>
      Try {
        val dim    = renderer.buildDimensions()
        val walker = new ParseTreeWalker
        val l      = OutlineBuilder.DEFAULT_BUILDER(dim)

        walker.walk(l, renderer.p.gerber())

        val outlineDescriptions = l.getOutlines()

        outlineDescriptions.map { ol =>
          val bounds    = Try(Geometry.bounds(ol.path)).toOption.flatten.getOrElse(ol.path.getBounds2D)
          val dimension = Dimension.of(bounds)

          Renderer.prepareGraphics(graphics, dimension)
          val olshape = graphics.getShapeConverter.toSVG(ol.path)
          val paths =
            Seq(Seq(GraphicElement(path = Some(olshape.getAttribute("d")), tag = None, use = None, None)))

          val format = Format(
            dimension = Some(dimension),
            unit = renderer.meta.gContext.format.get.getStardizedUnit.unit,
            resolution = renderer.meta.gContext.format.get.resolution(),
            complexity = None,
            scaling = Some(renderer.meta.gContext.format.get.getImageScaling)
          )

          val cand = TransientOutlineCandidate(
            ModelGraphic(
              viewbox = dimension,
              format = format,
              paths = paths,
              count = 1,
              defs = Seq()
            ),
            ol.path,
            bounds,
            Some(ol.intialAperture),
            Some(f.id)
          )

          cand
        }
      } match {
        case Failure(exception) =>
          logger.error("failed to build outline", exception)
          Seq()
        case Success(value) => value
      }
    }
}

object OutlineCandidateBuilder {
  protected def getTracesOnly(data: PCBLayerInternalData): Vector[graphics.Graphic] = {
    val hasPads: mutable.HashSet[String] = mutable.HashSet()
    data.traces.foreach(
      _.foreach { t =>
        if (t.containsPads.contains(true))
          hasPads.add(t.traceID)
      }
    )
    val traceIndex = TraceIndex(data.traces)

    data.tree.collect { g =>
      val trace = traceIndex(g.index.x)
      trace match {
        case Some(value) =>
          if (hasPads.contains(value.traceID)) {
            Some(g)
          } else {
            None
          }
        case None => None
      }
    }
  }

  def createOutlineCandidateForBounds(format: Format, ol: Rectangle2D): TransientOutlineCandidate = {
    val dim      = Dimension.of(ol)
    val graphics = Renderer.createGraphics
    val p        = new Path2D.Double()
    p.append(ol, false)

    val olshape = graphics.getShapeConverter.toSVG(p)

    val paths =
      Seq(Seq(GraphicElement(path = Some(olshape.getAttribute("d")), tag = None, use = None, None)))

    TransientOutlineCandidate(
      graphic = ModelGraphic(
        viewbox = dim,
        format = format,
        count = 1,
        paths = paths,
        defs = Seq()
      ),
      path = p,
      bounds = ol,
      aperture = None,
      file = None
    )
  }

  def writeAndCreateOutline(
      c: TransientOutlineCandidateWithDensity,
      a: AssemblyReference,
      userChoice: Boolean = false
  )(implicit conf: Config): Outline = {
    val id   = UUID.randomUUID()
    val path = Outline.graphicPath(a, id)

    FileWriter.writeCompressedObject(path.toJavaPath, c.candidate.graphic)

    Outline(
      id = id,
      file = c.candidate.file,
      path = path,
      userChoice = userChoice,
      metaInfo = MetaInfo(c.properties().groupBy(_.name).map(x => x._1 -> x._2.head)),
      density = c.density,
      score = OutlineCandidateHeuristic.score(c.density)
    )
  }

}
