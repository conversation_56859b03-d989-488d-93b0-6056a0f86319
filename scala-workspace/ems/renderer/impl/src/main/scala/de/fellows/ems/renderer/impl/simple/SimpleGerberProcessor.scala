package de.fellows.ems.renderer.impl.simple

import de.fellows.ems.gerber.parser.GerberParser
import de.fellows.ems.pcb.model.graphics.Geometry.ArcPositions
import de.fellows.ems.pcb.model.graphics._
import de.fellows.ems.renderer.impl.gerber._
import de.fellows.ems.renderer.impl.gerber.builders.{BasicFileListener, BoundsBuilder}
import de.fellows.ems.renderer.impl.simple.SimpleGerberProcessor.{findArcParameters, getArcPositions}
import de.fellows.utils.graphics.PathPrinter
import org.antlr.v4.runtime.ParserRuleContext

import java.awt.geom.Rectangle2D
import scala.collection.mutable

case class SVGTransform(commands: Seq[SVGTransformCommand], origin: Option[GPoint]) {}
sealed trait SVGTransformCommand {
  def toSVGString: String

}

case class SVGTranslateCommand(x: Double, y: Double) extends SVGTransformCommand {
  override def toSVGString: String = s"translate($x,$y)"
}
case class SVGScaleCommand(x: Double, y: Double) extends SVGTransformCommand {
  override def toSVGString: String = s"scale($x,$y)"
}
case class SVGRotateCommand(angle: Double, x: Option[Double], y: Option[Double]) extends SVGTransformCommand {
  override def toSVGString: String =
    s"rotate($angle${x.map(x => s",$x").getOrElse("")}${y.map(y => s",$y").getOrElse("")})"
}

/** A gerber layer.
  *
  * layers separate the dark/clear stages of a gerber file.
  * If a gerber switches to CLEAR, it clears *everything* that was drawn so far, but not what will be drawn in future DARK stages.
  *
  * A SVG mask on the other hand clears everything. To achieve the result we want to create bunch of nested SVG groups,
  * each with a mask of the clear elements of that stage.
  * previous layers should be nested into this group, so that the mask applies to those groups as well.
  *
  * @param id
  * @param svg
  * @param mask
  * @param bounds
  * @param transform
  */
case class GerberMaskLayer(
    id: String,
    svg: mutable.Builder[SVGCommand, Seq[SVGCommand]],
    mask: mutable.Builder[SVGCommand, Seq[SVGCommand]],
    bounds: BoundsBuilder,
    transform: Option[SVGTransform] = None
) {
  def hardTranslate(tx: Double, ty: Double): GerberMaskLayer = {
    val translatedSvg  = Seq.newBuilder[SVGCommand]
    val translatedMask = Seq.newBuilder[SVGCommand]

    translatedSvg ++= svg.result().map(_.hardTranslate(tx, ty))
    translatedMask ++= mask.result().map(_.hardTranslate(tx, ty))

    val originalBounds   = this.bounds.result()
    val translatedBounds = new BoundsBuilder()
    translatedBounds.extend(new Rectangle2D.Double(
      originalBounds.getMinX + tx,
      originalBounds.getMinY + ty,
      originalBounds.getWidth,
      originalBounds.getHeight
    ))

    GerberMaskLayer(
      id = id,
      svg = translatedSvg,
      mask = translatedMask,
      bounds = translatedBounds,
      transform = transform
    )
  }
}

object GerberMaskLayer {
  def apply(id: String): GerberMaskLayer =
    GerberMaskLayer(id, Seq.newBuilder[SVGCommand], Seq.newBuilder[SVGCommand], new BoundsBuilder(), None)
  def apply(id: String, transform: Option[SVGTransform]): GerberMaskLayer =
    GerberMaskLayer(id, Seq.newBuilder[SVGCommand], Seq.newBuilder[SVGCommand], new BoundsBuilder(), transform)
}

class SimpleGerberProcessor(ids: IDGenerator) extends BasicFileListener {
  val apertures = new SimpleApertureStore

  // we keep a list of "layers" of graphics. When the polarity is switched to clear, it should only clear what is drawn so far.
  // if it is switched to dark again, we create a new layer to keep this info
  val layers =
    Seq.newBuilder[GerberMaskLayer]

  var steprepeats = Seq.newBuilder[GerberMaskLayer]

  var currentLayer: GerberMaskLayer = null

  var currentStepRepeat: GerberMaskLayer = null

  def newLayer(overrideStepRepeat: Boolean = false, initialTransformation: Option[SVGTransform] = None): Unit =
    if (gContext.steprepeat.isDefined && !overrideStepRepeat) {
      val gl = GerberMaskLayer(ids.generateId() + "-sr", initialTransformation)
      steprepeats += gl
      currentStepRepeat = gl
    } else {
      val gl = GerberMaskLayer(ids.generateId(), initialTransformation)
      layers += gl
      currentLayer = gl
    }

  override def enterGerber(ctx: GerberParser.GerberContext): Unit =
    newLayer()

  override def apReg: ApertureRegistry = apertures

  private def repeat(value: Seq[GerberMaskLayer], tx: Double, ty: Double): Unit =
    value.foreach { gl =>
      val translatedLayer: GerberMaskLayer = gl.hardTranslate(tx, -ty)

      this.currentLayer.svg ++= translatedLayer.svg.result()
      this.currentLayer.mask ++= translatedLayer.mask.result()
      this.currentLayer.bounds.extend(translatedLayer.bounds.result())

      newLayer(
        true
      )
    }

  override def closeStepRepeat(implicit ctx: ParserRuleContext): Unit = {
    newLayer(true)
    val stepRepeatLayers = this.steprepeats.result()
    val sr               = this.gContext.steprepeat.get
    for (x <- 0 until sr.xRep)
      for (y <- 0 until sr.yRep) {

        val tx = (sr.iDist * scaling * x)
        val ty = (sr.jDist * scaling * y)

        repeat(stepRepeatLayers, tx.doubleValue, ty.doubleValue)
      }

    this.steprepeats = Seq.newBuilder[GerberMaskLayer]
    this.currentStepRepeat = null

  }

  override def openStepRepeat(implicit ctx: ParserRuleContext): Unit =
    this.newLayer()

  override def polarityChanged(from: Polarity, to: Polarity): Unit =
    if (from == Dark() && to == Clear()) {
      // the file switched from draw to clear, so we stay in the same layer.
    } else {
      newLayer()
    }

  private def addRegion(
      contour: Seq[GerberContour],
      polarity: Polarity,
      idx: Int,
      objectAttributes: Option[Map[String, Seq[String]]]
  ): Unit = {
    val layer = getCurrentLayer

    // TODO: Refactor base listener to not use GerberContour. We should build it here ourselves
    contour.foreach { c =>
      val sb     = Seq.newBuilder[SVGPathInstruction]
      val path   = c.path
      val bounds = BoundsBuilder.invertY(path.getBounds2D)
      layer.bounds.extend(bounds)
      PathPrinter.walkInt(
        path.getPathIterator(null),
        (inst, params) => sb += SVGPathInstruction(inst, params),
        invertY = true
      )
      addCommand(layer, SVGPolygonCommand(sb.result()))
    }

  }

  override def endRegion(): Unit =
    addRegion(this.gContext.region.get, this.gContext.polarity, gContext.getAndIncrementIndex, objectAttributes)

  override def flash(gerberTarget: GPoint)(implicit v: ParserRuleContext): Unit = {
    val target = gerberTarget.invertY()

    this.gContext.aperture match {
      case Some(value) =>
        val cmd = SVGUseCommand(value.dCode, target.x, target.y, value)
        val layer =
          getCurrentLayer

        val bounds1        = getApertureBounds(value, target)
        val apertureBounds = bounds1.getOrElse(new Rectangle2D.Double(0, 0, 0, 0))

        layer.bounds.extend(apertureBounds)

        addCommand(layer, cmd)

      case None => logger.warn("No Aperture Selected")
    }
  }

  private def getApertureBounds(value: GerberApertureDefinition, target: GPoint): Option[Rectangle2D] =
    value.bounds(target, scaling) match {
      case None =>
        macroDefinitions.get(value.template).flatMap { mac =>
          Some(
            new SimpleMacroConverter(value, mac, scaling)
              .bounds(target)
          )
        }
      case x =>
        x
    }

  private def getCenterCandidates(from: GPoint, to: GPoint, i: Double, j: Double, radius: Double): Seq[GPoint] = {

    val (dx, dy) = (to.x - from.x, to.y - from.y)
    val (sx, sy) = (to.x + from.x, to.y + from.y)

    val distance = from.distance(to)

    if (radius <= distance / 2) {
      Seq(GPoint(
        from.x + dx / 2,
        from.y + dy / 2
      ))
    } else {
      val factor             = Math.sqrt((4 * Math.pow(radius, 2)) / Math.pow(distance, 2) - 1)
      val (xBase, yBase)     = (sx / 2, sy / 2)
      val (xAddend, yAddend) = ((dy * factor) / 2, (dx * factor) / 2)

      Seq(
        GPoint(
          xBase + xAddend,
          yBase - yAddend
        ),
        GPoint(
          xBase - xAddend,
          yBase + yAddend
        )
      )

    }

  }

  def addCommand(layer: GerberMaskLayer, command: SVGCommand): Unit =
    gContext.polarity match {
      case Dark() =>
        layer.svg += command
      case Clear() =>
        layer.mask += command
    }

  override def doInterpolate(point: GPoint, i: Option[Double], j: Option[Double])(implicit
      v: GerberParser.OpContext
  ): Unit =
    for {
      aperture <- this.gContext.aperture
      from     <- gContext.point.map(_.precision(2))
      to       <- createFrom.map(_.precision(2))
    } yield {
      val mode = this.gContext.interpolation.getOrElse(Linear())
      val path = mode match {
        case Linear()                                                   => handleLinearPlotting(from, to)
        case Clockwise() | Counterclockwise() if i.isEmpty && j.isEmpty => handleLinearPlotting(from, to)
        case Clockwise() | Counterclockwise()                           => drawArc(i, j, from, to, mode)
      }

      val layer = getCurrentLayer

      addCommand(layer, SVGPathCommand(path, aperture))

    }

  private def handleLinearPlotting(from: GPoint, to: GPoint): Seq[SVGPathInstruction] =
    if (from == to) {
      Seq()
    } else {
      val invertedFrom = from.invertY()
      val invertedTo   = to.invertY()
      currentLayer.bounds.extend(invertedFrom)
      currentLayer.bounds.extend(invertedTo)

      Seq(
        M(invertedFrom.x, invertedFrom.y),
        L(invertedTo.x, invertedTo.y)
      )
    }

  private def getCurrentLayer =
    if (gContext.steprepeat.isDefined) {
      this.currentStepRepeat
    } else {
      this.currentLayer
    }

  private def drawArc(
      i: Option[Double],
      j: Option[Double],
      from: GPoint,
      to: GPoint,
      mode: InterpolationMode
  ): Seq[SVGPathInstruction] = {
    val _i = i.getOrElse(0.0)
    val _j = j.getOrElse(0.0)

    val radius =
      Math.pow(Math.pow(_i, 2) + Math.pow(_j, 2), 0.5)

    val ambiguousCenter = this.gContext.quadrant.contains(SingleQuadrant())

    val arc =
      if (ambiguousCenter) {
        getCenterCandidates(from, to, _i, _j, radius)
          .map { centerCandidate =>
            getArcPositions(from, to, centerCandidate, mode)
          }
          .sortWith { (a, b) =>
            Math.abs(a.endAngle - a.startAngle) < Math.abs(b.endAngle - b.startAngle)
          }.head
      } else {
        val _center = GPoint(
          (from.x + _i),
          (from.y + _j)
        )

        getArcPositions(from, to, _center, mode)
      }

    val (absSweep: Double, sweepFlag: Int, largeFlag: Int) = findArcParameters(arc)

    val sb = Seq.newBuilder[SVGPathInstruction]

    sb += M(from.x, -from.y)

    if (from == to && ambiguousCenter) {
      // from and to are the same, just draw a small line
      sb += L(to.x, -to.y)
    } else {

      if (absSweep == 2 * Math.PI) {
        // a full circle needs two separate arcs
        val (mx, my) = (2 * arc.center.x - to.x, -(2 * arc.center.y - to.y))
        logger.info(s"Full circle $radius $absSweep==${2 * Math.PI} $from $to $sweepFlag $largeFlag")
        sb += A(radius, radius, 0, 0, sweepFlag, mx, my)
        sb += A(radius, radius, 0, 0, sweepFlag, to.x, -to.y)
      } else {
        sb += A(
          rx = radius,
          ry = radius,
          rotation = 0,
          largeArcFlag = largeFlag,
          sweepFlag = sweepFlag,
          x = to.x,
          y = -to.y
        )
      }
      currentLayer.bounds.extend(new Rectangle2D.Double(
        arc.center.x - radius,
        (-arc.center.y) - radius,
        radius * 2,
        radius * 2
      ))

    }
    sb.result()
  }

}

object SimpleGerberProcessor {
  @deprecated("use Geometry.getArcPositions instead")
  def getArcPositions(from: GPoint, to: GPoint, center: GPoint, mode: InterpolationMode): ArcPositions =
    Geometry.getArcPositions(from, to, center, mode == Clockwise())

  @deprecated("use Geometry.findArcParameters instead")
  def findArcParameters(arc: ArcPositions): (Double, Int, Int) = {
    val params = Geometry.findArcParameters(arc)
    (params.absSweep, params.sweepFlag, params.largeFlag)
  }
}
