package de.fellows.ems.renderer.impl

import akka.Done
import de.fellows.ems.pcb.model.graphics.Graphic
import de.fellows.ems.pcb.model.{Graphic => ApiGraphic, _}
import de.fellows.ems.renderer.impl.gerber.{Inch, MeasurementUnit, Millimetre}
import de.fellows.utils.{FilePath, Units}
import org.apache.batik.svggen.SVGGraphics2D
import org.w3c.dom.Document

import scala.collection.mutable
import scala.concurrent.Future

object RendererHelper {

  case class HelperContext(
      persist: (Map[String, FilePath], Format) => Future[Done],
      persistPreview: (FilePath) => Future[Done],
      previewPath: () => FilePath,
      qtPath: () => FilePath,
      renderPath: (String, ApiGraphic) => FilePath,
      handleSVG: (SVGGraphics2D, Document) => Future[Done],
      margin: Double = 0
  )

  def stretchDim(od: Dimension, n: Dimension): Dimension =
    Dimension(
      BigPoint(od.min.x.min(n.min.x), od.min.y.min(n.min.y)),
      BigPoint(od.max.x.max(n.max.x), od.max.y.max(n.max.y))
    )

  def unit(get: MeasurementUnit): String =
    get match {
      case Inch()       => Units.INCH
      case Millimetre() => Units.MILLIMETER
    }

  def quadTreePath(f: GerberFile) = {
    val quadTreePath = f.path.copy(base = "data", filename = s"${f.path.filename}.layerdata.json.gzip")
    quadTreePath
  }

  def mergeAttributes[X, D](a: Option[Map[X, D]], b: Option[Map[X, D]]): Option[Map[X, D]] =
    (a.getOrElse(Map()) ++ b.getOrElse(Map())) match {
      case x if x.isEmpty => None
      case x              => Some(x)
    }

  def mergeAttributes(a: Vector[Graphic]): Option[Map[String, Seq[String]]] = {
    val map = mutable.Map[String, Seq[String]]()

    a.foreach(graphic => mergeAttributes(map, graphic))

    val result = map.toMap
    Option.when(result.nonEmpty)(result)
  }

  def mergeAttributes(map: mutable.Map[String, Seq[String]], graphic: Graphic): Unit =
    graphic.attributes match {
      case Some(attributes) =>
        attributes.foreach { case (key, value) =>
          val existing = map.getOrElse(key, Seq.empty)
          map.put(key, existing ++ value)
        }

      case None =>
    }
}
