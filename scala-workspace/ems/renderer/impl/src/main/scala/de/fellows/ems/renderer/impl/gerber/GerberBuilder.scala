package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.pcb.model.graphics.{ApertureDefinition, GPoint, Polarity}

import java.awt.Shape

trait GerberBuilder {

  def addFlash(
      aperture: ApertureDefinition,
      shape: Shape,
      polarity: Polarity,
      index: Int,
      target: GPoint,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit

  def addGeneric(
      shape: Shape,
      polarity: Polarity,
      index: Int,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit

  def addLine(
      aperture: ApertureDefinition,
      shape: Shape,
      polarity: Polarity,
      index: Int,
      from: GPoint,
      to: GPoint,
      rel: Option[GPoint],
      quad: Option[Quadrant],
      interpolationMode: Option[InterpolationMode],
      attributes: Option[Map[String, Seq[String]]]
  ): Unit

  def addRegion(
      path: Seq[GerberContour],
      polarity: Polarity,
      index: Int,
      attributes: Option[Map[String, Seq[String]]]
  ): Unit

  def finished(): Unit

}
