package de.fellows.ems.renderer.impl.outline

import com.typesafe.config.ConfigFactory
import de.fellows.app.assembly.commons.AssemblyFiles
import de.fellows.ems.pcb.model.{graphics, BigPoint, Density, Dimension, Graphic}
import de.fellows.ems.renderer.impl.{Rasterizer, SvgOps}
import de.fellows.ems.renderer.impl.render.Renderer
import de.fellows.utils.UUIDUtils.UUIDImprovements
import org.apache.batik.transcoder.TranscoderInput
import org.w3c.dom.Element

import java.awt.geom.{AffineTransform, Rectangle2D}
import java.awt.image.BufferedImage
import java.awt.{Color, Shape}
import java.util.UUID
import javax.imageio.ImageIO
import scala.util.{Failure, Success, Try}

/** calculates the density of copper in a potential outline by rasterizing the images and counting overlapping pixels.
  */
object DensityCalculator {

  def getDensityByGraphics(
      copperGraphics: Seq[graphics.Graphic],
      outlineRender: Graphic,
      debug: Option[(String, UUID, String)]
  ): Density =
    getDensityByGraphics(copperGraphics, Seq((outlineRender, 0)), debug).head._2

  /** get densities for multiple outline candidates.
    *
    * @param copperGraphics
    * @param outlineRender
    * @return
    */
  def getDensityByGraphics(
      copperGraphics: Seq[graphics.Graphic],
      outlineRender: Seq[(Graphic, Int)],
      debug: Option[(String, UUID, String)]
  ): Seq[(Int, Density)] =
    getDensityByGenerics(
      copperGraphics,
      outlineRender,
      (x: graphics.Graphic) => x.shape,
      (x: graphics.Graphic) => x.bounds,
      debug
    )

  def getDensityByShapes(
      copperGraphics: Seq[Shape],
      outlineRender: Graphic,
      debug: Option[(String, UUID, String)]
  ): Density =
    getDensityByGenerics(
      copperGraphics,
      Seq((outlineRender, 0)),
      (x: Shape) => x,
      (x: Shape) => x.getBounds2D,
      debug
    ).head._2

  private def getDensityByGenerics[X](
      copperGraphics: Seq[X],
      outlineRender: Seq[(Graphic, Int)],
      shapes: X => Shape,
      bounds: X => Rectangle2D,
      debug: Option[(String, UUID, String)]
  ): Seq[(Int, Density)] =
    if (outlineRender.nonEmpty) {
      val copperbounds = copperGraphics.map(bounds)
        .filter(b => b.getWidth > 0 && b.getHeight > 0)
        .reduceOption((a, b) => a.createUnion(b))
      val olbounds =
        outlineRender.flatMap(_._1.format.dimension).map(_.rectangle).reduceOption((a, b) => a.createUnion(b))

      val margin = 100

      val dimension =
        Seq(copperbounds, olbounds).flatten.reduceOption((a, b) => a.createUnion(b)).map(Dimension.of)

      dimension.toSeq.flatMap { d =>
        val width       = Math.abs((d.size.x.doubleValue + (margin * 2)))
        val height      = Math.abs((d.size.y.doubleValue + (margin * 2)))
        val scaledWidth = 1000.0
        val scale       = scaledWidth / width

        val coppersImage = createImage(
          gr = copperGraphics.map(shapes),
          width = width,
          height = height,
          scale = scale,
          dimension = d,
          margin = margin
        )
        debug.foreach { d =>
          val f = AssemblyFiles.getAssemblyBaseFolder(team = d._1, assembly = d._2)(ConfigFactory.load()).resolve(
            s"debug/${d._3}-${UUID.randomUUID().short()}.png"
          )
          f.toFile.getParentFile.mkdirs()
          ImageIO.write(coppersImage, "png", f.toFile)
        }
        //
        outlineRender.map { ol =>
          (
            ol._2,
            getDensity(
              outlineRender = ol._1,
              coppers = coppersImage,
              inverted = None,
              dimension = d,
              margin = margin,
              scaledWidth = (width * scale),
              debug = debug
            )
          )
        }
      }
    } else {
      Seq.empty
    }

  private def createImage(
      gr: Seq[Shape],
      width: Double,
      height: Double,
      scale: Double,
      dimension: Dimension,
      margin: Double
  ): BufferedImage = {

    val coppersImage = new BufferedImage(
      (width * scale).intValue(),
      (height * scale).intValue(),
      BufferedImage.TYPE_BYTE_BINARY
    )

    val cg2d = coppersImage.createGraphics()
    cg2d.fillRect(0, 0, coppersImage.getWidth, coppersImage.getHeight)
    cg2d.setColor(Color.BLACK)

    cg2d.transform(AffineTransform.getScaleInstance(scale, scale))
    cg2d.transform(AffineTransform.getTranslateInstance(
      -dimension.min.x.doubleValue + margin,
      -dimension.min.y.doubleValue + margin
    ))

    gr.foreach { s =>
      cg2d.fill(s)
    }

    cg2d.dispose()
    coppersImage

  }

  private def getDensity(
      outlineRender: Graphic,
      coppers: BufferedImage,
      inverted: Option[Boolean],
      dimension: Dimension,
      margin: Double,
      scaledWidth: Double,
      debug: Option[(String, UUID, String)]
  ): Density = {

    val outline = Renderer.createSvg(
      Seq(outlineRender),
      graphictransformer = SvgTransformer.translateTransform(dimension.min, margin)
    )
    SvgOps.setSvgDocumentSize(outline, dimension, margin)

    val outlineGraphic = Rasterizer.toBMP(new TranscoderInput(outline), (scaledWidth).floatValue())

    debug.foreach { d =>
      val f = AssemblyFiles.getAssemblyBaseFolder(team = d._1, assembly = d._2)(ConfigFactory.load()).resolve(
        s"debug/${d._3}-outline-${UUID.randomUUID().short()}.png"
      )
      f.toFile.getParentFile.mkdirs()
      ImageIO.write(outlineGraphic, "png", f.toFile)
    }

    getDensity(outlineGraphic, coppers, inverted, debug)
  }

  /** get a graphics own density, ie. the density to it's own outline
    *
    * @param ol
    * @return
    */
  def getDensity(ol: Graphic): Density = {
    val dimension = ol.format.dimension.getOrElse(Dimension(BigPoint(0, 0), BigPoint(0, 0)))
    val negOffset = dimension.min
    val outline = Renderer.createSvg(
      Seq(ol),
      graphictransformer = SvgTransformer.translateTransform(negOffset)
    )
    SvgOps.setSvgDocumentSize(outline, dimension)
    val outlineGraphic = Rasterizer.toBMP(new TranscoderInput(outline), 300f)

    val boxGraphic = new BufferedImage(outlineGraphic.getWidth, outlineGraphic.getHeight, BufferedImage.TYPE_INT_ARGB)
    val graphic    = boxGraphic.createGraphics()
    graphic.setBackground(Color.BLACK)

    val density = DensityCalculator.getDensity(boxGraphic, outlineGraphic, None, None)
    density
  }

  def getDensity(
      copperGraphics: Seq[Graphic],
      outlineRender: Graphic,
      inverted: Option[Boolean] = None
  ): Density = {
    val dimension = (copperGraphics.flatMap(_.format.dimension) :+ outlineRender.format.dimension.getOrElse(Dimension(
      BigPoint(0, 0),
      BigPoint(0, 0)
    ))).reduce(_ += _)

    val negOffset = dimension.min

    val coppers = Renderer.createSvg(
      copperGraphics,
      graphictransformer = SvgTransformer.translateTransform(negOffset)
    )
    SvgOps.setSvgDocumentSize(coppers, dimension)
    val coppersGraphic = Rasterizer.toBMP(new TranscoderInput(coppers), 300f)

    val outline = Renderer.createSvg(
      Seq(outlineRender),
      graphictransformer = SvgTransformer.translateTransform(negOffset)
    );
    SvgOps.setSvgDocumentSize(outline, dimension)
    val outlineGraphic = Rasterizer.toBMP(new TranscoderInput(outline), 300f)

    getDensity(outlineGraphic, coppersGraphic, inverted, None)
  }

  def getDensity(
      boundary: BufferedImage,
      features: BufferedImage,
      inverted: Option[Boolean],
      debug: Option[(String, UUID, String)]
  ): Density = {
    val width                   = Math.min(boundary.getWidth, features.getWidth)
    val height                  = Math.min(boundary.getHeight, features.getHeight)
    var completeBoundary        = 0
    var featuresInBoundary      = 0
    var featuresOutsideBoundary = 0
    var featuresArea            = 0

    val debugImage = debug.map { d =>
      val i = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB)

      val gr = i.createGraphics()
      gr.setColor(Color.WHITE)
      gr.fillRect(0, 0, width, height)
      i
    }

    (for {
      x <- 0 until width
      y <- 0 until height
    } yield {

      val valueInBoundary = Try(boundary.getRaster.getSample(x, y, 0) == 0) match {
        case Failure(exception) =>
          println(s" failed $x x $y")
          throw exception
        case Success(value) => value
      }

      val valueInFeatures = Try(features.getRaster.getSample(x, y, 0) == 0) match {
        case Failure(exception) =>
          println(s" failed $x x $y")
          throw exception
        case Success(value) => value
      }

      // count boundary and feature pixels
      if (valueInBoundary) {
        completeBoundary += 1
        debugImage.foreach(i => i.setRGB(x, y, 0xadadad))
      }

      if (valueInFeatures) {
        featuresArea += 1
      }

      // not in boundary but is feature
      if (!valueInBoundary && valueInFeatures) {
        featuresOutsideBoundary += 1
        debugImage.foreach(i => i.setRGB(x, y, 0xff0000))
      }

      // boundary and feature
      if (valueInBoundary && valueInFeatures) {
        featuresInBoundary += 1
        debugImage.foreach(i => i.setRGB(x, y, 0xffcc24))
      }

      // neither boundary nor feature
      if (!valueInBoundary && !valueInFeatures) {
        debugImage.foreach(i => i.setRGB(x, y, 0x00ff00))
      }

      // boundary but no feature
      if (valueInBoundary && !valueInFeatures) {
        debugImage.foreach(i => i.setRGB(x, y, 0xe3e3e3))
      }
    })

    debugImage.foreach { bi =>
      val d = debug.get
      val f = AssemblyFiles.getAssemblyBaseFolder(team = d._1, assembly = d._2)(ConfigFactory.load()).resolve(
        s"debug/${d._3}-comparison-${UUID.randomUUID().short()}.png"
      )
      f.toFile.getParentFile.mkdirs()
      ImageIO.write(bi, "png", f.toFile)
    }

    if (completeBoundary > 0) {
      println(
        s"[DENSITY CALCULATOR] \t all pixels: ${width * height}. complete boundary is ${completeBoundary}, features in boundary are ${featuresInBoundary} features outside of boundary are ${featuresOutsideBoundary}, feature area ${featuresArea}"
      )
      var surface = featuresInBoundary.doubleValue() / completeBoundary.doubleValue()
      surface =
        if (inverted.contains(true)) {
          1 - surface
        } else {
          surface
        }

      val portionOfCandidateIsCopper = surface * 100
      val percentOutside =
        if (featuresArea > 0)
          featuresOutsideBoundary.doubleValue() / featuresArea.doubleValue()
        else
          0.0

      Density(portionOfCandidateIsCopper, percentOutside)
    } else {
      Density(0.0, 0.0)
    }
  }

}

object SvgTransformer {

  def translateTransform(negOffset: BigPoint, margin: BigDecimal = 0)(g: Graphic, e: Element): Element = {
    val tf = s"translate(${-negOffset.x + margin} ,${-negOffset.y + margin})"
    e.setAttribute("transform", tf)
    e
  }

}
