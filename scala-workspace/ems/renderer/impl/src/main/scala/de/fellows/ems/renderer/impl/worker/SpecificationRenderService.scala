package de.fellows.ems.renderer.impl.worker

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assemby.api.AssemblyLifecycleStageName.Reconciliation
import de.fellows.app.assemby.api.{
  Assembly,
  AssemblyLifecycleStage,
  AssemblyLifecycleStageName,
  AssemblyService,
  AssemblyUtils,
  Version
}
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.pcb.api.PCBApi.SetPreviews
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.renderer.api.job.RenderSpecificationJobEntry
import de.fellows.ems.renderer.impl.{PreviewRenderer, StackConfig}
import de.fellows.utils.FutureUtils
import de.fellows.utils.internal.{LifecycleStageStatus, StageStatusName}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.jobs.QueuedJob

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class SpecificationRenderService(
    assemblyService: AssemblyService,
    pcbService: PCBService,
    layerstackService: LayerstackService,
    registry: PersistentEntityRegistry
) extends StackrateLogging {

  def handleSpecificationRender(
      jobInfo: RenderSpecificationJobEntry,
      job: QueuedJob
  )(implicit ec: ExecutionContext): Try[Done] =
    SafeAwait.result {
      assemblyService._getAssembly(jobInfo.team, jobInfo.assembly).invoke().flatMap { assembly =>
        val version = assembly.assembly.currentVersion.getOrElse(
          throw new IllegalArgumentException(s"No current version found for assembly ${assembly.assembly.id}")
        )

        val hasReconciliation =
          AssemblyLifecycleStage.hasState(version.lifecycles, Reconciliation, StageStatusName.Success)

        val lifecycle = jobInfo.lifecycle.getOrElse {
          if (hasReconciliation) {
            AssemblyLifecycleStageName.ReconciledSpecificationRender
          } else
            AssemblyLifecycleStageName.SpecificationRender
        }

        AssemblyUtils.lifecycle(
          team = jobInfo.team,
          assembly = jobInfo.assembly,
          version = version.id,
          service = assemblyService,
          lcName = lifecycle
        ) {
          for {
            _ <- doPreview(
              team = jobInfo.team,
              assembly = assembly.assembly,
              versionId = version.id
            )

            _ <- completeInitializationStep(
              assembly = assembly.assembly,
              version = version
            )
          } yield Done
        }
      }
    }

  private def completeInitializationStep(
      assembly: Assembly,
      version: Version
  ): Future[Done] =
    assemblyService
      ._updateVersionLifecycle(
        assembly.team,
        assembly.id,
        Some(version.id),
        AssemblyLifecycleStageName.Initialization.value,
        Some(System.currentTimeMillis())
      )
      .invoke(LifecycleStageStatus.emptySuccess)

  private def doPreview(
      team: String,
      assembly: Assembly,
      versionId: UUID
  )(implicit ec: ExecutionContext): Future[Done] =
    pcbService._getPCBVersion(
      team,
      assembly.id,
      versionId
    ).invoke().flatMap { pcbv =>
      if (pcbv.files.nonEmpty) {
        (for {
          previewOpt <-
            FutureUtils.option(pcbv.outline.map { outline =>
              new PreviewRenderer(pcbService, layerstackService, registry, pcbv, outline)
                ._doRenderPreview(
                  assembly.id,
                  versionId,
                  None,
                  assembly.team,
                  StackConfig(front = true, rear = true)
                )
            })
          _ <-
            FutureUtils.option(previewOpt.map { preview =>
              pcbService._setSpecificationPreview(team, assembly.id, versionId, preview.specification).invoke(
                SetPreviews(
                  preview.front,
                  preview.rear
                )
              )
            })
        } yield Done).recover {
          case e: Exception =>
            e.printStackTrace()
            Done
        }
      } else {
        // TODO: maybe handle projects with missing files better than just silently succeeding
        Future.successful(Done)
      }
    }
}
