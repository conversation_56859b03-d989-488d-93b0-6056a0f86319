package de.fellows.ems.renderer.impl.worker

import akka.Done
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{Assembly, AssemblyLifecycleStageName, AssemblyService, AssemblyUtils}
import de.fellows.app.converter.kicad.api.{ConvertFileRequest, KicadConverterService}
import de.fellows.ems.pcb.model.{GerberFile, LayerConstants}
import de.fellows.ems.renderer.api.RendererUtils
import de.fellows.ems.renderer.api.job.{ConvertFileJobEntry, RenderFileJobEntry}
import de.fellows.ems.renderer.impl.pool.AbstractTask
import de.fellows.ems.renderer.impl.{FutureRendering, PCBListener}
import de.fellows.utils.internal.LifecycleStageStatus
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.jobs.{Job<PERSON><PERSON>er, QueuedJob, WorkerId}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Success, Try}

class FileRenderService(
    assemblyService: AssemblyService,
    converterService: KicadConverterService,
    listener: PCBListener,
    jobBuilder: JobBuilder
) extends StackrateLogging {

  private val MIMES = Seq(LayerConstants.NATIVE_EAGLE, LayerConstants.NATIVE_KICAD)

  def handleFileRendering(
      workerId: WorkerId,
      jobInfo: RenderFileJobEntry,
      job: QueuedJob
  )(implicit ec: ExecutionContext): Try[Done] =
    if (RendererUtils.isRenderableMimeType(jobInfo.file.fType.mimeType)) {
      logger.info(s"[REDIS ${workerId}] rendering file ${jobInfo.file.name} jobId: ${job.job}")
      val result: Future[Done] =
        assemblyService._getAssembly(jobInfo.ass.team, jobInfo.ass.id).invoke().flatMap { assemblyWithShares =>
          val assembly = assemblyWithShares.assembly

          for {
            // TODO: pcb_render_started telemetry
            render <- renderFile(jobInfo.ass, assembly, jobInfo.file)
            futureRendering <- render match {
              case Some(value) => flatMapFutureRendering(job, workerId)(value.f)
              case None        => Future.successful(Done)
            }

          } yield futureRendering

        }

      SafeAwait.result(result)
    } else {
      logger.info(s"[REDIS ${workerId}] skipping unrenderable file ${jobInfo.file.name}")
      Success(Done)
    }

  def handleFileConversion(
      workerId: WorkerId,
      jobInfo: ConvertFileJobEntry,
      job: QueuedJob
  )(implicit ec: ExecutionContext): Try[Done] =
    if (MIMES.contains(jobInfo.file.fType.fileType)) {
      logger.info(s"[REDIS ${workerId}] triggering file conversion file: ${jobInfo.file.name} jobId: ${job.job}")
      val result = converterService
        .convertFiles(jobInfo.ass.team, jobInfo.ass.id, jobInfo.ass.version)
        .invoke(
          ConvertFileRequest(files = Seq(jobInfo.file))
        )

      SafeAwait.result(result)
    } else {
      logger.info(
        s"[REDIS ${workerId}] file type cannot be converted ${jobInfo.file.name} file type: ${jobInfo.file.fType.fileType}"
      )
      Success(Done)
    }

  private def renderFile(
      ass: AssemblyReference,
      assembly: Assembly,
      gf: GerberFile
  )(implicit ec: ExecutionContext): Future[Option[FutureRendering]] =
    AssemblyUtils.setLifecycle(
      assemblyService,
      ass,
      AssemblyLifecycleStageName.Initialization,
      LifecycleStageStatus.emptyProgress
    ).flatMap { _ =>
      listener.handleFileSet(
        ass,
        assembly,
        gf
      )
    }

  /** convert the rendering to a single [[ Future ]] that also contains the subtasks
    * @param job
    * @param workerId
    * @return
    */
  private def flatMapFutureRendering(
      job: QueuedJob,
      workerId: WorkerId
  )(
      t: AbstractTask[_, _]
  )(implicit ec: ExecutionContext): Future[Done] =
    t.result.flatMap { _ =>
      t.runlvl match {
        case Some(AbstractTask.RUNLVL_ERROR) =>
          throw t.thrw.getOrElse(new RuntimeException("Failed Render Task"))
        case _ =>
          val subtasks = t.subtasks.result()
          logger.info(s"[REDIS ${workerId}] waiting for ${subtasks.size} subtasks")
          jobBuilder.touch(job)
          Future.sequence(subtasks.map(flatMapFutureRendering(job, workerId)))
            .map(_ => Done)
      }
    }
}
