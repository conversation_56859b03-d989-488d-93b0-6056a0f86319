package de.fellows.ems.renderer.impl.pool.odb

import de.luminovo.odb.odbpp.model.{MMUnit, ODBUnit}

import java.io.File

/** This class parses user symbols and registers them in the symbol pool.
  * @param symbolFolders
  */
class UserSymbolParser(symbolFolders: Seq[File], pool: ODBUserSymbolPool) {

  def parse(desiredUnit: Option[ODBUnit]) =
    symbolFolders.foreach { folder =>
      val symbolName = folder.getName
      folder.listFiles(x => x.getName == "features").foreach { file =>
        val renderer = new ODBRenderer(file)
        val features = renderer.buildFeatures(desiredUnit)

        features.map { f =>
          pool += (symbolName -> f)
        }
      }
    }
}
