package de.fellows.ems.renderer.impl.gerber

import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.matchers.should.Matchers
import de.fellows.ems.renderer.impl.pool.SyncRenderer
import de.fellows.ems.pcb.model.{BigPoint, Dimension}
import java.io.File
import java.nio.file.{Files, Path, Paths}
import de.fellows.utils.logging.StackrateLogger
import play.api.Logger

class SyncRendererTest extends AnyWordSpec with Matchers {
  private val logger = new StackrateLogger(Logger(classOf[SyncRendererTest]))

  val HDMIBoard = Paths.get(getClass.getResource("/gerber/AnfrageHDMI_LRCnxFe3Tf4").toURI)
  val HDMIBoardFiles = Seq(
    "Colibri_HDMI_Adapter.GBL",
    "Colibri_HDMI_Adapter.GBO",
    "Colibri_HDMI_Adapter.GBP",
    "Colibri_HDMI_Adapter.GBS",
    "Colibri_HDMI_Adapter.GM15",
    "Colibri_HDMI_Adapter.GP1",
    "Colibri_HDMI_Adapter.GP2",
    "Colibri_HDMI_Adapter.GTL",
    "Colibri_HDMI_Adapter.GTO",
    "Colibri_HDMI_Adapter.GTP",
    "Colibri_HDMI_Adapter.GTS",
  )

  "SyncRenderer" should {
    "renders hdmi board Colibri_HDMI_Adapter.GBL" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GBL").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(3200, 3200))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GBO" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GBO").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(0, 0))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GBP" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GBP").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(0, 0))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GBS" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GBS").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(3200, 3200))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GM15" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GM15").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(3200, 3200))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GP1" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GP1").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(3200, 3200))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GP2" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GP2").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(3200, 3200))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GTL" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GTL").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(3200, 3200))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GTO" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GTO").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(23.8, 77.58), BigPoint(3370.58, 2702.56))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GTP" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GTP").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(340.8, 89.28999999999999), BigPoint(2515.58, 2540.0))
    }

    "renders hdmi board Colibri_HDMI_Adapter.GTS" in {
      val file     = HDMIBoard.resolve("Colibri_HDMI_Adapter.GTS").toFile
      val renderer = new SyncRenderer(file, None)(logger)
      renderer.prepare()

      renderer.dimensions shouldBe Dimension(BigPoint(0, 0), BigPoint(3200, 3200))
    }
  }
}
