package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.layerstack.api
import de.fellows.ems.layerstack.impl.MaterialReporter

import scala.concurrent.{ExecutionContext, Future}

class UnitTestMaterialReporter extends MaterialReporter {

  /** Get Material by ID
    */
  override def getMaterial(team: String, id: String)(implicit exc: ExecutionContext): Future[Option[api.Material]] =
    Future.successful(None)

  /** Get Material by ID using the index for performance
    */
  override def getIndexedMaterial(team: String, id: String)(implicit
      exc: ExecutionContext
  ): Future[Option[api.Material]] = Future.successful(None)

  /** Get All Materials
    */
  override def getMaterials(
      team: String,
      page: Int,
      pagesize: Int,
      filter: Option[String],
      flat: Option[Boolean]
  )(implicit exc: ExecutionContext): Future[Seq[api.Material]] = Future.successful(Seq())
}
