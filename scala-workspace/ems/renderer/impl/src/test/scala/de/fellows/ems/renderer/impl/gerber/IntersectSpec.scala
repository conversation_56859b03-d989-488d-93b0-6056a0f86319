package de.fellows.ems.renderer.impl.gerber

import de.fellows.ems.pcb.model.graphics.Paths
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.awt.geom.{Line2D, Rectangle2D}

class IntersectSpec extends AnyWordSpec with BeforeAndAfterAll with Matchers {

  "Intersect" should {
    "intersect extremely close lines" in {
      val l1 = new Line2D.Double(0, 0, 100, 0)
      val l2 = new Line2D.Double(0, 0.001, 100, 0.001)

      Paths.countourIntersects(l1, l2) should be(true)
    }
    "do not intersect " in {
      val l1 = new Line2D.Double(0, 0, 100, 0)
      val l2 = new Line2D.Double(0, 0.01, 100, 0.01)

      Paths.countourIntersects(l1, l2) should be(false)
    }

    "rectangle" in {

      val r1 = new Rectangle2D.Double(0, 0, 100, 100)
      val r2 = new Rectangle2D.Double(0, 100, 100, 100)

      Paths.countourIntersects(r1, r2) should be(true)
    }

    "line2" in {
      val l1 = new Line2D.Double(0, 0, 10000, 0)
      val l2 = new Line2D.Double(0, 0, 10001, 0)
      l1.intersectsLine(l2) should be(true)
    }
  }

}
