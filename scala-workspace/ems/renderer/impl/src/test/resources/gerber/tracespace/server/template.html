<!doctype html>
<html lang='en'>
<head>
    <link
            rel="stylesheet"
            href="https://fonts.googleapis.com/css?family=Open+Sans:400,700"
    >
    <link
            rel="stylesheet"
            href="https://unpkg.com/tachyons@4.9.0/css/tachyons.min.css"
    >
    <style>
        .open-sans {
            font-family: 'Open Sans', sans-serif;
        }

        .lh-none {
            line-height: 0;
        }

        .render-outline:hover {
            outline: rgba(0, 0, 0, .3) dashed 0.25rem;
        }
    </style>
    <title><%- name %> - integration test suite</title>
</head>
<body class='w-100 pa5 open-sans bg-light-gray'>
<h1 class='f1 lh-title mt0 mb3 mr3 normal'>
    <%- name %>
</h1>
<p class='f3 lh-title mv0'>
    integration test suite
</p>

<% results.forEach((suite) => { %>
<section class='mt4'>
    <h2 class='f2 mt0 mb3 normal'>
        <%- suite.name %>
    </h2>

    <div class='pl4 bl bw2'>
        <% suite.specs.forEach((spec) => { %>
        <div class='dib mt2 mb3 mr3 pa4 v-top'>
            <h3 class='f3 mt0 mb2 normal'>
                <%- spec.name %>
            </h3>

            <% if (spec.source) { %>
            <div class='dib mr3'>
                <h4 class='f4 lh-title mt0 mb2 normal'>
                    source
                </h4>
                <div class='near-white bg-mid-gray pv1 ph2 tl'>
                    <% spec.source.split("\n").filter(_ => _).forEach((line, i) => { %>
                    <code class='db f7 lh-copy tracked'>
                <span class='dib w1 tr light-red'>
                  <%- i + 1 %>
                </span>
                        <%- line %>
                    </code>
                    <% }) %>
                </div>
            </div>
            <% } %>

            <div class='dib v-top mr3 tc'>
                <% if (spec.source || spec.expected) { %>
                <h4 class='f4 lh-title mt0 mb2 normal'>
                    render
                </h4>
                <% } %>

                <div class='dib lh-none render-outline'>
                    <%= spec.render %>
                </div>

                <% if (spec.source) { %>
                <h4 class='f4 lh-title mt3 mb2 normal'>
                    expected
                </h4>
                <%= spec.expected %>
                <% } %>
            </div>
        </div>
        <% }) %>
    </div>
</section>
<% }) %>
</body>
</html>
