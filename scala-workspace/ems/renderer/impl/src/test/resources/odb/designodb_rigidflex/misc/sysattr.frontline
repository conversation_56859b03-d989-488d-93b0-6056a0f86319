UNITS=INCH

BOOLEAN {
    NAME=.etm_mirror
    PROMPT=Plate mirror for drill (ET)
    ENTITY=layer
    DEF=NO
    GROUP=Custom
}

BOOLEAN {
    NAME=.flipped_out_of_date
    PROMPT=Out of date
    ENTITY=step
    DEF=NO
    GROUP=Custom
}

BOOLEAN {
    NAME=.sr_pcb
    PROMPT=S&R PCB
    ENTITY=step
    DEF=NO
    GROUP=Custom
}


TEXT {
    NAME=.action_mask_layer
    PROMPT=Action Mask Layer
    MIN_LEN=0
    MAX_LEN=1000
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.flipped_of
    PROMPT=Source entity
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step;layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_cbnk_blank_name
    PROMPT=Blank Name (NEC-CBNK record)
    MIN_LEN=0
    MAX_LEN=8
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n1_draw_num
    PROMPT=Drawing Num.(NEC-N1 record)
    MIN_LEN=0
    MAX_LEN=20
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n1_rev
    PROMPT=Drawing Revision.(NEC-N1 record)
    MIN_LEN=0
    MAX_LEN=2
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n2_draw_num
    PROMPT=Drawing Num.(NEC-N2 record)
    MIN_LEN=0
    MAX_LEN=20
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n2_rev
    PROMPT=Drwing Revision.(NEC-N2 record)
    MIN_LEN=0
    MAX_LEN=2
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n3_edit_level
    PROMPT=Editing Level (NEC-N3 record)
    MIN_LEN=0
    MAX_LEN=1
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n3_lyr_type
    PROMPT=Layer Type (NEC-N3 record)
    MIN_LEN=0
    MAX_LEN=3
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n3_pol
    PROMPT=Data Polarity (NEC-N3 record)
    MIN_LEN=0
    MAX_LEN=1
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n3_prod_rev
    PROMPT=Production Revision (NEC-N3 record)
    MIN_LEN=0
    MAX_LEN=2
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.nec_n3_target_lyr
    PROMPT=Target Layer (NEC-N3 record)
    MIN_LEN=0
    MAX_LEN=2
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.numbered_layer
    PROMPT=Layer steps were numbered
    MIN_LEN=0
    MAX_LEN=500
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.pnl_class
    PROMPT=Panel class
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.pnl_pcb
    PROMPT=Panelized step
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.pnl_place
    PROMPT=Placement rule/directive
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step;feature
    DEF=
    GROUP=PANELIZATION
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.pnl_scheme
    PROMPT=Panelization scheme
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.released_from
    PROMPT=Released from step
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.rotated_of
    PROMPT=Source of rotated step
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.source_name
    PROMPT=Original entity name
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step;symbol
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.transform_data
    PROMPT=Transformation data
    MIN_LEN=0
    MAX_LEN=64
    ENTITY=step
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.entity_name
    PROMPT=Entity name
    MIN_LEN=0
    MAX_LEN=128
    ENTITY=eall
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}

TEXT {
    NAME=.se_coupon_imprint_layer
    PROMPT=Start/End Coupon imprint layer
    MIN_LEN=0
    MAX_LEN=10000
    ENTITY=layer
    DEF=
    GROUP=Custom
    OPTIONS=
    DEF_OPT=
}


OPTION {
    NAME=.action_mask_layer_type
    PROMPT=Use Action Mask Layer To
    OPTIONS=exclude;include
    DELETED=NO;NO
    ENTITY=layer
    DEF=exclude
    GROUP=Custom
}

OPTION {
    NAME=.cdr_drill_type
    PROMPT=CDR drill type
    OPTIONS=unset;laser;photo;through;blind
    DELETED=NO;NO;NO;NO;NO
    ENTITY=layer
    DEF=unset
    GROUP=Custom
}

OPTION {
    NAME=.cdr_mirror
    PROMPT=CDR mirror
    OPTIONS=unset;no;yes
    DELETED=NO;NO;NO
    ENTITY=layer
    DEF=unset
    GROUP=Custom
}

OPTION {
    NAME=.etm_pin_style
    PROMPT=Pin Guiding Style(et)
    OPTIONS=regular;mania
    DELETED=NO;NO
    ENTITY=step
    DEF=regular
    GROUP=Custom
}

OPTION {
    NAME=.etm_repair_fmt
    PROMPT=Repair File Format(et)
    OPTIONS=none;epc
    DELETED=NO;NO
    ENTITY=step
    DEF=none
    GROUP=Custom
}

OPTION {
    NAME=.etm_rotate
    PROMPT=Plate rotation for drill (ET)
    OPTIONS=0;90;180;270
    DELETED=NO;NO;NO;NO
    ENTITY=layer
    DEF=0
    GROUP=Custom
}

OPTION {
    NAME=.etm_tester
    PROMPT=Tester name(et)
    OPTIONS=mania;everett charles;circuitline;luther maelzer;probot;bsl;integritest;microcraft;atg
    DELETED=NO;NO;NO;NO;NO;NO;NO;NO;NO
    ENTITY=step
    DEF=mania
    GROUP=Custom
}

OPTION {
    NAME=.se_coupon
    PROMPT=Start/End coupon type
    OPTIONS=none;drill;rout
    DELETED=NO;NO;NO
    ENTITY=step
    DEF=none
    GROUP=Custom
}

OPTION {
    NAME=.se_coupon_direct
    PROMPT=Start/End coupon direction
    OPTIONS=0;90;180;270
    DELETED=NO;NO;NO;NO
    ENTITY=layer
    DEF=0
    GROUP=Custom
}

OPTION {
    NAME=.se_coupon_dist_type
    PROMPT=Start/End coupon distance type
    OPTIONS=spacing;center
    DELETED=NO;NO
    ENTITY=layer
    DEF=spacing
    GROUP=Custom
}

OPTION {
    NAME=.se_coupon_method
    PROMPT=Start/End coupon calculation method
    OPTIONS=none;auto;frompoint
    DELETED=NO;NO;NO
    ENTITY=layer
    DEF=none
    GROUP=Custom
}

OPTION {
    NAME=.se_coupon_mode
    PROMPT=Start/End coupon mode
    OPTIONS=start_end;start;end
    DELETED=NO;NO;NO
    ENTITY=step
    DEF=start_end
    GROUP=Custom
}

OPTION {
    NAME=.se_coupon_split_num
    PROMPT=Start/End coupon split number
    OPTIONS=1;2
    DELETED=NO;NO
    ENTITY=step
    DEF=1
    GROUP=Custom
}


INTEGER {
    NAME=.entity_version
    PROMPT=Number of changes
    MIN_VAL=0
    MAX_VAL=2147418112
    ENTITY=step;symbol
    DEF=0
    GROUP=Custom
}

INTEGER {
    NAME=.se_coupon_min_hits
    PROMPT=Start/End coupon min hits
    MIN_VAL=0
    MAX_VAL=10000
    ENTITY=layer
    DEF=0
    GROUP=Custom
}

INTEGER {
    NAME=.se_coupon_order
    PROMPT=Start/End coupon order
    MIN_VAL=1
    MAX_VAL=100
    ENTITY=step
    DEF=1
    GROUP=Custom
}


FLOAT {
    NAME=.copper_thickness
    PROMPT=Copper Thickness
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=1
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.depth
    PROMPT=Drill Depth
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer;feature
    DEF=0
    GROUP=ROUT
    UNITS=MIL_MICRON
    UNIT_TYPE=DISTANCE
}

FLOAT {
    NAME=.etm_adapter_h
    PROMPT=Adapter Height in Mils(et)
    MIN_VAL=9.999999974752427e-007
    MAX_VAL=5000
    ENTITY=step
    DEF=3750
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_height
    PROMPT=Plate Height(ET)
    MIN_VAL=0
    MAX_VAL=5000
    ENTITY=layer
    DEF=20
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_prim_sink_h
    PROMPT=Countersink Depth on Primary side(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_prim_sink_r
    PROMPT=Countersink Threshold Radius on Primary side(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_prim_sink_s
    PROMPT=Countersink Drill Size on Primary side(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_sec_sink_h
    PROMPT=Countersink Depth on Secondary side(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_sec_sink_r
    PROMPT=Countersink Threshold Radius on Secondary side(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_sec_sink_s
    PROMPT=Countersink Drill Size on Secondary side(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_shift_x
    PROMPT=Shift for drill by x (ET)
    MIN_VAL=-100000
    MAX_VAL=100000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_shift_y
    PROMPT=Shift for drill by y(ET)
    MIN_VAL=-100000
    MAX_VAL=100000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_step_x
    PROMPT=Grid step by x(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_step_y
    PROMPT=Grid step by y(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.etm_thickness
    PROMPT=Plate Thickness(ET)
    MIN_VAL=0
    MAX_VAL=1000
    ENTITY=layer
    DEF=20
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.inp_x_scale
    PROMPT=Input X scale
    MIN_VAL=-9.999990463256836
    MAX_VAL=9.999990463256836
    ENTITY=layer
    DEF=1
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.inp_y_scale
    PROMPT=Input Y scale
    MIN_VAL=-9.999990463256836
    MAX_VAL=9.999990463256836
    ENTITY=layer
    DEF=1
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.rotation_angle
    PROMPT=Step rotation angle (deg.)
    MIN_VAL=-360
    MAX_VAL=360
    ENTITY=step
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.se_coupon_max_size
    PROMPT=Start/End coupon max size
    MIN_VAL=0
    MAX_VAL=10000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=MIL_MICRON
    UNIT_TYPE=DISTANCE
}

FLOAT {
    NAME=.se_coupon_min_size
    PROMPT=Start/End coupon min size
    MIN_VAL=0
    MAX_VAL=10000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=MIL_MICRON
    UNIT_TYPE=DISTANCE
}

FLOAT {
    NAME=.se_coupon_slot_angle
    PROMPT=Start/End coupon slot angle
    MIN_VAL=0
    MAX_VAL=360
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=
    UNIT_TYPE=
}

FLOAT {
    NAME=.se_coupon_slot_length
    PROMPT=Start/End coupon slot length
    MIN_VAL=0
    MAX_VAL=10000
    ENTITY=layer
    DEF=0
    GROUP=Custom
    UNITS=MIL_MICRON
    UNIT_TYPE=DISTANCE
}

FLOAT {
    NAME=.se_coupon_dist
    PROMPT=Start/End coupon distance
    MIN_VAL=0
    MAX_VAL=10000
    ENTITY=layer
    DEF=0
    GROUP=LAYER
    UNITS=MIL_MICRON
    UNIT_TYPE=DISTANCE
}

