DISPLAY {
    L0 = comp_+_top
}
DISPLAY {
    L0 = comp_+_top
    L1 = comp_+_bot
}
DISPLAY {
    L0 = comp_+_top
    L1 = comp_+_top
    L2 = signal_1
    L3 = soldermask_top
}
DISPLAY {
    L0 = comp_+_top
    L1 = signal_10
}
DISPLAY {
    L0 = comp_+_top
    L1 = signal_1
}
DISPLAY {
    L0 = comp_+_top
    L1 = rout
}
DISPLAY {
    L0 = comp_+_top
    L1 = d_1_10
}
DISPLAY {
    L0 = comp_+_top
    L1 = signal_1
    L2 = soldermask_top
}
