import json
import subprocess
import sys

START_PORT = 16379

if len(sys.argv) != 4:
    print("Usage: redis-port-forward.py <context> <namespace> <service>")
    sys.exit(1)

context = sys.argv[1]
namespace = sys.argv[2]
service = sys.argv[3] # custom-parts-worker-redis

# get the endpoints of the service
out = json.loads(subprocess.check_output(
    ["kubectl", "--context", context, "-n", namespace, "get", "ep", service, "-o=json"]))

# build a map from pod name to ip
pods = {}
for add in (out["subsets"][0]['addresses']):
    ip = add['ip']
    pod = add['targetRef']['name']

    pods[pod] = ip

# start port-forwarding for each pod to increasing local ports starting with 6379
mapping = []
nodes = []
processes = []
for pod, ip in pods.items():
    port = START_PORT
    START_PORT += 1
    processes.append(subprocess.Popen(
        ["kubectl", "--context", context, "port-forward", "-n", namespace, f"pod/{pod}", f"{port}:6379"]))

    nodes.append(f"localhost:{port}")
    mapping.append(f"{ip}:6379=localhost:{port}")

jdbc = f"jdbc:redis:cluster://{','.join(nodes)}/0"

mappingstring = "{" + ','.join(mapping) + "}"

datasource = f"""
#DataSourceSettings#
#LocalDataSource: redis-{context}-{namespace}
#BEGIN#
<data-source source="LOCAL" name="redis-{context}-{namespace}">
    <database-info product="Redis Cluster" version="6.2.7" jdbc-version="4.2" driver-name="Redis JDBC Driver" >
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
    </database-info>
    <case-sensitivity plain-identifiers="mixed" quoted-identifiers="mixed"/>
    <driver-ref>redis</driver-ref>
    <synchronize>true</synchronize>
    <jdbc-driver>jdbc.RedisDriver</jdbc-driver>
    <jdbc-url>{jdbc}</jdbc-url>
    <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false"/>
    </jdbc-additional-properties>
    <secret-storage>master_key</secret-storage>
    <auth-provider>no-auth</auth-provider>
    <schema-mapping>
        <introspection-scope>
            <node kind="schema" negative="1"/>
        </introspection-scope>
    </schema-mapping>
    <working-dir>$ProjectFileDir$</working-dir>
    <driver-properties>
        <property name="hostAndPortMapping" value="{mappingstring}"/>
    </driver-properties>
</data-source>
#END#
"""

print("copy the following datasource to use in intellij")
print(datasource)

# block until all processes are finished (or the user presses Ctrl-C)
for proc in processes:
    proc.communicate()
