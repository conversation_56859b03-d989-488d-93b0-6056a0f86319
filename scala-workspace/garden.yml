kind: Build
type: container
name: sbt-builder
allowPublish: false
include: [sbt.<PERSON><PERSON><PERSON><PERSON>, docker]
spec:
  dockerfile: sbt.Dockerfile
---
kind: Build
type: container
name: jib-base-image
allowPublish: false
include: [jib-base.Dockerfile, docker]
spec:
  dockerfile: jib-base.Dockerfile
---
kind: Build
type: container
name: scala-builder
allowPublish: false
exclude: [sbt.Dockerfile, jib-base.Dockerfile, project.Dockerfile]
dependencies: [build.sbt-builder]
spec:
  dockerfile: scala-builder.Dockerfile
  buildArgs:
    BUILDER_IMAGE_VERSION: ${actions.build.sbt-builder.version}
    BUILDER_IMAGE_NAME: ${actions.build.sbt-builder.outputs.deployment-image-name}
---
kind: Test
type: container
name: scala-pcb-server-unit
description: Scala PCB server unit test
dependencies: [build.scala-builder]
spec:
  image: ${actions.build.scala-builder.outputs.deployment-image-id}
  command:
    - /bin/bash
    - '-c'
    - sbt "test"
  memory:
    min: 2048
    max: 4116
---
kind: Build
type: container
name: scala-project
exclude: [sbt.Dockerfile, jib-base.Dockerfile, pcb-server.test.Dockerfile, scala-builder.Dockerfile]
allowPublish: false
dependencies: [build.scala-builder, build.jib-base-image]
spec:
  dockerfile: project.Dockerfile
  buildArgs:
    BUILDER_IMAGE_VERSION: ${actions.build.scala-builder.version}
    BUILDER_IMAGE_NAME: ${actions.build.scala-builder.outputs.deployment-image-name}
    HTTP_VERSION: ${actions.build.http.version}
    USER_API_VERSION: ${actions.build.user-api.version}
    USER_IMPL_VERSION: ${actions.build.user-impl.version}
    CUSTOMER_API_VERSION: ${actions.build.customer-api.version}
    CUSTOMER_IMPL_VERSION: ${actions.build.customer-impl.version}
    PROFILE_API_VERSION: ${actions.build.profile-api.version}
    PROFILE_IMPL_VERSION: ${actions.build.profile-impl.version}
    UTILS_VERSION: ${actions.build.utils.version}
    KICAD_VERSION: ${actions.build.kicad.version}
    TEMPLATING_VERSION: ${actions.build.templating.version}
    IMPL_UTILS_VERSION: ${actions.build.impl-utils.version}
    ENTITY_UTILS_VERSION: ${actions.build.entity-utils.version}
    REDISLOG_VERSION: ${actions.build.redislog.version}
    MAILGUN_VERSION: ${actions.build.mailgun.version}
    SECURITY_API_VERSION: ${actions.build.security-api.version}
    SECURITY_IMPL_VERSION: ${actions.build.security-impl.version}
    COLLABORATION_API_VERSION: ${actions.build.collaboration-api.version}
    COLLABORATION_IMPL_VERSION: ${actions.build.collaboration-impl.version}
    NOTIFICATION_COMMON_VERSION: ${actions.build.notification-common.version}
    NOTIFICATION_API_VERSION: ${actions.build.notification-api.version}
    CAMUNDA_API_VERSION: ${actions.build.camunda-api.version}
    ERPNEXT_API_VERSION: ${actions.build.erpnext-api.version}
    ERPNEXT_BRIDGE_API_VERSION: ${actions.build.erpnext-bridge-api.version}
    ERPNEXT_BRIDGE_IMPL_VERSION: ${actions.build.erpnext-bridge-impl.version}
    NOTIFICATION_IMPL_VERSION: ${actions.build.notification-impl.version}
    ASSEMBLY_API_VERSION: ${actions.build.assembly-api.version}
    ASSEMBLY_COMMONS_VERSION: ${actions.build.assembly-commons.version}
    ASSEMBLY_IMPL_VERSION: ${actions.build.assembly-impl.version}
    PCB_API_VERSION: ${actions.build.pcb-api.version}
    PCB_MODEL_VERSION: ${actions.build.pcb-model.version}
    PCB_IMPL_VERSION: ${actions.build.pcb-impl.version}
    RENDERER_API_VERSION: ${actions.build.renderer-api.version}
    KI_MATCHER_VERSION: ${actions.build.ki-matcher.version}
    RENDERER_IMPL_VERSION: ${actions.build.renderer-impl.version}
    LAYERSTACK_API_VERSION: ${actions.build.layerstack-api.version}
    LAYERSTACK_IMPL_VERSION: ${actions.build.layerstack-impl.version}
    GERBER_PARSER_VERSION: ${actions.build.gerber-parser.version}
    SUPPLIER_API_VERSION: ${actions.build.supplier-api.version}
    SUPPLIER_IMPL_VERSION: ${actions.build.supplier-impl.version}
    DFM_API_VERSION: ${actions.build.dfm-api.version}
    DFM_IMPL_VERSION: ${actions.build.dfm-impl.version}
    CAMUNDA_BRIDGE_API_VERSION: ${actions.build.camunda-bridge-api.version}
    CAMUNDA_BRIDGE_IMPL_VERSION: ${actions.build.camunda-bridge-impl.version}
    QUOTATION_API_VERSION: ${actions.build.quotation-api.version}
    QUOTATION_IMPL_VERSION: ${actions.build.quotation-impl.version}
    PRICE_API_VERSION: ${actions.build.price-api.version}
    PRICE_IMPL_VERSION: ${actions.build.price-impl.version}
    NATIVE_CONVERTER_PYLIB_VERSION: ${actions.build.native-converter-pylib.version}
    NATIVE_CONVERTER_IMPL_VERSION: ${actions.build.native-converter-impl.version}
    PANEL_API_VERSION: ${actions.build.panel-api.version}
    PANEL_IMPL_VERSION: ${actions.build.panel-impl.version}
    INBOX_API_VERSION: ${actions.build.inbox-api.version}
    INBOX_COMMONS_VERSION: ${actions.build.inbox-commons.version}
    INBOX_IMPL_VERSION: ${actions.build.inbox-impl.version}
    ANALYSIS_API_VERSION: ${actions.build.analysis-api.version}
    ANALYSIS_IMPL_VERSION: ${actions.build.analysis-impl.version}
    PCB_SERVER_VERSION: ${actions.build.pcb-server-impl.version}
    JIB_BASE_IMAGE: ${actions.build.jib-base-image.outputs.deployment-image-name}:${actions.build.jib-base-image.version}
