package de.fellows.app.customer.impl

import akka.actor.ActorSystem
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.customer.api
import de.fellows.app.customer.api._
import de.fellows.app.customer.impl.entity.CollaborativeInfo
import de.fellows.app.customer.impl.entity.contact.Commands.{DeleteContact, GetContact, SetContact}
import de.fellows.app.customer.impl.entity.contact.{ContactEntity, InternalContact}
import de.fellows.app.customer.impl.entity.customer.Commands.{DeleteCustomer, GetCustomer, SetCustomer}
import de.fellows.app.customer.impl.entity.customer.Events.{CustomerDeleted, CustomerEvent}
import de.fellows.app.customer.impl.entity.customer.{CustomerEntity, InternalCustomer}
import de.fellows.app.customer.impl.read2.{
  ContactRepository2,
  CustomerDTO,
  CustomerLumiquoteRepository,
  CustomerRepository2
}
import de.fellows.app.security.AccessControlServiceComposition.authorizedString
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.CollaborativeEventInfo
import de.fellows.utils.{FutureUtils, PaginationListResult}
import play.api.Logging

import java.time.Instant
import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class CustomerServiceImpl(
    ereg: PersistentEntityRegistry,
    system: ActorSystem,
    customerRepo: CustomerRepository2,
    contactRepo: ContactRepository2,
    lqRepo: CustomerLumiquoteRepository
)(implicit ec: ExecutionContext, sd: ServiceDefinition) extends CustomerService with Logging
    with StackrateAPIImpl {
  implicit val actorSystem: ActorSystem = system

  def CUSTOMER_NOT_FOUND = new TransportException(TransportErrorCode.NotFound, "Customer not found")

  override def findCustomers(
      oWithContacts: Option[Boolean],
      domain: Option[String],
      page: Option[Int],
      pagesize: Option[Int],
      q: Option[String]
  ): ServiceCall[NotUsed, PaginationListResult[api.Customer]] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:*:*:read"
    } { (token, _) =>
      _findCustomers(token.team, oWithContacts, domain, page, pagesize, q)
    }
  override def _findCustomerByLumiquote(
      team: String,
      tenant: String
  ): ServiceCall[NotUsed, Seq[Customer]] =
    ServerServiceCall { _ =>
      lqRepo.getCustomersForTeam(team, tenant).flatMap {
        case Some(value) =>
          ereg.refFor[CustomerEntity](value.toString).ask(GetCustomer(team, value)).map(
            _.response.map(_.toApi()).toSeq
          )

        case None => Future.successful(Seq())
      }
    }

  def search(q: String, c: Customer): Boolean = {
    val _q = q.toLowerCase
    Seq(
      c.name.map(_.toLowerCase.contains(_q)),
      c.domains.map(_.exists(_.toLowerCase.contains(_q))),
      c.email.map(_.toLowerCase.contains(_q)),
      c.externalID.map(_.toLowerCase.contains(_q)),
      c.addresses.map(_.exists { add =>
        Seq(
          add.name.map(_.toLowerCase.contains(_q)),
          add.alias.map(_.toLowerCase.contains(_q)),
          add.phone.map(_.toLowerCase.contains(_q)),
          Some(add.city.toLowerCase.contains(_q)),
          add.company.map(_.toLowerCase.contains(_q)),
          Some(add.street.toLowerCase.contains(_q))
        ).flatten.contains(true)
      }),
      c.contacts.map(_.exists { c =>
        Seq(
          c.email.map(_.toLowerCase.contains(_q)),
          c.firstName.map(_.toLowerCase.contains(_q)),
          c.lastName.map(_.toLowerCase.contains(_q))
        ).flatten.contains(true)
      })
    ).flatten.contains(true)

  }

  override def _findCustomers(
      team: String,
      contacts: Option[Boolean],
      mailOrDomainO: Option[String],
      page: Option[Int],
      pagesize: Option[Int],
      q: Option[String]
  ): ServerServiceCall[NotUsed, PaginationListResult[Customer]] =
    mailOrDomainO match {
      case Some(mailOrDomain) =>
        val cleanedDomain = cleanDomain(mailOrDomain)
        _getCustomers(
          team,
          contacts,
          filterResolvedCustomers = true,
          filter = c => hasDomain(cleanedDomain, mailOrDomain, c) && q.forall(search(_, c)),
          page,
          pagesize
        )
      case None =>
        _getCustomers(team, contacts, filterResolvedCustomers = false, c => q.forall(search(_, c)), page, pagesize)
    }

  private def cleanDomain(_d: String) = {
    val domain = _d

    if (domain.contains("@")) {
      domain.substring(domain.indexOf('@') + 1).toLowerCase()
    } else {
      domain.toLowerCase()
    }
  }

  private def hasDomain(cleanedDomain: String, mail: String, c: Customer): Boolean = {
    val contactMails = c.contacts.getOrElse(Seq()).flatMap(_.email)
    val r = (c.domains.getOrElse(Seq()).map(cleanDomain).exists { cd =>
      cd.contains(cleanedDomain)
    } || contactMails.exists(m => m.contains(mail)))

    r
  }

  def _getCustomers(
      team: String,
      oWithContacts: Option[Boolean],
      filterResolvedCustomers: Boolean,
      filter: Customer => Boolean,
      page: Option[Int],
      pagesize: Option[Int]
  ): ServerServiceCall[NotUsed, PaginationListResult[Customer]] = {

    def resolveCustomer(cstDTO: CustomerDTO): Future[Option[Customer]] =
      for {
        cst <- ereg.refFor[CustomerEntity](cstDTO.id.toString).ask(GetCustomer(team, cstDTO.id)).map(_.response)
        cnt <- oWithContacts match {
          case Some(true) => contactRepo.getContactsForCustomer(team, cstDTO.id).flatMap(x =>
              Future.sequence(x.map { dto =>
                ereg.refFor[ContactEntity](dto.id.toString).ask(GetContact(team, cstDTO.id, dto.id)).map(_.response)
              })
            ).map(cnts => Some(cnts.flatten))
          case _ => Future.successful(None)
        }
      } yield cst.map(cst => toApi(cst, cnt))

    ServerServiceCall { _ =>
      if (filterResolvedCustomers) {
        customerRepo.getResolvedCustomersForTeam(team, page, pagesize, resolveCustomer, filter)
      } else {
        for {
          cdto <- customerRepo.getCustomersForTeam(team, None, None)
          cust <- Future.sequence(cdto.results.map(dto =>
            ereg.refFor[CustomerEntity](dto.id.toString).ask(GetCustomer(team, dto.id))
          )).map(_.map(_.response))
          cont <- oWithContacts match {
            case Some(true) =>
              Future.sequence(cust.flatMap(_.map { cst =>
                contactRepo.getContactsForCustomer(team, cst.id).map(x =>
                  Future.sequence(x.map { dto =>
                    ereg.refFor[ContactEntity](dto.id.toString).ask(GetContact(team, cst.id, dto.id)).map(_.response)
                  })
                ).flatten
                  .map(c => cst.id -> c.flatten)

              }))
                .map(_.toMap)
            case _ => Future.successful(Map[UUID, Seq[InternalContact]]())
          }
        } yield {

          val result = cust.flatten
            .map(c => toApi(c, cont.get(c.id)))
            .filter(filter)
          val list =
            if (pagesize.isDefined) {
              val ps = pagesize.getOrElse(100)
              val p  = page.getOrElse(0)
              result
                .drop(p * ps) // pagination: page
                .take(ps)     // pagination: pagesize
            } else {
              result
            }
          PaginationListResult(list, result.length)
        }
      }

    }
  }

  override def getCustomers(
      oWithContacts: Option[Boolean],
      page: Option[Int],
      pagesize: Option[Int]
  ): ServiceCall[NotUsed, PaginationListResult[api.Customer]] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:*:*:read"
    } { (token, _) =>
      _getCustomers(token.team, oWithContacts, filterResolvedCustomers = false, c => true, page, pagesize)
    }

  override def deleteContact(customer: UUID, contact: UUID): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:$contact:*:delete"
    } { (token, _) =>
      ServerServiceCall { _ =>
        val info = CollaborativeEventInfo(
          Instant.now(),
          token.userId.toString
        )
        ereg.refFor[ContactEntity](contact.toString).ask(DeleteContact(token.team, customer, contact, info))
      }
    }

  override def deleteCustomer(customer: UUID): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:$customer:*:delete"
    } { (token, _) =>
      ServerServiceCall { _ =>
        val customerEntity = ereg.refFor[CustomerEntity](customer.toString)
        (for {
          cust <- customerEntity.ask(GetCustomer(token.team, customer)).map(_.response)
          ctc <- FutureUtils.option(
            cust.map(c =>
              contactRepo.getContactsForCustomer(token.team, c.id)
            )
          )
        } yield {
          val info = CollaborativeEventInfo(
            Instant.now(),
            token.userId.toString
          )
          cust.getOrElse(throw CUSTOMER_NOT_FOUND)

          for {
            _ <- customerEntity.ask(DeleteCustomer(token.team, customer, info))
            _ <- Future.sequence(ctc.map(_.map { c =>
              val ent = ereg.refFor[ContactEntity](c.id.toString)
              ent.ask(DeleteContact(token.team, customer, c.id, info))
            }).toSeq.flatten)
          } yield Done
        }).flatten

      }
    }

  override def _getCustomer(
      team: String,
      customer: UUID,
      oWithContacts: Option[Boolean]
  ): ServerServiceCall[NotUsed, api.Customer] =
    ServerServiceCall { _ =>
      for {
        cst <- ereg.refFor[CustomerEntity](customer.toString)
          .ask(GetCustomer(team, customer)).map(_.response)
        c <- oWithContacts match {
          case Some(true) =>
            contactRepo.getContactsForCustomer(team, customer).flatMap(x =>
              Future.sequence(x.map(dto =>
                ereg.refFor[ContactEntity](dto.id.toString).ask(GetContact(team, customer, dto.id))
                  .map(_.response)
              ))
            )
              .map(x => Some(x.flatten))
          case _ => Future.successful(None)
        }
      } yield cst match {
        case None =>
          throw CUSTOMER_NOT_FOUND
        case Some(value) => toApi(value, c)
      }
    }

  override def getCustomer(customer: UUID, oWithContacts: Option[Boolean]): ServiceCall[NotUsed, api.Customer] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:*:*:read"
    } { (token, _) =>
      _getCustomer(token.team, customer, oWithContacts)
    }

  override def updateCustomer(id: UUID): ServiceCall[api.Customer, api.CustomerResult] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:*:*:write"
    } { (token, _) =>
      ServerServiceCall { c =>
        ereg.refFor[CustomerEntity](id.toString)
          .ask(GetCustomer(token.team, id))
          .map(_.response)
          .flatMap {
            case None    => throw CUSTOMER_NOT_FOUND
            case Some(_) => _doSetCustomer(token.team, token.userId, c, id)
          }
      }
    }

  override def _getContact(team: String, customer: UUID, contact: UUID): ServerServiceCall[NotUsed, api.Contact] =
    ServerServiceCall { _ =>
      ereg.refFor[ContactEntity](contact.toString).ask(GetContact(team, customer, contact)).map(_.response)
        .map(_.map(toApi).getOrElse(throw new TransportException(TransportErrorCode.NotFound, "Contact not found")))
    }

  override def getContact(customer: UUID, contact: UUID): ServiceCall[NotUsed, api.Contact] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:$contact:*:read"
    } { (token, _) =>
      _getContact(token.team, customer, contact)
    }

  override def getTags(customer: UUID): ServiceCall[NotUsed, Seq[CustomerTag]] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:$customer:tags:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        ereg.refFor[CustomerEntity](customer.toString).ask(GetCustomer(token.team, customer)).map(_.response)
          .map(_.flatMap(_.tags))
          .map(_.getOrElse(Seq()))
      }
    }

  override def setTags(customer: UUID): ServiceCall[Seq[CustomerTag], api.CustomerResult] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:$customer:tags:read"
    } { (token, _) =>
      ServerServiceCall { tags =>
        val entity = ereg.refFor[CustomerEntity](customer.toString)
        entity.ask(GetCustomer(token.team, customer))
          .map(_.response)
          .flatMap { c =>
            val info = CollaborativeEventInfo(Instant.now(), token.userId.toString)
            entity.ask(SetCustomer(
              content = c.map(_.copy(tags = Some(tags))),
              info = info,
              true
            )).map(_.response)
          }
          .map(x => CustomerResult(x.map(ic => ic.toApi)))
      }
    }

  override def getContacts(customer: UUID): ServiceCall[NotUsed, Seq[api.Contact]] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:*:*:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        for {
          dtos <- contactRepo.getContactsForCustomer(token.team, customer)
          contacts <- Future.sequence(dtos.map(dto =>
            ereg.refFor[ContactEntity](dto.id.toString)
              .ask(GetContact(token.team, customer, dto.id))
              .map(_.response)
          )).map(_.flatten)
        } yield contacts.map(toApi)
      }
    }

  override def createCustomer(): ServiceCall[api.Customer, api.CustomerResult] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:*:*:write"
    } { (token, _) =>
      ServerServiceCall { c =>
        //        ???
        val cid = UUID.randomUUID()

        _doSetCustomer(token.team, token.userId, c, cid)
      }
    }

  private def _doSetCustomer(team: String, creator: UUID, c: Customer, cid: UUID) = {
    val info = CollaborativeEventInfo(
      Instant.now,
      creator.toString
    )

    ereg.refFor[CustomerEntity](cid.toString).ask(SetCustomer(
      content = Some(InternalCustomer(
        id = cid,
        team = team,
        name = c.name,
        externalID = c.externalID,
        domain = c.domains,
        image = None,
        addresses = c.addresses.map(_.map { address =>
          if (address.id.isEmpty) {
            address.copy(id = Some(UUID.randomUUID()))
          } else {
            address
          }
        }),
        email = c.email,
        phone = c.phone,
        tags = c.tags,
        taxID = c.taxID,
        discount = c.discount,
        priceLevel = c.priceLevel,
        skontoEnabled = c.skontoEnabled,
        skontoEntries = c.skontoEntries,
        notes = c.notes,
        lumiquoteTenant = c.lumiquoteTenant,
        collab = CollaborativeInfo(
          None,
          creator.toString,
          info.time
        ),
      )),
      info = info,
      keepCollab = false
    ))
      .map(_.response)
      .map(x => CustomerResult(x.map(ic => ic.toApi)))
  }

  override def updateContact(customer: UUID, contact: UUID): ServiceCall[api.Contact, api.ContactResult] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:$contact:*:write"
    } { (token, _) =>
      ServerServiceCall { c =>
        val info = CollaborativeEventInfo(
          Instant.now,
          token.userId.toString
        )
        ereg.refFor[ContactEntity](contact.toString).ask(SetContact(
          team = token.team,
          customer = customer,
          contact = contact,
          content = InternalContact(
            team = token.team,
            contactID = contact,
            customerID = customer,
            c = c,
            collab = CollaborativeInfo(None, info.eventCreator, info.time)
          ),
          info = info
        ))
          .map(_.response)
          .map { r =>
            ContactResult(r.map(toApi))
          }
      }
    }

  def toApi(customer: InternalCustomer, c: Option[Seq[InternalContact]]): Customer =
    Customer(
      id = Some(customer.id),
      externalID = customer.externalID,
      name = customer.name,
      domains = customer.domain,
      image = customer.image.map(_.toApi),
      addresses = customer.addresses,
      email = customer.email,
      phone = customer.phone,
      tags = customer.tags,
      assignee = customer.collab.assignee,
      creator = Some(customer.collab.creator),
      created = Some(customer.collab.created),
      taxID = customer.taxID,
      discount = customer.discount,
      priceLevel = customer.priceLevel,
      skontoEnabled = customer.skontoEnabled,
      skontoEntries = customer.skontoEntries,
      notes = customer.notes,
      lumiquoteTenant = customer.lumiquoteTenant,
      contacts = c.map(_.map(toApi))
    )

  def toApi(contact: InternalContact): Contact =
    Contact(
      Some(contact.id),
      Some(contact.customer),
      contact.firstName,
      contact.lastName,
      contact.email,
      contact.phone,
      contact.address,
      contact.image.map(_.toApi),
      contact.collab.assignee,
      Option(contact.collab.creator),
      Option(contact.collab.created),
      contact.notes,
      contact.position
    )

  override def createContact(customer: UUID): ServiceCall[api.Contact, api.ContactResult] =
    authorizedString { token =>
      s"customer:${token.team}:${token.team}:*:*:write"
    } { (token, _) =>
      ServerServiceCall { _c =>
        val info = CollaborativeEventInfo(
          Instant.now,
          token.userId.toString
        )
        val c = _c.copy(customer = Some(customer))

        val contactid = UUID.randomUUID()

        ereg.refFor[CustomerEntity](customer.toString).ask(GetCustomer(token.team, customer))
          .map(_.response)
          .flatMap {
            case None => throw new TransportException(TransportErrorCode.NotFound, "Customer not Found")
            case Some(cust) =>
              ereg.refFor[ContactEntity](contactid.toString).ask(SetContact(
                token.team,
                cust.id,
                contactid,
                InternalContact(
                  team = token.team,
                  collab = CollaborativeInfo(None, info.eventCreator, info.time),
                  c,
                  contactid,
                  cust.id
                ),
                info
              ))
                .map(_.response)
                .map { r =>
                  ContactResult(r.map(toApi))
                }
          }

      //
      //        customerRepo.getCustomer(token.team, customer).flatMap{
      //          case Some(_) => {
      //            ereg.refFor[ContactEntity](contactid.toString).ask(SetContact(
      //              team = token.team,
      //              customer = customer,
      //              contact = contactid,
      //              content = Some(
      //                InternalContact(
      //                  token.team,
      //                  CollaborativeInfo(
      //                    None, token.userId, info.time
      //                  ),
      //                  c,
      //                  contactid,
      //                  customer)),
      //              info = info
      //            )).map(x => ContactResult(x.map(ic => ic.toApi)))
      //          }
      //          case None => throw new ServiceException(CustomerNotFound, s"Customer $customer not found")
      //        }
      }
    }

  //  override def customerCreated(): Topic[CustomerMessage] =
  //    TopicProducer.taggedStreamWithOffset(CustomerEvent.Tag)((tag, offset) => {
  //      ereg.eventStream(tag, offset).mapConcat(filterCustomerEvents)
  //    })
  //
  //  override def customerDeleted(): Topic[UUID] =
  //    TopicProducer.taggedStreamWithOffset(CustomerEvent.Tag)((tag, offset) => {
  //      ereg.eventStream(tag, offset).mapConcat(filterDeletedEvents)
  //    })

  //  private def filterCustomerEvents(ev: EventStreamElement[CustomerEvent]) = ev match {
  //    case x@EventStreamElement(_, ev: CustomerSet, _) if ev.content.isDefined => immutable.Seq((
  //      CustomerMessage(
  //        ev.content.get.team,
  //        ev.content.get.id,
  //        ev.content.get.toApi
  //      ), x.offset))
  //
  //    case _ => Nil
  //  }

  private def filterDeletedEvents(ev: EventStreamElement[CustomerEvent]) = ev match {
    case x @ EventStreamElement(_, ev: CustomerDeleted, _) => immutable.Seq((
        ev.id,
        x.offset
      ))

    case _ => Nil
  }

  override def fixAddressIdForTeam(team: Option[String]): ServiceCall[NotUsed, NotUsed] =
    authorizedString { token =>
      s"*:*:*:*:*:write"
    } { (token, _) =>
      ServerServiceCall { _ =>
        team.map { t =>
          for {
            cdto <- customerRepo.getCustomersForTeam(t, None, None)
            cust <- Future.sequence(cdto.results.map(dto =>
              ereg.refFor[CustomerEntity](dto.id.toString).ask(GetCustomer(t, dto.id)).map(_.response)
            ))
          } yield cust.flatten
            .map { c =>
              if (c.addresses.map(a => a.find(address => address.id.isEmpty)).isDefined) {
                _doSetCustomer(t, token.userId, toApi(c, None), c.id)
              }
            }
        }.getOrElse(Future.successful()).map(_ => NotUsed)
      }
    }

}
