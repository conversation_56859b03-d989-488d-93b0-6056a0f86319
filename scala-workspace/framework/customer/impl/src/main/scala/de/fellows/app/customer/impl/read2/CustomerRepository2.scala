package de.fellows.app.customer.impl.read2

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{ BoundStatement, PreparedStatement, Row, SimpleStatement }
import com.lightbend.lagom.scaladsl.persistence.cassandra.{ CassandraReadSide, CassandraSession }
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEventTag, ReadSideProcessor }
import de.fellows.app.customer.impl.entity.customer.Events._
import de.fellows.utils.{ Pagination, PaginationListResult }
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.CollaborativeEventInfo
import play.api.Logging

import scala.concurrent.{ ExecutionContext, Future }

class CustomerRepository2(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends Logging {
  implicit val csession: CassandraSession = session

  def getCustomersForTeam(team: String, page: Option[Int], pagesize: Option[Int]) =
    Pagination.pagination[CustomerDTO](page, pagesize, x => true, CustomerDTO.of) {
      new SimpleStatement(
        """
          |SELECT name, id FROM customerName WHERE team = ?
          |""".stripMargin,
        team
      )
    }

  def getResolvedCustomersForTeam[T](
      team: String,
      page: Option[Int],
      pagesize: Option[Int],
      resolver: CustomerDTO => Future[Option[T]],
      filter: T => Boolean
  ): Future[PaginationListResult[T]] =
    Pagination.paginationAsync[T](page, pagesize, filter, (x: Row) => resolver(CustomerDTO.of(x))) {
      new SimpleStatement(
        """
          |SELECT name, id FROM customerName WHERE team = ?
          |""".stripMargin,
        team
      )
    }

}

class CustomerEventProcessor2(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[CustomerEvent] {

  def setCustomer(event: CustomerSet): Future[Seq[BoundStatement]] =
    Future.successful(
      (if (
         event.oldContent.isDefined && event.oldContent.get.name.isDefined && event.oldContent.flatMap(
           _.name
         ) != event.content.flatMap(_.name)
       ) {
         List(stmtDeleteCustomer.bind(event.oldContent.get.team, event.oldContent.get.name.get))
       } else {
         Seq()
       }) :+ stmtUpdateCustomer.bind(event.content.get.id, event.content.get.team, event.content.get.name.get)
    )

  def deleteCustomer(name: Option[String], team: String, info: CollaborativeEventInfo): Future[Seq[BoundStatement]] =
    Future.successful(name.map(n => List(stmtDeleteCustomer.bind(team, n))).getOrElse(List()))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[CustomerEvent] =
    readSide.builder[CustomerEvent]("customer2EventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[CustomerSet] { e =>
        setCustomer(e.event)
      }
      .setEventHandler[CustomerDeleted](e => deleteCustomer(e.event.name, e.event.team, e.event.info))
      //      .setEventHandler[CollaborativeInfoSet](e => setCollab(e.event))
      //      .setEventHandler[CustomerUpdated](e => setCustomer(e.event))
      //      .setEventHandler[ImageSet](e => setImage(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[CustomerEvent]] = CustomerEvent.Tag.allTags

  var stmtUpdateCustomer: PreparedStatement = _
  var stmtDeleteCustomer: PreparedStatement = _

  def createTables(): Future[Done] =
    for {
      // language=SQL

      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS customerName(
          | team text,
          | name text,
          | id uuid,
          | PRIMARY KEY ( team, name )
          |)
          |""".stripMargin
      )

    } yield Done

  def prepareStatements(): Future[Done] =
    for {
      // language=SQL

      deleteCustomer <- session.prepare(
        """
          |DELETE FROM customerName WHERE team = :team AND name = :name
          |""".stripMargin
      )
      updateCustomer <- session.prepare(
        """
          |UPDATE customerName SET id = :id WHERE team = :team AND name = :name
          |""".stripMargin
      )

    } yield {
      stmtUpdateCustomer = updateCustomer
      stmtDeleteCustomer = deleteCustomer
      Done
    }

}
