package de.fellows.app.customer.impl

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.customer.impl.entity.CustomerServiceSerializerRegistry
import de.fellows.app.customer.impl.entity.contact.ContactEntity
import de.fellows.app.customer.impl.entity.customer.CustomerEntity
import de.fellows.app.customer.impl.read2.{
  ContactEventProcessor2,
  ContactRepository2,
  CustomerEventProcessor2,
  CustomerLumiquoteProcessor,
  CustomerLumiquoteRepository,
  CustomerRepository2
}
import de.fellows.app.erpnextbridge.api.ErpNextBridgeApi
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import de.fellows.utils.communication.ServiceDefinition
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class CustomerServiceLoader extends LagomApplicationLoader {

  override def loadDevMode(context: LagomApplicationContext) =
    new CustomerServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new CustomerServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[CustomerService])

}

trait CustomerServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("customer")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = CustomerServiceSerializerRegistry

  lazy val customerRepository          = wire[CustomerRepository2]
  lazy val contactRepository           = wire[ContactRepository2]
  lazy val customerLumiquoteRepository = wire[CustomerLumiquoteRepository]

  val customerEventProcessor = wire[CustomerEventProcessor2]
  readSide.register(customerEventProcessor)

  val contactEventProcessor = wire[ContactEventProcessor2]
  readSide.register(contactEventProcessor)

  val customerLumiquoteProcessor = wire[CustomerLumiquoteProcessor]
  readSide.register(customerLumiquoteProcessor)

  persistentEntityRegistry.register(wire[CustomerEntity])
  persistentEntityRegistry.register(wire[ContactEntity])

}

abstract class CustomerServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with CustomerServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {

  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)
  lazy val erp                                   = serviceClient.implement[ErpNextBridgeApi]

  lazy val fileRouter: CustomerFileService = wire[CustomerFileService]

  override lazy val lagomServer = serverFor[CustomerService](wire[CustomerServiceImpl])
    .additionalRouter(fileRouter.router)
}

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=**********.579809&cid=C02H1BV061F
