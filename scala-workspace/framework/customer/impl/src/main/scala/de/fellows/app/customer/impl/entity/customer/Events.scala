package de.fellows.app.customer.impl.entity.customer

import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.customer.impl.entity.CollaborativeInfo
import de.fellows.utils.FilePath
import de.fellows.utils.entities.CollaborativeEventInfo
import play.api.libs.json.{Format, Json}

import java.util.UUID

object Events {

  sealed trait CustomerEvent extends AggregateEvent[CustomerEvent] {
    override def aggregateTag: AggregateEventShards[CustomerEvent] = CustomerEvent.Tag
  }

  object CustomerEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[CustomerEvent](NumShards)
  }

  case class CustomerSet(
      id: UUID,
      team: String,
      content: Option[InternalCustomer],
      oldContent: Option[InternalCustomer],
      info: CollaborativeEventInfo
  ) extends CustomerEvent

  //  case class CustomerUpdated(id: UUID, team: String, updates: InternalCustomer, updated: InternalCustomer, info: CollaborativeEventInfo) extends CustomerEvent

  case class CustomerDeleted(
      id: UUID,
      team: String,
      name: Option[String],
      lumiquoteTenant: Option[String],
      info: CollaborativeEventInfo
  ) extends CustomerEvent

  case class CollaborativeInfoSet(id: UUID, team: String, content: CollaborativeInfo, info: CollaborativeEventInfo)
      extends CustomerEvent

  case class ImageSet(id: UUID, team: String, fp: Option[FilePath], info: CollaborativeEventInfo) extends CustomerEvent

  //  case class ContactsAdded(id: UUID, team: String, info: CollaborativeEventInfo, newContact: UUID, contacts: Seq[UUID]) extends CustomerEvent
  //
  //  case class ContactsRemoved(id: UUID, team: String, info: CollaborativeEventInfo, oldContact: UUID, contacts: Seq[UUID]) extends CustomerEvent

  object CustomerSet {
    implicit val format: Format[CustomerSet] = Json.format[CustomerSet]
  }

  //  object CustomerUpdated {
  //    implicit val format = Json.format[CustomerUpdated]
  //  }

  object CollaborativeInfoSet {
    implicit val format: Format[CollaborativeInfoSet] = Json.format[CollaborativeInfoSet]
  }

  object ImageSet {
    implicit val format: Format[ImageSet] = Json.format[ImageSet]
  }

  object CustomerDeleted {
    implicit val format: Format[CustomerDeleted] = Json.format[CustomerDeleted]
  }

  //  object ContactsAdded {
  //    implicit val format = Json.format[ContactsAdded]
  //  }
  //
  //  object ContactsRemoved {
  //    implicit val format = Json.format[ContactsRemoved]
  //  }

}
