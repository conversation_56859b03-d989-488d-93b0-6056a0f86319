package de.fellows.app.customer.impl.entity.contact

import akka.Done
import de.fellows.app.customer.impl.entity.contact.Commands._
import de.fellows.app.customer.impl.entity.contact.Events.{CollaborativeInfoSet, ContactDeleted, ContactSet, ImageSet}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity

class ContactEntity(implicit override val service: ServiceDefinition)
    extends SecureTeamEntity[Option[InternalContact]] {
  override type Command = Commands.ContactCommand
  override type Event   = Events.ContactEvent

  override def initialState: State = None

  override def isAllowed(a: ContactCommand, s: Option[InternalContact]): Boolean = a match {
    case SetContact(team, customer, contact, content, info) =>
      s.map(_s => (_s.team, _s.customer, _s.id)).forall(_ == (team, customer, contact))
    case GetContact(team, customer, contact) =>
      s.map(_s => (_s.team, _s.customer, _s.id)).forall(_ == (team, customer, contact))
    case DeleteContact(team, customer, contact, info) =>
      s.map(_s => (_s.team, _s.customer, _s.id)).forall(_ == (team, customer, contact))
    case SetCollaboration(team, customer, contact, collab, info) =>
      s.map(_s => (_s.team, _s.customer, _s.id)).forall(_ == (team, customer, contact))
    case SetImage(team, customer, contact, filePath, info) =>
      s.map(_s => (_s.team, _s.customer, _s.id)).forall(_ == (team, customer, contact))
  }

  override def entityBehavior(state: Option[InternalContact]): Actions =
    Actions()
      .onReadOnlyCommand[GetContact, ContactResponse] {
        case (x: GetContact, ctx, s) =>
          ctx.reply(ContactResponse(s))
      }
      .onCommand[SetContact, ContactResponse] {
        case (x: SetContact, ctx, s) =>
          val updated = s.getOrElse(x.content)
            .copy(
              firstName = x.content.firstName,
              lastName = x.content.lastName,
              email = x.content.email,
              phone = x.content.phone,
              address = x.content.address,
              notes = x.content.notes
            )

          ctx.thenPersist(ContactSet(x.contact, x.team, x.customer, Some(updated), s, x.info))(a =>
            ctx.reply(ContactResponse(Some(updated)))
          )

      }
      .onCommand[SetCollaboration, Done] {
        case (x: SetCollaboration, ctx, s) =>
          s match {
            case Some(state) =>
              ctx.thenPersist(CollaborativeInfoSet(state.id, state.team, x.customer, x.collab, x.info))(_ =>
                ctx.reply(Done)
              )
            case None =>
              ctx.invalidCommand("Entity does not exist")
              ctx.done
          }

      }
      .onCommand[SetImage, Done] {
        case (x: SetImage, ctx, s) =>
          s match {
            case Some(state) =>
              ctx.thenPersist(ImageSet(state.id, state.team, state.customer, x.filePath, x.info))(_ => ctx.reply(Done))
            case None =>
              ctx.invalidCommand("Entity does not exist")
              ctx.done
          }

      }
      .onCommand[DeleteContact, Done] {
        case (x: DeleteContact, ctx, s) =>
          s match {
            case Some(state) =>
              ctx.thenPersist(ContactDeleted(state.id, state.team, state.customer, x.info))(_ => ctx.reply(Done))
            case None =>
              ctx.invalidCommand(s"Entity $x:  $s does not exist")
              ctx.done
          }
      }
      //      .onCommand[UpdateContact, InternalContact]{
      //        case (x: UpdateContact, ctx, s) =>
      //          s match {
      //            case Some(state) =>
      //              val up = updated(s.get, x.content)
      //              ctx.thenPersist(ContactUpdated(state.id, state.team, x.customer, x.content, up, x.info))(_ => ctx.reply(up))
      //            case None =>
      //              ctx.invalidCommand(s"Entity $s does not exist")
      //              ctx.done
      //          }
      //
      //      }
      .onEvent {
        case (x: ContactSet, s) =>
          x.content
        case (_: ContactDeleted, s)       => None
        case (x: ImageSet, s)             => s.map(_.copy(image = x.fp))
        case (x: CollaborativeInfoSet, s) => s.map(_.copy(collab = x.content))
      }

  //  private def updated(state: InternalContact, updates: InternalContact) = {
  //    state.copy(
  //      firstName = updates.firstName.orElse(state.firstName),
  //      lastName = updates.lastName.orElse(state.lastName),
  //      email = updates.email.orElse(state.email),
  //      phone = updates.phone.orElse(state.phone),
  //      address = updates.address.orElse(state.address),
  //      image = updates.image.orElse(state.image)
  //    )
  //  }
}
