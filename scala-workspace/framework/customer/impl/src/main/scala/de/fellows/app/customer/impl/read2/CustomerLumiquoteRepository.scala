package de.fellows.app.customer.impl.read2

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import de.fellows.app.customer.impl.entity.customer.Events.{CustomerDeleted, CustomerEvent, CustomerSet}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.CollaborativeEventInfo
import play.api.Logging

import java.time.Instant
import java.util.Date
import scala.concurrent.{ExecutionContext, Future}

class CustomerLumiquoteRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends Logging {
  def getCustomersForTeam(team: String, lqtenant: String) =
    session.selectOne(
      """
          |SELECT id FROM customerLumiquote WHERE team = ? AND lqtenant = ?
          |""".stripMargin,
      team,
      lqtenant
    ).map(_.map(x => x.getUUID("id")))

}

class CustomerLumiquoteProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[CustomerEvent] {

  def setCustomer(event: CustomerSet): Future[Seq[BoundStatement]] =
    Future.successful(
      (if (
         event.oldContent.isDefined && event.oldContent.get.lumiquoteTenant.isDefined && event.oldContent.flatMap(
           _.lumiquoteTenant
         ) != event.content.flatMap(_.lumiquoteTenant)
       ) {
         List(
           stmtDeleteCustomer.bind()
             .setString("team", event.oldContent.get.team)
             .setString("lqtenant", event.oldContent.get.lumiquoteTenant.get)
             .setUUID("id", event.oldContent.get.id)
         )
       } else {
         Seq()
       }) ++ (event.content.get.lumiquoteTenant match {
        case None => Seq()
        case Some(lqt) =>
          Seq(stmtUpdateCustomer.bind()
            .setString("team", event.content.get.team)
            .setTimestamp("change", Date.from(Instant.now()))
            .setString("lqtenant", lqt)
            .setUUID("id", event.content.get.id))
      })
    )

  def deleteCustomer(name: CustomerDeleted, team: String, info: CollaborativeEventInfo): Future[Seq[BoundStatement]] =
    Future.successful(
      name.lumiquoteTenant.map { lq =>
        List(stmtDeleteCustomer.bind()
          .setString("team", team)
          .setString("lqtenant", lq)
          .setUUID("id", name.id))
      }.getOrElse(List())
    )

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[CustomerEvent] =
    readSide.builder[CustomerEvent]("customerLqTenantEventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[CustomerSet] { e =>
        setCustomer(e.event)
      }
      .setEventHandler[CustomerDeleted](e => deleteCustomer(e.event, e.event.team, e.event.info))
      //      .setEventHandler[CollaborativeInfoSet](e => setCollab(e.event))
      //      .setEventHandler[CustomerUpdated](e => setCustomer(e.event))
      //      .setEventHandler[ImageSet](e => setImage(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[CustomerEvent]] = CustomerEvent.Tag.allTags

  var stmtUpdateCustomer: PreparedStatement = _
  var stmtDeleteCustomer: PreparedStatement = _

  def createTables(): Future[Done] =
    for {
      // language=SQL

      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS customerLumiquote(
          | team text,
          | lqtenant text,
          | id uuid,
          | change timestamp,
          | PRIMARY KEY ( team, lqtenant, id )
          |)
          |""".stripMargin
      )

    } yield Done

  def prepareStatements(): Future[Done] =
    for {
      // language=SQL

      deleteCustomer <- session.prepare(
        """
          |DELETE FROM customerLumiquote WHERE team = :team AND lqtenant = :lqtenant AND id = :id
          |""".stripMargin
      )
      updateCustomer <- session.prepare(
        """
          |UPDATE customerLumiquote SET change = :change WHERE team = :team  AND lqtenant = :lqtenant AND id = :id
          |""".stripMargin
      )

    } yield {
      stmtUpdateCustomer = updateCustomer
      stmtDeleteCustomer = deleteCustomer
      Done
    }
}
