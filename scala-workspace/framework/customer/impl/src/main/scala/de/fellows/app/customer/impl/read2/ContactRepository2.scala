package de.fellows.app.customer.impl.read2

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{ BoundStatement, PreparedStatement }
import com.datastax.driver.extras.codecs.jdk8.InstantCodec
import com.lightbend.lagom.scaladsl.persistence.cassandra.{ CassandraReadSide, CassandraSession }
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEventTag, ReadSideProcessor }
import de.fellows.app.customer.impl.entity.contact.Events.{ ContactDeleted, ContactEvent, ContactSet }
import de.fellows.utils.codec.CodecHelper.registerCodec
import de.fellows.utils.communication.ServiceDefinition
import play.api.Logging

import java.time.Instant
import java.util.UUID
import scala.concurrent.{ ExecutionContext, Future }

class ContactRepository2(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends Logging {

  session.underlying().map(s => registerCodec(InstantCodec.instance)(s))

  def getContactsForCustomer(team: String, customer: UUID): Future[Seq[ContactDTO]] =
    session.selectAll(
      """
        |SELECT id, customer FROM contactByCustomer WHERE team = ? AND customer = ?
        |""".stripMargin,
      team,
      customer
    )
      .map(_.map(r => ContactDTO(r.getUUID("id"), r.getUUID("customer"))))
}

class ContactEventProcessor2(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[ContactEvent] {
  var stmtUpdateContact: PreparedStatement = _
  var stmtDeleteContact: PreparedStatement = _

  def createTables(): Future[Done] =
    for {
      // language=SQL

      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS contactByCustomer(
          | team text,
          | customer uuid,
          | id uuid,
          | updated timestamp,
          | PRIMARY KEY ( team, customer, id )
          |)
          |""".stripMargin
      )

    } yield Done

  def prepareStatements(): Future[Done] =
    for {
      // language=SQL

      deleteContact <- session.prepare(
        """
          |DELETE FROM contactByCustomer WHERE team = :team AND customer = :customer AND id = :id
          |""".stripMargin
      )
      updateContact <- session.prepare(
        """
          |UPDATE contactByCustomer SET updated = :updated WHERE team = :team  AND customer = :customer AND id = :id
          |""".stripMargin
      )

    } yield {
      stmtUpdateContact = updateContact
      stmtDeleteContact = deleteContact
      Done
    }

  def setContact(event: ContactSet): Future[Seq[BoundStatement]] =
    Future.successful((if (event.oldContent.isDefined && event.oldContent.get.customer != event.content.get.customer) {
                         val oldcnt = event.oldContent.get
                         List(stmtDeleteContact.bind(oldcnt.team, oldcnt.customer, oldcnt.id))
                       } else {
                         List()
                       }) :+ stmtUpdateContact.bind(
      Instant.now(),
      event.content.get.team,
      event.content.get.customer,
      event.content.get.id
    ))

  def deleteContact(event: ContactDeleted): Future[Seq[BoundStatement]] =
    Future.successful(List(stmtDeleteContact.bind(event.team, event.customer, event.id)))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[ContactEvent] =
    readSide.builder[ContactEvent]("contact2EventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[ContactSet](e => setContact(e.event))
      .setEventHandler[ContactDeleted](e => deleteContact(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[ContactEvent]] = ContactEvent.Tag.allTags
}
