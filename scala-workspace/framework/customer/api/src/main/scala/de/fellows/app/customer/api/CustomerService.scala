package de.fellows.app.customer.api

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.Service._
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.utils.PaginationListResult
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.{OpenAPIDefinition, Operation}

import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate Customer API"
  )
)
trait CustomerService extends Service with StackrateServiceAPI {
  val subPath          = "customer"
  val basePath         = s"/api/$subPath"
  val internalBasePath = s"/internal/$subPath/:team"

  val customerBasePath = s"/api/$subPath/customers"
  val contactsBasePath = s"$customerBasePath/:customer/contacts"

  val internalCustomerBasePath = s"$internalBasePath/customers"
  val internalContactsBasePath = s"$internalCustomerBasePath/:customer/contacts"

  val tagsBasePath = s"$customerBasePath/:customer/tags"

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Get Customers"
  )
  def getCustomers(
      contacts: Option[Boolean],
      page: Option[Int],
      pagesize: Option[Int]
  ): ServiceCall[NotUsed, PaginationListResult[Customer]]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Find Customers"
  )
  def findCustomers(
      contacts: Option[Boolean],
      domain: Option[String],
      page: Option[Int],
      pagesize: Option[Int],
      q: Option[String]
  ): ServiceCall[NotUsed, PaginationListResult[Customer]]

  def _findCustomers(
      team: String,
      contacts: Option[Boolean],
      domain: Option[String],
      page: Option[Int],
      pagesize: Option[Int],
      q: Option[String]
  ): ServiceCall[NotUsed, PaginationListResult[Customer]]

  def _findCustomerByLumiquote(
      team: String,
      tenant: String
  ): ServiceCall[NotUsed, Seq[Customer]]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Set Customer"
  )
  def updateCustomer(customer: UUID): ServiceCall[Customer, CustomerResult]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Create Customer"
  )
  def createCustomer(): ServiceCall[Customer, CustomerResult]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Get Customer"
  )
  def getCustomer(customer: UUID, contacts: Option[Boolean]): ServiceCall[NotUsed, Customer]

  def _getCustomer(team: String, customer: UUID, contacts: Option[Boolean]): ServiceCall[NotUsed, Customer]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Delete Customer"
  )
  def deleteCustomer(customer: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = "Contacts")
  @Operation(
    summary = "Get Contacts From Customer"
  )
  def getContacts(customer: UUID): ServiceCall[NotUsed, Seq[Contact]]

  @StackrateApi
  @Tag(name = "Contacts")
  @Operation(
    summary = "Create Contacts For Customer"
  )
  def createContact(customer: UUID): ServiceCall[Contact, ContactResult]

  @StackrateApi
  @Tag(name = "Contacts")
  @Operation(
    summary = "Update Contact"
  )
  def updateContact(customer: UUID, contact: UUID): ServiceCall[Contact, ContactResult]

  @StackrateApi
  @Tag(name = "Contacts")
  @Operation(
    summary = "Get Contact"
  )
  def getContact(customer: UUID, contact: UUID): ServiceCall[NotUsed, Contact]

  def _getContact(team: String, customer: UUID, contact: UUID): ServiceCall[NotUsed, Contact]

  @StackrateApi
  @Tag(name = "Contacts")
  @Operation(
    summary = "Delete Contact"
  )
  def deleteContact(customer: UUID, contact: UUID): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Get Customer Tags"
  )
  def getTags(customer: UUID): ServiceCall[NotUsed, Seq[CustomerTag]]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Set Customer Tags"
  )
  def setTags(customer: UUID): ServiceCall[Seq[CustomerTag], CustomerResult]

  @StackrateApi
  @Tag(name = "Customer")
  @Operation(
    summary = "Fix address IDs"
  )
  def fixAddressIdForTeam(team: Option[String]): ServiceCall[NotUsed, NotUsed]

  //  def customerCreated(): Topic[CustomerMessage]
  //
  //  def customerDeleted(): Topic[UUID]

  override def descriptor: Descriptor =
    withDocumentation(
      named("customer")
        .withCalls(
          restCall(Method.GET, s"$customerBasePath?contacts&page&pagesize", getCustomers _),
          restCall(Method.GET, s"$customerBasePath/find?contacts&domain&page&pagesize&q", findCustomers _),
          restCall(Method.GET, s"$customerBasePath/fixAddressId?team", fixAddressIdForTeam _),
          restCall(Method.POST, s"$customerBasePath", createCustomer _),
          restCall(Method.PUT, s"$customerBasePath/:id", updateCustomer _),
          restCall(Method.DELETE, s"$customerBasePath/:id", deleteCustomer _),
          restCall(Method.GET, s"$customerBasePath/:id?contacts", getCustomer _),
          restCall(Method.GET, s"$contactsBasePath", getContacts _),
          restCall(Method.POST, s"$contactsBasePath", createContact _),
          restCall(Method.GET, s"$tagsBasePath", getTags _),
          restCall(Method.POST, s"$tagsBasePath", setTags _),
          restCall(Method.PUT, s"$contactsBasePath/:contact", updateContact _),
          restCall(Method.GET, s"$contactsBasePath/:contact", getContact _),
          restCall(Method.DELETE, s"$contactsBasePath/:contact", deleteContact _),

          // INTERNAL
          restCall(Method.GET, s"$internalBasePath/find?contacts&domain&page&pagesize&q", _findCustomers _),
          restCall(Method.GET, s"$internalBasePath/find/by-lumiquote/:tenant", _findCustomerByLumiquote _),
          restCall(Method.GET, s"$internalContactsBasePath/:contact", _getContact _),
          restCall(Method.GET, s"$internalCustomerBasePath/:id?contacts", _getCustomer _)
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"/api/customer/.*")),
          ServiceAcl(pathRegex = Some(s"/files/customer/.*"))
        )
        .withTopics(
          //        topic(CUSTOMER_CREATED, customerCreated),
          //        topic(CUSTOMER_DELETED, customerDeleted)
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
}

object CustomerService {
  val VERSION          = "v1.1"
  val CUSTOMER_CREATED = s"domain.customer.created-$VERSION"
  val CUSTOMER_DELETED = s"domain.customer.deleted-$VERSION"

}
//

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
