package de.fellows.app.project.api

import java.time.Instant

import play.api.libs.json.{ Format, Json }

case class Project(
    name: String,
    owner: String,
    desc: String,
    lastUpdate: Option[Instant],
    created: Option[Instant],
    image: Option[String],
    status: ProjectStatus,
    tags: Seq[Tag],
    currentVersion: Option[ProjectVersionId]
)

case class ProjectVersionId(identifier: String, status: VersionStatus, ancestor: Option[ProjectVersionId])

case class Tag(title: String, color: Color, icon: String)

case class Color(r: Int, g: Int, b: Int, a: Option[Int])

sealed trait Versioned[T <: Versioned[T]] {
  def identifier: String
  def state: VersionStatus
  def predecessor: Option[T]
}
case class ProjectVersion(
    override val identifier: String,
    override val state: VersionStatus,
    override val predecessor: Option[ProjectVersion],
    features: Seq[ProjectFeature]
) extends Versioned[ProjectVersion]

case class ProjectFeature(
    override val identifier: String,
    override val state: VersionStatus,
    override val predecessor: Option[ProjectFeature],
    featureType: String
) extends Versioned[ProjectFeature]

sealed trait ProjectStatus
case class Active(name: String)   extends ProjectStatus
case class Archived(name: String) extends ProjectStatus
case class Deleted(name: String)  extends ProjectStatus

sealed trait VersionStatus {
  def created: Instant
}
case class Released(override val created: Instant, date: Instant) extends VersionStatus
case class Open(override val created: Instant)                    extends VersionStatus

object Project {
  implicit val format: Format[Project] = Json.format[Project]
}

object ProjectVersionId {
  implicit val format: Format[ProjectVersionId] = Json.format[ProjectVersionId]
}

object Tag {
  implicit val format: Format[Tag] = Json.format[Tag]
}

object Color {
  implicit val format: Format[Color] = Json.format[Color]
}

object ProjectVersion {
  implicit val format: Format[ProjectVersion] = Json.format[ProjectVersion]
}
object ProjectFeature {
  implicit val format: Format[ProjectFeature] = Json.format[ProjectFeature]
}

object Active {
  implicit val format: Format[Active] = Json.format[Active]
}

object Archived {
  implicit val format: Format[Archived] = Json.format[Archived]
}

object Deleted {
  implicit val format: Format[Deleted] = Json.format[Deleted]
}

object Released {
  implicit val format: Format[Released] = Json.format[Released]
}

object Open {
  implicit val format: Format[Open] = Json.format[Open]
}
