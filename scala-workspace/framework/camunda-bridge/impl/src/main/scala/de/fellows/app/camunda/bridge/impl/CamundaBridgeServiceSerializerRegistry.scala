package de.fellows.app.camunda.bridge.impl

import com.lightbend.lagom.scaladsl.playjson.{ JsonSerializer, JsonSerializerRegistry }
import de.fellows.utils.entities.teamsettings.TeamSettingsJsonSerializerRegistry
import play.api.libs.json._

object CamundaBridgeServiceSerializerRegistry extends JsonSerializerRegistry {
  override def serializers =
    List(
      JsonSerializer[JsValue],
      JsonSerializer[JsArray],
      JsonSerializer[JsObject],
      JsonSerializer[JsNumber],
      JsonSerializer[JsString],
      JsonSerializer[JsBoolean]
    ) ++ TeamSettingsJsonSerializerRegistry.serializers
}
