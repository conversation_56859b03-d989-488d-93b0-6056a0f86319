package de.fellows.app.camunda.bridge.impl

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.camunda.api.CamundaService
import de.fellows.app.camunda.bridge.api.CamundaBridgeService
import de.fellows.app.camunda.bridge.impl.entities.settings.CamundaTeamSettingsEntity
import de.fellows.app.camunda.bridge.impl.listeners._
import de.fellows.app.inbox.api.InboxService
import de.fellows.app.quotation.QuotationService
import de.fellows.app.supplier.SupplierService
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.ems.renderer.api.RendererService
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import de.fellows.utils.communication.ServiceDefinition
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class CamundaBridgeServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new CamundaBridgeServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new CamundaBridgeServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[CamundaBridgeService])
}

trait CamundaBridgeServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("camunda-bridge")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = CamundaBridgeServiceSerializerRegistry

  //  lazy val notificationRepository = wire[SupplierRepository]

  //  val userEventProcessor = wire[SupplierEventProcessor]
  //  readSide.register(userEventProcessor)
  //  readSide.register(wire[SessionEventProcessor])

  //  persistentEntityRegistry.register(wire[PCBSupplierEntity])

  persistentEntityRegistry.register(wire[CamundaTeamSettingsEntity])

}

abstract class CamundaBridgeServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with CamundaBridgeServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)
  override lazy val lagomServer                  = serverFor[CamundaBridgeService](wire[CamundaBridgeServiceImpl])

  serviceClient.implement[CamundaService]
  lazy val camundaService    = serviceClient.implement[CamundaService]
  lazy val supplierService   = serviceClient.implement[SupplierService]
  lazy val assemblyService   = serviceClient.implement[AssemblyService]
  lazy val inboxService      = serviceClient.implement[InboxService]
  lazy val pcbService        = serviceClient.implement[PCBService]
  lazy val rendererService   = serviceClient.implement[RendererService]
  lazy val layerstackService = serviceClient.implement[LayerstackService]
  lazy val quotatinService   = serviceClient.implement[QuotationService]
  lazy val userService       = serviceClient.implement[UserService]

  implicit val pcbChangeListener: SupplierServiceListener = wire[SupplierServiceListener]
  implicit val assemblyListener: AssemblyListener         = wire[AssemblyListener]
  implicit val pcbListener: PCBListener                   = wire[PCBListener]
  implicit val inboxListener: InboxListener               = wire[InboxListener]
  implicit val layerstackListener: LayerStackListener     = wire[LayerStackListener]
  implicit val userListener: UserServiceListener          = wire[UserServiceListener]
  implicit val qutationListener: QuotationListener        = wire[QuotationListener]
  //    .additionalRouter(fileRouter.router)

  //  lazy val fileRouter: AssemblyFileUploadService = wire[AssemblyFileUploadService].withApp(this)

}
