package de.fellows.app.camunda.bridge.impl.listeners

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.api.ServiceLocator
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.camunda.api.{CamundaService, SignalCall, Signals, Variable}
import de.fellows.app.camunda.bridge.impl.BridgeSignals
import de.fellows.ems.pcb.api._
import de.fellows.utils.TopicUtils
import de.fellows.utils.streams.{EmptyMessage, ValidMessage}
import org.slf4j.{Logger, LoggerFactory}
import play.api.libs.json.Json
import TopicUtils.defaultNaming

import scala.concurrent.{ExecutionContext, Future}

class PCBListener(pcb: PCBService, camunda: CamundaService, preg: PersistentEntityRegistry, locator: ServiceLocator)(
    implicit val ec: ExecutionContext
) {
  private final val log: Logger =
    LoggerFactory.getLogger(classOf[PCBListener])

  val started = System.currentTimeMillis()

  TopicUtils.subscribeLatest(pcb.pcbEvents(), started) { msg =>
    msg.payload match {

      case ValidMessage(msg: DFMMessage)          => _doDfmChange(msg)
      case ValidMessage(msg: RenderChangeMessage) => _doRenderChanged(msg)
      case ValidMessage(msg: RenderAddedMessage)  => _doRenderAdded(msg)

    }

  }

  def genericEvent(name: String, assRef: AssemblyReference, additionalVariables: Map[String, Variable]) = {
    val sig1 = name
    val sig2 = s"$name-${assRef.version}"

    def createSignal(s: String, vals: Map[String, Variable]) =
      SignalCall(
        s,
        None,
        additionalVariables ++ vals ++
          Signals.reference(assRef)
      )

    BridgeSignals.getDefaultVariables(assRef.team, preg).flatMap { vals =>
      for {
        _ <- camunda.signal().invoke(createSignal(sig1, vals))
        _ <- camunda.signal().invoke(createSignal(sig2, vals))
      } yield Done
    }.recover {
      case e: Throwable =>
        log.error(s"Failed to send BPM signal ${sig1}", e)
        Done
    }
  }

  private def _doRenderChanged(msg: RenderChangeMessage) = {
    val signal1 = s"backend.pcb.render.changed-${msg.ref.version}"
    val signal2 = s"backend.pcb.render.changed"

    def createSignal(s: String, vals: Map[String, Variable]) =
      SignalCall(
        s,
        None,
        Map(
          ("matchfiles" -> Variable.json(Json.toJson(msg.files))),
          ("matched"    -> Variable.json(Json.toJson(msg.matched))),
          ("unmatched"  -> Variable.json(Json.toJson(msg.unmatched))),
          ("known"      -> Variable.json(Json.toJson(msg.known))),
          ("unknown"    -> Variable.json(Json.toJson(msg.unknown))),
          ("rendered"   -> Variable.json(Json.toJson(msg.rendered))),
          ("failed"     -> Variable.json(Json.toJson(msg.failed))),
          ("version"    -> Variable.json(Json.toJson(msg.ref.version)))
        ) ++ vals ++
          Signals.reference(msg.ref)
      )

    BridgeSignals.getDefaultVariables(msg.ref.team, preg).flatMap { vals =>
      for {
        _ <- camunda.signal().invoke(createSignal(signal1, vals))
        _ <- camunda.signal().invoke(createSignal(signal2, vals))
      } yield Done
    }.recover {
      case e: Throwable =>
        log.error(s"Failed to send BPM signal ${signal1}", e)
        Done
    }
  }

  //  pcb.renderAddedTopic().subscribe.atLeastOnce(
  //    Flow[RenderAddedMessage].mapAsync(1){ msg => {
  //      _doRenderAdded(msg)
  //    }
  //    }
  //  )

  private def _doRenderAdded(msg: RenderAddedMessage) = {
    val signal = s"backend.pcb.render.added"
    BridgeSignals.getDefaultVariables(msg.ref.team, preg).flatMap { vals =>
      camunda.signal().invoke(
        SignalCall(
          signal,
          None,
          Map(
            ("file"     -> Variable.json(Json.toJson(msg.file))),
            ("fileName" -> Variable(msg.file.name)),
            ("fileId"   -> Variable(msg.file.id)),
            ("matched"  -> Variable.json(Json.toJson(msg.newRender)))
          ) ++ vals ++
            Signals.reference(msg.ref)
        )
      )
    }
      .map(_ => Done).recover {
        case e: Throwable =>
          log.error(s"Failed to send BPM signal ${signal}", e)
          Done
      }
  }

  pcb.specificationSavedTopic().subscribe.atLeastOnce(
    Flow[SpecificationSavedMessage].mapAsync(5) { msg =>
      msg.spec.map { spec =>
        val signal = s"specification-saved"
        log.warn(s"specification saved: ${msg}")
        BridgeSignals.getDefaultVariables(spec.assembly.team, preg).flatMap { vals =>
          camunda.signal().invoke(
            SignalCall(
              signal,
              None,
              Map(
                ("specification"   -> Variable.json(Json.toJson(spec.merged()))),
                ("specificationId" -> Variable(spec.id))
              ) ++ vals ++
                Signals.reference(spec.assembly)
            )
          )
        }
          .map(_ => Done).recover {
            case e: Throwable =>
              log.error(s"Failed to send BPM signal ${signal}", e)
              Done
          }

      }.getOrElse(Future.successful(Done))
    }
  )

  //  pcb.pcbUpdatesTopic().subscribe.atLeastOnce(
  //    Flow[PCBChangedMessage].mapAsync(1){
  //      case msg: DefaultSpecificationMessage => {
  //        genericEvent("backend.pcb.specification.defaultchanged", msg.assembly, Map(
  //          "defaultSpecification" -> Variable(msg.newDefault)
  //        ))
  //      }
  //
  //      case _ => Future.successful(Done)
  //    }
  //  )

  //  pcb.dfmTopic().subscribe.atLeastOnce(
  //    Flow[DFMMessage].mapAsync(5){ msg => {
  //      _doDfmChange(msg)
  //    }
  //    }
  //  )

  //  pcb.layerstackTopic().subscribe.atLeastOnce(
  //    Flow[LayerStackMessage].mapAsync(5){ msg =>
  //      val signal = s"layerStackCreated"
  //
  //      camunda.signal().invoke({
  //        val sg = Signals.create(msg.assRef.version, signal, Map(
  //          "stackSize" -> Variable(msg.stack.size)
  //        )).copy(tenantId = Some(msg.assRef.team))
  //
  //        log.info(s"send layerstack $sg")
  //        sg
  //      }
  //      ).map(_ => Done).recover{
  //        case e: Throwable => {
  //          log.error(s"Failed to send BPM signal ${signal}", e)
  //          Done
  //        }
  //      }
  //    }
  //  )

  private def _doDfmChange(msg: DFMMessage) = {
    val signal = s"dfm_${msg.status.name}"

    BridgeSignals.create(
      Some(msg.assRef.version),
      msg.assRef.team,
      signal,
      preg,
      Signals.reference(msg.assRef)
    ).flatMap { sgnl =>
      println(s"send signal $sgnl")
      camunda.signal().invoke(sgnl)
    }.map(_ => Done).recover {
      case e: Throwable =>
        log.error(s"Failed to send BPM signal ${signal}", e)
        Done
    }
  }
}
