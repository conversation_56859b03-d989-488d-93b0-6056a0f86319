package de.fellows.app.camunda.bridge.impl

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.ConfigFactory
import de.fellows.app.camunda.api.{ SignalCall, Variable }
import de.fellows.app.camunda.bridge.impl.entities.settings.{ CamundaSettings, CamundaTeamSettingsEntity }
import de.fellows.utils.entities.teamsettings.{ GetSettings, TeamSettings, TeamSettingsUtils }
import de.fellows.utils.security.{ JwtTokenUtil, SecurityClaim, TokenContent }

import java.util.UUID
import scala.concurrent.{ ExecutionContext, Future }

object BridgeSignals {

  def create(
      businessKey: Option[UUID],
      team: String,
      name: String,
      reg: PersistentEntityRegistry,
      vars: Map[String, Variable] = Map()
  )(implicit ec: ExecutionContext): Future[SignalCall] = {

    val v = businessKey.map(k =>
      Map(
        "businessKey" -> Variable(k)
      )
    ).getOrElse(Map())

    val sname = s"$name${businessKey.map(bk => s"-${bk}").getOrElse("")}"

    getDefaultVariables(team, reg).map { vars =>
      SignalCall(
        sname,
        None,
        v ++ vars
      )
    }
  }

  def getDefaultVariables(team: String, persistentEntityRegistry: PersistentEntityRegistry)(implicit
      ec: ExecutionContext
  ): Future[Map[String, Variable]] =
    persistentEntityRegistry.refFor[CamundaTeamSettingsEntity](team).ask(GetSettings(team)).map { ts =>
      getDefaultVariables(team, ts) { vars =>
        vars
      }
    }

  def getAdminVariables(persistentEntityRegistry: PersistentEntityRegistry)(implicit
      ec: ExecutionContext
  ): Future[Map[String, Variable]] = {
    val adminTeam = ConfigFactory.load().getString("fellows.services.camunda-bridge.admin_team")
    getDefaultVariables(adminTeam, persistentEntityRegistry)
  }

  def getDefaultVariables[T](team: String, ts: TeamSettings)(c: Map[String, Variable] => T): T = {

    val tkn       = TokenContent.technicalUser(claims = Seq(SecurityClaim(Some(s"*:$team:*:*:*:*"))), team = team)
    val authtoken = JwtTokenUtil.generateTokens(tkn)

    val e = Seq(
      TeamSettingsUtils.resolve[String](CamundaSettings.ENDPOINT, ts).map(x => "endpoint" -> Variable(x)),
      TeamSettingsUtils.resolve[String](CamundaSettings.TOKEN, ts).map(x => "apiToken" -> Variable(x))
    ).flatten

    c(Map(
      "team"      -> Variable(team),
      "authToken" -> Variable(authtoken.authToken)
    ) ++ e)

    //
    //    TeamSettingsUtils.resolve[String](CamundaSettings.TOKEN, ts) match {
    //      case Some(s) =>
    //        c(Map(
    //          "endpoint" -> Variable(conf.getString("fellows.baseURL")),
    //          "apiToken" -> Variable(s),
    //          "team" -> Variable(team),
    //        ))
    //      case None => throw new TransportException(TransportErrorCode.NotFound, "Team has no token")
    //    }
  }
}
