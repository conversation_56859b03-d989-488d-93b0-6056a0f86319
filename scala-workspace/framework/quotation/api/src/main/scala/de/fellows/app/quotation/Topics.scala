package de.fellows.app.quotation

import play.api.libs.json.{ Format, Json }

import java.util.UUID

object Topics {

  sealed trait QuotationMessage {}

  case class QuotationStatusChangedMsg(
      team: String,
      quotationId: UUID,
      customerId: UUID,
      name: String,
      oldStatus: QuotationStatus,
      status: QuotationStatus
  ) extends QuotationMessage

  case class QuotationCreatedMsg(quotation: Quotation) extends QuotationMessage

  case class QuotationSentMsg(quotation: Quotation) extends QuotationMessage

  object QuotationMessage {
    implicit val format: Format[QuotationMessage] = Json.format
  }

  object QuotationStatusChangedMsg {
    implicit val format: Format[QuotationStatusChangedMsg] = Json.format
  }

  object QuotationCreatedMsg {
    implicit val format: Format[QuotationCreatedMsg] = Json.format
  }

  object QuotationSentMsg {
    implicit val format: Format[QuotationSentMsg] = Json.format
  }

}
