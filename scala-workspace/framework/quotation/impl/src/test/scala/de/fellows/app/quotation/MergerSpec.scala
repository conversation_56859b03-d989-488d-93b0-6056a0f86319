package de.fellows.app.quotation

import de.fellows.app.quotation.uni.PriceBreakMerger
import de.fellows.app.quotation.uni.QuotationInternals.UnifiedPriceBreak
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
class MergerSpec extends AnyWordSpec with BeforeAndAfterAll with Matchers {
  "merger" should {
    "merge distinct" in {
      val u1 =
        UnifiedPriceBreak(
          "test",
          10,
          20,
          1,
          Some(10),
          None,
          None,
          None
        )
      val u2 =
        UnifiedPriceBreak(
          "test",
          30,
          40,
          1,
          Some(10),
          None,
          None,
          None
        )

      println(new PriceBreakMerger().merge(u1, u2).result())
    }

    "merge overlap" in {
      val u1 =
        UnifiedPriceBreak(
          "test",
          10,
          20,
          1,
          Some(10),
          None,
          None,
          None
        )
      val u2 =
        UnifiedPriceBreak(
          "test2",
          15,
          30,
          1,
          Some(10),
          None,
          None,
          None
        )

      println(new PriceBreakMerger().merge(u1, u2).result())
    }

    "merge overlap 1" in {
      val u1 =
        UnifiedPriceBreak(
          "test",
          10,
          20,
          1,
          Some(10),
          None,
          None,
          None
        )
      val u2 =
        UnifiedPriceBreak(
          "test",
          10,
          20,
          1,
          Some(10),
          None,
          None,
          None
        )

      println(new PriceBreakMerger().merge(u1, u2).result())
    }

    "merge overlap 2" in {
      val u1 =
        UnifiedPriceBreak(
          "test",
          10,
          40,
          1,
          Some(16),
          None,
          None,
          None
        )
      val u2 =
        UnifiedPriceBreak(
          "test2",
          20,
          30,
          1,
          Some(10),
          None,
          None,
          None
        )

      println(new PriceBreakMerger().merge(u1, u2).result())
    }

    "merge overlap unit/panel" in {
      val u1 =
        UnifiedPriceBreak(
          "test",
          10,
          40,
          1,
          Some(16),
          None,
          None,
          None
        )
      val u2 =
        UnifiedPriceBreak(
          "test2",
          20,
          30,
          1,
          None,
          Some(360),
          Some(1),
          None
        )

      println(new PriceBreakMerger().merge(u1, u2).result())
    }
  }
}
