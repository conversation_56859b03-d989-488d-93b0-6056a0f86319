package de.fellows.app.quotation

import akka.actor.ActorSystem
import akka.persistence.query.Offset
import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.datastax.driver.core.utils.UUIDs
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{ResponseHeader, TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.api.{ServiceCall, ServiceLocator}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRef, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.assembly.commons.{AssemblyReference, SharedAssemblyReference}
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.inbox.api.InboxService
import de.fellows.app.profile.api.ProfileService
import de.fellows.app.quotation.QuotationOrigin.ManualQuotation
import de.fellows.app.quotation.Topics.{QuotationCreatedMsg, QuotationMessage, QuotationStatusChangedMsg}
import de.fellows.app.quotation.entity.number.{AllocateNumbers, InitializeNumber, NumberEntity, PeekNext}
import de.fellows.app.quotation.entity.pricelist.PriceListEvents.PriceListSet
import de.fellows.app.quotation.entity.quotation.QuotationCommands._
import de.fellows.app.quotation.entity.quotation.QuotationEntity
import de.fellows.app.quotation.entity.quotation.QuotationEvents.{
  QuotationCreated,
  QuotationEvent,
  QuotationStatusSet,
  QuotationTimelineChanged
}
import de.fellows.app.quotation.entity.quotationitem.QuotationItemCommands.{
  DeleteQuotationItem,
  GetQuotationItem,
  SetQuotationItem,
  SetQuotationItemInfo,
  SetQuotationItemWon
}
import de.fellows.app.quotation.entity.quotationitem.QuotationItemEvents.{
  QuotationItemEvent,
  QuotationItemTimelineChange
}
import de.fellows.app.quotation.entity.quotationitem.{QuotationItemCommands, QuotationItemEntity}
import de.fellows.app.quotation.pdf._
import de.fellows.app.quotation.read.{
  PricedAssemblyRepo,
  QuotationContentReadSide,
  QuotationItemRepository,
  QuotationRepository
}
import de.fellows.app.security.AccessControl
import de.fellows.app.security.AccessControlServiceComposition.{authorizedString, authorizedStringWithToken}
import de.fellows.app.security.CombinedTokenAccessServiceComposition.auth
import de.fellows.app.supplier.SupplierService
import de.fellows.app.user.api.TemplateAPI.RenderRequest
import de.fellows.app.user.api.UserService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.collaboration.{TimelineCommand, TimelineEvent, TimelineUser}
import de.fellows.utils.common.Address
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.communication.TransportErrors._
import de.fellows.utils.mailgun.MailgunService
import de.fellows.utils.security.{Auth0Token, AuthenticationServiceComposition, TokenContent}
import de.fellows.utils.{FilePath, FutureUtils, PaginationListResult, UUIDUtils}
import play.api.Logging
import play.api.libs.json.Json
import play.api.libs.ws.WSClient
import play.mvc.Http.HeaderNames

import java.time.Instant
import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}
import de.fellows.app.quotation.QuotationOrigin.APIQuotation
import de.fellows.app.assembly.commons.AbstractAssemblyReference
import de.fellows.ems.pcb.api.PCBV2Layer
import de.fellows.utils.meta.Property
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.util.zip.ZipOutputStream
import java.util.zip.ZipEntry
import de.fellows.utils.meta.MetaInfo
import de.fellows.app.quotation.export.CSVExport

class QuotationServiceImpl(
    eReg: PersistentEntityRegistry,
    priceRepo: PricedAssemblyRepo,
    inboxService: InboxService,
    quots: QuotationRepository,
    quotContents: QuotationContentReadSide,
    quotItems: QuotationItemRepository,
    assService: AssemblyService,
    pcbService: PCBService,
    custService: CustomerService,
    userService: UserService,
    profileService: ProfileService,
    supplierService: SupplierService,
    panelService: PanelService,
    layerstackService: LayerstackService,
    mailgun: MailgunService,
    ws: WSClient,
    loc: ServiceLocator,
    system: ActorSystem
)(implicit ec: ExecutionContext, sd: ServiceDefinition) extends QuotationService
    with StackrateAPIImpl with Logging {
  implicit val actorSystem: ActorSystem = system

  implicit val cconfig: Config = ConfigFactory.load()

  def emtpyList[L <: Iterable[_]](l: Option[L]): Option[L] =
    l match {
      case None                 => None
      case Some(l) if l.isEmpty => None
      case Some(l)              => Some(l)
    }

  val templateCategory = "quotation"

  val conf = ConfigFactory.load()

  def renderTemplate(team: String, template: Option[String], quotation: Quotation): Future[Option[String]] =
    FutureUtils.option(template.map { t =>
      this.getEnrichedQuotation(team, quotation).flatMap { q =>
        val variable = Map(
          "quotation" -> Json.toJson(q)
        )

        val filepaths = collectFiles(q)
          .map(x => x.toPath)

        val body = RenderRequest(
          resources = filepaths,
          variables = variable
        )

        userService._renderHtmlTemplate(team, "quotation", Some(t)).invoke(body)
      }
    })

  override def deleteQuotation(idOrName: String): ServiceCall[NotUsed, Done] = {
    var quotation: Option[UUID] = None

    authorizedString { token =>
      quotation = UUIDUtils.maybeInstant(
        idOrName,
        () =>
          quots.getQuotationByName(token.team, idOrName)
      )
      s"quotation:${token.team}:${token.team}:${quotation.get}:quotation:write"
    } { (token, _) =>
      ServerServiceCall { q =>
        val tcmd = TimelineCommand.of(token)
        _doDeleteQuotation(token.team, quotation.get, tcmd)
      }
    }
  }

  override def _deleteQuotation(team: String, idOrName: String): ServiceCall[NotUsed, Done] =
    ServerServiceCall { q =>
      val tcmd = TimelineCommand.system
      UUIDUtils.maybe(
        idOrName,
        () =>
          quots.getQuotationByName(team, idOrName)
      ).flatMap {
        case quotation @ Some(value) =>
          _doDeleteQuotation(team, quotation.get, tcmd)
        case None =>
          throw new TransportException(TransportErrorCode.NotFound, "quotation not found")
      }
    }

  override def _deleteQuotationsForShare(team: String): ServiceCall[SharedQuotationDeletion, Done] =
    ServerServiceCall { deletion =>
      Future.sequence(
        deletion.shares.map { share =>

          (for {
            allQuotationIDs <- quots.getAllQuotationsByVersion(team, share.id)
            allQuotations <- Future.sequence(allQuotationIDs.map { qid =>
              val entity = eReg.refFor[QuotationEntity](qid.toString)
              entity.ask(GetQuotation(team, qid)).map(_ -> entity)
            })
          } yield Future.sequence(allQuotations.map { x =>
            val (quotation, entity) = x


            def checkRequestId(value: Seq[String]) = {
              quotation.requestID match {
                case Some(requestID) => value.contains(requestID)
                case None            => false
              }
            }

            val shouldDelete = (deletion.names, deletion.requestIds) match {
              case (Some(value), None) => value.contains(quotation.name)
              case (None, Some(value)) => {
                checkRequestId(value)
              }

              case (Some(name), Some(req)) => name.contains(quotation.name) && checkRequestId(req)
              case (None, None)        => true
            }

            if (shouldDelete) {
              entity.ask(SetQuotationStatus(
                team,
                quotation.quotationId,
                QuotationStatus.DELETED(Instant.now()),
                TimelineCommand.system
              )).map(_ => Done)
            } else {
              Future.successful(Done)
            }
          })).flatten
        }
      ).map(_ => Done)
    }

  private def _doDeleteQuotation(team: String, quotation: UUID, tcmd: TimelineCommand) = {
    val entity = eReg.refFor[QuotationEntity](quotation.toString)
    entity.ask(GetQuotation(team, quotation)).flatMap { q =>
      val status =
        if (q.status.name == QuotationStatus.DRAFT_NAME) {
          QuotationStatus.DELETED(Instant.now())
        } else {
          QuotationStatus.ARCHIVED(Instant.now())
        }
      entity.ask(SetQuotationStatus(team, quotation, status, tcmd)).map(_ => Done)
    }
  }

  override def updateQuotationStatus(idOrName: String): ServiceCall[QuotationStatus, Quotation] = {
    var quotation: Option[UUID] = None
    authorizedString { token =>
      quotation = UUIDUtils.maybeInstant(
        idOrName,
        () =>
          quots.getQuotationByName(token.team, idOrName)
      )
      s"quotation:${token.team}:${token.team}:${quotation.get}:quotation:create"
    } { (token, _) =>
      ServerServiceCall { q =>
        val tcmd   = TimelineCommand.of(token)
        val entity = eReg.refFor[QuotationEntity](quotation.get.toString)

        entity.ask(SetQuotationStatus(
          token.team,
          quotation = quotation.get,
          status = q.copy(date = Some(Instant.now())),
          tcmd
        ))
      }
    }
  }

  /** verifies that all the given item ids are created for the given version
    * @param version
    * @param items
    * @return
    */
  def validateItems(version: UUID, items: Seq[UUID]): Future[Boolean] =
    // TODO verify using read sides. depending on the number of items this might be too resource intensive
    Future.sequence(items.map(v => eReg.refFor[QuotationItemEntity](v.toString).ask(GetQuotationItem(v)))).map {
      items =>
        items.map(_.assembly.getReferenceIdentifier).forall(_ == version)
    }

  override def updateQuotation(idOrName: String): ServiceCall[QuotationCreation, Quotation] = {
    var quotation: Option[UUID] = None
    authorizedString { token =>
      quotation = UUIDUtils.maybeInstant(
        idOrName,
        () =>
          quots.getQuotationByName(token.team, idOrName)
      )
      s"quotation:${token.team}:${token.team}:${quotation.get}:quotation:create"
    } { (token, _) =>
      ServerServiceCall { _q =>
        val tcmd   = TimelineCommand.of(token)
        val entity = eReg.refFor[QuotationEntity](quotation.get.toString)
        entity.ask(GetQuotation(token.team, quotation.get)).flatMap {
          quot => // each item has to have the correct version

            val q = _q.copy(assembly = quot.assembly)

            // Set Quotation can not change items, no need to check
            (q.name match {
              case Some(name) =>
                quots.getQuotationsByName(token.team, name, None, None).flatMap {
                  case x if x.results.isEmpty || (x.results.size == 1 && x.results.head == quotation.get) =>
                    entity.ask(SetQuotation(
                      token.team,
                      quotation.get,
                      q,
                      tcmd
                    ))
                  case _ =>
                    throw new TransportException(TransportErrorCode.PolicyViolation, "Name already given")
                }

              case None =>
                entity.ask(SetQuotation(
                  token.team,
                  quotation.get,
                  q,
                  tcmd
                ))
            }).flatMap { q =>
              resolveItems(Some(true), q)
            }
        }
      }
    }
  }

  def initializeNum(team: String): Future[Done] = {
    val newnumber = eReg.refFor[NumberEntity](s"${team}-quotationname")
    val oldnumber = eReg.refFor[NumberEntity](s"quotationname")
    val apinumber = eReg.refFor[NumberEntity](s"${team}-api-quotationname")

    (for {
      newn <- newnumber.ask(PeekNext).map(_.response.head)
      oldn <- oldnumber.ask(PeekNext).map(_.response.head)
      apin <- apinumber.ask(PeekNext).map(_.response.head)
    } yield {

      val f1 =
        if (newn < oldn) {
          newnumber.ask(InitializeNumber(oldn))
        } else {
          Future.successful(Done)
        }

      val minApiNum = oldn.max(newn)
      val f2 =
        if (apin < minApiNum) {
          apinumber.ask(InitializeNumber(minApiNum))
        } else {
          Future.successful(Done)
        }

      Future.sequence(Seq(f1, f2)).map(_ => Done)
    }).flatten
  }

  override def _createQuotation(team: String): ServiceCall[QuotationCreation, Quotation] =
    ServerServiceCall { q =>
      _doCreateQuotation(team, q, TimelineUser.systemUserId, TimelineCommand.system)
    }

  override def createQuotation(): ServiceCall[QuotationCreation, Quotation] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:*:quotation:create"
    ) { (token, _) =>
      ServerServiceCall { q =>
        _doCreateQuotation(token.getTeam, q, token.userId, TimelineCommand.of(token))
      }
    }

  private def _doCreateQuotation(team: String, q: QuotationCreation, createdBy: UUID, tcmd: TimelineCommand) = {
    val newID = UUID.randomUUID()

    val (isShared, prefix) = q.assembly match {
      case x: AssemblyReference       => (false, "Q")
      case x: SharedAssemblyReference => (true, "R")
    }

    initializeNum(team).flatMap { _ =>
      val name = q.name match {
        case Some(n) => Future.successful(n)
        case None =>
          val entityName = if (isShared) s"${team}-api-quotationname" else s"${team}-quotationname"
          eReg.refFor[NumberEntity](entityName).ask(AllocateNumbers(1)).map(_.response).map {
            n =>
              f"$prefix-${n.last}%05d"
          }
      }

      val referencedVersion = q.assembly.getReferenceIdentifier

      validateItems(referencedVersion, q.items.getOrElse(Seq()))
        .flatMap {
          case true =>
            name.flatMap { name =>
              quots.getQuotationsByName(team, name, None, None).flatMap {
                case x if x.results.isEmpty =>
                  val value = eReg.refFor[QuotationEntity](newID.toString)
                  value.ask(CreateQuotation(
                    team,
                    newID,
                    q.copy(name = Some(name)),
                    created = Instant.now(),
                    createdBy = createdBy,
                    tcmd = tcmd
                    //                newID, name, token.team, customer, q.contact, q.billing, q.shipping, q.validUntil
                  ))
                case _ =>
                  throw new TransportException(TransportErrorCode.PolicyViolation, "Name already given")
              }
            }.flatMap(q => resolveItems(Some(true), q))

          case false => throw new TransportException(TransportErrorCode.NotAcceptable, "invalid items")
        }
    }
  }

  override def getQuotationsForTeam(
      items: Option[Boolean],
      page: Option[Int],
      pagesize: Option[Int]
  ): ServiceCall[NotUsed, PaginationListResult[Quotation]] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:*:quotation:read"
    ) { (token, _) =>
      ServerServiceCall { q =>
        val quotationsByTeam = quots.getQuotationsByTeam(token.team, page, pagesize)

        for {
          result <- quotationsByTeam.flatMap(l => retrieveQuotations(token.team, l.results))
            .map(_.sortBy(q => q.created.toEpochMilli).reverse)
            .map(_.filter(q =>
              q.status.name != QuotationStatus.ARCHIVED_NAME && q.status.name != QuotationStatus.DELETED_NAME
            ))
          resultCount <- quotationsByTeam.map(x => x.resultCount)
        } yield PaginationListResult(result, resultCount)

      }
    }

  private def retrieveQuotations(team: String, l: Seq[UUID]): Future[Seq[GetQuotation#ReplyType]] =
    Future.sequence(l.map(x => eReg.refFor[QuotationEntity](x.toString).ask(GetQuotation(team, x))))

  override def getQuotationsForCustomer(
      customer: UUID,
      items: Option[Boolean],
      page: Option[Int],
      pagesize: Option[Int]
  ): ServiceCall[NotUsed, PaginationListResult[Quotation]] =
    authorizedString(token =>
      s"quotation:${token.team}:${customer}:*:quotation:read"
    ) { (token, _) =>
      ServerServiceCall { q =>
        val quotationsByCustomer =
          quots.getQuotationsByCustomer(token.team, customer, page: Option[Int], pagesize: Option[Int])

        for {
          result <- quotationsByCustomer.flatMap(l => retrieveQuotations(token.team, l.results))
            .flatMap(qs => Future.sequence(qs.map(q => resolveItems(items, q))))

          resultCount <- quotationsByCustomer.map(x => x.resultCount)
        } yield PaginationListResult(result, resultCount)
      }
    }

  override def findQuotations(
      quotation: Option[String],
      version: Option[UUID],
      assembly: Option[UUID],
      specification: Option[UUID],
      customer: Option[UUID],
      contact: Option[UUID],
      status: Option[String],
      creator: Option[UUID],
      page: Option[Int],
      pagesize: Option[Int],
      origin: Option[String]
  ): ServiceCall[NotUsed, PaginationListResult[QuotationListItem]] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:*:quotation:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        val convertedOrigin = origin.map(QuotationOrigin(_)).getOrElse(ManualQuotation)
        if (
          customer.isEmpty &&
          contact.isEmpty &&
          version.isEmpty &&
          assembly.isEmpty &&
          status.isEmpty &&
          creator.isEmpty
        ) {

          quots.getSortedQuotationsByTeam(
            team = token.team,
            page = page,
            pagesize = pagesize,
            ereg = eReg,
            quotContents = quotContents,
            nameFilter = quotation,
            origin = convertedOrigin
          )
        } else {

          _findQuotationIDs(
            token.team,
            quotation,
            version,
            assembly,
            specification,
            customer,
            contact,
            status,
            creator,
            page,
            pagesize
          ).flatMap(x =>
            quotContents.getQuotationList(convertedOrigin, token.team, x.results)
              .map(mapped => PaginationListResult(mapped, x.resultCount))
          )
        }

      }
    }

  private def _findQuotationIDs(
      team: String,
      quotation: Option[String],
      version: Option[UUID],
      assembly: Option[UUID],
      specification: Option[UUID],
      customer: Option[UUID],
      contact: Option[UUID],
      status: Option[String],
      creator: Option[UUID],
      page: Option[Int],
      pagesize: Option[Int]
  ): Future[PaginationListResult[UUID]] = {

    val ps = pagesize.getOrElse(100)
    val p  = page.getOrElse(1) - 1

    for {
      byName     <- FutureUtils.option(quotation.map(q => quots.getQuotationsByName(team, q, None, None)))
      byCustomer <- FutureUtils.option(customer.map(v => quots.getQuotationsByCustomer(team, v, None, None)))
      byContact  <- FutureUtils.option(contact.map(v => quots.getQuotationsByContact(team, v, None, None)))
      byVersion  <- FutureUtils.option(version.map(v => quots.getQuotationsByVersion(team, v, None, None)))
      byAssembly <- FutureUtils.option(assembly.map(v => quots.getQuotationsByAssembly(team, v, None, None)))
      byStatus   <- FutureUtils.option(status.map(v => quots.getQuotationsByStatus(team, v, None, None)))
      byCreator  <- FutureUtils.option(creator.map(v => quots.getQuotationsByCreator(team, v, None, None)))
    } yield {
      val prelim = Seq(
        byName,
        byCustomer,
        byContact,
        byVersion,
        byAssembly,
        byStatus,
        byCreator
      )
      // source.map(_x => conv(_x)).filter(_x => filter(_x)).drop(p * ps).take(ps).runWith(Sink.seq)
      val result = (prelim.reduce((_a, _b) =>
        (_a, _b) match {
          case (Some(a), Some(b)) =>
            val intersectedResults = a.results.intersect(b.results)
            Some(PaginationListResult(intersectedResults, intersectedResults.length))
          case (Some(a), None) => Some(a)
          case (None, Some(b)) => Some(b)
          case _               => None
        }
      ))
        .getOrElse(PaginationListResult(Seq(), 0)).results.distinct

      PaginationListResult(result.drop(p * ps).take(ps), result.length)
    }
  }

  override def getQuotationsForVersion(version: UUID): ServiceCall[NotUsed, Seq[Quotation]] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:*:quotation:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        quots.getAllQuotationsByVersion(token.getTeam, version)
          .flatMap { ids =>
            Future.sequence(ids.map(id => _doGetQuotation(id, token.getTeam, Some(true))))
          }
      }
    }

  override def getQuotation(idOrName: String, items: Option[Boolean]): ServiceCall[NotUsed, Quotation] = {
    var quotation: Option[UUID] = None
    auth { token =>
      quotation = UUIDUtils.maybeInstant(
        idOrName,
        () =>
          quots.getQuotationByName(token.getTeam, idOrName)
      )

      token match {
        case _: Auth0Token => "view:pcb"
        case t             => s"quotation:${t.getTeam}:${t.getTeam}:${quotation.get}:quotation:read"
      }
    } { (token, _) =>
      ServerServiceCall { _ =>
        _doGetQuotation(quotation.get, token.getTeam, items)
      }
    }
  }

  private def _doGetQuotation(quotation: UUID, team: String, items: Option[Boolean]) =
    eReg.refFor[QuotationEntity](quotation.toString).ask(GetQuotation(team, quotation))
      .flatMap { q =>
        resolveItems(items, q)
      }

  private def resolveItems(items: Option[Boolean], q: Quotation): Future[Quotation] =
    items match {
      case Some(true) =>
        FutureUtils.option(q.items.map(l =>
          Future.sequence(l.map { item =>
            eReg.refFor[QuotationItemEntity](item.id.toString).ask(GetQuotationItem(item.id)).map { resolved =>
              item.copy(resolved = Some(resolved))
            }.recover(_ => item)
          })
        ))
          .map(items => q.copy(items = items))

      case _ => Future.successful(q)
    }

  override def getQuotationPreview(idOrName: String): ServiceCall[NotUsed, Array[Byte]] = ???

  //    authorizedString(token =>
  //      s"quotation:${token.team}:${customer}:$quotation:quotation:read"
  //    ){ (token, _) =>
  //      ServerServiceCall{ (h, _) =>
  //        _getQuotation(quotation, true).flatMap(quot => {
  //          val responseHeader =
  //            ResponseHeader.Ok
  //              // Setting the Content-Disposition header prompts browsers to
  //              // download the file rather than display it inline.
  //              .addHeader(HeaderNames.CONTENT_DISPOSITION, "attachment; filename=\"preview.pdf\"")
  //
  //          val team = token.team
  //
  //          createPDF(quot, team, true).map(a => (responseHeader, a))
  //        })
  //      }
  //    }

  //  private def createPDF(quot: Quotation, team: String, draft: Boolean) = {
  //    (for {
  //      contact <- Future.successful(None)
  //      customer <- custService._getCustomer(team, quot.customerId, Some(false)).invoke()
  //
  //    } yield {
  //      Future.sequence(
  //
  //      }).toSeq)
  //        .flatMap(eqi => {
  //          val jsonQuot = Json.toJson(quot)
  //          val items = Json.toJson(eqi)
  //
  //          val itemsBySpecification = eqi.groupBy(i => i.item.info.flatMap(_.specification))
  //
  //          val x = eqi
  //            .groupBy(i => i.assembly)
  //
  //          x.map((x: (Assembly, Seq[EnrichedQuotationItem])) => x)
  //
  //          val struct = eqi
  //            .groupBy(i => i.assembly)
  //            .map((assemblyGroup: (Assembly, Seq[EnrichedQuotationItem])) => {
  //              val versionTemplates = assemblyGroup._2.groupBy(_.version).map((versionGroup: (Version, Seq[EnrichedQuotationItem])) => {
  //                val specTemplates = versionGroup._2.groupBy(_.spec).map((specGroup: (Option[PCBSpecification], Seq[EnrichedQuotationItem])) => {
  //                  SpecificationTemplate(specification = specGroup._1, items = specGroup._2, image = specGroup._1.flatMap(s => s.preview.map(sp => {
  //                    val img = sp.toBase64String()
  //                    img
  //                  })))
  //                }).toSeq
  //
  //                VersionTemplate(version = versionGroup._1, pcb = versionGroup._2.head.pcb, specifications = specTemplates)
  //              }).toSeq
  //
  //              AssemblyTemplate(assembly = assemblyGroup._1, versions = versionTemplates)
  //            })
  //
  //
  //          new PDFGenerator()
  //            .withLocalTemplate("templates/default/test/quotation.html")
  //            .withVariable("draft", draft)
  //            .withJsonVariable("quotation", jsonQuot)
  //            .withJsonVariable("items", items)
  //            .withJsonVariable("quotationItemsStruct", Json.toJson(struct))
  //            //          .withJsonVariable("itemsBySpecification", Json.toJson(itemsBySpecification))
  //            .withJsonVariable("contact", Json.toJson(contact))
  //            .withJsonVariable("customer", Json.toJson(customer))
  //            .build()
  //        })
  //    }).flatten
  //  }

  override def createQuotationItem(): ServiceCall[QuotationItemCreation, QuotationItem] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:*:item:write"
    ) { (token, _) =>
      ServerServiceCall { ref =>
        val newID           = UUID.randomUUID()
        val quotationEntity = eReg.refFor[QuotationItemEntity](newID.toString)
        _createQuotationItem(token.team, ref, newID, quotationEntity, TimelineCommand.of(token))
      }
    }

  override def _createQuotationItem(team: String): ServiceCall[QuotationItemCreation, QuotationItem] =
    ServerServiceCall { ref =>
      val newID           = UUID.randomUUID()
      val quotationEntity = eReg.refFor[QuotationItemEntity](newID.toString)
      _createQuotationItem(team, ref, newID, quotationEntity, TimelineCommand.system)
    }

  private def _createQuotationItem(
      team: String,
      c: QuotationItemCreation,
      id: UUID,
      quotationEntity: PersistentEntityRef[QuotationItemCommands.QuotationItemCommand],
      tcmd: TimelineCommand
  ): Future[SetQuotationItem#ReplyType] = {

    // checks

    val _ass  = c.assembly
    val _inf  = c.info.getOrElse("Quotation Item info is necessary" ! TransportErrorCode.NotAcceptable)
    val _spec = _inf.specification.getOrElse("Specification is necessary" ! TransportErrorCode.NotAcceptable)
//    val _panel = _inf.panel.getOrElse("Panel is necessary" ! TransportErrorCode.NotAcceptable)
    //    val _tech = _inf.technology.getOrElse("Technology is necessary" ! TransportErrorCode.NotAcceptable)
    val reference = _ass.getReference()

    (for {
      specs <- pcbService._getSpecification(reference.team, reference.id, reference.version, _spec).invoke()
      panel <-
        FutureUtils.option(
          _inf.panel.map(panel =>
            panelService._getCustomerPanel(reference.team, reference.id, reference.version, panel.toString).invoke()
          )
        )
    } yield {
      specs.headOption.getOrElse("Specification not found" ! TransportErrorCode.NotFound)

      quotationEntity.ask(SetQuotationItem(id, c.assembly, c.name, c.description, c.info, tcmd))
    }).flatten
  }

  override def findQuotationItems(
      version: Option[UUID],
      technology: Option[String]
  ): ServiceCall[NotUsed, Seq[QuotationItem]] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:*:item:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        _doFindQuotationItems(token.team, version, technology)
      }
    }

  private def _doFindQuotationItems(
      team: String,
      version: Option[UUID],
      technology: Option[String]
  ): Future[Seq[QuotationItem]] =
    (for {
      byversion <- FutureUtils.option(version.map(v => quotItems.getItemsByVersion(team, v)))
      bytech    <- FutureUtils.option(technology.map(v => quotItems.getItemsByTechnology(team, v)))
    } yield byversion.orElse(bytech).getOrElse(Seq())
      .intersect(
        bytech.orElse(byversion).getOrElse(Seq())
      )
      //          byversion.getOrElse(Seq())
      .map { id =>
        eReg.refFor[QuotationItemEntity](id.toString).ask(GetQuotationItem(id))
      }).map(s => Future.sequence(s)).flatten

  override def getQuotationItem(item: UUID): ServiceCall[NotUsed, QuotationItem] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:$item:item:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        eReg.refFor[QuotationItemEntity](item.toString).ask(GetQuotationItem(item))
      }
    }

  override def deleteQuotationItem(item: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:$item:item:write"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        // TODO: remove from quotations
        val entity = eReg.refFor[QuotationItemEntity](item.toString)
        entity.ask(DeleteQuotationItem(item, TimelineCommand.of(token)))
      }
    }

  override def setQuotationItem(item: UUID): ServiceCall[QuotationItemInfo, QuotationItem] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:$item:item:write"
    ) { (token, _) =>
      ServerServiceCall { qi =>
        val entity = eReg.refFor[QuotationItemEntity](item.toString)
        entity.ask(SetQuotationItemInfo(item, qi, TimelineCommand.of(token)))
      }
    }

  override def setQuotationItemWon(quotation: UUID, item: UUID): ServiceCall[Boolean, QuotationItem] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:$item:item:write"
    ) { (token, _) =>
      ServerServiceCall { won =>
        val entity = eReg.refFor[QuotationItemEntity](item.toString)
        // First set the item to won
        entity.ask(SetQuotationItemWon(item, won, TimelineCommand.of(token))).flatMap { updatedItem =>
          if (won) {
            val quotEntity = eReg.refFor[QuotationEntity](quotation.toString)
            quotEntity.ask(SetQuotationStatus(
              token.team,
              quotation,
              QuotationStatus.WON(Instant.now()),
              TimelineCommand.of(token)
            )).map(_ => updatedItem)
          } else {
            Future.successful(updatedItem)
          }
        }
      }
    }

  def toList(event: PriceListSet): MappedPriceList =
    MappedPriceList(event.infos.lists.groupBy(_.supplier).map(x => (x._1 -> x._2.flatMap(p => p.list.breaks))))

  def setAddress(customer: UUID, quotation: UUID, billing: Boolean): ServiceCall[Address, Done] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:$quotation:address:write"
    ) { (token, _) =>
      ServerServiceCall { address =>
        val tcmd = TimelineCommand.of(token)
        eReg.refFor[QuotationEntity](quotation.toString).ask(SetAddress(token.team, quotation, billing, address, tcmd))
      }
    }

  override def setBillingAddress(customer: UUID, quotation: UUID): ServiceCall[Address, Done] =
    setAddress(customer, quotation, true)

  override def setShippingAddress(customer: UUID, quotation: UUID): ServiceCall[Address, Done] =
    setAddress(customer, quotation, false)

  override def addQuotationItemToQuotation(
      customer: UUID,
      quotation: UUID
  ): ServiceCall[QuotationItemReference, Quotation] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:$quotation:item:write"
    ) { (token, _) =>
      ServerServiceCall { qi =>
        val tcmd = TimelineCommand.of(token)
        eReg.refFor[QuotationItemEntity](qi.id.toString).ask(GetQuotationItem(qi.id))
          .flatMap { i =>
            val entity = eReg.refFor[QuotationEntity](quotation.toString)
            entity.ask(AddQuotationItem(token.team, quotation, qi.id, tcmd))
              .flatMap { q =>
                resolveItems(Some(true), q)
              }
          }
      }
    }

  override def removeQuotationItemFromQuotation(
      customer: UUID,
      quotation: UUID,
      item: UUID
  ): ServiceCall[NotUsed, Quotation] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:$quotation:item:write"
    ) { (token, _) =>
      ServerServiceCall { qi =>
        val tcmd   = TimelineCommand.of(token)
        val entity = eReg.refFor[QuotationEntity](quotation.toString)
        entity.ask(RemoveQuotationItem(token.team, quotation, item, tcmd))
          .flatMap { q =>
            resolveItems(Some(true), q)
          }
      }
    }

  val definitionTemplateCategory = "quotation"

  def enrich(
      panel: Option[api.CustomerPanel],
      workingPanels: Seq[api.WorkingPanel]
  ): Option[pdf.EnrichedCustomerPanel] =
    panel.map { cp =>
      val usages = cp.working.map(_.map(wpu => enrich(wpu, workingPanels)))

      EnrichedCustomerPanel(
        id = cp.id,
        name = cp.name,
        elements = cp.elements,
        description = cp.description,
        preview = cp.preview,
        spacing = cp.spacing,
        working = usages,
        selected = cp.selected.flatMap(wpu => enrichWP(wpu, usages.getOrElse(Seq()))),
        bestYield = cp.bestYield.flatMap(wpu => enrichWP(wpu, usages.getOrElse(Seq()))),
        width = cp.width,
        height = cp.height,
        boardsOnCustomerPanel = Some(cp.count())
      )
    }

  def enrichWP(panel: UUID, workingPanels: Seq[EnrichedWorkingPanelUsage]): Option[EnrichedWorkingPanelUsage] =
    workingPanels.find(_.workingPanel.id.contains(panel))

  def enrich(panel: api.WorkingPanelUsage, workingPanels: Seq[api.WorkingPanel]): EnrichedWorkingPanelUsage =
    EnrichedWorkingPanelUsage(
      workingPanels.find(_.id.contains(panel.workingPanel)).get,
      customerPanels = panel.customerPanels,
      customerBoards = panel.customerBoards,
      panelYield = panel.panelYield,
      preview = panel.preview
    )

  def getEnrichedItems(
      team: String,
      oitems: Option[Seq[QuotationItemReference]],
      assemblyTeam: String,
      assemblyId: UUID,
      assemblyVersion: UUID
  ): Future[Option[Seq[EnrichedQuotationItem]]] =
    FutureUtils.option(oitems.map(items =>
      Future.sequence(items.zipWithIndex.map { _i =>
        val itemId = _i._1.id

        for {
          item <- eReg.refFor[QuotationItemEntity](itemId.toString).ask(GetQuotationItem(itemId))
          spec <- FutureUtils.option(
            item.info
              .flatMap(_.specification)
              .map(id =>
                pcbService._getSpecification(assemblyTeam, assemblyId, assemblyVersion, id).invoke().map(_.headOption)
              )
          ).map(_.flatten)

          supplier <-
            FutureUtils.option(
              item.info
                .flatMap(_.supplier).map(i => supplierService._getSupplier(team, i.toString, None).invoke())
            )

          panel <-
            FutureUtils.option(
              item.info.flatMap(_.panel)
                .map(i =>
                  panelService._getCustomerPanel(assemblyTeam, assemblyId, assemblyVersion, i.toString).invoke()
                )
            )

          workingPanels <- panelService._getWorkingPanels(team).invoke()

          tech <- FutureUtils.option(item.info.flatMap(_.technology)
            .map(i => supplierService._getTechnology(team, i).invoke()))
        } yield {
          val infoWithFilledUserFields = item.info.map(_.getWithFilledUserFields())

          EnrichedQuotationItem(
            index = _i._2,
            name = item.name,
            description = item.description,
            spec = spec,
            preview = None, // TODO
            supplier = supplier,
            panel = enrich(panel, workingPanels),
            technology = tech,
            delivery = infoWithFilledUserFields.flatMap(_.delivery),
            quantity = infoWithFilledUserFields.flatMap(_.quantity),
            nre = infoWithFilledUserFields.flatMap(_.nre),
            unitPrice = infoWithFilledUserFields.flatMap(_.unitPrice),
            userDelivery = infoWithFilledUserFields.flatMap(_.userDelivery),
            userQuantity = infoWithFilledUserFields.flatMap(_.userQuantity),
            userNRE = infoWithFilledUserFields.flatMap(_.userNRE),
            userUnitPrice = infoWithFilledUserFields.flatMap(_.userUnitPrice),
            userTotal = infoWithFilledUserFields.flatMap(_.userTotal),
            total = infoWithFilledUserFields.flatMap(_.total),
            details = infoWithFilledUserFields.flatMap(_.details),
            currency = infoWithFilledUserFields.flatMap(_.currency.map(_.code))
          )
        }
      })
    ))

  def collectFiles(q: EnrichedQuotation): Seq[FilePath] = {
    val b = Seq.newBuilder[FilePath]

    q.basic.defaultSpecification.flatMap(_.preview).foreach(b.addOne)
    q.basic.createdBy.profile.foreach(_.avatar.foreach(b.addOne))
    q.basic.assignee.foreach(a => a.profile.foreach(_.avatar.foreach(b.addOne)))
    q.basic.items.foreach(_.foreach(_.spec.foreach(_.preview.foreach(b.addOne))))
    q.basic.items.foreach(_.foreach(_.panel.foreach(_.preview.foreach(b.addOne))))
    //    q.basic.items.foreach(_.foreach(_.panel.foreach(_.selected.foreach(_.))))

    b.result()
  }

  override def printQuotation(idOrName: String, template: Option[String]): ServiceCall[NotUsed, Array[Byte]] = {
    var quotationID: Option[UUID] = None
    authorizedString { token =>
      quotationID = UUIDUtils.maybeInstant(
        idOrName,
        () =>
          quots.getQuotationByName(token.team, idOrName)
      )
      s"quotation:${token.team}:${token.team}:${quotationID.get}:*:read"
    } { (token, _) =>
      ServerServiceCall { (_, _) =>
        val res: Future[Array[Byte]] = getQuotationPDFBytes(quotationID, token.team, template)

        import de.fellows.utils.ServiceCallUtils._
        res.map(inlinePDF)
      }
    }
  }

  override def printHtmlQuotation(
      idOrName: String,
      template: Option[String],
      k: String
  ): ServiceCall[NotUsed, String] = {
    val b                         = AuthenticationServiceComposition.decodeTokenWithResponse(k)
    var quotationID: Option[UUID] = None
    authorizedStringWithToken(b) { token =>
      quotationID = UUIDUtils.maybeInstant(
        idOrName,
        () =>
          quots.getQuotationByName(token.team, idOrName)
      )
      s"quotation:${token.team}:${token.team}:${quotationID.get}:*:read"
    } {
      (token, _) =>
        ServerServiceCall { (_, _) =>
          val res = getQuotationHtml(quotationID, token.team, template)
          res.map(x =>
            (
              ResponseHeader.Ok
                .addHeader(HeaderNames.CONTENT_TYPE, "text/html"),
              x
            )
          )
        }
    }
  }

  //  def createEnrichedItems(team: String, items: Seq[QuotationItem]): Future[Seq[EnrichedQuotationItem]] = {
  //    Future.sequence(items.zipWithIndex.map(qiwi => {
  //      //retrieve info for each item
  //      //todo: collect assemblues, versions, pbcc, etc and retrieve them once
  //      val qi = qiwi._1
  //      for {
  //        vers <- assService._getVersion(team, qi.assembly.id, qi.assembly.version).invoke()
  //        ass <- assService._getAssembly(team, qi.assembly.id).invoke()
  //        pcb <- pcbService._getPCB(team, qi.assembly.id, qi.assembly.version).invoke()
  //
  //        spec <- qi.info match {
  //          case Some(i) => i.specification match {
  //            case Some(id) => pcbService._getSpecification(team, qi.assembly.id, qi.assembly.version, qi.info.get.specification.get).invoke().map{
  //              case i if i.nonEmpty => Some(i.head)
  //              case _ => None
  //            }
  //            case None => Future.successful(None)
  //          }
  //          case None => Future.successful(None)
  //
  //        }
  //
  //      } yield {
  //        EnrichedQuotationItem(qiwi._2 + 1, qi, ass, vers, pcb, spec, spec.flatMap(_.preview.map(_.toPath)))
  //      }
  //    }))
  //  }

  private def renderQuotation[T](
      quotationID: Option[UUID],
      team: String,
      template: Option[String]
  )(req: RenderRequest => Future[T]): Future[T] = {
    val res = this._doGetQuotation(quotationID.get, team, Some(true)).flatMap { quotation =>
      getEnrichedQuotation(team, quotation).flatMap { q =>
        val variable = Map(
          "quotation" -> Json.toJson(q)
        )

        val filepaths = collectFiles(q)
          .map(x => x.toPath)

        val body = RenderRequest(
          resources = filepaths,
          variables = variable
        )

        req(body)
      }

    }
    res
  }

  private def getQuotationPDFBytes(
      quotationID: Option[UUID],
      team: String,
      template: Option[String]
  ): Future[Array[Byte]] =
    renderQuotation(quotationID, team, template) {
      body => userService._renderTemplate(team, definitionTemplateCategory, template).invoke(body)
    }

  private def getQuotationHtml(quotationID: Option[UUID], team: String, template: Option[String]): Future[String] =
    renderQuotation(quotationID, team, template) {
      body => userService._renderHtmlTemplate(team, definitionTemplateCategory, template).invoke(body)
    }

  private def getEnrichedQuotation(team: String, quotation: Quotation): Future[EnrichedQuotation] = {
    val underlyingTeam     = quotation.assembly.getReference().team
    val underlyingAssembly = quotation.assembly.getReference().id
    val underlyingVersion  = quotation.assembly.getReference().version

    val layerstackId =
      quotation.assembly match {
        case AssemblyReference(team, id, gid, version)         => version
        case SharedAssemblyReference(team, id, sharedAssembly) => id
      }

    (for {
      assembly <- assService._getAssembly(underlyingTeam, underlyingAssembly).invoke().map(_.assembly)
      pcb      <- pcbService._getPCBVersion(underlyingTeam, underlyingAssembly, underlyingVersion).invoke()
      layerstack <- layerstackService._getPCBLayerstack(
        team,
        layerstackId,
        Some(true)
      ).invoke()
        .map(Some(_))
        .recover {
          case e => None
        }
      customer       <- custService._getCustomer(team, quotation.customerId, None).invoke()
      creator        <- userService._getUserInfo(quotation.creator).invoke()
      teamprofile    <- profileService._getTeamProfile(quotation.team).invoke().map(Some(_)).recover { case e => None }
      creatorProfile <- profileService._getProfile(quotation.creator).invoke().map(Some(_)).recover { case e => None }
      assignee <- FutureUtils.option(quotation.assignee.map(a =>
        userService._getUserInfo(a).invoke().map(Some(_)).recover { case e => None }
      )).map(_.flatten)
      assigneeProfile <- FutureUtils.option(quotation.assignee.map(a =>
        profileService._getProfile(a).invoke().map(Some(_)).recover { case e => None }
      )).map(_.flatten)
      contact <-
        FutureUtils.option(quotation.contactId.map(a => custService._getContact(team, customer.id.get, a).invoke()))
      defaultSpec <- FutureUtils.option(pcb.defaultSpecification.map(id =>
        pcbService._getSpecification(underlyingTeam, underlyingAssembly, underlyingVersion, id).invoke().map(
          _.headOption
        )
      ))
      enrichedItems <- getEnrichedItems(team, quotation.items, underlyingTeam, underlyingAssembly, underlyingVersion)
    } yield {

      val specs = enrichedItems.map(_.flatMap(_.spec)).getOrElse(Seq()).groupBy(_.id)
        .map(x => x._1 -> x._2.head)

      val enrichedItemGroups = enrichedItems.map(_.groupBy(_.spec.map(_.id).getOrElse(UUIDUtils.nil)))
        .map(_.map { x =>
          EnrichedSpecificationGroup(
            specs.get(x._1),
            x._2.map(_.copy(spec = None)).sortBy(item =>
              (
                item.userQuantity.orElse(item.quantity).getOrElse(BigDecimal.decimal(0)),
                item.userDelivery.orElse(item.delivery).getOrElse(BigDecimal.decimal(0))
              )
            )
          )
        }.toSeq)

      val basicQuotationData = EnrichedQuotationBasic(
        EnrichedTeam(
          team,
          teamprofile.map(teamprofile =>
            EnrichedProfile(teamprofile.addresses, teamprofile.avatar, teamprofile.data.combinedMeta())
          )
        ),
        quotation.name,
        assembly,
        pcb,
        layerstack = layerstack,
        defaultSpecification = defaultSpec.flatten,
        customer = customer,
        externalID = quotation.externalID,
        description = quotation.description,
        created = quotation.created,
        createdBy = EnrichedUser(
          creator,
          creatorProfile.map(creatorProfile =>
            EnrichedProfile(creatorProfile.addresses, creatorProfile.avatar, creatorProfile.data.combinedMeta())
          )
        ),
        assignee = assignee.map(assignee =>
          EnrichedUser(
            assignee,
            assigneeProfile.map(assigneeProfile =>
              EnrichedProfile(
                assigneeProfile.addresses,
                assigneeProfile.avatar,
                assigneeProfile.data.combinedMeta()
              )
            )
          )
        ),
        status = quotation.status,
        contact = contact,
        billing = quotation.billing,
        shipping = quotation.shipping,
        items = enrichedItems,
        itemsBySpecification = enrichedItemGroups,
        publicNotes = quotation.publicNotes,
        validFor = quotation.validFor
      )
      val requestQuotationData = EnrichedQuotationRequestData(
        requestID = quotation.requestID,
        requestBy = quotation.requestBy,
        requestDate = quotation.requestDate
      )

      EnrichedQuotation(basic = basicQuotationData, requestData = requestQuotationData)
    })
  }

  override def printQuotationItems(
      version: Option[UUID],
      technology: Option[String],
      template: Option[String]
  ): ServiceCall[NotUsed, Array[Byte]] =
    authorizedString(token =>
      s"quotation:${token.team}:${token.team}:*:item:read"
    ) { (token, _) =>
      ServerServiceCall { (_, _) =>
        val res = _doFindQuotationItems(token.team, version, technology).flatMap { items =>
          ???
        //          createEnrichedItems(token.team, items).flatMap(eitems => {
        //            user._renderTemplate(token.team, templateCategory, template).invoke(RenderRequest(
        //              resources = eitems.flatMap(_.spec.flatMap(_.preview.map(_.toPath))),
        //              variables = Map(
        //                "items" -> Json.toJson(eitems)
        //
        //              )
        //            ))

        }
        import de.fellows.utils.ServiceCallUtils._
        res.map(inlinePDF)
      }
    }

  override def streamQuotations(k: String): ServiceCall[NotUsed, Source[Topics.QuotationMessage, NotUsed]] = {
    val b = AuthenticationServiceComposition.decodeTokenWithResponse(k)
    ServerServiceCall { t =>
      //    val ticks = StreamMessage.pingTicks

      val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
      val sources: Set[Source[Topics.QuotationMessage, NotUsed]] = QuotationEvent.Tag.allTags.map(tag =>
        eReg.eventStream(tag, nowOffset)
          .collect {
            case EventStreamElement(_, event: QuotationCreated, _)
                if assemblyAllowed(event.quotation.team, event.quotation.quotationId, b) =>
              val x: QuotationMessage = QuotationCreatedMsg(event.quotation)
              x
            case EventStreamElement(_, event: QuotationStatusSet, _)
                if assemblyAllowed(event.team, event.quotationId, b) =>
              val x: QuotationMessage = QuotationStatusChangedMsg(
                event.team,
                event.quotationId,
                event.customerId,
                event.name,
                event.oldStatus,
                event.status
              )
              x
          }
      )

      Future.successful(sources.reduce((a, b) => a.merge(b)))
      //    Future.successful(src.merge(ticks))
    }
  }

  def assemblyAllowed(team: String, quotation: UUID, token: TokenContent): Boolean = {
    val req  = s"quotation:${team}:${team}:${quotation}:quotation:read"
    val resp = AccessControl.check(req, token)
    resp.allowed && team == token.team
  }

  override def quotationTopic(): Topic[Topics.QuotationMessage] =
    TopicProducer.taggedStreamWithOffset(QuotationEvent.Tag) { (tag, offset) =>
      eReg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: QuotationStatusSet, offset) =>
          Seq(
            (
              QuotationStatusChangedMsg(
                team = ev.team,
                quotationId = ev.quotationId,
                customerId = ev.customerId,
                name = ev.name,
                oldStatus = ev.oldStatus,
                status = ev.status
              ),
              x.offset
            )
          )
        case x @ EventStreamElement(_, ev: QuotationCreated, offset) =>
          Seq(
            (
              QuotationCreatedMsg(
                ev.quotation
              ),
              x.offset
            )
          )

        case _ => Nil
      }
    }

  override def quotationTimeline(): Topic[TimelineEvent] =
    TopicProducer.taggedStreamWithOffset(QuotationEvent.Tag) { (tag, offset) =>
      eReg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: QuotationTimelineChanged, _) => immutable.Seq((ev.evt, x.offset))
        case _                                                          => Nil
      }
    }

  override def quotationItemTimeline(): Topic[TimelineEvent] =
    TopicProducer.taggedStreamWithOffset(QuotationItemEvent.Tag) { (tag, offset) =>
      eReg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: QuotationItemTimelineChange, _) => immutable.Seq((ev.evt, x.offset))
        case _                                                             => Nil
      }
    }

  override def exportQuotationsToCSV(): ServiceCall[NotUsed, Array[Byte]] =
    authorizedString(token =>
      s"admin:${token.getTeam}:*:*:*:*"
    ) { (token, _) =>
      ServerServiceCall { (_, _) =>
        logger.info(s"Exporting quotations to CSV for team ${token.team}")
        // Get all quotations for the team

        CSVExport.exportQuotationsToCSV(token.team, eReg, quots, quotContents, pcbService, _doGetQuotation).map {
          bytes =>
            (
              ResponseHeader.Ok
                .addHeader(HeaderNames.CONTENT_DISPOSITION, "attachment; filename=\"quotation.zip\"")
                .addHeader(HeaderNames.CONTENT_TYPE, "application/zip"),
              bytes
            )
        }

      }
    }

}
