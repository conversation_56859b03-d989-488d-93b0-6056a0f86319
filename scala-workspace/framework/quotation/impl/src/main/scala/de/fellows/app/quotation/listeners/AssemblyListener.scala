package de.fellows.app.quotation.listeners

import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.{ AssemblyDescription, AssemblyService, VersionDescription }
import de.fellows.app.supplier.SupplierService

import scala.concurrent.ExecutionContext

class AssemblyListener(ass: AssemblyService, reg: PersistentEntityRegistry, sess: CassandraSession)(implicit
    c: ExecutionContext
) {}
