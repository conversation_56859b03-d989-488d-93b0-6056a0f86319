package de.fellows.app.quotation.read

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row, SimpleStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, PersistentEntityRegistry, ReadSideProcessor}
import de.fellows.app.assembly.commons.codec.AssemblyCodec
import de.fellows.app.assembly.commons.{AssemblyReference, SharedAssemblyReference}
import de.fellows.app.quotation.entity.quotation.QuotationEvents._
import de.fellows.app.quotation.{Quotation, QuotationListItem, QuotationOrigin, QuotationStatus}
import de.fellows.ems.pcb.model.codec.PCBCodecHelper
import de.fellows.utils.Pagination._
import de.fellows.utils.PaginationListResult
import de.fellows.utils.communication.ServiceDefinition
import play.api.Logging

import java.time.Instant
import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class QuotationRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends Logging {
  implicit val csession: CassandraSession = session

  def getSortedQuotationsByTeam(
      team: String,
      page: Option[Int],
      pagesize: Option[Int],
      ereg: PersistentEntityRegistry,
      quotContents: QuotationContentReadSide,
      nameFilter: Option[String],
      origin: QuotationOrigin
  ): Future[PaginationListResult[QuotationListItem]] =
    // .map(_.filter(q => q.status.name != QuotationStatus.ARCHIVED_NAME && q.status.name != QuotationStatus.DELETED_NAME))

    (for {
      byOrigin <- session.selectAll(new SimpleStatement(
        "SELECT * FROM quotationsbyorigin WHERE team = ?  AND origin = ? ",
        team,
        origin.entryName
      )).map(_.map(_.getUUID("quotation")).toSet)

      byName <- session.selectAll(new SimpleStatement("SELECT * FROM quotationsbyname WHERE team = ?", team))
    } yield {

      def originMatches(r: QuotationListItem) =
        byOrigin.contains(
          r.quotationId
        )

      def filter(r: QuotationListItem) =
        originMatches(
          r
        ) &&
          r.status.name != QuotationStatus.ARCHIVED_NAME &&
          r.status.name != QuotationStatus.DELETED_NAME &&
          nameFilter.forall(f => r.name.contains(f))

      def convert(r: Row) =
        quotContents.getQuotation(origin, r.getString("team"), r.getUUID("quotation"))

      def sort(s: Seq[QuotationListItem]) =
        s.sortBy(_.created).reverse

      if (page.isDefined) {
        val p  = page.get - 1
        val ps = pagesize.getOrElse(100)

        Future.sequence(byName.map(convert)).map { x =>
          val allResults = sort(x.flatten.filter(filter))
          val result     = allResults.slice(p * ps, p * ps + ps)
          PaginationListResult(
            result,
            allResults.length
          )
        }
      } else {
        Future.sequence(byName.map(convert)).map { x =>
          val result = sort(x.flatten.filter(filter))
          PaginationListResult(
            result,
            result.length
          )
        }
      }

    }).flatten

  def getQuotationsByTeam(team: String, page: Option[Int], pagesize: Option[Int]): Future[PaginationListResult[UUID]] =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbyname WHERE team = ?", team)
    }

  def getQuotationsByName(
      team: String,
      quotationName: String,
      page: Option[Int],
      pagesize: Option[Int]
  ): Future[PaginationListResult[UUID]] =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbyname WHERE team = ? AND name = ?", team, quotationName)
    }

  def getQuotationsByVersion(team: String, version: UUID, page: Option[Int], pagesize: Option[Int]) =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbyversion WHERE team = ? AND version = ?", team, version)
    }

  def getQuotationsByAssembly(team: String, assembly: UUID, page: Option[Int], pagesize: Option[Int]) =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbyassembly WHERE team = ? AND assembly = ?", team, assembly)
    }
  def getAllQuotationsByVersion(team: String, version: UUID): Future[Seq[UUID]] =
    session.selectAll(new SimpleStatement(
      "SELECT * FROM quotationsbyversion WHERE team = ? AND version = ?",
      team,
      version
    ))
      .map(_.map(_.getUUID("quotation")))

  def getQuotationByName(team: String, quotationName: String) =
    pagination[UUID](None, None, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbyname WHERE team = ? AND name = ?", team, quotationName)
    }.map(_.results.headOption)

  def getQuotationsByCustomer(team: String, customer: UUID, page: Option[Int], pagesize: Option[Int]) =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbycustomer WHERE team = ? AND customer = ?", team, customer)
    }

  //
  def getQuotationsByContact(team: String, contact: UUID, page: Option[Int], pagesize: Option[Int]) =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbycontact WHERE team = ? AND contact = ?", team, contact)
    }

  //
  def getQuotationsByStatus(team: String, status: String, page: Option[Int], pagesize: Option[Int]) =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbystatus WHERE team = ? AND statusName = ?", team, status)
    }

  //
  def getQuotationsByCreator(team: String, creator: UUID, page: Option[Int], pagesize: Option[Int]) =
    pagination[UUID](page, pagesize, defaultFilter, r => r.getUUID("quotation")) {
      new SimpleStatement("SELECT * FROM quotationsbycreator WHERE team = ? AND creator = ?", team, creator)
    }

  private def defaultFilter(x: UUID) = true
}

class QuotationReadSide(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[QuotationEvent] with Logging {

  var setQuotationByNameStmt: PreparedStatement     = _
  var deleteQuotationByNameStmt: PreparedStatement  = _
  var setQuotationStatusNameStmt: PreparedStatement = _

  var setQuotationByVersionStmt: PreparedStatement     = _
  var setQuotationByAssemblyStmt: PreparedStatement    = _
  var setQuotationByContactStmt: PreparedStatement     = _
  var deleteQuotationByContactStmt: PreparedStatement  = _
  var setQuotationByCustomerStmt: PreparedStatement    = _
  var deleteQuotationByCustomerStmt: PreparedStatement = _

  def prepareStatements(): Future[Done] =
    for {
      // language=SQL
      setQuotationByCustomer <- session.prepare(
        // INSERT INTO quotationsbycustomer(team,customer,quotation, time) VALUES (:team, :customer, :quotation, :time)
        """
          | UPDATE quotationsbycustomer SET
          |  time = :time
          | WHERE team = :team AND customer = :customer AND quotation = :quotation
          |""".stripMargin
      )
      deleteQuotationByCustomer <- session.prepare(
        "DELETE FROM quotationsbycustomer WHERE team = :team AND customer = :customer AND quotation = :quotation"
      )

      setQuotationByName <- session.prepare(
        """
          | UPDATE quotationsbyname SET
          |  statusName = :statusname, statusTime = :statustime
          | WHERE team = :team AND name = :name AND quotation = :quotation
          |""".stripMargin
      )

      deleteQuotationByName <-
        session.prepare("DELETE FROM quotationsbyname WHERE team = :team AND name = :name AND quotation = :quotation")

      setQuotationByVersion <- session.prepare(
        """
          | UPDATE quotationsbyversion SET
          |  time = :time
          | WHERE team = :team AND version = :version AND quotation = :quotation
          |""".stripMargin
      )

      setQuotationByAssembly <- session.prepare(
        """
          | UPDATE quotationsbyassembly SET
          |  time = :time
          | WHERE team = :team AND assembly = :assembly AND quotation = :quotation
          |""".stripMargin
      )

      setQuotationByContact <- session.prepare(
        """
          | UPDATE quotationsbycontact SET
          |  statusName = :statusname, statusTime = :statustime
          | WHERE team = :team AND contact = :contact AND quotation = :quotation
          |""".stripMargin
      )
      deleteQuotationByContact <- session.prepare(
        "DELETE FROM  quotationsbycontact WHERE team = :team AND contact = :contact AND quotation = :quotation"
      )

      setQuotationStatusName <- session.prepare(
        """
          |UPDATE quotationsbyname SET
          |statusName = :statusname,
          |statusTime = :statustime
          |WHERE team = :team AND name = :name AND quotation = :quotation
          |""".stripMargin
      )

    } yield {
      QuotationReadSide.registerCodecs(session).map(_ => Done)

      setQuotationByVersionStmt = setQuotationByVersion
      setQuotationByAssemblyStmt = setQuotationByAssembly

      setQuotationByNameStmt = setQuotationByName
      deleteQuotationByNameStmt = deleteQuotationByName
      setQuotationStatusNameStmt = setQuotationStatusName

      setQuotationByCustomerStmt = setQuotationByCustomer
      deleteQuotationByCustomerStmt = deleteQuotationByCustomer
      setQuotationByContactStmt = setQuotationByContact
      deleteQuotationByContactStmt = deleteQuotationByContact

      Done
    }

  def updateQuotation(event: QuotationUpdated): Future[immutable.Seq[BoundStatement]] = {
    //    Future.successful(

    val name: Seq[BoundStatement] =
      ((if (event.oldQuotation.name != event.quotation.name) {
          removeQuotationByName(event.oldQuotation)
        } else {
          List()
        }) ++ setQuotationByName(event.quotation) :+ setQuotationByVersion(event.quotation)) ++ setQuotationByAssembly(
        event.quotation
      )

    val customer =
      (if (event.oldQuotation.customerId != event.quotation.customerId) {
         List(
           removeQuotationByCustomer(event.oldQuotation),
           setQuotationByCustomer(event.quotation)
         )
       } else {
         List()
       })

    val contact =
      (if (
         event.oldQuotation.contactId.isDefined && (event.oldQuotation.contactId.get != event.quotation.contactId.get)
       ) {
         List(removeQuotationByContact(event.oldQuotation, event.oldQuotation.contactId.get))
       } else {
         List()
       }) ++
        (if (event.oldQuotation.contactId.isDefined) {
           setQuotationByContact(event.quotation, event.quotation.contactId.get)
         } else {
           List()
         })

    Future.successful(name ++ contact ++ customer)

    //      List() ++ (event.oldQuotation.name match {
    //        case Some(n) =>
    //          event.oldName.filterNot(_ == n).map(oldname => removeQuotationByName(event.quotation.copy(name = oldname))).toSeq :+
    //            setQuotationByName(event.quotation)
    //        case None => List()
    //      }) ++ (event.contactId match {
    //        case Some(n) =>
    //          event.oldContact.filterNot(_ == n).map(old => removeQuotationByContact(event.quotation.copy(contactId = event.oldContact), old)).toList ++
    //            setQuotationByContact(event.quotation, n)
    //        case None => List()
    //      })
    //    )
  }

  def createQuotation(event: QuotationCreated): Future[immutable.Seq[BoundStatement]] =
    Future.successful(
      ((List(
        setQuotationByCustomerStmt.bind()
          .setUUID("customer", event.quotation.customerId)
          .setString("team", event.quotation.team)
          .setUUID("quotation", event.quotation.quotationId)
          .set("time", Instant.now(), classOf[Instant])
      ) ++ setQuotationByName(event.quotation) :+ setQuotationByVersion(event.quotation)) ++ setQuotationByAssembly(
        event.quotation
      )) ++ (event.quotation.contactId match {
        case Some(contact) =>
          setQuotationByContact(event.quotation, contact)

        case None => List()
      })
    )

  private def setQuotationByCustomer(quotation: Quotation) =
    setQuotationByCustomerStmt.bind()
      .setUUID("customer", quotation.customerId)
      .setString("team", quotation.team)
      .setUUID("quotation", quotation.quotationId)
      .set("time", Instant.now(), classOf[Instant])

  private def removeQuotationByCustomer(quotation: Quotation) =
    deleteQuotationByCustomerStmt.bind()
      .setUUID("customer", quotation.customerId)
      .setString("team", quotation.team)
      .setUUID("quotation", quotation.quotationId)

  private def setQuotationByContact(quotation: Quotation, contact: UUID) =
    List(
      setQuotationByContactStmt.bind()
        .setUUID("contact", contact)
        .setString("team", quotation.team)
        .setUUID("quotation", quotation.quotationId)
        .setString("statusname", quotation.status.name)
        .set("statustime", quotation.status.date.get, classOf[Instant])
    )

  private def removeQuotationByContact(quotation: Quotation, contact: UUID) =
    deleteQuotationByContactStmt.bind()
      .setUUID("contact", contact)
      .setString("team", quotation.team)
      .setUUID("quotation", quotation.quotationId)

  private def setQuotationByName(quotation: Quotation) = {
    println(s"set quotation $quotation")
    Seq(
      setQuotationByNameStmt.bind()
        .setString("name", quotation.name)
        .setString("team", quotation.team)
        .setUUID("quotation", quotation.quotationId)
        .setString("statusname", quotation.status.name)
        .set("statustime", quotation.status.date.get, classOf[Instant])
    )
  }

  private def setQuotationByVersion(quotation: Quotation) = {
    println(s"set quotation $quotation")
    setQuotationByVersionStmt.bind()
      .setUUID("version", quotation.assembly.getReferenceIdentifier)
      .setString("team", quotation.team)
      .setUUID("quotation", quotation.quotationId)
      .set("time", quotation.status.date.get, classOf[Instant])
  }

  private def setQuotationByAssembly(quotation: Quotation): Seq[BoundStatement] = {
    println(s"set quotation $quotation")
    quotation.assembly match {
      case AssemblyReference(_, id, _, _) =>
        Seq(setQuotationByAssemblyStmt.bind()
          .setUUID("assembly", id)
          .setString("team", quotation.team)
          .setUUID("quotation", quotation.quotationId)
          .set("time", quotation.status.date.get, classOf[Instant]))
      case SharedAssemblyReference(team, id, sharedAssembly) => Seq()
    }

  }

  private def removeQuotationByName(quotation: Quotation) = {
    println(s"remove quotation $quotation")
    List(
      deleteQuotationByNameStmt.bind()
        .setString("name", quotation.name)
        .setString("team", quotation.team)
        .setUUID("quotation", quotation.quotationId)
    )
  }

  def setQuotationStatus(event: QuotationStatusSet): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      // UPDATE quotation SET
      // statusName = :statusname,
      // statusTime = :statustime
      // WHERE team = :team AND customer = :customer AND quotation = :quotation
      //      setQuotationStatusStmt.bind()
      //        .setString("statusname", event.status.name)
      //        .set("statustime", event.status.date.get, classOf[Instant])
      //        .setString("team", event.team)
      //        .setUUID("customer", event.customerId)
      //        .setUUID("quotation", event.quotationId),

      setQuotationStatusNameStmt.bind()
        .setString("statusname", event.status.name)
        .set("statustime", event.status.date.get, classOf[Instant])
        .setString("team", event.team)
        .setString("name", event.name)
        .setUUID("quotation", event.quotationId)
    ))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[QuotationEvent] =
    readSide.builder[QuotationEvent]("quotationEventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[QuotationCreated] { e =>
        createQuotation(e.event)
      }
      .setEventHandler[QuotationUpdated](e => updateQuotation(e.event))
      .setEventHandler[QuotationStatusSet] { e =>
        setQuotationStatus(e.event)
      }
      .build()

  override def aggregateTags: Set[AggregateEventTag[QuotationEvent]] = QuotationEvent.Tag.allTags

  private def createTables(): Future[Done.type] =
    (for {
      _ <- PCBCodecHelper.loadTypes(session)
      _ <- AssemblyCodec.loadTypes(session)

      //      _ <- session.executeCreateTable(
      //        //language=SQL
      //        s"""
      //           |CREATE TABLE IF NOT EXISTS assembliereferences (
      //           |            team text,
      //           |            assembly uuid,
      //           |            reference ${AssemblyCodec.TYPE_NAME},
      //           |
      //           |            PRIMARY KEY (team, assembly)
      //           |);
      //        """.stripMargin)

      //      _ <- session.executeCreateTable(
      //        //language=SQL
      //        s"""
      //           |CREATE TABLE IF NOT EXISTS customerreferences (
      //           |            team text,
      //           |            customer uuid,
      //           |
      //           |            PRIMARY KEY (team, customer)
      //           |);
      //        """.stripMargin)

      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbyname (
           |            team text,
           |            name text,
           |            quotation uuid,
           |            statusName text,
           |            statusTime timestamp,
           |            PRIMARY KEY (team, name, quotation)
           |);
        """.stripMargin
      )

      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbyorigin (
           |            team text,
           |            origin text,
           |            quotation uuid,
           |            time timestamp,
           |            PRIMARY KEY (team, origin, quotation)
           |);
        """.stripMargin
      )

      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbyversion ( //ITEMS
           |            team text,
           |            version uuid ,
           |            quotation uuid,
           |            time timestamp,
           |            PRIMARY KEY (team, version, quotation)
           |);
        """.stripMargin
      )
      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbyassembly ( //ITEMS
           |            team text,
           |            assembly uuid ,
           |            quotation uuid,
           |            time timestamp,
           |            PRIMARY KEY (team, assembly, quotation)
           |);
        """.stripMargin
      )
      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbyspecification ( //ITEMS
           |            team text,
           |            specification uuid ,
           |            quotation uuid,
           |            item uuid,
           |            time timestamp,
           |            PRIMARY KEY (team, specification, quotation, item)
           |);
        """.stripMargin
      )
      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbycustomer (
           |            team text,
           |            customer uuid ,
           |            quotation uuid,
           |            time timestamp,
           |            PRIMARY KEY (team, customer, quotation)
           |);
        """.stripMargin
      )
      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbycontact (
           |            team text,
           |            contact uuid ,
           |            quotation uuid,
           |            statusName text,
           |            statusTime timestamp,
           |            PRIMARY KEY (team, contact, quotation)
           |);
        """.stripMargin
      )

    } yield QuotationReadSide.registerCodecs(session).map(_ => Done)).flatten

}

object QuotationReadSide {
  def registerCodecs(session: CassandraSession)(implicit ex: ExecutionContext) = {
    PricedAssemblyRepo.registerCodecs(session)
    AssemblyCodec.registerCodecs(session)
    PCBCodecHelper.registerPCBCodecs(session)
  }
}
