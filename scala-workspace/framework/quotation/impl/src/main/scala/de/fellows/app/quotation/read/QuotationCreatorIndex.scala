package de.fellows.app.quotation.read

import akka.Done
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.quotation.entity.quotation.QuotationEvents.{QuotationCreated, QuotationEvent, QuotationUpdated}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging

import scala.concurrent.{ExecutionContext, Future}

class QuotationCreatorIndex(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[QuotationEvent] with StackrateLogging {
  var setQuotationByStatusStmt: PreparedStatement    = _
  var deleteQuotationByStatusStmt: PreparedStatement = _

  def prepareStatements(): Future[Done] =
    for {
      // language=SQL
      setQuotationByStatus <- session.prepare(
        // INSERT INTO quotationsbycustomer(team,customer,quotation, time) VALUES (:team, :customer, :quotation, :time)
        """
          | UPDATE quotationsbycreator SET
          |  name = :name
          | WHERE team = :team AND creator = :creator AND quotation = :quotation
          |""".stripMargin
      )
      deleteQuotationByStatus <- session.prepare(
        "DELETE FROM quotationsbycreator WHERE team = :team AND creator = :creator AND quotation = :quotation"
      )

    } yield {
      QuotationReadSide.registerCodecs(session).map(_ => Done)

      setQuotationByStatusStmt = setQuotationByStatus
      deleteQuotationByStatusStmt = deleteQuotationByStatus

      Done
    }

  def createQuotation(event: QuotationCreated): Future[Seq[BoundStatement]] =
    Future.successful(Seq(
      setQuotationByStatusStmt.bind()
        .setString("team", event.quotation.team)
        .setUUID("creator", event.quotation.creator)
        .setUUID("quotation", event.quotation.quotationId)
        .setString("name", event.quotation.name)
    ))

  def updateQuotation(event: QuotationUpdated): Future[Seq[BoundStatement]] =
    if (event.oldQuotation.creator != event.quotation.creator) {
      Future.successful(Seq(
        deleteQuotationByStatusStmt.bind()
          .setString("team", event.oldQuotation.team)
          .setUUID("creator", event.oldQuotation.creator)
          .setUUID("quotation", event.oldQuotation.quotationId),
        setQuotationByStatusStmt.bind()
          .setString("team", event.quotation.team)
          .setUUID("creator", event.quotation.creator)
          .setUUID("quotation", event.quotation.quotationId)
          .setString("name", event.quotation.name)
      ))
    } else {
      Future.successful(Seq())
    }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[QuotationEvent] =
    readSide.builder[QuotationEvent]("quotationCreatorEventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[QuotationCreated] { e =>
        createQuotation(e.event)
      }
      .setEventHandler[QuotationUpdated](e => updateQuotation(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[QuotationEvent]] = QuotationEvent.Tag.allTags

  private def createTables() =
    (for {
      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsbycreator (
           |            team text,
           |            name text,
           |            quotation uuid,
           |            creator uuid,
           |            PRIMARY KEY (team, creator, quotation)
           |);
        """.stripMargin
      )

    } yield QuotationReadSide.registerCodecs(session).map(_ => Done)).flatten
}
