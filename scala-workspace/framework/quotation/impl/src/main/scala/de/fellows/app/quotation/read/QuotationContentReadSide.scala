package de.fellows.app.quotation.read

import akka.Done
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import de.fellows.app.quotation.{Quotation, QuotationListItem, QuotationOrigin, QuotationStatus}
import de.fellows.app.quotation.entity.quotation.QuotationEvents.{QuotationCreated, QuotationEvent, QuotationStatusSet, QuotationUpdated}
import de.fellows.app.quotation.entity.quotationitem.QuotationItemEvents.QuotationItemEvent
import de.fellows.utils.communication.ServiceDefinition
import play.api.Logging

import java.util.{Date, UUID}
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._

class QuotationContentReadSide(session: CassandraSession)(implicit ec: ExecutionContext, service: ServiceDefinition) {

  def getQuotation(origin: QuotationOrigin, team: String, id: UUID): Future[Option[QuotationListItem]] =
    session.selectOne("SELECT * FROM quotationsWithContent WHERE team = ? AND id = ?", team, id)
      .map(_.map(convertRow(origin)))

  def getQuotationList(origin: QuotationOrigin,team: String, ids: Seq[UUID]): Future[Seq[QuotationListItem]] =
    session.selectAll("SELECT * FROM quotationsWithContent WHERE team = ? AND id IN ?", team, ids.asJava)
      .map(_.map(convertRow(origin)))

  private def convertRow(origin: QuotationOrigin)(row: Row): QuotationListItem =
    QuotationListItem(
      team = row.getString("team"),
      quotationId = row.getUUID("id"),
      name = row.getString("name"),
      customerId = row.getUUID("customer"),
      status = QuotationStatus(
        name = row.getString("status"),
        date = Option(row.getTimestamp("statusTime")).map(_.toInstant)
      ),
      created = row.getTimestamp("created").toInstant,
      creator = row.getUUID("creator"),
      origin = origin
    )
}

class QuotationContentReadSideProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[QuotationEvent] with Logging {

  var _updateQuotation: PreparedStatement = _
  var _updateStatus: PreparedStatement    = _
  var _deleteQuotation: PreparedStatement = _
  private def createTables() =
    for {
      _ <- session.executeCreateTable(
        // language=SQL
        s"""
           |CREATE TABLE IF NOT EXISTS quotationsWithContent (
           |            team text,
           |            id uuid,
           |            
           |            name text,
           |            customer uuid,
           |            status text,
           |            statusTime timestamp,
           |            created timestamp,
           |            creator uuid,
           |
           |            PRIMARY KEY (team, id)
           |);
        """.stripMargin
      )
    } yield Done

  def prepareStatements(): Future[Done] =
    for {
      update <- session.prepare(
        // language=SQL
        """
          |UPDATE quotationsWithContent SET name = :name, customer = :customer, status = :status, statusTime = :statusTime, created = :created, creator = :creator WHERE team = :team AND id = :id;
          |""".stripMargin
      )
      updateStatus <- session.prepare(
        // language=SQL
        """
          |UPDATE quotationsWithContent SET  status = :status, statusTime = :statusTime WHERE team = :team AND id = :id;
          |""".stripMargin
      )
      delete <- session.prepare(
        // language=SQL
        """
          |DELETE FROM quotationsWithContent WHERE team = :team AND id = :id;
          |""".stripMargin
      )
    } yield {
      this._updateQuotation = update
      this._updateStatus = updateStatus
      this._deleteQuotation = delete
      Done
    }

  def updateQuotation(q: Quotation): Future[Seq[BoundStatement]] =
    Future.successful(
      Seq(
        this._updateQuotation.bind()
          .setString("team", q.team)
          .setUUID("id", q.quotationId)
          .setString("name", q.name)
          .setUUID("customer", q.customerId)
          .setString("status", q.status.name)
          .setTimestamp("statusTime", Date.from(q.status.date.orNull))
          .setTimestamp("created", Date.from(q.created))
          .setUUID("creator", q.creator)
      )
    )

  def setQuotationStatus(event: QuotationStatusSet): Future[Seq[BoundStatement]] =
    Future.successful(Seq(
      this._updateStatus.bind()
        .setString("team", event.team)
        .setUUID("id", event.quotationId)
        .setString("status", event.status.name)
        .setTimestamp("statusTime", Date.from(event.status.date.orNull))
    ))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[QuotationEvent] =
    readSide.builder[QuotationEvent]("quotationContentEventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[QuotationCreated] { e =>
        updateQuotation(e.event.quotation)
      }
      .setEventHandler[QuotationUpdated](e => updateQuotation(e.event.quotation))
      .setEventHandler[QuotationStatusSet] { e =>
        setQuotationStatus(e.event)
      }
      .build()

  override def aggregateTags: Set[AggregateEventTag[QuotationEvent]] = QuotationEvent.Tag.allTags

}
