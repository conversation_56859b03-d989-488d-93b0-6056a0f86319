package de.fellows.app.quotation.entity.quotationitem

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.assembly.commons.{AbstractAssemblyReference, AssemblyReference}
import de.fellows.app.quotation.{QuotationItem, QuotationItemInfo}
import de.fellows.utils.collaboration.TimelineCommand
import play.api.libs.json.{Format, Json}

import java.util.UUID

object QuotationItemCommands {

  sealed trait QuotationItemCommand

  case class GetQuotationItem(item: UUID) extends QuotationItemCommand with ReplyType[QuotationItem]

  case class SetQuotationItem(
      item: UUID,
      ass: AbstractAssemblyReference,
      name: Option[String],
      description: Option[String],
      info: Option[QuotationItemInfo],
      tcmd: TimelineCommand
  ) extends QuotationItemCommand with ReplyType[QuotationItem]

  case class DeleteQuotationItem(
      item: UUID,
      tcmd: TimelineCommand
  ) extends QuotationItemCommand with ReplyType[Done]

  case class SetQuotationItemInfo(
      item: UUID,
      info: QuotationItemInfo,
      tcmd: TimelineCommand
  ) extends QuotationItemCommand with ReplyType[QuotationItem]

  case class SetQuotationItemWon(item: UUID, won: Boolean, tcmd: TimelineCommand)
      extends QuotationItemCommand
      with ReplyType[QuotationItem]

  object SetQuotationItem {
    implicit val f: Format[SetQuotationItem] = Json.format[SetQuotationItem]
  }

  object DeleteQuotationItem {
    implicit val f: Format[DeleteQuotationItem] = Json.format[DeleteQuotationItem]
  }

  object SetQuotationItemInfo {
    implicit val f: Format[SetQuotationItemInfo] = Json.format[SetQuotationItemInfo]
  }

  object SetQuotationItemWon {
    implicit val f: Format[SetQuotationItemWon] = Json.format[SetQuotationItemWon]
  }

  object GetQuotationItem {
    implicit val f: Format[GetQuotationItem] = Json.format[GetQuotationItem]
  }

}
