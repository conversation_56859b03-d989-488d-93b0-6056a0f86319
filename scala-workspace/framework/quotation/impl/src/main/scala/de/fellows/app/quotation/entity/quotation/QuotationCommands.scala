package de.fellows.app.quotation.entity.quotation

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import de.fellows.app.quotation.{Quotation, QuotationCreation, QuotationStatus}
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.common.Address
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

object QuotationCommands {

  sealed trait QuotationCommand

  case class CreateQuotation(
      team: String,
      id: UUID,
      c: QuotationCreation,
      created: Instant,
      createdBy: UUID,
      tcmd: TimelineCommand
  ) extends QuotationCommand with ReplyType[Quotation]

  case class SetQuotationStatus(team: String, quotation: UUID, status: QuotationStatus, tcmd: TimelineCommand)
      extends QuotationCommand with ReplyType[Quotation]

  case class AddQuotationItem(team: String, quotation: UUID, item: UUID, tcmd: TimelineCommand) extends QuotationCommand
      with ReplyType[Quotation]

  case class RemoveQuotationItem(team: String, quotation: UUID, item: UUID, tcmd: TimelineCommand)
      extends QuotationCommand with ReplyType[Quotation]

  case class SetQuotation(team: String, id: UUID, c: QuotationCreation, tcmd: TimelineCommand) extends QuotationCommand
      with ReplyType[Quotation]

  case class GetQuotation(team: String, id: UUID) extends QuotationCommand with ReplyType[Quotation]

  case class SetAddress(
      team: String,
      quotation: UUID,
      billing: Boolean,
      address: Address,
      tcmd: TimelineCommand
  ) extends QuotationCommand with ReplyType[Done]

  object CreateQuotation {
    implicit val f: Format[CreateQuotation] = Json.format[CreateQuotation]
  }

  object GetQuotation {
    implicit val f: Format[GetQuotation] = Json.format[GetQuotation]
  }

  object AddQuotationItem {
    implicit val f: Format[AddQuotationItem] = Json.format[AddQuotationItem]
  }

  object RemoveQuotationItem {
    implicit val f: Format[RemoveQuotationItem] = Json.format[RemoveQuotationItem]
  }

  object SetQuotationStatus {
    implicit val f: Format[SetQuotationStatus] = Json.format[SetQuotationStatus]
  }

  object SetAddress {
    implicit val f: Format[SetAddress] = Json.format[SetAddress]
  }

  object SetQuotation {
    implicit val f: Format[SetQuotation] = Json.format[SetQuotation]
  }

}
