package de.fellows.app.quotation.uni

import de.fellows.app.quotation.{ PriceBreak, PriceList }
import play.api.libs.json.{ Format, Json }

object QuotationInternals {

  case class UnifiedPriceList(service: String, priceBreaks: Seq[UnifiedPriceBreak])

  case class UnifiedPriceBreak(
      supplier: String,
      min: Int,
      max: Int,
      deliveryTime: BigDecimal,
      unitPrice: Option[BigDecimal],
      panelPrice: Option[BigDecimal],
      panelCount: Option[Int],
      fixCosts: Option[BigDecimal]
  ) {
    def contains(amount: Int): Boolean =
      amount >= min && amount <= max

    def contains(rangeMin: Int, rangeMax: Int): Boolean =
      rangeMin >= this.min && rangeMax <= max

    def intersects(rangeMin: Int, rangeMax: Int): Boolean =
      rangeMin < this.max && rangeMax > min

    def intersects(other: UnifiedPriceBreak): Boolean =
      other.min < this.max && other.max > min

    def isUnitPrice: Boolean =
      unitPrice.isDefined && panelCount.isEmpty && panelPrice.isEmpty

    def isPanelPrice: Boolean =
      unitPrice.isEmpty && panelCount.isDefined && panelPrice.isDefined

    def toApi: PriceBreak =
      PriceBreak(
        this.min,
        this.max,
        this.deliveryTime,
        this.unitPrice,
        this.panelPrice,
        this.panelCount,
        this.fixCosts
      )
  }

  object UnifiedPriceList {
    implicit val format: Format[UnifiedPriceList] = Json.format

    def apply(priceList: PriceList, supplier: String, service: String): UnifiedPriceList =
      new UnifiedPriceList(
        service,
        priceList.breaks.map { b =>
          UnifiedPriceBreak(supplier, b)
        }
      )
  }

  object UnifiedPriceBreak {
    implicit val format: Format[UnifiedPriceBreak] = Json.format

    def apply(supplier: String, priceBreak: PriceBreak): UnifiedPriceBreak =
      UnifiedPriceBreak(
        supplier,
        priceBreak.min,
        priceBreak.max,
        priceBreak.deliveryTime,
        priceBreak.unitPrice,
        priceBreak.panelPrice,
        priceBreak.panelCount,
        priceBreak.fixCosts
      )

  }

}
