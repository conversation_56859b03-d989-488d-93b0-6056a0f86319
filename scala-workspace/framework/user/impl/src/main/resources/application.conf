include "main-application.conf"

play.application.loader = de.fellows.app.user.impl.UserServiceLoader


user.cassandra.keyspace = ${fellows.persistence.rootKeyspace}user


cassandra-journal {
  keyspace = ${user.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${user.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${user.cassandra.keyspace}read
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "user"


# fellows.serviceconfig = ${fellows.services.user}
fellows.user.teams.default = {
}

play.http.parser.maxMemoryBuffer = 8M

fellows.storage {
  service = ${fellows.storage.base}/user
}

akka {
  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # Log level for the very basic logger activated during ActorSystem startup.
  # This logger prints the log messages to stdout (System.out).
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  stdout-loglevel = "DEBUG"
}


