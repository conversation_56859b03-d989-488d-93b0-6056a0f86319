package de.fellows.app.user.impl.entities.teams

import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.user.api.Teams.Team
import play.api.libs.json.{Format, Json}

object Events {

  sealed trait TeamEvent extends AggregateEvent[TeamEvent] {
    override def aggregateTag: AggregateEventShards[TeamEvent] = TeamEvent.Tag
  }

  object TeamEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[TeamEvent](NumShards)
  }

  case class TeamCreated(team: Team) extends TeamEvent

  case class TeamUpdated(team: Team) extends TeamEvent

  case class TeamDeleted(team: Team) extends TeamEvent

  object TeamCreated {
    implicit val f: Format[TeamCreated] = Json.format[TeamCreated]
  }

  object TeamUpdated {
    implicit val f: Format[TeamUpdated] = Json.format[TeamUpdated]
  }

  object TeamDeleted {
    implicit val f: Format[TeamDeleted] = Json.format[TeamDeleted]
  }

}
