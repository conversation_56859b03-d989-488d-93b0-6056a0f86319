// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.user.impl

import akka.actor.ActorSystem
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport._
import com.lightbend.lagom.scaladsl.api.{ServiceCall, ServiceLocator}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntity, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.app.notification
import de.fellows.app.notification.common.Notification
import de.fellows.app.security.AccessControlServiceComposition.authorizedString
import de.fellows.app.security.SecurityService
import de.fellows.app.user.api
import de.fellows.app.user.api.Teams.Team
import de.fellows.app.user.api.TemplateAPI.RenderRequest
import de.fellows.app.user.api.{CreateUser, _}
import de.fellows.app.user.impl.entities._
import de.fellows.app.user.impl.entities.teams.Commands.{CreateTeam, GetTeam, UpdateTeam}
import de.fellows.app.user.impl.entities.teams.Events.{TeamCreated, TeamEvent}
import de.fellows.app.user.impl.entities.teams.{Events, TeamEntity}
import de.fellows.app.user.impl.entities.templates._
import de.fellows.app.user.impl.i18n.InviteI18n
import de.fellows.app.user.impl.read.TeamRepository
import de.fellows.app.user.impl.read.template.TemplateRepository
import de.fellows.app.user.impl.templates.FilePathFilter
import de.fellows.app.user.impl.{entities => userentities}
import de.fellows.utils.HashUtils._
import de.fellows.utils.ServiceCallUtils._
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.communication.{InternalServiceError, ServiceDefinition, ServiceException}
import de.fellows.utils.mailgun.Mailgun
import de.fellows.utils.metrics.Metrics.metric
import de.fellows.utils.security.AuthenticationServiceComposition._
import de.fellows.utils.security._
import de.fellows.utils.streams.{StreamMessage, ValidMessage}
import de.fellows.utils.telemetry.KamonUtils
import de.fellows.utils.templating.{HTMLGenerator, PDFGenerator, TemplateGenerator}
import de.fellows.utils.{Stackrate, _}
import play.api.Logging
import play.api.libs.json._
import play.api.libs.ws.WSClient

import java.nio.file.{Files, Paths}
import java.time.Instant
import java.util.UUID
import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}
import scala.reflect.ClassTag

class UserServiceImpl(
    registry: PersistentEntityRegistry,
    system: ActorSystem,
    authRepository: AuthRepository,
    teamRepository: TeamRepository,
    templates: TemplateRepository,
    sec: SecurityService,
    sl: ServiceLocator,
    ws: WSClient
)(implicit ec: ExecutionContext, sd: ServiceDefinition) extends UserService with StackrateAPIImpl with Logging {

  val conf: Config                      = ConfigFactory.load
  implicit val actorSystem: ActorSystem = system

  implicit val userService: UserServiceImpl = this
  implicit val s: SecurityService           = sec;

  def mapByName(name: String): Future[AvailableResponse] =
    authRepository.getUserByName(name).map {
      case Some(_) => AvailableResponse(Some(false), None)
      case _       => AvailableResponse(Some(true), None)
    }

  def mapByMail(mail: String): Future[AvailableResponse] =
    authRepository.getUserByMail(mail).map {
      case Some(_) => AvailableResponse(None, Some(false))
      case _       => AvailableResponse(None, Some(true))
    }

  override def nameAvailable(): ServiceCall[AvailableRequest, AvailableResponse] = ServiceCall {
    case AvailableRequest(Some(name), None) => mapByName(name)

    case AvailableRequest(None, Some(mail)) => mapByMail(mail)

    case AvailableRequest(Some(name), Some(mail)) =>
      val mailF = mapByMail(mail)
      val nameF = mapByName(name)

      for {
        mailRes <- mailF
        nameRes <- nameF
      } yield AvailableResponse(nameRes.username, mailRes.email)

    case _ => Future.successful(AvailableResponse(None, None))

  }

  override def ip(): ServerServiceCall[String, String] = ServerServiceCall { (header, content) =>
    Future.successful(ResponseHeader.Ok, s"${header.headerMap}")
  }

  override def invite(team: String): ServiceCall[Invite, String] =
    authorizedString(token => s"user:${team}:*:*:*:write") { (token, _) =>
      ServerServiceCall { inv: Invite =>
        val mailaddr = inv.mail.map { m =>
          val r = m.trim
          if (!r.contains('@') || r.startsWith(".") || r.endsWith(".")) {
            throw new TransportException(TransportErrorCode.BadRequest, s"illegal email ${r}")
          }
          r.toLowerCase()
        }

        val t           = InvitationToken(team = team, inviter = token.userId, mail = mailaddr)
        val inviteToken = JwtTokenUtil.generateInvitationToken(t).authToken

        val domain   = Stackrate.endpoint(team).toString
        val endpoint = domain + s"/auth/register?invitation=$inviteToken"

        if (inv.sendNotification.getOrElse(false)) {
          if (inv.greeting.isEmpty) {
            throw new TransportException(TransportErrorCode.BadRequest, "greeting is needed")
          }
          mailaddr.foreach { mail =>
            val invDesc = InviteI18n.get(inv.language.getOrElse(""), domain, endpoint)

            Mailgun.sendGenericEmail(
              to = Seq(mail),
              name = inv.greeting.get,
              desc = invDesc
            )(ws, sl, ec)
          }
        }
        Future.successful(inviteToken)
      }
    }

  import de.fellows.utils.ConfigUtils._

  override def addToTeam(id: UUID): ServiceCall[Seq[String], Done] =
    authorizedString(token =>
      s"admin:*:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { teams =>
        addUserToTeams(id, teams)
      }
    }

  override def addToTeamByEmailOrName(emailOrName: Option[String]): ServiceCall[Seq[String], Done] =
    authorizedString(token =>
      s"admin:*:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { teams =>
        FutureUtils.option(
          emailOrName.map(name => authRepository.getUserOr(name, name))
        ).flatMap {
          _.flatten match {
            case Some(user) if !user.technical => addUserToTeams(user.uid, teams)
            case _ => throw new TransportException(TransportErrorCode.NotFound, "User not found")
          }
        }
      }
    }

  private def addUserToTeams(userId: UUID, teams: Seq[String]): Future[Done.type] =
    registry.refFor[UserEntity](userId.toString)
      .ask(AddUserToTeam(userId, teams))
      .map(_ => Done)

  override def removeFromTeam(id: UUID, team: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"admin:*:*:*:write"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        registry.refFor[UserEntity](id.toString).ask(RemoveUserFromTeam(id, Seq(team))).map(_ => Done)
      }
    }

  override def registerTechnical(
      team: String,
      invitation: Option[String]
  ): ServerServiceCall[api.CreateUser, UserLogin] =
    authorizedString(token =>
      s"team:$team:$team:*:users:create"
    ) { (token, _) =>
      ServerServiceCall { (x, createUser) =>
        _doRegister(team, createUser, true)
      }
    }

  override def register(team: String, invitation: Option[String]): ServerServiceCall[api.CreateUser, UserLogin] =
    metric { ctx =>
      ServerServiceCall { (x, createUser) =>
        val open = conf.getBooleanOption("fellows.env.registration.open") match {
          case Some(value) => value
          case None        => true
        }

        if (!open) {

          invitation match {
            case Some(value) =>
              val b = AuthenticationServiceComposition.decodeTypedWithResponse[InvitationToken](value)

              if (
                (team != b.team) || b.mail.orElse(createUser.email) != (createUser.email.map(email =>
                  email.toLowerCase()
                ))
              ) {
                logger.warn(
                  s"Illegal Token! token is ${b}, requested team: $team, requested email: ${createUser.email}"
                )
                throw new TransportException(TransportErrorCode.Forbidden, "Illegal Invite Token.")
              }

            case None =>
              throw new TransportException(TransportErrorCode.Forbidden, "Registration without invitation is closed")
          }
        }

        _doRegister(team, createUser, false)
      }
    }

  private def _doRegister(team: String, createUser: CreateUser, technical: Boolean) = {

    val username = createUser.username.toLowerCase
    val email    = createUser.email.map(email => email.toLowerCase)

    (for {
      existingUser <-
        FutureUtils.option(createUser.email.map(e => authRepository.getUserOr(username, e))).map(_.flatten)
      teamOption <- registry.refFor[TeamEntity](team).ask(GetTeam).map(_.response)
    } yield {
      if (!technical && email.isEmpty) {
        throw Forbidden(s"Regular User needs email")
      }
      if (!technical && createUser.password.isEmpty) {
        throw Forbidden(s"Regular User needs password")
      }

      if (existingUser.isDefined) {
        val u = existingUser.get

        if (u.username == username) {
          throw Forbidden(s"Username exists")
        }

        if (email == u.email.map(email => email.toLowerCase)) {
          throw Forbidden("Email exists")
        }

        throw Forbidden("User exists")
      }

      if (teamOption.isEmpty) {
        throw Forbidden(s"Team does not exist")
      }

      val team = teamOption.get

      val userId = UUID.randomUUID()
      val hash   = createUser.password.map(pw => hashPassword(pw.toCharArray))
      refFor[UserEntity](userId).ask(userentities.CreateUser(
        email,
        Seq(team.domain),
        username,
        hash,
        userId,
        technical
      )).flatMap {
        u =>
          val usr = toApi(team.domain, u)
          if (!u.technical) {
            _sendMail(usr)
          }

          sec._newUser(team.domain, technical).invoke(usr.uid).flatMap { perms =>
            FutureUtils.option(createUser.password.map(pw =>
              loginUser(usr, pw, team.domain, Some(perms))
            )).map(x => (u, x))
          }

      }
        .map(x => (ResponseHeader.Ok, x._2.getOrElse(UserLogin(toApi(team.domain, x._1), None, None))))

    }).flatten
  }

  private def toApi(team: String, u: userentities.User) =
    api.User(u.email, Seq(team), u.username, u.uid, u.technical, u.activated)

  private def toApi(u: userentities.User) =
    api.User(u.email, u.team, u.username, u.uid, u.technical, u.activated)

  def loginUser(
      user: api.User,
      pass: String,
      team: String,
      perms: Option[Seq[Permission]]
  ): Future[UserLogin] = {
    val defUser = authRepository.getDefaultUser()
    if (defUser.isDefined && defUser.map(_._1.uid).contains(user.uid) && defUser.map(_._2).contains(pass)) {
      val u = defUser.get._1
      val tokenContent = TokenContent(
        u.uid,
        team,
        u.username,
        u.email,
        None,
        false,
        Seq(SecurityClaim(Some("*")))
      )

      val t = JwtTokenUtil.generateTokens(tokenContent)
      Future.successful(
        UserLogin(
          user = u,
          authToken = Some(t.authToken),
          refreshToken = t.refreshToken
        )
      )
    } else {
      refFor[UserEntity](user.uid).ask(GetUser)
        .map(_.response)
        .flatMap {
          case None => throw ACCESS_DENIED
          case Some(_u) =>
            if (!_u.technical && _u.hash.isDefined) {
              val hashed = hashPassword(pass.toCharArray, _u.hash.get.salt)
              if (hashed.hash.sameElements(_u.hash.get.hash)) {
                createSessionUserEntity(_u, team, perms).map { token =>
                  UserLogin(
                    toApi(team, _u),
                    Some(token.authToken),
                    Some(token.refreshToken.getOrElse(throw new IllegalStateException("Refresh token missing")))
                  )
                }
              } else {
                Thread.sleep(1000) // bruteforce prevention
                throw ACCESS_DENIED
              }
            } else {
              throw ACCESS_DENIED
            }
        }
    }
  }

  def createSessionUserEntity(
      user: userentities.User,
      team: String,
      perms: Option[Seq[Permission]] = None
  ) =
    (perms match {
      case Some(p) => Future.successful(p)
      case None    => sec._getAllPermissions(team, user.uid).invoke()
    }).map { perms =>
      val tokenContent = TokenContent(
        user.uid,
        team,
        user.username,
        user.email,
        None,
        false,
        perms.map(p => SecurityClaim(Some(p.toPermissionString())))
      )
      JwtTokenUtil.generateTokens(tokenContent)
    }

  def createSession(
      user: api.User,
      team: String,
      perms: Option[Seq[Permission]] = None
  ) =
    (perms match {
      case Some(p) => Future.successful(p)
      case None    => sec._getAllPermissions(team, user.uid).invoke()
    }).map { perms =>
      val tokenContent = TokenContent(
        user.uid,
        team,
        user.username,
        user.email,
        None,
        false,
        perms.map(p => SecurityClaim(Some(p.toPermissionString())))
      )
      JwtTokenUtil.generateTokens(tokenContent)
    }

  override def apiToken(): ServiceCall[ApiTokenLogin, UserLogin] =
    ServerServiceCall {
      (h, apiToken) =>
        authRepository.getFullUserByToken(apiToken.apiToken)
          .flatMap {
            case None => throw Forbidden(s"Token is invalid")
            case Some(t) =>
              if (t._2.isExpired()) {
                throw Forbidden(s"Token expired")
              }

              if (!t._1.team.contains(t._2.team)) {
                throw ACCESS_DENIED
              }

              createSession(t._1, t._2.team, None).map { token =>
                UserLogin(
                  t._1,
                  Some(token.authToken),
                  Some(token.refreshToken.getOrElse(throw new IllegalStateException("Refresh token missing")))
                )
              }
          }
          .map(x => (ResponseHeader.Ok, x))
    }

  override def createApiToken(userId: UUID, team: String): ServiceCall[NotUsed, ApiToken] =
    authorizedString(token => s"user:${token.team}:${userId}:${userId}:apitoken:write") { (token, _) =>
      ServerServiceCall { _ =>
        val entity = refFor[UserEntity](userId)
        entity.ask(GetUser).map(_.response).flatMap {
          case Some(user) if user.team.contains(team) => entity.ask(AddApiToken(userId, team, Instant.now(), None))
          case Some(_) =>
            throw new TransportException(TransportErrorCode.Forbidden, s"User $userId not part of team $team")
          case None => throw new TransportException(TransportErrorCode.NotFound, s"User $userId not found")
        }
      }
    }

  val ACCESS_DENIED = Forbidden(s"Access denied")

  override def login(team: String, emailOrName: String): ServerServiceCall[Login, UserLogin] =
    metric { metrics =>
      ServerServiceCall {
        (h, pass) =>
          metrics.withRequest = false
          (for {
            fullteam   <- teamRepository.getTeamByDomain(team)
            user       <- authRepository.getUser(emailOrName)
            partOfTeam <- authRepository.isPartOfTeam(user.map(_.uid), fullteam.map(_.domain))
          } yield {
            if (fullteam.isEmpty || user.isEmpty || !partOfTeam) {
              throw ACCESS_DENIED
            }

            loginUser(user.get, pass.password, fullteam.map(_.domain).getOrElse(team), None)
          }).flatten.map(x => (ResponseHeader.Ok, x))
      }
    }

  override def refresh(updateFields: Option[Boolean] = None): ServiceCall[NotUsed, TokenRefreshDone] =
    authenticatedWithRefreshToken { (tokenContent, _) =>
      ServerServiceCall { _ =>
        val updateAuthTokenOnly = {
          val token = JwtTokenUtil.generateAuthTokenOnly(tokenContent)
          Future.successful(TokenRefreshDone(token.authToken))
        }

        if (tokenContent.share.isDefined) {
          updateAuthTokenOnly
        } else {
          updateFields match {
            case None =>
              updateAuthTokenOnly
            case Some(false) =>
              updateAuthTokenOnly

            case Some(true) =>
              (for {
                user <- refFor[UserEntity](tokenContent.userId).ask(GetUser).map(_.response)
              } yield user match {
                case Some(u) =>
                  sec._getAllPermissions(tokenContent.team, tokenContent.userId).invoke().map { perms =>
                    val newTokenContent = TokenContent(
                      u.uid,
                      tokenContent.team,
                      u.username,
                      u.email,
                      None,
                      false,
                      perms.map(p => SecurityClaim(Some(p.toPermissionString())))
                    )
                    val token = JwtTokenUtil.generateAuthTokenOnly(newTokenContent)
                    TokenRefreshDone(token.authToken)
                  }

                case _ => throw NotFound(s"user ${tokenContent.userId} not found")
              }).flatten
          }
        }

      }
    }

  private def refFor[T <: PersistentEntity: ClassTag](id: UUID) = registry.refFor[T](id.toString)

  override def userTopic(): Topic[StreamMessage[UserMessage]] =
    TopicProducer.taggedStreamWithOffset(UserEvent.Tag) { (tag, offset) =>
      registry.eventStream(tag, offset).mapConcat { x =>
        x.event match {
          case msg: UserCreated => immutable.Seq((
              ValidMessage(UserCreatedMessage(msg.team, msg.username, msg.uid, msg.email, msg.technical)),
              x.offset
            ))
          case msg: UserDeleted => immutable.Seq((
              ValidMessage(UserDeletedMessage(msg.team, msg.username, msg.uid, msg.email, msg.technical)),
              x.offset
            ))
          case msg: PasswordReset => immutable.Seq((ValidMessage(PasswordResetMessage(msg.user)), x.offset))
          case msg: PasswordSet   => immutable.Seq((ValidMessage(PasswordSetMessage(msg.user.uid)), x.offset))
          case msg: EmailSet =>
            immutable.Seq((ValidMessage(EmailSetMessage(msg.user, msg.oldMail, msg.newMail)), x.offset))
          case msg: TeamsChanged =>
            immutable.Seq((ValidMessage(TeamsChangedMessage(msg.user, msg.oldTeams, msg.teams)), x.offset))
          case msg: Activated => immutable.Seq((ValidMessage(ActivatedMessage(msg.id)), x.offset))
          case msg: TokenRefreshed =>
            if (msg.user.isEmpty) {
              Nil
            } else {
              immutable.Seq((
                ValidMessage(TokenRefreshedMessage(toApi(msg.user.get), msg.token.token, msg.token.created)),
                x.offset
              ))
            }
          case msg: ResetTokenCreated =>
            if (msg.user.isEmpty) {
              Nil
            } else {
              immutable.Seq((
                ValidMessage(ResetTokenCreatedMessage(toApi(msg.user.get), msg.token.token, msg.token.created)),
                x.offset
              ))
            }
          case msg: ApiTokenAdded => immutable.Seq((ValidMessage(ApiTokenAddedMessage(msg.id)), x.offset))
        }
      }
    }

  override def teamTopic(): Topic[StreamMessage[TeamMessage]] =
    TopicProducer.taggedStreamWithOffset(TeamEvent.Tag) { (tag, offset) =>
      registry.eventStream(tag, offset).mapConcat { x =>
        x.event match {
          case TeamCreated(team) => immutable.Seq((ValidMessage(TeamMessage(team, TeamMessage.CREATED)), x.offset))
          case Events.TeamUpdated(team) =>
            immutable.Seq((ValidMessage(TeamMessage(team, TeamMessage.UPDATED)), x.offset))
          case Events.TeamDeleted(team) =>
            immutable.Seq((ValidMessage(TeamMessage(team, TeamMessage.DELETED)), x.offset))
        }
      }
    }

  //  override def userCreatedTopic(): Topic[api.User] =
  //    TopicProducer.taggedStreamWithOffset(UserEvent.Tag)((tag, offset) => {
  //      registry.eventStream(tag, offset).mapConcat(filterUserCreatedEvents(convertEvent))
  //    })
  //
  //  override def teamCreatedTopic(): Topic[Team] =
  //    TopicProducer.taggedStreamWithOffset(TeamEvent.Tag)((tag, offset) => {
  //      registry.eventStream(tag, offset).mapConcat{
  //        case x@EventStreamElement(_, ev: TeamCreated, _) => immutable.Seq((ev.team, x.offset))
  //        case _ => Nil
  //      }
  //    })
  //
  //  override def userDeletedTopic(): Topic[api.User] =
  //    TopicProducer.taggedStreamWithOffset(UserEvent.Tag)((tag, offset) => {
  //      registry.eventStream(tag, offset).mapConcat(filterUserDeletedEvents(convertEvent))
  //    })

  override def userNotificationTopic(): Topic[Notification] =
    TopicProducer.taggedStreamWithOffset(UserEvent.Tag) { (tag, offset) =>
      registry.eventStream(tag, offset).mapConcat(filterNotificationEvents)
    }

  //  override def userStateTopic(): Topic[UserStateMessage] =
  //    TopicProducer.taggedStreamWithOffset(UserEvent.Tag)((tag, offset) => {
  //      registry.eventStream(tag, offset).mapConcat{
  //        case x@EventStreamElement(a, event: TokenRefreshed, b) =>
  //          if (event.user.isDefined) {
  //            immutable.Seq(
  //              (ActivationTokenChanged(toApi(event.user.get), event.token.token), x.offset)
  //            )
  //          } else {
  //            Nil
  //          }
  //        case x@EventStreamElement(a, event: ResetTokenCreated, b) =>
  //          if (event.user.isDefined) {
  //            immutable.Seq(
  //              (PasswordResetRequested(toApi(event.user.get), event.token.token), x.offset)
  //            )
  //          } else {
  //            Nil
  //          }
  //        case _ => Nil
  //      }
  //    })

  private def filterNotificationEvents(ev: EventStreamElement[UserEvent]) = ev match {
    case x @ EventStreamElement(a, event: EmailSet, b) => immutable.Seq((
        Notification(
          id = None,
          service = "user",
          icon = None,
          message =
            s"""
             | The email address of your account was changed to ${event.newMail}
          """.stripMargin,
          links = Seq(),
          title = "Your Email Address has been Changed",
          read = false,
          resource = Some(event.user.toString),
          owner = notification.common.User(event.user, mail = event.oldMail, username = Some(event.username)),
          date = Instant.now()
        ),
        x.offset
      ))

    case x @ EventStreamElement(_, PasswordSet(user, _), _) => immutable.Seq((
        Notification(
          id = None,
          service = "user",
          icon = None,
          message =
            s"""
             |  The password for your account has been changed.
          """.stripMargin,
          links = Seq(),
          title = "Your Password was Changed",
          read = false,
          resource = Some(user.uid.toString),
          owner = notification.common.User(user.uid, mail = user.email, username = Some(user.username)),
          date = Instant.now()
        ),
        x.offset
      ))

    case _ => Nil
  }

  private def filterUserDeletedEvents(
      converter: EventStreamElement[UserEvent] => api.User
  )(ev: EventStreamElement[UserEvent]) = ev match {
    case x @ EventStreamElement(_, _: UserDeleted, _) => immutable.Seq((converter(x), x.offset))
    case _                                            => Nil
  }

  private def filterUserCreatedEvents(
      converter: EventStreamElement[UserEvent] => api.User
  )(ev: EventStreamElement[UserEvent]) = ev match {
    case x @ EventStreamElement(_, _: UserCreated, _) => immutable.Seq((converter(x), x.offset))
    case _                                            => Nil
  }

  private def convertEvent(userEvent: EventStreamElement[UserEvent]): api.User = {
    println(s"converting event $userEvent ")
    userEvent.event match {
      case UserCreated(mail, team, user, _, id, _, _, tech) =>
        val u = api.User(mail, team, user, id, tech, false)
        println(s"converted to $u")
        u
      case UserDeleted(mail, team, user, id, technical, ac) => api.User(mail, team, user, id, technical, ac)
      case _                                                => throw new IllegalStateException()
    }
  }

  override def activate(emailOrName: String): ServiceCall[api.ActivationToken, Done] =
    ServiceCall {
      token =>
        authRepository.getUserSafe(emailOrName).flatMap(user =>
          refFor[UserEntity](user.uid).ask(Activate(user.uid, token.token))
        )
    }

  override def openSendMail(sendMail: String): ServerServiceCall[NotUsed, Done] =
    authorizedString(token => s"user:${token.username}:${token.userId}:activation") { (token, _) =>
      this._sendMail(sendMail)
    }

  override def _sendMail(sendMail: String): ServerServiceCall[NotUsed, Done] =
    ServerServiceCall {
      _ =>
        authRepository.getUserSafe(sendMail).flatMap(user => _sendMail(user))
    }

  override def changeMail(): ServiceCall[EmailChange, Done] =
    authorizedString(token => s"user:${token.team}:${token.userId}:${token.userId}:email:write") { (token, _) =>
      ServerServiceCall { change =>
        authRepository.getUserByMail(change.email).flatMap {
          case Some(_) => throw new ServiceException(EmailInUse)
          case None =>
            val user = refFor[UserEntity](token.userId)

            user.ask(SetEmail(change.email)).map {
              _: EmailSet => Done
            }
        }
      }
    }

  def _sendMail(user: api.User): Future[Done] = {
    logger.warn(s"refreshing activation token for user $user")
    refFor[UserEntity](user.uid).ask(RefreshActivationToken).map {
      token =>
        Done
    }
  }

  def _requestPasswordReset(user: api.User): Future[GeneralToken] =
    refFor[UserEntity](user.uid).ask(RequestPasswordReset) // email will be send through the notification service

  override def requestPasswordReset(emailOrName: String): ServiceCall[NotUsed, Done] =
    ServiceCall {
      _ =>
        KamonUtils.span(s"request password reset for ${emailOrName}") {
          KamonUtils.safe {
            authRepository.getUserSafe(emailOrName).flatMap(user => _requestPasswordReset(user))
          }.map(_ => Done)
        }
    }

  def _resetPassword(user: api.User, reset: api.ResetPassword): Future[Done] = {
    val hash = hashPassword(reset.password.toCharArray)
    refFor[UserEntity](user.uid).ask(userentities.ResetPassword(user.uid, reset.token, hash))
  }

  def _changePassword(user: api.User, change: ChangePassword): Future[Done] = {
    val userEntity = refFor[UserEntity](user.uid)
    userEntity.ask(GetUser).map(_.response).flatMap {
      case None => throw new ServiceException(InternalServiceError, s"User ${user.username} is not available")
      case Some(_u) =>
        if (_u.hash.isDefined) {
          val hashed = hashPassword(change.oldPassword.toCharArray, _u.hash.get.salt)

          if (HashUtils.hashEquals(hashed, _u.hash.get)) {
            val newPassword = HashUtils.hashPassword(change.newPassword.toCharArray)
            userEntity.ask(SetPassword(newPassword))
          } else {
            throw Forbidden(s"Wrong Password for user $user")
          }
        } else {
          throw Forbidden(s"User $user has no Password. Please request a password reset instead")
        }
    }
  }

  override def changePassword(): ServiceCall[ChangePassword, Done] =
    authorizedString(token => s"user:${token.team}:${token.userId}:${token.userId}:password:write") { (token, c) =>
      ServerServiceCall { change =>
        authRepository.getUser(token.username).flatMap {
          case Some(user) => _changePassword(user, change)
          case None => throw new ServiceException(InternalServiceError, s"User ${token.username} is not available")
        }
      }
    }

  override def resetPassword(emailOrName: String): ServerServiceCall[api.ResetPassword, Done] =
    ServerServiceCall {
      reset =>
        authRepository.getUserSafe(emailOrName).flatMap(user => _resetPassword(user, reset))
    }

  def _deleteUser(user: api.User, lgn: Login): Future[Done] = {
    val userEntity = refFor[UserEntity](user.uid)
    userEntity.ask(GetUser).map(_.response).flatMap {
      case None => throw new ServiceException(InternalServiceError, s"User ${user.username} is not available")
      case Some(_u) =>
        if (_u.hash.isDefined) {
          if (HashUtils.checkPassword(lgn.password, _u.hash.get)) {
            userEntity.ask(DeleteUser(_u.uid)).map(_ => Done)
          } else {
            throw Forbidden(s"Wrong Password for user $user")
          }
        } else {
          throw Forbidden(s"Delete prevented for user $user")
        }
    }
  }

  def _deleteUserByID(user: UUID): Future[Done] = {
    val userEntity = refFor[UserEntity](user)
    userEntity.ask(GetUser).map(_.response).flatMap {
      case None => throw new ServiceException(InternalServiceError, s"User is not available")
      case Some(_u) =>
        userEntity.ask(DeleteUser(_u.uid)).map(_ => Done)
    }
  }

  override def deleteUser(): ServiceCall[Login, Done] =
    authorizedString(token => s"user:${token.team}:${token.userId}:${token.userId}:user:delete") { (token, c) =>
      ServerServiceCall { lgn =>
        authRepository.getUser(token.username).flatMap {
          case Some(user) => _deleteUser(user, lgn)
          case None => throw new ServiceException(InternalServiceError, s"User ${token.username} is not available")
        }
      }
    }

  override def deleteSpecificUser(id: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token => s"user:${token.team}:*:*:user:delete") { (token, c) =>
      ServerServiceCall { lgn =>
        _deleteUserByID(id)
      }
    }

  override def getUserInfo: ServiceCall[NotUsed, SessionInfo] =
    authenticatedDirect { (token, y) =>
      ServerServiceCall { _ =>
        authRepository.getUserById(token.userId).map {
          case Some(u) => SessionInfo(
              user = u,
              team = token.team
            )
          case None => throw NotFound(s"user not found")
        }
      }
    }

  override def _getUserInfo(id: UUID): ServiceCall[NotUsed, api.User] =
    ServiceCall { _ =>
      authRepository.getUserById(id).map {
        case Some(u) => u
        case None    => throw NotFound(s"user $id not found")
      }
    }

  override def getUsersOfTeams(team: String): ServiceCall[NotUsed, Seq[api.User]] =
    authorizedString { token =>
      s"user:${team}:${team}:*:*:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        authRepository.getUsers(team)
      }
    }

  override def getAllUsers: ServiceCall[NotUsed, Seq[api.User]] =
    authorizedString { token =>
      s"user:*:*:*:*:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        authRepository.getAllUsers
      }
    }

  override def getTeams(domain: Option[String]): ServiceCall[NotUsed, Seq[Teams.Team]] =
    authorizedString { token =>
      s"team:*:*:*:*:read"
    } { (token, _) =>
      ServerServiceCall { x =>
        domain match {
          case None    => teamRepository.getTeams()
          case Some(d) => teamRepository.getTeamByDomain(d).map(_.toSeq)
        }
      }
    }

  override def _getTeam(domain: String): ServiceCall[NotUsed, Team] =
    ServerServiceCall { x =>
      registry.refFor[TeamEntity](domain).ask(GetTeam).map(_.response).map {
        case Some(team) => team
        case None =>
          teamRepository.getDefaultTeams().find(_.domain == domain)
            .getOrElse(throw new TransportException(TransportErrorCode.NotFound, s"team ${domain} not found"))

      }
    }

  override def createTeam(): ServiceCall[Teams.Team, Done] =
    authorizedString(token =>
      s"team:*:*:*:*:create"
    ) { (token, _) =>
      ServerServiceCall { x =>
        for {
          t <- registry.refFor[TeamEntity](x.domain).ask(CreateTeam(x))
        } yield Done

      }
    }

  override def updateTeam(id: String): ServiceCall[Teams.Team, Done] =
    authorizedString { token =>
      s"team:$id:$id:*:*:update"
    } { (token, _) =>
      ServerServiceCall { x =>
        registry.refFor[TeamEntity](x.domain).ask(UpdateTeam(x))
      }
    }

  override def deleteTeam(id: String): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"team:$id:$id:*:*:update"
    } { (token, _) =>
      ServerServiceCall { x =>
        val entity = registry.refFor[TeamEntity](id)
        entity.ask(GetTeam).map(_.response).flatMap {
          case Some(t) => entity.ask(UpdateTeam(t.copy(deactivated = true)))
          case None    => throw new TransportException(TransportErrorCode.NotFound, s"Team $id not found")
        }
      }
    }

  override def createTemplate(): ServiceCall[TemplateAPI.PrintTemplateAPI, TemplateAPI.PrintTemplateAPI] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:*:*:update"
    } { (token, _) =>
      ServerServiceCall { tmplt =>
        val id          = UUIDUtils.createShort()
        val templateRef = registry.refFor[PrintTemplateEntity](id)

        templates.getTemplatesByCategoryAndName(token.team, tmplt.category, tmplt.name).flatMap { tmplts =>
          if (tmplts.isEmpty) {
            templateRef.ask(SetTemplate(
              id,
              tmplt.copy(
                id = Some(id),
                team = Some(token.team)
              )
            )).map(_.toApi)
          } else {
            throw new TransportException(TransportErrorCode.BadRequest, "Template exists")
          }
        }

      }
    }

  override def updateTemplate(id: String): ServiceCall[TemplateAPI.PrintTemplateAPI, TemplateAPI.PrintTemplateAPI] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:$id:*:update"
    } { (token, _) =>
      ServerServiceCall { tmplt =>
        val templateRef = registry.refFor[PrintTemplateEntity](id)

        templateRef.ask(GetTemplate(id)).flatMap { orig =>
          if (orig.name != tmplt.name || orig.category != tmplt.category) {
            templates.getTemplatesByCategoryAndName(token.team, tmplt.category, tmplt.name).flatMap { tmplts =>
              if (tmplts.isEmpty) {
                templateRef.ask(SetTemplate(
                  id,
                  tmplt.copy(
                    id = Some(id)
                  )
                )).map(_.toApi)
              } else {
                throw new TransportException(TransportErrorCode.BadRequest, "Template exists")
              }
            }
          } else {
            templateRef.ask(SetTemplate(
              id,
              tmplt.copy(
                id = Some(id)
              )
            )).map(_.toApi)
          }
        }

      }
    }

  override def getTemplate(id: String): ServiceCall[NotUsed, TemplateAPI.PrintTemplateAPI] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:$id:*:read"
    } { (token, _) =>
      ServerServiceCall { tmplt =>
        val templateRef = registry.refFor[PrintTemplateEntity](id)
        templateRef.ask(GetTemplate(id)).map(_.toApi)
      }
    }

  override def searchTemplates(
      category: Option[String],
      name: Option[String]
  ): ServiceCall[NotUsed, Seq[TemplateAPI.PrintTemplateAPI]] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:*:*:read"
    } { (token, _) =>
      ServerServiceCall { tmplt =>
        if (category.isDefined) {
          if (name.isDefined) {
            templates.getTemplatesByCategoryAndName(token.team, category.get, name.get).map(_.map(_.toApi))
          } else {
            templates.getTemplatesByCategory(token.team, category.get).map(_.map(_.toApi))
          }
        } else {
          templates.getTemplates(token.team).map(_.map(_.toApi))
        }
      }
    }

  override def deleteTemplate(id: String): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:$id:*:write"
    } { (token, _) =>
      ServerServiceCall { tmplt =>
        val templateRef = registry.refFor[PrintTemplateEntity](id)
        templateRef.ask(DeleteTemplate(id))
      }
    }

  def pdfInitializer(tmplt: PrintTemplate) =
    new PDFGenerator()
      .withDebug(true)
      .withTemplate(tmplt.content)
      .withHeader(tmplt.header)
      .withFooter(tmplt.footer)
      .withWorkingDirectory(Files.createTempDirectory("templating"))

  def htmlInitializer(tmplt: PrintTemplate) =
    new HTMLGenerator()
      .withDebug(true)
      .withTemplate(tmplt.content)
      .withWorkingDirectory(Files.createTempDirectory("templating"))

  override def renderTemplate(id: String): ServiceCall[Map[String, JsValue], Array[Byte]] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:$id:*:read"
    } { (token, _) =>
      ServerServiceCall { (hdr, vars) =>
        _doRenderTemplate(token.team, id, RenderRequest(Seq(), vars), pdfInitializer).map { x =>
          inlinePDF(x)
        }
      }
    }

  def _doRenderTemplate[T](
      team: String,
      id: String,
      req: RenderRequest,
      _gen: PrintTemplate => TemplateGenerator[T]
  ): Future[T] = {

    val templateRef = registry.refFor[PrintTemplateEntity](id)
    templateRef.ask(GetTemplate(id)).flatMap { tmplt =>
      templates.getTemplates(tmplt.team).flatMap { ts =>
        val baseTemplates = ts.filter(t => t.category == tmplt.category || t.category == "global")

        var gen = _gen(tmplt).withDebug(true)
        baseTemplates.foreach { bt =>
          gen = gen.withImportableResource(bt.name, bt.content)
        }

        val resources =
          Option(UserServiceFiles.templateResourceFolder(team).toFile.listFiles(f => f.isFile)).getOrElse(Array()).map {
            f =>
              f.toPath
          }

        gen.withResources(
          (resources ++ req.resources.flatMap { s =>
            try
              Some(Paths.get(s))
            catch {
              case e: Exception =>
                e.printStackTrace()
                None
            }
          }).distinct
        )

        import de.fellows.utils.internal.FileReader.withResource
        withResource(getClass.getResourceAsStream("/templates/frappe-bootstrap.css")) { i =>
          gen.withResource("bootstrap.css", i)
        }

        req.variables.foreach { x =>
          gen = gen.withJsonVariable(x._1, x._2)
        }

        gen.withFilter(new FilePathFilter(conf.getString("fellows.storage.base")))

        gen.build() match {
          case Some(x) => Future.successful(x)
          case None    => Future.failed(new Exception("Failed to build template"))
        }
      }
    }
  }

  override def _renderTemplate(
      team: String,
      category: String,
      name: Option[String]
  ): ServiceCall[TemplateAPI.RenderRequest, Array[Byte]] =
    ServerServiceCall { (_, req) =>
      val foundTemplates = (name match {
        case Some(n) => templates.getTemplatesByCategoryAndName(team, category, n)
        case None    => templates.getTemplatesByCategory(team, category)
      })

      val res = foundTemplates.map(_.headOption).flatMap {
        case Some(t) =>
          _doRenderTemplate(team, t.id, req, pdfInitializer)
        case None => throw new TransportException(TransportErrorCode.NotFound, s"Template $category/$name not found")
      }

      res.map(inlinePDF)
    }

  override def _renderHtmlTemplate(
      team: String,
      category: String,
      name: Option[String]
  ): ServiceCall[TemplateAPI.RenderRequest, String] =
    ServerServiceCall { (req) =>
      val foundTemplates = (name match {
        case Some(n) => templates.getTemplatesByCategoryAndName(team, category, n)
        case None    => templates.getTemplatesByCategory(team, category)
      })

      val res = foundTemplates.map(_.headOption).flatMap {
        case Some(t) =>
          _doRenderTemplate(team, t.id, req, htmlInitializer)
        case None => throw new TransportException(TransportErrorCode.NotFound, s"Template $category/$name not found")
      }

      res
    }

  override def getResources(): ServiceCall[NotUsed, Seq[String]] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:*:resources:read"
    } { (token, _) =>
      ServerServiceCall { vars =>
        Future.successful(UserServiceFiles.templateResourceFolder(token.team).toFile.listFiles().map(_.getName))
      }
    }

  override def deleteResource(name: String): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"template:${token.team}:${token.team}:*:resources:write"
    } { (token, _) =>
      ServerServiceCall { vars =>
        val f = UserServiceFiles.createFilePath(token.team, name).toJavaFile
        if (f.exists()) {
          f.delete()
        } else {
          throw new TransportException(TransportErrorCode.NotFound, s"file $name not found")
        }

        Future.successful(Done)
      }
    }

}
