package de.fellows.app.user.impl

import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server._
import com.softwaremill.macwire._
import de.fellows.app.notification.common.NotificationSerializerRegistry
import de.fellows.app.profile.api.ProfileService
import de.fellows.app.security.SecurityService
import de.fellows.app.user.api.UserService
import de.fellows.app.user.impl.entities.settings.UserTeamSettingsEntity
import de.fellows.app.user.impl.entities.teams.{TeamEntity, TeamSerializerRegistry}
import de.fellows.app.user.impl.entities.templates.{PrintTemplateEntity, PrintTemplateSerializerRegistry}
import de.fellows.app.user.impl.entities.{UserEntity, UserSerializerRegistry}
import de.fellows.app.user.impl.read.template.{TemplateEventProcessor, TemplateRepository}
import de.fellows.app.user.impl.read.{TeamEventProcessor, TeamRepository}
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.mailgun.MailgunService
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents
import de.fellows.utils.health.HealthCheckComponents

class UserServiceLoader extends LagomApplicationLoader {

  override def loadDevMode(context: LagomApplicationContext) =
    new UserServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new UserServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[UserService])
}

abstract class UserServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {

  lazy val files: UserServiceFiles = wire[UserServiceFiles]

  lazy implicit val service: ServiceDefinition = ServiceDefinition("user")

  override lazy val lagomServer = serverFor[UserService](wire[UserServiceImpl])
    .additionalRouter(files.router)

  override lazy val jsonSerializerRegistry =
    UserSerializerRegistry ++ NotificationSerializerRegistry ++ TeamSerializerRegistry ++ PrintTemplateSerializerRegistry
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  lazy val securityService = serviceClient.implement[SecurityService]
  lazy val profileService  = serviceClient.implement[ProfileService]

  lazy val authRepository     = wire[AuthRepository]
  lazy val teamRepository     = wire[TeamRepository]
  lazy val templateRepository = wire[TemplateRepository]

  lazy val mailgun = serviceClient.implement[MailgunService]

  readSide.register(wire[UserEventProcessor])
  readSide.register(wire[TeamEventProcessor])
  readSide.register(wire[TemplateEventProcessor])

  persistentEntityRegistry.register(wire[UserEntity])
  persistentEntityRegistry.register(wire[TeamEntity])
  persistentEntityRegistry.register(wire[PrintTemplateEntity])
  persistentEntityRegistry.register(wire[UserTeamSettingsEntity])
}
