package de.fellows.app.user.impl.entities.teams

import com.lightbend.lagom.scaladsl.playjson.{ JsonSerializer, JsonSerializerRegistry }
import de.fellows.app.user.api.Teams.Team
import de.fellows.app.user.impl.entities.teams.Commands.{ CreateTeam, DeleteTeam, GetTeam, TeamResponse, UpdateTeam }
import de.fellows.app.user.impl.entities.teams.Events.{ TeamCreated, TeamDeleted, TeamUpdated }

object TeamSerializerRegistry extends JsonSerializerRegistry {
  override def serializers =
    List(
      JsonSerializer[Team],
      JsonSerializer[TeamResponse],
      JsonSerializer[GetTeam.type],
      JsonSerializer[CreateTeam],
      JsonSerializer[TeamCreated],
      JsonSerializer[UpdateTeam],
      JsonSerializer[TeamUpdated],
      JsonSerializer[DeleteTeam],
      JsonSerializer[TeamDeleted]
    )

}
