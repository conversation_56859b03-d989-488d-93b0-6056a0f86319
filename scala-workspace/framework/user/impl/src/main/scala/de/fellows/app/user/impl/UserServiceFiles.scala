package de.fellows.app.user.impl

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.typesafe.config.ConfigFactory
import de.fellows.app.security.SecurityBodyParser
import de.fellows.utils.FilePath
import de.fellows.utils.playutils.FileHelper
import de.fellows.utils.security.GenericTokenContent
import play.api.http.FileMimeTypes
import play.api.mvc._
import play.api.routing.Router
import play.api.routing.sird._
import play.core.parsers.Multipart.{FileInfo, FilePartHandler}

import java.nio.file.Path
import scala.concurrent.ExecutionContext

class UserServiceFiles(
                        action: DefaultAction<PERSON>uilder,
                        parser: PlayBodyParsers,
                        registry: PersistentEntityRegistry,
                        mime: FileMimeTypes,
                        exCtx: ExecutionContext
                      ) {
  implicit val x: ExecutionContext = exCtx
  implicit val m: FileMimeTypes    = mime;

  private val filesize: Long = ********

  def uploadPanelResource(): Handler = {
    var tkncnt: Option[GenericTokenContent] = None

    action(
      SecurityBodyParser(token => s"template:${token.getTeam}:${token.getTeam}:*:resources:write") { token =>
        tkncnt = Some(token)
        parser.multipartFormData(imageHandler(token, token.getTeam), filesize)
      }
    ) {
      request =>
        Results.Ok("")
    }
  }

  def imageHandler(token: GenericTokenContent, team: String): FilePartHandler[FilePath] = {
    case FileInfo(partName, fileName, contentType, s) =>
      contentType match {
        case _ =>
          val path = UserServiceFiles.createFilePath(team, fileName)
          FileHelper.receiveFile(token.getTeam, path, partName, fileName, contentType)
      }
  }

  // @formatter:off
  def router: Router = {
    println("create route")
    Router.from{
      case POST(p"/files/user/templateresources") =>
        uploadPanelResource()

    }
  }
  // @formatter:on
}

object UserServiceFiles {
  lazy val conf = ConfigFactory.load()
  private val path_base = "images"

  val basePath: String =
    conf.getString("fellows.storage.service")

  // FilePath(basePath, team, version, s"$p/$path_base", fileName)

  def createFilePath(team: String, fileName: String): FilePath =
    FilePath(basePath, team, "templates", s"resources", None, fileName)

  def templateResourceFolder(team: String): Path =
    FilePath(basePath, team, "templates", s"resources", None, "x").toJavaFile.getParentFile.toPath
}
