package de.fellows.app.user.impl.read

import akka.stream.Materializer
import com.datastax.driver.core.Row
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import com.typesafe.config.ConfigFactory
import de.fellows.app.user.api.Teams.Team
import de.fellows.utils.communication.ServiceDefinition

import scala.concurrent.{ExecutionContext, Future}

class TeamRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {

  val c          = ConfigFactory.load()
  val teamconfig = c.getConfig("fellows.user.teams")

  def toTeam(r: Row): Team =
    Team(
      r.getString("teamDomain"),
      r.getString("teamName"),
      r.getString("description")
    )

  import scala.jdk.CollectionConverters._

  def getDefaultTeams() = {
    val cf  = teamconfig.getObject("default")
    val cfg = teamconfig.getConfig("default")
    cf.keySet().asScala.map { k =>
      val thisTeam = cfg.getConfig(k)
      Team(thisTeam.getString("domain"), thisTeam.getString("name"), thisTeam.getString("description"))
    }
  }

  // language=SQL
  def getTeams(): Future[Seq[Team]] =
    session.selectAll("SELECT * FROM teams").map(_.map(toTeam)).map(_ ++ getDefaultTeams())

  // language=SQL
  def getTeamByDomain(domain: String): Future[Option[Team]] =
    session.selectOne("SELECT * FROM teams WHERE teamDomain = ?", domain)
      .map(_.map(toTeam))
      .map(_.orElse(getDefaultTeams()
        .find(_.domain == domain)))

  // language=SQL
  def getTeamsByName(name: String): Future[Seq[Team]] =
    session.selectAll("SELECT * FROM teams WHERE teamName = ?", name).map(_.map(toTeam)).map(_ ++ getDefaultTeams())

}
