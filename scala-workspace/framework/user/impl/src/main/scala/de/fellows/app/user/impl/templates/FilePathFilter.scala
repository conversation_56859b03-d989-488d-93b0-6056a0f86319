package de.fellows.app.user.impl.templates

import com.hubspot.jinjava.interpret.{InvalidInputException, JinjavaInterpreter}
import com.hubspot.jinjava.lib.filter.Filter
import com.hubspot.jinjava.objects.collections.PyMap
import de.fellows.utils.FilePath
import play.api.Logging

class FilePathFilter(storageBase: String) extends Filter with Logging {

  override def filter(o: Any, interpreter: JinjavaInterpreter, args: String*): AnyRef = {

    logger.warn(s"try to parse filepath ${o.getClass}: ${o}")
    val r =
      (if (o.isInstanceOf[PyMap]) {
         val map      = o.asInstanceOf[PyMap]
         val fsroot   = map.get("fsroot")
         val team     = map.get("team")
         val resource = map.get("resource")
         val base     = map.get("base")
         val subPath  = Option(map.get("subPath"))
         val filename = map.get("filename")

         if (
           fsroot.isInstanceOf[String] &&
           team.isInstanceOf[String] &&
           resource.isInstanceOf[String] &&
           base.isInstanceOf[String] &&
           filename.isInstanceOf[String]
         ) {
           Some(new FilePath(
             fsroot.asInstanceOf[String],
             team.asInstanceOf[String],
             resource.asInstanceOf[String],
             base.asInstanceOf[String],
             subPath.map(_.asInstanceOf[Seq[String]]), // TODO: check if this is correct
             filename.asInstanceOf[String] match {
               case x if x.endsWith(".webp") => x.substring(0, x.length - 4) + "png"
               case x                        => x
             }
           ).toPath)
         } else {
           None
         }
       } else if (o.isInstanceOf[FilePath]) {
         val fp = o.asInstanceOf[FilePath]
         if (fp.filename.endsWith(".webp")) {
           Some(fp.copy(filename = fp.filename.substring(0, fp.filename.length - 4) + "png").toPath)

         } else {
           Some(fp.toPath)
         }
       } else {
         throw new InvalidInputException(
           interpreter,
           this.getName,
           "Invalid input"
         )
       })

    r.getOrElse("")
  }

  override def getName: String = "filepath"
}
