package de.fellows.app.user.impl.entities.templates

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.user.api.TemplateAPI.PrintTemplateAPI
import play.api.libs.json.{Format, Json}

case class PrintTemplate(
    id: String,
    team: String,
    category: String,
    name: String,
    content: String,
    header: Option[String],
    footer: Option[String]
) {
  def toApi: PrintTemplateAPI =
    PrintTemplateAPI(
      id = Some(id),
      team = Some(team),
      category = category,
      name = name,
      content = content,
      header = header,
      footer = footer
    )
}

sealed trait PrintTemplateEvent extends AggregateEvent[PrintTemplateEvent] {
  override def aggregateTag: AggregateEventShards[PrintTemplateEvent] = PrintTemplateEvent.Tag
}

object PrintTemplateEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[PrintTemplateEvent](NumShards)
}

sealed trait PrintTemplateCommand

case class SetTemplate(id: String, template: PrintTemplateAPI) extends PrintTemplateCommand
    with ReplyType[PrintTemplate]

case class DeleteTemplate(id: String) extends PrintTemplateCommand with ReplyType[Done]

case class GetTemplate(id: String) extends PrintTemplateCommand with ReplyType[PrintTemplate]

case class TemplateChanged(id: String, oldTemplate: Option[PrintTemplate], newTemplate: Option[PrintTemplate])
    extends PrintTemplateEvent

object PrintTemplate {
  implicit val f: Format[PrintTemplate] = Json.format[PrintTemplate]
}

object SetTemplate {
  implicit val f: Format[SetTemplate] = Json.format[SetTemplate]
}

object DeleteTemplate {
  implicit val f: Format[DeleteTemplate] = Json.format[DeleteTemplate]
}

object GetTemplate {
  implicit val f: Format[GetTemplate] = Json.format[GetTemplate]
}

object TemplateChanged {
  implicit val f: Format[TemplateChanged] = Json.format[TemplateChanged]
}
