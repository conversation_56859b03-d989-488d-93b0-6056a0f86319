package de.fellows.app.user.impl

import com.lightbend.lagom.scaladsl.testkit.PersistentEntityTestDriver
import de.fellows.app.user.impl.entities.{ User, UserEvent }

object TestUtils {

  def printOutcome(outcome: PersistentEntityTestDriver.Outcome[UserEvent, Option[User]]) = {
    println(outcome.events)
    println(outcome.issues)
    println(outcome.sideEffects)
    println(outcome.state)
    println(outcome.replies)
  }
}
