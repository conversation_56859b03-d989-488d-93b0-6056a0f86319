package de.fellows.app.user.impl

import akka.Done
import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.playjson.JsonSerializerRegistry
import com.lightbend.lagom.scaladsl.testkit.PersistentEntityTestDriver
import com.lightbend.lagom.scaladsl.testkit.PersistentEntityTestDriver.UsingJavaSerializer
import de.fellows.app.user.impl.entities._
import de.fellows.utils
import de.fellows.utils.HashUtils
import de.fellows.utils.communication.ServiceException
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{BeforeAndAfterAll, Inside}

import java.util.UUID

class UserActivationSpec extends AnyWordSpec with Matchers with BeforeAndAfterAll with Inside {
  private val system =
    ActorSystem("UserActivationSpec", JsonSerializerRegistry.actorSystemSetupFor(UserSerializerRegistry))

  private val userId = UUID.randomUUID

  val fakeResource = UUID.randomUUID()

  private def withUser(block: PersistentEntityTestDriver[UserCommand, UserEvent, Option[User]] => Unit): Unit = {
    val driver = new PersistentEntityTestDriver(system, new UserEntity, userId.toString)
    block(driver)
    utils.TestUtils.checkIssues(driver)

  }

  "User" should {

    "allow activation" in withUser { user =>
      val hash    = HashUtils.hashPassword("test".toCharArray)
      var outcome = user.run(CreateUser(Some("<EMAIL>"), Seq("testteam"), "test", Some(hash), userId, false))
      val userObj = outcome.replies.head.asInstanceOf[User]
      outcome = user.run(Activate(userId, outcome.state.get.activationToken.get.token))

      checkNoIssues(outcome)

      outcome.events.size shouldBe 1
      outcome.sideEffects.size shouldBe 1

      outcome.events.head shouldBe Activated(Some(userObj), userId)
      outcome.replies.head shouldBe Done

    }

    "allow refresh" in withUser { user =>
      val hash     = HashUtils.hashPassword("test".toCharArray)
      var outcome  = user.run(CreateUser(Some("<EMAIL>"), Seq("testteam"), "test", Some(hash), userId, false))
      val userObj  = outcome.replies.head.asInstanceOf[User]
      val oldToken = outcome.state.get.activationToken.get.token
      outcome = user.run(RefreshActivationToken)

      outcome.replies.size shouldBe 1
      outcome.replies.head shouldBe a[GeneralToken]

      val newToken = outcome.replies.head.asInstanceOf[GeneralToken]

      assert(oldToken != newToken.token)

      outcome = user.run(Activate(userId, newToken.token))

      checkNoIssues(outcome)

      outcome.events.size shouldBe 1
      outcome.sideEffects.size shouldBe 1

      val expectedUser = userObj.copy(activationToken = Some(newToken))

      outcome.events.head shouldBe Activated(Some(expectedUser), userId)
      outcome.replies.head shouldBe Done
    }

    "forbid activation with old token" in withUser { user =>
      val hash    = HashUtils.hashPassword("test".toCharArray)
      var outcome = user.run(CreateUser(Some("<EMAIL>"), Seq("testteam"), "test", Some(hash), userId, false))

      val oldToken = outcome.state.get.activationToken.get.token
      outcome = user.run(RefreshActivationToken)

      outcome.replies.size shouldBe 1
      outcome.replies.head shouldBe a[GeneralToken]

      val newToken = outcome.replies.head.asInstanceOf[GeneralToken].token

      assert(oldToken != newToken)

      outcome = user.run(Activate(userId, oldToken))

      checkNoIssues(outcome)

      outcome.events.size shouldBe 0
      outcome.sideEffects.size shouldBe 1
      outcome.replies.size shouldBe 1

      outcome.replies.head shouldBe a[ServiceException]
      outcome.replies.head.asInstanceOf[ServiceException].ecode shouldBe WrongToken
    }

    "forbid double activation" in withUser { user =>
      val hash    = HashUtils.hashPassword("test".toCharArray)
      var outcome = user.run(CreateUser(Some("<EMAIL>"), Seq("testteam"), "test", Some(hash), userId, false))

      val token = outcome.state.get.activationToken.get.token
      outcome = user.run(Activate(userId, token))
      outcome = user.run(Activate(userId, token))

      checkNoIssues(outcome)

      outcome.events.size shouldBe 0
      outcome.sideEffects.size shouldBe 1

      outcome.replies.head shouldBe a[ServiceException]
      outcome.replies.head.asInstanceOf[ServiceException].ecode shouldBe UserAlreadyActivated

    //      outcome.replies.head shouldBe Done

    }

  }

  private def checkNoIssues(outcome: PersistentEntityTestDriver.Outcome[UserEvent, Option[User]]) =
    if (outcome.issues.size == 1) {
      outcome.issues.head shouldBe a[UsingJavaSerializer]
      outcome.issues.head.asInstanceOf[UsingJavaSerializer].obj shouldBe a[ServiceException]
    } else {
      outcome.issues.size shouldBe 0
    }

}
