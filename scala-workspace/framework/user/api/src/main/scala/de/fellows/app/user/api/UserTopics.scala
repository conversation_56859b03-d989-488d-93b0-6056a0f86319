package de.fellows.app.user.api

import de.fellows.app.user.api.Teams.Team
import de.fellows.utils.streams.PayloadName
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

sealed trait UserMessage

sealed trait UserStateMessage

case class ActivationTokenChanged(user: User, activationToken: UUID) extends UserStateMessage

case class PasswordResetRequested(user: User, resetToken: UUID) extends UserStateMessage

object ActivationTokenChanged {
  implicit val f: Format[ActivationTokenChanged] = Json.format[ActivationTokenChanged]
}

object PasswordResetRequested {
  implicit val f: Format[PasswordResetRequested] = Json.format[PasswordResetRequested]
}

object UserStateMessage {
  implicit val f: Format[UserStateMessage] = Json.format[UserStateMessage]
}

case class UserCreatedMessage(
    team: Seq[String],
    username: String,
    id: UUID,
    email: Option[String],
    technical: Boolean
) extends UserMessage

object UserCreatedMessage {
  implicit val f: Format[UserCreatedMessage] = Json.format[UserCreatedMessage]
}

case class UserDeletedMessage(
    team: Seq[String],
    username: String,
    id: UUID,
    email: Option[String],
    technical: Boolean
) extends UserMessage

object UserDeletedMessage {
  implicit val f: Format[UserDeletedMessage] = Json.format[UserDeletedMessage]
}

case class PasswordResetMessage(
    id: UUID
) extends UserMessage

object PasswordResetMessage {
  implicit val f: Format[PasswordResetMessage] = Json.format[PasswordResetMessage]
}

case class PasswordSetMessage(
    id: UUID
) extends UserMessage

object PasswordSetMessage {
  implicit val f: Format[PasswordSetMessage] = Json.format[PasswordSetMessage]
}

case class EmailSetMessage(
    id: UUID,
    oldMail: Option[String],
    newMail: String
) extends UserMessage

object EmailSetMessage {
  implicit val f: Format[EmailSetMessage] = Json.format[EmailSetMessage]
}

case class TeamsChangedMessage(
    id: UUID,
    oldTeams: Seq[String],
    newTeams: Seq[String]
) extends UserMessage

object TeamsChangedMessage {
  implicit val f: Format[TeamsChangedMessage] = Json.format[TeamsChangedMessage]
}

case class ActivatedMessage(
    id: UUID
) extends UserMessage

object ActivatedMessage {
  implicit val f: Format[ActivatedMessage] = Json.format[ActivatedMessage]
}

case class ApiTokenAddedMessage(
    id: UUID
) extends UserMessage

object ApiTokenAddedMessage {
  implicit val f: Format[ApiTokenAddedMessage] = Json.format[ApiTokenAddedMessage]
}

case class ResetTokenCreatedMessage(
    user: User,
    token: UUID,
    created: Instant
) extends UserMessage

object ResetTokenCreatedMessage {
  implicit val f: Format[ResetTokenCreatedMessage] = Json.format[ResetTokenCreatedMessage]
}

case class TokenRefreshedMessage(user: User, token: UUID, created: Instant) extends UserMessage

object TokenRefreshedMessage {
  implicit val f: Format[TokenRefreshedMessage] = Json.format[TokenRefreshedMessage]
}

case class TeamMessage(
    team: Team,
    change: String
)

object UserMessage {
  implicit val f: Format[UserMessage] = Json.format[UserMessage]
}

object TeamMessage {
  val CREATED = "created"
  val UPDATED = "updated"
  val DELETED = "deleted"

  implicit def naming: PayloadName[TeamMessage] = _.toString
  implicit val f: Format[TeamMessage]           = Json.format[TeamMessage]
}
