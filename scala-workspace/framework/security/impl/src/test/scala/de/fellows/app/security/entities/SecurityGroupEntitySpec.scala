package de.fellows.app.security.entities

import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.playjson.JsonSerializerRegistry
import com.lightbend.lagom.scaladsl.testkit.PersistentEntityTestDriver
import de.fellows.app.security.SecurityApi.Group
import de.fellows.app.security.entities.group._
import de.fellows.app.security.entities.user._
import de.fellows.utils.security.Permission
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{BeforeAndAfterAll, Inside}

import java.util.UUID

class SecurityGroupEntitySpec extends AnyWordSpec with Matchers with BeforeAndAfterAll with Inside {
  private val system = ActorSystem(
    "SecurityGroupEntitySpec",
    JsonSerializerRegistry.actorSystemSetupFor(SecurityUserSerializerRegistry ++ SecurityGroupSerializerRegistry)
  )

  private val groupId = UUID.randomUUID

  val fakeResource = UUID.randomUUID().toString

  val newPerm = Permission(
    Seq("profile"),
    team = Seq("team"),
    owner = Seq("owner"),
    resource = Seq(fakeResource),
    attribute = Seq("attr"),
    action = Seq("action")
  )
  val newPerm2 = Permission(
    Seq("profile2"),
    team = Seq("team2"),
    owner = Seq("owner2"),
    resource = Seq(fakeResource),
    attribute = Seq("attr2"),
    action = Seq("action2")
  )

  private def withGroup(block: PersistentEntityTestDriver[
    SecurityGroupCommand,
    SecurityGroupEvent,
    Option[Group]
  ] => Unit): Unit = {
    val driver = new PersistentEntityTestDriver(system, new GroupEntity, groupId.toString)
    block(driver)
    if (driver.getAllIssues.nonEmpty) {
      driver.getAllIssues.foreach(println)
      fail("There were issues: " + driver.getAllIssues.head)
    }
  }

  "Group" should {
    "allow creation" in withGroup { group =>
      val name    = "TestGroup"
      val outcome = group.run(CreateSecurityGroup(groupId, name, None))

      outcome.events should contain only SecurityGroupCreated(groupId, name, Seq())
      outcome.state should ===(Some(Group(groupId, name, Seq())))
    }

    "allow granting single permission" in withGroup { group =>
      val name = "TestGroup"
      group.run(CreateSecurityGroup(groupId, name, None))

      val outcome = group.run(GrantGroupPermissions(Seq(newPerm)))

      outcome.events should contain only GroupPermissionsUpdated(name, Seq(newPerm))
      outcome.state.get.permissions should ===(Seq(newPerm))
    }

    "allow granting multiple permissions" in withGroup { group =>
      val name = "TestGroup"
      group.run(CreateSecurityGroup(groupId, name, None))

      val outcome = group.run(GrantGroupPermissions(Seq(newPerm, newPerm2)))

      outcome.events should contain only GroupPermissionsUpdated(name, Seq(newPerm, newPerm2))
      outcome.state.get.permissions should ===(Seq(newPerm, newPerm2))
    }

    "allow revoking of single permissions" in withGroup { group =>
      val name = "TestGroup"
      group.run(CreateSecurityGroup(groupId, name, None))

      group.run(GrantGroupPermissions(Seq(newPerm, newPerm2)))

      var outcome = group.run(RevokeGroupPermissions(Seq(newPerm2)))
      outcome.events should contain only GroupPermissionsUpdated(name, Seq(newPerm))
      outcome.state.get.permissions should ===(Seq(newPerm))

      outcome = group.run(RevokeGroupPermissions(Seq(newPerm)))
      outcome.events should contain only GroupPermissionsUpdated(name, Seq())
      outcome.state.get.permissions should ===(Seq())

    }

    "allow revoking of multiple permissions" in withGroup { group =>
      val name = "TestGroup"
      group.run(CreateSecurityGroup(groupId, name, None))

      group.run(GrantGroupPermissions(Seq(newPerm, newPerm2)))

      var outcome = group.run(RevokeGroupPermissions(Seq(newPerm2, newPerm)))

      outcome.events should contain only GroupPermissionsUpdated(name, Seq())
      outcome.state.get.permissions should ===(Seq())

    }

    "forbid revoking of non-granted permissions" in withGroup { group =>
      val name = "TestGroup"
      group.run(CreateSecurityGroup(groupId, name, None))

      group.run(GrantGroupPermissions(Seq(newPerm2)))

      var outcome = group.run(RevokeGroupPermissions(Seq(newPerm)))

      outcome.replies.head shouldBe a[InvalidCommandException]
      outcome.events should equal(Seq())
      outcome.state.get.permissions should ===(Seq(newPerm2))
    }

    "forbid granting of already granted permissions" in withGroup { group =>
      val name = "TestGroup"
      group.run(CreateSecurityGroup(groupId, name, None))

      group.run(GrantGroupPermissions(Seq(newPerm)))

      var outcome = group.run(GrantGroupPermissions(Seq(newPerm)))

      outcome.replies.head shouldBe a[InvalidCommandException]
      outcome.events should equal(Seq())
      outcome.state.get.permissions should ===(Seq(newPerm))
    }

  }
}
