apiVersion: "apps/v1beta2"
kind: Deployment
metadata:
  name: "security-test"
  labels:
    group: fellows
    log: logback
    app: security
    appNameVersion: "security-test"
    "akka.lightbend.com/service-name": security
spec:
  replicas: 2
  selector:
    matchLabels:
      appNameVersion: "security-test"
  template:
    metadata:
      labels:
        app: security
        group: fellows
        log: logback
        sha: "${GIT_SHA_FULL}"
        appNameVersion: "security-test"
        "akka.lightbend.com/service-name": security
    spec:
      restartPolicy: Always
      imagePullSecrets:
        - name: gitlab-auth
      containers:
        - name: security
          image: "jira.electronic-fellows.de:5000/app/backend/security-impl:latest"
          imagePullPolicy: Always
          env:
            - name: "REQUIRED_CONTACT_POINT_NR"
              value: "2"
            - name: "JAVA_OPTS"
              value: "-Dplay.crypto.secret=amazingsecret"
          ports:
            - containerPort: 9000
              name: http
            - containerPort: 2552
              name: remoting
            - containerPort: 8558
              name: management

          readinessProbe:
            httpGet:
              path: "/ready"
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
          livenessProbe:
            httpGet:
              path: "/alive"
              port: management
            periodSeconds: 10
            failureThreshold: 10
            initialDelaySeconds: 20
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: security
  name: security
spec:
  ports:
    - name: http
      port: 9000
      protocol: TCP
      targetPort: 9000
    - name: remoting
      port: 2552
      protocol: TCP
      targetPort: 2552
    - name: management
      port: 8558
      protocol: TCP
      targetPort: 8558
  selector:
    app: security
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: { }
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: security
spec:
  rules:
    - http:
        paths:
          - backend:
              serviceName: security
              servicePort: 9000
            path: /api/security
status:
  loadBalancer: { }

