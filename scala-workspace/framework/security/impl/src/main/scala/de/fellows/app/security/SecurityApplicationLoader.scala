package de.fellows.app.security

import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{LagomApplication, LagomApplicationContext, LagomApplicationLoader, LagomServerComponents}
import com.softwaremill.macwire.wire
import de.fellows.app.security.entities.group._
import de.fellows.app.security.entities.user.{SecurityUserSerializerRegistry, UserEntity}
import de.fellows.app.security.read.GroupRepository
import de.fellows.app.user.api.UserService
import de.fellows.utils.{CombinedServiceLocatorComponents, MirrorDHealthCheckComponents, MirrorDServiceLocatorComponents, StackrateApplication}
import de.fellows.utils.communication.ServiceDefinition
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class SecurityServiceLoader extends LagomApplicationLoader {

  override def loadDevMode(context: LagomApplicationContext) = {
    val app = new SecurityServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents
    app
  }

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new SecurityServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[SecurityService])
}

trait SecurityServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  lazy implicit val service: ServiceDefinition = ServiceDefinition("security")

  val groups = wire[GroupRepository]
  readSide.register(groups)

  override lazy val lagomServer            = serverFor[SecurityService](wire[SecurityServiceImpl])
  override lazy val jsonSerializerRegistry = SecurityUserSerializerRegistry ++ SecurityGroupSerializerRegistry

  lazy val securityRepository = wire[SecurityRepository]

  //  readSide.register(wire[SessionEventProcessor])

  persistentEntityRegistry.register(wire[UserEntity])
  persistentEntityRegistry.register(wire[GroupEntity])
}

abstract class SecurityServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with SecurityServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {

  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  lazy val userService = serviceClient.implement[UserService]
  val userEvents       = wire[UserEventsConsumer]

}
