include "main-application.conf"

play.application.loader = de.fellows.app.security.SecurityServiceLoader


user.cassandra.keyspace = ${fellows.persistence.rootKeyspace}security


cassandra-journal {
  keyspace = ${user.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${user.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${user.cassandra.keyspace}read
}

akka {

  management {
    cluster.bootstrap {
      contact-point-discovery {
        service-name = "security"
      }
    }
  }
}

# fellows.serviceconfig = ${fellows.services.security}

fellows.security {
  default-group = "default"
  systemtoken = "TKz2ieSCw2KEDc2gPsmqjS7Qp6x68CzTMXb2fkf3oAYxhnyrBgG9adUWtr3Hsg5oHoEsBcHgUumASApANResAcJQrbgRprnC4yAexfVY7GrSTvbdMMLZDgbJYggqHU2d"
  //  admins = [
  //    "cb82d6c6-9b6e-436f-90cb-71045389a550",
  //    "d64d1ac6-4fac-4ef3-9ddd-d39550d352a0",
  //    "61eb98c3-d970-4b38-8b14-2a85c321bf12",
  //    "9dd29809-9610-41b6-a30d-18686a05fa37",
  //    "811b5a98-7c43-4fff-a0e4-442ceab5c3e2",
  //    "fa93bffc-7370-49ea-80a0-8ef4bb20d726",
  //    "96c48ce8-7194-4a35-9451-6584d06a97be"
  //  ]

  //  initial = [
  //    {
  //      name = admin
  //      permissions = [
  //        "*" // grant all to admin
  //      ]
  //    },
  //    {
  //      name = default-group
  //
  //      //default user permissions in this form: resourceClass, team, owner, resource, attribute, action
  //      //team and owner will be bound to their respective values on login.
  //      permissions = [
  //        "profile,user,notification:_:_:*", // grant all permissions for general user-owned stuff
  //        "profile,user:_:*:*:*:read", // grant read permissions for other users in t he same team
  //        "assembly,pcb,renderer,quotation:_:*", // grant management of assemblies, PCBs and quotations for the whole team
  //        //"assembly,pcb,renderer:_:*", // grant management of assemblies and PCBs for the whole team
  //        "customer,pcbsupplier,inbox,layerstackusage,layerstackdefinition,library:_:*:*:*:read" // grant read access to various services
  //        "pricelist:_:*:*:*:read"
  //        //,,customer,pcbsupplier,inbox
  //        //"quotation:_:*:*:item,quotation,address:*",
  //      ]
  //    }
  //  ]
}
