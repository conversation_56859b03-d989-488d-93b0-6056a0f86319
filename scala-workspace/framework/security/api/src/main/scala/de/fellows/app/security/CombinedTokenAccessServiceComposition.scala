package de.fellows.app.security

import akka.actor.ActorSystem
import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.deser.MessageSerializer
import com.lightbend.lagom.scaladsl.api.transport.{Forbidden, RequestHeader, Unauthorized}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.security.SecurityApi.PermissionResponse
import de.fellows.utils.metrics.Metrics.metric
import de.fellows.utils.security.AuthenticationServiceComposition.{decode, extractFileTokenContent, extractTokenContent, extractTokenHeader, isAuthToken}
import de.fellows.utils.security.{Auth0TokenContent, GenericTokenContent, ServiceCallContext}
import play.api.libs.streams.Accumulator
import play.api.mvc.Result
import play.api.mvc.Results.{Unauthorized => PlayUnauthorized}

import scala.concurrent.ExecutionContext
import scala.util.{Success, Try}

object CombinedTokenAccessServiceComposition {

  def auth[Request, Response](
      permissionRequest: GenericTokenContent => String
  )(serviceCall: (GenericTokenContent, ServiceCallContext) => ServerServiceCall[Request, Response])(implicit
      e: ExecutionContext,
      ac: ActorSystem,
      f: MessageSerializer[Request, _]
  ): ServerServiceCall[Request, Response] =
    authorized((token: GenericTokenContent) => permissionRequest(token))(serviceCall)

  private def authorized[Request, Response](
      permissionRequest: GenericTokenContent => String
  )(serviceCall: (GenericTokenContent, ServiceCallContext) => ServerServiceCall[Request, Response])(implicit
      e: ExecutionContext,
      ac: ActorSystem,
      f: MessageSerializer[Request, _]
  ): ServerServiceCall[Request, Response] =
    authenticatedDirect { (a, b) =>
      ServerServiceCall.compose {
        createCall(a, b, permissionRequest)(serviceCall)
      }
    }

  def authenticatedDirect[Request, Response](serviceCall: (
      GenericTokenContent,
      ServiceCallContext
  ) => ServerServiceCall[Request, Response])(implicit ac: ActorSystem, f: MessageSerializer[Request, _]) =
    metric { ctx =>
      ServerServiceCall.compose { requestHeader =>
        val jwtTokenContent   = extractTokenContent(requestHeader).filter(tokenContent => isAuthToken(tokenContent))
        val auth0TokenContent = extractAuth0TokenContent(requestHeader)
        val fileTokenContent  = extractFileTokenContent(requestHeader)

        (jwtTokenContent, auth0TokenContent, fileTokenContent) match {
          case (Success(jwt), _, _) =>
            ctx.metricsTracker.setToken(jwt)
            serviceCall(jwt, ctx)

          case (_, Success(auth0), _) =>
            ctx.metricsTracker.setToken(auth0)
            serviceCall(auth0, ctx)

          case (_, _, Success(filetoken)) =>
            ctx.metricsTracker.setToken(filetoken)
            serviceCall(filetoken, ctx)

          case _ => throw Unauthorized("Invalid token")
        }
      }
    }

  def authenticatedPlay[T, A](header: play.api.mvc.RequestHeader)(call: GenericTokenContent => Accumulator[
    ByteString,
    Either[Result, A]
  ]): Accumulator[ByteString, Either[Result, A]] = {

    val jwtTokenContent   = extractTokenContent(header).filter(tokenContent => isAuthToken(tokenContent))
    val auth0TokenContent = extractAuth0TokenContent(header)
    val fileTokenContent  = extractFileTokenContent(header)

    (jwtTokenContent, auth0TokenContent, fileTokenContent) match {
      case (Success(jwt), _, _) =>
        call(jwt)
      case (_, Success(auth0), _) =>
        call(auth0)
      case (_, _, Success(filetoken)) =>
        call(filetoken)

      case _ => Accumulator.done(Left(PlayUnauthorized(s"Authorization token is invalid")))
    }
  }

  def extractAuth0TokenContent(header: RequestHeader): Try[Auth0TokenContent] =
    extractTokenHeader(header)
      .flatMap(rawToken => decode(rawToken).map(_.as[Auth0TokenContent]))

  def extractAuth0TokenContent(header: play.api.mvc.RequestHeader): Try[Auth0TokenContent] =
    extractTokenHeader(header).flatMap(decodeToken)

  /** Extract encoded structure from the encoded validation token
    */
  def decodeToken(token: String): Try[Auth0TokenContent] = decode(token).map(_.as[Auth0TokenContent])

  private def createCall[Request, Response](
      a: GenericTokenContent,
      b: ServiceCallContext,
      permissionRequest: GenericTokenContent => String
  )(serviceCall: (GenericTokenContent, ServiceCallContext) => ServerServiceCall[Request, Response])(implicit
      e: ExecutionContext
  ): RequestHeader => ServerServiceCall[Request, Response] = { h =>
    val request = permissionRequest(a)
    val pl      = AccessControl.check(request, a)

    pl match {
      case PermissionResponse(true) =>
        serviceCall(a, b)
      case PermissionResponse(false) => throw Forbidden("The User is not allowed to access this resource")
    }
  }
}
