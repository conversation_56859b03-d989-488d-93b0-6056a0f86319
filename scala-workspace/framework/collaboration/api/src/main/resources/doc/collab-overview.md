# Timeline

The Timeline is a series of events, in order of occurence. The client describes which events are relevant using the
TimelineParameters.

An Event in the timeline contains `reference`, `change`, `creator` and `time`

**The reference** describes the entity on which this change occured. It contains the id of the entity (`entity`) and and
the type of the entity (`entityType`)

## Reference

The `reference` describes the entity on which this change occured. It contains the id of the entity (`entity`) and and
the type of the entity (`entityType`). For instance, this reference points to an assembly:

```json
{
  "entity": "c9b8016e-2d4c-4170-9c32-65be3523d5fe",
  "entityType": "assembly"
}
```

This is a list of entity types currently in use:

| type     |  description  | notes |
|----------|:-------------:| ----- |
| assembly | The main Assembly ||
| pcb | The main PCB  | usually the version id
| pcbspecification | a specific specification | has its own id |
| customerpanel | a specific customerpanel| has its own id, but also publishes certain events under the version id|
| quotationitem | a specific quotation item | has its own id, but also publishes certain events under the assembly id|
| quotation | a specific quotation |has its own id, but also publishes certain events under the assembly id|
| layerstackusage | a used layerstack |uses the version id|
| dfmviolations | DFM violations |uses the version id|

### Usage notes

To get (for instance) the general timeline of an assembly, specifying the assembly-id and version-id is sufficient. To
get a more specific timeline for e.g. a specification, use the specification id

## Change

The change describes the actual change. It is split in 2 cases

1. **SpecificTimelineEvent**: contains a change with summary and description
2. **PredefinedTimelineEvent**: contains only ids for the summary and description. It is the clients obligation to apply
   any i18n on it and render it into a readable message

Both cases contain the following attributes:

1. **changeCategory**: a category for the change (used for filtering, often the same as the `entityType` of the
   reference)
2. **changeType**: a name for the type of change

You can find a list of the names of available change types [at the end of this document](#list-of-change-types)

The `SpecificTimelineEvent` contains a straightforward `summary` and `description`.

The `PredefinedTimelineEvent` instead contains `summaryIdentifier` and `descriptionIdentifier`. These fields contain
distinct ids for translation. for instance:
`i18n.timeline.customerpanel.customerpanelcreated.summary`
or `i18n.timeline.customerpanel.customerpanelcreated.description`

it also contains a `params` field, which is a string -> string map containing parameters for string interpolation,
depending on the event.

## Creator

The `creator` is also split into two cases:

1. `TimelineSystemUser`: contains just the `id` of the creating user. This id will
   be `00000000-0000-0000-0000-000000000000` for the system user
2. `TimelineGuest`: is used when the change is made by a guest using a share. it will contain the Share-ID `share`, and
   the user-id `grantingUser` of the user that authorized this share.

## User Info

The timeline object also contains a map `users`, thant maps from a user id to user info that contains the `name`
and `avatar`

## List of change types

**Change Type Assembly**

| change     |   notes |
|----------|:-------------:|
|DescriptionChanged| | 
|AssemblyCreated| | 
|AssemblyUIStatusChanged| | 
|FileMatchingApproved| | 
|AssemblyReleased| | 
|FileMatchingUnlocked| | 
|FileDeleted| | 
|ProjectAssigned| | 
|AssemblyStatusChanged| | 
|LifecycleUpdated| | 
|VersionCreated| | 
|VersionLifecycleUpdated| | 
|FileTypesUpdated| | 
|FilesAdded| | 
|NameChanged| | 
|CustomerAssigned| | 

**Change Type PCB**

| change     |   notes |
|----------|:-------------:| 
|MetaInfoSet | | 
|DefaultSpecificationChanged | | 
|OutlineSet | | 
|AnalysisStateChanged | | 
|SpecificationsChanged | | 
|PCBUpdated | | 

**Change type customerpanel**

| change     |   notes |
|----------|:-------------:|
|CustomerPanelInWorkingPanelsUpdated| |
|CustomerPanelDeleted| |
|CustomerPanelCreated| |
|CustomerPanelUpdated| |

**change type quotationitem**

| change     |   notes |
|----------|:-------------:|
|QuotationItemCreated||
|QuotationItemChanged||
|QuotationItemInfoSet||
|QuotationItemDeleted||

**change type layerstackusage**

| change | notes | 
|----------|:-------------:| 
|LayerStackChanged|| 
|LayerStackSet||

**change type quotation**

| change | notes |
|----------|:-------------:| 
| QuotationItemAdded ||
| AddressesSet ||

**change type dfmviolations**

| change | notes |
|----------|:-------------:|
| ViolationStatusChanged || 
| AnalysisStarted || 
| AnalysisStopped || 
| ViolationsChanged || 
| ViolationDeleted || 
