package de.fellows.app.collab.impl.listeners

import akka.cluster.sharding.typed.scaladsl.ClusterSharding
import akka.util.Timeout
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.utils.TopicUtils
import play.api.Logging

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.DurationInt

class LayerstackListener(service: LayerstackService, clusterSharding: ClusterSharding)(implicit
    val exc: ExecutionContext
) extends Logging {
  implicit val timeout: Timeout = Timeout(5 seconds)
  import TopicUtils.defaultNaming
  TopicUtils.subscribe(service.layerstackTimeline(), 10) {
    _.payload match {
      case msg => EventHelper.createEvent(msg, clusterSharding)
    }
  }
}
