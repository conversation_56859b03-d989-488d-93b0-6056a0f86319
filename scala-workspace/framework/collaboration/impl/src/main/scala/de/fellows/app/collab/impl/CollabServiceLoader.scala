package de.fellows.app.collab.impl

import akka.cluster.sharding.typed.scaladsl.Entity
import akka.stream.Materializer
import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.devmode.LagomDevModeComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{
  LagomApplication,
  LagomApplicationContext,
  LagomApplicationLoader,
  LagomServerComponents
}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.collab.api.CollabService
import de.fellows.app.collab.impl.entity.events.TimelineEntry
import de.fellows.app.collab.impl.entity.share.ShareEntity
import de.fellows.app.collab.impl.listeners.{
  AssemblyEventListener,
  DFMListener,
  LayerstackListener,
  PC<PERSON>istener,
  PanelListener,
  QuotationListener
}
import de.fellows.app.collab.impl.read.events.{TimelineEventProcessor, TimelineRepository}
import de.fellows.app.collab.impl.read.share.{ShareEventProcessor, ShareRepository}
import de.fellows.app.profile.api.ProfileService
import de.fellows.app.quotation.QuotationService
import de.fellows.app.user.api.UserService
import de.fellows.ems.dfm.api.DFMService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import de.fellows.utils.communication.ServiceDefinition
import kamon.Kamon
import play.api.Environment
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents

import scala.concurrent.ExecutionContext
import de.fellows.utils.health.HealthCheckComponents

class CollabServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new CollabServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new CollabServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[CollabService])
}

trait CollabServiceComponents extends LagomServerComponents
    with CassandraPersistenceComponents {
  lazy implicit val service: ServiceDefinition = ServiceDefinition("collaboration")

  implicit def executionContext: ExecutionContext

  def environment: Environment

  implicit def materializer: Materializer

  override lazy val jsonSerializerRegistry = CollabServiceSerializerRegistry

  lazy val notificationRepository = wire[ShareRepository]
  lazy val timelineRepository     = wire[TimelineRepository]

  val shareEventProcessor = wire[ShareEventProcessor]
  readSide.register(shareEventProcessor)
  val timelineEventProcessor = wire[TimelineEventProcessor]
  readSide.register(timelineEventProcessor)

  // elasticconf.getString("fellows.metrics.elasticsearch.protocol"),
  //      elasticconf.getString("fellows.metrics.elasticsearch.host"),
  //      elasticconf.getInt("fellows.metrics.elasticsearch.port"),

  clusterSharding.init(
    Entity(TimelineEntry.typeKey) { entityContext =>
      TimelineEntry(entityContext)
    }
  )

  persistentEntityRegistry.register(wire[ShareEntity])

}

abstract class CollabServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with CollabServiceComponents
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  lazy val assService       = serviceClient.implement[AssemblyService]
  lazy val userService      = serviceClient.implement[UserService]
  lazy val lsService        = serviceClient.implement[LayerstackService]
  lazy val pcbService       = serviceClient.implement[PCBService]
  lazy val profileService   = serviceClient.implement[ProfileService]
  lazy val quotationService = serviceClient.implement[QuotationService]
  lazy val panelService     = serviceClient.implement[PanelService]
  lazy val dfmService       = serviceClient.implement[DFMService]

  lazy val impl: CollabServiceImpl = wire[CollabServiceImpl]

  implicit val assemblyListener: AssemblyEventListener = wire[AssemblyEventListener]
  implicit val pcbListener: PCBListener                = wire[PCBListener]
  implicit val lsListener: LayerstackListener          = wire[LayerstackListener]
  implicit val panelListener: PanelListener            = wire[PanelListener]
  implicit val quotationListener: QuotationListener    = wire[QuotationListener]
  implicit val dfmListener: DFMListener                = wire[DFMListener]

  override lazy val lagomServer = serverFor[CollabService](impl)

}
