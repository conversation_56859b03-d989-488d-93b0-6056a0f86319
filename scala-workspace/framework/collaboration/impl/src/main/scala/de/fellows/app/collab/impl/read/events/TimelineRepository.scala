package de.fellows.app.collab.impl.read.events

import akka.Done
import akka.stream.Materializer
import akka.stream.scaladsl.Sink
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.collab.impl.entity.events.{TimelineEntryChanged, TimelineEntryEvent}
import de.fellows.utils.UUIDUtils
import de.fellows.utils.collaboration.{
  PredefinedTimelineChange,
  SpecificTimelineChange,
  TimelineEvent,
  TimelineGuest,
  TimelineSystemUser
}
import de.fellows.utils.communication.ServiceDefinition
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.{Date, UUID}
import scala.concurrent.{ExecutionContext, Future}
import scala.jdk.CollectionConverters._

case class TimelineEventDTO(
    team: String,
    id: UUID,
    entityType: String,
    ref: String,
    category: String,
    ctype: String,
    timestmp: Instant,
    changeclass: String,
    summary: String,
    description: String
)

object TimelineEventDTO {
  implicit val format: Format[TimelineEventDTO] = Json.format[TimelineEventDTO]
}

class TimelineRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) {
  def getTimeline(
      team: String,
      entityRefs: Seq[String],
      category: Seq[String],
      changeType: Option[Seq[String]],
      max: Option[Int],
      offset: Option[Int],
      excludeSystem: Boolean
  ) = {

    // language=SQL
    var x = (changeType match {
      case Some(value) =>
        session.select(
          """
            |SELECT * FROM timelineEntry WHERE team = ? AND ref IN ? AND category IN ? AND ctype in ?
            |""".stripMargin,
          team,
          entityRefs.asJava,
          category.asJava,
          value.asJava
        )
      case None =>
        session.select(
          """
            |SELECT * FROM timelineEntry WHERE team = ? AND ref IN ? AND category IN ?
            |""".stripMargin,
          team,
          entityRefs.asJava,
          category.asJava
        )
    })

    x = (excludeSystem match {
      case true  => x.filter(r => r.getString("changeuser") != UUIDUtils.nil.toString)
      case false => x
    })

    //    x = offset.map(o => x.drop(o)).getOrElse(x)
    //    x = max.map(m => x.take(m)).getOrElse(x)
    x.runWith(Sink.seq).map(_.map(toTimelineEvent))
      .map(_.sortBy(_.timestmp))
      .map { x =>
        var r = x
        if (offset.isDefined) {
          r = r.drop(offset.get)
        }
        if (max.isDefined) {
          r = r.take(max.get)
        }
        r
      }
  }

  def toTimelineEvent(r: Row): TimelineEventDTO =
    TimelineEventDTO(
      r.getString("team"),
      r.getUUID("id"),
      r.getString("entityType"),
      r.getString("ref"),
      r.getString("category"),
      r.getString("ctype"),
      r.getTimestamp("timestmp").toInstant,
      r.getString("changeclass"),
      r.getString("summary"),
      r.getString("description")
    )

  def getTimeline(
      team: String,
      entityRefs: Seq[String],
      max: Option[Int],
      offset: Option[Int],
      excludeSystem: Boolean
  ) = {
    // language=SQL
    var x = session.select(
      """
        |SELECT * FROM timelineEntry WHERE team = ? AND ref IN ?
        |""".stripMargin,
      team,
      entityRefs.asJava
    )

    //    x = offset.map(o => x.drop(o)).getOrElse(x)
    //    x = max.map(m => x.take(m)).getOrElse(x)
    x.runWith(Sink.seq).map(_.map(toTimelineEvent))
      .map(_.sortBy(_.timestmp))
      .map { x =>
        var r = x
        if (offset.isDefined) {
          r = r.drop(offset.get)
        }
        if (max.isDefined) {
          r = r.take(max.get)
        }
        r
      }
  }

}

private[impl] class TimelineEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[TimelineEntryEvent] {
  var updateStmt: PreparedStatement = _

  // language=SQL
  private def prepareStatements(): Future[Done] =
    for {
      update <- session.prepare(
        "UPDATE timelineEntry SET entityType = :entityType, changeclass = :changeclass, summary = :summary, description = :description, changeuser = :changeuser WHERE team = :team AND ref = :ref AND category = :category AND ctype = :ctype AND id = :id AND timestmp = :timestmp"
      )
    } yield {
      this.updateStmt = update
      Done
    }

  // language=SQL
  private def createTables(): Future[Done] =
    for {
      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS timelineEntry (
          |   team text,
          |   entityType text,
          |   ref text,
          |   category text,
          |   ctype text,
          |   id uuid,
          |   timestmp timestamp,
          |   changeclass text,
          |   summary text,
          |   description text,
          |   changeuser text,
          | PRIMARY KEY (team, ref, category, ctype,  timestmp, id)
          |)
        """.stripMargin
      )
    } yield Done

  def setEntry(id: UUID, event: TimelineEvent): Future[Seq[BoundStatement]] = {
    var id1 = this.updateStmt.bind()
      .setTimestamp("timestmp", Date.from(event.time))
      .setString("team", event.team)
      .setString("entityType", event.reference.entityType)
      .setString("ref", event.reference.entity)
      .setString("category", event.change.changeCategory)
      .setString("ctype", event.change.changeType)
      .setUUID("id", id)

    id1 = (event.creator match {
      case TimelineSystemUser(id)                    => id1.setString("changeuser", id.toString)
      case TimelineGuest(share, grantingUser, token) => id1.setString("changeuser", grantingUser.toString)
    })

    id1 = event.change match {
      case SpecificTimelineChange(changeCategory, changeType, summary, description) =>
        id1.setString("summary", summary)
        id1.setString("changeclass", "specific")
        id1.setString("description", description)
      case PredefinedTimelineChange(changeCategory, changeType, summaryIdentifier, descriptionIdentifier, params) =>
        id1.setString("changeclass", "defined")
        id1.setString("summary", summaryIdentifier)
        id1.setString("description", descriptionIdentifier)
    }

    Future.successful(Seq(
      id1
    ))
  }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[TimelineEntryEvent] =
    readSide.builder[TimelineEntryEvent]("timelineOffset-v1.1")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[TimelineEntryChanged](e => setEntry(e.event.id, e.event.evt))
      .build()

  override def aggregateTags: Set[AggregateEventTag[TimelineEntryEvent]] = TimelineEntryEvent.Tag.allTags
}
