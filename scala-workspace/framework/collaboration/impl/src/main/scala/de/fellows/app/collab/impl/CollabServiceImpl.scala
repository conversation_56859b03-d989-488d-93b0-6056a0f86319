package de.fellows.app.collab.impl

import akka.actor.ActorSystem
import akka.cluster.sharding.typed.scaladsl.{ClusterSharding, EntityRef}
import akka.util.Timeout
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.transport.{ExceptionMessage, TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.collab.api.{
  CollabService,
  ShareRequest,
  ShareRetrieval,
  Timeline,
  TimelineParameters,
  TimelineUserInfo,
  VersionShare
}
import de.fellows.app.collab.impl.entity.events.{Accepted, GetEvent, Rejected, TimelineEntry, TimelineEntryCommand}
import de.fellows.app.collab.impl.entity.share._
import de.fellows.app.collab.impl.read.events.TimelineRepository
import de.fellows.app.collab.impl.read.share.ShareRepository
import de.fellows.app.profile.api.{InternalProfileAPI, ProfileService}
import de.fellows.app.security.AccessControl
import de.fellows.app.security.AccessControlServiceComposition._
import de.fellows.app.user.api.UserService
import de.fellows.utils.UUIDUtils
import de.fellows.utils.UUIDUtils._
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.collaboration.TimelineSystemUser
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.communication.TransportErrors.StrExOps
import de.fellows.utils.security.AuthenticationServiceComposition.authenticatedDirect
import de.fellows.utils.security._
import play.api.Logging

import java.util.UUID
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

class CollabServiceImpl(
    ereg: PersistentEntityRegistry,
    ac: ActorSystem,
    shares: ShareRepository,
    timeline: TimelineRepository,
    clusterSharding: ClusterSharding,
    profiles: ProfileService,
    users: UserService
)(implicit val ec: ExecutionContext, sd: ServiceDefinition) extends CollabService
    with StackrateAPIImpl with Logging {
  implicit val acs: ActorSystem = ac

  override def shareVersion(assembly: UUID, version: UUID): ServiceCall[ShareRequest, VersionShare] =
    authorizedString { token =>
      s"collab:${token.team}:${assembly}:*:*:write"
    } { (token, _) =>
      ServerServiceCall { s =>
        users._getUserInfo(token.userId).invoke().flatMap { userInfo =>
          val newID = UUID.randomUUID().short()
          ereg.refFor[ShareEntity](version.toString).ask(SetShare(
            Some(AssemblyReference(token.team, assembly, None, version)),
            Some(userInfo),
            Some(newID),
            s.features,
            s.password,
            s.expires,
            None
          ))
        }
      }
    }

  override def changeVersionShare(assembly: UUID, version: UUID): ServiceCall[ShareRequest, VersionShare] =
    authorizedString { token =>
      s"collab:${token.team}:${assembly}:*:*:write"
    } { (token, _) =>
      ServerServiceCall { s =>
        ereg.refFor[ShareEntity](version.toString).ask(SetShare(
          None,
          None,
          None,
          s.features,
          s.password,
          s.expires,
          s.clearPassword
        ))
      }
    }

  override def unshareVersion(assembly: UUID, version: UUID): ServiceCall[NotUsed, Done] =
    authorizedString { token =>
      s"collab:${token.team}:${assembly}:*:*:write"
    } { (token, _) =>
      ServerServiceCall { s =>
        ereg.refFor[ShareEntity](version.toString).ask(StopShare(version))
      }
    }

  override def getShareState(assembly: UUID, version: UUID): ServiceCall[NotUsed, VersionShare] =
    authorizedString { token =>
      s"collab:${token.team}:${assembly}:*:*:read"
    } { (token, _) =>
      ServerServiceCall { s =>
        ereg.refFor[ShareEntity](version.toString).ask(GetShare(version)).recover {
          case e: Throwable => throw new TransportException(
              TransportErrorCode.NotFound,
              new ExceptionMessage("Not Found", "Share not found"),
              e
            )
        }
      }
    }

  import scala.jdk.CollectionConverters._

  def fill(p: String, share: VersionShare): String = {
    var res = p.replace("$team", share.assembly.team)
      .replace("$version", share.assembly.version.toString)
      .replace("$assembly", share.assembly.id.toString)

    val allParams = share.features.flatMap(_.parameters.toSeq).toMap

    allParams.foreach { e =>
      res = res.replace("$" + e._1, e._2)
    }

    res

  }

  override def getShareToken(shareID: String): ServiceCall[ShareRetrieval, VersionShare] =
    ServiceCall { sr =>
      shares.getShareByID(shareID).flatMap { eid =>
        val entity = ereg.refFor[ShareEntity](eid.toString)
        entity.ask(AccessShare(sr.password)).map { share =>
          //          val fakeUser = UUIDUtils.ofShort(share.shareID)
          // todo: features

          val perms = share.features.flatMap { sf =>
            sf.shareOptions.flatMap { opt =>
              config.getStringList(s"fellows.services.collaboration.features.${sf.name}.${opt}").asScala.map { p =>
                fill(p, share)
              }
            }
          }.distinct

          val tokenContent = TokenContent(
            share.shareOwner.uid,
            share.assembly.team,
            share.shareOwner.username,
            share.shareOwner.email,
            share = Some(shareID),
            isRefreshToken = false,
            perms.map(p => SecurityClaim(Some(p)))
          )
          val token = JwtTokenUtil.generateTokens(tokenContent)

          share.copy(token = Some(token.authToken), refresh = token.refreshToken)
        }
      }
    //      entity.ask(AccessShare)
    }

  override def isProtected(shareID: String): ServiceCall[NotUsed, Boolean] =
    ServiceCall { sr =>
      shares.getShareByID(shareID).flatMap { eid =>
        val entity = ereg.refFor[ShareEntity](eid.toString)
        entity.ask(GetShare(eid)).map(_.isProtected).recover {
          case e: Throwable => throw new TransportException(
              TransportErrorCode.NotFound,
              new ExceptionMessage("Not Found", "Share not found"),
              e
            )
        }
      }
    }

  def toApi(profile: InternalProfileAPI): TimelineUserInfo =
    TimelineUserInfo(
      UUID.fromString(profile.user),
      (profile.data.firstName, profile.data.lastName) match {
        case (Some(first), Some(last)) => s"$first $last"
        case (None, Some(last))        => s"$last"
        case (Some(first), None)       => s"$first"
        case (None, None)              => s"User"
      },
      profile.avatar.map(_.toApi(ServiceDefinition("profile")))
    )

  def _doGetTimeline(team: String, tlp: TimelineParameters): Future[Timeline] = {
    implicit val timeout = Timeout(5 seconds)

    val r =
      (if (tlp.filteredChangeCategories.isDefined) {
         timeline.getTimeline(
           team,
           tlp.filteredReferences,
           tlp.filteredChangeCategories.get,
           tlp.filteredChangeTypes,
           tlp.max,
           tlp.offset,
           tlp.excludeSystem.getOrElse(false)
         )
       } else {
         timeline.getTimeline(team, tlp.filteredReferences, tlp.max, tlp.offset, tlp.excludeSystem.getOrElse(false))
       })

    r.map(y =>
      Future.sequence(y.map { dto =>
        val eref: EntityRef[TimelineEntryCommand] = clusterSharding.entityRefFor(TimelineEntry.typeKey, dto.id.toString)
        eref.ask(x => GetEvent(dto.id, x)).map {
          case Accepted(i) => Some(i)
          case Rejected(code, message) =>
            logger.error(message)
            None
        }
      })
    ).flatten.map(_.flatten)
      .flatMap { evts =>
        val userIds = evts.map(_.creator).filter(_.isInstanceOf[TimelineSystemUser]).map(
          _.asInstanceOf[TimelineSystemUser]
        ).map(_.id).distinct

        import de.fellows.utils.FutureUtils._

        Future.sequence(userIds.flatMap(uid => UUIDUtils.fromString(uid)).map(uid =>
          (profiles._getProfile(uid).invoke() ??)
        ))
          .map(_.flatten.groupBy(_.user).map(x => (x._1, toApi(x._2.head))))
          .map(users => Timeline(evts, users))
      }

  }

  def _getTimeline(team: String, token: TokenContent, tlp: TimelineParameters): Future[Timeline] = {
    val permCheck = AccessControl.check(s"timeline:${team}:${tlp.filteredReferences.mkString(",")}:*:*:read", token)

    if (permCheck.allowed) {
      _doGetTimeline(team, tlp)
    } else {
      "Access Denied" ! TransportErrorCode.Forbidden
    }

  }

  override def getTimelineByReference(
      entityType: String,
      reference: String
  ): ServiceCall[TimelineParameters, Timeline] =
    authenticatedDirect { (token, y) =>
      ServerServiceCall { tlp =>
        _getTimeline(token.team, token, tlp.copy(filteredTypes = Seq(entityType), filteredReferences = Seq(reference)))
      }
    }

  override def getTimeline(): ServiceCall[TimelineParameters, Timeline] =
    authenticatedDirect { (token, y) =>
      ServerServiceCall { tlp =>
        _getTimeline(token.team, token, tlp)
      }
    }
}

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
