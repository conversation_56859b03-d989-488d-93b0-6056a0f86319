package de.fellows.app.collab.impl.listeners

import akka.Done
import akka.cluster.sharding.typed.scaladsl.{ClusterSharding, EntityRef}
import akka.util.Timeout
import de.fellows.app.collab.impl.entity.events.{SetEvent, TimelineEntry, TimelineEntryCommand}
import de.fellows.utils.collaboration.TimelineEvent
import kamon.Kamon
import play.api.Logging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

object EventHelper extends Logging {
  def createEvent(
      msg: TimelineEvent,
      clusterSharding: ClusterSharding
  )(implicit t: Timeout, ex: ExecutionContext): Future[Done] = {
    Kamon.currentSpan().trace.drop()

    val id                                         = UUID.randomUUID()
    val outboxRef: EntityRef[TimelineEntryCommand] = clusterSharding.entityRefFor(TimelineEntry.typeKey, id.toString)

    logger.warn(s"Add event ${id}: ${msg.description}")
    outboxRef.ask(rep => SetEvent(id, msg, rep))
      .map(_ => Done)
  }
}
