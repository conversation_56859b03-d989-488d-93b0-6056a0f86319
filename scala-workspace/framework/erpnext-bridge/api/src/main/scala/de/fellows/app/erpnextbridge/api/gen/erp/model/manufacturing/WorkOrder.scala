package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class WorkOrder(
    name: String,
    naming_series: String,
    status: String,
    production_item: String,
    item_name: Option[String],
    image: Option[String],
    bom_no: String,
    company: String,
    qty: Double,
    material_transferred_for_manufacturing: Option[Double],
    produced_qty: Option[Double],
    sales_order: Option[String],
    project: Option[String],
    allow_alternative_item: Option[Int],
    use_multi_level_bom: Option[Int],
    skip_transfer: Option[Int],
    from_wip_warehouse: Option[Int],
    update_consumed_material_cost_in_project: Option[Int],
    wip_warehouse: Option[String],
    fg_warehouse: Option[String],
    scrap_warehouse: Option[String],
    required_items: Option[Seq[WorkOrderItem]],
    planned_start_date: String,
    actual_start_date: Option[String],
    planned_end_date: Option[String],
    actual_end_date: Option[String],
    expected_delivery_date: Option[String],
    transfer_material_against: Option[String],
    operations: Option[Seq[WorkOrderOperation]],
    planned_operating_cost: Option[Double],
    actual_operating_cost: Option[Double],
    additional_operating_cost: Option[Double],
    total_operating_cost: Option[Double],
    stock_uom: Option[String],
    material_request: Option[String],
    material_request_item: Option[String],
    sales_order_item: Option[String],
    production_plan: Option[String],
    production_plan_item: Option[String],
    product_bundle_item: Option[String],
    amended_from: Option[String],
    scheduling: Option[String],
    include_this_work_order: Option[Int],
    panels_to_manufacture: Option[Int]
)

object WorkOrder {
  val NAME_FIELD = "name"

  def apply(v: JsValue): WorkOrder = new WorkOrder(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    status = (v \ "status").get.as[String],
    production_item = (v \ "production_item").get.as[String],
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    image = (v \ "image").toOption.map(_.as[String]),
    bom_no = (v \ "bom_no").get.as[String],
    company = (v \ "company").get.as[String],
    qty = (v \ "qty").get.as[Double],
    material_transferred_for_manufacturing = (v \ "material_transferred_for_manufacturing").toOption.map(_.as[Double]),
    produced_qty = (v \ "produced_qty").toOption.map(_.as[Double]),
    sales_order = (v \ "sales_order").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    allow_alternative_item = (v \ "allow_alternative_item").toOption.map(_.as[Int]),
    use_multi_level_bom = (v \ "use_multi_level_bom").toOption.map(_.as[Int]),
    skip_transfer = (v \ "skip_transfer").toOption.map(_.as[Int]),
    from_wip_warehouse = (v \ "from_wip_warehouse").toOption.map(_.as[Int]),
    update_consumed_material_cost_in_project = (v \ "update_consumed_material_cost_in_project").toOption.map(_.as[Int]),
    wip_warehouse = (v \ "wip_warehouse").toOption.map(_.as[String]),
    fg_warehouse = (v \ "fg_warehouse").toOption.map(_.as[String]),
    scrap_warehouse = (v \ "scrap_warehouse").toOption.map(_.as[String]),
    required_items = (v \ "required_items").toOption.map(x => x.as[JsArray].value.map(_.as[WorkOrderItem]).toSeq),
    planned_start_date = (v \ "planned_start_date").get.as[String],
    actual_start_date = (v \ "actual_start_date").toOption.map(_.as[String]),
    planned_end_date = (v \ "planned_end_date").toOption.map(_.as[String]),
    actual_end_date = (v \ "actual_end_date").toOption.map(_.as[String]),
    expected_delivery_date = (v \ "expected_delivery_date").toOption.map(_.as[String]),
    transfer_material_against = (v \ "transfer_material_against").toOption.map(_.as[String]),
    operations = (v \ "operations").toOption.map(x => x.as[JsArray].value.map(_.as[WorkOrderOperation]).toSeq),
    planned_operating_cost = (v \ "planned_operating_cost").toOption.map(_.as[Double]),
    actual_operating_cost = (v \ "actual_operating_cost").toOption.map(_.as[Double]),
    additional_operating_cost = (v \ "additional_operating_cost").toOption.map(_.as[Double]),
    total_operating_cost = (v \ "total_operating_cost").toOption.map(_.as[Double]),
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    material_request = (v \ "material_request").toOption.map(_.as[String]),
    material_request_item = (v \ "material_request_item").toOption.map(_.as[String]),
    sales_order_item = (v \ "sales_order_item").toOption.map(_.as[String]),
    production_plan = (v \ "production_plan").toOption.map(_.as[String]),
    production_plan_item = (v \ "production_plan_item").toOption.map(_.as[String]),
    product_bundle_item = (v \ "product_bundle_item").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    scheduling = (v \ "scheduling").toOption.map(_.as[String]),
    include_this_work_order = (v \ "include_this_work_order").toOption.map(_.as[Int]),
    panels_to_manufacture = (v \ "panels_to_manufacture").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[WorkOrder] = Reads[WorkOrder] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Work Order") => JsSuccess(WorkOrder(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
