package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class LandedCostPurchaseReceipt(
    name: String,
    receipt_document_type: String,
    supplier: Option[String],
    posting_date: Option[String],
    grand_total: Option[Double]
)

object LandedCostPurchaseReceipt {
  val NAME_FIELD = "name"

  def apply(v: JsValue): LandedCostPurchaseReceipt = new LandedCostPurchaseReceipt(
    name = (v \ "name").get.as[String],
    receipt_document_type = (v \ "receipt_document_type").get.as[String],
    supplier = (v \ "supplier").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").toOption.map(_.as[String]),
    grand_total = (v \ "grand_total").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[LandedCostPurchaseReceipt] = Reads[LandedCostPurchaseReceipt] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Landed Cost Purchase Receipt") => JsSuccess(LandedCostPurchaseReceipt(js))
      case Some(_)                              => JsError("Wrong Doctype")
      case _                                    => JsError("Doctype not Found")
    }
  }

}
