package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class POSClosingVoucherInvoices(
    name: String,
    invoice: Option[String],
    qty_of_items: Option[String],
    grand_total: Option[Double]
)

object POSClosingVoucherInvoices {
  val NAME_FIELD = "name"

  def apply(v: JsValue): POSClosingVoucherInvoices = new POSClosingVoucherInvoices(
    name = (v \ "name").get.as[String],
    invoice = (v \ "invoice").toOption.map(_.as[String]),
    qty_of_items = (v \ "qty_of_items").toOption.map(_.as[String]),
    grand_total = (v \ "grand_total").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[POSClosingVoucherInvoices] = Reads[POSClosingVoucherInvoices] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("POS Closing Voucher Invoices") => JsSuccess(POSClosingVoucherInvoices(js))
      case Some(_)                              => JsError("Wrong Doctype")
      case _                                    => JsError("Doctype not Found")
    }
  }

}
