package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class QualityInspection(
    name: String,
    naming_series: String,
    report_date: String,
    inspection_type: String,
    reference_type: Option[String],
    item_code: String,
    item_serial_no: Option[String],
    batch_no: Option[String],
    sample_size: Double,
    item_name: Option[String],
    status: String,
    inspected_by: String,
    verified_by: Option[String],
    bom_no: Option[String],
    remarks: Option[String],
    amended_from: Option[String],
    quality_inspection_template: Option[String],
    readings: Option[Seq[QualityInspectionReading]]
)

object QualityInspection {
  val NAME_FIELD = "name"

  def apply(v: JsValue): QualityInspection = new QualityInspection(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    report_date = (v \ "report_date").get.as[String],
    inspection_type = (v \ "inspection_type").get.as[String],
    reference_type = (v \ "reference_type").toOption.map(_.as[String]),
    item_code = (v \ "item_code").get.as[String],
    item_serial_no = (v \ "item_serial_no").toOption.map(_.as[String]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    sample_size = (v \ "sample_size").get.as[Double],
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    status = (v \ "status").get.as[String],
    inspected_by = (v \ "inspected_by").get.as[String],
    verified_by = (v \ "verified_by").toOption.map(_.as[String]),
    bom_no = (v \ "bom_no").toOption.map(_.as[String]),
    remarks = (v \ "remarks").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    quality_inspection_template = (v \ "quality_inspection_template").toOption.map(_.as[String]),
    readings = (v \ "readings").toOption.map(x => x.as[JsArray].value.map(_.as[QualityInspectionReading]).toSeq)
  )

  implicit val reads: Reads[QualityInspection] = Reads[QualityInspection] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Quality Inspection") => JsSuccess(QualityInspection(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
