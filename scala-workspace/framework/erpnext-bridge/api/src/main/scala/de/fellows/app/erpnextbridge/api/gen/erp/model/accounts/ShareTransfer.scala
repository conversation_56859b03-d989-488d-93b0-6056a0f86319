package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ShareTransfer(
    name: String,
    transfer_type: String,
    date: String,
    from_shareholder: Option[String],
    from_folio_no: Option[String],
    to_shareholder: Option[String],
    to_folio_no: Option[String],
    equity_or_liability_account: String,
    asset_account: Option[String],
    share_type: String,
    from_no: Int,
    rate: Double,
    no_of_shares: Int,
    to_no: Int,
    amount: Option[Double],
    company: String,
    amended_from: Option[String]
)

object ShareTransfer {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ShareTransfer = new ShareTransfer(
    name = (v \ "name").get.as[String],
    transfer_type = (v \ "transfer_type").get.as[String],
    date = (v \ "date").get.as[String],
    from_shareholder = (v \ "from_shareholder").toOption.map(_.as[String]),
    from_folio_no = (v \ "from_folio_no").toOption.map(_.as[String]),
    to_shareholder = (v \ "to_shareholder").toOption.map(_.as[String]),
    to_folio_no = (v \ "to_folio_no").toOption.map(_.as[String]),
    equity_or_liability_account = (v \ "equity_or_liability_account").get.as[String],
    asset_account = (v \ "asset_account").toOption.map(_.as[String]),
    share_type = (v \ "share_type").get.as[String],
    from_no = (v \ "from_no").get.as[Int],
    rate = (v \ "rate").get.as[Double],
    no_of_shares = (v \ "no_of_shares").get.as[Int],
    to_no = (v \ "to_no").get.as[Int],
    amount = (v \ "amount").toOption.map(_.as[Double]),
    company = (v \ "company").get.as[String],
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ShareTransfer] = Reads[ShareTransfer] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Share Transfer") => JsSuccess(ShareTransfer(js))
      case Some(_)                => JsError("Wrong Doctype")
      case _                      => JsError("Doctype not Found")
    }
  }

}
