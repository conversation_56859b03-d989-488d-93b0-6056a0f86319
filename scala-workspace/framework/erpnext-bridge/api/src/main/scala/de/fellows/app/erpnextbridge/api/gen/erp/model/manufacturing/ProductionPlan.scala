package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsE<PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ProductionPlan(
    name: String,
    naming_series: String,
    company: String,
    get_items_from: Option[String],
    posting_date: String,
    item_code: Option[String],
    customer: Option[String],
    warehouse: Option[String],
    project: Option[String],
    from_date: Option[String],
    to_date: Option[String],
    sales_orders: Option[Seq[ProductionPlanSalesOrder]],
    material_requests: Option[Seq[ProductionPlanMaterialRequest]],
    po_items: Seq[ProductionPlanItem],
    include_non_stock_items: Option[Int],
    include_subcontracted_items: Option[Int],
    ignore_existing_ordered_qty: Option[Int],
    for_warehouse: Option[String],
    mr_items: Option[Seq[MaterialRequestPlanItem]],
    total_planned_qty: Option[Double],
    total_produced_qty: Option[Double],
    status: Option[String],
    amended_from: Option[String]
)

object ProductionPlan {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ProductionPlan = new ProductionPlan(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    company = (v \ "company").get.as[String],
    get_items_from = (v \ "get_items_from").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").get.as[String],
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    customer = (v \ "customer").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    from_date = (v \ "from_date").toOption.map(_.as[String]),
    to_date = (v \ "to_date").toOption.map(_.as[String]),
    sales_orders =
      (v \ "sales_orders").toOption.map(x => x.as[JsArray].value.map(_.as[ProductionPlanSalesOrder]).toSeq),
    material_requests =
      (v \ "material_requests").toOption.map(x => x.as[JsArray].value.map(_.as[ProductionPlanMaterialRequest]).toSeq),
    po_items = (v \ "po_items").toOption.map(x => x.as[JsArray].value.map(_.as[ProductionPlanItem])).get.toSeq,
    include_non_stock_items = (v \ "include_non_stock_items").toOption.map(_.as[Int]),
    include_subcontracted_items = (v \ "include_subcontracted_items").toOption.map(_.as[Int]),
    ignore_existing_ordered_qty = (v \ "ignore_existing_ordered_qty").toOption.map(_.as[Int]),
    for_warehouse = (v \ "for_warehouse").toOption.map(_.as[String]),
    mr_items = (v \ "mr_items").toOption.map(x => x.as[JsArray].value.map(_.as[MaterialRequestPlanItem]).toSeq),
    total_planned_qty = (v \ "total_planned_qty").toOption.map(_.as[Double]),
    total_produced_qty = (v \ "total_produced_qty").toOption.map(_.as[Double]),
    status = (v \ "status").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ProductionPlan] = Reads[ProductionPlan] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Production Plan") => JsSuccess(ProductionPlan(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
