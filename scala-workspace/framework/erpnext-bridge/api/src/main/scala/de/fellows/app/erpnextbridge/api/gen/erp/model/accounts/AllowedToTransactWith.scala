package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class AllowedToTransactWith(
    name: String,
    company: String
)

object AllowedToTransactWith {
  val NAME_FIELD = "name"

  def apply(v: JsValue): AllowedToTransactWith = new AllowedToTransactWith(
    name = (v \ "name").get.as[String],
    company = (v \ "company").get.as[String]
  )

  implicit val reads: Reads[AllowedToTransactWith] = Reads[AllowedToTransactWith] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Allowed To Transact With") => JsSuccess(AllowedToTransactWith(js))
      case Some(_)                          => JsError("Wrong Doctype")
      case _                                => JsError("Doctype not Found")
    }
  }

}
