package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class AccountingDimension(
    document_type: String,
    label: Option[String],
    fieldname: Option[String],
    dimension_defaults: Option[Seq[AccountingDimensionDetail]],
    disabled: Option[Int]
)

object AccountingDimension {
  val NAME_FIELD = "label"

  def apply(v: JsValue): AccountingDimension = new AccountingDimension(
    document_type = (v \ "document_type").get.as[String],
    label = (v \ "label").toOption.map(_.as[String]),
    fieldname = (v \ "fieldname").toOption.map(_.as[String]),
    dimension_defaults =
      (v \ "dimension_defaults").toOption.map(x => x.as[JsArray].value.map(_.as[AccountingDimensionDetail]).toSeq),
    disabled = (v \ "disabled").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[AccountingDimension] = Reads[AccountingDimension] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Accounting Dimension") => JsSuccess(AccountingDimension(js))
      case Some(_)                      => JsError("Wrong Doctype")
      case _                            => JsError("Doctype not Found")
    }
  }

}
