package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsE<PERSON><PERSON>, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentRequest(
    name: String,
    payment_request_type: String,
    transaction_date: Option[String],
    naming_series: String,
    mode_of_payment: Option[String],
    party_type: Option[String],
    reference_doctype: Option[String],
    grand_total: Option[Double],
    is_a_subscription: Option[Int],
    currency: Option[String],
    subscription_plans: Option[Seq[SubscriptionPlanDetail]],
    bank_account: Option[String],
    print_format: Option[String],
    email_to: Option[String],
    subject: Option[String],
    payment_gateway_account: Option[String],
    status: Option[String],
    make_sales_invoice: Option[Int],
    message: Option[String],
    mute_email: Option[Int],
    payment_order: Option[String],
    amended_from: Option[String]
)

object PaymentRequest {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentRequest = new PaymentRequest(
    name = (v \ "name").get.as[String],
    payment_request_type = (v \ "payment_request_type").get.as[String],
    transaction_date = (v \ "transaction_date").toOption.map(_.as[String]),
    naming_series = (v \ "naming_series").get.as[String],
    mode_of_payment = (v \ "mode_of_payment").toOption.map(_.as[String]),
    party_type = (v \ "party_type").toOption.map(_.as[String]),
    reference_doctype = (v \ "reference_doctype").toOption.map(_.as[String]),
    grand_total = (v \ "grand_total").toOption.map(_.as[Double]),
    is_a_subscription = (v \ "is_a_subscription").toOption.map(_.as[Int]),
    currency = (v \ "currency").toOption.map(_.as[String]),
    subscription_plans =
      (v \ "subscription_plans").toOption.map(x => x.as[JsArray].value.map(_.as[SubscriptionPlanDetail]).toSeq),
    bank_account = (v \ "bank_account").toOption.map(_.as[String]),
    print_format = (v \ "print_format").toOption.map(_.as[String]),
    email_to = (v \ "email_to").toOption.map(_.as[String]),
    subject = (v \ "subject").toOption.map(_.as[String]),
    payment_gateway_account = (v \ "payment_gateway_account").toOption.map(_.as[String]),
    status = (v \ "status").toOption.map(_.as[String]),
    make_sales_invoice = (v \ "make_sales_invoice").toOption.map(_.as[Int]),
    message = (v \ "message").toOption.map(_.as[String]),
    mute_email = (v \ "mute_email").toOption.map(_.as[Int]),
    payment_order = (v \ "payment_order").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PaymentRequest] = Reads[PaymentRequest] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Request") => JsSuccess(PaymentRequest(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
