package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class AccountingDimensionDetail(
    name: String,
    company: Option[String],
    reference_document: Option[String],
    mandatory_for_bs: Option[Int],
    mandatory_for_pl: Option[Int]
)

object AccountingDimensionDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): AccountingDimensionDetail = new AccountingDimensionDetail(
    name = (v \ "name").get.as[String],
    company = (v \ "company").toOption.map(_.as[String]),
    reference_document = (v \ "reference_document").toOption.map(_.as[String]),
    mandatory_for_bs = (v \ "mandatory_for_bs").toOption.map(_.as[Int]),
    mandatory_for_pl = (v \ "mandatory_for_pl").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[AccountingDimensionDetail] = Reads[AccountingDimensionDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Accounting Dimension Detail") => JsSuccess(AccountingDimensionDetail(js))
      case Some(_)                             => JsError("Wrong Doctype")
      case _                                   => JsError("Doctype not Found")
    }
  }

}
