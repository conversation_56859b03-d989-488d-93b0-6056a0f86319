package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ClosedDocument(
    name: String,
    document_type: String,
    closed: Option[Int]
)

object ClosedDocument {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ClosedDocument = new ClosedDocument(
    name = (v \ "name").get.as[String],
    document_type = (v \ "document_type").get.as[String],
    closed = (v \ "closed").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[ClosedDocument] = Reads[ClosedDocument] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Closed Document") => JsSuccess(ClosedDocument(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
