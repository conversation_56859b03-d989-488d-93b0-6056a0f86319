package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class CustomRole(
    name: String,
    page: Option[String],
    report: Option[String],
    roles: Option[Seq[HasRole]],
    ref_doctype: Option[String]
)

object CustomRole {
  val NAME_FIELD = "name"

  def apply(v: JsValue): CustomRole = new CustomRole(
    name = (v \ "name").get.as[String],
    page = (v \ "page").toOption.map(_.as[String]),
    report = (v \ "report").toOption.map(_.as[String]),
    roles = (v \ "roles").toOption.map(x => x.as[JsArray].value.map(_.as[HasRole]).toSeq),
    ref_doctype = (v \ "ref_doctype").toOption.map(_.as[String])
  )

  implicit val reads: Reads[CustomRole] = Reads[CustomRole] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Custom Role") => JsSuccess(CustomRole(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
