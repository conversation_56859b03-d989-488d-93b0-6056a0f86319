package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankStatementTransactionEntry(
    name: String,
    bank_account: String,
    from_date: String,
    to_date: String,
    bank_settings: Option[String],
    bank: Option[String],
    receivable_account: String,
    payable_account: String,
    new_transaction_items: Option[Seq[BankStatementTransactionPaymentItem]],
    payment_invoice_items: Option[Seq[BankStatementTransactionInvoiceItem]],
    reconciled_transaction_items: Option[Seq[BankStatementTransactionPaymentItem]],
    amended_from: Option[String]
)

object BankStatementTransactionEntry {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankStatementTransactionEntry = new BankStatementTransactionEntry(
    name = (v \ "name").get.as[String],
    bank_account = (v \ "bank_account").get.as[String],
    from_date = (v \ "from_date").get.as[String],
    to_date = (v \ "to_date").get.as[String],
    bank_settings = (v \ "bank_settings").toOption.map(_.as[String]),
    bank = (v \ "bank").toOption.map(_.as[String]),
    receivable_account = (v \ "receivable_account").get.as[String],
    payable_account = (v \ "payable_account").get.as[String],
    new_transaction_items = (v \ "new_transaction_items").toOption.map(x =>
      x.as[JsArray].value.map(_.as[BankStatementTransactionPaymentItem]).toSeq
    ),
    payment_invoice_items = (v \ "payment_invoice_items").toOption.map(x =>
      x.as[JsArray].value.map(_.as[BankStatementTransactionInvoiceItem]).toSeq
    ),
    reconciled_transaction_items = (v \ "reconciled_transaction_items").toOption.map(x =>
      x.as[JsArray].value.map(_.as[BankStatementTransactionPaymentItem]).toSeq
    ),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BankStatementTransactionEntry] = Reads[BankStatementTransactionEntry] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Statement Transaction Entry") => JsSuccess(BankStatementTransactionEntry(js))
      case Some(_)                                  => JsError("Wrong Doctype")
      case _                                        => JsError("Doctype not Found")
    }
  }

}
