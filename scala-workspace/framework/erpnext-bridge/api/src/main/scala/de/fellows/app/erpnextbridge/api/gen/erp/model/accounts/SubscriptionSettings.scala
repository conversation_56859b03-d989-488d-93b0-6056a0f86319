package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SubscriptionSettings(
    name: String,
    grace_period: Option[Int],
    cancel_after_grace: Option[Int],
    prorate: Option[Int]
)

object SubscriptionSettings {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SubscriptionSettings = new SubscriptionSettings(
    name = (v \ "name").get.as[String],
    grace_period = (v \ "grace_period").toOption.map(_.as[Int]),
    cancel_after_grace = (v \ "cancel_after_grace").toOption.map(_.as[Int]),
    prorate = (v \ "prorate").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[SubscriptionSettings] = Reads[SubscriptionSettings] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Subscription Settings") => JsSuccess(SubscriptionSettings(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
