package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class AccountSubtype(
    account_subtype: Option[String]
)

object AccountSubtype {
  val NAME_FIELD = "account_subtype"

  def apply(v: JsValue): AccountSubtype = new AccountSubtype(
    account_subtype = (v \ "account_subtype").toOption.map(_.as[String])
  )

  implicit val reads: Reads[AccountSubtype] = Reads[AccountSubtype] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Account Subtype") => JsSuccess(AccountSubtype(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
