package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Comment(
    name: String,
    comment_type: String,
    comment_email: Option[String],
    subject: Option[String],
    comment_by: Option[String],
    published: Option[Int],
    seen: Option[Int],
    reference_doctype: Option[String],
    link_doctype: Option[String],
    reference_owner: Option[String]
)

object Comment {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Comment = new Comment(
    name = (v \ "name").get.as[String],
    comment_type = (v \ "comment_type").get.as[String],
    comment_email = (v \ "comment_email").toOption.map(_.as[String]),
    subject = (v \ "subject").toOption.map(_.as[String]),
    comment_by = (v \ "comment_by").toOption.map(_.as[String]),
    published = (v \ "published").toOption.map(_.as[Int]),
    seen = (v \ "seen").toOption.map(_.as[Int]),
    reference_doctype = (v \ "reference_doctype").toOption.map(_.as[String]),
    link_doctype = (v \ "link_doctype").toOption.map(_.as[String]),
    reference_owner = (v \ "reference_owner").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Comment] = Reads[Comment] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Comment") => JsSuccess(Comment(js))
      case Some(_)         => JsError("Wrong Doctype")
      case _               => JsError("Doctype not Found")
    }
  }

}
