package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class JobCardTimeLog(
    name: String,
    from_time: Option[String],
    to_time: Option[String],
    time_in_mins: Option[Double],
    completed_qty: Double
)

object JobCardTimeLog {
  val NAME_FIELD = "name"

  def apply(v: JsValue): JobCardTimeLog = new JobCardTimeLog(
    name = (v \ "name").get.as[String],
    from_time = (v \ "from_time").toOption.map(_.as[String]),
    to_time = (v \ "to_time").toOption.map(_.as[String]),
    time_in_mins = (v \ "time_in_mins").toOption.map(_.as[Double]),
    completed_qty = (v \ "completed_qty").get.as[Double]
  )

  implicit val reads: Reads[JobCardTimeLog] = Reads[JobCardTimeLog] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Job Card Time Log") => JsSuccess(JobCardTimeLog(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
