package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class CommunicationLink(
    name: String,
    link_doctype: String
)

object CommunicationLink {
  val NAME_FIELD = "name"

  def apply(v: JsValue): CommunicationLink = new CommunicationLink(
    name = (v \ "name").get.as[String],
    link_doctype = (v \ "link_doctype").get.as[String]
  )

  implicit val reads: Reads[CommunicationLink] = Reads[CommunicationLink] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Communication Link") => JsSuccess(CommunicationLink(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
