package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ProductionPlanSalesOrder(
    name: String,
    sales_order: String,
    sales_order_date: Option[String],
    customer: Option[String],
    grand_total: Option[Double]
)

object ProductionPlanSalesOrder {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ProductionPlanSalesOrder = new ProductionPlanSalesOrder(
    name = (v \ "name").get.as[String],
    sales_order = (v \ "sales_order").get.as[String],
    sales_order_date = (v \ "sales_order_date").toOption.map(_.as[String]),
    customer = (v \ "customer").toOption.map(_.as[String]),
    grand_total = (v \ "grand_total").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[ProductionPlanSalesOrder] = Reads[ProductionPlanSalesOrder] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Production Plan Sales Order") => JsSuccess(ProductionPlanSalesOrder(js))
      case Some(_)                             => JsError("Wrong Doctype")
      case _                                   => JsError("Doctype not Found")
    }
  }

}
