package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SMSSettings(
    name: String,
    sms_gateway_url: String,
    message_parameter: String,
    receiver_parameter: String,
    parameters: Option[Seq[SMSParameter]],
    use_post: Option[Int]
)

object SMSSettings {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SMSSettings = new SMSSettings(
    name = (v \ "name").get.as[String],
    sms_gateway_url = (v \ "sms_gateway_url").get.as[String],
    message_parameter = (v \ "message_parameter").get.as[String],
    receiver_parameter = (v \ "receiver_parameter").get.as[String],
    parameters = (v \ "parameters").toOption.map(x => x.as[JsArray].value.map(_.as[SMSParameter]).toSeq),
    use_post = (v \ "use_post").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[SMSSettings] = Reads[SMSSettings] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("SMS Settings") => JsSuccess(SMSSettings(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
