package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class StockEntryType(
    name: String,
    purpose: String
)

object StockEntryType {
  val NAME_FIELD = "name"

  def apply(v: JsValue): StockEntryType = new StockEntryType(
    name = (v \ "name").get.as[String],
    purpose = (v \ "purpose").get.as[String]
  )

  implicit val reads: Reads[StockEntryType] = Reads[StockEntryType] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Stock Entry Type") => JsSuccess(StockEntryType(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
