package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class TransactionLog(
    name: String,
    row_index: Option[String],
    reference_doctype: Option[String],
    document_name: Option[String],
    timestamp: Option[String],
    checksum_version: Option[String],
    amended_from: Option[String]
)

object TransactionLog {
  val NAME_FIELD = "name"

  def apply(v: JsValue): TransactionLog = new TransactionLog(
    name = (v \ "name").get.as[String],
    row_index = (v \ "row_index").toOption.map(_.as[String]),
    reference_doctype = (v \ "reference_doctype").toOption.map(_.as[String]),
    document_name = (v \ "document_name").toOption.map(_.as[String]),
    timestamp = (v \ "timestamp").toOption.map(_.as[String]),
    checksum_version = (v \ "checksum_version").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[TransactionLog] = Reads[TransactionLog] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Transaction Log") => JsSuccess(TransactionLog(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
