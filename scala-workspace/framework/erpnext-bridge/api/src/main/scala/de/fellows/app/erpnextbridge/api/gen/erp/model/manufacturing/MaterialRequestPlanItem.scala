package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class MaterialRequestPlanItem(
    name: String,
    item_code: String,
    item_name: Option[String],
    warehouse: String,
    material_request_type: Option[String],
    quantity: Double,
    uom: Option[String],
    projected_qty: Option[Double],
    actual_qty: Option[Double],
    description: Option[String],
    min_order_qty: Option[Double],
    sales_order: Option[String],
    requested_qty: Option[Double]
)

object MaterialRequestPlanItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): MaterialRequestPlanItem = new MaterialRequestPlanItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").get.as[String],
    material_request_type = (v \ "material_request_type").toOption.map(_.as[String]),
    quantity = (v \ "quantity").get.as[Double],
    uom = (v \ "uom").toOption.map(_.as[String]),
    projected_qty = (v \ "projected_qty").toOption.map(_.as[Double]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    description = (v \ "description").toOption.map(_.as[String]),
    min_order_qty = (v \ "min_order_qty").toOption.map(_.as[Double]),
    sales_order = (v \ "sales_order").toOption.map(_.as[String]),
    requested_qty = (v \ "requested_qty").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[MaterialRequestPlanItem] = Reads[MaterialRequestPlanItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Material Request Plan Item") => JsSuccess(MaterialRequestPlanItem(js))
      case Some(_)                            => JsError("Wrong Doctype")
      case _                                  => JsError("Doctype not Found")
    }
  }

}
