package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ShareType(
    title: String
)

object ShareType {
  val NAME_FIELD = "title"

  def apply(v: JsValue): ShareType = new ShareType(
    title = (v \ "title").get.as[String]
  )

  implicit val reads: Reads[ShareType] = Reads[ShareType] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Share Type") => JsSuccess(ShareType(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
