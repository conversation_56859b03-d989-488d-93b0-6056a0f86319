package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class StockEntryDetail(
    name: String,
    barcode: Option[String],
    s_warehouse: Option[String],
    t_warehouse: Option[String],
    item_code: String,
    item_group: Option[String],
    item_name: Option[String],
    description: Option[String],
    qty: Double,
    basic_rate: Option[Double],
    basic_amount: Option[Double],
    additional_cost: Option[Double],
    amount: Option[Double],
    valuation_rate: Option[Double],
    uom: String,
    conversion_factor: Double,
    stock_uom: String,
    transfer_qty: Double,
    retain_sample: Option[Int],
    sample_quantity: Option[Int],
    batch_no: Option[String],
    quality_inspection: Option[String],
    expense_account: Option[String],
    cost_center: Option[String],
    allow_zero_valuation_rate: Option[Int],
    actual_qty: Option[Double],
    bom_no: Option[String],
    allow_alternative_item: Option[Int],
    material_request: Option[String],
    material_request_item: Option[String],
    original_item: Option[String],
    subcontracted_item: Option[String],
    against_stock_entry: Option[String],
    ste_detail: Option[String],
    po_detail: Option[String],
    transferred_qty: Option[Double],
    reference_purchase_receipt: Option[String],
    project: Option[String]
)

object StockEntryDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): StockEntryDetail = new StockEntryDetail(
    name = (v \ "name").get.as[String],
    barcode = (v \ "barcode").toOption.map(_.as[String]),
    s_warehouse = (v \ "s_warehouse").toOption.map(_.as[String]),
    t_warehouse = (v \ "t_warehouse").toOption.map(_.as[String]),
    item_code = (v \ "item_code").get.as[String],
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String]),
    qty = (v \ "qty").get.as[Double],
    basic_rate = (v \ "basic_rate").toOption.map(_.as[Double]),
    basic_amount = (v \ "basic_amount").toOption.map(_.as[Double]),
    additional_cost = (v \ "additional_cost").toOption.map(_.as[Double]),
    amount = (v \ "amount").toOption.map(_.as[Double]),
    valuation_rate = (v \ "valuation_rate").toOption.map(_.as[Double]),
    uom = (v \ "uom").get.as[String],
    conversion_factor = (v \ "conversion_factor").get.as[Double],
    stock_uom = (v \ "stock_uom").get.as[String],
    transfer_qty = (v \ "transfer_qty").get.as[Double],
    retain_sample = (v \ "retain_sample").toOption.map(_.as[Int]),
    sample_quantity = (v \ "sample_quantity").toOption.map(_.as[Int]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    quality_inspection = (v \ "quality_inspection").toOption.map(_.as[String]),
    expense_account = (v \ "expense_account").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    allow_zero_valuation_rate = (v \ "allow_zero_valuation_rate").toOption.map(_.as[Int]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    bom_no = (v \ "bom_no").toOption.map(_.as[String]),
    allow_alternative_item = (v \ "allow_alternative_item").toOption.map(_.as[Int]),
    material_request = (v \ "material_request").toOption.map(_.as[String]),
    material_request_item = (v \ "material_request_item").toOption.map(_.as[String]),
    original_item = (v \ "original_item").toOption.map(_.as[String]),
    subcontracted_item = (v \ "subcontracted_item").toOption.map(_.as[String]),
    against_stock_entry = (v \ "against_stock_entry").toOption.map(_.as[String]),
    ste_detail = (v \ "ste_detail").toOption.map(_.as[String]),
    po_detail = (v \ "po_detail").toOption.map(_.as[String]),
    transferred_qty = (v \ "transferred_qty").toOption.map(_.as[Double]),
    reference_purchase_receipt = (v \ "reference_purchase_receipt").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String])
  )

  implicit val reads: Reads[StockEntryDetail] = Reads[StockEntryDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Stock Entry Detail") => JsSuccess(StockEntryDetail(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
