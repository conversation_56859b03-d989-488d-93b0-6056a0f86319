package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Subscription(
    name: String,
    customer: String,
    status: Option[String],
    start: Option[String],
    cancelation_date: Option[String],
    trial_period_start: Option[String],
    trial_period_end: Option[String],
    current_invoice_start: Option[String],
    current_invoice_end: Option[String],
    days_until_due: Option[Int],
    cancel_at_period_end: Option[Int],
    generate_invoice_at_period_start: Option[Int],
    plans: Seq[SubscriptionPlanDetail],
    tax_template: Option[String],
    apply_additional_discount: Option[String],
    additional_discount_amount: Option[Double],
    invoices: Option[Seq[SubscriptionInvoice]]
)

object Subscription {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Subscription = new Subscription(
    name = (v \ "name").get.as[String],
    customer = (v \ "customer").get.as[String],
    status = (v \ "status").toOption.map(_.as[String]),
    start = (v \ "start").toOption.map(_.as[String]),
    cancelation_date = (v \ "cancelation_date").toOption.map(_.as[String]),
    trial_period_start = (v \ "trial_period_start").toOption.map(_.as[String]),
    trial_period_end = (v \ "trial_period_end").toOption.map(_.as[String]),
    current_invoice_start = (v \ "current_invoice_start").toOption.map(_.as[String]),
    current_invoice_end = (v \ "current_invoice_end").toOption.map(_.as[String]),
    days_until_due = (v \ "days_until_due").toOption.map(_.as[Int]),
    cancel_at_period_end = (v \ "cancel_at_period_end").toOption.map(_.as[Int]),
    generate_invoice_at_period_start = (v \ "generate_invoice_at_period_start").toOption.map(_.as[Int]),
    plans = (v \ "plans").toOption.map(x => x.as[JsArray].value.map(_.as[SubscriptionPlanDetail])).get.toSeq,
    tax_template = (v \ "tax_template").toOption.map(_.as[String]),
    apply_additional_discount = (v \ "apply_additional_discount").toOption.map(_.as[String]),
    additional_discount_amount = (v \ "additional_discount_amount").toOption.map(_.as[Double]),
    invoices = (v \ "invoices").toOption.map(x => x.as[JsArray].value.map(_.as[SubscriptionInvoice]).toSeq)
  )

  implicit val reads: Reads[Subscription] = Reads[Subscription] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Subscription") => JsSuccess(Subscription(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
