package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, Js<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class LoyaltyProgramCollection(
    name: String,
    tier_name: String,
    min_spent: Option[Double],
    collection_factor: Double
)

object LoyaltyProgramCollection {
  val NAME_FIELD = "name"

  def apply(v: JsValue): LoyaltyProgramCollection = new LoyaltyProgramCollection(
    name = (v \ "name").get.as[String],
    tier_name = (v \ "tier_name").get.as[String],
    min_spent = (v \ "min_spent").toOption.map(_.as[Double]),
    collection_factor = (v \ "collection_factor").get.as[Double]
  )

  implicit val reads: Reads[LoyaltyProgramCollection] = Reads[LoyaltyProgramCollection] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Loyalty Program Collection") => JsSuccess(LoyaltyProgramCollection(js))
      case Some(_)                            => JsError("Wrong Doctype")
      case _                                  => JsError("Doctype not Found")
    }
  }

}
