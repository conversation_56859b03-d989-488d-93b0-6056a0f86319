package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BudgetAccount(
    name: String,
    account: String,
    budget_amount: Double
)

object BudgetAccount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BudgetAccount = new BudgetAccount(
    name = (v \ "name").get.as[String],
    account = (v \ "account").get.as[String],
    budget_amount = (v \ "budget_amount").get.as[Double]
  )

  implicit val reads: Reads[BudgetAccount] = Reads[BudgetAccount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Budget Account") => JsSuccess(BudgetAccount(js))
      case Some(_)                => JsError("Wrong Doctype")
      case _                      => JsError("Doctype not Found")
    }
  }

}
