package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DefaultValue(
    name: String,
    defkey: String,
    defvalue: Option[String]
)

object DefaultValue {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DefaultValue = new DefaultValue(
    name = (v \ "name").get.as[String],
    defkey = (v \ "defkey").get.as[String],
    defvalue = (v \ "defvalue").toOption.map(_.as[String])
  )

  implicit val reads: Reads[DefaultValue] = Reads[DefaultValue] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("DefaultValue") => JsSuccess(DefaultValue(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
