package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PriceList(
    enabled: Option[Int],
    price_list_name: String,
    currency: String,
    buying: Option[Int],
    selling: Option[Int],
    price_not_uom_dependent: Option[Int],
    countries: Option[Seq[PriceListCountry]]
)

object PriceList {
  val NAME_FIELD = "price_list_name"

  def apply(v: JsValue): PriceList = new PriceList(
    enabled = (v \ "enabled").toOption.map(_.as[Int]),
    price_list_name = (v \ "price_list_name").get.as[String],
    currency = (v \ "currency").get.as[String],
    buying = (v \ "buying").toOption.map(_.as[Int]),
    selling = (v \ "selling").toOption.map(_.as[Int]),
    price_not_uom_dependent = (v \ "price_not_uom_dependent").toOption.map(_.as[Int]),
    countries = (v \ "countries").toOption.map(x => x.as[JsArray].value.map(_.as[PriceListCountry]).toSeq)
  )

  implicit val reads: Reads[PriceList] = Reads[PriceList] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Price List") => JsSuccess(PriceList(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
