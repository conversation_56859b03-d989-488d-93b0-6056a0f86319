package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>sV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DeliveryNote(
    name: String,
    title: Option[String],
    naming_series: String,
    customer: String,
    customer_name: Option[String],
    amended_from: Option[String],
    company: String,
    posting_date: String,
    set_posting_time: Option[Int],
    is_return: Option[Int],
    issue_credit_note: Option[Int],
    return_against: Option[String],
    po_no: Option[String],
    pick_list: Option[String],
    po_date: Option[String],
    shipping_address_name: Option[String],
    contact_person: Option[String],
    contact_email: Option[String],
    customer_address: Option[String],
    tax_id: Option[String],
    company_address: Option[String],
    currency: String,
    conversion_rate: Double,
    selling_price_list: String,
    price_list_currency: String,
    plc_conversion_rate: Double,
    ignore_pricing_rule: Option[Int],
    set_warehouse: Option[String],
    to_warehouse: Option[String],
    scan_barcode: Option[String],
    items: Seq[DeliveryNoteItem],
    pricing_rules: Option[Seq[PricingRuleDetail]],
    packed_items: Option[Seq[PackedItem]],
    total_qty: Option[Double],
    base_total: Option[Double],
    base_net_total: Option[Double],
    total: Option[Double],
    net_total: Option[Double],
    total_net_weight: Option[Double],
    tax_category: Option[String],
    shipping_rule: Option[String],
    taxes_and_charges: Option[String],
    taxes: Option[Seq[SalesTaxesandCharges]],
    base_total_taxes_and_charges: Option[Double],
    total_taxes_and_charges: Option[Double],
    apply_discount_on: Option[String],
    base_discount_amount: Option[Double],
    additional_discount_percentage: Option[Double],
    discount_amount: Option[Double],
    base_grand_total: Option[Double],
    base_rounding_adjustment: Option[Double],
    base_rounded_total: Option[Double],
    base_in_words: Option[String],
    grand_total: Option[Double],
    rounding_adjustment: Option[Double],
    rounded_total: Option[Double],
    in_words: Option[String],
    tc_name: Option[String],
    terms: Option[String],
    transporter: Option[String],
    driver: Option[String],
    lr_no: Option[String],
    vehicle_no: Option[String],
    transporter_name: Option[String],
    driver_name: Option[String],
    lr_date: Option[String],
    project: Option[String],
    campaign: Option[String],
    source: Option[String],
    customer_group: Option[String],
    territory: Option[String],
    letter_head: Option[String],
    select_print_heading: Option[String],
    language: Option[String],
    print_without_amount: Option[Int],
    group_same_items: Option[Int],
    status: String,
    installation_status: Option[String],
    excise_page: Option[String],
    instructions: Option[String],
    auto_repeat: Option[String],
    sales_partner: Option[String],
    commission_rate: Option[Double],
    total_commission: Option[Double],
    sales_team: Option[Seq[SalesTeam]]
)

object DeliveryNote {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DeliveryNote = new DeliveryNote(
    name = (v \ "name").get.as[String],
    title = (v \ "title").toOption.map(_.as[String]),
    naming_series = (v \ "naming_series").get.as[String],
    customer = (v \ "customer").get.as[String],
    customer_name = (v \ "customer_name").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    company = (v \ "company").get.as[String],
    posting_date = (v \ "posting_date").get.as[String],
    set_posting_time = (v \ "set_posting_time").toOption.map(_.as[Int]),
    is_return = (v \ "is_return").toOption.map(_.as[Int]),
    issue_credit_note = (v \ "issue_credit_note").toOption.map(_.as[Int]),
    return_against = (v \ "return_against").toOption.map(_.as[String]),
    po_no = (v \ "po_no").toOption.map(_.as[String]),
    pick_list = (v \ "pick_list").toOption.map(_.as[String]),
    po_date = (v \ "po_date").toOption.map(_.as[String]),
    shipping_address_name = (v \ "shipping_address_name").toOption.map(_.as[String]),
    contact_person = (v \ "contact_person").toOption.map(_.as[String]),
    contact_email = (v \ "contact_email").toOption.map(_.as[String]),
    customer_address = (v \ "customer_address").toOption.map(_.as[String]),
    tax_id = (v \ "tax_id").toOption.map(_.as[String]),
    company_address = (v \ "company_address").toOption.map(_.as[String]),
    currency = (v \ "currency").get.as[String],
    conversion_rate = (v \ "conversion_rate").get.as[Double],
    selling_price_list = (v \ "selling_price_list").get.as[String],
    price_list_currency = (v \ "price_list_currency").get.as[String],
    plc_conversion_rate = (v \ "plc_conversion_rate").get.as[Double],
    ignore_pricing_rule = (v \ "ignore_pricing_rule").toOption.map(_.as[Int]),
    set_warehouse = (v \ "set_warehouse").toOption.map(_.as[String]),
    to_warehouse = (v \ "to_warehouse").toOption.map(_.as[String]),
    scan_barcode = (v \ "scan_barcode").toOption.map(_.as[String]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[DeliveryNoteItem])).get.toSeq,
    pricing_rules = (v \ "pricing_rules").toOption.map(x => x.as[JsArray].value.map(_.as[PricingRuleDetail]).toSeq),
    packed_items = (v \ "packed_items").toOption.map(x => x.as[JsArray].value.map(_.as[PackedItem]).toSeq),
    total_qty = (v \ "total_qty").toOption.map(_.as[Double]),
    base_total = (v \ "base_total").toOption.map(_.as[Double]),
    base_net_total = (v \ "base_net_total").toOption.map(_.as[Double]),
    total = (v \ "total").toOption.map(_.as[Double]),
    net_total = (v \ "net_total").toOption.map(_.as[Double]),
    total_net_weight = (v \ "total_net_weight").toOption.map(_.as[Double]),
    tax_category = (v \ "tax_category").toOption.map(_.as[String]),
    shipping_rule = (v \ "shipping_rule").toOption.map(_.as[String]),
    taxes_and_charges = (v \ "taxes_and_charges").toOption.map(_.as[String]),
    taxes = (v \ "taxes").toOption.map(x => x.as[JsArray].value.map(_.as[SalesTaxesandCharges]).toSeq),
    base_total_taxes_and_charges = (v \ "base_total_taxes_and_charges").toOption.map(_.as[Double]),
    total_taxes_and_charges = (v \ "total_taxes_and_charges").toOption.map(_.as[Double]),
    apply_discount_on = (v \ "apply_discount_on").toOption.map(_.as[String]),
    base_discount_amount = (v \ "base_discount_amount").toOption.map(_.as[Double]),
    additional_discount_percentage = (v \ "additional_discount_percentage").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    base_grand_total = (v \ "base_grand_total").toOption.map(_.as[Double]),
    base_rounding_adjustment = (v \ "base_rounding_adjustment").toOption.map(_.as[Double]),
    base_rounded_total = (v \ "base_rounded_total").toOption.map(_.as[Double]),
    base_in_words = (v \ "base_in_words").toOption.map(_.as[String]),
    grand_total = (v \ "grand_total").toOption.map(_.as[Double]),
    rounding_adjustment = (v \ "rounding_adjustment").toOption.map(_.as[Double]),
    rounded_total = (v \ "rounded_total").toOption.map(_.as[Double]),
    in_words = (v \ "in_words").toOption.map(_.as[String]),
    tc_name = (v \ "tc_name").toOption.map(_.as[String]),
    terms = (v \ "terms").toOption.map(_.as[String]),
    transporter = (v \ "transporter").toOption.map(_.as[String]),
    driver = (v \ "driver").toOption.map(_.as[String]),
    lr_no = (v \ "lr_no").toOption.map(_.as[String]),
    vehicle_no = (v \ "vehicle_no").toOption.map(_.as[String]),
    transporter_name = (v \ "transporter_name").toOption.map(_.as[String]),
    driver_name = (v \ "driver_name").toOption.map(_.as[String]),
    lr_date = (v \ "lr_date").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    campaign = (v \ "campaign").toOption.map(_.as[String]),
    source = (v \ "source").toOption.map(_.as[String]),
    customer_group = (v \ "customer_group").toOption.map(_.as[String]),
    territory = (v \ "territory").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    select_print_heading = (v \ "select_print_heading").toOption.map(_.as[String]),
    language = (v \ "language").toOption.map(_.as[String]),
    print_without_amount = (v \ "print_without_amount").toOption.map(_.as[Int]),
    group_same_items = (v \ "group_same_items").toOption.map(_.as[Int]),
    status = (v \ "status").get.as[String],
    installation_status = (v \ "installation_status").toOption.map(_.as[String]),
    excise_page = (v \ "excise_page").toOption.map(_.as[String]),
    instructions = (v \ "instructions").toOption.map(_.as[String]),
    auto_repeat = (v \ "auto_repeat").toOption.map(_.as[String]),
    sales_partner = (v \ "sales_partner").toOption.map(_.as[String]),
    commission_rate = (v \ "commission_rate").toOption.map(_.as[Double]),
    total_commission = (v \ "total_commission").toOption.map(_.as[Double]),
    sales_team = (v \ "sales_team").toOption.map(x => x.as[JsArray].value.map(_.as[SalesTeam]).toSeq)
  )

  implicit val reads: Reads[DeliveryNote] = Reads[DeliveryNote] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Delivery Note") => JsSuccess(DeliveryNote(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
