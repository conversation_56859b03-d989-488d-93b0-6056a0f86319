package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SystemSettings(
    name: String,
    country: Option[String],
    language: String,
    time_zone: String,
    is_first_startup: Option[Int],
    setup_complete: Option[Int],
    date_format: String,
    time_format: String,
    number_format: String,
    float_precision: Option[String],
    currency_precision: Option[String],
    backup_limit: Option[Int],
    enable_scheduler: Option[Int],
    dormant_days: Option[Int],
    apply_strict_user_permissions: Option[Int],
    allow_guests_to_upload_files: Option[Int],
    session_expiry: Option[String],
    session_expiry_mobile: Option[String],
    deny_multiple_sessions: Option[Int],
    allow_login_using_mobile_number: Option[Int],
    allow_login_using_user_name: Option[Int],
    allow_error_traceback: Option[Int],
    force_user_to_reset_password: Option[Int],
    enable_password_policy: Option[Int],
    minimum_password_score: Option[String],
    allow_consecutive_login_attempts: Option[Int],
    allow_login_after_fail: Option[Int],
    enable_two_factor_auth: Option[Int],
    bypass_2fa_for_retricted_ip_users: Option[Int],
    bypass_restrict_ip_check_if_2fa_enabled: Option[Int],
    two_factor_method: Option[String],
    lifespan_qrcode_image: Option[Int],
    otp_issuer_name: Option[String],
    disable_standard_email_footer: Option[Int],
    hide_footer_in_auto_email_reports: Option[Int],
    enable_chat: Option[Int],
    use_socketio_to_upload_file: Option[Int]
)

object SystemSettings {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SystemSettings = new SystemSettings(
    name = (v \ "name").get.as[String],
    country = (v \ "country").toOption.map(_.as[String]),
    language = (v \ "language").get.as[String],
    time_zone = (v \ "time_zone").get.as[String],
    is_first_startup = (v \ "is_first_startup").toOption.map(_.as[Int]),
    setup_complete = (v \ "setup_complete").toOption.map(_.as[Int]),
    date_format = (v \ "date_format").get.as[String],
    time_format = (v \ "time_format").get.as[String],
    number_format = (v \ "number_format").get.as[String],
    float_precision = (v \ "float_precision").toOption.map(_.as[String]),
    currency_precision = (v \ "currency_precision").toOption.map(_.as[String]),
    backup_limit = (v \ "backup_limit").toOption.map(_.as[Int]),
    enable_scheduler = (v \ "enable_scheduler").toOption.map(_.as[Int]),
    dormant_days = (v \ "dormant_days").toOption.map(_.as[Int]),
    apply_strict_user_permissions = (v \ "apply_strict_user_permissions").toOption.map(_.as[Int]),
    allow_guests_to_upload_files = (v \ "allow_guests_to_upload_files").toOption.map(_.as[Int]),
    session_expiry = (v \ "session_expiry").toOption.map(_.as[String]),
    session_expiry_mobile = (v \ "session_expiry_mobile").toOption.map(_.as[String]),
    deny_multiple_sessions = (v \ "deny_multiple_sessions").toOption.map(_.as[Int]),
    allow_login_using_mobile_number = (v \ "allow_login_using_mobile_number").toOption.map(_.as[Int]),
    allow_login_using_user_name = (v \ "allow_login_using_user_name").toOption.map(_.as[Int]),
    allow_error_traceback = (v \ "allow_error_traceback").toOption.map(_.as[Int]),
    force_user_to_reset_password = (v \ "force_user_to_reset_password").toOption.map(_.as[Int]),
    enable_password_policy = (v \ "enable_password_policy").toOption.map(_.as[Int]),
    minimum_password_score = (v \ "minimum_password_score").toOption.map(_.as[String]),
    allow_consecutive_login_attempts = (v \ "allow_consecutive_login_attempts").toOption.map(_.as[Int]),
    allow_login_after_fail = (v \ "allow_login_after_fail").toOption.map(_.as[Int]),
    enable_two_factor_auth = (v \ "enable_two_factor_auth").toOption.map(_.as[Int]),
    bypass_2fa_for_retricted_ip_users = (v \ "bypass_2fa_for_retricted_ip_users").toOption.map(_.as[Int]),
    bypass_restrict_ip_check_if_2fa_enabled = (v \ "bypass_restrict_ip_check_if_2fa_enabled").toOption.map(_.as[Int]),
    two_factor_method = (v \ "two_factor_method").toOption.map(_.as[String]),
    lifespan_qrcode_image = (v \ "lifespan_qrcode_image").toOption.map(_.as[Int]),
    otp_issuer_name = (v \ "otp_issuer_name").toOption.map(_.as[String]),
    disable_standard_email_footer = (v \ "disable_standard_email_footer").toOption.map(_.as[Int]),
    hide_footer_in_auto_email_reports = (v \ "hide_footer_in_auto_email_reports").toOption.map(_.as[Int]),
    enable_chat = (v \ "enable_chat").toOption.map(_.as[Int]),
    use_socketio_to_upload_file = (v \ "use_socketio_to_upload_file").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[SystemSettings] = Reads[SystemSettings] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("System Settings") => JsSuccess(SystemSettings(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
