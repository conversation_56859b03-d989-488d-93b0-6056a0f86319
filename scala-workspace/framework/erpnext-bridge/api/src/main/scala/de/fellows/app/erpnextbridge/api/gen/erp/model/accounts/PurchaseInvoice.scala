package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PurchaseInvoice(
    name: String,
    title: Option[String],
    naming_series: String,
    supplier: Option[String],
    supplier_name: Option[String],
    due_date: Option[String],
    is_paid: Option[Int],
    is_return: Option[Int],
    apply_tds: Option[Int],
    company: Option[String],
    posting_date: String,
    set_posting_time: Option[Int],
    amended_from: Option[String],
    cost_center: Option[String],
    on_hold: Option[Int],
    release_date: Option[String],
    bill_no: Option[String],
    bill_date: Option[String],
    return_against: Option[String],
    supplier_address: Option[String],
    contact_person: Option[String],
    shipping_address: Option[String],
    currency: Option[String],
    conversion_rate: Option[Double],
    buying_price_list: Option[String],
    price_list_currency: Option[String],
    plc_conversion_rate: Option[Double],
    ignore_pricing_rule: Option[Int],
    set_warehouse: Option[String],
    rejected_warehouse: Option[String],
    is_subcontracted: Option[String],
    supplier_warehouse: Option[String],
    update_stock: Option[Int],
    scan_barcode: Option[String],
    items: Seq[PurchaseInvoiceItem],
    pricing_rules: Option[Seq[PricingRuleDetail]],
    total_qty: Option[Double],
    base_total: Option[Double],
    base_net_total: Option[Double],
    total: Option[Double],
    net_total: Option[Double],
    total_net_weight: Option[Double],
    tax_category: Option[String],
    shipping_rule: Option[String],
    taxes_and_charges: Option[String],
    taxes: Option[Seq[PurchaseTaxesandCharges]],
    base_taxes_and_charges_added: Option[Double],
    base_taxes_and_charges_deducted: Option[Double],
    base_total_taxes_and_charges: Option[Double],
    taxes_and_charges_added: Option[Double],
    taxes_and_charges_deducted: Option[Double],
    total_taxes_and_charges: Option[Double],
    apply_discount_on: Option[String],
    base_discount_amount: Option[Double],
    additional_discount_percentage: Option[Double],
    discount_amount: Option[Double],
    base_grand_total: Option[Double],
    base_rounding_adjustment: Option[Double],
    base_rounded_total: Option[Double],
    base_in_words: Option[String],
    grand_total: Option[Double],
    rounding_adjustment: Option[Double],
    rounded_total: Option[Double],
    in_words: Option[String],
    total_advance: Option[Double],
    outstanding_amount: Option[Double],
    disable_rounded_total: Option[Int],
    mode_of_payment: Option[String],
    cash_bank_account: Option[String],
    clearance_date: Option[String],
    paid_amount: Option[Double],
    base_paid_amount: Option[Double],
    write_off_amount: Option[Double],
    base_write_off_amount: Option[Double],
    write_off_account: Option[String],
    write_off_cost_center: Option[String],
    allocate_advances_automatically: Option[Int],
    advances: Option[Seq[PurchaseInvoiceAdvance]],
    payment_terms_template: Option[String],
    payment_schedule: Option[Seq[PaymentSchedule]],
    tc_name: Option[String],
    terms: Option[String],
    letter_head: Option[String],
    group_same_items: Option[Int],
    select_print_heading: Option[String],
    language: Option[String],
    credit_to: String,
    party_account_currency: Option[String],
    is_opening: Option[String],
    status: Option[String],
    inter_company_invoice_reference: Option[String],
    from_date: Option[String],
    to_date: Option[String],
    auto_repeat: Option[String]
)

object PurchaseInvoice {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PurchaseInvoice = new PurchaseInvoice(
    name = (v \ "name").get.as[String],
    title = (v \ "title").toOption.map(_.as[String]),
    naming_series = (v \ "naming_series").get.as[String],
    supplier = (v \ "supplier").toOption.map(_.as[String]),
    supplier_name = (v \ "supplier_name").toOption.map(_.as[String]),
    due_date = (v \ "due_date").toOption.map(_.as[String]),
    is_paid = (v \ "is_paid").toOption.map(_.as[Int]),
    is_return = (v \ "is_return").toOption.map(_.as[Int]),
    apply_tds = (v \ "apply_tds").toOption.map(_.as[Int]),
    company = (v \ "company").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").get.as[String],
    set_posting_time = (v \ "set_posting_time").toOption.map(_.as[Int]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    on_hold = (v \ "on_hold").toOption.map(_.as[Int]),
    release_date = (v \ "release_date").toOption.map(_.as[String]),
    bill_no = (v \ "bill_no").toOption.map(_.as[String]),
    bill_date = (v \ "bill_date").toOption.map(_.as[String]),
    return_against = (v \ "return_against").toOption.map(_.as[String]),
    supplier_address = (v \ "supplier_address").toOption.map(_.as[String]),
    contact_person = (v \ "contact_person").toOption.map(_.as[String]),
    shipping_address = (v \ "shipping_address").toOption.map(_.as[String]),
    currency = (v \ "currency").toOption.map(_.as[String]),
    conversion_rate = (v \ "conversion_rate").toOption.map(_.as[Double]),
    buying_price_list = (v \ "buying_price_list").toOption.map(_.as[String]),
    price_list_currency = (v \ "price_list_currency").toOption.map(_.as[String]),
    plc_conversion_rate = (v \ "plc_conversion_rate").toOption.map(_.as[Double]),
    ignore_pricing_rule = (v \ "ignore_pricing_rule").toOption.map(_.as[Int]),
    set_warehouse = (v \ "set_warehouse").toOption.map(_.as[String]),
    rejected_warehouse = (v \ "rejected_warehouse").toOption.map(_.as[String]),
    is_subcontracted = (v \ "is_subcontracted").toOption.map(_.as[String]),
    supplier_warehouse = (v \ "supplier_warehouse").toOption.map(_.as[String]),
    update_stock = (v \ "update_stock").toOption.map(_.as[Int]),
    scan_barcode = (v \ "scan_barcode").toOption.map(_.as[String]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[PurchaseInvoiceItem])).get.toSeq,
    pricing_rules = (v \ "pricing_rules").toOption.map(x => x.as[JsArray].value.map(_.as[PricingRuleDetail]).toSeq),
    total_qty = (v \ "total_qty").toOption.map(_.as[Double]),
    base_total = (v \ "base_total").toOption.map(_.as[Double]),
    base_net_total = (v \ "base_net_total").toOption.map(_.as[Double]),
    total = (v \ "total").toOption.map(_.as[Double]),
    net_total = (v \ "net_total").toOption.map(_.as[Double]),
    total_net_weight = (v \ "total_net_weight").toOption.map(_.as[Double]),
    tax_category = (v \ "tax_category").toOption.map(_.as[String]),
    shipping_rule = (v \ "shipping_rule").toOption.map(_.as[String]),
    taxes_and_charges = (v \ "taxes_and_charges").toOption.map(_.as[String]),
    taxes = (v \ "taxes").toOption.map(x => x.as[JsArray].value.map(_.as[PurchaseTaxesandCharges]).toSeq),
    base_taxes_and_charges_added = (v \ "base_taxes_and_charges_added").toOption.map(_.as[Double]),
    base_taxes_and_charges_deducted = (v \ "base_taxes_and_charges_deducted").toOption.map(_.as[Double]),
    base_total_taxes_and_charges = (v \ "base_total_taxes_and_charges").toOption.map(_.as[Double]),
    taxes_and_charges_added = (v \ "taxes_and_charges_added").toOption.map(_.as[Double]),
    taxes_and_charges_deducted = (v \ "taxes_and_charges_deducted").toOption.map(_.as[Double]),
    total_taxes_and_charges = (v \ "total_taxes_and_charges").toOption.map(_.as[Double]),
    apply_discount_on = (v \ "apply_discount_on").toOption.map(_.as[String]),
    base_discount_amount = (v \ "base_discount_amount").toOption.map(_.as[Double]),
    additional_discount_percentage = (v \ "additional_discount_percentage").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    base_grand_total = (v \ "base_grand_total").toOption.map(_.as[Double]),
    base_rounding_adjustment = (v \ "base_rounding_adjustment").toOption.map(_.as[Double]),
    base_rounded_total = (v \ "base_rounded_total").toOption.map(_.as[Double]),
    base_in_words = (v \ "base_in_words").toOption.map(_.as[String]),
    grand_total = (v \ "grand_total").toOption.map(_.as[Double]),
    rounding_adjustment = (v \ "rounding_adjustment").toOption.map(_.as[Double]),
    rounded_total = (v \ "rounded_total").toOption.map(_.as[Double]),
    in_words = (v \ "in_words").toOption.map(_.as[String]),
    total_advance = (v \ "total_advance").toOption.map(_.as[Double]),
    outstanding_amount = (v \ "outstanding_amount").toOption.map(_.as[Double]),
    disable_rounded_total = (v \ "disable_rounded_total").toOption.map(_.as[Int]),
    mode_of_payment = (v \ "mode_of_payment").toOption.map(_.as[String]),
    cash_bank_account = (v \ "cash_bank_account").toOption.map(_.as[String]),
    clearance_date = (v \ "clearance_date").toOption.map(_.as[String]),
    paid_amount = (v \ "paid_amount").toOption.map(_.as[Double]),
    base_paid_amount = (v \ "base_paid_amount").toOption.map(_.as[Double]),
    write_off_amount = (v \ "write_off_amount").toOption.map(_.as[Double]),
    base_write_off_amount = (v \ "base_write_off_amount").toOption.map(_.as[Double]),
    write_off_account = (v \ "write_off_account").toOption.map(_.as[String]),
    write_off_cost_center = (v \ "write_off_cost_center").toOption.map(_.as[String]),
    allocate_advances_automatically = (v \ "allocate_advances_automatically").toOption.map(_.as[Int]),
    advances = (v \ "advances").toOption.map(x => x.as[JsArray].value.map(_.as[PurchaseInvoiceAdvance]).toSeq),
    payment_terms_template = (v \ "payment_terms_template").toOption.map(_.as[String]),
    payment_schedule = (v \ "payment_schedule").toOption.map(x => x.as[JsArray].value.map(_.as[PaymentSchedule]).toSeq),
    tc_name = (v \ "tc_name").toOption.map(_.as[String]),
    terms = (v \ "terms").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    group_same_items = (v \ "group_same_items").toOption.map(_.as[Int]),
    select_print_heading = (v \ "select_print_heading").toOption.map(_.as[String]),
    language = (v \ "language").toOption.map(_.as[String]),
    credit_to = (v \ "credit_to").get.as[String],
    party_account_currency = (v \ "party_account_currency").toOption.map(_.as[String]),
    is_opening = (v \ "is_opening").toOption.map(_.as[String]),
    status = (v \ "status").toOption.map(_.as[String]),
    inter_company_invoice_reference = (v \ "inter_company_invoice_reference").toOption.map(_.as[String]),
    from_date = (v \ "from_date").toOption.map(_.as[String]),
    to_date = (v \ "to_date").toOption.map(_.as[String]),
    auto_repeat = (v \ "auto_repeat").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PurchaseInvoice] = Reads[PurchaseInvoice] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Purchase Invoice") => JsSuccess(PurchaseInvoice(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
