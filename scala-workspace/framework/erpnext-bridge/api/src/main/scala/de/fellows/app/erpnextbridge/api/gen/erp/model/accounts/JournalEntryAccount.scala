package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class JournalEntryAccount(
    name: String,
    account: String,
    account_type: Option[String],
    balance: Option[Double],
    bank_account: Option[String],
    party_type: Option[String],
    party_balance: Option[Double],
    cost_center: Option[String],
    account_currency: Option[String],
    exchange_rate: Option[Double],
    debit_in_account_currency: Option[Double],
    debit: Option[Double],
    credit_in_account_currency: Option[Double],
    credit: Option[Double],
    reference_type: Option[String],
    reference_due_date: Option[String],
    project: Option[String],
    is_advance: Option[String],
    against_account: Option[String]
)

object JournalEntryAccount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): JournalEntryAccount = new JournalEntryAccount(
    name = (v \ "name").get.as[String],
    account = (v \ "account").get.as[String],
    account_type = (v \ "account_type").toOption.map(_.as[String]),
    balance = (v \ "balance").toOption.map(_.as[Double]),
    bank_account = (v \ "bank_account").toOption.map(_.as[String]),
    party_type = (v \ "party_type").toOption.map(_.as[String]),
    party_balance = (v \ "party_balance").toOption.map(_.as[Double]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    account_currency = (v \ "account_currency").toOption.map(_.as[String]),
    exchange_rate = (v \ "exchange_rate").toOption.map(_.as[Double]),
    debit_in_account_currency = (v \ "debit_in_account_currency").toOption.map(_.as[Double]),
    debit = (v \ "debit").toOption.map(_.as[Double]),
    credit_in_account_currency = (v \ "credit_in_account_currency").toOption.map(_.as[Double]),
    credit = (v \ "credit").toOption.map(_.as[Double]),
    reference_type = (v \ "reference_type").toOption.map(_.as[String]),
    reference_due_date = (v \ "reference_due_date").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    is_advance = (v \ "is_advance").toOption.map(_.as[String]),
    against_account = (v \ "against_account").toOption.map(_.as[String])
  )

  implicit val reads: Reads[JournalEntryAccount] = Reads[JournalEntryAccount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Journal Entry Account") => JsSuccess(JournalEntryAccount(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
