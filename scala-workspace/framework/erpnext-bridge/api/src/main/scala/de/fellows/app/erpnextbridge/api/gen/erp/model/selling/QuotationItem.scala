package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class QuotationItem(
    name: String,
    item_code: String,
    customer_item_code: Option[String],
    item_name: String,
    description: String,
    item_group: Option[String],
    brand: Option[String],
    qty: Double,
    stock_uom: Option[String],
    uom: String,
    conversion_factor: Double,
    stock_qty: Option[Double],
    price_list_rate: Option[Double],
    base_price_list_rate: Option[Double],
    margin_type: Option[String],
    margin_rate_or_amount: Option[Double],
    rate_with_margin: Option[Double],
    discount_amount: Option[Double],
    base_rate_with_margin: Option[Double],
    rate: Option[Double],
    net_rate: Option[Double],
    amount: Option[Double],
    net_amount: Option[Double],
    item_tax_template: Option[String],
    base_rate: Option[Double],
    base_net_rate: Option[Double],
    base_amount: Option[Double],
    base_net_amount: Option[Double],
    is_free_item: Option[Int],
    weight_per_unit: Option[Double],
    total_weight: Option[Double],
    weight_uom: Option[String],
    warehouse: Option[String],
    prevdoc_doctype: Option[String],
    projected_qty: Option[Double],
    actual_qty: Option[Double],
    additional_notes: Option[String],
    page_break: Option[Int]
)

object QuotationItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): QuotationItem = new QuotationItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    customer_item_code = (v \ "customer_item_code").toOption.map(_.as[String]),
    item_name = (v \ "item_name").get.as[String],
    description = (v \ "description").get.as[String],
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    brand = (v \ "brand").toOption.map(_.as[String]),
    qty = (v \ "qty").get.as[Double],
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    uom = (v \ "uom").get.as[String],
    conversion_factor = (v \ "conversion_factor").get.as[Double],
    stock_qty = (v \ "stock_qty").toOption.map(_.as[Double]),
    price_list_rate = (v \ "price_list_rate").toOption.map(_.as[Double]),
    base_price_list_rate = (v \ "base_price_list_rate").toOption.map(_.as[Double]),
    margin_type = (v \ "margin_type").toOption.map(_.as[String]),
    margin_rate_or_amount = (v \ "margin_rate_or_amount").toOption.map(_.as[Double]),
    rate_with_margin = (v \ "rate_with_margin").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    base_rate_with_margin = (v \ "base_rate_with_margin").toOption.map(_.as[Double]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    net_rate = (v \ "net_rate").toOption.map(_.as[Double]),
    amount = (v \ "amount").toOption.map(_.as[Double]),
    net_amount = (v \ "net_amount").toOption.map(_.as[Double]),
    item_tax_template = (v \ "item_tax_template").toOption.map(_.as[String]),
    base_rate = (v \ "base_rate").toOption.map(_.as[Double]),
    base_net_rate = (v \ "base_net_rate").toOption.map(_.as[Double]),
    base_amount = (v \ "base_amount").toOption.map(_.as[Double]),
    base_net_amount = (v \ "base_net_amount").toOption.map(_.as[Double]),
    is_free_item = (v \ "is_free_item").toOption.map(_.as[Int]),
    weight_per_unit = (v \ "weight_per_unit").toOption.map(_.as[Double]),
    total_weight = (v \ "total_weight").toOption.map(_.as[Double]),
    weight_uom = (v \ "weight_uom").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    prevdoc_doctype = (v \ "prevdoc_doctype").toOption.map(_.as[String]),
    projected_qty = (v \ "projected_qty").toOption.map(_.as[Double]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    additional_notes = (v \ "additional_notes").toOption.map(_.as[String]),
    page_break = (v \ "page_break").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[QuotationItem] = Reads[QuotationItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Quotation Item") => JsSuccess(QuotationItem(js))
      case Some(_)                => JsError("Wrong Doctype")
      case _                      => JsError("Doctype not Found")
    }
  }

}
