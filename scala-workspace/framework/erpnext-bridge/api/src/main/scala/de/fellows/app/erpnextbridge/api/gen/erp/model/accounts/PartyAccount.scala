package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PartyAccount(
    name: String,
    company: String,
    account: String
)

object PartyAccount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PartyAccount = new PartyAccount(
    name = (v \ "name").get.as[String],
    company = (v \ "company").get.as[String],
    account = (v \ "account").get.as[String]
  )

  implicit val reads: Reads[PartyAccount] = Reads[PartyAccount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Party Account") => JsSuccess(PartyAccount(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
