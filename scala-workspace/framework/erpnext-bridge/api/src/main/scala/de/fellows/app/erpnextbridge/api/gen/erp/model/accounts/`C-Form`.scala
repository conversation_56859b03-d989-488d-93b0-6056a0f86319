package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads}

case class `C-Form`(
    name: String,
    naming_series: String,
    c_form_no: String,
    received_date: String,
    customer: String,
    company: Option[String],
    quarter: Option[String],
    total_amount: Double,
    state: String,
    invoices: Option[Seq[`C-FormInvoiceDetail`]],
    total_invoiced_amount: Option[Double],
    amended_from: Option[String]
)

object `C-Form` {
  val NAME_FIELD = "name"

  def apply(v: JsValue): `C-Form` = new `C-Form`(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    c_form_no = (v \ "c_form_no").get.as[String],
    received_date = (v \ "received_date").get.as[String],
    customer = (v \ "customer").get.as[String],
    company = (v \ "company").toOption.map(_.as[String]),
    quarter = (v \ "quarter").toOption.map(_.as[String]),
    total_amount = (v \ "total_amount").get.as[Double],
    state = (v \ "state").get.as[String],
    invoices = (v \ "invoices").toOption.map(x => x.as[JsArray].value.map(_.as[`C-FormInvoiceDetail`]).toSeq),
    total_invoiced_amount = (v \ "total_invoiced_amount").toOption.map(_.as[Double]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[`C-Form`] = Reads[`C-Form`] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("C-Form") => JsSuccess(`C-Form`(js))
      case Some(_)        => JsError("Wrong Doctype")
      case _              => JsError("Doctype not Found")
    }
  }

}
