package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PurchaseReceiptItem(
    name: String,
    barcode: Option[String],
    item_code: String,
    supplier_part_no: Option[String],
    item_name: String,
    description: String,
    item_group: Option[String],
    brand: Option[String],
    manufacturer: Option[String],
    manufacturer_part_no: Option[String],
    received_qty: Double,
    qty: Option[Double],
    rejected_qty: Option[Double],
    uom: String,
    stock_uom: String,
    conversion_factor: Double,
    retain_sample: Option[Int],
    sample_quantity: Option[Int],
    price_list_rate: Option[Double],
    discount_amount: Option[Double],
    base_price_list_rate: Option[Double],
    rate: Option[Double],
    amount: Option[Double],
    base_rate: Double,
    base_amount: Option[Double],
    is_free_item: Option[Int],
    net_rate: Option[Double],
    net_amount: Option[Double],
    item_tax_template: Option[String],
    base_net_rate: Option[Double],
    base_net_amount: Option[Double],
    valuation_rate: Option[Double],
    item_tax_amount: Option[Double],
    rm_supp_cost: Option[Double],
    landed_cost_voucher_amount: Option[Double],
    billed_amt: Option[Double],
    weight_per_unit: Option[Double],
    total_weight: Option[Double],
    weight_uom: Option[String],
    warehouse: Option[String],
    rejected_warehouse: Option[String],
    purchase_order: Option[String],
    material_request: Option[String],
    is_fixed_asset: Option[Int],
    asset_location: Option[String],
    asset_category: Option[String],
    schedule_date: Option[String],
    quality_inspection: Option[String],
    stock_qty: Option[Double],
    purchase_order_item: Option[String],
    material_request_item: Option[String],
    allow_zero_valuation_rate: Option[Int],
    bom: Option[String],
    batch_no: Option[String],
    expense_account: Option[String],
    include_exploded_items: Option[Int],
    project: Option[String],
    cost_center: Option[String],
    page_break: Option[Int]
)

object PurchaseReceiptItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PurchaseReceiptItem = new PurchaseReceiptItem(
    name = (v \ "name").get.as[String],
    barcode = (v \ "barcode").toOption.map(_.as[String]),
    item_code = (v \ "item_code").get.as[String],
    supplier_part_no = (v \ "supplier_part_no").toOption.map(_.as[String]),
    item_name = (v \ "item_name").get.as[String],
    description = (v \ "description").get.as[String],
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    brand = (v \ "brand").toOption.map(_.as[String]),
    manufacturer = (v \ "manufacturer").toOption.map(_.as[String]),
    manufacturer_part_no = (v \ "manufacturer_part_no").toOption.map(_.as[String]),
    received_qty = (v \ "received_qty").get.as[Double],
    qty = (v \ "qty").toOption.map(_.as[Double]),
    rejected_qty = (v \ "rejected_qty").toOption.map(_.as[Double]),
    uom = (v \ "uom").get.as[String],
    stock_uom = (v \ "stock_uom").get.as[String],
    conversion_factor = (v \ "conversion_factor").get.as[Double],
    retain_sample = (v \ "retain_sample").toOption.map(_.as[Int]),
    sample_quantity = (v \ "sample_quantity").toOption.map(_.as[Int]),
    price_list_rate = (v \ "price_list_rate").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    base_price_list_rate = (v \ "base_price_list_rate").toOption.map(_.as[Double]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    amount = (v \ "amount").toOption.map(_.as[Double]),
    base_rate = (v \ "base_rate").get.as[Double],
    base_amount = (v \ "base_amount").toOption.map(_.as[Double]),
    is_free_item = (v \ "is_free_item").toOption.map(_.as[Int]),
    net_rate = (v \ "net_rate").toOption.map(_.as[Double]),
    net_amount = (v \ "net_amount").toOption.map(_.as[Double]),
    item_tax_template = (v \ "item_tax_template").toOption.map(_.as[String]),
    base_net_rate = (v \ "base_net_rate").toOption.map(_.as[Double]),
    base_net_amount = (v \ "base_net_amount").toOption.map(_.as[Double]),
    valuation_rate = (v \ "valuation_rate").toOption.map(_.as[Double]),
    item_tax_amount = (v \ "item_tax_amount").toOption.map(_.as[Double]),
    rm_supp_cost = (v \ "rm_supp_cost").toOption.map(_.as[Double]),
    landed_cost_voucher_amount = (v \ "landed_cost_voucher_amount").toOption.map(_.as[Double]),
    billed_amt = (v \ "billed_amt").toOption.map(_.as[Double]),
    weight_per_unit = (v \ "weight_per_unit").toOption.map(_.as[Double]),
    total_weight = (v \ "total_weight").toOption.map(_.as[Double]),
    weight_uom = (v \ "weight_uom").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    rejected_warehouse = (v \ "rejected_warehouse").toOption.map(_.as[String]),
    purchase_order = (v \ "purchase_order").toOption.map(_.as[String]),
    material_request = (v \ "material_request").toOption.map(_.as[String]),
    is_fixed_asset = (v \ "is_fixed_asset").toOption.map(_.as[Int]),
    asset_location = (v \ "asset_location").toOption.map(_.as[String]),
    asset_category = (v \ "asset_category").toOption.map(_.as[String]),
    schedule_date = (v \ "schedule_date").toOption.map(_.as[String]),
    quality_inspection = (v \ "quality_inspection").toOption.map(_.as[String]),
    stock_qty = (v \ "stock_qty").toOption.map(_.as[Double]),
    purchase_order_item = (v \ "purchase_order_item").toOption.map(_.as[String]),
    material_request_item = (v \ "material_request_item").toOption.map(_.as[String]),
    allow_zero_valuation_rate = (v \ "allow_zero_valuation_rate").toOption.map(_.as[Int]),
    bom = (v \ "bom").toOption.map(_.as[String]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    expense_account = (v \ "expense_account").toOption.map(_.as[String]),
    include_exploded_items = (v \ "include_exploded_items").toOption.map(_.as[Int]),
    project = (v \ "project").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    page_break = (v \ "page_break").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[PurchaseReceiptItem] = Reads[PurchaseReceiptItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Purchase Receipt Item") => JsSuccess(PurchaseReceiptItem(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
