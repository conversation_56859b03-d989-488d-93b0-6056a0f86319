package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON>rror, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class TaxWithholdingCategory(
    name: String,
    category_name: Option[String],
    rates: Seq[TaxWithholdingRate],
    accounts: Seq[TaxWithholdingAccount]
)

object TaxWithholdingCategory {
  val NAME_FIELD = "name"

  def apply(v: JsValue): TaxWithholdingCategory = new TaxWithholdingCategory(
    name = (v \ "name").get.as[String],
    category_name = (v \ "category_name").toOption.map(_.as[String]),
    rates = (v \ "rates").toOption.map(x => x.as[JsArray].value.map(_.as[TaxWithholdingRate])).get.toSeq,
    accounts = (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[TaxWithholdingAccount])).get.toSeq
  )

  implicit val reads: Reads[TaxWithholdingCategory] = Reads[TaxWithholdingCategory] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Tax Withholding Category") => JsSuccess(TaxWithholdingCategory(js))
      case Some(_)                          => JsError("Wrong Doctype")
      case _                                => JsError("Doctype not Found")
    }
  }

}
