package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankReconciliationDetail(
    name: String,
    payment_document: Option[String],
    against_account: Option[String],
    amount: Option[String],
    posting_date: Option[String],
    cheque_number: Option[String],
    cheque_date: Option[String],
    clearance_date: Option[String]
)

object BankReconciliationDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankReconciliationDetail = new BankReconciliationDetail(
    name = (v \ "name").get.as[String],
    payment_document = (v \ "payment_document").toOption.map(_.as[String]),
    against_account = (v \ "against_account").toOption.map(_.as[String]),
    amount = (v \ "amount").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").toOption.map(_.as[String]),
    cheque_number = (v \ "cheque_number").toOption.map(_.as[String]),
    cheque_date = (v \ "cheque_date").toOption.map(_.as[String]),
    clearance_date = (v \ "clearance_date").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BankReconciliationDetail] = Reads[BankReconciliationDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Reconciliation Detail") => JsSuccess(BankReconciliationDetail(js))
      case Some(_)                            => JsError("Wrong Doctype")
      case _                                  => JsError("Doctype not Found")
    }
  }

}
