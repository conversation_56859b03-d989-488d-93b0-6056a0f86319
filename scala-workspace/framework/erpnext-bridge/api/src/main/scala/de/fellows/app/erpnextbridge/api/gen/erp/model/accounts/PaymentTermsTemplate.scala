package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentTermsTemplate(
    template_name: Option[String],
    terms: Seq[PaymentTermsTemplateDetail]
)

object PaymentTermsTemplate {
  val NAME_FIELD = "template_name"

  def apply(v: JsValue): PaymentTermsTemplate = new PaymentTermsTemplate(
    template_name = (v \ "template_name").toOption.map(_.as[String]),
    terms = (v \ "terms").toOption.map(x => x.as[JsArray].value.map(_.as[PaymentTermsTemplateDetail])).get.toSeq
  )

  implicit val reads: Reads[PaymentTermsTemplate] = Reads[PaymentTermsTemplate] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Terms Template") => JsSuccess(PaymentTermsTemplate(js))
      case Some(_)                        => JsError("Wrong Doctype")
      case _                              => JsError("Doctype not Found")
    }
  }

}
