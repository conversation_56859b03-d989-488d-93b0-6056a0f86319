package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemTax(
    name: String,
    item_tax_template: String,
    tax_category: Option[String],
    valid_from: Option[String]
)

object ItemTax {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemTax = new ItemTax(
    name = (v \ "name").get.as[String],
    item_tax_template = (v \ "item_tax_template").get.as[String],
    tax_category = (v \ "tax_category").toOption.map(_.as[String]),
    valid_from = (v \ "valid_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ItemTax] = Reads[ItemTax] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Tax") => JsSuccess(ItemTax(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
