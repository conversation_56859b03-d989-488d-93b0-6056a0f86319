package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class UserSocialLogin(
    name: String,
    provider: Option[String],
    username: Option[String],
    userid: Option[String]
)

object UserSocialLogin {
  val NAME_FIELD = "name"

  def apply(v: JsValue): UserSocialLogin = new UserSocialLogin(
    name = (v \ "name").get.as[String],
    provider = (v \ "provider").toOption.map(_.as[String]),
    username = (v \ "username").toOption.map(_.as[String]),
    userid = (v \ "userid").toOption.map(_.as[String])
  )

  implicit val reads: Reads[UserSocialLogin] = Reads[UserSocialLogin] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("User Social Login") => JsSuccess(UserSocialLogin(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
