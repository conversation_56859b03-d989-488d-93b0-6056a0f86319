package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class AccessLog(
    name: String,
    export_from: Option[String],
    user: Option[String],
    reference_document: Option[String],
    timestamp: Option[String],
    file_type: Option[String],
    method: Option[String],
    report_name: Option[String]
)

object AccessLog {
  val NAME_FIELD = "name"

  def apply(v: JsValue): AccessLog = new AccessLog(
    name = (v \ "name").get.as[String],
    export_from = (v \ "export_from").toOption.map(_.as[String]),
    user = (v \ "user").toOption.map(_.as[String]),
    reference_document = (v \ "reference_document").toOption.map(_.as[String]),
    timestamp = (v \ "timestamp").toOption.map(_.as[String]),
    file_type = (v \ "file_type").toOption.map(_.as[String]),
    method = (v \ "method").toOption.map(_.as[String]),
    report_name = (v \ "report_name").toOption.map(_.as[String])
  )

  implicit val reads: Reads[AccessLog] = Reads[AccessLog] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Access Log") => JsSuccess(AccessLog(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
