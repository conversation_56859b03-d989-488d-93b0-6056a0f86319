package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class JobCardItem(
    name: String,
    item_code: Option[String],
    source_warehouse: Option[String],
    uom: Option[String],
    item_name: Option[String],
    description: Option[String],
    required_qty: Option[Double],
    allow_alternative_item: Option[Int]
)

object JobCardItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): JobCardItem = new JobCardItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    source_warehouse = (v \ "source_warehouse").toOption.map(_.as[String]),
    uom = (v \ "uom").toOption.map(_.as[String]),
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String]),
    required_qty = (v \ "required_qty").toOption.map(_.as[Double]),
    allow_alternative_item = (v \ "allow_alternative_item").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[JobCardItem] = Reads[JobCardItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Job Card Item") => JsSuccess(JobCardItem(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
