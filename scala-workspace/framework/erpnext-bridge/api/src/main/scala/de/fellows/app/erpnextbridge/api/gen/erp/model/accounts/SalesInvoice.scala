package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SalesInvoice(
    name: String,
    title: Option[String],
    naming_series: String,
    customer: Option[String],
    customer_name: Option[String],
    tax_id: Option[String],
    is_pos: Option[Int],
    pos_profile: Option[String],
    offline_pos_name: Option[String],
    is_return: Option[Int],
    company: String,
    posting_date: String,
    set_posting_time: Option[Int],
    due_date: Option[String],
    amended_from: Option[String],
    return_against: Option[String],
    update_billed_amount_in_sales_order: Option[Int],
    project: Option[String],
    cost_center: Option[String],
    po_no: Option[String],
    po_date: Option[String],
    customer_address: Option[String],
    contact_person: Option[String],
    contact_email: Option[String],
    territory: Option[String],
    shipping_address_name: Option[String],
    company_address: Option[String],
    currency: String,
    conversion_rate: Double,
    selling_price_list: String,
    price_list_currency: String,
    plc_conversion_rate: Double,
    ignore_pricing_rule: Option[Int],
    set_warehouse: Option[String],
    update_stock: Option[Int],
    scan_barcode: Option[String],
    items: Seq[SalesInvoiceItem],
    pricing_rules: Option[Seq[PricingRuleDetail]],
    packed_items: Option[Seq[PackedItem]],
    timesheets: Option[Seq[SalesInvoiceTimesheet]],
    total_billing_amount: Option[Double],
    total_qty: Option[Double],
    base_total: Option[Double],
    base_net_total: Double,
    total: Option[Double],
    net_total: Option[Double],
    total_net_weight: Option[Double],
    taxes_and_charges: Option[String],
    shipping_rule: Option[String],
    tax_category: Option[String],
    taxes: Option[Seq[SalesTaxesandCharges]],
    base_total_taxes_and_charges: Option[Double],
    total_taxes_and_charges: Option[Double],
    loyalty_points: Option[Int],
    loyalty_amount: Option[Double],
    redeem_loyalty_points: Option[Int],
    loyalty_program: Option[String],
    loyalty_redemption_account: Option[String],
    loyalty_redemption_cost_center: Option[String],
    apply_discount_on: Option[String],
    base_discount_amount: Option[Double],
    additional_discount_percentage: Option[Double],
    discount_amount: Option[Double],
    base_grand_total: Double,
    base_rounding_adjustment: Option[Double],
    base_rounded_total: Option[Double],
    base_in_words: Option[String],
    grand_total: Double,
    rounding_adjustment: Option[Double],
    rounded_total: Option[Double],
    in_words: Option[String],
    total_advance: Option[Double],
    outstanding_amount: Option[Double],
    allocate_advances_automatically: Option[Int],
    advances: Option[Seq[SalesInvoiceAdvance]],
    payment_terms_template: Option[String],
    payment_schedule: Option[Seq[PaymentSchedule]],
    cash_bank_account: Option[String],
    payments: Option[Seq[SalesInvoicePayment]],
    base_paid_amount: Option[Double],
    paid_amount: Option[Double],
    base_change_amount: Option[Double],
    change_amount: Option[Double],
    account_for_change_amount: Option[String],
    write_off_amount: Option[Double],
    base_write_off_amount: Option[Double],
    write_off_outstanding_amount_automatically: Option[Int],
    write_off_account: Option[String],
    write_off_cost_center: Option[String],
    tc_name: Option[String],
    terms: Option[String],
    letter_head: Option[String],
    group_same_items: Option[Int],
    language: Option[String],
    select_print_heading: Option[String],
    inter_company_invoice_reference: Option[String],
    customer_group: Option[String],
    campaign: Option[String],
    is_discounted: Option[Int],
    status: Option[String],
    source: Option[String],
    debit_to: String,
    party_account_currency: Option[String],
    is_opening: Option[String],
    c_form_applicable: Option[String],
    c_form_no: Option[String],
    sales_partner: Option[String],
    commission_rate: Option[Double],
    total_commission: Option[Double],
    sales_team: Option[Seq[SalesTeam]],
    from_date: Option[String],
    to_date: Option[String],
    auto_repeat: Option[String],
    pos_total_qty: Option[Double]
)

object SalesInvoice {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SalesInvoice = new SalesInvoice(
    name = (v \ "name").get.as[String],
    title = (v \ "title").toOption.map(_.as[String]),
    naming_series = (v \ "naming_series").get.as[String],
    customer = (v \ "customer").toOption.map(_.as[String]),
    customer_name = (v \ "customer_name").toOption.map(_.as[String]),
    tax_id = (v \ "tax_id").toOption.map(_.as[String]),
    is_pos = (v \ "is_pos").toOption.map(_.as[Int]),
    pos_profile = (v \ "pos_profile").toOption.map(_.as[String]),
    offline_pos_name = (v \ "offline_pos_name").toOption.map(_.as[String]),
    is_return = (v \ "is_return").toOption.map(_.as[Int]),
    company = (v \ "company").get.as[String],
    posting_date = (v \ "posting_date").get.as[String],
    set_posting_time = (v \ "set_posting_time").toOption.map(_.as[Int]),
    due_date = (v \ "due_date").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    return_against = (v \ "return_against").toOption.map(_.as[String]),
    update_billed_amount_in_sales_order = (v \ "update_billed_amount_in_sales_order").toOption.map(_.as[Int]),
    project = (v \ "project").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    po_no = (v \ "po_no").toOption.map(_.as[String]),
    po_date = (v \ "po_date").toOption.map(_.as[String]),
    customer_address = (v \ "customer_address").toOption.map(_.as[String]),
    contact_person = (v \ "contact_person").toOption.map(_.as[String]),
    contact_email = (v \ "contact_email").toOption.map(_.as[String]),
    territory = (v \ "territory").toOption.map(_.as[String]),
    shipping_address_name = (v \ "shipping_address_name").toOption.map(_.as[String]),
    company_address = (v \ "company_address").toOption.map(_.as[String]),
    currency = (v \ "currency").get.as[String],
    conversion_rate = (v \ "conversion_rate").get.as[Double],
    selling_price_list = (v \ "selling_price_list").get.as[String],
    price_list_currency = (v \ "price_list_currency").get.as[String],
    plc_conversion_rate = (v \ "plc_conversion_rate").get.as[Double],
    ignore_pricing_rule = (v \ "ignore_pricing_rule").toOption.map(_.as[Int]),
    set_warehouse = (v \ "set_warehouse").toOption.map(_.as[String]),
    update_stock = (v \ "update_stock").toOption.map(_.as[Int]),
    scan_barcode = (v \ "scan_barcode").toOption.map(_.as[String]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[SalesInvoiceItem])).get.toSeq,
    pricing_rules = (v \ "pricing_rules").toOption.map(x => x.as[JsArray].value.map(_.as[PricingRuleDetail]).toSeq),
    packed_items = (v \ "packed_items").toOption.map(x => x.as[JsArray].value.map(_.as[PackedItem]).toSeq),
    timesheets = (v \ "timesheets").toOption.map(x => x.as[JsArray].value.map(_.as[SalesInvoiceTimesheet]).toSeq),
    total_billing_amount = (v \ "total_billing_amount").toOption.map(_.as[Double]),
    total_qty = (v \ "total_qty").toOption.map(_.as[Double]),
    base_total = (v \ "base_total").toOption.map(_.as[Double]),
    base_net_total = (v \ "base_net_total").get.as[Double],
    total = (v \ "total").toOption.map(_.as[Double]),
    net_total = (v \ "net_total").toOption.map(_.as[Double]),
    total_net_weight = (v \ "total_net_weight").toOption.map(_.as[Double]),
    taxes_and_charges = (v \ "taxes_and_charges").toOption.map(_.as[String]),
    shipping_rule = (v \ "shipping_rule").toOption.map(_.as[String]),
    tax_category = (v \ "tax_category").toOption.map(_.as[String]),
    taxes = (v \ "taxes").toOption.map(x => x.as[JsArray].value.map(_.as[SalesTaxesandCharges]).toSeq),
    base_total_taxes_and_charges = (v \ "base_total_taxes_and_charges").toOption.map(_.as[Double]),
    total_taxes_and_charges = (v \ "total_taxes_and_charges").toOption.map(_.as[Double]),
    loyalty_points = (v \ "loyalty_points").toOption.map(_.as[Int]),
    loyalty_amount = (v \ "loyalty_amount").toOption.map(_.as[Double]),
    redeem_loyalty_points = (v \ "redeem_loyalty_points").toOption.map(_.as[Int]),
    loyalty_program = (v \ "loyalty_program").toOption.map(_.as[String]),
    loyalty_redemption_account = (v \ "loyalty_redemption_account").toOption.map(_.as[String]),
    loyalty_redemption_cost_center = (v \ "loyalty_redemption_cost_center").toOption.map(_.as[String]),
    apply_discount_on = (v \ "apply_discount_on").toOption.map(_.as[String]),
    base_discount_amount = (v \ "base_discount_amount").toOption.map(_.as[Double]),
    additional_discount_percentage = (v \ "additional_discount_percentage").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    base_grand_total = (v \ "base_grand_total").get.as[Double],
    base_rounding_adjustment = (v \ "base_rounding_adjustment").toOption.map(_.as[Double]),
    base_rounded_total = (v \ "base_rounded_total").toOption.map(_.as[Double]),
    base_in_words = (v \ "base_in_words").toOption.map(_.as[String]),
    grand_total = (v \ "grand_total").get.as[Double],
    rounding_adjustment = (v \ "rounding_adjustment").toOption.map(_.as[Double]),
    rounded_total = (v \ "rounded_total").toOption.map(_.as[Double]),
    in_words = (v \ "in_words").toOption.map(_.as[String]),
    total_advance = (v \ "total_advance").toOption.map(_.as[Double]),
    outstanding_amount = (v \ "outstanding_amount").toOption.map(_.as[Double]),
    allocate_advances_automatically = (v \ "allocate_advances_automatically").toOption.map(_.as[Int]),
    advances = (v \ "advances").toOption.map(x => x.as[JsArray].value.map(_.as[SalesInvoiceAdvance]).toSeq),
    payment_terms_template = (v \ "payment_terms_template").toOption.map(_.as[String]),
    payment_schedule = (v \ "payment_schedule").toOption.map(x => x.as[JsArray].value.map(_.as[PaymentSchedule]).toSeq),
    cash_bank_account = (v \ "cash_bank_account").toOption.map(_.as[String]),
    payments = (v \ "payments").toOption.map(x => x.as[JsArray].value.map(_.as[SalesInvoicePayment]).toSeq),
    base_paid_amount = (v \ "base_paid_amount").toOption.map(_.as[Double]),
    paid_amount = (v \ "paid_amount").toOption.map(_.as[Double]),
    base_change_amount = (v \ "base_change_amount").toOption.map(_.as[Double]),
    change_amount = (v \ "change_amount").toOption.map(_.as[Double]),
    account_for_change_amount = (v \ "account_for_change_amount").toOption.map(_.as[String]),
    write_off_amount = (v \ "write_off_amount").toOption.map(_.as[Double]),
    base_write_off_amount = (v \ "base_write_off_amount").toOption.map(_.as[Double]),
    write_off_outstanding_amount_automatically =
      (v \ "write_off_outstanding_amount_automatically").toOption.map(_.as[Int]),
    write_off_account = (v \ "write_off_account").toOption.map(_.as[String]),
    write_off_cost_center = (v \ "write_off_cost_center").toOption.map(_.as[String]),
    tc_name = (v \ "tc_name").toOption.map(_.as[String]),
    terms = (v \ "terms").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    group_same_items = (v \ "group_same_items").toOption.map(_.as[Int]),
    language = (v \ "language").toOption.map(_.as[String]),
    select_print_heading = (v \ "select_print_heading").toOption.map(_.as[String]),
    inter_company_invoice_reference = (v \ "inter_company_invoice_reference").toOption.map(_.as[String]),
    customer_group = (v \ "customer_group").toOption.map(_.as[String]),
    campaign = (v \ "campaign").toOption.map(_.as[String]),
    is_discounted = (v \ "is_discounted").toOption.map(_.as[Int]),
    status = (v \ "status").toOption.map(_.as[String]),
    source = (v \ "source").toOption.map(_.as[String]),
    debit_to = (v \ "debit_to").get.as[String],
    party_account_currency = (v \ "party_account_currency").toOption.map(_.as[String]),
    is_opening = (v \ "is_opening").toOption.map(_.as[String]),
    c_form_applicable = (v \ "c_form_applicable").toOption.map(_.as[String]),
    c_form_no = (v \ "c_form_no").toOption.map(_.as[String]),
    sales_partner = (v \ "sales_partner").toOption.map(_.as[String]),
    commission_rate = (v \ "commission_rate").toOption.map(_.as[Double]),
    total_commission = (v \ "total_commission").toOption.map(_.as[Double]),
    sales_team = (v \ "sales_team").toOption.map(x => x.as[JsArray].value.map(_.as[SalesTeam]).toSeq),
    from_date = (v \ "from_date").toOption.map(_.as[String]),
    to_date = (v \ "to_date").toOption.map(_.as[String]),
    auto_repeat = (v \ "auto_repeat").toOption.map(_.as[String]),
    pos_total_qty = (v \ "pos_total_qty").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[SalesInvoice] = Reads[SalesInvoice] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Sales Invoice") => JsSuccess(SalesInvoice(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
