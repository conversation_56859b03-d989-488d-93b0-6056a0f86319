package de.fellows.app.erpnextbridge.api.util

import play.api.libs.json.{<PERSON>s<PERSON><PERSON><PERSON>, JsD<PERSON>ined, JsString, JsValue, <PERSON>son}
import scalaj.http.{Http, HttpRequest}

import java.io.{FileOutputStream, PrintStream, UnsupportedEncodingException}
import java.net.URLEncoder
import java.nio.file.{Path, Paths}
import java.util.Scanner

object ERPNextConverter extends App {

  case class ERPType(name: String, fields: Seq[ERPField], nameField: String, typeArgs: JsValue)

  case class ERPField(
      name: String,
      scalaLine: String,
      reqd: Boolean,
      scalaname: String,
      scalaType: String,
      table: Boolean,
      options: Option[String],
      fieldArgs: Option[JsValue]
  )

  case class ERPModule(name: String, types: Seq[ERPType])

  var modules: Seq[ERPModule] = Seq()

  def encode(s: String): String =
    try
      URLEncoder.encode(s, "UTF-8")
        .replaceAll("\\+", "%20")
        .replaceAll("\\%21", "!")
        .replaceAll("\\%27", "'")
        .replaceAll("\\%28", "(")
        .replaceAll("\\%29", ")")
        .replaceAll("\\%7E", "~")
    catch {
      case e: UnsupportedEncodingException => s
    }

  def allDocTypes: Seq[String] =
    modules.flatMap(_.types.map(_.name))

  def fieldTypeString(orig: String, reqd: Boolean, option: Option[String]): Option[(String, String, Boolean)] = {
    var table = false;
    val ((x, origType)) = orig.toLowerCase match {
      case "check"                                             => Some("Int")    -> None
      case "link" | "select" | "data" | "text" | "text editor" => Some("String") -> None
      case "float" | "currency"                                => Some("Double") -> None
      case "int"                                               => Some("Int")    -> None
      case "date"                                              => Some("String") -> None
      case "datetime"                                          => Some("String") -> None
      case "attach image"                                      => Some("String") -> None
      case "table" =>
        table = true
        option.map(o => s"Seq[${scala(o)}]") -> option.map(o => scala(o))
      case _ => None -> None
    }

    reqd match {
      case true => x.map(f => (f, origType.getOrElse(f), table))
      case _    => x.map(f => (s"Option[$f]", origType.getOrElse(f), table))
    }
  }

  def fieldTypeString(jsValue: JsValue, reqd: Boolean): Option[(String, String, Boolean)] =
    fieldTypeString(
      (jsValue \ "fieldtype").get.as[String],
      reqd,
      ((jsValue \ "options").toOption.flatMap {
        case JsString(value) => Some(value)
        case _               => None
      })
    )

  def scala(s: String): String = {
    var res = s.replace(" ", "")
      .replaceAll("^type$", "`type`")
      .replaceAll("^import$", "`import`")
      .replaceAll("^private$", "`private`")
      .replaceAll("^public$", "`public`")
      .replaceAll("^class$", "`class`")
      .replaceAll("^new$", "`new`")

    if (res.contains("-")) {
      s"`$res`"
    } else {
      res
    }
  }

  def withHeaders(h: HttpRequest) =
    h.header("Authorization", "token b24147b214c6726:4891c5a630407ca")
      .header("Accept", "application/json")
      .header("Content-Type", "application/json")

  def writeDocType(jsv: JsValue, customFields: JsArray, additionalFields: Seq[ERPField]): ERPType = {
    val name = (jsv \ "name").get.as[String]

    val namingOption = (jsv \ "autoname").toOption.map(_.as[String])

    val fields = ((jsv \ "fields").get.as[JsArray]).value ++ customFields.value

    val nameField = ERPField("name", s"name: String", true, "name", "String", false, None, None)

    val allFields =
      (namingOption match {
        case Some(x) if x.startsWith("field") => Seq()
        case Some(_)                          => Seq(nameField)
        case None                             => Seq(nameField)
      }) ++ (additionalFields ++ fields.flatMap(f => createField(f, additionalFields)))

    //    val fieldStrings = (createFields(fields, additionalFields)).distinct

    ERPType(
      name,
      allFields,
      namingOption match {
        case Some(x) if x.startsWith("field:") => x.substring("field:".length)
        case _                                 => "name"
      },
      jsv
    )

    //    println(template)

  }

  private def createField(v: JsValue, additionalFields: Seq[ERPField]) = {
    val fieldName = (v \ "fieldname").get.as[String]
    if (!additionalFields.exists(af => af.name.startsWith(fieldName))) {
      val reqd = (v \ "reqd").toOption.map(_.as[Int]).getOrElse(0) == 1
      fieldTypeString(v, reqd) match {
        case Some((field, stype, table)) =>
          val scalaname = scala(fieldName)
          val scalastring = s"${
              scalaname
            }: ${
              field
            }"

          Some(ERPField(
            fieldName,
            scalastring,
            reqd,
            scalaname,
            stype,
            table,
            (v \ "options").toOption.flatMap {
              case JsString(s) => Some(s)
              case _           => None
            },
            Some(v)
          ))

        case _ => None
      }
    } else {
      None
    }
  }

  def convertDoctypes(arr: JsArray, additionalFields: Seq[ERPField]): collection.IndexedSeq[ERPType] =
    arr.value.flatMap { v =>
      v \ "name" match {
        case JsDefined(name) =>
          val dtName     = name.as[String]
          val dt: String = s"${endpoint}/api/resource/DocType/${encode(dtName)}"
          val request    = withHeaders(Http(dt))
          val jsv        = Json.parse(request.asString.body)

          val customFieldUrl: String =
            s"${endpoint}/api/resource/Custom%20Field?fields=${encode("""["*"]""")}&filters=${encode(s"""[["dt","=","$dtName"]]""")}"
          println(customFieldUrl)
          val customFields = (Json.parse(withHeaders(Http(customFieldUrl)).asString.body) \ "data").as[JsArray]
          println(s"custom fields ${customFields}")
          Some(writeDocType((jsv \ "data").get, customFields, additionalFields))
        case _ => None
      }

    }

  def convertModule(e: JsValue, additionalFields: Seq[ERPField]): Option[ERPModule] =
    e \ "name" match {
      case JsDefined(name) =>
        println(s"covert module $name")
        val filter = encode(s"""[["module", "=", "${name.as[String]}"]]""")
        val req    = withHeaders(Http(s"""${endpoint}/api/resource/DocType?limit_page_length=0&filters=$filter"""))
        val jsv    = Json.parse(req.asString.body)

        (jsv \ "data") match {
          case JsDefined(x) => x match {
              case arr: JsArray => Some(ERPModule(name.as[String], convertDoctypes(arr, additionalFields).toSeq))
              case _            => None
            }
          case _ => None
        }

      case _ => None
    }

  val in = new Scanner(System.in)

  val endpoint = "http://localhost:8000"
  //  println("api")
  //  val apitoken = in.nextLine()

  // http://demo.stackrate.de s
  // http://********* s
  // http://localhost:9010 s

  //  http :// localhost: 8000 / api / resource / DocType ? limit_page_length
  //  = 0

  //  val jsv = Json.parse(withHeaders(Http(s"${endpoint}/api/resource/DocType?limit_page_length=0")).asString.body)

  def getRootFields(value: String) = {
    val dt: String = s"${endpoint}/api/resource/DocType/DocType"
    val dtrequest  = withHeaders(Http(dt))
    val dtjsv      = Json.parse(dtrequest.asString.body)
    val rootFields = (dtjsv \ "data" \ "fields").get.as[JsArray].value
    rootFields.flatMap(f => createField(f, Seq()))

    Seq()

  }

  val genericFields = getRootFields("DocType")

  val jsv = Json.parse(withHeaders(Http(s"${endpoint}/api/resource/Module%20Def?limit_page_length=0")).asString.body)

  val path = Paths.get(
    "/home/<USER>/Code/fellows/backend/app-backend/framework/erpnext-bridge/api/src/main/scala/de/fellows/app/erpnextbridge/api/gen/erp"
  )

  def writeCaseClasses(modules: Seq[ERPModule], path: Path) = {
    path.toFile.mkdirs()
    modules.foreach { mod =>
      val fieldfilter: ERPField => Boolean = f =>
        !f.table || allDocTypes.contains(f.options.get)

      val typeToString: ERPType => String = t => {
        val fields = t.fields.filter(fieldfilter).map { field =>
          field.scalaLine
        }

        //  def apply(v: JsValue): Test = new Test(
        //    (v \ "a").get.as[Int],
        //    (v \ "b").get.as[String],
        //    (v \ "x").toOption.map(_.as[Int]),
        //    (v \ "y").toOption.map(_.as[String]),
        //  )

        val applyLines = t.fields.filter(fieldfilter).map { field =>
          if (field.table) {
            if (field.reqd) {
              s"""${field.scalaname} = (v \\ "${field.name}").toOption.map(x => x.as[JsArray].value.map(_.as[${field.scalaType}])).get.toSeq"""
            } else {
              s"""${field.scalaname} = (v \\ "${field.name}").toOption.map(x => x.as[JsArray].value.map(_.as[${field.scalaType}]).toSeq)"""
            }
          } else {
            if (field.reqd) {
              s"""${field.scalaname} = (v \\ "${field.name}").get.as[${field.scalaType}]"""
            } else {
              s"""${field.scalaname} = (v \\ "${field.name}").toOption.map(_.as[${field.scalaType}])"""
            }
          }

        }

        val typename = scala(t.name)
        val reads =
          s"""
             |  implicit val reads = Reads[${typename}](js => {
             |    (js \\ "doctype").toOption.map(_.as[String]) match {
             |      case Some("${t.name}") => JsSuccess(${typename}(js))
             |      case Some(_) => JsError("Wrong Doctype")
             |      case _ => JsError("Doctype not Found")
             |    }
             |  })

             |""".stripMargin

        val apply =
          s"""
             |  def apply(v: JsValue): ${typename} = new ${typename}(
             |    ${applyLines.mkString(",\n    ")}
             |  )
             |""".stripMargin

        s"""
           |  case class ${typename}(
           |    ${fields.mkString(",\n    ")}
           |  )
           |
           |  object ${typename}{
           |    val NAME_FIELD = "${t.nameField}"
           |
           |    $apply
           |
           |    $reads
           |  }
           |""".stripMargin
      }

      def moduleToName(mod: ERPModule): String =
        scala(mod.name).toLowerCase()

      modules.foreach { mod =>
        val pkgName = moduleToName(mod)
        val pkg     = path.resolve(pkgName)
        pkg.toFile.mkdirs()

        mod.types.foreach { t =>
          val tp = typeToString(t)
          println(s"creating type ${t.name} ...")

          val o = new PrintStream(new FileOutputStream(pkg.resolve(s"${scala(t.name)}.scala").toFile, false))
          //               |${modules.map(mod => s"import de.fellows.app.erpnextbridge.api.gen.erp.model.${mod.name}._").mkString("\n")}
          o.println(
            s"""
               |package de.fellows.app.erpnextbridge.api.gen.erp.model.$pkgName
               |
               |import play.api.libs.json.{JsArray, JsError, JsSuccess, JsValue, Reads}
               |${modules.map(moduleToName).map(x =>
                s"import de.fellows.app.erpnextbridge.api.gen.erp.model.$x._"
              ).mkString("\n")}
               |
               | ${tp}
               |""".stripMargin
          )

          o.close()
        }
      }

    }

  }

  def writeConstants(modules: Seq[ERPModule], path: Path) = {

    //    val docName = scala(name)
    //    val template =
    //      s"""
    //         |case class ${docName} (
    //         |  ${fieldStrings.mkString(",\n  ")}
    //         |) extends ErpDocType
    //         |
    //         |""".stripMargin

    val o = new PrintStream(new FileOutputStream(path.toFile, false))

    val modulestrings = modules.map { module =>
      val tps = module.types.map { tp =>
        // language=scala
        s"""val ${tp.name.replace(" ", "").replace("-", "").toUpperCase()} = "${tp.name}""""
      }

      s"""
         |  object ${scala(module.name)} {
         |    ${tps.mkString("\n    ")}
         |  }
         |""".stripMargin
    }

    o.println(
      // language=scala
      s"""
         |package de.fellows.app.erpnextbridge.api.gen.erp
         |
         |object Constants {
         |  ${modulestrings.mkString("\n  ")}
         |}
         |""".stripMargin
    )

    o.close();

  }

  jsv \ "data" match {
    case JsDefined(x) => x match {
        case JsArray(arr) =>
          modules = arr
            .filter { jsv =>
              Seq("Core", "Manufacturing", "Selling", "Stock", "Stackrate", "PCB", "Accounts", "Contacts").contains(
                (jsv \ "name").get.as[String]
              )
            }
            .flatMap { mod =>
              convertModule(mod, genericFields.toSeq)
            //          val modPath = path.resolve(s"${(mod \ "name").get.as[String].replace(" ", "").toLowerCase()}/")
            //          modPath.toFile.mkdir()

            //          val o = new PrintStream(new FileOutputStream(path.resolve(modName + ".scala").toFile, false))
            //          o.println(
            //            s"""
            //               |package de.fellows.app.erpnextbridge.api.gen
            //               |
            //               |import java.time.Instant
            //               |import java.time.LocalDate
            //               |import play.api.libs.json.Json
            //               |import de.fellows.app.erpnext.api.ErpDocType
            //               |
            //               |object $modName {
            //               |  ${classes.mkString("\n  ")}
            //               |}
            //               |""".stripMargin)
            //
            //          o.close();
            }.toSeq

          writeConstants(modules, path.resolve("Constants.scala"))
          writeCaseClasses(modules, path.resolve("model"))

        case _ =>
      }
    case _ =>

  }

}
