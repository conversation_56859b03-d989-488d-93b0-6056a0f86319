package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Routing(
    routing_name: Option[String],
    disabled: Option[Int],
    operations: Option[Seq[BOMOperation]],
    full_routing: Option[Int]
)

object Routing {
  val NAME_FIELD = "routing_name"

  def apply(v: JsValue): Routing = new Routing(
    routing_name = (v \ "routing_name").toOption.map(_.as[String]),
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    operations = (v \ "operations").toOption.map(x => x.as[JsArray].value.map(_.as[BOMOperation]).toSeq),
    full_routing = (v \ "full_routing").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[Routing] = Reads[Routing] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Routing") => JsSuccess(Routing(js))
      case Some(_)         => JsError("Wrong Doctype")
      case _               => JsError("Doctype not Found")
    }
  }

}
