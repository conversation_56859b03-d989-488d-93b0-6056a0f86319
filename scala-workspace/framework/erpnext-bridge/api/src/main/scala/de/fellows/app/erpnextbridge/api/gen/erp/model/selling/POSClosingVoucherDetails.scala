package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class POSClosingVoucherDetails(
    name: String,
    mode_of_payment: String,
    collected_amount: Double,
    expected_amount: Option[Double],
    difference: Option[Double]
)

object POSClosingVoucherDetails {
  val NAME_FIELD = "name"

  def apply(v: JsValue): POSClosingVoucherDetails = new POSClosingVoucherDetails(
    name = (v \ "name").get.as[String],
    mode_of_payment = (v \ "mode_of_payment").get.as[String],
    collected_amount = (v \ "collected_amount").get.as[Double],
    expected_amount = (v \ "expected_amount").toOption.map(_.as[Double]),
    difference = (v \ "difference").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[POSClosingVoucherDetails] = Reads[POSClosingVoucherDetails] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("POS Closing Voucher Details") => JsSuccess(POSClosingVoucherDetails(js))
      case Some(_)                             => JsError("Wrong Doctype")
      case _                                   => JsError("Doctype not Found")
    }
  }

}
