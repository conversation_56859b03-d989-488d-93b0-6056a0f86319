package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DataImport(
    name: String,
    reference_doctype: String,
    action: String,
    insert_new: Option[Int],
    overwrite: Option[Int],
    only_update: Option[Int],
    skip_errors: Option[Int],
    submit_after_import: Option[Int],
    ignore_encoding_errors: Option[Int],
    no_email: Option[Int],
    import_status: Option[String],
    show_only_errors: Option[Int],
    amended_from: Option[String],
    total_rows: Option[Int]
)

object DataImport {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DataImport = new DataImport(
    name = (v \ "name").get.as[String],
    reference_doctype = (v \ "reference_doctype").get.as[String],
    action = (v \ "action").get.as[String],
    insert_new = (v \ "insert_new").toOption.map(_.as[Int]),
    overwrite = (v \ "overwrite").toOption.map(_.as[Int]),
    only_update = (v \ "only_update").toOption.map(_.as[Int]),
    skip_errors = (v \ "skip_errors").toOption.map(_.as[Int]),
    submit_after_import = (v \ "submit_after_import").toOption.map(_.as[Int]),
    ignore_encoding_errors = (v \ "ignore_encoding_errors").toOption.map(_.as[Int]),
    no_email = (v \ "no_email").toOption.map(_.as[Int]),
    import_status = (v \ "import_status").toOption.map(_.as[String]),
    show_only_errors = (v \ "show_only_errors").toOption.map(_.as[Int]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    total_rows = (v \ "total_rows").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[DataImport] = Reads[DataImport] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Data Import") => JsSuccess(DataImport(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
