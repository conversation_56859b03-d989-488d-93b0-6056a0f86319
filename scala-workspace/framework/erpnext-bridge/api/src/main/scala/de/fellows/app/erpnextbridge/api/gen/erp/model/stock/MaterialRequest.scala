package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class MaterialRequest(
    name: String,
    naming_series: String,
    title: Option[String],
    material_request_type: String,
    customer: Option[String],
    schedule_date: Option[String],
    company: String,
    amended_from: Option[String],
    scan_barcode: Option[String],
    items: Seq[MaterialRequestItem],
    requested_by: Option[String],
    transaction_date: String,
    status: Option[String],
    letter_head: Option[String],
    select_print_heading: Option[String],
    tc_name: Option[String],
    terms: Option[String],
    job_card: Option[String]
)

object MaterialRequest {
  val NAME_FIELD = "name"

  def apply(v: JsValue): MaterialRequest = new MaterialRequest(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    title = (v \ "title").toOption.map(_.as[String]),
    material_request_type = (v \ "material_request_type").get.as[String],
    customer = (v \ "customer").toOption.map(_.as[String]),
    schedule_date = (v \ "schedule_date").toOption.map(_.as[String]),
    company = (v \ "company").get.as[String],
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    scan_barcode = (v \ "scan_barcode").toOption.map(_.as[String]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[MaterialRequestItem])).get.toSeq,
    requested_by = (v \ "requested_by").toOption.map(_.as[String]),
    transaction_date = (v \ "transaction_date").get.as[String],
    status = (v \ "status").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    select_print_heading = (v \ "select_print_heading").toOption.map(_.as[String]),
    tc_name = (v \ "tc_name").toOption.map(_.as[String]),
    terms = (v \ "terms").toOption.map(_.as[String]),
    job_card = (v \ "job_card").toOption.map(_.as[String])
  )

  implicit val reads: Reads[MaterialRequest] = Reads[MaterialRequest] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Material Request") => JsSuccess(MaterialRequest(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
