package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class MonthlyDistribution(
    distribution_id: String,
    fiscal_year: Option[String],
    percentages: Option[Seq[MonthlyDistributionPercentage]]
)

object MonthlyDistribution {
  val NAME_FIELD = "distribution_id"

  def apply(v: JsValue): MonthlyDistribution = new MonthlyDistribution(
    distribution_id = (v \ "distribution_id").get.as[String],
    fiscal_year = (v \ "fiscal_year").toOption.map(_.as[String]),
    percentages =
      (v \ "percentages").toOption.map(x => x.as[JsArray].value.map(_.as[MonthlyDistributionPercentage]).toSeq)
  )

  implicit val reads: Reads[MonthlyDistribution] = Reads[MonthlyDistribution] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Monthly Distribution") => JsSuccess(MonthlyDistribution(js))
      case Some(_)                      => JsError("Wrong Doctype")
      case _                            => JsError("Doctype not Found")
    }
  }

}
