package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BOMItem(
    name: String,
    item_code: String,
    item_name: Option[String],
    operation: Option[String],
    bom_no: Option[String],
    source_warehouse: Option[String],
    allow_alternative_item: Option[Int],
    description: Option[String],
    qty: Double,
    uom: String,
    stock_qty: Option[Double],
    stock_uom: Option[String],
    conversion_factor: Option[Double],
    rate: Double,
    base_rate: Option[Double],
    amount: Option[Double],
    base_amount: Option[Double],
    scrap: Option[Double],
    qty_consumed_per_unit: Option[Double],
    include_item_in_manufacturing: Option[Int],
    original_item: Option[String]
)

object BOMItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BOMItem = new BOMItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    operation = (v \ "operation").toOption.map(_.as[String]),
    bom_no = (v \ "bom_no").toOption.map(_.as[String]),
    source_warehouse = (v \ "source_warehouse").toOption.map(_.as[String]),
    allow_alternative_item = (v \ "allow_alternative_item").toOption.map(_.as[Int]),
    description = (v \ "description").toOption.map(_.as[String]),
    qty = (v \ "qty").get.as[Double],
    uom = (v \ "uom").get.as[String],
    stock_qty = (v \ "stock_qty").toOption.map(_.as[Double]),
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    conversion_factor = (v \ "conversion_factor").toOption.map(_.as[Double]),
    rate = (v \ "rate").get.as[Double],
    base_rate = (v \ "base_rate").toOption.map(_.as[Double]),
    amount = (v \ "amount").toOption.map(_.as[Double]),
    base_amount = (v \ "base_amount").toOption.map(_.as[Double]),
    scrap = (v \ "scrap").toOption.map(_.as[Double]),
    qty_consumed_per_unit = (v \ "qty_consumed_per_unit").toOption.map(_.as[Double]),
    include_item_in_manufacturing = (v \ "include_item_in_manufacturing").toOption.map(_.as[Int]),
    original_item = (v \ "original_item").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BOMItem] = Reads[BOMItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("BOM Item") => JsSuccess(BOMItem(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
