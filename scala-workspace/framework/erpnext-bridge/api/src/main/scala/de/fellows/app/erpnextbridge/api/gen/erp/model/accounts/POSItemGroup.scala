package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, Js<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class POSItemGroup(
    name: String,
    item_group: String
)

object POSItemGroup {
  val NAME_FIELD = "name"

  def apply(v: JsValue): POSItemGroup = new POSItemGroup(
    name = (v \ "name").get.as[String],
    item_group = (v \ "item_group").get.as[String]
  )

  implicit val reads: Reads[POSItemGroup] = Reads[POSItemGroup] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("POS Item Group") => JsSuccess(POSItemGroup(js))
      case Some(_)                => JsError("Wrong Doctype")
      case _                      => JsError("Doctype not Found")
    }
  }

}
