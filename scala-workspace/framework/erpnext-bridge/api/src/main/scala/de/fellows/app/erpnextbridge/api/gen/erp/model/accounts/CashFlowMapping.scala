package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class CashFlowMapping(
    mapping_name: String,
    label: String,
    accounts: Seq[CashFlowMappingAccounts],
    is_finance_cost: Option[Int],
    is_working_capital: Option[Int],
    is_finance_cost_adjustment: Option[Int],
    is_income_tax_liability: Option[Int],
    is_income_tax_expense: Option[Int]
)

object CashFlowMapping {
  val NAME_FIELD = "mapping_name"

  def apply(v: JsValue): CashFlowMapping = new CashFlowMapping(
    mapping_name = (v \ "mapping_name").get.as[String],
    label = (v \ "label").get.as[String],
    accounts = (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[CashFlowMappingAccounts])).get.toSeq,
    is_finance_cost = (v \ "is_finance_cost").toOption.map(_.as[Int]),
    is_working_capital = (v \ "is_working_capital").toOption.map(_.as[Int]),
    is_finance_cost_adjustment = (v \ "is_finance_cost_adjustment").toOption.map(_.as[Int]),
    is_income_tax_liability = (v \ "is_income_tax_liability").toOption.map(_.as[Int]),
    is_income_tax_expense = (v \ "is_income_tax_expense").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[CashFlowMapping] = Reads[CashFlowMapping] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Cash Flow Mapping") => JsSuccess(CashFlowMapping(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
