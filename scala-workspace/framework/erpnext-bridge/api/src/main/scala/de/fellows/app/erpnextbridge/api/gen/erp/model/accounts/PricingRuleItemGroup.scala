package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PricingRuleItemGroup(
    name: String,
    item_group: Option[String],
    uom: Option[String]
)

object PricingRuleItemGroup {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PricingRuleItemGroup = new PricingRuleItemGroup(
    name = (v \ "name").get.as[String],
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    uom = (v \ "uom").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PricingRuleItemGroup] = Reads[PricingRuleItemGroup] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Pricing Rule Item Group") => JsSuccess(PricingRuleItemGroup(js))
      case Some(_)                         => JsError("Wrong Doctype")
      case _                               => JsError("Doctype not Found")
    }
  }

}
