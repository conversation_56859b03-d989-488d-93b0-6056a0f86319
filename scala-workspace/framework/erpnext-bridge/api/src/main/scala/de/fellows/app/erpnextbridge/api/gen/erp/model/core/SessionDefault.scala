package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SessionDefault(
    name: String,
    ref_doctype: Option[String]
)

object SessionDefault {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SessionDefault = new SessionDefault(
    name = (v \ "name").get.as[String],
    ref_doctype = (v \ "ref_doctype").toOption.map(_.as[String])
  )

  implicit val reads: Reads[SessionDefault] = Reads[SessionDefault] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Session Default") => JsSuccess(SessionDefault(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
