package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DocPerm(
    name: String,
    role: String,
    if_owner: Option[Int],
    permlevel: Option[Int],
    read: Option[Int],
    write: Option[Int],
    create: Option[Int],
    delete: Option[Int],
    submit: Option[Int],
    cancel: Option[Int],
    amend: Option[Int],
    report: Option[Int],
    export: Option[Int],
    `import`: Option[Int],
    set_user_permissions: Option[Int],
    share: Option[Int],
    print: Option[Int],
    email: Option[Int]
)

object DocPerm {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DocPerm = new DocPerm(
    name = (v \ "name").get.as[String],
    role = (v \ "role").get.as[String],
    if_owner = (v \ "if_owner").toOption.map(_.as[Int]),
    permlevel = (v \ "permlevel").toOption.map(_.as[Int]),
    read = (v \ "read").toOption.map(_.as[Int]),
    write = (v \ "write").toOption.map(_.as[Int]),
    create = (v \ "create").toOption.map(_.as[Int]),
    delete = (v \ "delete").toOption.map(_.as[Int]),
    submit = (v \ "submit").toOption.map(_.as[Int]),
    cancel = (v \ "cancel").toOption.map(_.as[Int]),
    amend = (v \ "amend").toOption.map(_.as[Int]),
    report = (v \ "report").toOption.map(_.as[Int]),
    export = (v \ "export").toOption.map(_.as[Int]),
    `import` = (v \ "import").toOption.map(_.as[Int]),
    set_user_permissions = (v \ "set_user_permissions").toOption.map(_.as[Int]),
    share = (v \ "share").toOption.map(_.as[Int]),
    print = (v \ "print").toOption.map(_.as[Int]),
    email = (v \ "email").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[DocPerm] = Reads[DocPerm] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("DocPerm") => JsSuccess(DocPerm(js))
      case Some(_)         => JsError("Wrong Doctype")
      case _               => JsError("Doctype not Found")
    }
  }

}
