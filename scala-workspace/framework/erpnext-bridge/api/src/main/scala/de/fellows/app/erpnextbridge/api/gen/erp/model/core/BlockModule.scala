package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BlockModule(
    name: String,
    module: String
)

object BlockModule {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BlockModule = new BlockModule(
    name = (v \ "name").get.as[String],
    module = (v \ "module").get.as[String]
  )

  implicit val reads: Reads[BlockModule] = Reads[BlockModule] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Block Module") => JsSuccess(BlockModule(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
