package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemDefault(
    name: String,
    company: String,
    default_warehouse: Option[String],
    default_price_list: Option[String],
    buying_cost_center: Option[String],
    default_supplier: Option[String],
    expense_account: Option[String],
    selling_cost_center: Option[String],
    income_account: Option[String]
)

object ItemDefault {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemDefault = new ItemDefault(
    name = (v \ "name").get.as[String],
    company = (v \ "company").get.as[String],
    default_warehouse = (v \ "default_warehouse").toOption.map(_.as[String]),
    default_price_list = (v \ "default_price_list").toOption.map(_.as[String]),
    buying_cost_center = (v \ "buying_cost_center").toOption.map(_.as[String]),
    default_supplier = (v \ "default_supplier").toOption.map(_.as[String]),
    expense_account = (v \ "expense_account").toOption.map(_.as[String]),
    selling_cost_center = (v \ "selling_cost_center").toOption.map(_.as[String]),
    income_account = (v \ "income_account").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ItemDefault] = Reads[ItemDefault] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Default") => JsSuccess(ItemDefault(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
