package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class POSCustomerGroup(
    name: String,
    customer_group: String
)

object POSCustomerGroup {
  val NAME_FIELD = "name"

  def apply(v: JsValue): POSCustomerGroup = new POSCustomerGroup(
    name = (v \ "name").get.as[String],
    customer_group = (v \ "customer_group").get.as[String]
  )

  implicit val reads: Reads[POSCustomerGroup] = Reads[POSCustomerGroup] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("POS Customer Group") => JsSuccess(POSCustomerGroup(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
