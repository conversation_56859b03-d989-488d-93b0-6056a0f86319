package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class FiscalYear(
    year: String,
    disabled: Option[Int],
    year_start_date: String,
    year_end_date: String,
    companies: Option[Seq[FiscalYearCompany]],
    auto_created: Option[Int]
)

object FiscalYear {
  val NAME_FIELD = "year"

  def apply(v: JsValue): FiscalYear = new FiscalYear(
    year = (v \ "year").get.as[String],
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    year_start_date = (v \ "year_start_date").get.as[String],
    year_end_date = (v \ "year_end_date").get.as[String],
    companies = (v \ "companies").toOption.map(x => x.as[JsArray].value.map(_.as[FiscalYearCompany]).toSeq),
    auto_created = (v \ "auto_created").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[FiscalYear] = Reads[FiscalYear] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Fiscal Year") => JsSuccess(FiscalYear(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
