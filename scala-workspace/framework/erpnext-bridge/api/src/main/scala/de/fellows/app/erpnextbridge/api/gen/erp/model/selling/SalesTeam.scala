package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SalesTeam(
    name: String,
    sales_person: String,
    contact_no: Option[String],
    allocated_percentage: Option[Double],
    allocated_amount: Option[Double],
    commission_rate: Option[String],
    incentives: Option[Double]
)

object SalesTeam {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SalesTeam = new SalesTeam(
    name = (v \ "name").get.as[String],
    sales_person = (v \ "sales_person").get.as[String],
    contact_no = (v \ "contact_no").toOption.map(_.as[String]),
    allocated_percentage = (v \ "allocated_percentage").toOption.map(_.as[Double]),
    allocated_amount = (v \ "allocated_amount").toOption.map(_.as[Double]),
    commission_rate = (v \ "commission_rate").toOption.map(_.as[String]),
    incentives = (v \ "incentives").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[SalesTeam] = Reads[SalesTeam] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Sales Team") => JsSuccess(SalesTeam(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
