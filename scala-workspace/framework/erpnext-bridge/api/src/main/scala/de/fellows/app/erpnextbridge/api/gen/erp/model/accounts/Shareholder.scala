package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Shareholder(
    name: String,
    title: String,
    naming_series: Option[String],
    folio_no: Option[String],
    company: String,
    is_company: Option[Int],
    share_balance: Option[Seq[ShareBalance]]
)

object Shareholder {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Shareholder = new Shareholder(
    name = (v \ "name").get.as[String],
    title = (v \ "title").get.as[String],
    naming_series = (v \ "naming_series").toOption.map(_.as[String]),
    folio_no = (v \ "folio_no").toOption.map(_.as[String]),
    company = (v \ "company").get.as[String],
    is_company = (v \ "is_company").toOption.map(_.as[Int]),
    share_balance = (v \ "share_balance").toOption.map(x => x.as[JsArray].value.map(_.as[ShareBalance]).toSeq)
  )

  implicit val reads: Reads[Shareholder] = Reads[Shareholder] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Shareholder") => JsSuccess(Shareholder(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
