package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankAccount(
    name: String,
    account_name: String,
    account: Option[String],
    bank: String,
    account_type: Option[String],
    account_subtype: Option[String],
    is_default: Option[Int],
    is_company_account: Option[Int],
    company: Option[String],
    party_type: Option[String],
    iban: Option[String],
    bank_account_no: Option[String],
    branch_code: Option[String],
    swift_number: Option[String],
    website: Option[String],
    integration_id: Option[String],
    last_integration_date: Option[String],
    mask: Option[String]
)

object BankAccount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankAccount = new BankAccount(
    name = (v \ "name").get.as[String],
    account_name = (v \ "account_name").get.as[String],
    account = (v \ "account").toOption.map(_.as[String]),
    bank = (v \ "bank").get.as[String],
    account_type = (v \ "account_type").toOption.map(_.as[String]),
    account_subtype = (v \ "account_subtype").toOption.map(_.as[String]),
    is_default = (v \ "is_default").toOption.map(_.as[Int]),
    is_company_account = (v \ "is_company_account").toOption.map(_.as[Int]),
    company = (v \ "company").toOption.map(_.as[String]),
    party_type = (v \ "party_type").toOption.map(_.as[String]),
    iban = (v \ "iban").toOption.map(_.as[String]),
    bank_account_no = (v \ "bank_account_no").toOption.map(_.as[String]),
    branch_code = (v \ "branch_code").toOption.map(_.as[String]),
    swift_number = (v \ "swift_number").toOption.map(_.as[String]),
    website = (v \ "website").toOption.map(_.as[String]),
    integration_id = (v \ "integration_id").toOption.map(_.as[String]),
    last_integration_date = (v \ "last_integration_date").toOption.map(_.as[String]),
    mask = (v \ "mask").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BankAccount] = Reads[BankAccount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Account") => JsSuccess(BankAccount(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
