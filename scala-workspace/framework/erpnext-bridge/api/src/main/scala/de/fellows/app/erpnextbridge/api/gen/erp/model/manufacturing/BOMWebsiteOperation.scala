package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BOMWebsiteOperation(
    name: String,
    operation: String,
    workstation: Option[String],
    time_in_mins: Double,
    thumbnail: Option[String]
)

object BOMWebsiteOperation {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BOMWebsiteOperation = new BOMWebsiteOperation(
    name = (v \ "name").get.as[String],
    operation = (v \ "operation").get.as[String],
    workstation = (v \ "workstation").toOption.map(_.as[String]),
    time_in_mins = (v \ "time_in_mins").get.as[Double],
    thumbnail = (v \ "thumbnail").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BOMWebsiteOperation] = Reads[BOMWebsiteOperation] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("BOM Website Operation") => JsSuccess(BOMWebsiteOperation(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
