package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsE<PERSON>r, JsSuc<PERSON>, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankStatementTransactionInvoiceItem(
    name: String,
    transaction_date: Option[String],
    payment_description: Option[String],
    party_type: Option[String],
    invoice_date: String,
    invoice_type: String,
    outstanding_amount: Option[String],
    allocated_amount: Option[String]
)

object BankStatementTransactionInvoiceItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankStatementTransactionInvoiceItem = new BankStatementTransactionInvoiceItem(
    name = (v \ "name").get.as[String],
    transaction_date = (v \ "transaction_date").toOption.map(_.as[String]),
    payment_description = (v \ "payment_description").toOption.map(_.as[String]),
    party_type = (v \ "party_type").toOption.map(_.as[String]),
    invoice_date = (v \ "invoice_date").get.as[String],
    invoice_type = (v \ "invoice_type").get.as[String],
    outstanding_amount = (v \ "outstanding_amount").toOption.map(_.as[String]),
    allocated_amount = (v \ "allocated_amount").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BankStatementTransactionInvoiceItem] = Reads[BankStatementTransactionInvoiceItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Statement Transaction Invoice Item") => JsSuccess(BankStatementTransactionInvoiceItem(js))
      case Some(_)                                         => JsError("Wrong Doctype")
      case _                                               => JsError("Doctype not Found")
    }
  }

}
