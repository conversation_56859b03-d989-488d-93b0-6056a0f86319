package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemQualityInspectionParameter(
    name: String,
    specification: String,
    value: Option[String]
)

object ItemQualityInspectionParameter {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemQualityInspectionParameter = new ItemQualityInspectionParameter(
    name = (v \ "name").get.as[String],
    specification = (v \ "specification").get.as[String],
    value = (v \ "value").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ItemQualityInspectionParameter] = Reads[ItemQualityInspectionParameter] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Quality Inspection Parameter") => JsSuccess(ItemQualityInspectionParameter(js))
      case Some(_)                                   => JsError("Wrong Doctype")
      case _                                         => JsError("Doctype not Found")
    }
  }

}
