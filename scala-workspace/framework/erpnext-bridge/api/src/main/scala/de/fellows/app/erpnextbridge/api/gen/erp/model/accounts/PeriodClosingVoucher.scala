package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PeriodClosingVoucher(
    name: String,
    transaction_date: Option[String],
    posting_date: String,
    fiscal_year: String,
    amended_from: Option[String],
    company: String,
    closing_account_head: String
)

object PeriodClosingVoucher {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PeriodClosingVoucher = new PeriodClosingVoucher(
    name = (v \ "name").get.as[String],
    transaction_date = (v \ "transaction_date").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").get.as[String],
    fiscal_year = (v \ "fiscal_year").get.as[String],
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    company = (v \ "company").get.as[String],
    closing_account_head = (v \ "closing_account_head").get.as[String]
  )

  implicit val reads: Reads[PeriodClosingVoucher] = Reads[PeriodClosingVoucher] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Period Closing Voucher") => JsSuccess(PeriodClosingVoucher(js))
      case Some(_)                        => JsError("Wrong Doctype")
      case _                              => JsError("Doctype not Found")
    }
  }

}
