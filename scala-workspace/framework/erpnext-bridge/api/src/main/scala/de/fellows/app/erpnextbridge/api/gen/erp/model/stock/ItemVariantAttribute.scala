package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemVariantAttribute(
    name: String,
    variant_of: Option[String],
    attribute: String,
    attribute_value: Option[String],
    numeric_values: Option[Int],
    from_range: Option[Double],
    increment: Option[Double],
    to_range: Option[Double]
)

object ItemVariantAttribute {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemVariantAttribute = new ItemVariantAttribute(
    name = (v \ "name").get.as[String],
    variant_of = (v \ "variant_of").toOption.map(_.as[String]),
    attribute = (v \ "attribute").get.as[String],
    attribute_value = (v \ "attribute_value").toOption.map(_.as[String]),
    numeric_values = (v \ "numeric_values").toOption.map(_.as[Int]),
    from_range = (v \ "from_range").toOption.map(_.as[Double]),
    increment = (v \ "increment").toOption.map(_.as[Double]),
    to_range = (v \ "to_range").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[ItemVariantAttribute] = Reads[ItemVariantAttribute] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Variant Attribute") => JsSuccess(ItemVariantAttribute(js))
      case Some(_)                        => JsError("Wrong Doctype")
      case _                              => JsError("Doctype not Found")
    }
  }

}
