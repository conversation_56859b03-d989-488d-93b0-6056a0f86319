package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SalesInvoiceTimesheet(
    name: String,
    time_sheet: Option[String],
    billing_hours: Option[Double],
    billing_amount: Option[Double],
    timesheet_detail: Option[String]
)

object SalesInvoiceTimesheet {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SalesInvoiceTimesheet = new SalesInvoiceTimesheet(
    name = (v \ "name").get.as[String],
    time_sheet = (v \ "time_sheet").toOption.map(_.as[String]),
    billing_hours = (v \ "billing_hours").toOption.map(_.as[Double]),
    billing_amount = (v \ "billing_amount").toOption.map(_.as[Double]),
    timesheet_detail = (v \ "timesheet_detail").toOption.map(_.as[String])
  )

  implicit val reads: Reads[SalesInvoiceTimesheet] = Reads[SalesInvoiceTimesheet] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Sales Invoice Timesheet") => JsSuccess(SalesInvoiceTimesheet(js))
      case Some(_)                         => JsError("Wrong Doctype")
      case _                               => JsError("Doctype not Found")
    }
  }

}
