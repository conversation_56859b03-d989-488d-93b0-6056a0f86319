package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ShareBalance(
    name: String,
    share_type: String,
    from_no: Int,
    rate: Int,
    no_of_shares: Int,
    to_no: Int,
    amount: Int,
    is_company: Option[Int],
    current_state: Option[String]
)

object ShareBalance {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ShareBalance = new ShareBalance(
    name = (v \ "name").get.as[String],
    share_type = (v \ "share_type").get.as[String],
    from_no = (v \ "from_no").get.as[Int],
    rate = (v \ "rate").get.as[Int],
    no_of_shares = (v \ "no_of_shares").get.as[Int],
    to_no = (v \ "to_no").get.as[Int],
    amount = (v \ "amount").get.as[Int],
    is_company = (v \ "is_company").toOption.map(_.as[Int]),
    current_state = (v \ "current_state").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ShareBalance] = Reads[ShareBalance] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Share Balance") => JsSuccess(ShareBalance(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
