package de.fellows.app.erpnextbridge.api.gen.erp.model.contacts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Address(
    name: String,
    address_title: Option[String],
    address_type: String,
    address_line1: String,
    address_line2: Option[String],
    city: String,
    county: Option[String],
    state: Option[String],
    country: String,
    pincode: Option[String],
    email_id: Option[String],
    phone: Option[String],
    fax: Option[String],
    is_primary_address: Option[Int],
    is_shipping_address: Option[Int],
    disabled: Option[Int],
    is_your_company_address: Option[Int],
    links: Option[Seq[DynamicLink]],
    tax_category: Option[String]
)

object Address {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Address = new Address(
    name = (v \ "name").get.as[String],
    address_title = (v \ "address_title").toOption.map(_.as[String]),
    address_type = (v \ "address_type").get.as[String],
    address_line1 = (v \ "address_line1").get.as[String],
    address_line2 = (v \ "address_line2").toOption.map(_.as[String]),
    city = (v \ "city").get.as[String],
    county = (v \ "county").toOption.map(_.as[String]),
    state = (v \ "state").toOption.map(_.as[String]),
    country = (v \ "country").get.as[String],
    pincode = (v \ "pincode").toOption.map(_.as[String]),
    email_id = (v \ "email_id").toOption.map(_.as[String]),
    phone = (v \ "phone").toOption.map(_.as[String]),
    fax = (v \ "fax").toOption.map(_.as[String]),
    is_primary_address = (v \ "is_primary_address").toOption.map(_.as[Int]),
    is_shipping_address = (v \ "is_shipping_address").toOption.map(_.as[Int]),
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    is_your_company_address = (v \ "is_your_company_address").toOption.map(_.as[Int]),
    links = (v \ "links").toOption.map(x => x.as[JsArray].value.map(_.as[DynamicLink]).toSeq),
    tax_category = (v \ "tax_category").toOption.map(_.as[String])
  )

  implicit val reads: Reads[Address] = Reads[Address] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Address") => JsSuccess(Address(js))
      case Some(_)         => JsError("Wrong Doctype")
      case _               => JsError("Doctype not Found")
    }
  }

}
