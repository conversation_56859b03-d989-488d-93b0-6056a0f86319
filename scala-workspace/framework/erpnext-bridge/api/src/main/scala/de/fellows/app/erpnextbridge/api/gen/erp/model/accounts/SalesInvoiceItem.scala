package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class SalesInvoiceItem(
    name: String,
    barcode: Option[String],
    item_code: Option[String],
    item_name: String,
    customer_item_code: Option[String],
    description: String,
    item_group: Option[String],
    brand: Option[String],
    qty: Option[Double],
    stock_uom: Option[String],
    uom: String,
    conversion_factor: Double,
    stock_qty: Option[Double],
    price_list_rate: Option[Double],
    base_price_list_rate: Option[Double],
    margin_type: Option[String],
    margin_rate_or_amount: Option[Double],
    rate_with_margin: Option[Double],
    discount_amount: Option[Double],
    base_rate_with_margin: Option[Double],
    rate: Double,
    amount: Double,
    item_tax_template: Option[String],
    base_rate: Double,
    base_amount: Double,
    is_free_item: Option[Int],
    net_rate: Option[Double],
    net_amount: Option[Double],
    base_net_rate: Option[Double],
    base_net_amount: Option[Double],
    delivered_by_supplier: Option[Int],
    income_account: String,
    is_fixed_asset: Option[Int],
    asset: Option[String],
    finance_book: Option[String],
    expense_account: Option[String],
    deferred_revenue_account: Option[String],
    service_stop_date: Option[String],
    enable_deferred_revenue: Option[Int],
    service_start_date: Option[String],
    service_end_date: Option[String],
    weight_per_unit: Option[Double],
    total_weight: Option[Double],
    weight_uom: Option[String],
    warehouse: Option[String],
    target_warehouse: Option[String],
    quality_inspection: Option[String],
    batch_no: Option[String],
    allow_zero_valuation_rate: Option[Int],
    actual_batch_qty: Option[Double],
    actual_qty: Option[Double],
    sales_order: Option[String],
    so_detail: Option[String],
    delivery_note: Option[String],
    dn_detail: Option[String],
    delivered_qty: Option[Double],
    cost_center: String,
    page_break: Option[Int]
)

object SalesInvoiceItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): SalesInvoiceItem = new SalesInvoiceItem(
    name = (v \ "name").get.as[String],
    barcode = (v \ "barcode").toOption.map(_.as[String]),
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    item_name = (v \ "item_name").get.as[String],
    customer_item_code = (v \ "customer_item_code").toOption.map(_.as[String]),
    description = (v \ "description").get.as[String],
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    brand = (v \ "brand").toOption.map(_.as[String]),
    qty = (v \ "qty").toOption.map(_.as[Double]),
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    uom = (v \ "uom").get.as[String],
    conversion_factor = (v \ "conversion_factor").get.as[Double],
    stock_qty = (v \ "stock_qty").toOption.map(_.as[Double]),
    price_list_rate = (v \ "price_list_rate").toOption.map(_.as[Double]),
    base_price_list_rate = (v \ "base_price_list_rate").toOption.map(_.as[Double]),
    margin_type = (v \ "margin_type").toOption.map(_.as[String]),
    margin_rate_or_amount = (v \ "margin_rate_or_amount").toOption.map(_.as[Double]),
    rate_with_margin = (v \ "rate_with_margin").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    base_rate_with_margin = (v \ "base_rate_with_margin").toOption.map(_.as[Double]),
    rate = (v \ "rate").get.as[Double],
    amount = (v \ "amount").get.as[Double],
    item_tax_template = (v \ "item_tax_template").toOption.map(_.as[String]),
    base_rate = (v \ "base_rate").get.as[Double],
    base_amount = (v \ "base_amount").get.as[Double],
    is_free_item = (v \ "is_free_item").toOption.map(_.as[Int]),
    net_rate = (v \ "net_rate").toOption.map(_.as[Double]),
    net_amount = (v \ "net_amount").toOption.map(_.as[Double]),
    base_net_rate = (v \ "base_net_rate").toOption.map(_.as[Double]),
    base_net_amount = (v \ "base_net_amount").toOption.map(_.as[Double]),
    delivered_by_supplier = (v \ "delivered_by_supplier").toOption.map(_.as[Int]),
    income_account = (v \ "income_account").get.as[String],
    is_fixed_asset = (v \ "is_fixed_asset").toOption.map(_.as[Int]),
    asset = (v \ "asset").toOption.map(_.as[String]),
    finance_book = (v \ "finance_book").toOption.map(_.as[String]),
    expense_account = (v \ "expense_account").toOption.map(_.as[String]),
    deferred_revenue_account = (v \ "deferred_revenue_account").toOption.map(_.as[String]),
    service_stop_date = (v \ "service_stop_date").toOption.map(_.as[String]),
    enable_deferred_revenue = (v \ "enable_deferred_revenue").toOption.map(_.as[Int]),
    service_start_date = (v \ "service_start_date").toOption.map(_.as[String]),
    service_end_date = (v \ "service_end_date").toOption.map(_.as[String]),
    weight_per_unit = (v \ "weight_per_unit").toOption.map(_.as[Double]),
    total_weight = (v \ "total_weight").toOption.map(_.as[Double]),
    weight_uom = (v \ "weight_uom").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    target_warehouse = (v \ "target_warehouse").toOption.map(_.as[String]),
    quality_inspection = (v \ "quality_inspection").toOption.map(_.as[String]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    allow_zero_valuation_rate = (v \ "allow_zero_valuation_rate").toOption.map(_.as[Int]),
    actual_batch_qty = (v \ "actual_batch_qty").toOption.map(_.as[Double]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    sales_order = (v \ "sales_order").toOption.map(_.as[String]),
    so_detail = (v \ "so_detail").toOption.map(_.as[String]),
    delivery_note = (v \ "delivery_note").toOption.map(_.as[String]),
    dn_detail = (v \ "dn_detail").toOption.map(_.as[String]),
    delivered_qty = (v \ "delivered_qty").toOption.map(_.as[Double]),
    cost_center = (v \ "cost_center").get.as[String],
    page_break = (v \ "page_break").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[SalesInvoiceItem] = Reads[SalesInvoiceItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Sales Invoice Item") => JsSuccess(SalesInvoiceItem(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
