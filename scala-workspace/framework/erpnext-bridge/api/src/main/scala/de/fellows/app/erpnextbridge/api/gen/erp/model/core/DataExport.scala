package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DataExport(
    name: String,
    reference_doctype: String,
    file_type: String
)

object DataExport {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DataExport = new DataExport(
    name = (v \ "name").get.as[String],
    reference_doctype = (v \ "reference_doctype").get.as[String],
    file_type = (v \ "file_type").get.as[String]
  )

  implicit val reads: Reads[DataExport] = Reads[DataExport] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Data Export") => JsSuccess(DataExport(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
