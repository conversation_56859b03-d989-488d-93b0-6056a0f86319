package de.fellows.app.erpnextbridge.api.gen.erp.model.pcb

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class WorkSchedulingItem(
    name: String,
    operation: String,
    time_before_finish: Int,
    operation_title: Option[String]
)

object WorkSchedulingItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): WorkSchedulingItem = new WorkSchedulingItem(
    name = (v \ "name").get.as[String],
    operation = (v \ "operation").get.as[String],
    time_before_finish = (v \ "time_before_finish").get.as[Int],
    operation_title = (v \ "operation_title").toOption.map(_.as[String])
  )

  implicit val reads: Reads[WorkSchedulingItem] = Reads[WorkSchedulingItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Work Scheduling Item") => JsSuccess(WorkSchedulingItem(js))
      case Some(_)                      => JsError("Wrong Doctype")
      case _                            => JsError("Doctype not Found")
    }
  }

}
