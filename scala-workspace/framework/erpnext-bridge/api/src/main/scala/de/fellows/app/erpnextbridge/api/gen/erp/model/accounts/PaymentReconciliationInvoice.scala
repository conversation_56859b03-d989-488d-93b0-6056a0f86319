package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentReconciliationInvoice(
    name: String,
    invoice_type: Option[String],
    invoice_date: Option[String],
    amount: Option[Double],
    outstanding_amount: Option[Double]
)

object PaymentReconciliationInvoice {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentReconciliationInvoice = new PaymentReconciliationInvoice(
    name = (v \ "name").get.as[String],
    invoice_type = (v \ "invoice_type").toOption.map(_.as[String]),
    invoice_date = (v \ "invoice_date").toOption.map(_.as[String]),
    amount = (v \ "amount").toOption.map(_.as[Double]),
    outstanding_amount = (v \ "outstanding_amount").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[PaymentReconciliationInvoice] = Reads[PaymentReconciliationInvoice] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Reconciliation Invoice") => JsSuccess(PaymentReconciliationInvoice(js))
      case Some(_)                                => JsError("Wrong Doctype")
      case _                                      => JsError("Doctype not Found")
    }
  }

}
