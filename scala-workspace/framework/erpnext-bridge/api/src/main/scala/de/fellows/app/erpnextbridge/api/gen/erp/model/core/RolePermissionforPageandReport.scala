package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class RolePermissionforPageandReport(
    name: String,
    set_role_for: String,
    page: Option[String],
    report: Option[String],
    disable_prepared_report: Option[Int],
    roles: Option[Seq[HasRole]]
)

object RolePermissionforPageandReport {
  val NAME_FIELD = "name"

  def apply(v: JsValue): RolePermissionforPageandReport = new RolePermissionforPageandReport(
    name = (v \ "name").get.as[String],
    set_role_for = (v \ "set_role_for").get.as[String],
    page = (v \ "page").toOption.map(_.as[String]),
    report = (v \ "report").toOption.map(_.as[String]),
    disable_prepared_report = (v \ "disable_prepared_report").toOption.map(_.as[Int]),
    roles = (v \ "roles").toOption.map(x => x.as[JsArray].value.map(_.as[HasRole]).toSeq)
  )

  implicit val reads: Reads[RolePermissionforPageandReport] = Reads[RolePermissionforPageandReport] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Role Permission for Page and Report") => JsSuccess(RolePermissionforPageandReport(js))
      case Some(_)                                     => JsError("Wrong Doctype")
      case _                                           => JsError("Doctype not Found")
    }
  }

}
