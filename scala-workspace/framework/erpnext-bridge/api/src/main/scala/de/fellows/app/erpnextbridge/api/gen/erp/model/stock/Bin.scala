package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Bin(
    name: String,
    warehouse: Option[String],
    item_code: Option[String],
    reserved_qty: Option[Double],
    actual_qty: Option[Double],
    ordered_qty: Option[Double],
    indented_qty: Option[Double],
    planned_qty: Option[Double],
    projected_qty: Option[Double],
    reserved_qty_for_production: Option[Double],
    reserved_qty_for_sub_contract: Option[Double],
    ma_rate: Option[Double],
    stock_uom: Option[String],
    fcfs_rate: Option[Double],
    valuation_rate: Option[Double],
    stock_value: Option[Double]
)

object Bin {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Bin = new Bin(
    name = (v \ "name").get.as[String],
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    reserved_qty = (v \ "reserved_qty").toOption.map(_.as[Double]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    ordered_qty = (v \ "ordered_qty").toOption.map(_.as[Double]),
    indented_qty = (v \ "indented_qty").toOption.map(_.as[Double]),
    planned_qty = (v \ "planned_qty").toOption.map(_.as[Double]),
    projected_qty = (v \ "projected_qty").toOption.map(_.as[Double]),
    reserved_qty_for_production = (v \ "reserved_qty_for_production").toOption.map(_.as[Double]),
    reserved_qty_for_sub_contract = (v \ "reserved_qty_for_sub_contract").toOption.map(_.as[Double]),
    ma_rate = (v \ "ma_rate").toOption.map(_.as[Double]),
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    fcfs_rate = (v \ "fcfs_rate").toOption.map(_.as[Double]),
    valuation_rate = (v \ "valuation_rate").toOption.map(_.as[Double]),
    stock_value = (v \ "stock_value").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[Bin] = Reads[Bin] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bin") => JsSuccess(Bin(js))
      case Some(_)     => JsError("Wrong Doctype")
      case _           => JsError("Doctype not Found")
    }
  }

}
