package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ModuleDef(
    module_name: String,
    app_name: String,
    restrict_to_domain: Option[String]
)

object ModuleDef {
  val NAME_FIELD = "module_name"

  def apply(v: JsValue): ModuleDef = new ModuleDef(
    module_name = (v \ "module_name").get.as[String],
    app_name = (v \ "app_name").get.as[String],
    restrict_to_domain = (v \ "restrict_to_domain").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ModuleDef] = Reads[ModuleDef] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Module Def") => JsSuccess(ModuleDef(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
