package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class POSClosingVoucher(
    name: String,
    period_start_date: String,
    period_end_date: String,
    posting_date: String,
    company: String,
    pos_profile: String,
    user: String,
    expense_amount: Option[Double],
    custody_amount: Option[String],
    total_amount: Option[Double],
    difference: Option[Double],
    payment_reconciliation: Option[Seq[POSClosingVoucherDetails]],
    grand_total: Option[Double],
    net_total: Option[Double],
    total_quantity: Option[Double],
    taxes: Option[Seq[POSClosingVoucherTaxes]],
    sales_invoices_summary: Option[Seq[POSClosingVoucherInvoices]],
    amended_from: Option[String]
)

object POSClosingVoucher {
  val NAME_FIELD = "name"

  def apply(v: JsValue): POSClosingVoucher = new POSClosingVoucher(
    name = (v \ "name").get.as[String],
    period_start_date = (v \ "period_start_date").get.as[String],
    period_end_date = (v \ "period_end_date").get.as[String],
    posting_date = (v \ "posting_date").get.as[String],
    company = (v \ "company").get.as[String],
    pos_profile = (v \ "pos_profile").get.as[String],
    user = (v \ "user").get.as[String],
    expense_amount = (v \ "expense_amount").toOption.map(_.as[Double]),
    custody_amount = (v \ "custody_amount").toOption.map(_.as[String]),
    total_amount = (v \ "total_amount").toOption.map(_.as[Double]),
    difference = (v \ "difference").toOption.map(_.as[Double]),
    payment_reconciliation =
      (v \ "payment_reconciliation").toOption.map(x => x.as[JsArray].value.map(_.as[POSClosingVoucherDetails]).toSeq),
    grand_total = (v \ "grand_total").toOption.map(_.as[Double]),
    net_total = (v \ "net_total").toOption.map(_.as[Double]),
    total_quantity = (v \ "total_quantity").toOption.map(_.as[Double]),
    taxes = (v \ "taxes").toOption.map(x => x.as[JsArray].value.map(_.as[POSClosingVoucherTaxes]).toSeq),
    sales_invoices_summary =
      (v \ "sales_invoices_summary").toOption.map(x => x.as[JsArray].value.map(_.as[POSClosingVoucherInvoices]).toSeq),
    amended_from = (v \ "amended_from").toOption.map(_.as[String])
  )

  implicit val reads: Reads[POSClosingVoucher] = Reads[POSClosingVoucher] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("POS Closing Voucher") => JsSuccess(POSClosingVoucher(js))
      case Some(_)                     => JsError("Wrong Doctype")
      case _                           => JsError("Doctype not Found")
    }
  }

}
