package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class HasDomain(
    name: String,
    domain: Option[String]
)

object HasDomain {
  val NAME_FIELD = "name"

  def apply(v: JsValue): HasDomain = new HasDomain(
    name = (v \ "name").get.as[String],
    domain = (v \ "domain").toOption.map(_.as[String])
  )

  implicit val reads: Reads[HasDomain] = Reads[HasDomain] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Has Domain") => JsSuccess(HasDomain(js))
      case Some(_)            => JsError("Wrong Doctype")
      case _                  => JsError("Doctype not Found")
    }
  }

}
