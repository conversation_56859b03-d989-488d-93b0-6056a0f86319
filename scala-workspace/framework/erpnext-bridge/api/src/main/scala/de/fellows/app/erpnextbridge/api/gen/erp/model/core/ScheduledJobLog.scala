package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ScheduledJobLog(
    name: String,
    status: String,
    scheduled_job_type: String
)

object ScheduledJobLog {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ScheduledJobLog = new ScheduledJobLog(
    name = (v \ "name").get.as[String],
    status = (v \ "status").get.as[String],
    scheduled_job_type = (v \ "scheduled_job_type").get.as[String]
  )

  implicit val reads: Reads[ScheduledJobLog] = Reads[ScheduledJobLog] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Scheduled Job Log") => JsSuccess(ScheduledJobLog(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
