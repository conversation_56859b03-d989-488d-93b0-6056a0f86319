package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemBarcode(
    barcode: Option[String],
    barcode_type: Option[String]
)

object ItemBarcode {
  val NAME_FIELD = "barcode"

  def apply(v: JsValue): ItemBarcode = new ItemBarcode(
    barcode = (v \ "barcode").toOption.map(_.as[String]),
    barcode_type = (v \ "barcode_type").toOption.map(_.as[String])
  )

  implicit val reads: Reads[ItemBarcode] = Reads[ItemBarcode] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Barcode") => JsSuccess(ItemBarcode(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
