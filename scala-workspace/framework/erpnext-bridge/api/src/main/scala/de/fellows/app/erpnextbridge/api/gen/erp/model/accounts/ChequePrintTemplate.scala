package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ChequePrintTemplate(
    has_print_format: Option[Int],
    bank_name: String,
    cheque_size: Option[String],
    starting_position_from_top_edge: Option[Double],
    cheque_width: Option[Double],
    cheque_height: Option[Double],
    is_account_payable: Option[Int],
    acc_pay_dist_from_top_edge: Option[Double],
    acc_pay_dist_from_left_edge: Option[Double],
    message_to_show: Option[String],
    date_dist_from_top_edge: Option[Double],
    date_dist_from_left_edge: Option[Double],
    payer_name_from_top_edge: Option[Double],
    payer_name_from_left_edge: Option[Double],
    amt_in_words_from_top_edge: Option[Double],
    amt_in_words_from_left_edge: Option[Double],
    amt_in_word_width: Option[Double],
    amt_in_words_line_spacing: Option[Double],
    amt_in_figures_from_top_edge: Option[Double],
    amt_in_figures_from_left_edge: Option[Double],
    acc_no_dist_from_top_edge: Option[Double],
    acc_no_dist_from_left_edge: Option[Double],
    signatory_from_top_edge: Option[Double],
    signatory_from_left_edge: Option[Double]
)

object ChequePrintTemplate {
  val NAME_FIELD = "bank_name"

  def apply(v: JsValue): ChequePrintTemplate = new ChequePrintTemplate(
    has_print_format = (v \ "has_print_format").toOption.map(_.as[Int]),
    bank_name = (v \ "bank_name").get.as[String],
    cheque_size = (v \ "cheque_size").toOption.map(_.as[String]),
    starting_position_from_top_edge = (v \ "starting_position_from_top_edge").toOption.map(_.as[Double]),
    cheque_width = (v \ "cheque_width").toOption.map(_.as[Double]),
    cheque_height = (v \ "cheque_height").toOption.map(_.as[Double]),
    is_account_payable = (v \ "is_account_payable").toOption.map(_.as[Int]),
    acc_pay_dist_from_top_edge = (v \ "acc_pay_dist_from_top_edge").toOption.map(_.as[Double]),
    acc_pay_dist_from_left_edge = (v \ "acc_pay_dist_from_left_edge").toOption.map(_.as[Double]),
    message_to_show = (v \ "message_to_show").toOption.map(_.as[String]),
    date_dist_from_top_edge = (v \ "date_dist_from_top_edge").toOption.map(_.as[Double]),
    date_dist_from_left_edge = (v \ "date_dist_from_left_edge").toOption.map(_.as[Double]),
    payer_name_from_top_edge = (v \ "payer_name_from_top_edge").toOption.map(_.as[Double]),
    payer_name_from_left_edge = (v \ "payer_name_from_left_edge").toOption.map(_.as[Double]),
    amt_in_words_from_top_edge = (v \ "amt_in_words_from_top_edge").toOption.map(_.as[Double]),
    amt_in_words_from_left_edge = (v \ "amt_in_words_from_left_edge").toOption.map(_.as[Double]),
    amt_in_word_width = (v \ "amt_in_word_width").toOption.map(_.as[Double]),
    amt_in_words_line_spacing = (v \ "amt_in_words_line_spacing").toOption.map(_.as[Double]),
    amt_in_figures_from_top_edge = (v \ "amt_in_figures_from_top_edge").toOption.map(_.as[Double]),
    amt_in_figures_from_left_edge = (v \ "amt_in_figures_from_left_edge").toOption.map(_.as[Double]),
    acc_no_dist_from_top_edge = (v \ "acc_no_dist_from_top_edge").toOption.map(_.as[Double]),
    acc_no_dist_from_left_edge = (v \ "acc_no_dist_from_left_edge").toOption.map(_.as[Double]),
    signatory_from_top_edge = (v \ "signatory_from_top_edge").toOption.map(_.as[Double]),
    signatory_from_left_edge = (v \ "signatory_from_left_edge").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[ChequePrintTemplate] = Reads[ChequePrintTemplate] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Cheque Print Template") => JsSuccess(ChequePrintTemplate(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
