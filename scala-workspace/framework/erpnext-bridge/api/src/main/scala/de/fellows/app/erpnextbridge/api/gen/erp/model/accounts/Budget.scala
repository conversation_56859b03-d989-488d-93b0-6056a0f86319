package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Budget(
    name: String,
    budget_against: String,
    company: String,
    cost_center: Option[String],
    project: Option[String],
    fiscal_year: String,
    monthly_distribution: Option[String],
    amended_from: Option[String],
    applicable_on_material_request: Option[Int],
    action_if_annual_budget_exceeded_on_mr: Option[String],
    action_if_accumulated_monthly_budget_exceeded_on_mr: Option[String],
    applicable_on_purchase_order: Option[Int],
    action_if_annual_budget_exceeded_on_po: Option[String],
    action_if_accumulated_monthly_budget_exceeded_on_po: Option[String],
    applicable_on_booking_actual_expenses: Option[Int],
    action_if_annual_budget_exceeded: Option[String],
    action_if_accumulated_monthly_budget_exceeded: Option[String],
    accounts: Seq[BudgetAccount]
)

object Budget {
  val NAME_FIELD = "name"

  def apply(v: JsValue): Budget = new Budget(
    name = (v \ "name").get.as[String],
    budget_against = (v \ "budget_against").get.as[String],
    company = (v \ "company").get.as[String],
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    fiscal_year = (v \ "fiscal_year").get.as[String],
    monthly_distribution = (v \ "monthly_distribution").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    applicable_on_material_request = (v \ "applicable_on_material_request").toOption.map(_.as[Int]),
    action_if_annual_budget_exceeded_on_mr = (v \ "action_if_annual_budget_exceeded_on_mr").toOption.map(_.as[String]),
    action_if_accumulated_monthly_budget_exceeded_on_mr =
      (v \ "action_if_accumulated_monthly_budget_exceeded_on_mr").toOption.map(_.as[String]),
    applicable_on_purchase_order = (v \ "applicable_on_purchase_order").toOption.map(_.as[Int]),
    action_if_annual_budget_exceeded_on_po = (v \ "action_if_annual_budget_exceeded_on_po").toOption.map(_.as[String]),
    action_if_accumulated_monthly_budget_exceeded_on_po =
      (v \ "action_if_accumulated_monthly_budget_exceeded_on_po").toOption.map(_.as[String]),
    applicable_on_booking_actual_expenses = (v \ "applicable_on_booking_actual_expenses").toOption.map(_.as[Int]),
    action_if_annual_budget_exceeded = (v \ "action_if_annual_budget_exceeded").toOption.map(_.as[String]),
    action_if_accumulated_monthly_budget_exceeded =
      (v \ "action_if_accumulated_monthly_budget_exceeded").toOption.map(_.as[String]),
    accounts = (v \ "accounts").toOption.map(x => x.as[JsArray].value.map(_.as[BudgetAccount])).get.toSeq
  )

  implicit val reads: Reads[Budget] = Reads[Budget] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Budget") => JsSuccess(Budget(js))
      case Some(_)        => JsError("Wrong Doctype")
      case _              => JsError("Doctype not Found")
    }
  }

}
