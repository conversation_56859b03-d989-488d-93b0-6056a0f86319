package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BlanketOrderItem(
    name: String,
    item_code: String,
    item_name: Option[String],
    qty: Option[Double],
    rate: Double,
    ordered_qty: Option[Double],
    terms_and_conditions: Option[String]
)

object BlanketOrderItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BlanketOrderItem = new BlanketOrderItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    qty = (v \ "qty").toOption.map(_.as[Double]),
    rate = (v \ "rate").get.as[Double],
    ordered_qty = (v \ "ordered_qty").toOption.map(_.as[Double]),
    terms_and_conditions = (v \ "terms_and_conditions").toOption.map(_.as[String])
  )

  implicit val reads: Reads[BlanketOrderItem] = Reads[BlanketOrderItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Blanket Order Item") => JsSuccess(BlanketOrderItem(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
