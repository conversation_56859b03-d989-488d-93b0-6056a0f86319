package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class GSTAccount(
    name: String,
    company: String,
    cgst_account: String,
    sgst_account: String,
    igst_account: String,
    cess_account: Option[String]
)

object GSTAccount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): GSTAccount = new GSTAccount(
    name = (v \ "name").get.as[String],
    company = (v \ "company").get.as[String],
    cgst_account = (v \ "cgst_account").get.as[String],
    sgst_account = (v \ "sgst_account").get.as[String],
    igst_account = (v \ "igst_account").get.as[String],
    cess_account = (v \ "cess_account").toOption.map(_.as[String])
  )

  implicit val reads: Reads[GSTAccount] = Reads[GSTAccount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("GST Account") => JsSuccess(GSTAccount(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
