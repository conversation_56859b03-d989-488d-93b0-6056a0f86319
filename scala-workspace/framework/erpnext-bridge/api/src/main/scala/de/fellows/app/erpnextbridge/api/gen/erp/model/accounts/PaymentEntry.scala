package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, JsSuc<PERSON>, <PERSON>s<PERSON><PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PaymentEntry(
    name: String,
    naming_series: String,
    payment_type: String,
    payment_order_status: Option[String],
    posting_date: String,
    company: String,
    mode_of_payment: Option[String],
    party_type: Option[String],
    party_name: Option[String],
    bank_account: Option[String],
    party_bank_account: Option[String],
    contact_person: Option[String],
    contact_email: Option[String],
    party_balance: Option[Double],
    paid_from: String,
    paid_from_account_currency: String,
    paid_from_account_balance: Option[Double],
    paid_to: String,
    paid_to_account_currency: String,
    paid_to_account_balance: Option[Double],
    paid_amount: Double,
    source_exchange_rate: Double,
    base_paid_amount: Double,
    received_amount: Double,
    target_exchange_rate: Double,
    base_received_amount: Double,
    references: Option[Seq[PaymentEntryReference]],
    total_allocated_amount: Option[Double],
    base_total_allocated_amount: Option[Double],
    unallocated_amount: Option[Double],
    difference_amount: Option[Double],
    deductions: Option[Seq[PaymentEntryDeduction]],
    reference_no: Option[String],
    reference_date: Option[String],
    clearance_date: Option[String],
    project: Option[String],
    cost_center: Option[String],
    status: Option[String],
    letter_head: Option[String],
    print_heading: Option[String],
    payment_order: Option[String],
    auto_repeat: Option[String],
    amended_from: Option[String],
    title: Option[String]
)

object PaymentEntry {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PaymentEntry = new PaymentEntry(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    payment_type = (v \ "payment_type").get.as[String],
    payment_order_status = (v \ "payment_order_status").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").get.as[String],
    company = (v \ "company").get.as[String],
    mode_of_payment = (v \ "mode_of_payment").toOption.map(_.as[String]),
    party_type = (v \ "party_type").toOption.map(_.as[String]),
    party_name = (v \ "party_name").toOption.map(_.as[String]),
    bank_account = (v \ "bank_account").toOption.map(_.as[String]),
    party_bank_account = (v \ "party_bank_account").toOption.map(_.as[String]),
    contact_person = (v \ "contact_person").toOption.map(_.as[String]),
    contact_email = (v \ "contact_email").toOption.map(_.as[String]),
    party_balance = (v \ "party_balance").toOption.map(_.as[Double]),
    paid_from = (v \ "paid_from").get.as[String],
    paid_from_account_currency = (v \ "paid_from_account_currency").get.as[String],
    paid_from_account_balance = (v \ "paid_from_account_balance").toOption.map(_.as[Double]),
    paid_to = (v \ "paid_to").get.as[String],
    paid_to_account_currency = (v \ "paid_to_account_currency").get.as[String],
    paid_to_account_balance = (v \ "paid_to_account_balance").toOption.map(_.as[Double]),
    paid_amount = (v \ "paid_amount").get.as[Double],
    source_exchange_rate = (v \ "source_exchange_rate").get.as[Double],
    base_paid_amount = (v \ "base_paid_amount").get.as[Double],
    received_amount = (v \ "received_amount").get.as[Double],
    target_exchange_rate = (v \ "target_exchange_rate").get.as[Double],
    base_received_amount = (v \ "base_received_amount").get.as[Double],
    references = (v \ "references").toOption.map(x => x.as[JsArray].value.map(_.as[PaymentEntryReference]).toSeq),
    total_allocated_amount = (v \ "total_allocated_amount").toOption.map(_.as[Double]),
    base_total_allocated_amount = (v \ "base_total_allocated_amount").toOption.map(_.as[Double]),
    unallocated_amount = (v \ "unallocated_amount").toOption.map(_.as[Double]),
    difference_amount = (v \ "difference_amount").toOption.map(_.as[Double]),
    deductions = (v \ "deductions").toOption.map(x => x.as[JsArray].value.map(_.as[PaymentEntryDeduction]).toSeq),
    reference_no = (v \ "reference_no").toOption.map(_.as[String]),
    reference_date = (v \ "reference_date").toOption.map(_.as[String]),
    clearance_date = (v \ "clearance_date").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    status = (v \ "status").toOption.map(_.as[String]),
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    print_heading = (v \ "print_heading").toOption.map(_.as[String]),
    payment_order = (v \ "payment_order").toOption.map(_.as[String]),
    auto_repeat = (v \ "auto_repeat").toOption.map(_.as[String]),
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    title = (v \ "title").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PaymentEntry] = Reads[PaymentEntry] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Payment Entry") => JsSuccess(PaymentEntry(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
