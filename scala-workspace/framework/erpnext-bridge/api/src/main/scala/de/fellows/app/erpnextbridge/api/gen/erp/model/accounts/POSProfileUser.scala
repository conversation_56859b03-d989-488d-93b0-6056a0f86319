package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON>s<PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class POSProfileUser(
    name: String,
    default: Option[Int],
    user: Option[String]
)

object POSProfileUser {
  val NAME_FIELD = "name"

  def apply(v: JsValue): POSProfileUser = new POSProfileUser(
    name = (v \ "name").get.as[String],
    default = (v \ "default").toOption.map(_.as[Int]),
    user = (v \ "user").toOption.map(_.as[String])
  )

  implicit val reads: Reads[POSProfileUser] = Reads[POSProfileUser] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("POS Profile User") => JsSuccess(POSProfileUser(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
