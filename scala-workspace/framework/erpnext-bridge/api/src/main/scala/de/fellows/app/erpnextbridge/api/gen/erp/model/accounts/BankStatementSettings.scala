package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BankStatementSettings(
    name: String,
    bank_account: String,
    date_format: Option[String],
    header_items: Option[Seq[BankStatementSettingsItem]],
    mapped_items: Option[Seq[BankStatementTransactionSettingsItem]]
)

object BankStatementSettings {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BankStatementSettings = new BankStatementSettings(
    name = (v \ "name").get.as[String],
    bank_account = (v \ "bank_account").get.as[String],
    date_format = (v \ "date_format").toOption.map(_.as[String]),
    header_items =
      (v \ "header_items").toOption.map(x => x.as[JsArray].value.map(_.as[BankStatementSettingsItem]).toSeq),
    mapped_items =
      (v \ "mapped_items").toOption.map(x => x.as[JsArray].value.map(_.as[BankStatementTransactionSettingsItem]).toSeq)
  )

  implicit val reads: Reads[BankStatementSettings] = Reads[BankStatementSettings] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Bank Statement Settings") => JsSuccess(BankStatementSettings(js))
      case Some(_)                         => JsError("Wrong Doctype")
      case _                               => JsError("Doctype not Found")
    }
  }

}
