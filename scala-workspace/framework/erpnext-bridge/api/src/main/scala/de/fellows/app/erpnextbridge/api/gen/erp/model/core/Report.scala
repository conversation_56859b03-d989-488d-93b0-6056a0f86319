package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class Report(
    report_name: String,
    ref_doctype: String,
    reference_report: Option[String],
    is_standard: String,
    module: Option[String],
    report_type: String,
    letter_head: Option[String],
    add_total_row: Option[Int],
    disabled: Option[Int],
    disable_prepared_report: Option[Int],
    prepared_report: Option[Int],
    roles: Option[Seq[HasRole]]
)

object Report {
  val NAME_FIELD = "report_name"

  def apply(v: JsValue): Report = new Report(
    report_name = (v \ "report_name").get.as[String],
    ref_doctype = (v \ "ref_doctype").get.as[String],
    reference_report = (v \ "reference_report").toOption.map(_.as[String]),
    is_standard = (v \ "is_standard").get.as[String],
    module = (v \ "module").toOption.map(_.as[String]),
    report_type = (v \ "report_type").get.as[String],
    letter_head = (v \ "letter_head").toOption.map(_.as[String]),
    add_total_row = (v \ "add_total_row").toOption.map(_.as[Int]),
    disabled = (v \ "disabled").toOption.map(_.as[Int]),
    disable_prepared_report = (v \ "disable_prepared_report").toOption.map(_.as[Int]),
    prepared_report = (v \ "prepared_report").toOption.map(_.as[Int]),
    roles = (v \ "roles").toOption.map(x => x.as[JsArray].value.map(_.as[HasRole]).toSeq)
  )

  implicit val reads: Reads[Report] = Reads[Report] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Report") => JsSuccess(Report(js))
      case Some(_)        => JsError("Wrong Doctype")
      case _              => JsError("Doctype not Found")
    }
  }

}
