package de.fellows.app.erpnextbridge.api

import akka.NotUsed
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceCall}
import com.typesafe.config.ConfigFactory
import de.fellows.app.erpnext.api.{ListResponse, ObjectContainer}
import play.api.libs.json.Reads

import java.net.URL
import scala.concurrent.{ExecutionContext, Future}

abstract class ErpNextBridgeApi extends Service {
  def listDocuments(
                     team: String,
                     docType: String,
                     limit_start: Option[Int] = None,
                     limit_page_length: Option[Int] = None,
                     fields: Option[Seq[String]] = None,
                     order_by: Option[String] = None,
                     filters: Option[Seq[String]] = None
                   ): ServiceCall[NotUsed, ListResponse]

  def getDocument(team: String, docType: String, docName: String): ServiceCall[NotUsed, ObjectContainer]

  //  def listDocuments(docType: String): ServiceCall[NotUsed, ListResponse]

  val subPath = "erpnext"
  val basePath = s"/api/$subPath"

  val internalBasePath = s"/internal/$subPath/:team"

  def getTypedDocument[T](team: String, docType: String, name: String)(implicit
                                                                       ctx: ExecutionContext,
                                                                       reads: Reads[T]
  ): Future[T] =
    getDocument(team, docType, name).invoke().map { r =>
      r.data.as[T]
    }

  def listTypedDocument[T](
                            team: String,
                            docType: String,
                            limit_start: Option[Int] = None,
                            limit_page_length: Option[Int] = None,
                            order_by: Option[String] = None,
                            filters: Option[Seq[String]] = None
                          )(implicit ctx: ExecutionContext, reads: Reads[T]): Future[Seq[T]] = {
    println(s"list doc filter ${filters}")
    listDocuments(
      team,
      docType,
      limit_start,
      limit_page_length,
      fields = Some(Seq("name")),
      order_by,
      filters
    ).invoke().flatMap { lr =>
      Future.sequence(lr.data.map { r =>
        getTypedDocument[T](team, docType, (r \ "name").get.as[String])
      })
    }

  }

  override def descriptor: Descriptor = {
    import Service._

    named("erpnext-bridge")
      .withCalls(
        restCall(
          Method.GET,
          s"$internalBasePath/resource/:docType?limit_start&limit_page_length&fields&order_by&filters",
          listDocuments _
        ),
        restCall(Method.GET, s"$internalBasePath/resource/:docType/:docName", getDocument _)
        //        restCall(Method.GET, s"$internalBasePath/resource/:docType", listDocuments _),
      )
      .withAutoAcl(true)

    // .withCircuitBreaker(CircuitBreaker.identifiedBy("camunda"));
  }
}

object ErpNextBridgeApi {
  val conf = ConfigFactory.load()
  val endpoint = conf.getString("fellows.services.erpnext-bridge.endpoint")

  def file[T](path: String): URL = {
    val sanitized =
      if (path.startsWith("/")) {
        path
      } else {
        s"/$path"
      }
    new URL(s"$endpoint$sanitized")
  }
}
//
