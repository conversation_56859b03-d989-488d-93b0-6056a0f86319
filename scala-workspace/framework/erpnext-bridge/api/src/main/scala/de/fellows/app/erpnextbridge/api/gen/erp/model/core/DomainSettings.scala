package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads}
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class DomainSettings(
    name: String,
    active_domains: Option[Seq[HasDomain]]
)

object DomainSettings {
  val NAME_FIELD = "name"

  def apply(v: JsValue): DomainSettings = new DomainSettings(
    name = (v \ "name").get.as[String],
    active_domains = (v \ "active_domains").toOption.map(x => x.as[JsArray].value.map(_.as[HasDomain]).toSeq)
  )

  implicit val reads: Reads[DomainSettings] = Reads[DomainSettings] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Domain Settings") => JsSuccess(DomainSettings(js))
      case Some(_)                 => JsError("Wrong Doctype")
      case _                       => JsError("Doctype not Found")
    }
  }

}
