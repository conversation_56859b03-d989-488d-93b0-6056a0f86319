package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PricingRule(
    title: String,
    disable: Option[Int],
    apply_on: String,
    price_or_product_discount: String,
    warehouse: Option[String],
    items: Option[Seq[PricingRuleItemCode]],
    item_groups: Option[Seq[PricingRuleItemGroup]],
    brands: Option[Seq[PricingRuleBrand]],
    mixed_conditions: Option[Int],
    is_cumulative: Option[Int],
    coupon_code_based: Option[Int],
    apply_rule_on_other: Option[String],
    other_item_code: Option[String],
    other_item_group: Option[String],
    other_brand: Option[String],
    selling: Option[Int],
    buying: Option[Int],
    applicable_for: Option[String],
    customer: Option[String],
    customer_group: Option[String],
    territory: Option[String],
    sales_partner: Option[String],
    campaign: Option[String],
    supplier: Option[String],
    supplier_group: Option[String],
    min_qty: Option[Double],
    max_qty: Option[Double],
    min_amt: Option[Double],
    max_amt: Option[Double],
    valid_from: Option[String],
    valid_upto: Option[String],
    company: Option[String],
    currency: String,
    margin_type: Option[String],
    margin_rate_or_amount: Option[Double],
    rate_or_discount: Option[String],
    apply_discount_on: Option[String],
    rate: Option[Double],
    discount_amount: Option[Double],
    discount_percentage: Option[Double],
    for_price_list: Option[String],
    same_item: Option[Int],
    free_item: Option[String],
    free_qty: Option[Double],
    free_item_uom: Option[String],
    free_item_rate: Option[Double],
    priority: Option[String],
    apply_multiple_pricing_rules: Option[Int],
    apply_discount_on_rate: Option[Int],
    validate_applied_rule: Option[Int],
    promotional_scheme_id: Option[String],
    promotional_scheme: Option[String]
)

object PricingRule {
  val NAME_FIELD = "title"

  def apply(v: JsValue): PricingRule = new PricingRule(
    title = (v \ "title").get.as[String],
    disable = (v \ "disable").toOption.map(_.as[Int]),
    apply_on = (v \ "apply_on").get.as[String],
    price_or_product_discount = (v \ "price_or_product_discount").get.as[String],
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[PricingRuleItemCode]).toSeq),
    item_groups = (v \ "item_groups").toOption.map(x => x.as[JsArray].value.map(_.as[PricingRuleItemGroup]).toSeq),
    brands = (v \ "brands").toOption.map(x => x.as[JsArray].value.map(_.as[PricingRuleBrand]).toSeq),
    mixed_conditions = (v \ "mixed_conditions").toOption.map(_.as[Int]),
    is_cumulative = (v \ "is_cumulative").toOption.map(_.as[Int]),
    coupon_code_based = (v \ "coupon_code_based").toOption.map(_.as[Int]),
    apply_rule_on_other = (v \ "apply_rule_on_other").toOption.map(_.as[String]),
    other_item_code = (v \ "other_item_code").toOption.map(_.as[String]),
    other_item_group = (v \ "other_item_group").toOption.map(_.as[String]),
    other_brand = (v \ "other_brand").toOption.map(_.as[String]),
    selling = (v \ "selling").toOption.map(_.as[Int]),
    buying = (v \ "buying").toOption.map(_.as[Int]),
    applicable_for = (v \ "applicable_for").toOption.map(_.as[String]),
    customer = (v \ "customer").toOption.map(_.as[String]),
    customer_group = (v \ "customer_group").toOption.map(_.as[String]),
    territory = (v \ "territory").toOption.map(_.as[String]),
    sales_partner = (v \ "sales_partner").toOption.map(_.as[String]),
    campaign = (v \ "campaign").toOption.map(_.as[String]),
    supplier = (v \ "supplier").toOption.map(_.as[String]),
    supplier_group = (v \ "supplier_group").toOption.map(_.as[String]),
    min_qty = (v \ "min_qty").toOption.map(_.as[Double]),
    max_qty = (v \ "max_qty").toOption.map(_.as[Double]),
    min_amt = (v \ "min_amt").toOption.map(_.as[Double]),
    max_amt = (v \ "max_amt").toOption.map(_.as[Double]),
    valid_from = (v \ "valid_from").toOption.map(_.as[String]),
    valid_upto = (v \ "valid_upto").toOption.map(_.as[String]),
    company = (v \ "company").toOption.map(_.as[String]),
    currency = (v \ "currency").get.as[String],
    margin_type = (v \ "margin_type").toOption.map(_.as[String]),
    margin_rate_or_amount = (v \ "margin_rate_or_amount").toOption.map(_.as[Double]),
    rate_or_discount = (v \ "rate_or_discount").toOption.map(_.as[String]),
    apply_discount_on = (v \ "apply_discount_on").toOption.map(_.as[String]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    discount_percentage = (v \ "discount_percentage").toOption.map(_.as[Double]),
    for_price_list = (v \ "for_price_list").toOption.map(_.as[String]),
    same_item = (v \ "same_item").toOption.map(_.as[Int]),
    free_item = (v \ "free_item").toOption.map(_.as[String]),
    free_qty = (v \ "free_qty").toOption.map(_.as[Double]),
    free_item_uom = (v \ "free_item_uom").toOption.map(_.as[String]),
    free_item_rate = (v \ "free_item_rate").toOption.map(_.as[Double]),
    priority = (v \ "priority").toOption.map(_.as[String]),
    apply_multiple_pricing_rules = (v \ "apply_multiple_pricing_rules").toOption.map(_.as[Int]),
    apply_discount_on_rate = (v \ "apply_discount_on_rate").toOption.map(_.as[Int]),
    validate_applied_rule = (v \ "validate_applied_rule").toOption.map(_.as[Int]),
    promotional_scheme_id = (v \ "promotional_scheme_id").toOption.map(_.as[String]),
    promotional_scheme = (v \ "promotional_scheme").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PricingRule] = Reads[PricingRule] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Pricing Rule") => JsSuccess(PricingRule(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
