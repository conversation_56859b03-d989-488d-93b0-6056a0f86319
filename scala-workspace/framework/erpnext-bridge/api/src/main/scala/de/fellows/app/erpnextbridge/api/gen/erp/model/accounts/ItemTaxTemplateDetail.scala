package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemTaxTemplateDetail(
    name: String,
    tax_type: String,
    tax_rate: Option[Double]
)

object ItemTaxTemplateDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemTaxTemplateDetail = new ItemTaxTemplateDetail(
    name = (v \ "name").get.as[String],
    tax_type = (v \ "tax_type").get.as[String],
    tax_rate = (v \ "tax_rate").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[ItemTaxTemplateDetail] = Reads[ItemTaxTemplateDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Tax Template Detail") => JsSuccess(ItemTaxTemplateDetail(js))
      case Some(_)                          => JsError("Wrong Doctype")
      case _                                => JsError("Doctype not Found")
    }
  }

}
