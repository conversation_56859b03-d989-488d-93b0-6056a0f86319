package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class GLEntry(
    name: String,
    posting_date: Option[String],
    transaction_date: Option[String],
    account: Option[String],
    party_type: Option[String],
    cost_center: Option[String],
    debit: Option[Double],
    credit: Option[Double],
    account_currency: Option[String],
    debit_in_account_currency: Option[Double],
    credit_in_account_currency: Option[Double],
    against: Option[String],
    against_voucher_type: Option[String],
    voucher_type: Option[String],
    voucher_detail_no: Option[String],
    project: Option[String],
    remarks: Option[String],
    is_opening: Option[String],
    is_advance: Option[String],
    fiscal_year: Option[String],
    company: Option[String],
    finance_book: Option[String],
    to_rename: Option[Int],
    due_date: Option[String]
)

object GLEntry {
  val NAME_FIELD = "name"

  def apply(v: JsValue): GLEntry = new GLEntry(
    name = (v \ "name").get.as[String],
    posting_date = (v \ "posting_date").toOption.map(_.as[String]),
    transaction_date = (v \ "transaction_date").toOption.map(_.as[String]),
    account = (v \ "account").toOption.map(_.as[String]),
    party_type = (v \ "party_type").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    debit = (v \ "debit").toOption.map(_.as[Double]),
    credit = (v \ "credit").toOption.map(_.as[Double]),
    account_currency = (v \ "account_currency").toOption.map(_.as[String]),
    debit_in_account_currency = (v \ "debit_in_account_currency").toOption.map(_.as[Double]),
    credit_in_account_currency = (v \ "credit_in_account_currency").toOption.map(_.as[Double]),
    against = (v \ "against").toOption.map(_.as[String]),
    against_voucher_type = (v \ "against_voucher_type").toOption.map(_.as[String]),
    voucher_type = (v \ "voucher_type").toOption.map(_.as[String]),
    voucher_detail_no = (v \ "voucher_detail_no").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    remarks = (v \ "remarks").toOption.map(_.as[String]),
    is_opening = (v \ "is_opening").toOption.map(_.as[String]),
    is_advance = (v \ "is_advance").toOption.map(_.as[String]),
    fiscal_year = (v \ "fiscal_year").toOption.map(_.as[String]),
    company = (v \ "company").toOption.map(_.as[String]),
    finance_book = (v \ "finance_book").toOption.map(_.as[String]),
    to_rename = (v \ "to_rename").toOption.map(_.as[Int]),
    due_date = (v \ "due_date").toOption.map(_.as[String])
  )

  implicit val reads: Reads[GLEntry] = Reads[GLEntry] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("GL Entry") => JsSuccess(GLEntry(js))
      case Some(_)          => JsError("Wrong Doctype")
      case _                => JsError("Doctype not Found")
    }
  }

}
