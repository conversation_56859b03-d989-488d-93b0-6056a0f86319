package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class StockLedgerEntry(
    name: String,
    item_code: Option[String],
    batch_no: Option[String],
    warehouse: Option[String],
    posting_date: Option[String],
    voucher_type: Option[String],
    voucher_detail_no: Option[String],
    actual_qty: Option[Double],
    incoming_rate: Option[Double],
    outgoing_rate: Option[Double],
    stock_uom: Option[String],
    qty_after_transaction: Option[Double],
    valuation_rate: Option[Double],
    stock_value: Option[Double],
    stock_value_difference: Option[Double],
    stock_queue: Option[String],
    project: Option[String],
    company: Option[String],
    fiscal_year: Option[String],
    is_cancelled: Option[String],
    to_rename: Option[Int]
)

object StockLedgerEntry {
  val NAME_FIELD = "name"

  def apply(v: JsValue): StockLedgerEntry = new StockLedgerEntry(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    posting_date = (v \ "posting_date").toOption.map(_.as[String]),
    voucher_type = (v \ "voucher_type").toOption.map(_.as[String]),
    voucher_detail_no = (v \ "voucher_detail_no").toOption.map(_.as[String]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    incoming_rate = (v \ "incoming_rate").toOption.map(_.as[Double]),
    outgoing_rate = (v \ "outgoing_rate").toOption.map(_.as[Double]),
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    qty_after_transaction = (v \ "qty_after_transaction").toOption.map(_.as[Double]),
    valuation_rate = (v \ "valuation_rate").toOption.map(_.as[Double]),
    stock_value = (v \ "stock_value").toOption.map(_.as[Double]),
    stock_value_difference = (v \ "stock_value_difference").toOption.map(_.as[Double]),
    stock_queue = (v \ "stock_queue").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    company = (v \ "company").toOption.map(_.as[String]),
    fiscal_year = (v \ "fiscal_year").toOption.map(_.as[String]),
    is_cancelled = (v \ "is_cancelled").toOption.map(_.as[String]),
    to_rename = (v \ "to_rename").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[StockLedgerEntry] = Reads[StockLedgerEntry] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Stock Ledger Entry") => JsSuccess(StockLedgerEntry(js))
      case Some(_)                    => JsError("Wrong Doctype")
      case _                          => JsError("Doctype not Found")
    }
  }

}
