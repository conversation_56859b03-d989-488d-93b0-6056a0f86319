package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemReorder(
    name: String,
    warehouse_group: Option[String],
    warehouse: String,
    warehouse_reorder_level: Double,
    warehouse_reorder_qty: Option[Double],
    material_request_type: String
)

object ItemReorder {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemReorder = new ItemReorder(
    name = (v \ "name").get.as[String],
    warehouse_group = (v \ "warehouse_group").toOption.map(_.as[String]),
    warehouse = (v \ "warehouse").get.as[String],
    warehouse_reorder_level = (v \ "warehouse_reorder_level").get.as[Double],
    warehouse_reorder_qty = (v \ "warehouse_reorder_qty").toOption.map(_.as[Double]),
    material_request_type = (v \ "material_request_type").get.as[String]
  )

  implicit val reads: Reads[ItemReorder] = Reads[ItemReorder] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Reorder") => JsSuccess(ItemReorder(js))
      case Some(_)              => JsError("Wrong Doctype")
      case _                    => JsError("Doctype not Found")
    }
  }

}
