package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemCustomerDetail(
    name: String,
    customer_name: Option[String],
    customer_group: Option[String],
    ref_code: String
)

object ItemCustomerDetail {
  val NAME_FIELD = "name"

  def apply(v: JsValue): ItemCustomerDetail = new ItemCustomerDetail(
    name = (v \ "name").get.as[String],
    customer_name = (v \ "customer_name").toOption.map(_.as[String]),
    customer_group = (v \ "customer_group").toOption.map(_.as[String]),
    ref_code = (v \ "ref_code").get.as[String]
  )

  implicit val reads: Reads[ItemCustomerDetail] = Reads[ItemCustomerDetail] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Customer Detail") => JsSuccess(ItemCustomerDetail(js))
      case Some(_)                      => JsError("Wrong Doctype")
      case _                            => JsError("Doctype not Found")
    }
  }

}
