package de.fellows.app.erpnextbridge.api.gen.erp.model.core

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class TestRunner(
    name: String,
    module_path: Option[String],
    app: Option[String]
)

object TestRunner {
  val NAME_FIELD = "name"

  def apply(v: JsValue): TestRunner = new TestRunner(
    name = (v \ "name").get.as[String],
    module_path = (v \ "module_path").toOption.map(_.as[String]),
    app = (v \ "app").toOption.map(_.as[String])
  )

  implicit val reads: Reads[TestRunner] = Reads[TestRunner] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Test Runner") => JsSuccess(TestRunner(js))
      case Some(_)             => JsError("Wrong Doctype")
      case _                   => JsError("Doctype not Found")
    }
  }

}
