package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BOMOperation(
    name: String,
    operation: String,
    workstation: Option[String],
    description: Option[String],
    hour_rate: Option[Double],
    time_in_mins: Double,
    batch_size: Option[Int],
    operating_cost: Option[Double],
    base_hour_rate: Option[Double],
    base_operating_cost: Option[Double],
    operation_name: String
)

object BOMOperation {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BOMOperation = new BOMOperation(
    name = (v \ "name").get.as[String],
    operation = (v \ "operation").get.as[String],
    workstation = (v \ "workstation").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String]),
    hour_rate = (v \ "hour_rate").toOption.map(_.as[Double]),
    time_in_mins = (v \ "time_in_mins").get.as[Double],
    batch_size = (v \ "batch_size").toOption.map(_.as[Int]),
    operating_cost = (v \ "operating_cost").toOption.map(_.as[Double]),
    base_hour_rate = (v \ "base_hour_rate").toOption.map(_.as[Double]),
    base_operating_cost = (v \ "base_operating_cost").toOption.map(_.as[Double]),
    operation_name = (v \ "operation_name").get.as[String]
  )

  implicit val reads: Reads[BOMOperation] = Reads[BOMOperation] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("BOM Operation") => JsSuccess(BOMOperation(js))
      case Some(_)               => JsError("Wrong Doctype")
      case _                     => JsError("Doctype not Found")
    }
  }

}
