package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PackingSlipItem(
    name: String,
    item_code: String,
    item_name: Option[String],
    batch_no: Option[String],
    description: Option[String],
    qty: Double,
    net_weight: Option[Double],
    stock_uom: Option[String],
    weight_uom: Option[String],
    page_break: Option[Int],
    dn_detail: Option[String]
)

object PackingSlipItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PackingSlipItem = new PackingSlipItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    batch_no = (v \ "batch_no").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String]),
    qty = (v \ "qty").get.as[Double],
    net_weight = (v \ "net_weight").toOption.map(_.as[Double]),
    stock_uom = (v \ "stock_uom").toOption.map(_.as[String]),
    weight_uom = (v \ "weight_uom").toOption.map(_.as[String]),
    page_break = (v \ "page_break").toOption.map(_.as[Int]),
    dn_detail = (v \ "dn_detail").toOption.map(_.as[String])
  )

  implicit val reads: Reads[PackingSlipItem] = Reads[PackingSlipItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Packing Slip Item") => JsSuccess(PackingSlipItem(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
