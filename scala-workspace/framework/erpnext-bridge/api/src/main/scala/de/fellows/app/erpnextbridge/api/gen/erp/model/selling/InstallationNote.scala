package de.fellows.app.erpnextbridge.api.gen.erp.model.selling

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class InstallationNote(
    name: String,
    naming_series: String,
    customer: String,
    customer_address: Option[String],
    contact_person: Option[String],
    customer_name: Option[String],
    contact_email: Option[String],
    territory: String,
    customer_group: Option[String],
    inst_date: String,
    status: String,
    company: String,
    amended_from: Option[String],
    items: Seq[InstallationNoteItem]
)

object InstallationNote {
  val NAME_FIELD = "name"

  def apply(v: JsValue): InstallationNote = new InstallationNote(
    name = (v \ "name").get.as[String],
    naming_series = (v \ "naming_series").get.as[String],
    customer = (v \ "customer").get.as[String],
    customer_address = (v \ "customer_address").toOption.map(_.as[String]),
    contact_person = (v \ "contact_person").toOption.map(_.as[String]),
    customer_name = (v \ "customer_name").toOption.map(_.as[String]),
    contact_email = (v \ "contact_email").toOption.map(_.as[String]),
    territory = (v \ "territory").get.as[String],
    customer_group = (v \ "customer_group").toOption.map(_.as[String]),
    inst_date = (v \ "inst_date").get.as[String],
    status = (v \ "status").get.as[String],
    company = (v \ "company").get.as[String],
    amended_from = (v \ "amended_from").toOption.map(_.as[String]),
    items = (v \ "items").toOption.map(x => x.as[JsArray].value.map(_.as[InstallationNoteItem])).get.toSeq
  )

  implicit val reads: Reads[InstallationNote] = Reads[InstallationNote] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Installation Note") => JsSuccess(InstallationNote(js))
      case Some(_)                   => JsError("Wrong Doctype")
      case _                         => JsError("Doctype not Found")
    }
  }

}
