package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class MaterialRequestItem(
    name: String,
    item_code: String,
    item_name: Option[String],
    description: String,
    item_group: Option[String],
    brand: Option[String],
    image: Option[String],
    manufacturer: Option[String],
    manufacturer_part_no: Option[String],
    qty: Double,
    uom: String,
    conversion_factor: Double,
    stock_uom: String,
    warehouse: Option[String],
    schedule_date: String,
    rate: Option[Double],
    amount: Option[Double],
    stock_qty: Option[Double],
    lead_time_date: Option[String],
    sales_order: Option[String],
    sales_order_item: Option[String],
    production_plan: Option[String],
    material_request_plan_item: Option[String],
    min_order_qty: Option[Double],
    projected_qty: Option[Double],
    actual_qty: Option[Double],
    ordered_qty: Option[Double],
    received_qty: Option[Double],
    expense_account: Option[String],
    project: Option[String],
    cost_center: Option[String],
    page_break: Option[Int]
)

object MaterialRequestItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): MaterialRequestItem = new MaterialRequestItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").get.as[String],
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    description = (v \ "description").get.as[String],
    item_group = (v \ "item_group").toOption.map(_.as[String]),
    brand = (v \ "brand").toOption.map(_.as[String]),
    image = (v \ "image").toOption.map(_.as[String]),
    manufacturer = (v \ "manufacturer").toOption.map(_.as[String]),
    manufacturer_part_no = (v \ "manufacturer_part_no").toOption.map(_.as[String]),
    qty = (v \ "qty").get.as[Double],
    uom = (v \ "uom").get.as[String],
    conversion_factor = (v \ "conversion_factor").get.as[Double],
    stock_uom = (v \ "stock_uom").get.as[String],
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    schedule_date = (v \ "schedule_date").get.as[String],
    rate = (v \ "rate").toOption.map(_.as[Double]),
    amount = (v \ "amount").toOption.map(_.as[Double]),
    stock_qty = (v \ "stock_qty").toOption.map(_.as[Double]),
    lead_time_date = (v \ "lead_time_date").toOption.map(_.as[String]),
    sales_order = (v \ "sales_order").toOption.map(_.as[String]),
    sales_order_item = (v \ "sales_order_item").toOption.map(_.as[String]),
    production_plan = (v \ "production_plan").toOption.map(_.as[String]),
    material_request_plan_item = (v \ "material_request_plan_item").toOption.map(_.as[String]),
    min_order_qty = (v \ "min_order_qty").toOption.map(_.as[Double]),
    projected_qty = (v \ "projected_qty").toOption.map(_.as[Double]),
    actual_qty = (v \ "actual_qty").toOption.map(_.as[Double]),
    ordered_qty = (v \ "ordered_qty").toOption.map(_.as[Double]),
    received_qty = (v \ "received_qty").toOption.map(_.as[Double]),
    expense_account = (v \ "expense_account").toOption.map(_.as[String]),
    project = (v \ "project").toOption.map(_.as[String]),
    cost_center = (v \ "cost_center").toOption.map(_.as[String]),
    page_break = (v \ "page_break").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[MaterialRequestItem] = Reads[MaterialRequestItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Material Request Item") => JsSuccess(MaterialRequestItem(js))
      case Some(_)                       => JsError("Wrong Doctype")
      case _                             => JsError("Doctype not Found")
    }
  }

}
