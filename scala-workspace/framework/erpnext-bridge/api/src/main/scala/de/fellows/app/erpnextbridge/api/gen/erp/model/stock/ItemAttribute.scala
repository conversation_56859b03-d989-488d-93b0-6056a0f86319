package de.fellows.app.erpnextbridge.api.gen.erp.model.stock

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON>r, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class ItemAttribute(
    attribute_name: String,
    numeric_values: Option[Int],
    from_range: Option[Double],
    increment: Option[Double],
    to_range: Option[Double],
    item_attribute_values: Option[Seq[ItemAttributeValue]]
)

object ItemAttribute {
  val NAME_FIELD = "attribute_name"

  def apply(v: JsValue): ItemAttribute = new ItemAttribute(
    attribute_name = (v \ "attribute_name").get.as[String],
    numeric_values = (v \ "numeric_values").toOption.map(_.as[Int]),
    from_range = (v \ "from_range").toOption.map(_.as[Double]),
    increment = (v \ "increment").toOption.map(_.as[Double]),
    to_range = (v \ "to_range").toOption.map(_.as[Double]),
    item_attribute_values =
      (v \ "item_attribute_values").toOption.map(x => x.as[JsArray].value.map(_.as[ItemAttributeValue]).toSeq)
  )

  implicit val reads: Reads[ItemAttribute] = Reads[ItemAttribute] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Item Attribute") => JsSuccess(ItemAttribute(js))
      case Some(_)                => JsError("Wrong Doctype")
      case _                      => JsError("Doctype not Found")
    }
  }

}
