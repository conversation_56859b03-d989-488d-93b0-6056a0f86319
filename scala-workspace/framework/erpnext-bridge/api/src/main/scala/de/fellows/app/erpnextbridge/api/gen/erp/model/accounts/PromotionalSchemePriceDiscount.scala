package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, Js<PERSON><PERSON><PERSON>, JsSuc<PERSON>, JsV<PERSON><PERSON>, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PromotionalSchemePriceDiscount(
    name: String,
    disable: Option[Int],
    min_qty: Option[Double],
    max_qty: Option[Double],
    min_amount: Option[Double],
    max_amount: Option[Double],
    rate_or_discount: Option[String],
    rate: Option[Double],
    discount_amount: Option[Double],
    discount_percentage: Option[Double],
    warehouse: Option[String],
    validate_applied_rule: Option[Int],
    priority: Option[String],
    apply_multiple_pricing_rules: Option[Int],
    apply_discount_on_rate: Option[Int]
)

object PromotionalSchemePriceDiscount {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PromotionalSchemePriceDiscount = new PromotionalSchemePriceDiscount(
    name = (v \ "name").get.as[String],
    disable = (v \ "disable").toOption.map(_.as[Int]),
    min_qty = (v \ "min_qty").toOption.map(_.as[Double]),
    max_qty = (v \ "max_qty").toOption.map(_.as[Double]),
    min_amount = (v \ "min_amount").toOption.map(_.as[Double]),
    max_amount = (v \ "max_amount").toOption.map(_.as[Double]),
    rate_or_discount = (v \ "rate_or_discount").toOption.map(_.as[String]),
    rate = (v \ "rate").toOption.map(_.as[Double]),
    discount_amount = (v \ "discount_amount").toOption.map(_.as[Double]),
    discount_percentage = (v \ "discount_percentage").toOption.map(_.as[Double]),
    warehouse = (v \ "warehouse").toOption.map(_.as[String]),
    validate_applied_rule = (v \ "validate_applied_rule").toOption.map(_.as[Int]),
    priority = (v \ "priority").toOption.map(_.as[String]),
    apply_multiple_pricing_rules = (v \ "apply_multiple_pricing_rules").toOption.map(_.as[Int]),
    apply_discount_on_rate = (v \ "apply_discount_on_rate").toOption.map(_.as[Int])
  )

  implicit val reads: Reads[PromotionalSchemePriceDiscount] = Reads[PromotionalSchemePriceDiscount] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Promotional Scheme Price Discount") => JsSuccess(PromotionalSchemePriceDiscount(js))
      case Some(_)                                   => JsError("Wrong Doctype")
      case _                                         => JsError("Doctype not Found")
    }
  }

}
