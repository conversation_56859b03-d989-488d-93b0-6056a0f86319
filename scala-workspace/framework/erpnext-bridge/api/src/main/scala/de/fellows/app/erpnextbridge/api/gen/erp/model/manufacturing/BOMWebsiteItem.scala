package de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuccess, JsValue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class BOMWebsiteItem(
    name: String,
    item_code: Option[String],
    item_name: Option[String],
    description: Option[String],
    qty: Option[Double]
)

object BOMWebsiteItem {
  val NAME_FIELD = "name"

  def apply(v: JsValue): BOMWebsiteItem = new BOMWebsiteItem(
    name = (v \ "name").get.as[String],
    item_code = (v \ "item_code").toOption.map(_.as[String]),
    item_name = (v \ "item_name").toOption.map(_.as[String]),
    description = (v \ "description").toOption.map(_.as[String]),
    qty = (v \ "qty").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[BOMWebsiteItem] = Reads[BOMWebsiteItem] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("BOM Website Item") => JsSuccess(BOMWebsiteItem(js))
      case Some(_)                  => JsError("Wrong Doctype")
      case _                        => JsError("Doctype not Found")
    }
  }

}
