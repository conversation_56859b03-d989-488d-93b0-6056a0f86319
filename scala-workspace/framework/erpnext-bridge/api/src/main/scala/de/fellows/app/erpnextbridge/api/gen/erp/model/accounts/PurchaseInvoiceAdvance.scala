package de.fellows.app.erpnextbridge.api.gen.erp.model.accounts

import play.api.libs.json.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, JsError, JsSuc<PERSON>, JsV<PERSON>ue, Reads }
import de.fellows.app.erpnextbridge.api.gen.erp.model.pcb._
import de.fellows.app.erpnextbridge.api.gen.erp.model.core._
import de.fellows.app.erpnextbridge.api.gen.erp.model.contacts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.accounts._
import de.fellows.app.erpnextbridge.api.gen.erp.model.selling._
import de.fellows.app.erpnextbridge.api.gen.erp.model.manufacturing._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stock._
import de.fellows.app.erpnextbridge.api.gen.erp.model.stackrate._

case class PurchaseInvoiceAdvance(
    name: String,
    reference_type: Option[String],
    remarks: Option[String],
    reference_row: Option[String],
    advance_amount: Option[Double],
    allocated_amount: Option[Double]
)

object PurchaseInvoiceAdvance {
  val NAME_FIELD = "name"

  def apply(v: JsValue): PurchaseInvoiceAdvance = new PurchaseInvoiceAdvance(
    name = (v \ "name").get.as[String],
    reference_type = (v \ "reference_type").toOption.map(_.as[String]),
    remarks = (v \ "remarks").toOption.map(_.as[String]),
    reference_row = (v \ "reference_row").toOption.map(_.as[String]),
    advance_amount = (v \ "advance_amount").toOption.map(_.as[Double]),
    allocated_amount = (v \ "allocated_amount").toOption.map(_.as[Double])
  )

  implicit val reads: Reads[PurchaseInvoiceAdvance] = Reads[PurchaseInvoiceAdvance] { js =>
    (js \ "doctype").toOption.map(_.as[String]) match {
      case Some("Purchase Invoice Advance") => JsSuccess(PurchaseInvoiceAdvance(js))
      case Some(_)                          => JsError("Wrong Doctype")
      case _                                => JsError("Doctype not Found")
    }
  }

}
