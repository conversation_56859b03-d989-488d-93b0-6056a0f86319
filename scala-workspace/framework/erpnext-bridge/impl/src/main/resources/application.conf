include "main-application.conf"

play.application.loader = de.fellows.app.erpnextbridge.impl.ErpNextBridgeLoader


erpnextbridge.cassandra.keyspace = ${fellows.persistence.rootKeyspace}erpnextbridge


cassandra-journal {
  keyspace = ${erpnextbridge.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${erpnextbridge.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${erpnextbridge.cassandra.keyspace}read
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "erpnext-bridge"
# fellows.serviceconfig = ${fellows.services.erpnext-bridge}


fellows.storage {
  service = ${fellows.storage.base}/erpnextbridge
}
