package de.fellows.app.notification.impl

import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.playjson.JsonSerializerRegistry
import com.lightbend.lagom.scaladsl.testkit.PersistentEntityTestDriver
import de.fellows.app.notification.common.{Notification, User}
import de.fellows.app.notification.impl.entities._
import de.fellows.utils.TestUtils
import de.fellows.utils.communication.{ServiceDefinition, ServiceException}
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.time.Instant
import java.util.UUID

class NotificationEntitySpec extends AnyWordSpec with BeforeAndAfterAll with Matchers {
  private val system = ActorSystem(
    "NotificationEntitySpec",
    JsonSerializerRegistry.actorSystemSetupFor(NotificationServiceSerializerRegistry)
  )

  implicit val service: ServiceDefinition = ServiceDefinition("notification")

  private val notificationId = UUID.randomUUID()
  private val userId         = UUID.randomUUID()

  val notification = Notification(
    id = Some(notificationId),
    priority = 0,
    service = "test",
    icon = None,
    message = "wow",
    links = Seq(),
    title = "title",
    read = false,
    resource = Some(""),
    owner = User(userId),
    date = Instant.now()
  )

  private def withDriver(block: PersistentEntityTestDriver[
    NotificationCommand,
    NotificationEvent,
    Option[Notification]
  ] => Unit): Unit = {
    val driver = new PersistentEntityTestDriver(system, new NotificationEntity, notificationId.toString)
    block(driver)
    TestUtils.checkIssues(driver)
  }

  "NotificationEntitySpec" should {
    "allow creation" in withDriver { driver =>
      val outcome = driver.run(CreateNotification(notification))

      outcome.state shouldBe Some(notification)
    }

    "allow reading" in withDriver { driver =>
      driver.run(CreateNotification(notification))
      val outcome = driver.run(ReadNotification(notificationId))

      outcome.state shouldBe Some(notification.copy(read = true))
    }

    "allow deleting" in withDriver { driver =>
      driver.run(CreateNotification(notification))
      val outcome = driver.run(DeleteNotification(notificationId))

      outcome.state shouldBe None
    }

    "allow recreation" in withDriver { driver =>
      driver.run(CreateNotification(notification))
      driver.run(DeleteNotification(notificationId))
      val outcome = driver.run(CreateNotification(notification))
      outcome.state shouldBe Some(notification)
    }

    "forbid multiple creation " in withDriver { driver =>
      val now = Instant.now()

      driver.run(CreateNotification(notification))
      val outcome = driver.run(CreateNotification(notification))

      outcome.state shouldBe Some(notification)

      outcome.replies.size shouldBe 1
      outcome.replies.head shouldBe new ServiceException(NotificationExist)
    }

  }
}
