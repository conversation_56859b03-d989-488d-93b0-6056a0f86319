package de.fellows.app.notification.impl

import akka.actor.ActorSystem
import akka.persistence.query.Offset
import akka.stream.scaladsl.Source
import akka.{Done, NotUsed}
import com.datastax.driver.core.utils.UUIDs
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntity, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.notification.api.NotificationService
import de.fellows.app.notification.common.{Notification, NotificationMessage, Ping}
import de.fellows.app.notification.impl.entities._
import de.fellows.app.security.AccessControlServiceComposition._
import de.fellows.utils.communication.{ServiceDefinition, ServiceException}
import de.fellows.utils.security.{AuthenticationServiceComposition, TokenContent}

import java.time.Instant
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.reflect.ClassTag

class NotificationServiceImpl(registry: PersistentEntityRegistry, repo: NotificationRepository, system: ActorSystem)(
    implicit
    service: ServiceDefinition,
    ec: ExecutionContext
) extends NotificationService {
  implicit val actorSystem: ActorSystem = system

  def forUser(event: NotificationCreated, token: TokenContent): Boolean =
    event.notification.owner.id == token.userId

  override def streamNotifications(k: String): ServiceCall[NotUsed, Source[NotificationMessage, NotUsed]] = {

    val b = AuthenticationServiceComposition.decodeTokenWithResponse(k)
    authorizedStringWithToken(b)(token => s"notification:${token.team}:${token.userId}:*:*:read") {
      (token, _) =>
        ServerServiceCall { _ =>
          // retrieve existing notifications from read side
          val existingNotifications: Source[NotificationMessage, NotUsed] =
            Source.fromFuture(repo.getNotificationsByUser(token.userId))
              .mapConcat[Notification](x => x.toList).map(n => NotificationMessage(t = "notification", m = n))

          import scala.concurrent.duration._
          val ticks = Source.tick(30 seconds, 30 seconds, NotificationMessage(t = "ping", m = Ping(Instant.now())))

          // get a stream of NotificationCreated Events starting now (for all shards)
          val nowOffset = Offset.timeBasedUUID(UUIDs.timeBased())
          val sources = NotificationEvent.Tag.allTags.map(tag =>
            registry.eventStream(tag, nowOffset)
              .collect {
                case EventStreamElement(_, event: NotificationCreated, _) if forUser(event, token) =>
                  NotificationMessage(t = "notification", m = event.notification)
              }
          )

          // emit historic events, then combine all streams for future events

          val src = existingNotifications ++ sources.reduce((a, b) => a.merge(b))
          Future.successful(src.merge(ticks))

        }
    }
  }

  override def updateNotification(id: UUID): ServiceCall[Notification, Done] =
    authorizedString(token =>
      s"notification:${token.team}:${
          token.userId
        }:${
          id
        }:*:write"
    ) { (_, _) =>
      ServerServiceCall { n =>
        if (n.read) {
          refFor[NotificationEntity](id).ask(ReadNotification(id)).map {
            _: Notification => Done
          }
        } else {
          throw new ServiceException(NotificationNotRead)
        }
      }
    }

  override def getNotification(id: UUID): ServiceCall[NotUsed, Notification] =
    authorizedString(token =>
      s"notification:${token.team}:${
          token.userId
        }:${
          id
        }:*:read"
    ) { (token, _) =>
      ServerServiceCall { _ =>
        repo.getNotification(token.userId, id).map {
          case Some(n) => n
          case None    => throw new ServiceException(NotificationNotFound)
        }
      }
    }

  override def deleteNotification(id: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"notification:${token.team}:${
          token.userId
        }:${
          id
        }:*:write"
    ) { (_, _) =>
      ServerServiceCall { _ =>
        refFor[NotificationEntity](id).ask(DeleteNotification(id)).map {
          _: Notification => Done
        }

      }
    }

  private def refFor[T <: PersistentEntity: ClassTag](id: UUID) = registry.refFor[T](id.toString)

}
