package de.fellows.app.notification.impl

import java.util.UUID
import akka.Done
import akka.stream.Materializer
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.notification.common.Notification
import de.fellows.app.notification.impl.entities.{CreateNotification, NotificationEntity}
import de.fellows.app.user.api.UserService
import de.fellows.utils.TopicUtils

import scala.concurrent.ExecutionContext

class NotificationEventsConsumer(userService: UserService, registry: PersistentEntityRegistry)(implicit
    ec: ExecutionContext,
    mat: Materializer
) {
  import TopicUtils.defaultNaming
  TopicUtils.subscribe(userService.userNotificationTopic(), 10) {
    _.payload match {
      case msg =>
        val id = UUID.randomUUID()
        val u  = registry.refFor[NotificationEntity](id.toString)
        u.ask(CreateNotification(msg.copy(id = Some(id)))).map { _: Notification => Done }
    }
  }

}
