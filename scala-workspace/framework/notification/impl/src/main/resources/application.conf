include "main-application.conf"

play.application.loader = de.fellows.app.notification.impl.NotificationServiceLoader


profile.cassandra.keyspace = ${fellows.persistence.rootKeyspace}notification


cassandra-journal {
  keyspace = ${profile.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${profile.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${profile.cassandra.keyspace}
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "notification"
# fellows.serviceconfig = ${fellows.services.notification}
akka {
  # Log level used by the configured loggers (see "loggers") as soon
  # as they have been started; before that, see "stdout-loglevel"
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  loglevel = "DEBUG"

  # Log level for the very basic logger activated during ActorSystem startup.
  # This logger prints the log messages to stdout (System.out).
  # Options: OFF, ERROR, WARNING, INFO, DEBUG
  stdout-loglevel = "DEBUG"
}

fellows.storage {
  service = ${fellows.storage.base}/notification
}
