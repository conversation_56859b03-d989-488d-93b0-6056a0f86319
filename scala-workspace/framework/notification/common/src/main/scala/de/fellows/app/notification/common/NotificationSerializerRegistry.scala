package de.fellows.app.notification.common

import com.lightbend.lagom.scaladsl.playjson.{ JsonSerializer, JsonSerializerRegistry }

object NotificationSerializerRegistry extends JsonSerializerRegistry {
  override def serializers =
    List(
      JsonSerializer[User],
      JsonSerializer[Link],
      JsonSerializer[Notification],
      JsonSerializer[Message],
      JsonSerializer[NotificationMessage],
      JsonSerializer[Ping]
    )

}
