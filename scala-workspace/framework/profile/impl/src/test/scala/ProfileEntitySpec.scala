import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.playjson.JsonSerializerRegistry
import com.lightbend.lagom.scaladsl.testkit.PersistentEntityTestDriver
import de.fellows.app.profile.api.{Address, ProfileData}
import de.fellows.app.profile.impl.entities._
import de.fellows.utils.TestUtils
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import org.scalatest.{BeforeAndAfterAll, Inside}
import de.fellows.utils.meta.{MetaInfo, ObjectProperty}
import play.api.libs.json.{JsObject, Json}

import java.util.UUID

class ProfileEntitySpec extends AnyWordSpec with Matchers with BeforeAndAfterAll with Inside {
  private val system =
    ActorSystem("ProfileEntitySpec", JsonSerializerRegistry.actorSystemSetupFor(ProfileSerializerRegistry))

  private val profileId = UUID.randomUUID()
  private val addId     = UUID.randomUUID()

  private def withProfile(block: PersistentEntityTestDriver[
    ProfileCommand,
    ProfileEvent,
    Option[InternalProfile]
  ] => Unit): Unit = {
    val driver = new PersistentEntityTestDriver(system, new ProfileEntity, profileId.toString)
    block(driver)
    TestUtils.checkIssues(driver)
  }

  "The Profile" should {
    "support creation" in withProfile { driver =>
      val outcome = driver.run(CreateProfile(profileId))
      val expected = InternalProfile(
        user = profileId,
        data = ProfileData(None, None, None, Some(ProfileEntity.defaultProfileInfo), Some("en")),
        addresses = Seq(),
        avatar = None
      )

      outcome.events should contain only ProfileCreated(expected)
      outcome.state should ===(Some(expected))
    }

    "support adding Addresses" in withProfile { driver =>
      driver.run(CreateProfile(profileId))

      val add = Address(
        id = Some(addId),
        name = Some(""),
        alias = "",
        phone = Some(""),
        company = Some(""),
        street = "",
        no = "",
        apartment = Some(""),
        district = Some(""),
        city = "",
        postal = "",
        country = Some(""),
        county = Some(""),
        billing = true,
        shipping = false
      )

      val outcome = driver.run(AddAddress(add))

      outcome.events should contain(AddressAdded(profileId, add))
      outcome.state should ===(Some(InternalProfile(
        user = profileId,
        data = ProfileData(None, None, None, Some(ProfileEntity.defaultProfileInfo), Some("en")),
        addresses = Seq(add),
        avatar = None
      )))
    }
  }

}
