//package de.fellows.app.profile.impl
//
//import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, ReadSideProcessor}
//import de.fellows.app.profile.impl.entities.{AddressAdded, AddressRemoved, AddressUpdated, ProfileEvent}
//
//import scala.collection.immutable
//import scala.concurrent.Future
//
//class ProfileServiceEventLog(readSide: ElasticEventLog, client: ElasticClient) extends ReadSideProcessor[ProfileEvent] {
//
//
//  def addAddress(e: EventStreamElement[AddressAdded], index: String, baseFields: Seq[(String, Any)]): immutable.Seq[Future[Response[IndexResponse]]] = {
//    immutable.Seq(client.execute(
//      indexInto(s"$index" / "ProfileEvent") fields (
//        baseFields ++
//          Seq(
//            CommonFields.userId -> e.event.user,
//            CommonFields.address -> e.event.add.id,
//          )
//        )
//    ))
//  }
//
//  def removeAddress(e: EventStreamElement[AddressRemoved], index: String, baseFields: Seq[(String, Any)]): immutable.Seq[Future[Response[IndexResponse]]] = {
//    immutable.Seq(client.execute(
//      indexInto(s"$index" / "ProfileEvent") fields (
//        baseFields ++
//          Seq(
//            CommonFields.userId -> e.event.user,
//            CommonFields.address -> e.event.rem,
//          )
//        )
//    ))
//  }
//
//  def updateAddress(e: EventStreamElement[AddressUpdated], index: String, baseFields: Seq[(String, Any)]): immutable.Seq[Future[Response[IndexResponse]]] = {
//    immutable.Seq(client.execute(
//      indexInto(s"$index" / "ProfileEvent") fields (
//        baseFields ++
//          Seq(
//            CommonFields.userId -> e.event.user,
//            CommonFields.address -> e.event.id,
//          )
//        )
//    ))
//  }
//
//  override def buildHandler(): ReadSideProcessor.ReadSideHandler[ProfileEvent] = {
//    readSide.builder[ProfileEvent, IndexResponse]("profileEventLog")
//      .setEventHandler[AddressAdded]((e, index, baseFields) => addAddress(e, index, baseFields))
//      .setEventHandler[AddressRemoved]((e, index, baseFields) => removeAddress(e, index, baseFields))
//      .setEventHandler[AddressUpdated]((e, index, baseFields) => updateAddress(e, index, baseFields))
//
//      .build()
//  }
//
//  def aggregateTags = ProfileEvent.Tag.allTags
//}
