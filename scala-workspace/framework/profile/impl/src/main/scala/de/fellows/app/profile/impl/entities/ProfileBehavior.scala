package de.fellows.app.profile.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{ AggregateEvent, AggregateEventTag }
import de.fellows.app.profile.api.{ Address, ProfileData }
import de.fellows.utils.FilePath
import play.api.libs.json.{ Format, Json }

import java.util.UUID

sealed trait ProfileCommand

sealed trait ProfileEvent extends AggregateEvent[ProfileEvent] {
  override def aggregateTag = ProfileEvent.Tag
}

object ProfileEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[ProfileEvent](NumShards)
}

case class CreateProfile(user: UUID) extends ProfileCommand with ReplyType[InternalProfile]

case class GetProfile(user: UUID) extends ProfileCommand with ReplyType[ProfileResponse]

case class DeleteProfile(user: UUID) extends ProfileCommand with ReplyType[Done]

//case class SetField(key: String, value: Option[String]) extends ProfileCommand with ReplyType[Done] //TODO: SetFields with ProfileData

case class SetFields(user: UUID, data: ProfileData) extends ProfileCommand
    with ReplyType[InternalProfile] //TODO: SetFields with ProfileData

case class AddAddress(add: Address) extends ProfileCommand with ReplyType[Done]

case class RemoveAddress(address: UUID) extends ProfileCommand with ReplyType[Done]

case class SetAddressField(address: UUID, field: AddressField, value: Option[String]) extends ProfileCommand
    with ReplyType[Done]

case class UpdateAddressFields(address: UUID, newFields: Address) extends ProfileCommand with ReplyType[Done]

case class SetAvatar(avatar: Option[FilePath]) extends ProfileCommand with ReplyType[Done]

case class ProfileCreated(profile: InternalProfile) extends ProfileEvent

case class ProfileDeleted(user: UUID, addresses: Seq[Address]) extends ProfileEvent

@deprecated("use ProfileDataChanged")
case class FieldSet(user: UUID, key: String, value: Option[String], createdNew: Boolean) extends ProfileEvent

case class ProfileDataChanged(user: UUID, data: ProfileData) extends ProfileEvent

case class AddressFieldSet(user: UUID, id: UUID, key: AddressField, value: Option[String], createdNew: Boolean)
    extends ProfileEvent

case class AddressUpdated(user: UUID, id: UUID, newFields: Address) extends ProfileEvent

case class AddressAdded(user: UUID, add: Address) extends ProfileEvent

case class AddressRemoved(user: UUID, rem: UUID) extends ProfileEvent

case class AvatarSet(user: UUID, avatar: Option[FilePath]) extends ProfileEvent

case class ProfileResponse(response: Option[InternalProfile])

object ProfileResponse {
  implicit val format: Format[ProfileResponse] = Json.format
}

object CreateProfile {
  implicit val format: Format[CreateProfile] = Json.format
}

object GetProfile {
  implicit val format: Format[GetProfile] = Json.format
}

object DeleteProfile {
  implicit val format: Format[DeleteProfile] = Json.format
}

object SetFields {
  implicit val format: Format[SetFields] = Json.format
}

object AddAddress {
  implicit val format: Format[AddAddress] = Json.format
}

object RemoveAddress {
  implicit val format: Format[RemoveAddress] = Json.format
}

object SetAddressField {
  implicit val format: Format[SetAddressField] = Json.format
}

object UpdateAddressFields {
  implicit val format: Format[UpdateAddressFields] = Json.format
}

object ProfileCreated {
  implicit val format: Format[ProfileCreated] = Json.format
}

object ProfileDeleted {
  implicit val format: Format[ProfileDeleted] = Json.format
}

object FieldSet {
  implicit val format: Format[FieldSet] = Json.format
}
object ProfileDataChanged {
  implicit val format: Format[ProfileDataChanged] = Json.format
}

object AddressFieldSet {
  implicit val format: Format[AddressFieldSet] = Json.format
}

object AddressUpdated {
  implicit val format: Format[AddressUpdated] = Json.format
}

object AddressAdded {
  implicit val format: Format[AddressAdded] = Json.format
}

object AvatarSet {
  implicit val format: Format[AvatarSet] = Json.format
}

object SetAvatar {
  implicit val format: Format[SetAvatar] = Json.format
}

object AddressRemoved {
  implicit val format: Format[AddressRemoved] = Json.format
}
