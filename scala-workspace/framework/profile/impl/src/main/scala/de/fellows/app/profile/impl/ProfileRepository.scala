// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.profile.impl

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.profile.api.ProfileData
import de.fellows.app.profile.impl.entities._
import de.fellows.utils.FilePath
import de.fellows.utils.codec.CodecHelper

import java.util
import java.util.UUID
import scala.jdk.CollectionConverters._
import scala.collection.{immutable, mutable}
import scala.concurrent.{ExecutionContext, Future}

class ProfileRepository(session: CassandraSession)(implicit ec: ExecutionContext, mat: Materializer) {

  //  def convertProfile(v1: Row): ProfileData = {
  //    val m = v1.getMap("fields", classOf[String], classOf[String]).asScala.toMap
  //    ProfileData(
  //      m.get("firstName"),
  //      m.get("lastName"),
  //      m.get("country"),
  //    )
  //  }

  //  def convertAddress(r: Row): Address = {
  //    val fields = r.getMap("fields", classOf[String], classOf[String]).asScala
  //    Address(
  //      Some(r.getUUID("address")),
  //      fields.get(Name.field),
  //      fields.getOrElse(Alias.field, ""),
  //      fields.get(Phone.field),
  //      fields.get(Company.field),
  //      fields.getOrElse(Street.field, ""),
  //      fields.getOrElse(No.field, ""),
  //      fields.get(Apartment.field),
  //      fields.get(District.field),
  //      fields.getOrElse(City.field, ""),
  //      fields.getOrElse(Postal.field, ""),
  //      fields.get(Country.field),
  //      fields.get(County.field),
  //
  //      r.getBool("billing"),
  //      r.getBool("shipping"),
  //    )
  //
  //  }

  //  def getAddresses(user: UUID) = {
  //    session.selectAll(
  //      """
  //       SELECT *
  //       FROM address
  //       WHERE user = ?
  //        """, user).map(convertAddresses)
  //  }

  //  def getAvatar(user: UUID) = {
  //    session.selectOne(
  //      //language=SQL
  //      """
  //       SELECT *
  //       FROM avatars
  //       WHERE user = ?
  //        """, user).map(_.map(_.get("avatar", classOf[FilePath])))
  //  }

  //  def convertAddresses(v2: Seq[Row]): Seq[Address] = {
  //    v2.map(
  //      r => convertAddress(r)
  //    )
  //
  //  }
  //
  //  def getProfileParts(user: UUID): Future[Source[ProfilePart, NotUsed]] = {
  //    ???
  //  }

  //  def getProfile(user: UUID): Future[Option[InternalProfile]] = {
  //    val prof = session.selectOne(
  //      //language=SQL
  //      """
  //        |SELECT * FROM profile WHERE user = ?
  //      """.stripMargin, user)
  //
  //    val adds = session.selectAll(
  //      //language=SQL
  //      """
  //        |SELECT * FROM address WHERE user = ?
  //      """.stripMargin, user)
  //
  //    val avatar = session.selectOne(
  //      //language=SQL
  //      """
  //        |SELECT * FROM avatars WHERE user = ?
  //      """.stripMargin, user)
  //
  //    for {
  //      v1 <- prof
  //      v2 <- adds
  //      av <- avatar
  //    } yield
  //      v1 match {
  //        case Some(row) =>
  //          Some(
  //            InternalProfile(
  //              user,
  //              convertProfile(row),
  //              convertAddresses(v2),
  //              av.map(
  //                _.get("avatar", classOf[FilePath])
  //              ),
  //              Some(MetaInfo())
  //            )
  //          )
  //        case None => None
  //      }
  //  }

  // language=SQL
  def profileExists(user: UUID) =
    session.selectOne(
      """
        |SELECT COUNT(*) FROM profile WHERE user = ?
      """.stripMargin,
      user
    ).map(_.get.getLong(0) > 0)
}

private[impl] class ProfileEventProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[ProfileEvent] {

  private var insertProfileStatement: PreparedStatement      = null
  private var deleteProfileStatement: PreparedStatement      = null
  private var updateProfileFieldStatement: PreparedStatement = null
  private var removeProfileFieldStatement: PreparedStatement = null
  private var addProfileFieldStatement: PreparedStatement    = null

  private var addAddressStatement: PreparedStatement    = null
  private var removeAddressStatement: PreparedStatement = null

  private var updateAddressFieldStatement: PreparedStatement = null
  private var updateAddressTypeStatement: PreparedStatement  = null
  private var removeAddressFieldStatement: PreparedStatement = null
  private var addAddressFieldStatement: PreparedStatement    = null
  private var updateAvatarStatement: PreparedStatement       = null

  def toMap(data: ProfileData): util.Map[String, String] = {
    val m: mutable.Map[String, String] = mutable.Map()

    if (data.firstName.isDefined) {
      m.put("firstName", data.firstName.get)
    }
    if (data.lastName.isDefined) {
      m.put("lastName", data.lastName.get)
    }
    if (data.country.isDefined) {
      m.put("country", data.country.get)
    }
    if (data.language.isDefined) {
      m.put("language", data.language.get)
    }

    m.toMap.asJava
  }

  def insertProfile(event: ProfileCreated): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(insertProfileStatement.bind(
      event.profile.user,
      toMap(event.profile.data)
    )))

  def deleteProfile(event: ProfileDeleted): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      deleteProfileStatement.bind(
        event.user
      )
    ) ++ event.addresses.map(add => removeAddressStatement.bind(add.id.get, event.user)))

  def addAddress(event: AddressAdded): Future[immutable.Seq[BoundStatement]] =
    if (event.add.id.isEmpty) {
      Future.successful(List())
    } else {
      Future.successful(List(
        addAddressStatement.bind(
          event.user,
          event.add.id.get,
          event.add.getFieldMap().asJava,
          boolean2Boolean(event.add.billing),
          boolean2Boolean(event.add.shipping)
        )
      ))
    }

  def removeAddress(event: AddressRemoved): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      removeAddressStatement.bind(
        event.rem,
        event.user
      )
    ))

  def updateAddress(event: AddressUpdated): Future[immutable.Seq[BoundStatement]] = {
    val fields = event.newFields.getFieldMap()

    Future.successful(List(
      updateAddressFieldStatement.bind(
        fields.asJava,
        event.id,
        event.user
      ),
      updateAddressTypeStatement.bind(
        boolean2Boolean(event.newFields.billing),
        boolean2Boolean(event.newFields.shipping),
        event.id,
        event.user
      )
    ))

  }

  def setAddressField(event: AddressFieldSet): Future[immutable.Seq[BoundStatement]] = {
    var statements: List[BoundStatement] = List()

    if (event.createdNew) {
      if (event.value.isDefined) {
        // add new
        statements = statements :+ addAddressFieldStatement.bind(
          event.key.field,
          event.value.get,
          event.id,
          event.user
        )
      } else {
        // remove new, makes no sense
      }
    } else {
      if (event.value.isDefined) {
        // change existing
        statements = statements :+ updateAddressFieldStatement.bind(
          mutable.Map(event.key.field -> event.value.get).asJava,
          event.id,
          event.user
        )
      } else {
        // remove existing
        statements = statements :+ removeAddressFieldStatement.bind(
          event.key.field,
          event.id,
          event.user
        )
      }
    }

    Future.successful(statements)
  }

  def setProfileField(event: FieldSet): Future[immutable.Seq[BoundStatement]] = {
    var statements: List[BoundStatement] = List()

    if (event.createdNew) {
      if (event.value.isDefined) {
        // add new
        statements = statements :+ addProfileFieldStatement.bind(
          mutable.Map(event.key -> event.value.get).asJava,
          event.user
        )
      } else {
        // remove new, makes no sense
      }
    } else {
      if (event.value.isDefined) {
        // change existing
        statements = statements :+ updateProfileFieldStatement.bind(
          event.key,
          event.value.get,
          event.user
        )
      } else {
        // remove existing
        statements = statements :+ removeProfileFieldStatement.bind(
          event.key,
          event.user
        )
      }
    }

    Future.successful(statements)
  }

  def setAvatar(event: AvatarSet): Future[immutable.Seq[BoundStatement]] =
    Future.successful(List(
      updateAvatarStatement.bind()
        .set("av", event.avatar.orNull, classOf[FilePath])
        .set("userid", event.user, classOf[UUID])
    ))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[ProfileEvent] =
    readSide.builder[ProfileEvent]("profileEventOffset")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[ProfileCreated](e => insertProfile(e.event))
      .setEventHandler[ProfileDeleted](e => deleteProfile(e.event))
      .setEventHandler[AddressAdded](e => addAddress(e.event))
      .setEventHandler[AddressRemoved](e => removeAddress(e.event))
      .setEventHandler[AddressFieldSet](e => setAddressField(e.event))
      .setEventHandler[AddressUpdated](e => updateAddress(e.event))
      .setEventHandler[FieldSet](e => setProfileField(e.event))
      .setEventHandler[AvatarSet](e => setAvatar(e.event))
      .build

  override def aggregateTags: Set[AggregateEventTag[ProfileEvent]] = ProfileEvent.Tag.allTags

  // language=SQL

  private def createTables() =
    for {
      _ <- CodecHelper.loadTypes(session)

      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS profile (
            user UUID,
            fields map<text,text>,

            PRIMARY KEY (user)
          )
        """
      )

      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS address (
            user UUID,
            address UUID,

            fields map<text,text>,

            billing boolean,
            shipping boolean,

            PRIMARY KEY (user,address)
          )
        """
      )
      _ <- session.executeCreateTable(
        """
          CREATE TABLE IF NOT EXISTS avatars (
            user UUID,
            avatar filepath,
            PRIMARY KEY (user)
          )
        """
      )
      s <- session.underlying()
    } yield {
      CodecHelper.registerCodecs(s)
      Done
    }

  // language=SQL

  private def prepareStatements() = {

    for {

      updateAvatar <- session.prepare(
        """
          |UPDATE avatars SET avatar = :av WHERE user = :userid
          |""".stripMargin
      )

      insertProfile <- session.prepare(
        """
        INSERT INTO profile(user, fields) VALUES (?, ?)
      """
      )
      deleteProfile <- session.prepare(
        """
        DELETE FROM profile WHERE user = ?
      """
      )

      updateProfileField <- session.prepare(
        """
          | UPDATE profile
          | SET fields[?] = ?
          | WHERE user = ?
        """.stripMargin
      )
      removeProfileField <- session.prepare(
        """
          | DELETE fields[?]
          | FROM profile
          | WHERE user = ?
        """.stripMargin
      )
      addProfileField <- session.prepare(
        """
          | UPDATE profile
          | SET fields = fields + ?
          | WHERE user = ?
        """.stripMargin
      )

      addAddress <- session.prepare(
        """
          | INSERT INTO address(user, address, fields,  billing, shipping) VALUES (?, ?, ?, ?, ?)
        """.stripMargin
      )

      deleteAddress <- session.prepare(
        """
          | DELETE
          | FROM address
          | WHERE address = ?
          | AND user = ?
          |
        """.stripMargin
      )

      updateAddressField <- session.prepare(
        """
          | UPDATE address
          | SET fields = fields + ?
          | WHERE address = ?
          | AND user = ?
        """.stripMargin
      )
      updateAddressType <- session.prepare(
        """
          | UPDATE address
          | SET billing = ?, shipping = ?
          | WHERE address = ?
          | AND user = ?
        """.stripMargin
      )

      removeAddressField <- session.prepare(
        """
          | DELETE fields[?]
          | FROM address
          | WHERE address = ?
          | AND user = ?
        """.stripMargin
      )

      addAddressField <- session.prepare(
        """
          | UPDATE address
          | SET fields = fields + ?
          | WHERE address = ?
          | AND user = ?
          |
        """.stripMargin
      )

      s <- session.underlying()
    } yield {
      CodecHelper.registerCodecs(s)

      insertProfileStatement = insertProfile
      deleteProfileStatement = deleteProfile
      updateProfileFieldStatement = updateProfileField
      removeProfileFieldStatement = removeProfileField
      addProfileFieldStatement = addProfileField

      addAddressStatement = addAddress
      removeAddressStatement = deleteAddress

      updateAddressFieldStatement = updateAddressField
      updateAddressTypeStatement = updateAddressType
      removeAddressFieldStatement = removeAddressField
      addAddressFieldStatement = addAddressField

      updateAvatarStatement = updateAvatar

      //      addProfileFieldStatement.getCodecRegistry.register(InstantCodec)

      Done
    }
  }
}
