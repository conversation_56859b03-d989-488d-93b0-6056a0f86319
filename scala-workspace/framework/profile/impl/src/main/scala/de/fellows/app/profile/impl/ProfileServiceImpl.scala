package de.fellows.app.profile.impl

import akka.actor.ActorSystem
import akka.stream.Materializer
import akka.stream.scaladsl.Source
import akka.{ Done, NotUsed }
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.transport.NotFound
import com.lightbend.lagom.scaladsl.persistence.{ PersistentEntity, PersistentEntityRegistry }
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.profile.api._
import de.fellows.app.profile.impl.entities._
import de.fellows.app.security.AccessControlServiceComposition._
import de.fellows.app.security.SecurityApi.PermissionResponse
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.communication.ServiceDefinition

import java.util.UUID
import scala.concurrent.{ Await, ExecutionContext, Future }
import scala.reflect.ClassTag

class ProfileServiceImpl(registry: PersistentEntityRegistry, system: ActorSystem, profileRepository: ProfileRepository)(
    implicit
    ec: ExecutionContext,
    mat: Materializer,
    sd: ServiceDefinition,
) extends ProfileService with StackrateAPIImpl {
  implicit val actorSystem: ActorSystem = system
  //  override def getProfileParts(name: String): ServerServiceCall[NotUsed, Source[ProfilePart, NotUsed]] =
  //    authorizedString(token => s"profile:${token.username}:${token.userId}:parts:read") { (token, _) =>
  //      ServerServiceCall { _ =>
  //        profileRepository.getProfileParts(token.userId) //TODO user
  //
  //      }
  //    }


  override def waitForProfile(uid: String, name: String): ServerServiceCall[NotUsed, Source[ProfileMessage, NotUsed]] =
    ServerServiceCall { head =>
      val id = UUID.fromString(uid)

      Future.successful(Source.future(
        Future {
          import scala.concurrent.duration._

          var profileAvailable: Option[Boolean]             = None
          var profileAccessible: Option[PermissionResponse] = None
          var i                                             = 0
          while (
            (profileAccessible.isEmpty || profileAvailable.isEmpty || !profileAvailable.get || !profileAccessible.get.allowed) && i < 10
          ) {
            ProfileMessage("wait", None)

            //            profileAccessible = Some(Await.result(s.isUserPermitted(id).invoke(PermissionRequest(Seq(Seq(Permission.create(perm))))), atMost = 10.second))
            profileAvailable = Some(Await.result(profileRepository.profileExists(id), atMost = 10.second))

            Thread.sleep(1000)
            i = i + 1
          }

          ProfileMessage("done", Some(profileAccessible.get.allowed && profileAvailable.get))
        }
      ))

    }

  //    ServiceCall {
  //      _ => {
  //
  //      }
  //    }

  //  def doWaitForProfile(name: String): Future[Profile] = {
  //
  //    s.isUserPermitted(a.userId).invoke(permissionRequest(a))
  //
  //    Future {
  //      val d = false
  //      do{
  //        val p = getProfile(name)
  //
  //      }while(!d)
  //    }
  //  }

  override def _getProfile(profileOwner: UUID): ServerServiceCall[NotUsed, InternalProfileAPI] =
    ServerServiceCall { _ =>
      doGetProfile(
        profileOwner,
        x =>
          toInternalAPI(x)
      )
    }

  private def toInternalAPI(x: InternalProfile) =
    InternalProfileAPI(
      user = x.user.toString,
      data = x.data,
      addresses = x.addresses,
      avatar = x.avatar
    )

  override def getProfile(profileOwner: UUID): ServerServiceCall[NotUsed, Profile] =
    authorizedString(token => s"profile:${token.team}:${profileOwner}:${profileOwner}:*:read") { (token, _) =>
      ServerServiceCall { _ =>
        doGetProfile(profileOwner, toApi)
      }
    }

  def toApi(p: InternalProfile): Profile =
    Profile(
      p.user.toString,
      p.data,
      p.addresses,
      p.avatar.map(_.toApi)
    )

  private def doGetProfile[T](profileOwner: UUID, conv: InternalProfile => T): Future[T] =
    refFor[ProfileEntity](profileOwner).ask(GetProfile(profileOwner))
      .map(_.response)
      .map {
        case Some(p) => conv(p)
        case None    => throw NotFound("profile not found")
      }

  override def updateProfile(name: String): ServiceCall[ProfileData, Done] =
    authorizedString(token =>
      s"profile:${token.team}:${
          token.userId
        }:${
          token.userId
        }:*:write"
    ) {
      (token, _) =>
        ServerServiceCall {
          s => // TODO user

            val prof                 = refFor[ProfileEntity](token.userId)
            var f: Seq[Future[Done]] = Seq()
            f = f :+ prof.ask(SetFields(
              token.userId,
              ProfileData(
                firstName = s.firstName,
                lastName = s.lastName,
                country = s.country,
                info = s.info,
                language = s.language
              )
            )).map(_ => Done)

            Future.sequence(f).map { l =>
              if (l.forall(_ == Done)) {
                Done
              } else {
                Done
              }
            }
        }

    }

  def _createProfile(id: UUID) =
    refFor[ProfileEntity](id).ask(CreateProfile(id))

  override def createProfile(name: String): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"profile:${token.team}:${
          token.userId
        }:${
          token.userId
        }:*:write"
    ) {
      (token, _) =>
        ServerServiceCall {
          s => // TODO user
            refFor[ProfileEntity](token.userId).ask(CreateProfile(token.userId))
              .map(_ => Done)
        }
    }

  override def getAddresses(name: String): ServiceCall[NotUsed, Seq[Address]] =
    authorizedString(token =>
      s"profile:${token.team}:${
          token.userId
        }:${
          token.userId
        }:address:read"
    ) {
      (token, _) =>
        ServerServiceCall {
          s => // TODO user
            doGetProfile(token.userId, toApi).map(p => p.addresses)
        }
    }

  override def createAddress(name: String): ServiceCall[Address, Done] =
    authorizedString(token =>
      s"profile:${token.team}:${
          token.userId
        }:${
          token.userId
        }:address:write"
    ) {
      (token, _) =>
        ServerServiceCall {
          s => // TODO user
            refFor[ProfileEntity](token.userId).ask(AddAddress(s.copy(id = Some(UUID.randomUUID()))))
        }
    }

  override def deleteAddress(name: String, id: UUID): ServiceCall[NotUsed, Done] =
    authorizedString(token =>
      s"profile:${token.team}:${
          token.userId
        }:${
          token.userId
        }:address:write"
    ) {
      (token, _) =>
        ServerServiceCall {
          _ => // TODO user
            refFor[ProfileEntity](token.userId).ask(RemoveAddress(id))
        }
    }

  override def updateAddress(name: String, id: UUID): ServiceCall[Address, Done] =
    authorizedString(token =>
      s"profile:${token.team}:${
          token.userId
        }:${
          token.userId
        }:address:write"
    ) {
      (token, _) =>
        ServerServiceCall {
          add => // TODO user
            refFor[ProfileEntity](token.userId).ask(UpdateAddressFields(id, add))
        }
    }

  override def getTeamProfile(team: Option[String]): ServiceCall[NotUsed, Profile] =
    authorizedString { token =>
      val t = team.getOrElse(token.team)
      s"teamprofile:${t}:${t}:${t}:*:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        val t = team.getOrElse(token.team)
        doGetTeamProfile(t).map(toApi)
      }
    }

  override def _getTeamProfile(team: String): ServerServiceCall[NotUsed, InternalProfileAPI] =
    ServerServiceCall { _ =>
      doGetTeamProfile(team)
        .map(toInternalAPI)
    }

  private def doGetTeamProfile(team: String): Future[InternalProfile] =
    registry.refFor[TeamIDEntity](team).ask(GetID(team))
      .flatMap { id =>
        doGetProfile(id.id, x => x)
          .recoverWith {
            case _: NotFound =>
              refFor[ProfileEntity](id.id).ask(CreateProfile(id.id))
          }
      }

  override def updateTeamProfile(team: Option[String]): ServiceCall[ProfileData, Profile] =
    authorizedString { token =>
      val t = team.getOrElse(token.team)
      s"teamprofile:${t}:${t}:${t}:*:write"
    } { (token, _) =>
      ServerServiceCall { s =>
        val t = team.getOrElse(token.team)
        registry.refFor[TeamIDEntity](t).ask(GetID(t))
          .flatMap { id =>
            val prof = refFor[ProfileEntity](id.id)
            prof.ask(SetFields(
              token.userId,
              ProfileData(
                firstName = s.firstName,
                lastName = s.lastName,
                country = s.country,
                info = s.info,
                language = s.language
              )
            )).map(toApi)
          }
      }
    }

  override def updateTeamAddress(address: Option[UUID], team: Option[String]): ServiceCall[Address, Profile] =
    authorizedString { token =>
      val t = team.getOrElse(token.team)
      s"teamprofile:${t}:${t}:${t}:address:write"
    } {
      (token, _) =>
        val t = team.getOrElse(token.team)
        ServerServiceCall {
          add =>
            doGetTeamProfile(t)
              .map { p =>
                address match {
                  case Some(value) =>
                    refFor[ProfileEntity](p.user).ask(UpdateAddressFields(value, add))
                  case None =>
                    refFor[ProfileEntity](p.user).ask(AddAddress(add.copy(id = Some(UUID.randomUUID()))))
                }

              }
              .flatMap(_ => doGetTeamProfile(t))
              .map(toApi)
        }
    }

  override def deleteTeamAddress(address: UUID, team: Option[String]): ServiceCall[NotUsed, Profile] =
    authorizedString { token =>
      val t = team.getOrElse(token.team)
      s"teamprofile:${t}:${t}:${t}:address:write"
    } { (token, _) =>
      val t = team.getOrElse(token.team)
      ServerServiceCall { _ =>
        doGetTeamProfile(t)
          .map(p => refFor[ProfileEntity](p.user).ask(RemoveAddress(address)))
          .flatMap(_ => doGetTeamProfile(t))
          .map(toApi)

      }
    }

  private def refFor[T <: PersistentEntity: ClassTag](id: UUID) = registry.refFor[T](id.toString)

}
