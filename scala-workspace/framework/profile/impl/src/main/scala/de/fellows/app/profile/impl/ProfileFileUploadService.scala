package de.fellows.app.profile.impl

import akka.stream.IOResult
import akka.stream.scaladsl.{FileIO, Sink}
import akka.util.ByteString
import com.typesafe.config.ConfigFactory
import de.fellows.app.security.AccessControlServiceComposition._
import de.fellows.app.security.SecurityBodyParser
import de.fellows.utils.security.GenericTokenContent
import org.slf4j.LoggerFactory
import play.api.http.FileMimeTypes
import play.api.libs.streams.Accumulator
import play.api.mvc.MultipartFormData.FilePart
import play.api.mvc._
import play.api.routing.Router
import play.api.routing.sird._
import play.core.parsers.Multipart.{FileInfo, FilePartHandler}

import java.io.File
import java.time.{Instant, ZoneId, ZonedDateTime}
import scala.concurrent.{ExecutionContext, Future}

class ProfileFileUploadService(
    action: DefaultActionBuilder,
    parser: PlayBodyParsers,
    mime: FileMimeTypes,
    exCtx: ExecutionContext
) {
  lazy final val logger             = LoggerFactory.getLogger(classOf[ProfileFileUploadService])
  implicit val ec: ExecutionContext = exCtx
  implicit val m: FileMimeTypes     = mime

  var app: ProfileServiceApp = _

  def withApp(app: ProfileServiceApp): ProfileFileUploadService = {
    this.app = app
    this
  }

  lazy val conf = ConfigFactory.load()
  val basePath: String =
    conf.getString("fellows.storage.service")

  // A Play FilePartHandler[T] creates an Accumulator (similar to Akka Stream's Sinks)
  // for each FileInfo in the multipart request.
  private def fileHandler(token: GenericTokenContent): FilePartHandler[File] = {
    case FileInfo(partName, filename, contentType, s) =>
      logger.debug(s"upload file $partName, $filename, $contentType, $s")
      contentType match {
        case Some(t) if t.startsWith("image/") => handleValidFile(token, filename, partName, contentType)
        case Some(_)                           => throw new IllegalArgumentException("unaccepted contenttype")
        case None                              => throw new IllegalArgumentException("missing contenttype")
      }

  }

  private def handleValidFile(
      token: GenericTokenContent,
      filename: String,
      partName: String,
      contentType: Option[String]
  ) = {
    val tempFile = {
      // create a temp file in the `target` folder

      val folder = new File(s"$basePath/${token.getUserId}/avatar/")
      folder.mkdirs()

      if (folder.canWrite) {
        folder.listFiles().foreach(_.delete())
      }

      val f = new java.io.File(s"$basePath/${token.getUserId}/avatar", filename).getAbsoluteFile
      // make sure the sub-folders inside `target` exist.

      f.getParentFile.mkdirs()
      f
    }

    val sink: Sink[ByteString, Future[IOResult]] = FileIO.toPath(tempFile.toPath)
    val acc: Accumulator[ByteString, IOResult]   = Accumulator(sink)
    acc.map {
      case akka.stream.IOResult(_, _) =>
        FilePart(partName, filename, contentType, tempFile)
    }
  }

  val router = Router.from {
    case POST(p"/files/profile/avatar       ") =>
      uploadAvatar

    case GET(p"/files/profile/avatar       ") =>
      downloadAvatar
  }

  private def downloadAvatar =
    action(parser.anyContent) {
      request =>
        authorizedStringRequest(
          token =>
            s"profile:${
                token.team
              }:${
                token.userId
              }:avatar:write",
          request
        ) { token =>
          val folder = new File(s"$basePath/${token.userId}/avatar/").getAbsoluteFile
          if (folder.exists()) {
            val imgs = folder.listFiles()
            if (imgs.nonEmpty) {

              val v = Results.Ok.sendFile(imgs.head)

              val instant       = Instant.ofEpochMilli(imgs.head.lastModified())
              val localDateTime = ZonedDateTime.ofInstant(instant, ZoneId.systemDefault)
              v.withDateHeaders("Last-Modified" -> localDateTime)

            } else {
              Results.NotFound("No Avatar available")
            }
          } else {
            Results.NotFound("No Avatar available")
          }
        }
    }

  private def uploadAvatar =
    action(
      SecurityBodyParser(token =>
        s"profile:${
            token.getTeam
          }:${
            token.getUserId
          }:${
            token.getUserId
          }:avatar:write"
      )(token => parser.multipartFormData(fileHandler(token), 2 * 1024 * 1024))
    ) {

      request =>
        val files = request.body.files.map(_.ref.getAbsolutePath)
        Results.Ok("{}")
    }
}
