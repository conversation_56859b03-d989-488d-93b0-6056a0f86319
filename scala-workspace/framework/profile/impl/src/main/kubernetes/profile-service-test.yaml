---
apiVersion: "apps/v1beta2"
kind: Deployment
metadata:
  name: "profile-test"
  labels:
    appName: profile
    appNameVersion: "profile-test"
spec:
  replicas: 2
  selector:
    matchLabels:
      appNameVersion: "profile-test"
  template:
    metadata:
      labels:
        appName: profile
        appNameVersion: "profile-test"
    spec:
      restartPolicy: Always
      imagePullSecrets:
        - name: gitlab-auth
      containers:
        - name: profile
          image: "jira.electronic-fellows.de:5000/app/backend/profile-impl:latest"
          imagePullPolicy: Always
          env:
            - name: "JAVA_OPTS"
              value: "-Dplay.crypto.secret=amazingsecret"
            - name: "RP_APP_NAME"
              value: profile
            - name: "RP_APP_TYPE"
              value: lagom
            - name: "RP_APP_VERSION"
              value: "1.0-SNAPSHOT"
            - name: "RP_DYN_JAVA_OPTS"
              value: "-Dakka.discovery.kubernetes-api.pod-namespace=$RP_NAMESPACE"
            - name: "RP_ENDPOINTS"
              value: "HTTP,AKKA_REMOTE,AKKA_MGMT_HTTP"
            - name: "RP_ENDPOINTS_COUNT"
              value: "3"
            - name: "RP_ENDPOINT_0_BIND_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_0_BIND_PORT"
              value: "10000"
            - name: "RP_ENDPOINT_0_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_0_PORT"
              value: "10000"
            - name: "RP_ENDPOINT_1_BIND_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_1_BIND_PORT"
              value: "10001"
            - name: "RP_ENDPOINT_1_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_1_PORT"
              value: "10001"
            - name: "RP_ENDPOINT_2_BIND_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_2_BIND_PORT"
              value: "10002"
            - name: "RP_ENDPOINT_2_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_2_PORT"
              value: "10002"
            - name: "RP_ENDPOINT_AKKA_MGMT_HTTP_BIND_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_AKKA_MGMT_HTTP_BIND_PORT"
              value: "10002"
            - name: "RP_ENDPOINT_AKKA_MGMT_HTTP_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_AKKA_MGMT_HTTP_PORT"
              value: "10002"
            - name: "RP_ENDPOINT_AKKA_REMOTE_BIND_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_AKKA_REMOTE_BIND_PORT"
              value: "10001"
            - name: "RP_ENDPOINT_AKKA_REMOTE_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_AKKA_REMOTE_PORT"
              value: "10001"
            - name: "RP_ENDPOINT_HTTP_BIND_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_HTTP_BIND_PORT"
              value: "10000"
            - name: "RP_ENDPOINT_HTTP_HOST"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_ENDPOINT_HTTP_PORT"
              value: "10000"
            - name: "RP_JAVA_OPTS"
              value: "-Dconfig.resource=rp-application.conf -Dakka.management.cluster.bootstrap.contact-point-discovery.discovery-method=kubernetes-api -Dakka.management.cluster.bootstrap.contact-point-discovery.port-name=akka-mgmt-http -Dakka.management.cluster.bootstrap.contact-point-discovery.effective-name=profile -Dakka.management.cluster.bootstrap.contact-point-discovery.required-contact-point-nr=2 -Dakka.discovery.kubernetes-api.pod-label-selector=appName=%s -Dcom.lightbend.platform-tooling.service-discovery.external-service-addresses.cas_native.0=cassandra"
            - name: "RP_KUBERNETES_POD_IP"
              valueFrom:
                fieldRef:
                  fieldPath: "status.podIP"
            - name: "RP_KUBERNETES_POD_NAME"
              valueFrom:
                fieldRef:
                  fieldPath: "metadata.name"
            - name: "RP_MODULES"
              value: "akka-cluster-bootstrapping,akka-management,common,play-http-binding,service-discovery,status"
            - name: "RP_NAMESPACE"
              valueFrom:
                fieldRef:
                  fieldPath: "metadata.namespace"
            - name: "RP_PLATFORM"
              value: kubernetes
          ports:
            - containerPort: 10000
              name: http
            - containerPort: 10001
              name: "akka-remote"
            - containerPort: 10002
              name: "akka-mgmt-http"
          volumeMounts: [ ]
          command:
            - "/rp-start"
          args:
            - "bin/profile-impl"
          readinessProbe:
            httpGet:
              path: "/platform-tooling/ready"
              port: "akka-mgmt-http"
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: "/platform-tooling/healthy"
              port: "akka-mgmt-http"
            periodSeconds: 10
            initialDelaySeconds: 60
      volumes: [ ]
