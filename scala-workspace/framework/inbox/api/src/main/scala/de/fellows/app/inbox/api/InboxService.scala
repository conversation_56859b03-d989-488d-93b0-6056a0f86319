// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F

package de.fellows.app.inbox.api

import akka.NotUsed
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.app.inbox.commons.MailMessageRef
import de.fellows.utils.UUIDUtils.Base64EncodedString
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.{OpenAPIDefinition, Operation}

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.2",
    title = "Stackrate Inbox API"
  )
)
abstract class InboxService extends Service with StackrateServiceAPI {

  val internalBasePath = "/internal/inbox/mails"
  val basePath         = "/api/inbox/mails"

  /** Accepts parsed emails from SendGrid and sends them further for processing when needed
    * @return
    */
  def _acceptEmailHook(): ServiceCall[NotUsed, String]

  /** For internal use only.
    */
  def _getMailMessage: ServiceCall[MailMessageRef, MailMessage]

  @StackrateApi
  @Tag(name = "Mail")
  @Operation(
    summary = "Get Mail",
    description =
      """
        |Get a specific mail
        |"""
  )
  def getMail(messageEntityId: Base64EncodedString): ServiceCall[NotUsed, ApiMail]

  def mailsWithAttachmentTopic(): Topic[MailMessage]

  override def descriptor: Descriptor = {
    import Service._
    withDocumentation(
      named("inbox")
        .withCalls(
          restCall(Method.POST, s"$basePath/webhook", _acceptEmailHook _),
          restCall(Method.GET, s"$internalBasePath", _getMailMessage _),
          restCall(Method.GET, s"$basePath/inboxes/mails/:messageEntityId", getMail _)
        )
        .withTopics(
          topic(InboxService.MAIL, mailsWithAttachmentTopic())
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"/api/inbox/.*")),
          ServiceAcl(pathRegex = Some(s"/files/inbox/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
  }
}

object InboxService {
  val V = s"v1.0"

  val MAIL = s"domain.inbox.mail-$V"
}

// 1
