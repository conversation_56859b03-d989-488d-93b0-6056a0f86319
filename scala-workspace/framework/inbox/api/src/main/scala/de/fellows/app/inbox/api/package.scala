package de.fellows.app.inbox

import de.fellows.app.inbox.commons.{MailAddress, MailBody, MailBox, MailMessageEntityId}
import de.fellows.utils.FilePath
import de.fellows.utils.UUIDUtils.Base64EncodedString
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.streams.PayloadName
import play.api.libs.json.{Format, Json}

import java.time.Instant

package object api {

  case class MailContact(
      from: Option[String],
      to: Seq[String],
      cc: Seq[String],
      bcc: Seq[String],
      replyTo: Option[String]
  )

  case class ApiMail(
      id: String,
      mailbox: MailBox,
      encodedEntityId: Base64EncodedString,
      subject: Option[String] = None,
      message: Option[String] = None,
      plainMessage: Option[String] = None,
      contact: MailContact,
      sentDate: Option[Instant] = None,
      attachments: Seq[ApiAttachment] = Seq()
  )

  case class ApiAttachment(
      name: String,
      disposition: String,
      path: String
  )

  case class MailMessage(
      id: String,
      mailbox: MailBox,
      team: Option[String] = None,
      subject: Option[String] = None,
      sender: Option[MailAddress] = None,
      recipients: Seq[MailAddress] = Seq(),
      replyTo: Seq[MailAddress] = Seq(),
      sentDate: Option[Instant] = None,
      body: Option[MailBody] = None,
      attachments: Seq[Attachment] = Seq()
  )

  case class Attachment(
      name: String,
      disposition: String,
      path: FilePath
  )

  case class InboxHealth(running: Boolean, connected: Boolean, msg: Option[String])

  object Attachment {
    implicit val format: Format[Attachment] = Json.format[Attachment]
  }

  object MailMessage {
    implicit val format: Format[MailMessage] = Json.format[MailMessage]
  }

  object MailContact {
    implicit val format: Format[MailContact] = Json.format[MailContact]
  }

  object ApiAttachment {
    implicit val format: Format[ApiAttachment] = Json.format[ApiAttachment]

  }

  object ApiMail {
    implicit val format: Format[ApiMail] = Json.format[ApiMail]

    def apply(mail: MailMessage)(implicit sd: ServiceDefinition): ApiMail = {
      import de.fellows.utils.UUIDUtils._
      val entityId = MailMessageEntityId(mail.mailbox.team, mail.mailbox.inboxId, mail.id).value.encode
      new ApiMail(
        id = mail.id,
        mailbox = mail.mailbox,
        encodedEntityId = entityId,
        subject = mail.subject,
        message = mail.body.map(_.html.mkString("")),
        plainMessage = mail.body.map(_.plain.mkString("")),
        contact = MailContact(
          from = mail.sender.map(_.address),
          to = mail.recipients.map(_.address),
          cc = Seq(),
          bcc = Seq(),
          replyTo = mail.replyTo.map(_.address).headOption
        ),
        sentDate = mail.sentDate,
        attachments = mail.attachments.map(m => ApiAttachment(m.name, m.disposition, m.path.toApi))
      )
    }
  }
}
