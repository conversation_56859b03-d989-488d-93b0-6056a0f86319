include "main-application.conf"

play.application.loader = de.fellows.app.inbox.impl.InboxServiceLoader


inbox.cassandra.keyspace = ${fellows.persistence.rootKeyspace}inbox


cassandra-journal {
  keyspace = ${inbox.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${inbox.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${inbox.cassandra.keyspace}_read_v1
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "inbox"
# fellows.serviceconfig = ${fellows.services.inbox}


fellows.storage {
  service = ${fellows.storage.base}/inbox
}

akka.actor {
  serialization-bindings {
    "de.fellows.app.inbox.api.package$MailMessage" = jackson-json
  }
}

akka.management {
  health-checks {
    readiness-checks {
    }
  }
}

# reaper removes temporary files older than 30 minutes (in this case)
play.temporaryFile {
  reaper {
    enabled = true
    initialDelay = "5 minutes"
    interval = "30 seconds"
    olderThan = "30 minutes"
  }
}
