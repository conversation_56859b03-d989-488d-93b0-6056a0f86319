package de.fellows.app.inbox.impl.entities

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import de.fellows.app.inbox.impl.entities.mail.{MailReceived, MailTriggered, StartReceiveMail, StopReceiveMail}

object InboxServiceSerializerRegistry extends JsonSerializerRegistry {
  override def serializers =
    List(
      JsonSerializer[StartReceiveMail],
      JsonSerializer[StartReceiveMail],
      JsonSerializer[MailReceived],
      JsonSerializer[MailTriggered]
    )

}
