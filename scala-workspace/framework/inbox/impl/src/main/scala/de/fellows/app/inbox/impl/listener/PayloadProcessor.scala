package de.fellows.app.inbox.impl.listener

import de.fellows.app.inbox.api.MailMessage
import de.fellows.app.inbox.commons.{MailAddress, MailBody, MailBox, MailMessageId}
import play.api.Logging

import java.time.format.{DateTimeFormatter, DateTimeParseException}
import java.time.{Instant, ZonedDateTime}
import javax.mail.internet.InternetAddress

/** The purpose of this processor is to convert the payload from SendGrid into an object we can work with later on.
 *
 * The details of SendGrid's payload:
 * https://docs.sendgrid.com/for-developers/parsing-email/setting-up-the-inbound-parse-webhook
 */
object PayloadProcessor extends Logging {

  def get(parts: Map[String, Seq[String]])(names: Seq[String]): Option[Seq[String]] =
    names.flatMap(parts.get).headOption

  def extractTeamId(value: String): Option[MailMessageId] =
    extractMailAddress(value).map(_.split("@").head)

  protected def extractMailAddress(value: String): Option[String] = {
    val add = InternetAddress.parse(value).headOption
    add.map(_.getAddress)
  }

  def convert(id: MailMessageId, parts: Map[String, Seq[String]]): Option[MailMessage] = {
    val conv: Seq[String] => Option[Seq[String]] = get(parts)

    val recipient = conv(Seq("to", "recipient"))

    val mayBeTeam = recipient.flatMap { recipients =>
      recipients.headOption.flatMap(extractTeamId)
    }
    mayBeTeam.map { team =>
      val headers = parts.get("headers") match {
        case None => Map[String, String]()
        case Some(value) => parseHeaders(value.head)
      }
      val body =
        MailBody(conv(Seq("text", "body-plain")).getOrElse(Seq()), conv(Seq("html", "body-html")).getOrElse(Seq()))
      MailMessage(
        id,
        MailBox(team, team),
        Some(team),
        subject = parts.get("subject").flatMap(_.headOption),
        sender = parts.get("from").flatMap(_.headOption.flatMap(x => extractMailAddress(x).map(MailAddress(_)))),
        recipients = recipient.getOrElse(Seq()).flatMap(x => extractMailAddress(x).map(MailAddress(_))),
        replyTo = headers.getOrElse("Reply-To", "").split(',').flatMap(extractMailAddress).map(MailAddress(_)),
        sentDate = headers.get("Date").map(parseDate),
        body = Some(body)
      )
    }
  }

  def parseDate(value: String): Instant =
    try
      ZonedDateTime.parse(value, DateTimeFormatter.RFC_1123_DATE_TIME).toInstant
    catch {
      case e: DateTimeParseException =>
        logger.warn(s"unable to parse email date ${value}, use 'now'")
        Instant.now()
    }

  /** Parse the mail headers where each header is on its own line
   */
  protected def parseHeaders(headers: String): Map[String, String] = headers
    .split('\n')
    .map { x =>
      // Separate the name of the header and its body
      // Example:
      // Reply-To: <<EMAIL>>
      val tokens = x.split(':')
      tokens.head.trim -> tokens.tail.mkString(":").trim
    }.toMap
}
