package de.fellows.app.inbox.impl.listener

import de.fellows.app.inbox.api.Attachment
import de.fellows.app.inbox.commons.UniqueTeamId
import de.fellows.utils.FilePath
import play.api.libs.Files
import play.api.mvc.MultipartFormData

object Attachments {

  /** Stores the received attachment to disk
    */
  def saveAttachment(
      basePath: String,
      teamId: UniqueTeamId,
      msgID: String,
      contentType: String,
      file: MultipartFormData.FilePart[Files.TemporaryFile]
  ): Attachment = {
    val path = getPath(basePath, teamId, msgID, file.filename)
    path.createParentDir()
    file.ref.copyTo(path.toJavaPath, replace = true)
    Attachment(
      name = file.filename,
      disposition = contentType,
      path = path
    )
  }

  /** Returns unique path for an email's attachment
    */
  protected def getPath(basePath: String, team: UniqueTeamId, msgID: String, filename: String): FilePath = {
    def ap(fname: String) = FilePath(basePath, team, msgID, "attachments", None, fname)

    var currentPath = ap(filename)

    var index = 1
    // In case the file already exists, we increase its index. Alternatively, we can simply generate a random
    //  number instead of using index because we store the whole path, not its components
    while (currentPath.toJavaFile.exists()) {
      currentPath = ap(s"${index}_$filename")
      index += 1
    }
    currentPath
  }
}
