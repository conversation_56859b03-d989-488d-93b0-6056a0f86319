package de.fellows.app.inbox.impl

import akka.NotUsed
import akka.actor.ActorSystem
import akka.actor.typed.Scheduler
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.api.broker.Topic
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.broker.TopicProducer
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.InvalidCommandException
import com.lightbend.lagom.scaladsl.persistence.{EventStreamElement, PersistentEntityRegistry}
import com.lightbend.lagom.scaladsl.server.{PlayServiceCall, ServerServiceCall}
import com.typesafe.config.ConfigFactory
import de.fellows.app.inbox.api
import de.fellows.app.inbox.api._
import de.fellows.app.inbox.commons.{MailMessageEntityId, MailMessageRef, UniqueTeamId}
import de.fellows.app.inbox.impl.entities.mail.{GetMailMessage, MailEntity, MailEvent, MailReceived}
import de.fellows.app.inbox.impl.listener.EmailProcessor.processNewMailMessage
import de.fellows.app.inbox.impl.listener.PayloadProcessor
import de.fellows.app.security.AccessControlServiceComposition._
import de.fellows.utils.UUIDUtils.Base64EncodedString
import de.fellows.utils.apidoc.StackrateAPIImpl
import de.fellows.utils.communication.ServiceDefinition
import play.api.Logging
import play.api.mvc.{ControllerComponents, EssentialAction, Results}

import scala.collection.immutable
import scala.concurrent.{ExecutionContext, Future}

class InboxServiceImpl(
    val components: ControllerComponents,
    ereg: PersistentEntityRegistry,
    system: ActorSystem
)(implicit val exc: ExecutionContext, sd: ServiceDefinition, scheduler: Scheduler)
    extends InboxService with StackrateAPIImpl with Logging with Results {
  implicit val actorSystem: ActorSystem = system
  lazy val conf            = ConfigFactory.load()

  /** Accepts parsed emails from SendGrid and sends them further for processing when needed
    *
    * @return
    */
  override def _acceptEmailHook(): ServiceCall[NotUsed, String] = PlayServiceCall { wrapCall =>
    // Need to use EssentialAction so that we can check the content type before we choose
    // a body parser
    EssentialAction { requestHeader =>
      logger.info(
        s"""got mail request ${requestHeader} with contenttype ${requestHeader.contentType}
           | headers: ${requestHeader.headers.headers.map(x => s"${x._1}: ${x._2}").mkString(", ")}
           |""".stripMargin
      )
      val action = requestHeader.contentType match {
        case Some("multipart/form-data") =>
          components.actionBuilder.async(components.parsers.multipartFormData(60 * 1024 * 1024)) { request =>
            val messageEntityId = MailMessageEntityId.create

            logger.info(
              s"""found multipart message:
                 |${request.body.dataParts.map(x => s"${x._1} -> ${x._2.mkString(",")}").mkString("; ")}
                 |""".stripMargin
            )

            val message = PayloadProcessor.convert(messageEntityId.value, request.body.dataParts)
            message match {
              case None =>
                logger.error(s"Error while parsing data parts, ${request.body.dataParts}")
                Future.successful(Ok)
              case Some(msg) =>
                if (request.body.files.isEmpty) {
                  logger.info("Email message has no files attached. Skip processing")
                  Future.successful(Ok)
                } else {
                  logger.info(s"Processing message with id=${messageEntityId.value} and ${request.body.files}")
                  for {
                    _      <- processNewMailMessage(msg, request.body.files, ereg)
                    result <- Future.successful(Ok)
                  } yield result
                }
            }
          }
        case _ =>
          // any request which is not multipart we skip as it's not a correct one - SendGrid
          //  always sends multipart
          wrapCall(ServiceCall(_ => Future.successful("ok")))
      }
      action(requestHeader)
    }
  }

  override def _getMailMessage: ServerServiceCall[MailMessageRef, api.MailMessage] =
    ServerServiceCall { ref =>
      retrieveMail(ref.mailbox.team, ref.entityId)
    }

  override def getMail(encodedEntityId: Base64EncodedString): ServiceCall[NotUsed, ApiMail] =
    authorizedString { token =>
      s"inbox:${token.team}:${token.team}:*:mail:read"
    } { (token, _) =>
      ServerServiceCall { _ =>
        import de.fellows.utils.UUIDUtils._
        retrieveMail(token.team, encodedEntityId.decode).map(mail => ApiMail(mail))
      }
    }

  private def retrieveMail(teamId: UniqueTeamId, mailEntityId: String): Future[MailMessage] =
    ereg.refFor[MailEntity](mailEntityId).ask(GetMailMessage(teamId)).recover {
      case e: InvalidCommandException =>
        throw new TransportException(
          TransportErrorCode.NotFound,
          s"Mail not found with id=$mailEntityId for team=$teamId: ${e.message}"
        )
    }

  override def mailsWithAttachmentTopic(): Topic[api.MailMessage] =
    TopicProducer.taggedStreamWithOffset(MailEvent.Tag) { (tag, offset) =>
      ereg.eventStream(tag, offset).mapConcat {
        case x @ EventStreamElement(_, ev: MailReceived, _) if ev.mail.attachments.nonEmpty =>
          immutable.Seq((ev.mail, x.offset))
        case _ => Nil
      }
    }

}
