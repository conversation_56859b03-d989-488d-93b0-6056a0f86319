package de.fellows.app.inbox.impl.entities

import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.inbox.api.MailMessage
import de.fellows.app.inbox.commons.{MailBox, UniqueTeamId}
import play.api.libs.json.{Format, Json}

package object mail {

  sealed trait MailEvent extends AggregateEvent[MailEvent] {
    override def aggregateTag: AggregateEventShards[MailEvent] = MailEvent.Tag
  }

  object MailEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[MailEvent](NumShards)
  }

  sealed trait MailCommand

  case class StartReceiveMail(msgId: String, teamId: UniqueTeamId) extends MailCommand with ReplyType[Done]

  case class StopReceiveMail(mail: MailMessage) extends MailCommand with ReplyType[MailMessage]

  /** The sole purpose of team is to verify that the requested message belongs to the given team.
    */
  case class GetMailMessage(teamId: UniqueTeamId) extends MailCommand with ReplyType[MailMessage]

  case class MailTriggered(mail: MailMessage) extends MailEvent

  case class MailReceived(mail: MailMessage) extends MailEvent

  object StartReceiveMail {
    implicit val format: Format[StartReceiveMail] = Json.format[StartReceiveMail]
  }
  object StopReceiveMail {
    implicit val format: Format[StopReceiveMail] = Json.format[StopReceiveMail]
  }

  object MailTriggered {
    implicit val format: Format[MailTriggered] = Json.format[MailTriggered]
  }

  object MailReceived {
    implicit val format: Format[MailReceived] = Json.format[MailReceived]
  }

}
