package de.fellows.app.camunda.api

import akka.actor.ActorSystem
import akka.http.scaladsl.Http
import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport._
import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model._
import akka.http.scaladsl.settings.ConnectionPoolSettings
import akka.http.scaladsl.unmarshalling.Unmarshal
import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.transport.{TransportErrorCode, TransportException}
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.typesafe.config.ConfigFactory
import play.api.libs.json._

import java.io.File
import scala.concurrent.{ExecutionContext, Future}

case class CamundaContext(team: String, endpoint: String)

class Camunda(ctx: CamundaContext)(implicit a: ActorSystem, ec: ExecutionContext)
    extends CamundaBase[ServerServiceCall[_, _]] {
  implicit val context: CamundaContext = ctx

  val conf = ConfigFactory.load()
  val defaultConnectionPoolSettings = ConnectionPoolSettings(a)
    .withMaxOpenRequests(128)
    .withMaxConnections(128)

  import Camunda._

  def call[T](uri: String, method: HttpMethod, entity: T)(implicit w: Writes[T]): Future[HttpResponse] =
    call(uri, method, Some(Json.toJson(entity)))

  def call(uri: String, method: HttpMethod = HttpMethods.GET, entity: Option[JsValue] = None): Future[HttpResponse] =
    Http()
      .singleRequest(
        HttpRequest(
          uri = uri,
          method = method,
          entity =
            entity.map(v => HttpEntity(ContentTypes.`application/json`, Json.stringify(v))).getOrElse(HttpEntity.Empty)
        ),
        settings = defaultConnectionPoolSettings
      )

  def callUri(uri: Uri, method: HttpMethod = HttpMethods.GET, entity: Option[JsValue] = None): Future[HttpResponse] =
    Http()
      .singleRequest(
        HttpRequest(
          uri = uri,
          method = method,
          entity =
            entity.map(v => HttpEntity(ContentTypes.`application/json`, Json.stringify(v))).getOrElse(HttpEntity.Empty)
        ),
        settings = defaultConnectionPoolSettings
      )

  def form(uri: String, method: HttpMethod = HttpMethods.POST, entity: Multipart.FormData): Future[HttpResponse] =
    Http()
      .singleRequest(
        HttpRequest(
          uri = uri,
          method = method,
          entity = entity.toEntity()
        ),
        settings = defaultConnectionPoolSettings
      )

  def startProcessByKey(key: String): ServerServiceCall[FormCall, CallResult] =
    ServerServiceCall { c =>
      call(e"/process-definition/key/$key/start", HttpMethods.POST, Some(c)).flatMap(r =>
        PlayUnmarshal(r).to[CallResult]
      )
    }

  def startProcessByKeyAndTenant(key: String, tenant: String): ServerServiceCall[FormCall, CallResult] =
    ServerServiceCall { c =>
      call(e"/process-definition/key/$key/tenant-id/$tenant/start", HttpMethods.POST, Some(c)).flatMap(r =>
        PlayUnmarshal(r).to[CallResult]
      )
    }

  def startProcessByID(id: String): ServerServiceCall[FormCall, CallResult] =
    ServerServiceCall { c =>
      call(e"/process-definition/$id/start", HttpMethods.POST, Some(c)).flatMap(r => PlayUnmarshal(r).to[CallResult])
    }

  def getProcessDefinitionList(): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { c =>
      call(e"/process-definition")
        .flatMap(r =>
          PlayUnmarshal(r)
            .to[JsValue]
        )
    }

  override def getProcessDefinitionByKey(key: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { c =>
      call(e"/process-definition/key/$key")
        .flatMap(r =>
          PlayUnmarshal(r)
            .to[JsValue]
        )
    }

  override def getProcessDefinitionByKeyAndTenant(key: String, tenant: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { c =>
      call(e"/process-definition/key/$key/tenant-id/$tenant")
        .flatMap(r =>
          PlayUnmarshal(r)
            .to[JsValue]
        )
    }

  def submitStartFormByKey(key: String): ServerServiceCall[FormCall, NotUsed] =
    ServerServiceCall { c =>
      call(e"/process-definition/key/$key/submit-form", HttpMethods.POST, Some(c)).flatMap(empty)
    }

  def submitStartFormByID(id: String): ServerServiceCall[FormCall, NotUsed] =
    ServerServiceCall { c =>
      call(e"/process-definition/$id/submit-form", HttpMethods.POST, Some(c)).flatMap(empty)
    }

  def getProcessInstanceVariable(
      id: String,
      varName: String,
      deserialize: Boolean
  ): ServerServiceCall[NotUsed, JsObject] =
    ServerServiceCall { c =>
      call(e"/process-instance/$id/variables/$varName?deserializeValue=$deserialize").flatMap(r =>
        PlayUnmarshal(r).to[JsObject]
      )
    }

  def getProcessInstanceVariables(id: String, deserialize: Boolean): ServerServiceCall[NotUsed, JsObject] =
    ServerServiceCall { c =>
      call(e"/process-instance/$id/variables?deserializeValue=$deserialize").flatMap(r => PlayUnmarshal(r).to[JsObject])
    }

  def getTasks(
      processInstanceId: Option[String],
      processInstanceBusinessKey: Option[String]
  ): ServerServiceCall[NotUsed, Seq[Task]] =
    ???

  def getTaskByID(id: String): ServerServiceCall[NotUsed, Task] =
    ???

  def signal(): ServerServiceCall[SignalCall, NotUsed] =
    ServerServiceCall { c =>
      call(e"/signal", HttpMethods.POST, Some(c)).flatMap(empty)
    }

  protected def result(r: HttpResponse): Future[Unmarshal[HttpResponse]] =
    if (r.status.isFailure()) {
      Unmarshal(r).to[String].flatMap { res =>
        Future.failed(new TransportException(TransportErrorCode.fromHttp(r.status.intValue()), res))
      }
    } else {
      Future.successful(Unmarshal(r))
    }

  private def empty(r: HttpResponse) =
    if (r.status.isFailure()) {
      Unmarshal(r).to[String].flatMap { res =>
        Future.failed(new TransportException(TransportErrorCode.fromHttp(r.status.intValue()), res))
      }
    } else {
      Future.successful(NotUsed)
    }

  def message(): ServerServiceCall[MessageCall, NotUsed] =
    ServerServiceCall { c =>
      call(e"/message", HttpMethods.POST, Some(c)).flatMap(empty)
    }

  override def getDecisionXML(id: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { _ =>
      call(e"/decision-definition/$id/xml", HttpMethods.GET, None).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def getDecisionXMLByKey(key: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { _ =>
      call(e"/decision-definition/key/$key/xml", HttpMethods.GET, None).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def getDecisionXMLByKeyAndTenant(key: String, tenant: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { _ =>
      call(e"/decision-definition/key/$key/tenant-id/$tenant/xml", HttpMethods.GET, None).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def getDecision(id: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { _ =>
      call(e"/decision-definition/$id", HttpMethods.GET, None).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def getDecisions(
      tenantIdIn: Option[String] = None,
      key: Option[String] = None,
      version: Option[Int] = None,
      name: Option[String] = None
  ): ServerServiceCall[NotUsed, JsArray] =
    ServerServiceCall { _ =>
      val qm = Seq(
        tenantIdIn.map("tenantIdIn" -> _),
        key.map("key"               -> _),
        version.map("version"       -> _.toString),
        name.map("name"             -> _)
      ).flatten.toMap

      val q = Query(qm)

      val u = Uri.apply(e"/decision-definition")
        .withQuery(q)
      callUri(u, HttpMethods.GET, None).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint).as[JsArray])
    }

  override def getDecisionByKey(key: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { _ =>
      call(e"/decision-definition/key/$key", HttpMethods.GET, None).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def getDecisionByKeyAndTenant(key: String, tenant: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { _ =>
      call(e"/decision-definition/key/$key/tenant-id/$tenant", HttpMethods.GET, None).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def evaluateDecision(id: String): ServerServiceCall[FormCall, JsValue] =
    ServerServiceCall { c =>
      call(e"/decision-definition/$id/evaluate", HttpMethods.POST, Some(c)).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def evaluateDecisionByKey(key: String): ServerServiceCall[FormCall, JsValue] =
    ServerServiceCall { c =>
      call(e"/decision-definition/key/$key/evaluate", HttpMethods.POST, Some(c)).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def evaluateDecisionByKeyAndTenant(key: String, tenant: String): ServerServiceCall[FormCall, JsValue] =
    ServerServiceCall { c =>
      call(e"/decision-definition/key/$key/tenant-id/$tenant/evaluate", HttpMethods.POST, Some(c)).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def deleteDeployment(id: String, cascade: Boolean): ServerServiceCall[NotUsed, Done] =
    ServerServiceCall { c =>
      call(e"/deployment/$id?cascade=$cascade", HttpMethods.DELETE).flatMap(empty)
        .map(_ => Done)
    }

  override def getDeployment(id: String): ServerServiceCall[NotUsed, JsValue] =
    ServerServiceCall { c =>
      call(e"/deployment/$id", HttpMethods.GET).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }

  override def createDeployment(name: String, tenant: Option[String]): ServerServiceCall[File, JsValue] =
    ServerServiceCall { file =>
      val data = Multipart.FormData(
        Multipart.FormData.BodyPart.Strict("tenant-id", tenant.get),
        Multipart.FormData.BodyPart.Strict("deploy-changed-only", "true"),
        Multipart.FormData.BodyPart.Strict("deployment-name", name),
        Multipart.FormData.BodyPart.Strict("deployment-source", "stackrate"),
        Multipart.FormData.BodyPart.fromFile("data", ContentType.Binary(MediaTypes.`application/octet-stream`), file)
        //
      )
      form(e"/deployment/create", HttpMethods.POST, data).flatMap(result)
        .flatMap(_.to[spray.json.JsValue])
        .map(j => Json.parse(j.compactPrint))
    }
}

object Camunda {
  val conf = ConfigFactory.load()

  implicit class EndpointHelper(val sc: StringContext) extends AnyVal {
    def e(args: Any*)(implicit ctx: CamundaContext): String = {
      val res = ctx.endpoint + sc.s(args: _*)
      res
    }
  }

}
