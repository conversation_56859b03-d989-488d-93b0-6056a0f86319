package de.fellows.app.camunda.api

trait CamundaBase[T] {
  def startProcessByKey(key: String): T

  def startProcessByKeyAndTenant(key: String, tenant: String): T

  def startProcessByID(id: String): T

  def getProcessDefinitionList(): T

  def getProcessDefinitionByKey(key: String): T

  def getProcessDefinitionByKeyAndTenant(key: String, tenant: String): T

  def submitStartFormByKey(key: String): T

  def submitStartFormByID(id: String): T

  def getProcessInstanceVariable(id: String, varName: String, deserialize: Boolean = true): T

  def getProcessInstanceVariables(id: String, deserialize: Boolean = true): T

  def getTasks(processInstanceId: Option[String], processInstanceBusinessKey: Option[String]): T

  def getTaskByID(id: String): T

  def signal(): T

  def message(): T

  def getDecisionXML(id: String): T

  def getDecisionXMLByKey(key: String): T

  def getDecisionXMLByKeyAndTenant(key: String, tenant: String): T

  def getDecision(id: String): T

  def deleteDeployment(id: String, cascade: Boolean): T

  def getDecisions(tenantIdIn: Option[String], key: Option[String], version: Option[Int], name: Option[String]): T

  def getDecisionByKey(key: String): T

  def getDecisionByKeyAndTenant(key: String, tenant: String): T

  def evaluateDecision(id: String): T

  def evaluateDecisionByKey(key: String): T

  def evaluateDecisionByKeyAndTenant(key: String, tenant: String): T

  def getDeployment(id: String): T

  def createDeployment(name: String, tenant: Option[String]): T

}
