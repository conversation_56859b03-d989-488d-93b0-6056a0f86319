package de.fellows.app.price.impl

import com.lightbend.lagom.scaladsl.broker.kafka.LagomKafkaComponents
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraPersistenceComponents
import com.lightbend.lagom.scaladsl.server.{LagomApplication, LagomApplicationContext, LagomApplicationLoader}
import com.softwaremill.macwire.wire
import de.fellows.app.assemby.api.AssemblyService
import de.fellows.app.camunda.api.CamundaPricingService
import de.fellows.app.customer.api.CustomerService
import de.fellows.app.price.api.PriceService
import de.fellows.app.price.impl.entity.capabilities.CapabilitiesEntity
import de.fellows.app.price.impl.entity.pricing.{PricingEntity, PricingProcessor, PricingRepo}
import de.fellows.app.price.impl.entity.tables.DecisionTableEntity
import de.fellows.app.price.impl.read.{
  CapabilitiesIndexProcessor,
  CapabilitiesRepository,
  DecisionTableProcessor,
  DecisionTableRepository
}
import de.fellows.app.supplier.SupplierService
import de.fellows.ems.layerstack.api.LayerstackService
import de.fellows.ems.panel.api.PanelService
import de.fellows.ems.pcb.api.PCBService
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.{
  CombinedServiceLocatorComponents,
  MirrorDHealthCheckComponents,
  MirrorDServiceLocatorComponents,
  StackrateApplication
}
import kamon.Kamon
import play.api.libs.ws.ahc.AhcWSComponents
import play.api.mvc.EssentialFilter
import play.filters.cors.CORSComponents
import de.fellows.app.price.impl.read.DMNTableCache
import de.fellows.utils.health.HealthCheckComponents

class PriceServiceLoader extends LagomApplicationLoader {
  override def loadDevMode(context: LagomApplicationContext) =
    new PriceServiceApp(context) with MirrorDServiceLocatorComponents with MirrorDHealthCheckComponents

  override def load(context: LagomApplicationContext): LagomApplication = {
    Kamon.initWithoutAttaching(context.playContext.initialConfiguration.underlying)

    context.playContext.lifecycle.addStopHook { () =>
      Kamon.stop()
    }

    new PriceServiceApp(context) with CombinedServiceLocatorComponents
  }

  override def describeService = Some(readDescriptor[PriceService])
}

abstract class PriceServiceApp(context: LagomApplicationContext)
    extends StackrateApplication(context)
    with AhcWSComponents
    with CassandraPersistenceComponents
    with CORSComponents
    with LagomKafkaComponents
    with HealthCheckComponents {
  implicit val serviceDefinition: ServiceDefinition = ServiceDefinition("price")

  val pricingprocessor = wire[PricingProcessor]
  readSide.register(pricingprocessor)

  val pricingcamunda = serviceClient.implement[CamundaPricingService]
  val pcb            = serviceClient.implement[PCBService]
  val ass            = serviceClient.implement[AssemblyService]
  val cust           = serviceClient.implement[CustomerService]
  val panel          = serviceClient.implement[PanelService]
  val stacks         = serviceClient.implement[LayerstackService]
  val supplier       = serviceClient.implement[SupplierService]

  val capabilitiesIndexProcessor = wire[CapabilitiesIndexProcessor]
  readSide.register(capabilitiesIndexProcessor)

  val tableIndexProcessor = wire[DecisionTableProcessor]
  readSide.register(tableIndexProcessor)

  lazy val pricingRepo      = wire[PricingRepo]
  lazy val capabilitiesRepo = wire[CapabilitiesRepository]
  lazy val tablesRepo       = wire[DecisionTableRepository]

  lazy val dmnTableCache = wire[DMNTableCache]

  override lazy val jsonSerializerRegistry       = PriceSerializerRegistry
  override val httpFilters: Seq[EssentialFilter] = Seq(corsFilter)

  override lazy val lagomServer = serverFor[PriceService](wire[PriceServiceImpl])

  persistentEntityRegistry.register(wire[CapabilitiesEntity])
  persistentEntityRegistry.register(wire[PricingEntity])
  persistentEntityRegistry.register(wire[DecisionTableEntity])

}
