package de.fellows.app.price.impl.camunda

import de.fellows.app.price.impl.calculator.StackrateVariableContext
import de.fellows.app.price.impl.capability.DMNCapabilityCheckResult
import de.fellows.app.price.impl.dmn.OptionalVariable
import de.fellows.utils.logging.StackrateLogging
import org.camunda.bpm.dmn.engine.delegate.{
  DmnDecisionEvaluationEvent,
  DmnDecisionEvaluationListener,
  DmnDecisionTableEvaluationEvent,
  DmnDecisionTableEvaluationListener
}
import org.camunda.bpm.dmn.engine.impl.{DmnDecisionTableImpl, DmnDecisionTableRuleImpl}
import org.camunda.bpm.dmn.engine.{DmnDecisionResult, DmnDecisionTableResult, DmnEngine}
import org.camunda.bpm.model.dmn.DmnModelInstance

import java.util.concurrent.Executors
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success, Try}
import de.fellows.app.price.api.CapabilitiesApi.CapabilityCheckFailure
import de.fellows.app.price.api.CapabilitiesApi.StackratePricingFailedField
import de.fellows.app.price.impl.calculator.Calculator

class StackrateDmnEngine(val underlying: DmnEngine, val conf: StackrateDmnEngineConfiguration)
    extends StackrateLogging {

  private val executor                                    = Executors.newWorkStealingPool(10)
  implicit val executionContext: ExecutionContextExecutor = ExecutionContext.fromExecutor(executor)

  class TableDecisionListener(decisionKey: String) extends DmnDecisionTableEvaluationListener {
    var result = Seq.newBuilder[DmnDecisionTableEvaluationEvent]

    override def notify(evaluationEvent: DmnDecisionTableEvaluationEvent): Unit =
      // we only have one engine, but multiple listeners. We need to filter the events for the table this listener is interested in
      if (evaluationEvent.getDecisionTable.getKey == decisionKey) {
        this.result += evaluationEvent
      }

    def getResult: Seq[DmnDecisionTableEvaluationEvent] = result.result()
  }
  class DecisionListener(decisionKey: String) extends DmnDecisionEvaluationListener {
    var result = Seq.newBuilder[DmnDecisionEvaluationEvent]

    override def notify(evaluationEvent: DmnDecisionEvaluationEvent): Unit =
      if (evaluationEvent.getDecisionResult.getDecision.getKey == decisionKey) {
        this.result += evaluationEvent
      }

    def getResult: Seq[DmnDecisionEvaluationEvent] = result.result()
  }

  def evaluateDecisionWithRules(
      str: String,
      model: DmnModelInstance,
      varContext: StackrateVariableContext
  ): Future[Try[(DmnDecisionResult, Seq[DmnDecisionEvaluationEvent])]] =
    Future {
      Try {
        val listener = new DecisionListener(str)
        try {
          conf.addDecisionEvaluationListener(listener)
          (underlying.evaluateDecision(str, model, varContext) -> listener.getResult)
        } finally
          conf.removeDecisionEvaluationListener(listener)
      }
    }

  def evaluateDecisionTableWithRules(
      str: String,
      model: DmnModelInstance,
      varContext: StackrateVariableContext
  ): Future[Try[(DmnDecisionTableResult, Seq[DmnDecisionTableEvaluationEvent])]] =
    Future {
      Try {
        val listener = new TableDecisionListener(str)
        try {
          conf.addDecisionTableEvaluationListener(listener)
          (underlying.evaluateDecisionTable(str, model, varContext) -> listener.getResult)
        } finally
          conf.removeDecisionTableEvaluationListener(listener)
      }
    }
}

object StackrateDmnEngine extends StackrateLogging {
  def getResultSummary(check: Seq[Try[DMNCapabilityCheckResult]]): Seq[CapabilityCheckFailure] =
    check
      .flatMap {
        case Failure(x) =>
          logger.error(s"failed to execute capability check", x)
          Seq.empty

        case Success(value) =>
          val table  = value.instance
          val events = value.events

          events.flatMap { ev =>
            val logic: DmnDecisionTableImpl = ev.getDecisionTable.getDecisionLogic.asInstanceOf[DmnDecisionTableImpl]
            val rules                       = logic.getRules.asScala

            ev.getMatchingRules.asScala.flatMap { rule =>
              val ruleLogic: Option[DmnDecisionTableRuleImpl] = rules.find(_.id == rule.getId)
              ruleLogic.toSeq.map { rl =>
                val inputs = ev.getInputs.asScala
                val cond   = rl.getConditions.asScala

                val hints = cond.zipWithIndex.map { exWithIndex =>
                  val (ex, index) = exWithIndex
                  val input       = inputs.toSeq(index)
                  val inputLogic  = logic.getInputs.asScala.find(_.id == input.getId)

                  val variable = inputLogic.map(_.getExpression.getExpression match {
                    case OptionalVariable(variable) => variable
                    case x                          => x
                  })

                  DmnRuleFailureHint(
                    input.getName,
                    variable.getOrElse(input.getName),
                    input.getValue,
                    ex.getExpression
                  )
                }

                DmnFailureHint(rule.getOutputEntries.asScala.toMap, hints.toSeq)
              }
            }
          }
      }
      .map { h =>
        val outputvalues = h.outputs.map { x =>
          x._1 -> Option(x._2.getValue).flatMap(s => Option(s.getValue))
        }

        val varFailures = h.hints.map { hint =>
          val variableName = Calculator.getPropertyNameFromVariable(hint.variable)
          StackratePricingFailedField(
            variableName = variableName.getOrElse(hint.name),
            variable = hint.variable,
            value = Option(hint.value.getValue).map(_.toString).getOrElse(""),
            expression = Option(hint.expression)
          )
        }

        CapabilityCheckFailure(
          hint = outputvalues.get("info").flatten.map(_.toString),
          variableFailures = varFailures
        )
      }
}
