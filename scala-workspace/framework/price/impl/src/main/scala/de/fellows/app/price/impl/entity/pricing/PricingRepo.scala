package de.fellows.app.price.impl.entity.pricing

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.utils.communication.ServiceDefinition

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class PricingRepo(session: CassandraSession)(implicit ec: ExecutionContext) {

  def getPricing(team: String, name: String): Future[Option[UUID]] =
    session.selectOne(
      """
        |SELECT * FROM pricings WHERE team = ? AND name = ?
        |""".stripMargin,
      team,
      name
    )
      .map(_.map(r => r.getUUID("id")))

  def getPricingsForTeam(team: String): Future[Seq[(UUID, String)]] =
    session.selectAll(
      """
        |SELECT * FROM pricings WHERE team = ?
        |""".stripMargin,
      team
    )
      .map(_.map(r => (r.getUUID("id"), r.getString("name"))))

}

class PricingProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit ec: ExecutionContext)
    extends ReadSideProcessor[PricingEvent] {

  override def aggregateTags: Set[AggregateEventTag[PricingEvent]] = PricingEvent.Tag.allTags

  def setDeployment(e: Deployment): Future[Seq[BoundStatement]] =
    Future.successful(
      (if (e.oldstate.map(_.name).exists(n => n != e.name)) {
         // name changed
         List(
           stmtDelete.bind(e.team, e.oldstate.get.name)
         )
       } else {
         List()
       }) :+ stmtUpdate.bind(
        e.id,
        e.team,
        e.name
      )
    )

  def deleteDeployment(event: DeploymentDeleted): Future[Seq[BoundStatement]] =
    Future.successful(List(
      stmtDelete.bind(event.d.team, event.d.name)
    ))

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[PricingEvent] =
    readSide.builder[PricingEvent]("pricelistEventOffset-v1.0")
      .setGlobalPrepare(createTables _)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[Deployment](e => setDeployment(e.event))
      .setEventHandler[DeploymentDeleted](e => deleteDeployment(e.event))
      .build()

  var stmtUpdate: PreparedStatement = _
  var stmtDelete: PreparedStatement = _

  private def prepareStatements() =
    for {
      setList <- session.prepare(
        """
          | UPDATE pricings SET ID = ? WHERE team = ? AND name =?
        """.stripMargin
      )
      delete <- session.prepare(
        """
          | DELETE FROM pricings WHERE team = ? AND name =?
        """.stripMargin
      )

    } yield {
      stmtUpdate = setList
      stmtDelete = delete
      Done
    }

  private def createTables() =
    for {
      _ <- session.executeCreateTable(
        """
          |CREATE TABLE IF NOT EXISTS pricings (
          |            id uuid,
          |            name text,
          |            team text,
          |            PRIMARY KEY (team, name)
          |);
        """.stripMargin
      )
    } yield Done
}
