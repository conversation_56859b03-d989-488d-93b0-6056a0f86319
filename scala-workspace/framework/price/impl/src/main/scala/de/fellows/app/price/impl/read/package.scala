package de.fellows.app.price.impl

import de.fellows.app.price.api.{DecisionTable, DecisionTableVersion, Input, Output, Rule}
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

package object read {

  case class DecisionTableVersionDTO(table: DecisionTableDTO, created: Instant, creator: String) {
    def to: DecisionTableVersion =
      DecisionTableVersion(
        table = table.to,
        created = created,
        creator = creator
      )
  }

  case class DecisionTableDTO(
      team: Option[String],
      name: String,
      inputs: Seq[Input],
      outputs: Seq[Output],
      rules: Seq[Rule],
      hitpolicy: Option[String]
  ) {
    def to: DecisionTable =
      DecisionTable(
        team = team,
        name = name,
        inputs = inputs,
        outputs = outputs,
        rules = rules,
        hitpolicy = hitpolicy
      )
  }

  object DecisionTableDTO {
    implicit val format: Format[DecisionTableDTO] = Json.format[DecisionTableDTO]

    def from(t: DecisionTable): DecisionTableDTO = new DecisionTableDTO(
      team = t.team,
      name = t.name,
      inputs = t.inputs,
      outputs = t.outputs,
      rules = t.rules,
      hitpolicy = t.hitpolicy
    )

  }

  object DecisionTableVersionDTO {
    implicit val format: Format[DecisionTableVersionDTO] = Json.format[DecisionTableVersionDTO]

    def from(v: DecisionTableVersion): DecisionTableVersionDTO = new DecisionTableVersionDTO(
      table = DecisionTableDTO.from(
        v.table
      ),
      created = v.created,
      creator = v.creator
    )

  }

  case class CapabilitiesDeploymentDTO(
      team: String,
      supplier: UUID,
      capabilities: Seq[TableDTO]
  )
  case class TableDTO(
      key: String,
      index: Int
  )

}
