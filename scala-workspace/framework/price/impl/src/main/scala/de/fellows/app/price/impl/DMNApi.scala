package de.fellows.app.price.impl

import de.fellows.app.camunda.api.Variable
import de.fellows.app.price.impl.calculator.StackrateVariableContext
import de.fellows.app.price.impl.entity.pricing.DecisionDeployment
import de.fellows.app.quotation.PriceDetail
import de.fellows.app.supplier
import de.fellows.app.supplier.Capability
import de.fellows.utils.meta._
import org.camunda.bpm.engine.variable.value.TypedValue
import play.api.libs.json.{Format, Json}
import de.fellows.app.price.api.CapabilitiesApi.StackratePricingFailedField

object DMNApi {

  case class EvaluatedPriceDecision(
      state: String,
      prices: Map[DecisionDeployment, Seq[PriceDetail]],
      message: Option[String],
      variableFailures: Seq[StackratePricingFailedField],
      hint: Seq[String]
  )

  case class EvaluatedPricing(state: String, prices: Map[DecisionDeployment, Seq[PriceDetail]], message: Option[String])

  case class OrderParameter(
      quantity: Int,
      deliveryTime: Int
  )

  case class PanelParameter(
      customerPanels: Int,
      customerBoards: Int,
      boardsOnCustomerPanel: Option[Int],
      panelYield: Double
  )

  object EvaluatedPriceDecision {
    val STATE_OK    = "ok"
    val STATE_ERROR = "error"
    val STATE_BREAK = "break"

    def success(prices: Map[DecisionDeployment, Seq[PriceDetail]]): EvaluatedPriceDecision =
      EvaluatedPriceDecision(
        state = STATE_OK,
        prices = prices,
        message = None,
        variableFailures = Seq.empty,
        hint = Seq.empty
      )

    def priceBreak(
        message: String,
        variableFailures: Seq[StackratePricingFailedField],
        hint: Seq[String]
    ): EvaluatedPriceDecision =
      EvaluatedPriceDecision(
        state = STATE_BREAK,
        prices = Map.empty,
        message = Some(message),
        variableFailures = variableFailures,
        hint = hint
      )

    def error(message: String): EvaluatedPriceDecision =
      EvaluatedPriceDecision(
        state = STATE_ERROR,
        prices = Map.empty,
        message = Some(message),
        variableFailures = Seq.empty,
        hint = Seq.empty
      )
  }

  object OrderParameter {
    implicit val format: Format[OrderParameter] = Json.format
  }

  object PanelParameter {
    implicit val format: Format[PanelParameter] = Json.format
  }

  def toVariable(p: Capability, prefix: String): Map[String, TypedValue] =
    p match {
      case supplier.NumericalCapability(name, min, max, l) => Seq(
          min.map(StackrateVariableContext.typed).map(s"${prefix}_${name}_min" -> _),
          max.map(StackrateVariableContext.typed).map(s"${prefix}_${name}_max" -> _)
        ).flatten.toMap
      //      case supplier.ListCapability(name, allowed, forbidden) =>Seq(
      //        allowed.map(Variable.apply).map(s"${prefix}_${name}_min" -> _),
      //        forbidden.map(Variable.apply).map(s"${prefix}_${name}_max" -> _),
      //      ).flatten.toMap
      case supplier.BooleanCapability(name, allowed, l) => Seq(
          s"${prefix}_${name}" -> StackrateVariableContext.typed(allowed)
        ).toMap
      case supplier.StringCapability(name, allowed, forbidden, l) => Seq(
          allowed.map(StackrateVariableContext.typed).map(s"${prefix}_${name}"             -> _),
          forbidden.map(StackrateVariableContext.typed).map(s"${prefix}_${name}_forbidden" -> _)
        ).flatten.toMap

      case _ => Map()
    }

  def toVariable(p: Property): Option[Variable] =
    p match {
      case DecimalProperty(_, value)         => Some(Variable.apply(value))
      case BooleanProperty(_, value)         => Some(Variable.apply(value))
      case StringProperty(_, value)          => Some(Variable.apply(value))
      case ListProperty(_, _)                => None // TODO
      case ObjectProperty(_, _)              => None // TODO
      case PropertyWithLabel(name, _, value) => toVariable(value.rename(name))
      case PropertyWithUOM(name, _, value)   => toVariable(value.rename(name))
    }

  def toTypedValue(p: Property): Option[TypedValue] =
    p match {
      case DecimalProperty(_, value)         => Some(StackrateVariableContext.typed(value))
      case BooleanProperty(_, value)         => Some(StackrateVariableContext.typed((value)))
      case StringProperty(_, value)          => Some(StackrateVariableContext.typed((value)))
      case ListProperty(_, _)                => None // TODO
      case ObjectProperty(_, _)              => None // TODO
      case PropertyWithLabel(name, _, value) => toTypedValue(value.rename(name))
      case PropertyWithUOM(name, _, value)   => toTypedValue(value.rename(name))
    }
}
