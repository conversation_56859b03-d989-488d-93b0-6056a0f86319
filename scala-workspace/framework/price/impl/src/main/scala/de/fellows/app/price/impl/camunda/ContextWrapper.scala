package de.fellows.app.price.impl.camunda

import org.camunda.feel.impl.interpreter.EvalContext
import org.camunda.feel.syntaxtree.{Val, ValError}

class VariableMissingException(val variable: String) extends Exception

/** Wrapping the default [[EvalContext]] to throw exceptions when variables are missing. For some reason the default behaviour is to always suppress these errors
  * @param underlying
  */
class ContextWrapper(underlying: EvalContext)
    extends EvalContext(underlying.valueMapper, underlying.variableProvider, underlying.functionProvider) {}
