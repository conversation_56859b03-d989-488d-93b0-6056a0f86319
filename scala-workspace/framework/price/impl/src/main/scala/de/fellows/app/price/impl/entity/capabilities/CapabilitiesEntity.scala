package de.fellows.app.price.impl.entity.capabilities

import akka.Done
import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.app.price.impl.entity.pricing.PricingDeployment
import de.fellows.utils.common.EntityException
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity

import java.util.UUID

class CapabilitiesEntity(implicit override val service: ServiceDefinition)
    extends SecureTeamEntity[Option[CapabilitiesDeployment]] {

  override type Command = CapabilitiesCommand
  override type Event   = CapabilitiesEvent

  override def initialState: Option[CapabilitiesDeployment] = None

  override def isAllowed(c: CapabilitiesCommand, s: Option[CapabilitiesDeployment]): Boolean =
    allowed(s, c.team, c.supplier)

  private def allowed(s: Option[CapabilitiesDeployment], team: String, supplier: UUID): Boolean =
    s.isEmpty ||
      (s.map(_.team).contains(team) && s.map(_.supplier).contains(supplier))

  override def entityBehavior(state: Option[CapabilitiesDeployment]): Actions =
    Actions()
      .onReadOnlyCommand[GetDeployedCapabilities, CapabilitiesDeploymentResponse] {
        case (x: GetDeployedCapabilities, ctx, s) =>
          ctx.reply(CapabilitiesDeploymentResponse(s))
      }
      .onCommand[DeployCapabilities, CapabilitiesDeployment] {
        case (x: DeployCapabilities, ctx, s) =>
          val state = CapabilitiesDeployment(x.team, x.supplier, x.d)

          ctx.thenPersist(
            CapabilityDeployment(
              team = state.team,
              supplier = state.supplier,
              deployment = state.capabilities
            )
          )(_ => ctx.reply(state))
      }
      .onCommand[ChangeCapabilityDeployment, CapabilitiesDeployment] {
        case (x: ChangeCapabilityDeployment, ctx, s) =>
          if (s.isEmpty) {
            ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Deployment not found"))
            ctx.done
          } else {
            val thisState = s.get
            val updated = thisState.copy(
              capabilities = x.d.getOrElse(thisState.capabilities)
            )

            ctx.thenPersist(
              CapabilityDeployment(
                team = updated.team,
                supplier = updated.supplier,
                deployment = updated.capabilities
              )
            )(_ => ctx.reply(updated))
          }
      }
      .onCommand[DeleteDeployedCapabilities, Done] {
        case (x: DeleteDeployedCapabilities, ctx, s) =>
          if (s.isEmpty) {
            ctx.commandFailed(EntityException(TransportErrorCode.NotFound, "Deployment not found"))
            ctx.done
          } else {
            ctx.thenPersist(
              CapabilityDeploymentDeleted(
                team = s.get.team,
                supplier = s.get.supplier
              )
            )(_ => ctx.reply(Done))
          }
      }
      .onEvent {
        case (CapabilityDeployment(team, supplier, deployment), _) =>
          Some(CapabilitiesDeployment(team, supplier, deployment))

        case (CapabilityDeploymentDeleted(team, supplier), _) =>
          None
      }
}

object CapabilitiesEntity {
  def id(team: String, supplier: UUID): String = s"capabilities-$team-$supplier"
}
