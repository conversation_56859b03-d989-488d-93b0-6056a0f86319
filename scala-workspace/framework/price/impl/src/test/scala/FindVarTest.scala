import de.fellows.app.price.impl.PriceServiceImpl
import org.camunda.bpm.model.dmn.instance._
import org.camunda.bpm.model.dmn.{ BuiltinAggregator, Dmn, HitPolicy }

object FindVarTest extends App {
  def findVars(value: String): Seq[String] = {
    val functionNames = Set("floor", "string")
    value.replaceAll("\"[^\"]*\"", "")
      .split("[ ()]")
      .filter(part => part.nonEmpty && part.matches("[a-zA-Z_]+[\\w]*") && !(functionNames contains part))
  }

//  !part.matches("[[:alpha:]][[:alnum:]_-]*")

  def test(value: String): Unit = {
    System.out.println("----------------------------------")
    System.out.println(value)
    findVars(value).foreach(x => System.out.println(x))
  }

  test("\"Anzahl Pakete: \" + floor( 1.7 / layerstack_targetheight )")
  test("1.23 + 0.000122 * ( pcb_ph_count   *  order_quantity ) / floor ( 1.7 / layerstack_targetheight )")
  test("] 0.25 .. 0.35 ] ")
  test("\"Materialpreis <h1>area</h1> = \" + Math.floor(layerstack_unit_price)")
  test("layerstack_unit_price")
  test("\"pcb_ph_count = \" + string ( pcb_ph_count )")
  test("\"less than two\"")
  test("5")
  test("2 * order_quantity * pcb_area / 1000")
  test("\"Less than 100 vias (\" + pcb_ph_count + \" vias)\"")
  test("order_quantity * pcb_ph_count * 0.005 * pcb_ph_tool_count")
  test("order_quantity * 1.35")
  test("\"panels \" + string(order_working_panels) + \" boards on cp: \"+ string(panel_boards_on_customer_panel) ")
  test("test")
  test("pcb_cucount * order_working_panels")
  test("string(floor(pcb_cucount))")

}
