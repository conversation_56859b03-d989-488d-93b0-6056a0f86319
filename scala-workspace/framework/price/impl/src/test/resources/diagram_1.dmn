<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="https://www.omg.org/spec/DMN/20191111/MODEL/" xmlns:dmndi="https://www.omg.org/spec/DMN/20191111/DMNDI/" xmlns:dc="http://www.omg.org/spec/DMN/20180521/DC/" xmlns:camunda="http://camunda.org/schema/1.0/dmn" id="Definitions_1ck6r8p" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="4.11.1">
  <decision id="Decision_18fcrsz" name="Decision 1">
    <decisionTable id="DecisionTable_09xzofm">
      <input id="Input_1" camunda:inputVariable="in">
        <inputExpression id="InputExpression_1" typeRef="integer" expressionLanguage="feel">
          <text></text>
        </inputExpression>
      </input>
      <output id="Output_1" name="out" typeRef="string" />
      <rule id="DecisionRule_15aq4nl">
        <inputEntry id="UnaryTests_1czcr64" expressionLanguage="feel">
          <text>&gt; 5</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0z594qc">
          <text>"Yes"</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0euz5u0">
        <inputEntry id="UnaryTests_059urq8" expressionLanguage="feel">
          <text>&lt;=5</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_16usoly">
          <text>"No"</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
  <dmndi:DMNDI>
    <dmndi:DMNDiagram>
      <dmndi:DMNShape dmnElementRef="Decision_18fcrsz">
        <dc:Bounds height="80" width="180" x="160" y="100" />
      </dmndi:DMNShape>
    </dmndi:DMNDiagram>
  </dmndi:DMNDI>
</definitions>
