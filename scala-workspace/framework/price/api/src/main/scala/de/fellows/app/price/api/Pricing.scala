package de.fellows.app.price.api

import de.fellows.app.quotation.CalculatedPriceTable
import de.fellows.utils.{CurrencyCode, JsonFormats}
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json._

import java.time.Instant
import java.util.UUID
import de.fellows.app.price.api.CapabilitiesApi.StackratePricingFailedField

case class PricingDescriptor(
    name: String,
    supplier: UUID,
    id: Option[UUID],
    tables: Option[Seq[DeployedDecision]],
    enableApi: Option[Boolean],
    currency: Option[CurrencyCode]
)

case class PricingDeploymentDescriptor(
    name: String,
    id: Option[UUID],
    supplier: UUID,
    enableApi: Option[Boolean],
    currency: Option[CurrencyCode],
    tables: Seq[DecisionTable]
)

case class CapabilitiesDeployment(
    supplier: UUID,
    tables: Seq[DecisionTable]
)

case class CapabilitiesDeploymentDescriptor(
    supplier: UUID,
    tables: Seq[DeployedDecision]
)

object CapabilitiesDeploymentDescriptor {
  implicit val format: Format[CapabilitiesDeploymentDescriptor] = Json.format
}

object CapabilitiesDeployment {
  implicit val format: Format[CapabilitiesDeployment] = Json.format
}

case class Pricing(
    name: String,
    id: Option[UUID],
    supplier: UUID,
    tables: Seq[VersionedDecisionTable],
    enableApi: Boolean,
    currency: Option[CurrencyCode]
)

case class Capabilities(
    supplier: UUID,
    tables: Seq[VersionedDecisionTable]
)

case class DeployedVersion(id: String, version: Int, time: Option[Instant])

@Deprecated
case class DeploymentInfo(
    deployedID: String,
    version: Int,
    versionTag: Option[String],
    time: Option[Instant],
    allVersions: Option[Seq[DeployedVersion]]
)

case class DecisionTable(
    team: Option[String],
    name: String,
    inputs: Seq[Input],
    outputs: Seq[Output],
    rules: Seq[Rule],
    hitpolicy: Option[String]
)

case class VersionedDecisionTable(
    team: String,
    currentVersion: Int,
    name: String,
    id: String,
    versions: Seq[DecisionTableVersion]
) {
  def getCurrentVersion: DecisionTableVersion = versions(currentVersion)
}

case class DecisionTableVersion(table: DecisionTable, created: Instant, creator: String)

case class Input(label: String, name: String, varType: String)

case class Output(label: String, name: String, varType: String)

case class Rule(inputEntries: Seq[InputEntry], outputEntries: Seq[OutputEntry])

case class InputEntry(name: String, value: String)

case class OutputEntry(name: String, value: String)

case class CalculatedPrices(request: PriceRequest, pricings: Seq[PricingResult])

sealed trait PricingResult {
  val state: String
  val supplier: UUID
}

case class CalculatedPricing(
    name: String,
    supplier: UUID,
    tables: Seq[CalculatedPriceTable],
    price: BigDecimal,
    unitPrice: BigDecimal,
    sumDetails: DetailedSum,
    currency: CurrencyCode,
    override val state: String = PricingResult.STATE_OK
) extends PricingResult {}

case class PricingError(
    name: String,
    supplier: UUID,
    message: String,
    override val state: String = PricingResult.STATE_ERROR
) extends PricingResult {}

case class PricingBreak(
    name: String,
    supplier: UUID,
    message: String,
    variableFailures: Seq[StackratePricingFailedField],
    hint: Seq[String],
    override val state: String = PricingResult.STATE_BREAK
) extends PricingResult {}

case class DetailedSum(flexSum: BigDecimal, fixSum: BigDecimal, nreSum: Option[BigDecimal])

object DecisionTableVersion {
  implicit val format: Format[DecisionTableVersion] = Json.format[DecisionTableVersion]
}

object VersionedDecisionTable {
  implicit val format: Format[VersionedDecisionTable] = Json.format[VersionedDecisionTable]
}

object Capabilities {
  implicit val format: Format[Capabilities] = Json.format
}

object PricingDescriptor {
  implicit val format: Format[PricingDescriptor] = Json.format
}

object DeployedVersion {
  implicit val f: Format[DeployedVersion] = Json.format[DeployedVersion]
}

object DeploymentInfo {
  implicit val format: Format[DeploymentInfo] = Json.format
}

object PricingDeploymentDescriptor {
  implicit val format: Format[PricingDeploymentDescriptor] = Json.format
}

object Pricing {
  implicit val format: Format[Pricing] = Json.format
}

object DecisionTable {
  implicit val format: Format[DecisionTable] = Json.format
}

object Input {
  implicit val format: Format[Input] = Json.format
}

object Output {
  implicit val format: Format[Output] = Json.format
}

object Rule {
  implicit val format: Format[Rule] = Json.format
}

object InputEntry {
  implicit val format: Format[InputEntry] = Json.format
}

object OutputEntry {
  implicit val format: Format[OutputEntry] = Json.format
}

object DetailedSum {
  implicit val format: Format[DetailedSum] = Json.format
}

object CalculatedPricing {
  implicit val format: Format[CalculatedPricing] = Json.format
}

object PricingError {
  implicit val format: Format[PricingError] = Json.format
}

object PricingBreak {
  implicit val format: Format[PricingBreak] = Json.format
}

object PricingResult {
  val STATE_OK    = "ok"
  val STATE_ERROR = "error"
  val STATE_BREAK = "break"

  def unapply(foo: PricingResult): JsValue =
    foo match {
      case x: CalculatedPricing => Json.toJson(x)
      case x: PricingError      => Json.toJson(x)
      case x: PricingBreak      => Json.toJson(x)
    }

  def apply(data: JsValue): PricingResult =
    data \ "state" match {
      case JsDefined(JsString(value)) if value == PricingResult.STATE_OK    => data.as[CalculatedPricing]
      case JsDefined(JsString(value)) if value == PricingResult.STATE_ERROR => data.as[PricingError]
      case JsDefined(JsString(value)) if value == PricingResult.STATE_BREAK => data.as[PricingBreak]
      case _ => throw new IllegalStateException("unknown state")
    }

  implicit val cfg: JsonConfiguration.Aux[MacroOptions] = JsonFormats.JsonSealedTraitConfig
  implicit val format: Format[PricingResult]            = Json.format

}

object CalculatedPrices {
  implicit val format: Format[CalculatedPrices] = Json.format
}
