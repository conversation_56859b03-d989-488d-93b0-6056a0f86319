// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
package de.fellows.app.price.api

import akka.{Done, NotUsed}
import com.lightbend.lagom.scaladsl.api.Service._
import com.lightbend.lagom.scaladsl.api.transport.Method
import com.lightbend.lagom.scaladsl.api.{Descriptor, Service, ServiceAcl, ServiceCall}
import de.fellows.app.price.api
import de.fellows.app.price.api.CapabilitiesApi.{CapabilityCheckRequest, CapabilityCheckResult}
import de.fellows.ems.pcb.api.PCBV2Api.PCBV2
import de.fellows.utils.apidoc.StackrateApi
import de.fellows.utils.communication.ServiceExceptionSerializer
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.{OpenAPIDefinition, Operation}
import de.fellows.ems.pcb.api.specification.units.UnitConversions.implicits._
import java.util.UUID

@OpenAPIDefinition(
  info = new Info(
    version = "0.0.1",
    title = "Stackrate Price API"
  )
)
abstract class PriceService extends StackrateServiceAPI with Service {
  val basePath         = "/api/price"
  val internalBasePath = "/internal/price/teams/:team"

  @Operation(
    summary = "Calculate Prices",
    description =
      """Calls the price calculation and synchronously returns the calculation result
        |
        |Prices are calculated using the given assembly information, and for each deployed pricing.
        |The prices are ephemeral. To persist them a quotation can be used
        |"""
  )
  @StackrateApi
  @Tag(name = "Calculation")
  def calculatePrices(
      assembly: UUID,
      version: UUID,
      specification: UUID,
      panel: Option[String]
  ): ServiceCall[PriceRequest, CalculatedPrices]

  @Operation(
    summary = "Calculate Prices for a shared Assembly",
    description =
      """Similar to the `Calculate Prices` call, but uses a shared assembly instead of a direct assembly
        |
        |"""
  )
  @StackrateApi
  @Tag(name = "Calculation")
  def calculatePricesForShare(
      share: UUID,
      specification: UUID,
      panel: Option[String]
  ): ServiceCall[ExtendedPriceRequest, CalculatedPrices]
  def _calculatePricesForShare(
      team: String,
      share: UUID,
      specification: UUID,
      panel: Option[String]
  ): ServiceCall[FullPriceRequest, Seq[CalculatedPrices]]

  @Operation(
    summary = "Calculate Prices without Assembly",
    description =
      """Calls the price calculation and synchronously returns the calculation result
        |
        |Calls the same calculations as the `Calculate Prices` call, but does not extract the necessary information.
        |Those have to be provided by the client
        |"""
  )
  @StackrateApi
  @Tag(name = "Calculation")
  def calculateApiPrices(): ServiceCall[ExtendedPriceRequest, CalculatedPrices]

  def migrateFromCamunda(): ServiceCall[NotUsed, Done]

  //  @StackrateApi
  //  @Tag(name = "Calculation")
  //  @Operation(
  //    summary = "Calculate Form",
  //    description =
  //      """Calls the price calculation and synchronously returns the calculation result
  //        |
  //        |As `calculatePrices`, the calculation is called for each deployed pricing.
  //        |This call however has no assembly info, and all relevant information must be provided
  //        |
  //        |The prices are ephemeral. To persist them a quotation can be used
  //        |""",
  //  )
  //  def calculateForm(): ServiceCall[CalculateForm, ApproxPriceInfos]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Get Deployed Pricing",
    description =
      """Gets a deployed Pricing. accepts its name or UUID.
        |
        |Contains a number of Tables
        |"""
  )
  def getDeployedPricing(nameOrID: String): ServiceCall[NotUsed, Pricing]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Export Deployed Pricing",
    description =
      """Gets an export of a deployed Pricing. accepts its name or UUID.
        |
        |Contains a number of Tables
        |"""
  )
  def exportDeployedPricing(nameOrID: String): ServiceCall[NotUsed, PricingDeploymentDescriptor]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Get Pricing Variables",
    description =
      """Gets all variables for a pricing table. accepts its name or UUID.
        |
        |
        |"""
  )
  def getPricingVariables(nameOrID: String): ServiceCall[NotUsed, Seq[PricingVariable]]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Delete Deployed Pricing",
    description =
      """Deletes a deployed Pricing and all its versions.
        |"""
  )
  def deleteDeployedPricing(nameOrID: String): ServiceCall[NotUsed, Done]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Get Deployed Table in Specific Version",
    description =
      """Gets a deployed Pricing Table in a specific version
        |
        |Contains one table that is part of the given pricing
        |"""
  )
  def getDeployedTableVersion(nameOrID: String, table: String, version: Int): ServiceCall[NotUsed, DecisionTable]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Get Deployed Table",
    description =
      """Gets a deployed Pricing Table
        |
        |Contains one table that is part of the given pricing
        |"""
  )
  def getDeployedTableCurrentVersion(nameOrID: String, table: String): ServiceCall[NotUsed, DecisionTable]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Delete Deployed Table",
    description =
      """Deletes a deployed Pricing Table
        |
        |Deletes the table, and all associated versions and deployments from camunda
        |"""
  )
  def deleteDeployedTable(nameOrID: String, table: String): ServiceCall[NotUsed, Pricing]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Set Deployed Table Version",
    description =
      """Sets the version of the table that should be used for the price calculcation
        |
        |"""
  )
  def setDeployedTableVersion(
      nameOrID: String,
      table: String,
      version: Int
  ): ServiceCall[NotUsed, VersionedDecisionTable]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Create or Update Pricing (Deploy)",
    description =
      """Deploys a pricing
        |
        |If the IDs/names exist, this will update the pricing. Otherwise it will be created as a new pricing.
        |"""
  )
  def deployPricing(): ServiceCall[PricingDeploymentDescriptor, Pricing]
  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Change the name of a Pricing",
    description =
      """Changes the name of a pricing
        |
        |this requires the pricing by id, not name
        |"""
  )
  def changePricingName(id: UUID): ServiceCall[String, Pricing]

  def addTable(pricing: UUID): ServiceCall[DecisionTable, Pricing]

  def deployTable(pricing: UUID, table: String): ServiceCall[DecisionTable, VersionedDecisionTable]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Change Pricing",
    description =
      """Changes a pricing without deployment
        |
        |Can be used to change the name or supplier
        |"""
  )
  def changePricing(nameOrID: String): ServiceCall[PricingDescriptor, DeployedPricing]

  @StackrateApi
  @Tag(name = "Pricing")
  @Operation(
    summary = "Get Pricings",
    description =
      """Get all pricings for the current team
        |"""
  )
  def getPricingsForTeam(): ServiceCall[NotUsed, Seq[DeployedPricing]]
  def _getPricingsForTeam(team: String): ServiceCall[NotUsed, Seq[DeployedPricing]]

  // Used to import prices in garden task
  def _createPricingForTeam(team: String): ServiceCall[PricingDeploymentDescriptor, Pricing]

  def getCapabilitiesForSupplier(supplier: UUID): ServiceCall[NotUsed, DeployedCapabilities]
  def _getCapabilitiesForSupplier(team: String, supplier: UUID): ServiceCall[NotUsed, DeployedCapabilities]
  def getCapabilitiesForTeam(): ServiceCall[NotUsed, Seq[DeployedCapabilities]]
  def exportDeployedCapabilities(supplier: UUID): ServiceCall[NotUsed, CapabilitiesDeployment]
  def deployCapabilitiesForSupplier(supplier: UUID): ServiceCall[CapabilitiesDeployment, DeployedCapabilities]
  def _deployCapabilitiesForSupplier(
      team: String,
      supplier: UUID
  ): ServiceCall[CapabilitiesDeployment, DeployedCapabilities]
  def changeCapabilitiesForSupplier(supplier: UUID): ServiceCall[CapabilitiesDeploymentDescriptor, DeployedCapabilities]
  def checkCapabilitiesForSupplier(): ServiceCall[PCBV2, Seq[CapabilityCheckResult]]
  def _checkCapabilitiesForSupplier(team: String): ServiceCall[CapabilityCheckRequest, Seq[CapabilityCheckResult]]
  def deleteCapabilitiesForSupplier(supplier: UUID): ServiceCall[NotUsed, Done]

  def addCapabilityTable(supplier: UUID): ServiceCall[DecisionTable, Capabilities]
  def deployCapabilityTable(supplier: UUID, table: String): ServiceCall[DecisionTable, VersionedDecisionTable]
  def deleteCapabilityTable(supplier: UUID, table: String): ServiceCall[NotUsed, Done]

  //  @StackrateApi
  //  def validatePricing(): ServiceCall[Pricing, Done]

  override def descriptor: Descriptor =
    withDocumentation(
      named("price")
        .withCalls(
          //        pathCall(s"/_openapi/price/openapi?format", openapi _)
          //          .withResponseSerializer(new ApidocSerializer),

          restCall(
            Method.PUT,
            s"$basePath/prices/assemblies/:assembly/versions/:version/specifications/:specification/panels/:panel/calculate",
            calculatePrices _
          ),
          restCall(
            Method.PUT,
            s"$basePath/prices/assemblies/:assembly/versions/:version/specifications/:specification/calculate?panel",
            calculatePrices _
          ),
          restCall(
            Method.PUT,
            s"$basePath/prices/shares/:share/specifications/:specification/calculate?panel",
            calculatePricesForShare _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/prices/shares/:share/specifications/:specification/calculate?panel",
            _calculatePricesForShare _
          ),
          restCall(
            Method.PUT,
            s"$basePath/capabilitycheck/bypcb",
            checkCapabilitiesForSupplier _
          ),
          restCall(
            Method.PUT,
            s"$internalBasePath/capabilitycheck/bypcb",
            _checkCapabilitiesForSupplier _
          ),
          restCall(Method.POST, s"$basePath/migrate", migrateFromCamunda _),
          restCall(Method.PUT, s"$basePath/prices/calculate", calculateApiPrices _),
          restCall(Method.GET, s"$basePath/capabilities", getCapabilitiesForTeam _),
          restCall(Method.GET, s"$basePath/capabilities/:supplier", getCapabilitiesForSupplier _),
          restCall(Method.GET, s"$basePath/capabilities/:supplier/export", exportDeployedCapabilities _),
          restCall(Method.POST, s"$basePath/capabilities/:supplier", deployCapabilitiesForSupplier _),
          restCall(Method.POST, s"$internalBasePath/capabilities/:supplier", _deployCapabilitiesForSupplier _),
          restCall(Method.GET, s"$internalBasePath/capabilities/:supplier", _getCapabilitiesForSupplier _),
          restCall(Method.PUT, s"$basePath/capabilities/:supplier", changeCapabilitiesForSupplier _),
          restCall(Method.POST, s"$basePath/capabilities/:supplier/tables", addCapabilityTable _),
          restCall(Method.PUT, s"$basePath/capabilities/:supplier/tables/:table", deployCapabilityTable _),
          restCall(Method.DELETE, s"$basePath/capabilities/:supplier/tables/:table", deleteCapabilityTable _),
          restCall(Method.DELETE, s"$basePath/capabilities/:supplier", deleteCapabilitiesForSupplier _),
          restCall(Method.GET, s"$basePath/calculation", getPricingsForTeam _),
          restCall(Method.GET, s"$internalBasePath/calculation", _getPricingsForTeam _),
          restCall(Method.POST, s"$internalBasePath/calculation", _createPricingForTeam _),
          restCall(Method.GET, s"$basePath/calculation/:nameOrID", getDeployedPricing _),
          restCall(Method.GET, s"$basePath/calculation/:nameOrID/export", exportDeployedPricing _),
          restCall(Method.GET, s"$basePath/calculation/:nameOrID/variables", getPricingVariables _),
          restCall(Method.DELETE, s"$basePath/calculation/:nameOrID", deleteDeployedPricing _),
          restCall(
            Method.GET,
            s"$basePath/calculation/:nameOrID/tables/:table/versions/:version",
            getDeployedTableVersion _
          ),
          restCall(Method.GET, s"$basePath/calculation/:nameOrID/tables/:table", getDeployedTableCurrentVersion _),
          restCall(Method.DELETE, s"$basePath/calculation/:nameOrID/tables/:table", deleteDeployedTable _),
          restCall(Method.POST, s"$basePath/calculation", deployPricing _),
          restCall(Method.PUT, s"$basePath/calculation/:id/name", changePricingName _),
          restCall(
            Method.POST,
            s"$basePath/calculation/:nameOrID/tables/:table/versions/:version/use",
            setDeployedTableVersion _
          ),
          restCall(Method.POST, s"$basePath/calculation/:nameOrID/tables/:table", deployTable _),
          restCall(Method.POST, s"$basePath/calculation/:nameOrID/tables", addTable _),
          restCall(Method.PUT, s"$basePath/calculation/:nameOrID", changePricing _)
        )
        .withAcls(
          ServiceAcl(pathRegex = Some(s"$basePath/.*")),
          ServiceAcl(pathRegex = Some(s"/_openapi/price/.*"))
        ).withExceptionSerializer(new ServiceExceptionSerializer())
    )
}
//
