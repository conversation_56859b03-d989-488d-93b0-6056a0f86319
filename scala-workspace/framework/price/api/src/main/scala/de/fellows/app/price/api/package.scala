package de.fellows.app.price

import de.fellows.app.assembly.commons.AbstractAssemblyReference
import de.fellows.ems.panel.api.CustomerPanelHelper
import de.fellows.utils.CurrencyCode
import de.fellows.utils.apidoc.StackrateAPIObject
import de.fellows.utils.meta.MetaInfo
import io.swagger.v3.oas.annotations.Parameter
import play.api.libs.json.{Format, JsObject, Json}

import java.util.UUID
import scala.annotation.meta.field

package object api {

  case class PriceRequest(
      count: Int,
      delivery: Int,
      nre: Option[Boolean],
      assembly: Option[AbstractAssemblyReference],
      specification: Option[UUID],
      panel: Option[String]
  )

  case class FullPriceRequest(
      requests: Seq[ExtendedPriceRequest],
      supplierIds: Seq[UUID]
  )

  object FullPriceRequest {
    implicit val format: Format[FullPriceRequest] = Json.format[FullPriceRequest]
  }

  case class ExtendedPriceRequest(
      request: PriceRequest,
      panel: Option[CustomerPanelHelper],
      pcb: Option[MetaInfo],
      specification: Option[MetaInfo],
      specificationUser: Option[MetaInfo],
      layerstack: Option[MetaInfo],
      customer: Option[String]
  )

  case class DeployedDecision(team: String, key: String)

  case class DeployedPricing(
      name: String,
      id: UUID,
      supplier: UUID,
      pricings: Seq[DeployedDecision],
      enableApi: Boolean,
      currency: Option[CurrencyCode]
  )

  case class DeployedCapabilities(
      supplier: UUID,
      tables: Seq[VersionedDecisionTable]
  )

  object DeployedCapabilities {
    implicit val format: Format[DeployedCapabilities] = Json.format
  }

  case class PricingVariable(
      name: String,
      varType: String,
      label: Option[String] = None,
      min: Option[BigDecimal] = None,
      max: Option[BigDecimal] = None,
      options: Option[Seq[String]] = None
  )

  @StackrateAPIObject(
    title = "Calculation Form Request",
    summary = "Contains the properties with which to calculate a price"
  )
  case class CalculateForm(
      @(Parameter @field)(
        description =
          """The Quantity
                                  |
                                  |The count of customerboards (not panels) that are requested
                                  |"""
      )
      quantity: Int,
      @(Parameter @field)(
        description =
          """The Delivery Time
                                  |
                                  |The delivery time in days
                                  |"""
      )
      delivery: Int,
      @(Parameter @field)(
        description =
          """How many customer boards are on one customer panel ?
                                  |
                                  |If there is no customerpanel, set this to 1
                                  |(as this effectively means 'no panel')
                                  |"""
      )
      boardsOnCustomerPanel: Option[Int],
      @(Parameter @field)(
        description =
          """All requested meta data
                                  |"""
      )
      meta: JsObject,
      @(Parameter @field)(
        description =
          """The customer and working Panel information.
                                  |
                                  |These parameters will be delivered to camunda.
                                  |There are some ways to provide this data (use one of the following)
                                  |
                                  |## `width` and `height`
                                  |
                                  |the dimensions of the customer panel. The best fitting working panel will be selected
                                  |
                                  |*optionally specify `workingPanelID` to force a working panel*
                                  |
                                  |## `customerBoards` and `customerPanels`
                                  |
                                  |how many customer boards are on the working panel, and how many customer panels
                                  |are on the working panel
                                  |
                                  |## `customerPanelId`
                                  |
                                  |the id of the customer panel of an assembly.
                                  |
                                  |"""
      )
      panel: PanelInfo
  )

  case class PanelInfo(
      width: Option[BigDecimal],
      height: Option[BigDecimal],
      workingPanelID: Option[UUID],
      customerBoards: Option[Int],
      customerPanels: Option[Int],
      customerPanelID: Option[UUID]
  )

  case class PriceFilter(panel: Option[String], supplier: Option[String], count: Option[Int])

  object PanelInfo {
    implicit val format: Format[PanelInfo] = Json.format
  }

  object PriceRequest {
    implicit val format: Format[PriceRequest] = Json.format
  }

  object ExtendedPriceRequest {
    implicit val format: Format[ExtendedPriceRequest] = Json.format

    def apply(
        request: PriceRequest,
        panel: Option[CustomerPanelHelper],
        pcb: Option[MetaInfo],
        specification: Option[MetaInfo],
        specificationUser: Option[MetaInfo],
        layerstack: Option[MetaInfo],
        customer: Option[String]
    ): ExtendedPriceRequest =
      new ExtendedPriceRequest(request, panel, pcb, specification, specificationUser, layerstack, customer)

    def apply(request: PriceRequest): ExtendedPriceRequest =
      new ExtendedPriceRequest(request, None, None, None, None, None, None)
  }

  object CalculateForm {
    implicit val format: Format[CalculateForm] = Json.format
  }

  object PriceFilter {
    implicit val format: Format[PriceFilter] = Json.format
  }

  object DeployedDecision {
    implicit val format: Format[DeployedDecision] = Json.format
  }

  object DeployedPricing {
    implicit val format: Format[DeployedPricing] = Json.format
  }

  object PricingVariable {
    implicit val f: Format[PricingVariable] = Json.format[PricingVariable]
  }
}
