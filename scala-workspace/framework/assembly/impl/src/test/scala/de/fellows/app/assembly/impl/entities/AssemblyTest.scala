package de.fellows.app.assembly.impl.entities

import de.fellows.app.assembly.commons.ProjectType
import de.fellows.app.assemby.api.enums.{AssemblyActive, UIStatus}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import java.time.Instant
import java.util.UUID

class AssemblyTest extends AnyFlatSpec {
  val id = UUID.randomUUID()
  val version = Version(
    id,
    Some("1.0.0"),
    Instant.now(),
    None,
    Seq(),
    hints = Seq(),
    projectType = Some(ProjectType.NoFiles)
  )
  val assembly = Assembly(
    eid = id,
    gid = "name",
    name = "description",
    creator = id.toString,
    created = Instant.now(),
    team = "test",
    information = AssemblyInformation(
      customer = None,
      orderId = None,
      assignee = None,
      itemNo = None,
      description = None,
      uiStatus = UIStatus("active"),
      project = None
    ),
    status = AssemblyActive,
    versions = Seq(version),
    currentVersion = Some(version),
    features = Seq(),
    preview = None
  )

  "Automated approval of assembly" should "lock assembly version" in {
    assembly.approve(manually = false).currentVersion.map(_.filesLocked) shouldBe Some(true)
  }
  it should "keep assembly unlocked if it was manually unlocked" in {
    val changedAssembly = assembly.unlock(manually = true).approve(manually = false)
    changedAssembly.currentVersion.map(_.filesLocked) shouldBe Some(false)
    changedAssembly.manuallyUnlocked shouldBe true
  }
  "Manual approval of assembly" should "lock assembly version" in {
    val changedAssembly = assembly.approve(manually = true)
    changedAssembly.currentVersion.map(_.filesLocked) shouldBe Some(true)
    changedAssembly.manuallyUnlocked shouldBe false
  }
  it should "lock assembly version even if it was unlocked manually" in {
    val changedAssembly = assembly.unlock(manually = true).approve(manually = true)
    changedAssembly.currentVersion.map(_.filesLocked) shouldBe Some(true)
    changedAssembly.manuallyUnlocked shouldBe false
  }
}
