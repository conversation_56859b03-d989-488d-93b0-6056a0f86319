package de.fellows.app.assembly.impl.entities

import de.fellows.app.assembly.commons.ProjectType
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should
import play.api.libs.json.Json

import java.time.Instant
import java.util.UUID

class VersionTest extends AnyFlatSpec with should.Matchers {
  "JSON converter for Version" should "be valid" in {
    val id  = UUID.randomUUID()
    val now = Instant.now()
    val version = Version(
      id = id,
      name = Some("1.0.0"),
      created = now,
      released = Some(now),
      files = Seq(),
      filesLocked = true,
      hints = Seq(),
      projectType = Some(ProjectType.NoFiles)
    )
    val json = Json.toJson(version)(Version.format)
    Json.fromJson(json)(Version.format).get shouldBe version
    (json \ "filesApproved").as[<PERSON>olean] shouldBe true
  }
  it should "correctly convert default values" in {
    val json =
      """{"id":"9188e3e5-1350-48cf-a574-a0b7b62670fd","name":"1.0.0","created":"2022-05-18T13:20:19.888705Z",
        |"released":"2022-05-18T13:20:19.888705Z","files":[], "hints":[]}""".stripMargin
    val version = Json.parse(json).as[Version](Version.format)
    version.filesLocked shouldBe false
    version.lifecycles shouldBe None
    version.template shouldBe None
  }
}
