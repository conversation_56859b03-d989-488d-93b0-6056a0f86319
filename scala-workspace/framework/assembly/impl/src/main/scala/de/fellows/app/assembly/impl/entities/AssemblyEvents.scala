package de.fellows.app.assembly.impl.entities

import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import de.fellows.app.assembly.commons.{AssemblyFeature, AssemblyReference, LqReference}
import de.fellows.app.assemby.api.enums.{AssemblyStatus, HintStatus, UIStatus}
import de.fellows.app.assemby.api.{IndividualAssemblyLifecycleStage, OriginalFile, ShareAssemblyTo}
import de.fellows.app.inbox.commons.MailMessageRef
import de.fellows.utils.FilePath
import de.fellows.utils.collaboration.TimelineEvent
import de.fellows.utils.internal.{File, FileLifecycleStage}
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID

sealed trait AssemblyEvent extends AggregateEvent[AssemblyEvent] {
  override def aggregateTag: AggregateEventShards[AssemblyEvent] = AssemblyEvent.Tag
}

object AssemblyEvent {
  val NumShards = 4
  val Tag       = AggregateEventTag.sharded[AssemblyEvent](NumShards)
}

case class BaseAssemblyReference(
    eid: UUID,
    gid: String,
    team: String,
    creator: String,
    created: Instant,
    customer: Option[UUID] = None,
    orderId: Option[String] = None,
    assignee: Option[UUID],
    itemNo: Option[String],
    name: String,
    description: Option[String],
    project: Option[UUID],
    status: AssemblyStatus,
    uiStatus: UIStatus,
    preview: Option[FilePath] = None,
    features: Seq[AssemblyFeature],
    currentVersion: Option[UUID],
    mail: Option[MailMessageRef],
    template: Option[AssemblyReference],
    externalReference: Option[LqReference]
)

object BaseAssemblyReference {
  implicit val format: Format[BaseAssemblyReference] = Json.format

  def of(assembly: Assembly): BaseAssemblyReference =
    BaseAssemblyReference(
      eid = assembly.eid,
      gid = assembly.gid,
      team = assembly.team,
      creator = assembly.creator,
      created = assembly.created,
      customer = assembly.information.customer,
      assignee = assembly.information.assignee,
      itemNo = assembly.information.itemNo,
      name = assembly.name,
      description = assembly.information.description,
      project = assembly.information.project,
      status = assembly.status,
      uiStatus = assembly.information.uiStatus,
      preview = assembly.preview,
      features = assembly.features,
      currentVersion = assembly.currentVersion.map(_.id),
      mail = assembly.mail,
      template = assembly.template,
      externalReference = assembly.externalReference
    )
}

case class AssemblyTimelineEvent(event: TimelineEvent) extends AssemblyEvent

case class OriginalFilesAdded(assembly: BaseAssemblyReference, files: Seq[OriginalFile]) extends AssemblyEvent

case class AssemblyCreated(assembly: BaseAssemblyReference) extends AssemblyEvent

case class AssemblyCloned(
    newAssembly: Assembly,
    origAssRef: AssemblyReference
) extends AssemblyEvent

object AssemblyCloned {
  implicit val format: Format[AssemblyCloned] = Json.format
}

case class NameChanged(assembly: BaseAssemblyReference, oldName: String, newName: String) extends AssemblyEvent

case class PreviewUpdated(assembly: BaseAssemblyReference, preview: FilePath) extends AssemblyEvent

case class DescriptionChanged(assembly: BaseAssemblyReference, newDescription: Option[String]) extends AssemblyEvent

case class AssemblyReleased(assembly: BaseAssemblyReference, version: Version) extends AssemblyEvent

case class UserAssigned(assembly: BaseAssemblyReference, user: Option[UUID], oldUser: Option[UUID])
    extends AssemblyEvent

case class CustomerAssigned(assembly: BaseAssemblyReference, customer: Option[UUID], oldCustomer: Option[UUID])
    extends AssemblyEvent

case class VersionCreated(assembly: BaseAssemblyReference, version: Version, previousVersion: Option[Version])
    extends AssemblyEvent

case class HintStatusChanged(assembly: BaseAssemblyReference, version: UUID, hint: Hint, status: HintStatus)
    extends AssemblyEvent

@Deprecated
case class FileAdded(assembly: BaseAssemblyReference, version: UUID, file: File) extends AssemblyEvent

case class FilesAdded(assembly: BaseAssemblyReference, version: UUID, file: Seq[File]) extends AssemblyEvent

@Deprecated
case class FileTypeUpdated(assembly: BaseAssemblyReference, version: UUID, file: File) extends AssemblyEvent

case class FileTypesUpdated(assembly: BaseAssemblyReference, version: UUID, file: Seq[File]) extends AssemblyEvent

case class FileHashesUpdated(assembly: BaseAssemblyReference, version: UUID, files: Seq[File]) extends AssemblyEvent

case class FilePreviewUpdated(assembly: BaseAssemblyReference, version: UUID, file: File) extends AssemblyEvent

case class FileDeleted(assembly: BaseAssemblyReference, version: UUID, fileName: String, file: File)
    extends AssemblyEvent

case class HintAdded(assembly: BaseAssemblyReference, version: UUID, hint: Hint) extends AssemblyEvent

case class AssemblyStatusChanged(assembly: BaseAssemblyReference, status: AssemblyStatus) extends AssemblyEvent

case class AssemblyUIStatusChanged(assembly: BaseAssemblyReference, status: UIStatus, oldStatus: UIStatus)
    extends AssemblyEvent

case class AssemblyShared(
    assembly: BaseAssemblyReference,
    version: UUID,
    toTeam: String,
    shareID: UUID,
    sharedAt: Instant
) extends AssemblyEvent

object AssemblyShared {
  implicit val format: Format[AssemblyShared] = Json.format
}

case class FileMatchingApproved(
    assembly: BaseAssemblyReference,
    version: UUID,
    files: Seq[File],
    lifecycles: Option[Seq[IndividualAssemblyLifecycleStage]] = None,
    manually: Boolean = false
) extends AssemblyEvent

case class FileMatchingUnlocked(
    assembly: BaseAssemblyReference,
    version: UUID,
    files: Seq[File],
    lifecycles: Option[Seq[IndividualAssemblyLifecycleStage]] = None,
    manually: Boolean = false
) extends AssemblyEvent

@deprecated
case class PreviewRequested(assembly: BaseAssemblyReference, versionID: UUID) extends AssemblyEvent

case class LifecycleUpdated(
    assembly: BaseAssemblyReference,
    versionID: UUID,
    file: String,
    fileApproval: Boolean = false,
    lc: FileLifecycleStage,
    oldState: Option[FileLifecycleStage],
    fileLifecycles: Seq[FileLifecycleStage] = Seq(),
    versionLifecycles: Seq[IndividualAssemblyLifecycleStage] = Seq()
) extends AssemblyEvent

case class FileLifecycleContainer(file: File, lifecycles: Seq[FileLifecycleStage])

case class VersionLifecycleUpdated(
    assembly: BaseAssemblyReference,
    versionID: UUID,
    fileApproval: Boolean = false,
    lc: Option[IndividualAssemblyLifecycleStage],
    oldState: Option[IndividualAssemblyLifecycleStage],
    versionLifecycles: Seq[IndividualAssemblyLifecycleStage] = Seq()
) extends AssemblyEvent

case class VersionLifecyclesUpdated(
    assembly: BaseAssemblyReference,
    versionID: UUID,
    fileApproval: Boolean = false,
    lc: Seq[IndividualAssemblyLifecycleStage],
    oldState: Seq[IndividualAssemblyLifecycleStage],
    versionLifecycles: Seq[IndividualAssemblyLifecycleStage] = Seq()
) extends AssemblyEvent

object AssemblyTimelineEvent {
  implicit val format: Format[AssemblyTimelineEvent] = Json.format
}
object OriginalFilesAdded {
  implicit val format: Format[OriginalFilesAdded] = Json.format
}

object AssemblyCreated {
  implicit val format: Format[AssemblyCreated] = Json.format
}

object UserAssigned {
  implicit val format: Format[UserAssigned] = Json.format
}

object NameChanged {
  implicit val format: Format[NameChanged] = Json.format
}

object DescriptionChanged {
  implicit val format: Format[DescriptionChanged] = Json.format
}

object AssemblyReleased {
  implicit val format: Format[AssemblyReleased] = Json.format
}

object VersionCreated {
  implicit val format: Format[VersionCreated] = Json.format
}

object HintStatusChanged {
  implicit val format: Format[HintStatusChanged] = Json.format
}

object FileAdded {
  implicit val format: Format[FileAdded] = Json.format
}

object FilesAdded {
  implicit val format: Format[FilesAdded] = Json.format
}

object FileDeleted {
  implicit val format: Format[FileDeleted] = Json.format
}

object HintAdded {
  implicit val format: Format[HintAdded] = Json.format
}

object FileTypeUpdated {
  implicit val format: Format[FileTypeUpdated] = Json.format
}

object FileTypesUpdated {
  implicit val format: Format[FileTypesUpdated] = Json.format
}

object FileHashesUpdated {
  implicit val format: Format[FileHashesUpdated] = Json.format[FileHashesUpdated]
}

object FilePreviewUpdated {
  implicit val format: Format[FilePreviewUpdated] = Json.format
}

object PreviewUpdated {
  implicit val format: Format[PreviewUpdated] = Json.format
}

object AssemblyStatusChanged {
  implicit val format: Format[AssemblyStatusChanged] = Json.format
}

object FileMatchingApproved {
  implicit val format: Format[FileMatchingApproved] = Json.using[Json.WithDefaultValues].format
}

object FileMatchingUnlocked {
  implicit val format: Format[FileMatchingUnlocked] = Json.using[Json.WithDefaultValues].format
}

object AssemblyUIStatusChanged {
  implicit val format: Format[AssemblyUIStatusChanged] = Json.format
}

object CustomerAssigned {
  implicit val format: Format[CustomerAssigned] = Json.format
}

object PreviewRequested {
  implicit val format: Format[PreviewRequested] = Json.format
}

object FileLifecycleContainer {
  implicit val format: Format[FileLifecycleContainer] = Json.using[Json.WithDefaultValues].format
}

object LifecycleUpdated {
  implicit val format: Format[LifecycleUpdated] = Json.using[Json.WithDefaultValues].format
}

object VersionLifecycleUpdated {
  implicit val format: Format[VersionLifecycleUpdated] = Json.using[Json.WithDefaultValues].format
}

object VersionLifecyclesUpdated {
  implicit val format: Format[VersionLifecyclesUpdated] = Json.using[Json.WithDefaultValues].format
}
