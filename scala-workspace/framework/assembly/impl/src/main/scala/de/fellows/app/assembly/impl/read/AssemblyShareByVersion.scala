package de.fellows.app.assembly.impl.read

import akka.Done
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.assembly.impl.entities.share.SharedAssemblyEvents.{AssemblyShareCreated, AssemblyShareRemoved, SharedAssemblyEvent}

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

class AssemblyShareByVersionRepository(session: CassandraSession)(implicit
    ec: ExecutionContext
) {
  def getSharesForVersion(sharedToTeam: String, sharedVersion: UUID): Future[Seq[UUID]] =
    session.selectAll(
      "SELECT * FROM AssemblySharesByVersion WHERE team = ? AND sharedVersion = ?",
      sharedToTeam,
      sharedVersion
    )
      .map(_.map(_.getUUID("id")))
}

class AssemblyShareByVersionProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext
) extends ReadSideProcessor[SharedAssemblyEvent] {

  var insertAssemblyShareStmt: Seq[PreparedStatement] = _
  var deleteAssemblyShareStmt: Seq[PreparedStatement] = _

  private def createTables(): Future[Done] =
    for {
      _ <- session.executeCreateTable(
        // language=CQL
        """
                |CREATE TABLE IF NOT EXISTS AssemblySharesByVersion (
                |  team text,
                |  sharedVersion uuid,
                |  id uuid,
                |  sharedTeam text,
                |  sharedId uuid,
                |  PRIMARY KEY (team, sharedVersion, id)
                |)
                |""".stripMargin
      )
    } yield Done

  private def prepareStatements(): Future[Done] =
    for {
      insertAssemblyShare <- session.prepare(
        // language=CQL
        """
          |INSERT INTO AssemblySharesByVersion (team, sharedVersion, id, sharedTeam, sharedId)
          |VALUES (?, ?, ?, ?, ?)
          |""".stripMargin
      )

      deleteAssemblyShare <- session.prepare(
        // language=CQL
        """
          |DELETE FROM AssemblySharesByVersion WHERE team = ? AND sharedVersion = ? AND id = ?
          |""".stripMargin
      )
    } yield {
      this.insertAssemblyShareStmt = Seq(insertAssemblyShare)
      this.deleteAssemblyShareStmt = Seq(deleteAssemblyShare)
      Done
    }

  def insertAssemblyShare(event: AssemblyShareCreated) =
    Future.successful(
      this.insertAssemblyShareStmt.map(_.bind()
        .setString("team", event.share.team)
        .setUUID("sharedVersion", event.share.ref.version)
        .setUUID("id", event.share.id)
        .setString("sharedTeam", event.share.ref.team)
        .setUUID("sharedId", event.share.ref.id))
    )

  def deleteAssemblyShare(event: AssemblyShareRemoved): Future[Seq[BoundStatement]] =
    Future.successful(
      deleteAssemblyShareStmt.map(_.bind()
        .setString("team", event.team)
        .setUUID("sharedVersion", event.ref.version)
        .setUUID("id", event.id))
    )

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[SharedAssemblyEvent] =
    readSide.builder[SharedAssemblyEvent]("assemblySharesByVersionOffset-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[AssemblyShareCreated](e => insertAssemblyShare(e.event))
      .setEventHandler[AssemblyShareRemoved](e => deleteAssemblyShare(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[SharedAssemblyEvent]] = SharedAssemblyEvent.Tag.allTags

}
