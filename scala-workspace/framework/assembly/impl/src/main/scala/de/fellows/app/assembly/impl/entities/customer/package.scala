package de.fellows.app.assembly.impl.entities

import java.util.UUID
import akka.Done
import com.lightbend.lagom.scaladsl.persistence.PersistentEntity.ReplyType
import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
import play.api.libs.json.{Format, Json}

package object customer {

  sealed trait CustomerReferenceEvent extends AggregateEvent[CustomerReferenceEvent] {
    override def aggregateTag: AggregateEventShards[CustomerReferenceEvent] = CustomerReferenceEvent.Tag
  }

  object CustomerReferenceEvent {
    val NumShards = 4
    val Tag       = AggregateEventTag.sharded[CustomerReferenceEvent](NumShards)
  }

  sealed trait CustomerReferenceCommand

  case class CreateCustomerReference(id: UUID) extends CustomerReferenceCommand with ReplyType[Done]

  case class CustomerReferenceCreated(id: UUID) extends CustomerReferenceEvent

  case class RemoveCustomerReference(id: UUID) extends CustomerReferenceCommand with ReplyType[Done]

  case class CustomerReferenceRemoved(id: UUID) extends CustomerReferenceEvent

  object CreateCustomerReference {
    implicit val format: Format[CreateCustomerReference] = Json.format[CreateCustomerReference]
  }

  object CustomerReferenceCreated {
    implicit val format: Format[CustomerReferenceCreated] = Json.format[CustomerReferenceCreated]
  }

  object RemoveCustomerReference {
    implicit val format: Format[RemoveCustomerReference] = Json.format[RemoveCustomerReference]
  }

  object CustomerReferenceRemoved {
    implicit val format: Format[CustomerReferenceRemoved] = Json.format[CustomerReferenceRemoved]
  }

}
