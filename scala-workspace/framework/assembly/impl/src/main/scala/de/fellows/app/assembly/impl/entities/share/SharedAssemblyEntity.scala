package de.fellows.app.assembly.impl.entities.share

import akka.Done
import com.typesafe.config.Config
import de.fellows.app.assembly.impl.entities.Assembly
import de.fellows.app.assembly.impl.entities.share.SharedAssemblyCommands.{
  CreateAssemblyShare,
  GetAssemblyShare,
  RemoveAssemblyShare,
  SharedAssemblyCommand,
  UpdateAssemblyShare
}
import de.fellows.app.assembly.impl.entities.share.SharedAssemblyEvents.SharedAssemblyEvent
import de.fellows.app.assemby.api.{SharedAssembly, SharedAssemblyInformation}
import de.fellows.app.assemby.api.enums.UIStatus
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.entities.secure.SecureTeamEntity
import de.fellows.utils.logging.StackrateLogging

import java.time.Instant

class SharedAssemblyEntity(implicit override val service: ServiceDefinition)
    extends SecureTeamEntity[Option[SharedAssembly]]
    with StackrateLogging {

  override def initialState = None

  override type Command = SharedAssemblyCommand
  override type Event   = SharedAssemblyEvent

  override def isAllowed(a: SharedAssemblyCommand, s: Option[SharedAssembly]): Boolean =
    a match {
      case GetAssemblyShare(team, id) =>
        s.map(_.team).contains(team) && s.map(_.id).contains(id)
      case SharedAssemblyCommands.CreateAssemblyShare(team, id, ref, timestamp, _) =>
        true
      case SharedAssemblyCommands.RemoveAssemblyShare(team, id) =>
        s.map(_.team).contains(team) && s.map(_.id).contains(id)

      case SharedAssemblyCommands.UpdateAssemblyShare(team, id, _, _) =>
        s.map(_.team).contains(team) && s.map(_.id).contains(id)
    }
  override def entityBehavior(state: Option[SharedAssembly]): Actions =
    (state match {
      case Some(value) => existing(value)
      case None        => missing
    })
      .onEvent {
        case (SharedAssemblyEvents.AssemblyShareRemoved(team, id, _), _) =>
          None
        case (SharedAssemblyEvents.AssemblyShareCreated(share), _) =>
          Some(share)
        case (SharedAssemblyEvents.AssemblyShareUpdated(team, id, old, up), _) =>
          state.map(_.copy(information = up))
      }

  private def missing: Actions =
    Actions()
      .onReadOnlyCommand[GetAssemblyShare, SharedAssembly] {
        case (GetAssemblyShare(team, id), ctx, state) =>
          ctx.commandFailed(
            new IllegalArgumentException(
              s"Assembly share $team/$id does not exist"
            )
          )
      }
      .onCommand[CreateAssemblyShare, SharedAssembly] {
        case (CreateAssemblyShare(team, id, ref, timestamp, info), ctx, _) =>
          val share = SharedAssembly(
            team,
            id,
            ref,
            timestamp,
            SharedAssemblyInformation(info.uiStatus, info.customer, info.assignee)
          )
          ctx.thenPersist(
            SharedAssemblyEvents.AssemblyShareCreated(share)
          ) { _ =>
            ctx.reply(share)
          }
      }
      .onReadOnlyCommand[RemoveAssemblyShare, Done] {
        case (RemoveAssemblyShare(team, id), ctx, _) =>
          ctx.commandFailed(
            new IllegalArgumentException(
              s"Assembly share $team/$id does not exist"
            )
          )
      }
      .onReadOnlyCommand[UpdateAssemblyShare, SharedAssembly] {
        case (UpdateAssemblyShare(team, id, _, _), ctx, _) =>
          ctx.commandFailed(
            new IllegalArgumentException(
              s"Assembly share $team/$id does not exist"
            )
          )
      }
  private def existing(sharedAssembly: SharedAssembly): Actions =
    Actions()
      .onReadOnlyCommand[CreateAssemblyShare, SharedAssembly] {
        case (CreateAssemblyShare(team, id, ref, timestamp, info), ctx, _) =>
          ctx.commandFailed(
            new IllegalArgumentException(
              s"Assembly share $team/$id already exists"
            )
          )
      }
      .onCommand[RemoveAssemblyShare, Done] {
        case (RemoveAssemblyShare(team, id), ctx, _) =>
          ctx.thenPersist(
            SharedAssemblyEvents.AssemblyShareRemoved(team, id, sharedAssembly.ref)
          ) { _ =>
            ctx.reply(Done)
          }
      }
      .onCommand[UpdateAssemblyShare, SharedAssembly] {
        case (UpdateAssemblyShare(team, id, up, tcmd), ctx, _) =>
          val oldInfo = sharedAssembly.information
          val newInfo = SharedAssemblyInformation(
            up.status.getOrElse(oldInfo.uiStatus),
            up.customer.orElse(oldInfo.customer),
            up.assignee.orElse(oldInfo.assignee)
          )

          ctx.thenPersist(
            SharedAssemblyEvents.AssemblyShareUpdated(team, id, oldInfo, newInfo)
          ) { _ =>
            ctx.reply(sharedAssembly.copy(information = newInfo))
          }
      }
      .onReadOnlyCommand[GetAssemblyShare, SharedAssembly] {
        case (GetAssemblyShare(team, id), ctx, state) =>
          ctx.reply(sharedAssembly)
      }
}
