package de.fellows.app.assembly.impl.read

import akka.Done
import akka.stream.Materializer
import com.datastax.driver.core.{BoundStatement, PreparedStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, EventStreamElement, ReadSideProcessor}
import de.fellows.app.assembly.impl.entities.{AssemblyEvent, FileDeleted, LifecycleUpdated, VersionLifecycleUpdated}
import de.fellows.app.assembly.impl.read.LifecycleTimeoutRepo.bucket
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.internal.FileLifecycleStageName
import de.fellows.utils.logging.StackrateLogging

import java.time.temporal.{ChronoField, ChronoUnit}
import java.time.{Instant, LocalDate, ZoneId}
import java.util.{Date, UUID}
import scala.concurrent.duration.{Deadline, Duration, MILLISECONDS}
import scala.concurrent.{ExecutionContext, Future}

sealed trait LifecycleDTO {
  val team: String
  val assembly: UUID
  val version: UUID
  val lifecycle: String
  val deadline: Deadline
}

case class FileLifecycleDTO(
    override val team: String,
    override val assembly: UUID,
    override val version: UUID,
    file: String,
    override val lifecycle: String,
    override val deadline: Deadline
) extends LifecycleDTO

case class VersionLifecycleDTO(
    override val team: String,
    override val assembly: UUID,
    override val version: UUID,
    override val lifecycle: String,
    override val deadline: Deadline
) extends LifecycleDTO

class LifecycleTimeoutRepo(session: CassandraSession)(implicit
    ec: ExecutionContext,
    mat: Materializer,
    service: ServiceDefinition
) extends StackrateLogging {

  def getTimeoutedFileLifecycles: Future[Seq[FileLifecycleDTO]] = {

    val now       = LocalDate.now()
    val nowMillis = System.currentTimeMillis()
    val buckets = Range(0, 5).map { r =>
      bucket(now.minus(r, ChronoUnit.MONTHS))
    }

    Future.sequence(buckets.map { bucket =>
      session.selectAll(
        """
          |SELECT * FROM FileLifecycleTimeouts WHERE bucket = ? AND timeout < ?
          |""".stripMargin,
        Long.box(bucket),
        Long.box(nowMillis)
      )
        .map(_.map { r =>
          FileLifecycleDTO(
            team = r.getString("team"),
            assembly = r.getUUID("assembly"),
            version = r.getUUID("version"),
            file = r.getString("file"),
            lifecycle = r.getString("lifecycle"),
            deadline = Deadline(Duration(r.getLong("timeout"), MILLISECONDS))
          )
        })
    }).map(_.flatten)

  }

  def getTimeoutedLifecycles = {

    val now       = LocalDate.now()
    val nowMillis = System.currentTimeMillis()
    val buckets = Range(0, 5).map { r =>
      bucket(now.minus(r, ChronoUnit.MONTHS))
    }

    Future.sequence(buckets.map { bucket =>
      session.selectAll(
        """
           SELECT * FROM LifecycleTimeouts WHERE bucket = ? AND timeout < ?
          """.stripMargin,
        Long.box(bucket),
        Long.box(nowMillis)
      )
        .map(_.map { r =>
          VersionLifecycleDTO(
            team = r.getString("team"),
            assembly = r.getUUID("assembly"),
            version = r.getUUID("version"),
            lifecycle = r.getString("lifecycle"),
            deadline = Deadline(Duration(r.getLong("timeout"), MILLISECONDS))
          )
        })
    }).map(_.flatten)
  }
}

object LifecycleTimeoutRepo {

  def bucket(i: LocalDate): Long = {
    val y = i.getLong(ChronoField.YEAR)
    val m = i.getLong(ChronoField.MONTH_OF_YEAR)
    s"$y$m".toLong
  }

  def bucket(dl: Deadline): Long = {
    val i = Instant.ofEpochMilli(dl.time.toMillis)
    bucket(LocalDate.ofInstant(i, ZoneId.systemDefault()))
  }

}

class LifecycleTimeoutsProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[AssemblyEvent] {

  override def aggregateTags: Set[AggregateEventTag[AssemblyEvent]] = AssemblyEvent.Tag.allTags

  var updateFileLCTimeoutStmt: PreparedStatement    = _
  var updateVersionLCTimeoutStmt: PreparedStatement = _
  var deleteFileLCTimeoutStmt: PreparedStatement    = _
  var deleteVersionLCTimeoutStmt: PreparedStatement = _

  private def prepareStatements() =
    for {
      updateFileLCTimeout <- session.prepare(
        "UPDATE FileLifecycleTimeouts SET updated = :updated WHERE bucket = :bucket AND team = :team AND timeout = :timeout AND assembly = :assembly AND version = :version AND file = :file AND lifecycle = :lifecycle"
      )
      updateVersionLCTimeout <- session.prepare(
        "UPDATE LifecycleTimeouts SET updated = :updated WHERE bucket = :bucket AND team = :team AND timeout = :timeout AND assembly = :assembly AND version = :version AND lifecycle = :lifecycle"
      )

      deleteFileLCTimeout <- session.prepare(
        "DELETE FROM FileLifecycleTimeouts WHERE bucket = :bucket AND team = :team AND timeout = :timeout AND assembly = :assembly AND version = :version AND file = :file AND lifecycle = :lifecycle"
      )
      deleteVersionLCTimeout <- session.prepare(
        "DELETE FROM LifecycleTimeouts WHERE bucket = :bucket AND team = :team AND timeout = :timeout AND assembly = :assembly AND version = :version AND lifecycle = :lifecycle"
      )

    } yield {
      this.updateFileLCTimeoutStmt = updateFileLCTimeout
      this.updateVersionLCTimeoutStmt = updateVersionLCTimeout
      this.deleteFileLCTimeoutStmt = deleteFileLCTimeout
      this.deleteVersionLCTimeoutStmt = deleteVersionLCTimeout
      Done
    }

  def versionLC(e: EventStreamElement[VersionLifecycleUpdated]): Future[Seq[BoundStatement]] =
    e.event.lc.map { lc =>
      val oldDeletion = e.event.oldState.flatMap { os =>
        os.status.deadline.map { dl =>
          val n = dl.time.toMillis
          (
            os.name,
            n,
            deleteVersionLCTimeoutStmt.bind()
              .setLong("bucket", bucket(dl))
              .setString("team", e.event.assembly.team)
              .setLong("timeout", n)
              .setUUID("assembly", e.event.assembly.eid)
              .setUUID("version", e.event.versionID)
              .setString("lifecycle", os.name.value)
          )
        }
      }

      lc.status.deadline match {
        case Some(dl) =>
          val update =
            updateVersionLCTimeoutStmt.bind()
              .setLong("bucket", bucket(dl))
              .setTimestamp("updated", Date.from(Instant.now()))
              .setString("team", e.event.assembly.team)
              .setLong("timeout", dl.time.toMillis)
              .setUUID("assembly", e.event.assembly.eid)
              .setUUID("version", e.event.versionID)
              .setString("lifecycle", lc.name.value)

          if (
            oldDeletion.map(_._1).contains(lc.name) &&
            oldDeletion.map(_._2) == lc.status.deadline.map(_.time.toMillis)
          ) {
            Future.successful(Seq(update))
          } else {
            Future.successful(
              oldDeletion.map(_._3).toSeq ++
                Seq(
                  update
                )
            )
          }

        case None => // no new deadline, delete the old if there is one
          Future.successful(oldDeletion.map(_._3).toSeq)
      }
    }.getOrElse(Future.successful(Seq()))

  private def deleteFile(e: EventStreamElement[FileDeleted]): Future[Seq[BoundStatement]] = {
    val statements = e.event.file.lifecycles.getOrElse(Seq()).flatMap { flc =>
      flc.status.deadline.map { dl =>
        val n = dl.time.toMillis

          deleteFileLCTimeoutStmt.bind()
            .setLong("bucket", bucket(dl))
            .setString("team", e.event.assembly.team)
            .setLong("timeout", n)
            .setUUID("assembly", e.event.assembly.eid)
            .setUUID("version", e.event.version)
            .setString("lifecycle", flc.name.value)
            .setString("file", e.event.file.name)
      }
    }

    Future.successful(statements)
  }

  def fileLC(e: EventStreamElement[LifecycleUpdated]): Future[Seq[BoundStatement]] = {
    val lc = e.event.lc

    val oldDeletion = e.event.oldState.flatMap { os =>
      os.status.deadline.map { dl =>
        val n = dl.time.toMillis
        (
          os.name,
          n,
          deleteFileLCTimeoutStmt.bind()
            .setLong("bucket", bucket(dl))
            .setString("team", e.event.assembly.team)
            .setLong("timeout", n)
            .setUUID("assembly", e.event.assembly.eid)
            .setUUID("version", e.event.versionID)
            .setString("lifecycle", os.name.value)
            .setString("file", e.event.file)
        )
      }
    }

    lc.status.deadline match {
      case Some(dl) =>
        val update =
          updateFileLCTimeoutStmt.bind()
            .setLong("bucket", bucket(dl))
            .setTimestamp("updated", Date.from(Instant.now()))
            .setString("team", e.event.assembly.team)
            .setLong("timeout", dl.time.toMillis)
            .setUUID("assembly", e.event.assembly.eid)
            .setUUID("version", e.event.versionID)
            .setString("file", e.event.file)
            .setString("lifecycle", lc.name.value)

        if (
          oldDeletion.map(_._1).contains(lc.name) &&
          oldDeletion.map(_._2) == lc.status.deadline.map(_.time.toMillis)
        ) {
          Future.successful(Seq(update))
        } else {
          Future.successful(
            oldDeletion.map(_._3).toSeq ++
              Seq(
                update
              )
          )
        }

      case None => // no new deadline, delete the old if there is one
        Future.successful(oldDeletion.map(_._3).toSeq)
    }
  }

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[AssemblyEvent] =
    readSide.builder[AssemblyEvent]("assemblyLCTimeoutOffset-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[VersionLifecycleUpdated](e => versionLC(e))
      .setEventHandler[LifecycleUpdated](e => fileLC(e))
      .setEventHandler[FileDeleted](e => deleteFile(e))
      .build()

  private def createTables() =
    for {
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS FileLifecycleTimeouts (
          |   bucket bigint,
          |   team text,
          |   timeout bigint,
          |   assembly uuid,
          |   version uuid,
          |   file text,
          |   lifecycle text,
          |   updated timestamp,
          |   PRIMARY KEY (bucket, timeout,team, assembly, version, file, lifecycle)
          | )
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        """
          | CREATE TABLE IF NOT EXISTS LifecycleTimeouts (
          |   bucket bigint,
          |   team text,
          |   timeout bigint,
          |   assembly uuid,
          |   version uuid,
          |   lifecycle text,
          |   updated timestamp,
          |   PRIMARY KEY (bucket, timeout, team, assembly, version, lifecycle)
          | )
          |""".stripMargin
      )
    } yield Done

}
