package de.fellows.app.assembly.impl.read

import com.datastax.driver.core.{TypeCodec, UDTValue, UserType}
import de.fellows.app.assembly.commons.{AssemblyFeature, LqReference}
import de.fellows.app.assembly.impl.entities.BaseAssemblyReference
import de.fellows.app.assemby.api.enums.{AssemblyStatus, UIStatus}
import de.fellows.app.inbox.commons.{MailBox, MailMessageRef}
import de.fellows.utils.codec.AbstractCodec
import de.fellows.utils.{FilePath, UUIDUtils}

import java.time.Instant
import java.util.UUID

class BaseAssemblyReferenceCodec(cdc: TypeCodec[UDTValue])
    extends AbstractCodec[BaseAssemblyReference](cdc, classOf[BaseAssemblyReference]) {
  override def toValue(value: BaseAssemblyReference): UDTValue =
    if (value == null) null
    else {
      var v = cdc.getCqlType.asInstanceOf[UserType].newValue()
        .setUUID("eid", value.eid)
        .setString("gid", value.gid)
        .setString("team", value.team)
        .setUUID("creator", UUIDUtils.fromString(value.creator).getOrElse(UUIDUtils.nil))
        .set("created", value.created, classOf[Instant])
        .setUUID("customer", value.customer.orNull)
        .setString("orderid", value.orderId.orNull)
        .setUUID("assignee", value.assignee.orNull)
        .setString("itemNo", value.itemNo.orNull)
        .setString("name", value.name)
        .setString("description", value.description.orNull)
        .setUUID("project", value.project.orNull)
        .setString("status", value.status.s)
        .setString("uiStatus", value.uiStatus.status)
        .set("preview", value.preview.orNull, classOf[FilePath])
        .setUUID("currentVersion", value.currentVersion.orNull)
        .setString("mailId", value.mail.map(_.id).orNull)
        .setString("mailboxUser", value.mail.map(_.mailbox.team).orNull)
        .setString("mailboxFolder", value.mail.map(_.mailbox.inboxId).orNull)
        .set("externalReference", value.externalReference.orNull, classOf[LqReference])

      value.uiStatus.progress match {
        case Some(p) => v = v.setInt("uiStatusProgress", p)
        case _       => v = v.setToNull("uiStatusProgress")
      }

      v
    }

  override def fromValue(value: UDTValue): BaseAssemblyReference =
    if (value == null) null
    else {
      implicit val v = value
      val uuid       = get(classOf[UUID]) _
      val s          = get(classOf[String]) _
      val uuido      = geto(classOf[UUID]) _
      val so         = geto(classOf[String]) _

      BaseAssemblyReference(
        eid = uuid("eid"),
        gid = s("gid"),
        team = s("team"),
        creator = uuid("creator").toString,
        created = get(classOf[Instant])("created"),
        customer = uuido("customer"),
        orderId = so("orderid"),
        assignee = uuido("assignee"),
        itemNo = so("itemNo"),
        name = s("name"),
        description = so("description"),
        project = uuido("project"),
        status = AssemblyStatus(s("status")),
        uiStatus = UIStatus(s("uiStatus"), geto(classOf[Integer])("uiStatusProgress").map(_.intValue())),
        preview = geto(classOf[FilePath])("preview"),
        features = getl(classOf[AssemblyFeature])("features"),
        currentVersion = uuido("currentVersion"),
        mail = (so("mailId"), so("mailboxUser"), so("mailboxFolder")) match {
          case (Some(mid), Some(mu), Some(mf)) => Some(MailMessageRef(mid, MailBox(mu, mf)))
          case _                               => None
        },
        template = None,
        externalReference = geto(classOf[LqReference])("externalReference")
      )
    }
}
