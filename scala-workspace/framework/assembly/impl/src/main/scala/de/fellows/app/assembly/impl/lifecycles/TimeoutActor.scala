package de.fellows.app.assembly.impl.lifecycles

import akka.Done
import akka.actor.typed.scaladsl.AskPattern.Askable
import akka.actor.typed.scaladsl.{ActorContext, Behaviors}
import akka.actor.typed.{ActorRef, Behavior, Scheduler}
import akka.cluster.typed.{<PERSON><PERSON><PERSON><PERSON>leton, SingletonActor}
import akka.util.Timeout
import com.fasterxml.jackson.annotation.{JsonSubTypes, JsonTypeInfo}
import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRegistry
import de.fellows.app.assembly.impl.entities.{AssemblyEntity, UpdateFileLifecycle, UpdateVersionLifecycle}
import de.fellows.app.assembly.impl.read.{FileLifecycleDTO, LifecycleDTO, LifecycleTimeoutRepo, VersionLifecycleDTO}
import de.fellows.app.assemby.api.{AssemblyLifecycleStageName, IndividualAssemblyLifecycleStage}
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.internal.{FileLifecycleStage, FileLifecycleStageName, LifecycleStageStatus, StageStatusName}
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.jobs.JobBuilder
import kamon.Kamon
import play.api.libs.json.Json

import java.time.Instant
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes(
  Array(
    new JsonSubTypes.Type(value = classOf[StartTimeoutHandling], name = "StartTimeoutHandling"),
    new JsonSubTypes.Type(value = classOf[CheckTimeouts], name = "CheckTimeouts"),
    new JsonSubTypes.Type(value = classOf[FinishChecking], name = "FinishChecking")
  )
)
sealed trait TimeoutActorCommand

final case class StartTimeoutHandling(replyTo: ActorRef[TimeoutCheckingRunning]) extends TimeoutActorCommand

final case class CheckTimeouts() extends TimeoutActorCommand

final case class FinishChecking() extends TimeoutActorCommand

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes(
  Array(
    new JsonSubTypes.Type(value = classOf[TimeoutCheckingRunning], name = "TimeoutCheckingRunning")
  )
)
sealed trait TimeoutActorEvent

final case class TimeoutCheckingRunning(startedAt: Instant, fresh: Boolean, from: ActorRef[CheckTimeouts])
    extends TimeoutActorEvent

/** An Actor that runs periodically and checks whether lifecycles are over their deadlines.
  * if so, it'll put them into the timeout state
  */
object TimeoutActor extends StackrateLogging {
  implicit val actorTimeout: Timeout = (15 seconds)

  private def start(
      repo: LifecycleTimeoutRepo,
      reg: PersistentEntityRegistry,
      jobQueue: JobBuilder
  ): Behavior[TimeoutActorCommand] =
    Behaviors.setup { ctx =>
      val at = Instant.now()
      schedule(ctx)
      started(at, repo, reg, jobQueue)
    }

  private def schedule(ctx: ActorContext[TimeoutActorCommand]): Unit =
    ctx.scheduleOnce(1 minute, ctx.self, CheckTimeouts())

  def persistTimeouts(cycles: Seq[LifecycleDTO], reg: PersistentEntityRegistry)(implicit
      ectx: ExecutionContext
  ): Future[Seq[Try[Done]]] =
    Future.sequence(cycles.groupBy(_.assembly).flatMap { x =>
      val (assemblyId, lifecycles) = x

      lifecycles.groupBy(_.version).flatMap { y =>
        val (version, versionedLifecycles) = y
        val entity                         = reg.refFor[AssemblyEntity](assemblyId.toString)

        val status = LifecycleStageStatus(StageStatusName.Timeout, Seq(s"Timeout"), percent = None)
        val commands = versionedLifecycles
          .flatMap {
            case x: FileLifecycleDTO =>
              FileLifecycleStageName.fromName(x.lifecycle).map { stage =>
                val lc = FileLifecycleStage(stage, status, None)
                UpdateFileLifecycle(
                  team = x.team,
                  assemblyId,
                  version,
                  x.file,
                  lc,
                  Some(System.currentTimeMillis()),
                  TimelineCommand.system
                )
              }
            case x: VersionLifecycleDTO =>
              AssemblyLifecycleStageName.fromName(x.lifecycle).map { stage =>
                val lc = IndividualAssemblyLifecycleStage(stage, status, None)
                UpdateVersionLifecycle(
                  team = x.team,
                  assemblyId,
                  version,
                  lc,
                  Some(System.currentTimeMillis()),
                  TimelineCommand.system
                )
              }
          }

        commands.map(c =>
          entity.ask(c)
            .map(_ => Success(Done))
            .recover(e => Failure(e))
        )
      }

    }).map(_.toSeq)

  private def started(
      at: Instant,
      repo: LifecycleTimeoutRepo,
      reg: PersistentEntityRegistry,
      jobQueue: JobBuilder
  ): Behavior[TimeoutActorCommand] =
    Behaviors.receive { (ctx, message) =>
      logger.info(s"[TIMEOUT] ${message.getClass.getSimpleName} ?")

      message match {
        case StartTimeoutHandling(replyTo) =>
          logger.info(s"[TIMEOUT] already running ")
          replyTo ! TimeoutCheckingRunning(at, false, ctx.self)
        case CheckTimeouts() =>
          implicit val ectx = (ctx.executionContext)

          (
            Kamon.span(s"Timeout Job") {
              for {
                _ <- rescheduleJobs(jobQueue)
                _ <- timeoutLifecycles(repo, reg)
              } yield Done
            }
          )
            .map(_ => schedule(ctx))
            .recover {
              case e =>
                logger.info("[TIMEOUT] TimeoutsCheckedDone with errors!", e)
                schedule(ctx)
            }

          Behaviors.same

        case FinishChecking() =>
          schedule(ctx)
      }
      Behaviors.same
    }

  private def rescheduleJobs(jobBuilder: JobBuilder)(implicit ctx: ExecutionContext): Future[Done] =
    Future {
      val jobsInTimeout = jobBuilder.getTimeoutedJobs(5 minutes)
      logger.info(s"[TIMEOUT] Rescheduling ${jobsInTimeout.size} timeouted jobs")
      jobsInTimeout
        .foreach { job =>
          jobBuilder.reInsertExistingJob(job)
        }

      val unresponsiveJobs = jobBuilder.getUnresponsiveJobs
      logger.info(s"[TIMEOUT] Rescheduling ${unresponsiveJobs.size} unresponsive jobs")

      unresponsiveJobs
        .foreach { job =>
          jobBuilder.reInsertExistingJob(job)
        }

      Kamon.currentSpan().tag(s"timeouts.jobs.size", jobsInTimeout.size)
      Kamon.currentSpan().tag(s"timeout.unresponsive-jobs.size", unresponsiveJobs.size)
      Done
    }

  private def timeoutLifecycles(repo: LifecycleTimeoutRepo, reg: PersistentEntityRegistry)(implicit
      ctx: ExecutionContext
  ) =
    for {
      fileLifecycles    <- repo.getTimeoutedFileLifecycles
      versionLifecycles <- repo.getTimeoutedLifecycles
      results           <- persistTimeouts(fileLifecycles ++ versionLifecycles, reg)

    } yield {

      val fails = results.collect {
        case x: Failure[_] => x
      }

      fileLifecycles.foreach { x =>
        logger.info(s"[TIMEOUT] Timeouted file lifecycle: ${x.team}/${x.assembly}/${x.version} ${x.lifecycle}")
      }
      logger.info(
        s"[TIMEOUT] TimeoutsCheckedDone (timeouted ${fileLifecycles.size} fileLifecycles and ${versionLifecycles.size} versionLifecycles) with ${fails.size} failures !"
      )

      Kamon.currentSpan().tag("timeouts.file-lifecycles.teams", Json.stringify(Json.toJson(fileLifecycles.map(_.team).toSet)))
      Kamon.currentSpan().tag("timeouts.lifecycles.teams", Json.stringify(Json.toJson(versionLifecycles.map(_.team).toSet)))

      Kamon.currentSpan().tag("timeouts.file-lifecycles.size", fileLifecycles.size)
      Kamon.currentSpan().tag("timeouts.lifecycles.size", versionLifecycles.size)

    }

  def apply(
      repo: LifecycleTimeoutRepo,
      reg: PersistentEntityRegistry,
      jobQueue: JobBuilder
  ): Behavior[TimeoutActorCommand] =
    start(repo, reg, jobQueue)
}

class TimeoutActorSetup(
    sngltn: ClusterSingleton,
    repo: LifecycleTimeoutRepo,
    reg: PersistentEntityRegistry,
    jobQueue: JobBuilder
)(implicit
    ec: ExecutionContext,
    scheduler: Scheduler
) extends StackrateLogging {
  implicit val actorTimeout: Timeout = (15 seconds)

  def setup(): ActorRef[TimeoutActorCommand] = {
    val inboxActor = TimeoutActor(repo, reg, jobQueue)
    val sgltnName  = "assembly-service-lifecycle-timeout-actor"

    val actor = SingletonActor(
      inboxActor,
      sgltnName
    )
    logger.info("[TIMEOUT] starting timeoutactor! ")
    val proxy: ActorRef[TimeoutActorCommand] = sngltn.init(
      actor
    )

    proxy.ask(r => StartTimeoutHandling(r))

    proxy
  }

}
