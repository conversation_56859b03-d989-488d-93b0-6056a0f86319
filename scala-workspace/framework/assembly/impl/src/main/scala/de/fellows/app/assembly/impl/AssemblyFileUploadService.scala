package de.fellows.app.assembly.impl

import akka.Done
import akka.stream.IOResult
import akka.stream.scaladsl.{FileIO, Sink}
import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.transport.TransportException
import com.lightbend.lagom.scaladsl.persistence.{PersistentEntity, PersistentEntityRegistry}
import com.squareup.okhttp.OkHttpClient
import com.typesafe.config.ConfigFactory
import de.fellows.app.assembly.commons.AssemblyFiles
import de.fellows.app.assembly.commons.AssemblyFiles.getAssemblyBaseFolder
import de.fellows.app.assembly.impl.entities.{Assembly, AssemblyEntity, BaseAssemblyReference, GetAssembly}
import de.fellows.app.assembly.impl.fileprocessing.FileProcessor
import de.fellows.app.assemby.api.{AssemblyCreation, InternalAssemblyCreation}
import de.fellows.app.security.SecurityApi.PermissionRequest
import de.fellows.app.security.{AccessControl, SecurityBodyParser}
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.playutils._
import de.fellows.utils.security._
import de.fellows.utils.{FilePath, UUIDUtils}
import play.api.http.FileMimeTypes
import play.api.libs.json.Json
import play.api.libs.streams.Accumulator
import play.api.mvc.MultipartFormData.FilePart
import play.api.mvc._
import play.api.routing.Router
import play.api.routing.sird._
import play.core.parsers.Multipart.{FileInfo, FilePartHandler}

import java.io.{File, FileOutputStream}
import java.nio.file.{Files, Path}
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.reflect.ClassTag
import scala.util.{Failure, Success, Using}

class AssemblyFileUploadService(
    action: DefaultActionBuilder,
    parser: PlayBodyParsers,
    registry: PersistentEntityRegistry,
    mime: FileMimeTypes,
    exCtx: ExecutionContext
) extends StackrateLogging {

  val conf                          = ConfigFactory.load()
  implicit val ec: ExecutionContext = exCtx;
  implicit val m: FileMimeTypes     = mime;

  var app: AssemblyServiceApp = _

  def withApp(app: AssemblyServiceApp): AssemblyFileUploadService = {
    this.app = app
    this
  }

  private def tmpFileHandler(token: GenericTokenContent, tmp: Path): FilePartHandler[Path] = {
    case FileInfo(partName, _, contentType, s) =>
      contentType match {
        case _ =>
          val targetPath                               = tmp.resolve(partName)
          val sink: Sink[ByteString, Future[IOResult]] = FileIO.toPath(targetPath)
          val acc: Accumulator[ByteString, IOResult]   = Accumulator(sink)
          acc.map {
            case akka.stream.IOResult(_, _) =>
              FilePart(partName, targetPath.toFile.getName, contentType, targetPath)
          }
      }
  }

  private def createAssembly(): Action[MultipartFormData[Path]] = {
    var tkn: GenericTokenContent = null
    var tcmd: TimelineCommand    = null
    action.async(
      SecurityBodyParser(token =>
        s"assembly:${
            token.getTeam
          }:${
            token.getTeam
          }:*:*:write"
      ) { token =>
        tkn = token
        tcmd = TimelineCommand.of(token)
        val tmp = FilePath.createTempDirectory("assemblycreation")(conf)
        parser.multipartFormData(FileHelper.tmpFileHandler(tmp), 2 * 1024 * 1024000000)
      }
    ) {
      request: Request[MultipartFormData[Path]] =>
        val files = request.body.files
        logger.debug(s"Assembly creation files $files")
        val name        = request.body.dataParts.getOrElse("name", Seq()).headOption.getOrElse(UUIDUtils.createShort())
        val orderid     = request.body.dataParts.getOrElse("orderid", Seq()).headOption
        val description = request.body.dataParts.getOrElse("description", Seq()).headOption

        val creation = InternalAssemblyCreation(
          assembly = AssemblyCreation(
            name = Some(name),
            description = description,
            orderId = orderid,
            files = Some(files.map(_.ref.toAbsolutePath.toString)),
            forceFiles = Some(true)
          ),
          features = None,
          timelineCommand = Some(tcmd),
          withoutFiles = false
        )

        app.impl._doCreateAssembly(tkn.getUserId, tkn.getTeam, creation)
          .map { a =>
            logger.withAssembly(tkn.getTeam, a.id, a.currentVersion.map(_.id), Some(a.gid))
            Results.Ok(Json.toJson(a))
          }
          .recover {
            case e: TransportException => new Results.Status(e.errorCode.http)(e.exceptionMessage.detail)
          }
    }
  }

  private def uploadFile(assembly: String, version: String, filename: String): Action[MultipartFormData[FilePath]] =
    doUploadFiles(assembly, version, Some(filename))

  private def uploadFiles(assembly: String, version: String): Action[MultipartFormData[FilePath]] =
    doUploadFiles(assembly, version, None)

  private def doUploadFiles(
      assembly: String,
      version: String,
      filename: Option[String]
  ): Action[MultipartFormData[FilePath]] = {

    val entity = registry.refFor[AssemblyEntity](assembly)

    val helper: UploadHelper = new UploadHelper {

      var assEntity: Assembly          = _
      var timelineCmd: TimelineCommand = _

      override def permission(token: GenericTokenContent): String =
        token match {
          case _: Auth0TokenContent => "edit:pcb"
          case t                    => s"assembly:${t.getTeam}:${t.getTeam}:${assembly}:files:write"
        }

      override def init(token: GenericTokenContent, tcmd: TimelineCommand): Future[Done] =
        entity.ask(GetAssembly(token.getTeam, UUID.fromString(assembly)))
          .map { ass =>
            assEntity = ass
            timelineCmd = tcmd
          }
          .map(_ => Done)

      override def validate(
          team: String,
          filename: Option[String],
          partName: String,
          targetFile: FilePath
      ): Future[Option[String]] = {
        val locked = assEntity.currentVersion.exists(_.filesLocked)

        Future.successful(
          if (locked) {
            Some("Files are locked")
          } else {
            None
          }
        )
      }

      override def addFiles(files: Seq[FilePath]): Future[UploadFilesResult] = {
        // remove any files that already exist in the PCB
        // this allows the same behavior as uploading a zip file that contains duplicate files: no new files are added
        // (even though the assembly entity would handle that properly, we do not create those commands)
        val assemblyFiles = assEntity.currentVersion.map(_.files).getOrElse(Seq.empty)
        val nonDuplicate  = files.filterNot(f => assemblyFiles.exists(_.name == f.filename))
        val processor     = new FileProcessor(BaseAssemblyReference.of(assEntity), timelineCmd)

        processor.validateAndAddFiles(
          nonDuplicate,
          entity,
          original = true
        ).map {
          case (addedFiles, addWarnings) =>
            // We end up detecting a duplicate file on two different levels:
            //
            // 1. The file already exists in the assembly and we see the same file in the upload list
            // 2. The file already exists in the assembly and we see the same file inside a compressed archive
            //
            // The first case we can detect right here and avoid creating a command for it.
            // The second case is handled by the FileProcessor and we get a warning back.
            // We only want to return a warning in both cases so we end up checking in both places
            val duplicateFilesRemoved = nonDuplicate.size != files.size
            val warnings              = addWarnings ++ Option.when(duplicateFilesRemoved)(UploadError.FileAlreadyExists)
            val fileResults           = addedFiles.map(f => UploadedFile(f.name, f.fType))
            val mbWarnings            = Option.when(warnings.nonEmpty)(warnings.toSeq)

            UploadFilesResult(fileResults, mbWarnings)
        }
      }

      override def createFilePath(team: String, cleanFileName: String): FilePath =
        AssemblyFileUploadService.createAssemblyFilePath(team, assembly, version, cleanFileName)
    }

    FileHelper.upload(action, parser, filename, helper)
  }

  // @formatter:off
  def router: Router = {
    logger.debug("create route")
    Router.from {
      case POST(p"/files/assembly/$assembly/versions/$version/files/${filename}") =>
        uploadFile(assembly, version, filename)

      case POST(p"/files/assembly/$assembly/versions/$version/files") =>
        uploadFiles(assembly, version)

      case POST(p"/files/assembly/assemblies") =>
        createAssembly()

      case GET(p"/files/assembly/$assembly/versions/$version/$category/${filename}*") =>
        downloadFile(assembly, version, category, filename)

      case GET(p"/files/assembly/$assembly") =>
        downloadPreview(assembly)

      case GET(p"/files/assembly/$assembly/versions/$version/zip") =>
        downloadZip(assembly, version)

      case GET(p"/files/assembly/shares/$share/zip") =>
        downloadShareZip(share)

      case GET(p"/files/assembly/$assembly/versions/$version/internalzip") =>
        downloadInternalZip(assembly, version)
    }
  }


  // @formatter:onn
  private def downloadInternalZip(assembly: String, version: String): Action[AnyContent] =

    action(parser.anyContent).async {
      request =>
        request.queryString.get("k") match {
          case Some(k) if k.length == 1 =>
            transportError { () =>
              AuthenticationServiceComposition.decodeAnyToken(Some(request.headers), k.headOption) match {
                case Success(token: TokenContent) =>
                  logger.withAssembly(token.team, UUID.fromString(assembly), UUIDUtils.fromString(version), None)
                  val req = s"*:*:*:*:*:*" //admin
                  val p = token.claims.flatMap(_.permission).map(Permission.create)

                  val pl = AccessControl.checkPermissionsRequest(
                    PermissionRequest(
                      Seq(Seq(Permission.create(req)))
                    ),
                    p
                  )

                  if (pl.allowed) {

                    zipInternalFile(token.team, assembly, version, request)
                  } else {
                    Future.successful(Results.Forbidden(s"Permission Denied"))
                  }

                // Auth0/FileToken do not have claims
                case Success(_) => Future.successful(Results.Forbidden(s"Permission Denied: token has no claims"))
                case Failure(x) => Future.successful(Results.Forbidden(s"Permission Denied ${x.getMessage}"))
              }
            }

          case _ => Future.successful(Results.Forbidden(s"token missing"))
        }
    }

  private def downloadShareZip(share: String): Action[AnyContent] = {
    action(parser.anyContent).async {
      request =>
        request.queryString.get("k") match {
          case Some(k) if k.length == 1 =>
            transportError { () =>
              AuthenticationServiceComposition.decodeAnyToken(Some(request.headers), k.headOption) match {
                case Success(token: TokenContent) =>
                  val shareId = UUID.fromString(share)


                  val req = s"assembly:${token.team}:${token.team}:${shareId}:zip:read"
                  val p = token.claims.flatMap(_.permission).map(Permission.create)

                  val pl = AccessControl.checkPermissionsRequest(
                    PermissionRequest(
                      Seq(Seq(Permission.create(req)))
                    ),
                    p
                  )

                  if (pl.allowed) {


                    app.impl._doGetSharedAssembly(token.team, shareId).flatMap(assembly => {
                      val c = new okhttp3.OkHttpClient()
                      val req = new okhttp3.Request.Builder()
                        .url(s"http://pcb-server:8080/api/pcb/${assembly.assembly.currentVersion.get.id.toString}/download-pdf")
                        .header("validated-token", k.head)
                        .build()

                      val pdf = c.newCall(req).execute()
                      pdf.body().byteStream()
                      val tmp = Files.createTempFile("pdf", ".pdf").toFile
                      Using.resource(new FileOutputStream(tmp)) { r =>
                        r.write(pdf.body().bytes())
                      }
                      logger.info(s"pdf is ${pdf}")
                      zipFile(token.team, assembly.assembly.id.toString, request)
                    })
                  } else {
                    Future.successful(Results.Forbidden(s"Permission Denied"))
                  }

                // Auth0/FileToken do not have claims
                case Success(_) => Future.successful(Results.Forbidden(s"Permission Denied: token has no claims"))
                case Failure(x) => Future.successful(Results.Forbidden(s"Permission Denied ${x.getMessage}"))
              }
            }

          case _ => Future.successful(Results.Forbidden(s"token missing"))
        }
    }
  }

  private def downloadZip(assembly: String, version: String): Action[AnyContent] =
    action(parser.anyContent).async {
      request =>
        request.queryString.get("k") match {
          case Some(k) if k.length == 1 =>
            transportError { () =>
              AuthenticationServiceComposition.decodeAnyToken(Some(request.headers), k.headOption) match {
                case Success(token: TokenContent) =>
                  logger.withAssembly(token.team, UUID.fromString(assembly), UUIDUtils.fromString(version), None)

                  val req = s"assembly:${token.team}:${token.team}:${assembly}:zip:read"
                  val p = token.claims.flatMap(_.permission).map(Permission.create)

                  val pl = AccessControl.checkPermissionsRequest(
                    PermissionRequest(
                      Seq(Seq(Permission.create(req)))
                    ),
                    p
                  )

                  if (pl.allowed) {

                    zipFile(token.team, assembly, request)
                  } else {
                    Future.successful(Results.Forbidden(s"Permission Denied"))
                  }

                // Auth0/FileToken do not have claims
                case Success(_) => Future.successful(Results.Forbidden(s"Permission Denied: token has no claims"))
                case Failure(x) => Future.successful(Results.Forbidden(s"Permission Denied ${x.getMessage}"))
              }
            }

          case _ => Future.successful(Results.Forbidden(s"token missing"))
        }
    }

  private def downloadPreview(assembly: String): Action[AnyContent] = {
    var token: GenericTokenContent = null
    action.async(
      SecurityBodyParser(token => s"assembly:${token.getTeam}:${token.getTeam}:${assembly}:files:zip") { t =>
        token = t
        parser.anyContent
      }
    ) {
      request =>
        registry.refFor[AssemblyEntity](assembly).ask(GetAssembly(token.getTeam, UUID.fromString(assembly))).map { ass =>
          ass.preview.map { fp =>
              FileHelper.deliverFile(fp.toJavaFile, "preview", request.headers)
            }
            .getOrElse(Results.NotFound)
        }
    }
  }

  private def downloadFile(assembly: String, version: String, cat: String, relPath: String): Action[AnyContent] =
    action(parser.anyContent).async {
      request =>
        request.queryString.get("k").map(_.headOption) match {
          case Some(k) =>
            transportError { () =>
              doDownloadFile(assembly, version, cat, relPath, request, k)
            }

          case _ => Future.successful(Results.Forbidden("Invalid Key"))
        }
    }

  private def doDownloadFile(
                              assembly: String,
                              version: String,
                              cat: String,
                              relPath: String,
                              request: Request[AnyContent],
                              k: Option[String],
                            ): Future[Result] = {
    val (subPath, name) = FilePath.splitSubPath(relPath)
    AuthenticationServiceComposition.decodeAnyToken(Some(request.headers), k) match {
      case Success(FileTokenContent(team, claims, true)) =>
        logger.withAssembly(team, UUID.fromString(assembly), UUIDUtils.fromString(version), None)

        val fp = FilePath(
          AssemblyFileUploadService.basePath,
          team,
          AssemblyFileUploadService.getLocalResourceFolder(assembly, version),
          cat,
          subPath,
          name
        )

        if (
          claims.exists(c =>
            Seq("assembly", "pcb" /* Workaround. TODO: move pcb files to pcb service */).contains(c.service)
              && fp.matchesApiPath(c.path)
              && fp.matchesResource(c.resource)
          )

            && team == fp.team
        ) {

          deliverFile(team, assembly, version, cat, relPath, request, fp)
        } else {
          logger.info(s"file forbidden: ${claims}")
          Future.successful(Results.Forbidden(s"Permission Denied"))
        }

      case Success(token: TokenContent) =>
        logger.withAssembly(token.team, UUID.fromString(assembly), UUIDUtils.fromString(version), None)

        val fp = FilePath(
          AssemblyFileUploadService.basePath,
          token.team,
          AssemblyFileUploadService.getLocalResourceFolder(assembly, version),
          cat,
          subPath,
          name
        )
        val req = s"assembly:${token.team}:${token.team}:${assembly}:files:zip"
        val p = token.claims.flatMap(_.permission).map(Permission.create)

        val pl = AccessControl.checkPermissionsRequest(
          PermissionRequest(
            Seq(Seq(Permission.create(req)))
          ),
          p
        )

        if (pl.allowed) {
          deliverFile(token.team, assembly, version, cat, relPath, request, fp)
        } else {
          Future.successful(Results.Forbidden(s"Permission Denied"))
        }

      case x => Future.successful(Results.Forbidden(s"Permission Denied $x"))
    }
  }


  def zipInternalFile(team: String, assembly: String, version: String, request: Request[AnyContent]): Future[Result] = {
    registry.refFor[AssemblyEntity](assembly).ask(GetAssembly(team, UUID.fromString(assembly))).map { ass =>
      val f = Files.createTempFile("InternalAssemblyZip", ".zip").toFile
      FileHelper.zip(f, getAssemblyBaseFolder(team, ass.eid)(conf))

      val fileName = s"internalAssembly-${ass.gid}.zip"
      Results.Ok.sendFile(
        f,
        fileName = f =>
          Some(fileName),
        onClose = () => f.delete()
      ).withHeaders(
        ("X-File-Name" -> fileName),
        ("Access-Control-Expose-Headers" -> "Content-Disposition")
      )
    }
  }

  private def zipFile(team: String, assembly: String, request: Request[AnyContent]): Future[Result] =
    registry.refFor[AssemblyEntity](assembly).ask(GetAssembly(team, UUID.fromString(assembly))).map { ass =>

      val files = ass.currentVersion.toSeq.flatMap(_.originalFiles).flatten
      val (fileToDeliver, onClose): (File, () => Unit) =
        if (files.length == 1 && files.head.originalName.toLowerCase.endsWith(".zip")) {
          logger.info("deliver the original zip file")

          (files.head.path.toJavaFile, () => ())
        } else if (files.length > 1) {
          val f = Files.createTempFile("assemblyZip", ".zip").toFile

          FileHelper.zip(
            target = f,
            value = files.map(_.path),
            nameGenerator = file => s"${ass.gid}/${file.filename}",
          )

          (f, () => f.delete())
        } else {
          val f = Files.createTempFile("assemblyZip", ".zip").toFile

          FileHelper.zip(
            target = f,
            value = ass.currentVersion.toSeq.flatMap(_.files).map(_.path),
            nameGenerator = file => s"${ass.gid}/${file.filename}",
          )

          (f, () => f.delete())
        }

      val fileName = s"assembly-${ass.gid}.zip"
      Results.Ok.sendFile(
        fileToDeliver,
        fileName = f =>
          Some(fileName),
        onClose = onClose,
      ).withHeaders(
        ("X-File-Name" -> fileName),
        ("Access-Control-Expose-Headers" -> "Content-Disposition")
      )
    }

  private def deliverFile(
                           team: String,
                           assembly: String,
                           version: String,
                           cat: String,
                           relPath: String,
                           request: Request[AnyContent],
                           fp: FilePath
                         ): Future[Result] = {
    logger.info(s"deliver file $fp: ${fp.toJavaPath}")
    val file = fp.findExisting()
    if (file.isDefined) {
      Future.successful(FileHelper.deliverFile(file.get.toFile, relPath, request.headers))
    } else {
      val versionID = UUID.fromString(version)
      registry.refFor[AssemblyEntity](assembly).ask(GetAssembly(team, UUID.fromString(assembly))).map { ass =>
        val version = (ass.versions ++ ass.currentVersion.map(Seq(_)).getOrElse(Seq())).find(_.id == versionID)
        version.flatMap(_.files.find(_.name == relPath)).map { file =>
          FileHelper.deliverFile(file.path.toJavaFile, relPath, request.headers)
        }.getOrElse(Results.NotFound)
      }
    }
  }

  private def refFor[T <: PersistentEntity : ClassTag](id: String) = registry.refFor[T](id)

}

object AssemblyFileUploadService {
  lazy val conf = ConfigFactory.load()

  val basePath: String =
    conf.getString("fellows.storage.service")

  def getRootAssemblyPath(team: String, assembly: UUID): Option[Path] =
    AssemblyFiles.getRootAssemblyPath(team, assembly)(conf)

  def createAssemblyFilePath(team: String, assembly: String, version: String, filename: String): FilePath =
    AssemblyFiles.createAssemblyUploadFilePath(team, UUID.fromString(assembly), UUID.fromString(version), filename)(
      conf
    )

  def getLocalResourceFolder(assembly: String, version: String) =
    s"$assembly/$version/"

}
