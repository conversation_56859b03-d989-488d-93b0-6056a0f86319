package de.fellows.app.assembly.impl.entities

import de.fellows.app.assembly.commons.{AssemblyFeature, AssemblyReference, LqReference, ProjectType}
import de.fellows.app.assemby.api.enums.{AssemblyStatus, HintLevel, HintStatus, UIStatus}
import de.fellows.app.assemby.api.{IndividualAssemblyLifecycleStage, OriginalFile, SharedAssemblyInfo}
import de.fellows.app.inbox.commons.MailMessageRef
import de.fellows.utils.FilePath
import de.fellows.utils.internal.File
import play.api.libs.functional.syntax.{toFunctionalBuilderOps, unlift}
import play.api.libs.json.Json.MacroOptions
import play.api.libs.json.{__, Format, Json, JsonConfiguration}

import java.time.Instant
import java.util.UUID

case class AssemblyInformation(
    customer: Option[UUID],
    orderId: Option[String],
    assignee: Option[UUID],
    itemNo: Option[String],
    description: Option[String],
    uiStatus: UIStatus,
    project: Option[UUID]
)

case class Assembly(
    eid: UUID,
    gid: String,
    name: String,
    creator: String,
    created: Instant,
    team: String,
    status: AssemblyStatus,
    information: AssemblyInformation,
    versions: Seq[Version],
    currentVersion: Option[Version],
    features: Seq[AssemblyFeature],
    preview: Option[FilePath],
    mail: Option[MailMessageRef] = None,
    template: Option[AssemblyReference] = None,
    manuallyUnlocked: Boolean = false,
    externalReference: Option[LqReference] = None
) {
  def approve(manually: Boolean): Assembly =
    if (!manually && manuallyUnlocked) {
      this
    } else {
      copy(currentVersion = currentVersion.map(_.lock), manuallyUnlocked = false)
    }

  def unlock(manually: Boolean): Assembly =
    copy(currentVersion = currentVersion.map(_.unlock), manuallyUnlocked = manually)

  def locked: Boolean = currentVersion.exists(_.filesLocked)
}

final case class Version(
    id: UUID,
    name: Option[String],
    created: Instant,
    released: Option[Instant],
    files: Seq[File],
    filesLocked: Boolean = false,
    hints: Seq[Hint],
    lifecycles: Option[Seq[IndividualAssemblyLifecycleStage]] = None,
    projectType: Option[ProjectType],
    template: Option[UUID] = None,
    originalFiles: Option[Seq[OriginalFile]] = None,
    shares: Option[Seq[SharedAssemblyInfo]] = None
) {
  def lock: Version   = copy(filesLocked = true)
  def unlock: Version = copy(filesLocked = false)
}

case class Hint(
    id: UUID,
    service: String,
    title: String,
    message: Option[String],
    status: HintStatus,
    level: HintLevel
)

object Assembly {
  implicit val format: Format[Assembly] = Json.using[Json.WithDefaultValues].format
}

object AssemblyInformation {
  implicit val format: Format[AssemblyInformation] = Json.format
}

object Version {
  private implicit val config: JsonConfiguration.Aux[MacroOptions] = JsonConfiguration()
  implicit val format: Format[Version] = (
    (__ \ "id").format[UUID] and
      (__ \ "name").formatNullable[String] and
      (__ \ "created").format[Instant] and
      (__ \ "released").formatNullable[Instant] and
      (__ \ "files").format[Seq[File]] and
      (__ \ "filesApproved").formatWithDefault[Boolean](false) and
      (__ \ "hints").format[Seq[Hint]] and
      (__ \ "lifecycles").formatNullableWithDefault[Seq[IndividualAssemblyLifecycleStage]](None) and
      (__ \ "projectType").formatNullable[ProjectType] and
      (__ \ "template").formatNullableWithDefault[UUID](None) and
      (__ \ "originalFiles").formatNullable[Seq[OriginalFile]] and
      (__ \ "shares").formatNullable[Seq[SharedAssemblyInfo]]
  )(Version.apply, unlift(Version.unapply))
}

object Hint {
  implicit val format: Format[Hint] = Json.format
}
