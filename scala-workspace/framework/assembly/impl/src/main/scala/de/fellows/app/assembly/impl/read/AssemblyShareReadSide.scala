package de.fellows.app.assembly.impl.read

import akka.Done
import akka.stream.Materializer
import akka.stream.scaladsl.Sink
import com.datastax.driver.core.{BoundStatement, PreparedStatement, Row, SimpleStatement}
import com.lightbend.lagom.scaladsl.persistence.cassandra.{CassandraReadSide, CassandraSession}
import com.lightbend.lagom.scaladsl.persistence.{AggregateEventTag, ReadSideProcessor}
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assembly.impl.entities.share.SharedAssemblyEvents.{AssemblyShareCreated, AssemblyShareRemoved, SharedAssemblyEvent}
import de.fellows.app.assemby.api.enums.UIStatus
import de.fellows.app.assemby.api.{SharedAssembly, SharedAssemblyInformation}
import de.fellows.utils.PaginationListResult
import de.fellows.utils.communication.ServiceDefinition

import java.util.{Date, UUID}
import scala.concurrent.{ExecutionContext, Future}

class AssemblyShareRepository(session: CassandraSession)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition,
    mat: Materializer
) {

  def getSharesForAssembly(team: String, assembly: UUID, version: UUID): Future[Seq[SharedAssembly]] =
    session.selectAll(
      "SELECT * FROM AssemblySharesByAssembly WHERE sharedTeam = ? AND sharedId = ? AND sharedVersion = ?",
      team,
      assembly,
      version
    )
      .map(_.map(convert))

  def getSharesForTeam(
      team: String,
      page: Option[Int],
      pagesize: Option[Int]
  ): Future[PaginationListResult[SharedAssembly]] = {
    val stmt  = new SimpleStatement("SELECT * FROM AssemblySharesCreatedDesc WHERE team = ?", team)
    val count = new SimpleStatement("SELECT COUNT(*) FROM AssemblySharesCreatedDesc WHERE team = ?", team)

    val ps = pagesize.getOrElse(100)
    val p  = page.getOrElse(1) - 1

    stmt.setFetchSize(ps)
    val source = session.select(stmt)
      .drop(p * ps)
      .take(ps)
      .map(
        convert
      )

    for {
      count  <- session.selectOne(count)
      result <- source.runWith(Sink.seq)
    } yield {
      val number: Option[BigDecimal] = count.flatMap(r => Option(r.getLong(0)))
      PaginationListResult(result, number.map(_.intValue).getOrElse(0))
    }

  }

  private def convert(r: Row) = {
    val shareId = r.getUUID("id")
    val team    = r.getString("team")

    val sharedTeam    = r.getString("sharedTeam")
    val sharedId      = r.getUUID("sharedId")
    val sharedGid     = Option(r.getString("sharedGid"))
    val sharedVersion = r.getUUID("sharedVersion")

    SharedAssembly(
      team,
      shareId,
      AssemblyReference(
        sharedTeam,
        sharedId,
        sharedGid,
        sharedVersion
      ),
      (r.getTimestamp("created")).toInstant,
      SharedAssemblyInformation(
        UIStatus(r.getString("uiStatus")),
        Option(r.getUUID("customer")),
        Option(r.getUUID("assignee"))
      )
    )
  }
}
class AssemblyShareReadSideProcessor(session: CassandraSession, readSide: CassandraReadSide)(implicit
    ec: ExecutionContext,
    service: ServiceDefinition
) extends ReadSideProcessor[SharedAssemblyEvent] {

  var insertAssemblyShareStmt: Seq[PreparedStatement]      = _
  var deleteAssemblyShareStmt: Seq[PreparedStatement]      = _
  var insertAssemblyShareByAssemblyStmt: PreparedStatement = _

  private def createTables(): Future[Done] =
    for {
      _ <- session.executeCreateTable(
        // language=CQL
        """
          |CREATE TABLE IF NOT EXISTS AssemblyShares (
          |  team text,
          |  id uuid,
          |  sharedTeam text,
          |  sharedId uuid,
          |  sharedGid text,
          |  sharedVersion uuid,
          |  created timestamp,
          |  uiStatus text,
          |  customer uuid,
          |  assignee uuid,
          |  PRIMARY KEY (team, id)
          |)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        // language=CQL
        """
          |CREATE TABLE IF NOT EXISTS AssemblySharesCreatedDesc (
          |  team text,
          |  id uuid,
          |  sharedTeam text,
          |  sharedId uuid,
          |  sharedGid text,
          |  sharedVersion uuid,
          |  created timestamp,
          |  uiStatus text,
          |  customer uuid,
          |  assignee uuid,
          |  PRIMARY KEY (team, id, created)
          |) WITH CLUSTERING ORDER BY (id DESC)
          |""".stripMargin
      )
      _ <- session.executeCreateTable(
        // language=CQL
        """
          |CREATE TABLE IF NOT EXISTS AssemblySharesByAssembly (
          |  team text,
          |  id uuid,
          |  sharedTeam text,
          |  sharedId uuid,
          |  sharedGid text,
          |  sharedVersion uuid,
          |  created timestamp,
          |  uiStatus text,
          |  customer uuid,
          |  assignee uuid,
          |  PRIMARY KEY (sharedTeam, sharedId, sharedVersion, id)
          |)
          |""".stripMargin
      )
    } yield Done

  private def prepareStatements(): Future[Done] =
    for {
      insertAssemblyShare <- session.prepare(
        // language=CQL
        """
            |INSERT INTO AssemblyShares (team, id, sharedTeam, sharedId, sharedGid, sharedVersion, created, uiStatus, customer, assignee)
            |VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            |""".stripMargin
      )
      insertAssemblyShareCreatedDesc <- session.prepare(
        // language=CQL
        """
            |INSERT INTO AssemblySharesCreatedDesc (team, id, sharedTeam, sharedId, sharedGid, sharedVersion, created, uiStatus, customer, assignee)
            |VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            |""".stripMargin
      )
      deleteAssemblyShare <- session.prepare(
        // language=CQL
        """
            |DELETE FROM AssemblyShares WHERE team = ? AND id = ?
            |""".stripMargin
      )
      deleteAssemblyShareCreatedDesc <- session.prepare(
        // language=CQL
        """
            |DELETE FROM AssemblySharesCreatedDesc WHERE team = ? AND id = ?
            |""".stripMargin
      )
      insertAssemblyShareByAssembly <- session.prepare(
        // language=CQL
        """
            |INSERT INTO AssemblySharesByAssembly (sharedTeam, sharedId, id, team, sharedGid, sharedVersion, created, uiStatus, customer, assignee)
            |VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            |""".stripMargin
      )
    } yield {
      this.insertAssemblyShareStmt = Seq(insertAssemblyShare, insertAssemblyShareCreatedDesc)
      this.deleteAssemblyShareStmt = Seq(deleteAssemblyShare, deleteAssemblyShareCreatedDesc)
      this.insertAssemblyShareByAssemblyStmt = insertAssemblyShareByAssembly
      Done
    }

  def insertAssemblyShare(event: AssemblyShareCreated): Future[Seq[BoundStatement]] =
    Future.successful(
      insertAssemblyShareStmt.map(_.bind()
        .setString("team", event.share.team)
        .setUUID("id", event.share.id)
        .setString("sharedTeam", event.share.ref.team)
        .setUUID("sharedId", event.share.ref.id)
        .setString("sharedGid", event.share.ref.gid.getOrElse(""))
        .setUUID("sharedVersion", event.share.ref.version)
        .setTimestamp("created", Date.from(event.share.created))
        .setString("uiStatus", event.share.information.uiStatus.status)
        .setUUID("customer", event.share.information.customer.orNull)
        .setUUID("assignee", event.share.information.assignee.orNull)) :+
        insertAssemblyShareByAssemblyStmt.bind()
          .setString("team", event.share.team)
          .setUUID("id", event.share.id)
          .setString("sharedTeam", event.share.ref.team)
          .setUUID("sharedId", event.share.ref.id)
          .setString("sharedGid", event.share.ref.gid.getOrElse(""))
          .setUUID("sharedVersion", event.share.ref.version)
          .setTimestamp("created", Date.from(event.share.created))
          .setString("uiStatus", event.share.information.uiStatus.status)
          .setUUID("customer", event.share.information.customer.orNull)
          .setUUID("assignee", event.share.information.assignee.orNull)
    )

  def deleteAssemblyShare(event: AssemblyShareRemoved): Future[Seq[BoundStatement]] =
    Future.successful(
      deleteAssemblyShareStmt.map(_.bind()
        .setString("team", event.team)
        .setUUID("id", event.id))
    )

  override def buildHandler(): ReadSideProcessor.ReadSideHandler[SharedAssemblyEvent] =
    readSide.builder[SharedAssemblyEvent]("assemblySharesOffset-v1.0")
      .setGlobalPrepare(createTables)
      .setPrepare(_ => prepareStatements())
      .setEventHandler[AssemblyShareCreated](e => insertAssemblyShare(e.event))
      .setEventHandler[AssemblyShareRemoved](e => deleteAssemblyShare(e.event))
      .build()

  override def aggregateTags: Set[AggregateEventTag[SharedAssemblyEvent]] = SharedAssemblyEvent.Tag.allTags
}
