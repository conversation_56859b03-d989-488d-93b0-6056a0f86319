package de.fellows.app.assembly.impl.lifecycles

import de.fellows.app.assemby.api.AssemblyLifecycleStageName._
import de.fellows.utils.internal.{
  AssemblyLifecycleStage,
  HistoricLifecycleState,
  LifecycleStageName,
  LifecycleStageStatus
}
import play.api.libs.json._

sealed trait AggregateLifecycleStageName extends LifecycleStageName

object AggregateLifecycleStageName extends {
  final val COMPOSITION = "composition"
  final val DFMANALYSIS = "dfmanalysis"
  final val MAIN        = "main"

  case object Composition extends AggregateLifecycleStageName {
    val value = COMPOSITION
    val parts = Seq(
      AggregatePart(Render, weight = 80),
      AggregatePart(Initialization, weight = 20)
    )
  }
  case object DfmAnalysis extends AggregateLifecycleStageName {
    val value = DFMANALYSIS
    val parts = Seq(
      AggregatePart(FileAnalysis, weight = 50),
      AggregatePart(Analysis, weight = 50),
      AggregatePart(Dfm, required = false)
    )
  }
  case object Main extends AggregateLifecycleStageName {
    val value = MAIN
    val parts = Seq(
      AggregatePart(Files, weight = 5),
      AggregatePart(Render, weight = 30),
      AggregatePart(SpecificationRender, weight = 10),
      AggregatePart(ReconciledSpecificationRender, weight = 10),
      AggregatePart(Initialization, weight = 10),
      AggregatePart(FileAnalysis, weight = 20),
      AggregatePart(Analysis, weight = 30),
      AggregatePart(Reconciliation, weight = 10),
      AggregatePart(Dfm, weight = 5)
    )
  }

  private val names = Array(
    Composition,
    DfmAnalysis,
    Main
  )

  def fromName(name: String): Option[AggregateLifecycleStageName] = names.find(_.value == name)

  implicit val format: Format[AggregateLifecycleStageName] = new Format[AggregateLifecycleStageName] {
    override def writes(o: AggregateLifecycleStageName): JsValue = JsString(o.value)
    override def reads(json: JsValue): JsResult[AggregateLifecycleStageName] = json match {
      case JsString(name) =>
        fromName(name) match {
          case Some(value) => JsSuccess(value)
          case None        => JsError(s"Unknown AggregateLifecycleStageName: $name")
        }
      case _ => JsError(s"Expected JsString, got $json")
    }
  }
}

/** Aggregated stage is created on the fly and it consists of individual stages. It's not stored in the database.
  */
case class AggregateLifecycleStage(
    name: AggregateLifecycleStageName,
    status: LifecycleStageStatus,
    history: Option[Seq[HistoricLifecycleState]]
) extends AssemblyLifecycleStage
