package de.fellows.app.assembly.impl.fileprocessing

import de.fellows.app.assembly.impl.entities.AddFile
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.utils.FilePath
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.internal.FileType
import de.fellows.utils.logging.StackrateLogging
import de.luminovo.odb.odbpp.model.ODBUtils

import java.io.File
import java.util.UUID

sealed trait AddCommandGenerator {
  def buildAddCommands(
      team: String,
      tcmd: TimelineCommand,
      basePath: FilePath,
      file: java.io.File
  ): Seq[AddProjectCommand]
}

object GenericAddCommandGenerator extends AddCommandGenerator {
  override def buildAddCommands(
      team: String,
      tcmd: TimelineCommand,
      basePath: FilePath,
      file: java.io.File
  ): Seq[AddProjectCommand] = {
    val refToAdd  = basePath.copy(filename = file.getName)
    val newFileID = UUID.randomUUID()
    Seq(
      AddUnaffiliatedFiles(
        Seq(
          AddFile(
            team = team,
            id = newFileID,
            fileName = refToAdd.filename,
            path = refToAdd,
            fType = FileType.UNMATCHED,
            detectedTypes = Seq(),
            timeline = tcmd
          )
        )
      )
    )
  }
}

object ODBAddCommandGenerator extends AddCommandGenerator {

  override def buildAddCommands(
      team: String,
      tcmd: TimelineCommand,
      basePath: FilePath,
      file: java.io.File
  ): Seq[AddProjectCommand] = {
    val odbRoots = file.listFiles().toSeq

    odbRoots.find(_.getName == "steps").toSeq.flatMap { steps =>
      val individualSteps = steps.listFiles().toSeq
      val multiStep       = individualSteps.length > 1
      individualSteps
        .map { step =>
          // this is one step of the ODB
          val stepName = step.getName

          val stepFolder = basePath
            .sub(Seq("steps", stepName))

          val stepProfile =
            step.toPath.resolve("profile").toFile

          val extraStepFiles =
            if (stepProfile.exists() && stepProfile.isFile) {
              val newFileID = UUID.randomUUID()
              val refToAdd  = stepFolder.copy(filename = "profile")

              Seq(AddFile(
                team = team,
                id = newFileID,
                fileName = s"${stepName}_profile",
                path = refToAdd,
                fType = FileType.UNMATCHED_ODB.copy(
                  mimeType = LayerConstants.Mime.odblinerecord
                ),
                detectedTypes = Seq(),
                timeline = tcmd
              ))
            } else {
              Seq()
            }

          val adds = extraStepFiles ++
            step.listFiles().toSeq
              .find(_.getName == "layers").toSeq
              .flatMap { layers =>
                layers.listFiles().toSeq.map { layer =>
                  val layerName = layer.getName

                  val fileName =
                    if (multiStep) {
                      s"${stepName}_${layerName}"
                    } else {
                      layerName
                    }
                  val layerFolder = stepFolder
                    .sub(Seq("layers"))

                  val refToAdd = layerFolder.copy(filename = layerName)

                  val newFileID = UUID.randomUUID()
                  AddFile(
                    team = team,
                    id = newFileID,
                    fileName = fileName,
                    path = refToAdd,
                    fType = FileType.UNMATCHED_ODB,
                    detectedTypes = Seq(),
                    timeline = tcmd
                  )
                }

              }


          AddODBStep(Some(stepName), adds)
        }
    }
  }
}

/** This processor handles a second step of file processing.
  *
  * All the files are extracted at this point, and we can do some prefiltering and/or matching
  */
class SecondaryFileProcessor(team: String, tcmd: TimelineCommand, files: FileTree) extends StackrateLogging {

  private def addUnknownFile(basePath: FilePath, file: File): Seq[AddProjectCommand] =
    if (file.isFile) {
      // this is a plain file. use the default
      GenericAddCommandGenerator.buildAddCommands(
        team = team,
        tcmd = tcmd,
        basePath = basePath,
        file = file
      )
    } else if (file.isDirectory) {
      val folder = basePath.sub(file.getName)

      val children = Option(file.listFiles()).map(_.toSeq).getOrElse(Seq())

      if (ODBUtils.isODBFolder(children)) {
        logger.info(s"add odb folder ${folder.toPath}")
        // we found an ODB folder, so we'll add all the files explicitly and do not continue recursively
        val adds = ODBAddCommandGenerator.buildAddCommands(
            team = team,
            tcmd = tcmd,
            basePath = folder,
            file = file
        )
        adds
      } else {
        // this is not an ODB folder, so we just continue on recursively
        children.flatMap { f =>
          addUnknownFile(folder, f)
        }
      }
    } else {
      logger.warn(s"file is neither file nor directory: ${file.toPath}: ${file}")
      Seq()
    }

  def buildAddCommands(): Seq[AddProjectCommand] =
    files.roots.flatMap { fp =>
      // for the extracted files we'll need the parent directory as the sub path, since the individual files fill contain the root as well
      val relativized = fp.copy(subPath = fp.subPath.map(s => s.dropRight(1)))
      addUnknownFile(relativized, fp.toJavaFile)
    }

  private def printFileTree(root: java.io.File, level: Int = 0, stringBuilder: StringBuilder): Unit = {
    stringBuilder.append("  " * level)
    stringBuilder.append(root.getName)
    stringBuilder.append("\n")

    Option(root.listFiles).foreach(files => files.foreach(file => printFileTree(file, level + 1, stringBuilder)))
  }

  def dumpFileTree(): String = {
    val sb = new StringBuilder
    files.roots.foreach { r =>
      sb.append(s"root: ${r}\n")
      printFileTree(r.toJavaFile, 0, sb)
    }

    sb.toString()
  }

}
