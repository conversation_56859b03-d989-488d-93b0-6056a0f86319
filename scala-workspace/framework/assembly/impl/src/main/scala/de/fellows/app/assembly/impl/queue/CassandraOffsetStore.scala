package de.fellows.app.assembly.impl.queue

import akka.Done
import akka.persistence.query.{Offset, TimeBasedUUID}
import com.datastax.driver.core.PreparedStatement
import com.lightbend.lagom.scaladsl.persistence.AggregateEventTag
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import de.fellows.app.assembly.impl.entities.AssemblyEvent
import de.fellows.utils.redislog.OffsetStore

import scala.concurrent.{ExecutionContext, Future}

class CassandraOffsetStore(
    id: String,
    session: CassandraSession
)(implicit val ctx: ExecutionContext) extends OffsetStore[AssemblyEvent] {
  var setOffsetQueryStmt: PreparedStatement = _

  val processorId =
    s"redislog-${id}"

  override def prepare(tag: AggregateEventTag[AssemblyEvent]): Future[Offset] =
    for {
      setOffsetQuery <-
        session.prepare("UPDATE offsetstore SET timeuuidoffset = ? WHERE eventprocessorid = ? AND tag = ?")
      offset <-
        session.selectOne(
          "SELECT timeuuidoffset FROM offsetstore WHERE eventprocessorid = ? AND tag = ?",
          processorId,
          tag.tag
        )
    } yield {
      this.setOffsetQueryStmt = setOffsetQuery
      offset match {
        case Some(row) => Offset.timeBasedUUID(row.getUUID("timeuuidoffset"))
        case None      => Offset.noOffset
      }
    }

  override def setOffset(o: TimeBasedUUID, tag: AggregateEventTag[AssemblyEvent]): Future[Done] =
    session.executeWrite(setOffsetQueryStmt.bind(o.value, processorId, tag.tag)).map(_ => Done)
}
