package de.fellows.app.assembly.impl;

import com.datastax.driver.core.ProtocolVersion;
import com.datastax.driver.core.TypeCodec;
import com.datastax.driver.core.UDTValue;
import com.datastax.driver.core.UserType;
import com.datastax.driver.core.exceptions.InvalidTypeException;
import de.fellows.utils.internal.FileType;
import scala.None$;
import scala.Option;
import scala.runtime.AbstractFunction0;

import java.math.BigDecimal;
import java.nio.ByteBuffer;

public class FileTypeCodec extends TypeCodec<FileType> {

    private final TypeCodec<UDTValue> innerCodec;
    private final UserType userType;


    public FileTypeCodec(TypeCodec<UDTValue> innerCodec, Class<FileType> javaType) {
        super(innerCodec.getCqlType(), javaType);
        this.innerCodec = innerCodec;
        this.userType = (UserType) innerCodec.getCqlType();
    }

    @Override
    public ByteBuffer serialize(FileType value, ProtocolVersion protocolVersion) throws InvalidTypeException {
        return innerCodec.serialize(toUDTValue(value), protocolVersion);
    }

    @Override
    public FileType deserialize(ByteBuffer bytes, ProtocolVersion protocolVersion) throws InvalidTypeException {
        return toPermission(innerCodec.deserialize(bytes, protocolVersion));
    }

    @Override
    public FileType parse(String value) throws InvalidTypeException {
        return value == null || value.isEmpty() /*|| value.equals(NULL)*/ ? null : toPermission(innerCodec.parse(value));
    }

    @Override
    public String format(FileType value) throws InvalidTypeException {
        return value == null ? null : innerCodec.format(toUDTValue(value));
    }

    protected FileType toPermission(UDTValue value) {
        Option index = None$.MODULE$;
        BigDecimal i = value.getDecimal("typeIndex");
        if (i.intValue() >= 0) {
            index = Option.apply(i.intValue());
        }
        Option from = None$.MODULE$;
        i = value.getDecimal("typeFrom");
        if (i.intValue() >= 0) {
            from = Option.apply(i.intValue());
        }
        Option to = None$.MODULE$;
        i = value.getDecimal("typeTo");
        if (i.intValue() >= 0) {
            to = Option.apply(i.intValue());
        }

        return value == null ? null : new FileType(
                value.getString("typeService"),
                Option.apply(value.getString("typeCategory")),
                value.getString("typeType"),
                value.getBool("typeProd"),
                Option.apply(value.getString("typeMime")),
                index,
                from,
                to
        );
    }

    protected UDTValue toUDTValue(FileType value) {
        BigDecimal index = new BigDecimal(-1);
        if (value.index().isDefined()) {
            index = new BigDecimal(((Integer) value.index().get()));
        }
        BigDecimal from = new BigDecimal(-1);
        if (value.from().isDefined()) {
            from = new BigDecimal(((Integer) value.from().get()));
        }
        BigDecimal to = new BigDecimal(-1);
        if (value.to().isDefined()) {
            to = new BigDecimal(((Integer) value.to().get()));
        }

        return value == null ? null : userType.newValue()
                .setString("typeService", value.service())
                .setString("typeCategory", value.category().getOrElse(new AbstractFunction0<String>() {
                    @Override
                    public String apply() {
                        return null;
                    }
                }))
                .setString("typeType", value.fileType())
                .setBool("typeProd", value.productionFile())
                .setString("typeMime", value.mimeType().getOrElse(new AbstractFunction0<String>() {
                    @Override
                    public String apply() {
                        return null;
                    }
                }))
                .setDecimal("typeIndex", index)
                .setDecimal("typeTo", to)
                .setDecimal("typeFrom", from);
    }

}
