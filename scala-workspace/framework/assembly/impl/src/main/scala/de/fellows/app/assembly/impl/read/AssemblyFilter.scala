package de.fellows.app.assembly.impl.read

import de.fellows.app.assembly.impl.entities.BaseAssemblyReference
import de.fellows.utils.RequestFilter
import play.api.Logging

class AssemblyFilter(rFilter: RequestFilter) extends Logging {

  def filter(ref: BaseAssemblyReference): Boolean =
    rFilter.elements.forall { x =>
      val f = filter(x._2, x._3) _
      x._1 match {
        case "name"        => f(ref.name)
        case "gid"         => f(ref.gid)
        case "customer"    => f(ref.customer)
        case "creator"     => f(ref.creator)
        case "assignee"    => f(ref.assignee)
        case "description" => f(ref.description)
        case "project"     => f(ref.project)
        case "uiStatus"    => f(ref.uiStatus.status)
        case _ =>
          true
      }
    }

  def filter(op: String, filter: String)(_value: Any) = {
    // ">=", "<=", ">", "<", "!=", "="

    val value: Option[Any] = _value match {
      case Some(x) => Some(x)
      case None    => None
      case x       => Some(x)
    }

    op match {
      case "!=" => value.isEmpty || value.get.toString.toLowerCase.trim != filter.trim.toLowerCase
      case "="  => value.isDefined && value.get.toString.toLowerCase.trim == filter.trim.toLowerCase
      case ">=" | "<=" | ">" | "<" =>
        value.isDefined && value.get.toString.toLowerCase.trim.contains(filter.toLowerCase.trim)
    }
  }

}
