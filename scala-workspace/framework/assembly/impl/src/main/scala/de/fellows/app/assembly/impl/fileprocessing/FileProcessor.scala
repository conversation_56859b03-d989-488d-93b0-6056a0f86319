package de.fellows.app.assembly.impl.fileprocessing

import com.lightbend.lagom.scaladsl.persistence.PersistentEntityRef
import de.fellows.app.assembly.impl.entities._
import de.fellows.app.assembly.impl.fileprocessing.FileValidator.NULL_VALIDATOR
import de.fellows.app.assemby.api.OriginalFile
import de.fellows.utils.collaboration.TimelineCommand
import de.fellows.utils.internal.File
import de.fellows.utils.internal.FileReader.withResource
import de.fellows.utils.logging.StackrateLogger
import de.fellows.utils.playutils.UploadError
import de.fellows.utils.{FilePath, HashUtils}
import org.apache.commons.compress.archivers.sevenz.SevenZFile
import org.apache.commons.compress.archivers.{ArchiveEntry, ArchiveException, ArchiveInputStream, ArchiveStreamFactory}
import org.apache.commons.compress.compressors.CompressorStreamFactory
import org.apache.commons.compress.utils.IOUtils
import play.api.libs.json.Json
import org.apache.commons.compress.utils.{FileNameUtils, IOUtils}

import java.io.{BufferedInputStream, FileInputStream, FileOutputStream, InputStream}
import java.nio.charset.StandardCharsets
import java.nio.file.{Files, Path}
import java.util.zip.{ZipEntry, ZipFile, ZipInputStream}
import scala.concurrent.{ExecutionContext, Future}
import scala.sys.process.Process
import scala.util.{Failure, Success, Try, Using}

class InternalFileProcessor(
    addWarning: UploadError => Unit
)(implicit logger: StackrateLogger) {

  // These files are recognized as archives, but we don't want to extract them
  private val blacklistedZipEndings = Seq(
    "aar",
    "apk",
    "docx",
    "epub",
    "ipa",
    "jar",
    "kmz",
    "maff",
    "odp",
    "ods",
    "odt",
    "pk3",
    "pk4",
    "pptx",
    "usdz",
    "vsdx",
    "xlsx",
    "xpi"
  )

  private def is7z(ref: FilePath, magic: Seq[Int]): Boolean =
    hasHeader(magic, InternalFileProcessor.SZIP)

  private val isf = new ArchiveStreamFactory()

  /** detects whether a file is an archive. this recognizes plain archives (like tar) as well as compressed archives (like tar.gz)
    * @param ref
    * @return
    */
  private def isArchive(ref: FilePath): Boolean =
    if (blacklistedZipEndings.exists(e => ref.filename.endsWith(s".${e}"))) {
      false
    } else {
      withResource(new BufferedInputStream(new FileInputStream(ref.toJavaFile))) { is =>
        try {
          val compressed = Try(CompressorStreamFactory.detect(is))
          val archived   = Try(ArchiveStreamFactory.detect(is))

          (compressed orElse archived) match {
            case Failure(exception) => false
            case Success(value)     => value != ArchiveStreamFactory.SEVEN_Z
          }
        } catch {
          case e: ArchiveException => false
        }
      }
    }

  private def doAddFile(ref: FilePath, level: Int): Option[FilePath] =
    (try {
      val magic = withResource(new FileInputStream(ref.toJavaFile)) { fis =>
        var index           = 0
        var bytes: Seq[Int] = Seq()
        while (fis.available() > 0 && index < 10) {
          bytes = bytes :+ fis.read()
          index += 1
        }
        bytes
      }

      var deleteFile   = false
      val isFirstLevel = level == 0
      val isNested     = !isFirstLevel

      if (ref.toJavaFile.isFile && ref.toJavaFile.length() > 0) {
        val result =
          if (isZip(ref, magic)) {
            deleteFile = isNested
            (Try(handleZipFile(ref, level = level)) orElse Try(handleZipWithoutStream(ref, level = level))).get
          } else if (isRar(ref, magic)) {
            deleteFile = isNested
            handleRarFile(ref, level = level)
          } else if (is7z(ref, magic)) {
            deleteFile = isNested
            handle7z(ref, level = level)
          } else if (isArchive(ref)) {
            deleteFile = isNested
            handleCompressedFile(ref, level = level)
          } else {
            handleDirectFile(ref)
          }

        // delete if we're handling nested zip files
        if (deleteFile) {
          ref.toJavaFile.delete()
        }

        result
      } else {
        None
      }
    } catch {
      case e: Throwable =>
        logger.error(s"failed to add file ${ref}", e)
        None
    })

  private def isRar(ref: FilePath, magic: Seq[Int]) =
    hasHeader(magic, InternalFileProcessor.RAR1) ||
      hasHeader(magic, InternalFileProcessor.RAR5)

  private def hasHeader(actual: Seq[Int], expected: Seq[Int]) =
    actual.length >= expected.length && actual.slice(0, expected.length).equals(expected)

  private def isZip(ref: FilePath, magic: Seq[Int]) =
    // blacklist the unwanted filetypes that are based on zips and therefore contain the magic header
    // according to wikipedia

    if (blacklistedZipEndings.exists(e => ref.filename.endsWith(s".${e}"))) {
      false
    } else {
      hasHeader(magic, InternalFileProcessor.ZIP)
    }

  def addFile(ref: FilePath): Option[FilePath] =
    doAddFile(ref, 0)

  def clean(ref: FilePath): FilePath =
    ref.copy(filename = FileProcessor.clean(ref.filename))

  private def handleDirectFile(r: FilePath): Option[FilePath] = {
    val ref = clean(r)
    logger.info(s"add file ${ref} to assembly")

    Some(ref)
  }

  private def illegal(name: String): Boolean =
    name.startsWith("__MACOSX") ||
      name.split("/").exists(_.startsWith(".")) // hidden folder or file

  private def illegal(entry: ArchiveEntry): Boolean =
    illegal(entry.getName)

  private def illegal(entry: ZipEntry): Boolean =
    illegal(entry.getName)

  private def createEntryPath(folder: Path, name: String): Option[(String, String, Path)] = {
    val splitPath = name.split("/")
    val entryName = FileProcessor.clean(splitPath.last)
    val subPath   = splitPath.dropRight(1).mkString("/")

    val entryFile = folder.resolve(name)

    if (entryFile.toFile.exists()) {
      addWarning(UploadError.FileAlreadyExists)
      None
    } else {
      Some(subPath, entryName, entryFile)
    }
  }

  private def handle7z(ref: FilePath, prefix: String = "", level: Int = 0): Option[FilePath] =
    if (level <= 2) {
      val unzippedPath = createExctractedArchiveFolder(ref)

      val unzippedFolder = unzippedPath.toJavaPath
      unzippedFolder.toFile.mkdirs()

      withResource(new SevenZFile(ref.toJavaFile)) { zf =>
        var entry = zf.getNextEntry

        while (entry != null) {
          if (!entry.isDirectory && !illegal(entry.getName)) {
            val fileName = s"${entry.getName}".split("/").last

            val extractedPath = unzippedPath.copy(filename = fileName)

            withResource(new FileOutputStream(extractedPath.toJavaFile)) { out =>
              val content: Array[Byte] = new Array[Byte](entry.getSize.asInstanceOf[Int])
              zf.read(content, 0, content.length)
              out.write(content)
            }

            doAddFile(extractedPath, level + 1)
          }

          entry = zf.getNextEntry
        }
      }

      Some(unzippedPath)
    } else {
      None
    }

  /** handle a compressed file. this may be:
    * - a compressed file (like .gz or .z). This will be directly uncompressed without the suffix
    * - an archive file (like .tar) - this will be unpacked
    * - a compressed archive file (like .tar.gz) - this will be unpacked and decompressed
    * @return
    */
  private def handleCompressedFile(ref: FilePath, prefix: String = "", level: Int = 0): Option[FilePath] = {

    def wrapArchiveStream(s: InputStream): ArchiveInputStream =
      new ArchiveStreamFactory().createArchiveInputStream(s)

    def wrapCompressionStream(s: InputStream): InputStream =
      Try(CompressorStreamFactory.detect(s)) match {
        case Failure(exception) =>
          s
        case Success(value) =>
          new BufferedInputStream(new CompressorStreamFactory().createCompressorInputStream(value, s))
      }

    if (level <= 2) {

      withResource(new BufferedInputStream(new FileInputStream((ref.toJavaFile)))) { is =>
        val compressed = Try(CompressorStreamFactory.detect(is))

        val wrappedCompression =
          if (compressed.isSuccess) {
            wrapCompressionStream(is)
          } else {
            is
          }

        val archived = Try(ArchiveStreamFactory.detect(wrappedCompression))
        logger.info(s"handle compressed file ${ref} compressed: ${compressed}, archived: ${archived}")

        if (archived.isSuccess) {
          val i = wrapArchiveStream(wrappedCompression)
          Some(unpackArchive(ref, level, i))
        } else {
          Some(decompressFile(ref, wrappedCompression))
        }
      }

    } else {
      None
    }
  }

  private def decompressFile(ref: FilePath, wrappedCompression: InputStream) = {
    val decompressedFile = decompressedFileName(ref)

    withResource(new FileOutputStream(decompressedFile.toJavaFile)) { os =>
      IOUtils.copy(wrappedCompression, os)
    }

    decompressedFile
  }

  private def decompressedFileName(ref: FilePath) = {
    val split = ref.filename.split("\\.")
    val newFileName =
      if (split.length > 1) {
        split.dropRight(1).mkString(".")
      } else {
        split.mkString(".") + ".decompressed"
      }
    ref.copy(filename = s"${newFileName}")
  }

  private def unpackArchive(ref: FilePath, level: Int, i: ArchiveInputStream): FilePath = {
    val unzippedPath: FilePath = getUnzipPath(ref)
    var entry: ArchiveEntry    = null
    entry = i.getNextEntry
    while (entry != null) {
      if (i.canReadEntryData(entry)) {
        val (subPath, fileName) = FilePath.splitSubPath(entry.getName)
        val newSubPath = (unzippedPath.subPath.getOrElse(Seq()) ++ subPath.getOrElse(Seq())) match {
          case x if x.isEmpty => None
          case x              => Some(x)
        }

        if (!illegal(entry)) {
          if (entry.isDirectory) {
            // immediately create the folder. this will also create empty folders.
            // this is crucial, since archives like ODB rely on a file structure, even if the folders are empty
            val folderPath = unzippedPath.copy(subPath = newSubPath, filename = "").sub(fileName)
            folderPath.createParentDir()
          } else {

            val extractedPath = unzippedPath.copy(subPath = newSubPath, filename = fileName)
            extractedPath.createParentDir() // this should already be created, but just to be sure we'll check
            withResource(Files.newOutputStream(extractedPath.toJavaPath)) { os =>
              IOUtils.copy(i, os);
            }

            doAddFile(extractedPath, level + 1)
          }
        }
      }
      entry = i.getNextEntry
    }

    unzippedPath
  }

  private def getUnzipPath(ref: FilePath) = {
    val unzippedPath = createExctractedArchiveFolder(ref)

    val unzippedFolder = unzippedPath.toJavaPath
    unzippedFolder.toFile.mkdirs()
    unzippedPath
  }

  private def levelGuard[A](level: Int, default: A)(p: => A): A =
    if (level <= 2) {
      p
    } else {
      default
    }

  private def handleRarFile(ref: FilePath, prefix: String = "", level: Int = 0): Option[FilePath] =
    levelGuard[Option[FilePath]](level, None) {
      val unzippedPath = createExctractedArchiveFolder(ref)

      val unzippedFolder = unzippedPath.toJavaPath
      unzippedFolder.toFile.mkdirs()

      val origFiles = InternalFileProcessor.listAll(unzippedFolder) :+ ref.toJavaPath.toAbsolutePath

      val res = Process(
        s"""unrar x -o+ "${ref.toJavaPath.toAbsolutePath.toString}" "${unzippedFolder.toAbsolutePath.toString}" """
      ).!

      if (res == 0) {
        // ok
        val newFiles = InternalFileProcessor.listAll(unzippedFolder)
          .filter(p => !origFiles.contains(p))

        logger.warn(s"handling rar file ${ref}, extracted files are ${newFiles}")

        newFiles.flatMap { nf =>
          val r = ref.relativize(nf)
          logger.warn(s"found new file ${nf}, add with path ${r}")
          r
        }.foreach { addedFile =>
          doAddFile(addedFile, level + 1)
        }
      }

      Some(unzippedPath)
    }

  /** try to parse a zip file without using streams.
    * @see https://commons.apache.org/proper/commons-compress/zip.html
    * @return
    */
  private def handleZipWithoutStream(ref: FilePath, level: Int = 0): Option[FilePath] =
    levelGuard[Option[FilePath]](level, None) {
      Using.resource(new ZipFile(ref.toJavaFile, ZipFile.OPEN_READ)) { zip =>
        val unzippedPath = createExctractedArchiveFolder(ref)

        val unzippedFolder = unzippedPath.toJavaPath
        unzippedFolder.toFile.mkdirs()
        logger.info(s"now extracting to $unzippedFolder")

        import scala.jdk.CollectionConverters._

        zip.entries().asScala.foreach { entry =>
          val inputStream = zip.getInputStream(entry)
          writeZipEntry(level, unzippedPath, entry, inputStream)
        }

        Some(unzippedPath)
      }
    }

  def removeEnding(filename: String): String =
    s"$filename-extracted"

  def createExctractedArchiveFolder(ref: FilePath) = {
    val newSub = ref.subPath.getOrElse(Seq()) :+ removeEnding(ref.filename)
    ref.copy(subPath = Some(newSub), filename = "")
  }

  private def handleZipFile(ref: FilePath, prefix: String = "", level: Int = 0): Option[FilePath] = {
    val unzippedPath = createExctractedArchiveFolder(ref)
    levelGuard[Option[FilePath]](level, None) {
      withResource(new FileInputStream(ref.toJavaFile)) { fis =>
        withResource(new ZipInputStream(fis, StandardCharsets.ISO_8859_1)) { zis =>
          val unzippedFolder = unzippedPath.toJavaPath
          unzippedFolder.toFile.mkdirs()
          logger.info(s"now extracting to $unzippedFolder")
          var entry = zis.getNextEntry

          while (entry != null) {
            writeZipEntry(level, unzippedPath, entry, zis)
            zis.closeEntry()
            entry = zis.getNextEntry
          }
        }
      }

      Some(unzippedPath)
    }
  }

  private def writeZipEntry(level: Int, unzippedPath: FilePath, entry: ZipEntry, zis: InputStream): Option[FilePath] =
    if (!illegal(entry)) {
      val buffer     = new Array[Byte](1024)
      val entryFileO = createEntryPath(unzippedPath.toJavaPath, entry.getName)

      val trees = entryFileO.flatMap { entryFileInfo =>
        val (subPath, fileName, entryFile) = entryFileInfo
        entryFile.getParent.toFile.mkdirs()
        val subPathParts = (unzippedPath.subPath.getOrElse(Seq()) ++ subPath.split("/")) match {
          case x if x.isEmpty => None
          case x              => Some(x)
        }

        val extractedPath = unzippedPath.copy(subPath = subPathParts, filename = fileName)
        if (entry.isDirectory) {
          extractedPath.toJavaPath.toFile.mkdirs()
          None
        } else {
          extractedPath.createParentDir()
          val fos = new FileOutputStream(extractedPath.toJavaFile)

          try
            Iterator
              .continually(zis.read(buffer))
              .takeWhile(-1 != _)
              .foreach(read => fos.write(buffer, 0, read))

          finally {
            fos.flush()
            fos.getFD.sync()
            fos.close()
          }

          logger.info(s"created ${extractedPath}")

          Some(doAddFile(extractedPath, level + 1))
        }

      }.toSeq

      Some(unzippedPath)
    } else {
      None
    }
}

object InternalFileProcessor {

  private val ZIP  = Seq(0x50, 0x4b, 0x03, 0x04)
  private val RAR1 = Seq(0x52, 0x61, 0x72, 0x21, 0x1a, 0x07, 0x00)
  private val RAR5 = Seq(0x52, 0x61, 0x72, 0x21, 0x1a, 0x07, 0x01, 0x00)
  private val SZIP = Seq(0x37, 0x7a, 0xbc, 0xaf, 0x27, 0x1c)

  def listAll(p: Path): Seq[Path] =
    Option(p.toFile.listFiles()).map(_.flatMap { f =>
      val thisPath = f.toPath.toAbsolutePath
      listAll(thisPath)
    }).getOrElse(Array()) :+ p
}

sealed trait AddProjectCommand {
  val name: Option[String]
  val files: Seq[AddFile]
}

/** A ODB Step which is a self contained PCB
  * @param name
  *   The name of the ODB Step
  * @param files
  *   The files to add
  */
case class AddODBStep(name: Option[String], override val files: Seq[AddFile]) extends AddProjectCommand

/**
 * Files that are not part of a project that we can detect.
 * @param files
 * @param name
 */
case class AddUnaffiliatedFiles(files: Seq[AddFile], name: Option[String] = None)  extends AddProjectCommand

/** Processes new files to add to an assembly. takes care of extracting and filtering files. An optional
  * [[FileValidator]] can be provided to do additional filtering for each file, but also for the set of processed files.
  * @param assembly
  * @param tcmd
  * @param validator
  *   The optional [[FileValidator]] to do additional filtering.
  * @param logger
  */
class FileProcessor(assembly: BaseAssemblyReference, tcmd: TimelineCommand, validator: Option[FileValidator] = None)(
    implicit logger: StackrateLogger
) {

  val team = assembly.team

  private def processFileTree(adds: FileTree): (Seq[AddFile], Set[UploadError]) = {

    val secondaryProcessor = new SecondaryFileProcessor(team, tcmd, adds)

    logger.info(s"FileTree: \n${secondaryProcessor.dumpFileTree()}")

    val addCommands   = secondaryProcessor.buildAddCommands()
    val originalFiles = addCommands.flatMap(_.files)

    val (genericFiles, projects) = addCommands.partition {
      case _: AddUnaffiliatedFiles => true // These dont have a project we detected, so we just collect them all
      case _                  => false
    }

    // This will only choose one project to add to the assembly.
    // TODO: improve this. Also add a warning somewhere if there are multiple projects
    val projectToUse =
      projects.distinctBy(_.name)
        .headOption

    val uniqueFileAddCommands =
      genericFiles
        .flatMap(_.files)
        .map { addFile =>
          val hash = HashUtils.generateSha256Hash(addFile.path.toJavaFile)
          (addFile, hash)
        }
        .distinctBy(f => (f._1.fileName, f._2))
        .map(_._1)
        .groupBy(_.fileName)
        .toSeq
        .flatMap {
          case (_, files) =>
            files.zipWithIndex.map {
              case (file, idx) if idx == 0 => file
              case (file, idx) =>
                val fileName  = file.fileName
                val baseName  = FileNameUtils.getBaseName(fileName)
                val extension = FileNameUtils.getExtension(fileName)
                val newFileName =
                  if (extension.nonEmpty && baseName.nonEmpty) {
                    s"${baseName}_$idx.${extension}"
                  } else if (extension.isEmpty && baseName.nonEmpty) {
                    s"${baseName}_$idx"
                  } else if (extension.nonEmpty && baseName.isEmpty) {
                    s".${extension}_$idx"
                  } else {
                    s"${fileName}_$idx"
                  }

                file.copy(fileName = newFileName)
            }
        }

    val uniqueAddCommands = uniqueFileAddCommands ++ projectToUse.toSeq.flatMap(_.files)

    if (originalFiles.size != uniqueAddCommands.size) {
      uniqueAddCommands -> Set(UploadError.DuplicateFiles)
    } else {
      uniqueAddCommands -> Set() // TODO: add warnings
    }
  }

  def validateAndProcessFiles(ref: Seq[FilePath]): (Seq[AddFile], Set[UploadError]) = {
    val validated                                   = validateFiles(ref)
    val validationErrors: Set[UploadError]          = validated._2
    val processed: (Seq[AddFile], Set[UploadError]) = processFileTree(validated._1)
    val processingErrors                            = processed._2

    validator.getOrElse(NULL_VALIDATOR).validateFiles(processed._1) match {
      case Some(value) => throw new ValidationException(value)
      case None =>
        processed._1 -> (validationErrors ++ processingErrors)
    }
  }

  private def validateFiles(ref: Seq[FilePath]): (FileTree, Set[UploadError]) = {
    val warningsBuilder = Set.newBuilder[UploadError]

    val _proc = new InternalFileProcessor(
      addWarning = error => warningsBuilder += error
    )

    val filetree = ref.flatMap(fp => _proc.addFile(fp))

    FileTree(filetree) -> warningsBuilder.result()
  }

  def addFileCommands(ref: Seq[AddFile], entity: PersistentEntityRef[AssemblyCommand])(implicit
      ectx: ExecutionContext
  ): Future[Option[FilesAdded]] =
    entity.ask(AddFiles(team, ref, tcmd))
      .map(_.response)

  def validateAndAddFiles(
      ref: Seq[FilePath],
      entity: PersistentEntityRef[AssemblyCommand],
      original: Boolean
  )(implicit ectx: ExecutionContext): Future[(Seq[File], Set[UploadError])] = {
    val fileAdds = validateAndProcessFiles(ref)

    for {
      adds <- addFileCommands(fileAdds._1, entity)
        .map(_.toSeq.flatMap(_.file) -> fileAdds._2)
      origAdds <-
        if (original) {
          entity.ask(AddOriginalFiles(
            assembly.team,
            assembly.eid,
            ref.map(f => OriginalFile(f.filename, f)),
            tcmd
          ))
        } else {
          Future.successful(adds)
        }
    } yield adds
  }
}

object FileProcessor {
  def clean(ref: String): String =
    ref
      .replace("(", "")
      .replace(")", "")
      .replace("[", "")
      .replace("]", "")
      .replace("#", "_")
      .replace("?", "_")
      .replace("&", "_")
//      .replace("+", "_")
      .replace(" ", "_")
      .map { c =>
        val correctAscii       = (c <= 128) && (c >= 7)
        val extendedAsciiUpper = (c >= 192 && c <= 221 && c != 215)
        val extendedAsciiLower = (c >= 223 && c <= 255 && c != 247)

        if (correctAscii || extendedAsciiLower || extendedAsciiUpper) {
          c
        } else {
          '_'
        }
      }
}
