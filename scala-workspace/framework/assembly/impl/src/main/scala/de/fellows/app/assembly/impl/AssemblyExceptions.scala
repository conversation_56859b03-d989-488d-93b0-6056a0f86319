package de.fellows.app.assembly.impl

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.utils.JsonFormats.singletonFormat
import de.fellows.utils.communication.ServiceError
import play.api.libs.json.Format

object AssemblyNotFound
    extends ServiceError(1, "Assembly Does Not Exist", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[AssemblyNotFound.type] = singletonFormat(AssemblyNotFound)
}

object NoOpenVersionFound
    extends ServiceError(1, "Assembly has no open versions", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[NoOpenVersionFound.type] = singletonFormat(NoOpenVersionFound)
}

object Unnamed extends ServiceError(1, "Version has no name", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[NoOpenVersionFound.type] = singletonFormat(NoOpenVersionFound)
}

object VersionNotFound extends ServiceError(1, "Version does not exist", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[NoOpenVersionFound.type] = singletonFormat(NoOpenVersionFound)
}

object VersionIsReleased
    extends ServiceError(1, "Version is released already", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[NoOpenVersionFound.type] = singletonFormat(NoOpenVersionFound)
}

class FileExists(f: String)
    extends ServiceError(1, s"Version already has a file named $f", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[NoOpenVersionFound.type] = singletonFormat(NoOpenVersionFound)
}

class FileNotFound(f: String)
    extends ServiceError(1, s"File $f not found", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[NoOpenVersionFound.type] = singletonFormat(NoOpenVersionFound)
}

object HintDoesNotExist
    extends ServiceError(1, s"Hint does not exist in this Version", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[HintDoesNotExist.type] = singletonFormat(HintDoesNotExist)
}

object AssemblyHasOpenVersion
    extends ServiceError(1, "Assembly already has an open version", transportError = TransportErrorCode.NotAcceptable) {
  @transient implicit val format: Format[AssemblyHasOpenVersion.type] = singletonFormat(AssemblyHasOpenVersion)
}

object AssemblyExist extends ServiceError(2, "Assembly Exist") {
  @transient implicit val format: Format[AssemblyExist.type] = singletonFormat(AssemblyExist)
}

object CustomerNotFound
    extends ServiceError(1, "Customer Does Not Exist", transportError = TransportErrorCode.NotFound) {
  @transient implicit val format: Format[AssemblyExist.type] = singletonFormat(AssemblyExist)
}

object AssemblyHasExternalReference
    extends ServiceError(
      1,
      "Cannot change status of Assembly with external reference",
      TransportErrorCode.BadRequest
    ) {
  @transient implicit val format: Format[AssemblyHasExternalReference.type] =
    singletonFormat(AssemblyHasExternalReference)
}

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
