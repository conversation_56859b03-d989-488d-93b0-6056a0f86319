package de.fellows.app.assembly.commons

import com.typesafe.config.Config
import de.fellows.utils.FilePath
import play.api.Logging

import java.nio.file.{Path, Paths}
import java.util.UUID

object AssemblyFiles extends Logging {
  //  lazy val conf = ConfigFactory.load()

  def basePath(implicit conf: Config): String =
    conf.getString("fellows.storage.service")

  def servicePath(service: String)(implicit conf: Config): String =
    conf.getString("fellows.storage.base") + "/" + service

  def getRootAssemblyPath(team: String, assembly: UUID)(implicit conf: Config): Option[Path] = {
    val root     = servicePath("assembly")
    val resource = s"${getLocalResourceFolder(assembly)}"

    val p = FilePath(root, team, resource, "", None,  "").toJavaPath(false)

    val _basePath = Paths.get(basePath)
    val baseCheck = p.toAbsolutePath.startsWith(_basePath)

    val rel = _basePath.relativize(p.toAbsolutePath).toString
    logger.warn(s"construct path from ${root} and ${resource} : $p (base is ${_basePath}). constructed rel: ${rel}")
    val relCheck = rel.contains(team) && rel.contains(assembly.toString)
    logger.warn(s"rel check ${relCheck} baseCheck: ${baseCheck}")
    if (baseCheck && relCheck) {
      Some(p)
    } else {
      None
    }
  }

  def createAssemblyUploadFilePath(team: String, assembly: UUID, version: UUID, filename: String)(implicit
      conf: Config
  ): FilePath =
    createAssemblyResourcePath(team, assembly, version, "upload", filename)

  def createAssemblyResourcePath(
      team: String,
      assembly: UUID,
      version: UUID,
      resourceType: String,
      filename: String,
      subPath: Option[Seq[String]] = None
  )(implicit conf: Config): FilePath = {
    val root     = servicePath("assembly")
    val resource = s"${getLocalResourceFolder(assembly, version)}"
    val base     = s"${resourceType}/"

    val filepath = FilePath(root, team, resource, base, subPath, FilePath.clean(filename))
    filepath.createParentDir()

    filepath
  }

  def getAssemblyBaseFolder(team: String, assembly: UUID)(implicit conf: Config): Path =
    Paths.get(servicePath("assembly"), team, assembly.toString)

  def assemblyResourceFolder(team: String, assembly: UUID, version: UUID)(implicit conf: Config): FilePath = {
    val root     = servicePath("assembly")
    val resource = s"${getLocalResourceFolder(assembly, version)}"
    FilePath(root, team, resource, "", None, "")
  }

  def createAssemblyResourceFolder(team: String, assembly: UUID, version: UUID)(implicit conf: Config): FilePath = {
    val filepath = assemblyResourceFolder(team, assembly, version)
    filepath.createParentDir()
    filepath
  }

  def getLocalResourceFolder(assRef: AssemblyReference): String =
    getLocalResourceFolder(assRef.id, assRef.version)

  def getLocalResourceFolder(assembly: UUID, version: UUID): String =
    s"${assembly.toString}/${version.toString}/"

  def getLocalResourceFolder(assembly: UUID): String =
    s"${assembly.toString}/"

}
