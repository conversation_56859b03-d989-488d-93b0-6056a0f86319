package de.fellows.app.assemby.api

import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.enums.UIStatus
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID


/**
  * information about the share. This information can change.
  * @param uiStatus
  * @param customer
  * @param assignee
  */
case class SharedAssemblyInformation(
    uiStatus: UIStatus,
    customer: Option[UUID],
    assignee: Option[UUID]
) {
}

object SharedAssemblyInformation {
  implicit val format: Format[SharedAssemblyInformation] = Json.format
}

/** A cross-tenant assembly share
  * @param team
  * @param id
  * @param ref
  */
case class SharedAssembly(
    team: String,
    id: UUID,
    ref: AssemblyReference,
    created: Instant,
    information: SharedAssemblyInformation
) {}

object SharedAssembly {
  implicit val format: Format[SharedAssembly] = Json.format
}

case class ShareAssemblyTo(
    team: String,
    customer: Option[UUID]
)

object ShareAssemblyTo {
  implicit val format: Format[ShareAssemblyTo] = Json.format
}
