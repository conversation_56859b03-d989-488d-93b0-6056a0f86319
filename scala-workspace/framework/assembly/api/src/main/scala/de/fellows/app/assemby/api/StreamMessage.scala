package de.fellows.app.assemby.api

import akka.actor.Cancellable
import akka.stream.scaladsl.Source
import de.fellows.app.assembly.commons.AssemblyReference
import de.fellows.app.assemby.api.enums.{HintStatus, UIStatus}
import de.fellows.utils.internal.FileLifecycleStage
import play.api.libs.json.{Format, Json}

import java.time.Instant
import java.util.UUID
import scala.concurrent.duration._

case class StreamMessage(t: String, m: Message)

object StreamMessage {
  implicit val format: Format[StreamMessage] = Json.format

  def pingTicks: Source[StreamMessage, Cancellable] =
    Source.tick(2 seconds, 2 seconds, StreamMessage(t = "ping", m = Ping(Instant.now())))
}

case class StreamMessageV2(t: String, m: Message, ref: Option[AssemblyReference])

object StreamMessageV2 {
  implicit val format: Format[StreamMessageV2] = Json.format
}

sealed trait Message {
  val messageType: String
}

case class Ping(date: Instant) extends Message {
  override val messageType: String = "ping"
}

object Ping {
  implicit val format: Format[Ping] = Json.format[Ping]
}

case class AssemblyAddedMessage(assembly: Assembly) extends Message {
  override val messageType: String = "AssemblyAdded"
}

case class AssemblyUpdatedMessage(assembly: Assembly) extends Message {
  override val messageType: String = "AssemblyUpdated"
}

case class FileLockMessage(assembly: Assembly, locked: Boolean) extends Message {
  override val messageType: String = "FileLockMessage"
}

case class FileLifecycleMessage(assembly: UUID, file: String, lifecycle: FileLifecycleStage) extends Message {
  override val messageType: String = "FileLifecycleMessage"
}

case class VersionLifecycleMessage(assembly: UUID, lifecycle: Seq[AssemblyLifecycleStage]) extends Message {
  override val messageType: String = "VersionLifecycleMessage"
}

case class AssemblyVersionMessage(assembly: Assembly, version: Version) extends Message {
  override val messageType: String = "AssemblyVersionCreated"
}

case class AssemblyUIStateUpdatedMessage(assembly: UUID, version: UIStatus) extends Message {
  override val messageType: String = "AssemblyUIStateUpdatedMessage"
}

case class AssemblyRemovedMessage(id: UUID) extends Message {
  override val messageType: String = "AssemblyRemoved"
}

object AssemblyAddedMessage {
  implicit val format: Format[AssemblyAddedMessage] = Json.format[AssemblyAddedMessage]
}

object AssemblyUpdatedMessage {
  implicit val format: Format[AssemblyUpdatedMessage] = Json.format[AssemblyUpdatedMessage]
}

object AssemblyRemovedMessage {
  implicit val format: Format[AssemblyRemovedMessage] = Json.format[AssemblyRemovedMessage]
}

object FileLockMessage {
  implicit val format: Format[FileLockMessage] = Json.format[FileLockMessage]
}

object FileLifecycleMessage {
  implicit val format: Format[FileLifecycleMessage] = Json.format[FileLifecycleMessage]
}

case class HintCreatedMessage(hint: Hint) extends Message {
  override val messageType: String = "HintCreated"
}

object HintCreatedMessage {
  implicit val format: Format[HintCreatedMessage] = Json.format[HintCreatedMessage]
}

case class HintChangedMessage(hint: UUID, status: HintStatus) extends Message {
  override val messageType: String = "HintChanged"
}

object HintChangedMessage {
  implicit val format: Format[HintChangedMessage] = Json.format[HintChangedMessage]
}

case class FilesAddedMessage(assembly: UUID, version: UUID, file: Seq[File]) extends Message {
  override val messageType: String = "FilesAdded"
}

object FilesAddedMessage {
  implicit val format: Format[FilesAddedMessage] = Json.format[FilesAddedMessage]
}

case class FilesChangedMessage(assembly: UUID, version: UUID, file: Seq[File]) extends Message {
  override val messageType: String = "FileChanged"

}

object FilesChangedMessage {
  implicit val format: Format[FilesChangedMessage] = Json.format[FilesChangedMessage]
}

case class FilePreviewMessage(assembly: UUID, version: UUID, file: File) extends Message {
  override val messageType: String = "FilePreview"

}

object FilePreviewMessage {
  implicit val format: Format[FilePreviewMessage] = Json.format[FilePreviewMessage]
}

case class FileLifecycleMessageV2(
    assembly: UUID,
    version: UUID,
    file: String,
    lifecycles: Seq[FileLifecycleStage]
) extends Message {
  override val messageType: String = "FileLifecycleMessage"
}

object FileLifecycleMessageV2 {
  implicit val format: Format[FileLifecycleMessageV2] = Json.format[FileLifecycleMessageV2]
}

case class AssemblyPreviewMessage(assembly: UUID, preview: String) extends Message {
  override val messageType: String = "AssemblyPreview"
}

object AssemblyPreviewMessage {
  implicit val format: Format[AssemblyPreviewMessage] = Json.format[AssemblyPreviewMessage]
}

object AssemblyVersionMessage {
  implicit val format: Format[AssemblyVersionMessage] = Json.format[AssemblyVersionMessage]
}

object AssemblyUIStateUpdatedMessage {
  implicit val format: Format[AssemblyUIStateUpdatedMessage] = Json.format[AssemblyUIStateUpdatedMessage]
}

object VersionLifecycleMessage {
  implicit val format: Format[VersionLifecycleMessage] = Json.format[VersionLifecycleMessage]
}

object Message {
  implicit val format: Format[Message] = Json.format
}

// Force image rebuild TODO remove
// see https://luminovo.slack.com/archives/C02H1BV061F/p1726841676800809?thread_ts=1726825038.579809&cid=C02H1BV061F
