package de.fellows.app.assemby.api

import de.fellows.utils.internal.{HistoricLifecycleState, LifecycleStageStatus, StageStatusName}
import play.api.libs.json.{Format, Json}

object IndividualAssemblyLifecycleStage {

  implicit val format: Format[IndividualAssemblyLifecycleStage] = Json.format[IndividualAssemblyLifecycleStage]

  def hasState(
      lcs: Seq[IndividualAssemblyLifecycleStage],
      name: AssemblyLifecycleStageName,
      statusName: StageStatusName
  ): Boolean =
    lcs.find(_.name == name).exists(lc => lc.status.name == statusName)

  def allLifecyclesCompleted(lcs: Seq[IndividualAssemblyLifecycleStage]): Boolean =
    AssemblyLifecycleStageName
      .values
      .forall(name => hasState(lcs, name, StageStatusName.Success))

  def lifecycleString(newVersionCycles: Seq[IndividualAssemblyLifecycleStage]): Seq[String] =
    newVersionCycles.map(lc => s"${lc.name}: ${lc.status.name} ${lc.status.percent.getOrElse("-")}%")

  def waiting(name: AssemblyLifecycleStageName): IndividualAssemblyLifecycleStage =
    IndividualAssemblyLifecycleStage(name, LifecycleStageStatus.emptyWaiting, None)

  final val DFM                             = "dfm"
  final val RECONCILIATION                  = "reconciliation"
  final val ANALYSIS                        = "analysis"
  final val FILES                           = "files"
  final val FILEANALYSIS                    = "fileanalysis"
  final val LAYERSTACK                      = "layerstack"
  final val RENDER                          = "render"
  final val SPECIFICATION_RENDER            = "specification-render"
  final val RECONCILED_SPECIFICATION_RENDER = "reconciled_specification-render"
  final val INITIALIZATION                  = "initialization"
  final val PRODUCTION_ANALYSIS             = "production-analysis"

  object Aggregates {
    final val COMPOSITION = "composition"
    final val DFMANALYSIS = "dfmanalysis"
    final val MAIN        = "main"
  }
}

case class IndividualAssemblyLifecycleStage(
    name: AssemblyLifecycleStageName,
    status: LifecycleStageStatus,
    history: Option[Seq[HistoricLifecycleState]] = None
) extends de.fellows.utils.internal.AssemblyLifecycleStage {
  val desc = s"${name}, ${status.name}, ${status.percent.getOrElse(-1)}"
}

object SuccessAssemblyLifecycle {
  def unapply(arg: Option[IndividualAssemblyLifecycleStage]): Option[AssemblyLifecycleStageName] =
    arg match {
      case Some(value) if value.status.name == StageStatusName.Success => Some(value.name)
      case _                                                           => None
    }
}
