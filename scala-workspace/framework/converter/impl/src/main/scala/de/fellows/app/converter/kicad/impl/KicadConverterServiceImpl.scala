package de.fellows.app.converter.kicad.impl

import akka.Done
import akka.actor.ActorSystem
import com.lightbend.lagom.scaladsl.api.ServiceCall
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import de.fellows.app.assemby.api.{AssemblyService, FileAdd, FileState, FilesAdd}
import de.fellows.app.converter.kicad.api.{ConvertFileRequest, KicadConverterService}
import de.fellows.utils.internal.{LifecycleStageStatus, StageStatusName}
import de.fellows.utils.{FilePath, Tracing}
import play.api.Logging

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class KicadConverterServiceImpl(
    actorSystem: ActorSystem,
    ass: AssemblyService
) extends KicadConverterService with Logging {

  implicit val ec: ExecutionContext = actorSystem.dispatcher

  override def convertFiles(team: String, assembly: UUID, version: UUID): ServiceCall[ConvertFileRequest, Done] = {
    ServerServiceCall { convertRequest =>
      val time = System.currentTimeMillis()

      Future.sequence(
        convertRequest
          .files
          .filter(fd => Converter.MIMES.contains(fd.fType.fileType))
          .map { file =>
            logger.info(s"start converting ${file.name}")
            ass._updateFileLifecycle(
              team = team,
              assembly = assembly,
              version = Some(version),
              file = file.name,
              lcname = "render",
              time = Some(time)
            ).invoke(
              LifecycleStageStatus.emptyProgress
            ).map(_ => file)
          }
      ).flatMap { fds =>
        val adds = fds.map(fd => fd -> Converter.convert(fd))
          .map { conventry =>
            val orig = conventry._1

            conventry._2 match {
              case Failure(e) => orig -> Failure(e)
              case Success(files) =>
                orig -> Success(files.map { file =>
                  FileAdd(
                    name = file.f.getName,
                    file = FilePath(
                      fsroot = file.f.getParent,
                      team = "",
                      resource = "",
                      base = "",
                      subPath = None,
                      filename = file.f.getName
                    ),
                    fileType = file.ftype,
                    overwrite = false
                  )
                })
            }
          }

        Future.sequence(adds.map { e =>
          val orig = e._1
          logger.info(s"handle converted file: ${orig.name}: ${e._2}")
          e._2 match {
            case Failure(exception: ConversionException) =>
              ass._updateFileLifecycle(
                team = team,
                assembly = assembly,
                version = Some(version),
                file = orig.name,
                lcname = "render",
                time = Some(time)
              ).invoke(LifecycleStageStatus(
                name = StageStatusName.Success, // TODO
                messages = exception.getMessage +: exception.detail.toSeq,
                percent = None
              ))
            case Failure(exception) =>
              exception.printStackTrace()
              ass._updateFileLifecycle(
                team = team,
                assembly = assembly,
                version = Some(version),
                file = orig.name,
                lcname = "render",
                time = Some(time)
              ).invoke(LifecycleStageStatus.emptySuccess)
            case Success(fadds) =>
              for {
                unlock <- ass._approveFileMatchesWithState(
                  team = team,
                  assembly = assembly,
                  version = Some(version),
                  initial = None
                ).invoke(FileState(false))
                  .map(_ => true)
                  .recover { e =>
                    Tracing.error("Failed to unlock files", e)
                    false
                  }

                add <- ass._addFiles(
                  team = team,
                  assembly = assembly,
                  version = Some(version),
                  fullProject = true,
                  original = Some(false) // the converted files are not original files
                ).invoke(FilesAdd(
                  fadds,
                  None
                ))
                  .map(_ => true)
                  .recover { e =>
                    Tracing.error("Failed to add new files", e)
                    false
                  }

                lc <- ass._updateFileLifecycle(
                  team = team,
                  assembly = assembly,
                  version = Some(version),
                  file = orig.name,
                  lcname = "render",
                  time = Some(time)
                ).invoke(LifecycleStageStatus.emptySuccess)
              } yield {
                logger.info(s"unlock: $unlock, add $add, lc: $lc")
                fadds.foreach { fa =>
                  // delete the temporary conversion files
                  fa.file.toJavaFile.delete()
                }
              }
          }
        })
          .recover {
            case e: Throwable =>
              Tracing.error("Failed to convert files", e)
              e.printStackTrace()
              Done
          }
      }
        .map(_ => Done)
    }
  }
}
