include "main-application.conf"

play.application.loader = de.fellows.app.converter.kicad.impl.KicadConverterLoader

converter.cassandra.keyspace = ${fellows.persistence.rootKeyspace}converter

cassandra-journal {
  keyspace = ${converter.cassandra.keyspace}
}

cassandra-snapshot-store {
  keyspace = ${converter.cassandra.keyspace}
}

lagom.persistence.read-side {
  cassandra.keyspace = ${converter.cassandra.keyspace}read
}

akka.management.cluster.bootstrap.contact-point-discovery.service-name = "converter"
# fellows.serviceconfig = ${fellows.services.converter}

# akka {
#   cluster.seed-nodes = [
#     "akka://application@"${fellows.serviceconfig.seed}":"${fellows.serviceconfig.remoting}
#   ]
# }

fellows.native.eagle.cam = {
  1 = "example_1_layer.cam",
  2 = "example_2_layer.cam",
  4 = "example_4_layer.cam",
  8 = "example_8_layer.cam",
  10 = "example_10_layer.cam",
  12 = "example_12_layer.cam",
  14 = "example_14_layer.cam",
  16 = "example_16_layer.cam",
}

fellows.storage {
  service = ${fellows.storage.base}/converter
}
