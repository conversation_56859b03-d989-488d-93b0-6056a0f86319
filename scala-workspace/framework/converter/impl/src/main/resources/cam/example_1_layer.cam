{"author": {"email": "<EMAIL>", "name": "S L"}, "description": {"EN": "EAGLE default single layer CAM job."}, "job_prefix": "job_outputs", "output_type": "directory", "outputs": [{"filename_prefix": "CAMOutputs/GerberFiles", "format_specifier": {"decimal": 4, "integer": 3}, "generate_job_file": true, "output_type": "gerber", "outputs": [{"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Copper", "layer": 1, "layer_details": "mixed", "layer_type": "top"}, "filename_format": "%PREFIX/copper_top.gbr", "layers": [1, 17, 18], "name": "Top Copper", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": true, "config": {"file_function": "Profile", "plating": "non-plated"}, "filename_format": "%PREFIX/profile.gbr", "layers": [], "milling": true, "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Soldermask", "index": 1, "layer_type": "top"}, "filename_format": "%PREFIX/soldermask_top.gbr", "layers": [29], "name": "Soldermask Top", "polarity": "positive", "type": "gerber_layer"}, {"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "board_outline": false, "config": {"file_function": "Legend", "index": 1, "layer_type": "top"}, "filename_format": "%PREFIX/silkscreen_top.gbr", "layers": [21, 25], "milling": false, "name": "Silkscreen Top", "polarity": "positive", "type": "gerber_layer"}], "version": "X2"}, {"filename_prefix": "CAMOutputs/DrillFiles", "format_specifier": {"decimal": 3, "integer": 3}, "output_type": "drill", "outputs": [{"advanced_options": {"mirror": false, "offset_x": 0, "offset_y": 0, "rotate": false, "upside_down": false}, "filename_format": "%DRILLPREFIX/drill_%FROM_%TO.xln", "name": "Auto Drill", "type": "autodrills"}]}, {"filename_prefix": "CAMOutputs/Assembly", "output_type": "assembly", "outputs": [{"filename_format": "%ASSEMBLYPREFIX/%N", "list_attribute": true, "list_type": "values", "name": "Bill of Material", "output_format": "csv", "type": "bom"}, {"filename_format": "%ASSEMBLYPREFIX/PnP_%N_%BOARDSIDE", "name": "Pick and Place", "output_format": "csv", "type": "pick_and_place"}]}, {"filename_prefix": "CAMOutputs/DrawingFiles", "output_type": "drawing", "outputs": [{"filename_format": "%DRAWINGPREFIX/%N.dxf", "name": "dxf", "signals_on_individual_layers": false, "type": "DXF"}]}], "timestamp": "2021-10-21T14:48:18", "type": "EAGLE CAM job", "units": "metric", "version": "9.2.0"}