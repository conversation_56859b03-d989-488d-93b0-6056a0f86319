package de.fellows.app.erpnext

import play.api.libs.json.{Format, JsDefined, JsError, JsObject, Json, Reads}

package object api {

  trait ErpDocType {}

  object ErpDocType {
    implicit def reads[T <: ErpDocType](c: Class[T]): Reads[T] = Reads { jsv =>
      jsv \ "doctype" match {
        case JsDefined(value) => JsError()

        case _ => JsError()
      }
    }
  }

  case class ListResponse(data: Seq[JsObject])

  object ListResponse {
    implicit val format: Format[ListResponse] = Json.format[ListResponse]
  }

  case class ObjectContainer(data: JsObject)

  object ObjectContainer {
    implicit val format: Format[ObjectContainer] = Json.format[ObjectContainer]
  }

  case class ERPResponse(
      message: Option[String],
      exc: Option[String],
      exc_type: Option[String]
  )

  object ERPResponse {
    implicit val format: Format[ERPResponse] = Json.format[ERPResponse]
  }

}
