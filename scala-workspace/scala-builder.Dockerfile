ARG BUILDER_IMAGE_NAME
ARG BUILDER_IMAGE_VERSION
FROM ${BUILDER_IMAGE_NAME}:${BUILDER_IMAGE_VERSION} AS builder

ENV USER=non-root-sbt
ENV USER_ID=1000
USER ${USER_ID}
ENV XDG_RUNTIME_DIR=/home/<USER>

COPY --chown=${USER}:${USER} ./docker/_.sbtopts_ ./.sbtopts

COPY --chown=${USER}:${USER} ./build.sbt ./build.sbt
COPY --chown=${USER}:${USER} ./project ./project

RUN sbt update

COPY --chown=${USER}:${USER} . .

RUN sbt "compile; Test / compile"
