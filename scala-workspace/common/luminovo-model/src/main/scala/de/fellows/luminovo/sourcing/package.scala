package de.fellows.luminovo

import akka.http.scaladsl.unmarshalling.FromStringUnmarshaller
import play.api.libs.json.{Json, Reads, Writes}

import java.util.UUID
import scala.util.Try

package object sourcing {
  case class SourcingScenarioId(value: UUID) extends AnyVal {
    override def toString: String = value.toString
  }

  object SourcingScenarioId {
    def fromStringOption(str: String): Option[SourcingScenarioId] =
      Try(SourcingScenarioId(UUID.fromString(str))).toOption

    implicit val sourcingScenarioIdReads: Reads[SourcingScenarioId] =
      Reads.uuidReads.map(SourcingScenarioId.apply)

    implicit val sourcingScenarioIdWrites: Writes[SourcingScenarioId] = Writes { scenarioId =>
      Json.toJson(scenarioId.value)
    }

    implicit val fromStringUnmarshaller: FromStringUnmarshaller[SourcingScenarioId] =
      implicitly[FromStringUnmarshaller[UUID]].map(SourcingScenarioId.apply)
  }
}
