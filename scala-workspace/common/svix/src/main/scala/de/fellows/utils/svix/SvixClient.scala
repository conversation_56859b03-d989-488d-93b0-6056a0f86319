package de.fellows.utils.svix

import com.svix.models.{ApplicationIn, ApplicationOut, MessageIn, MessageOut}
import com.svix.{PostOptions, Svix, SvixOptions}
import com.typesafe.config.Config
import de.fellows.utils.svix.SvixClient.{ApplicationName, EventType}
import play.api.Logging
import play.api.libs.json.{<PERSON><PERSON>, Writes}

import scala.util.{Failure, Success, Try}

class SvixClient private[svix] (
    svix: Option[Svix]
) extends Logging {

  /** Send a Svix-Event to a customer application.
    */
  def send[T](
      tenant: ApplicationName,
      eventType: EventType,
      payload: T,
      idempotency: Option[String] = None
  )(implicit w: Writes[T]): Try[MessageOut] = {
    val messageOut = sendPayload(
      tenant,
      eventType,
      payload,
      idempotency
    )

    messageOut match {
      case Failure(ex) => logger.error(s"Failed to send Svix-Event: ${ex.getMessage}")
      case Success(_)  => ()
    }

    messageOut
  }

  private def sendPayload[T](
      tenant: ApplicationName,
      eventType: EventType,
      payload: T,
      idempotency: Option[String] = None
  )(implicit w: Writes[T]): Try[MessageOut] =
    svix match {
      case None => Failure(new IllegalStateException("SvixClient is not configured"))
      case Some(svix) =>
        Try {
          val app = getSvixApplicationOut(tenant, svix)
          val po  = new PostOptions
          idempotency.foreach(po.idempotencyKey)

          svix.getMessage.create(
            app.getId,
            new MessageIn()
              .eventType(eventType.value)
              .payload(Json.stringify(Json.toJson(payload))),
            po
          )
        }
    }

  private def getSvixApplicationOut(tenant: ApplicationName, svix: Svix): ApplicationOut = {
    val appName = tenant.value

    svix
      .getApplication
      .getOrCreate(new ApplicationIn().name(appName).uid(appName))
  }
}

object SvixClient extends Logging {
  case class ApplicationName(value: String) extends AnyVal
  case class EventType(value: String)       extends AnyVal

  def fromConfig(config: Config): SvixClient =
    Try {
      val endpoint = config.getString("svix.endpoint")
      val token    = config.getString("svix.token")

      val options = new SvixOptions()
      options.setServerUrl(endpoint)
      val svix = new Svix(token, options)

      new SvixClient(Some(svix))
    } match {
      case Failure(exception) =>
        logger.error(s"Failed to create SvixClient", exception)
        new SvixClient(None)
      case Success(value) => value
    }
}
