package de.fellows.utils.svix

import de.fellows.utils.svix.SvixClient.EventType
import enumeratum._
import json.schema.typeHint
import play.api.libs.json.{JsNumber, JsObject, JsString, JsValue, Json, Writes}

import java.time.Instant
import java.util.UUID

/** Represents an Assembly event that is sent to the SVIX API. */
sealed trait AssemblySvixEvent extends SvixEvent {
  def pcb_id: PcbId
  def eventType: EventType
  def created_at: Instant
}

object AssemblySvixEvent {

  implicit val RenderStartedEventWrites: Writes[RenderStartedEvent]       = Json.writes[RenderStartedEvent]
  implicit val RenderUpdateEventWrites: Writes[RenderUpdatedEvent]        = Json.writes[RenderUpdatedEvent]
  implicit val RenderFinishedEventWrites: Writes[RenderFinishedEvent]     = Json.writes[RenderFinishedEvent]
  implicit val AnalysisStartedEventWrites: Writes[AnalysisStartedEvent]   = <PERSON><PERSON>.writes[AnalysisStartedEvent]
  implicit val AnalysisFinishedEventWrites: Writes[AnalysisFinishedEvent] = Json.writes[AnalysisFinishedEvent]
  implicit val AssemblySvixEventWrites: Writes[AssemblySvixEvent] =
    new Writes[AssemblySvixEvent] {
      override def writes(o: AssemblySvixEvent): JsValue = (o match {
        case e: RenderStartedEvent    => Json.toJson(e)
        case e: RenderUpdatedEvent    => Json.toJson(e)
        case e: RenderFinishedEvent   => Json.toJson(e)
        case e: AnalysisStartedEvent  => Json.toJson(e)
        case e: AnalysisFinishedEvent => Json.toJson(e)
      }).as[JsObject] + ("event" -> JsString(o.eventType.value))
    }
}

/** Represents the possible result types for a render finished or analysis finished event.
  *
  * It can be either a Success or a Failure, and in case of a failure,
  * then we display the error message (if we have one)
  */
sealed trait ResultType extends EnumEntry
object ResultType extends Enum[ResultType] {
  val values: IndexedSeq[ResultType] = findValues

  @typeHint[String]
  case object Success extends ResultType
  @typeHint[String]
  case object Failure extends ResultType

  implicit val ResultTypeWrites: Writes[ResultType] = new Writes[ResultType] {
    override def writes(o: ResultType): JsValue = JsString(o.entryName)
  }
}

/** Represents the result of a finished operation. */
final case class Result(status: ResultType, message: Option[String])

object Result {
  implicit val writes: Writes[Result] = Json.writes[Result]

  def success: Result                = Result(ResultType.Success, message = None)
  def error(message: String): Result = Result(ResultType.Failure, Some(message))
}

case class TenantId(value: String) extends AnyVal

case class PcbId(value: UUID) extends AnyVal
object PcbId {
  implicit val writes: Writes[PcbId] = (o: PcbId) => JsString(o.value.toString)
}

case class Percentage(value: Int) extends AnyVal
object Percentage {
  implicit val writes: Writes[Percentage] = (o: Percentage) => JsNumber(o.value)
}

/** Event sent to SVIX when the PCB's render is started.
  * @param pcb_id The PCB's id
  * @param created_at Timestamp of when render was started
  */
final case class RenderStartedEvent(pcb_id: PcbId, created_at: Instant) extends AssemblySvixEvent {
  override def eventType: EventType = EventType("render.started")
}

/** Event sent to SVIX when the PCB's render progress is updated
  * @param pcb_id The PCB's id
  * @param percent Current rendering progress in %
  * @param created_at Timestamp of when render was updated
  */
final case class RenderUpdatedEvent(pcb_id: PcbId, percent: Percentage, created_at: Instant) extends AssemblySvixEvent {
  override def eventType: EventType = EventType("render.update")
}

/** Event sent to SVIX when the PCB's render is finished.
  * @param pcb_id The PCB's id
  * @param result Render result.
  * @param created_at Timestamp of when render was finished
  */
final case class RenderFinishedEvent(pcb_id: PcbId, result: Result, created_at: Instant) extends AssemblySvixEvent {
  override def eventType: EventType = EventType("render.finished")
}

/** Event sent to SVIX when the PCB's DFM analysis is started.
  * @param pcb_id The PCB's id
  * @param created_at Timestamp of when DFM analysis was started
  */
final case class AnalysisStartedEvent(pcb_id: PcbId, created_at: Instant) extends AssemblySvixEvent {
  override def eventType: EventType = EventType("analysis.started")
}

/** Event sent to SVIX when the DFM analysis is finished.
  * @param pcb_id The PCB's id
  * @param result DFM analysis result.
  * @param created_at Timestamp of when DFM analysis was finished
  */
final case class AnalysisFinishedEvent(pcb_id: PcbId, result: Result, created_at: Instant) extends AssemblySvixEvent {
  override def eventType: EventType = EventType("analysis.finished")
}
