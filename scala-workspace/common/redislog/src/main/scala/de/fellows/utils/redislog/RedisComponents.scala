package de.fellows.utils.redislog

import com.lightbend.lagom.scaladsl.persistence.ReadSidePersistenceComponents
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.redislog.jobs.JobBuilder
import redis.clients.jedis.{ClusterCommandObjects, CommandObjects, DefaultJedisClientConfig, HostAndPort, Jedis, JedisCluster, JedisPool}

trait RedisComponents extends ReadSidePersistenceComponents with StackrateLogging {
  lazy val redisHost     = Option(System.getenv("CUSTOM_PARTS_REDIS_HOST")).getOrElse({
    logger.info(s"using default redis host")
    "custom-parts-worker-redis"
  })
  lazy val redisPort     = Option(System.getenv("CUSTOM_PARTS_REDIS_PORT")).map(_.toInt).getOrElse({
    logger.info(s"using default redis port")
    6379
  })


  lazy val jedisCluster = new JedisCluster(new HostAndPort(redisHost, redisPort))

  lazy val commandObjects = new ClusterCommandObjects

  lazy val jobBuilder = new JobBuilder(commandObjects, jedisCluster)

}

trait RedisLogComponents extends RedisComponents {
  lazy val redisLog: RedisLog = new RedisLog(jedisCluster)
}
