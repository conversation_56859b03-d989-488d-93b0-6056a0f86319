package de.fellows.utils.redislog

package object jobs {

  sealed trait QueueType

  case object WaitingQueue extends QueueType {
    override def toString: String = "waiting"
  }
  case object FailedQueue extends QueueType {
    override def toString: String = "failed"
  }
  case object ProgressQueue extends QueueType {
    override def toString: String = "progress"
  }

  case class WorkerId(id: String)

}
