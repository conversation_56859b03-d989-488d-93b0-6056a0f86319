package de.fellows.utils

import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class UUIDUtilsTest extends AnyWordSpec with Matchers {
  "Base64 decoder/encoder" should {
    import UUIDUtils._
    "produce base64 encoded string" in {
      "test".encode should be("dGVzdA")
    }
    "produce base64 decoded string" in {
      "YW5vdGhlciB0ZXN0".decode should be("another test")
    }
  }
}
