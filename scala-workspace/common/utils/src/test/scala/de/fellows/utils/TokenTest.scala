package de.fellows.utils

import de.fellows.utils.security.AuthenticationServiceComposition

object TokenTest extends App{

  AuthenticationServiceComposition.decode("eyJ0eXAiOiJKV1QiLCJhbGciOiJITUQ1In0.eyJleHAiOjE2MTk3NDA4MDAsInRlYW0iOiJjcSIsInNlcnZpY2UiOiJhc3NlbWJseSIsInJlc291cmNlIjoiODMzZjNmYzEtOGQzMi00YjVmLTlmNTAtMmE4ZGMyYTQ5ZTI5L2M4MzA0ZGU5LWI5MGEtNDZmOS1hZTlkLWRmMDFiNDUyZTc0MC8iLCJwYXRoIjoic3BlY2lmaWNhdGlvbnMvZDQwMThiOWItMjJlYS00OWYwLWEwYTYtNjA5YjI0OWJmYTc5L3NwZWNpZmljYXRpb24tcHJldmlldy53ZWJwIn0.Ma1uUa9veMHhqmsMUOgrHg")

}
