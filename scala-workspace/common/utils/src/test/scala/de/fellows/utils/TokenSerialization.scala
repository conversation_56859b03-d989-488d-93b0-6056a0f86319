package de.fellows.utils

import de.fellows.utils.security.{Auth0TokenContent, FileSecurityClaim, FileTokenContent, TokenContent}
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import play.api.libs.json.Json

class TokenSerialization extends AsyncWordSpec with BeforeAndAfterAll with Matchers {

  "FileToken" should {
    "contain query param" in {
      val c: FileTokenContent = FileTokenContent("1", Seq(FileSecurityClaim("2", "3", "4")))

      Json.toJson(c).toString() should be(
        """{"team":"1","claims":[{"service":"2","resource":"3","path":"4"}],"isQueryParameter":true}"""
      )
    }
  }

  "BearerToken" should {
    "not contain query param" in {
      val c: TokenContent = TokenContent(UUIDUtils.nil, "1", "2", Some("3"), Some("4"), false, Seq())

      Json.toJson(c).toString() should be(
        """{"userId":"00000000-0000-0000-0000-000000000000","team":"1","username":"2","email":"3","share":"4","isRefreshToken":false,"claims":[],"isQueryParameter":false}"""
      )
    }
  }

  val auth0token = Auth0TokenContent(
    sub = "1",
    iss = "2",
    scope = "3",
    azp = "4",
    permissions = Seq("5", "6"),
    tenant = "7",
    analyticsId = "8"
  )
  "Auth0 token" should {
    "serialize correctly" in {

      val serialized = (Json.toJson(auth0token).toString())

      serialized should be(
        """{"sub":"1","iss":"2","scope":"3","azp":"4","permissions":["5","6"],"tenant":"7","analytics_id":"8"}"""
      )
    }

    "deserialize correctly" in {
      val token = Json.parse(
        """{"sub":"1","iss":"2","scope":"3","azp":"4","permissions":["5","6"],"tenant":"7","analytics_id":"8"}"""
      ).as[Auth0TokenContent]
      token should be(auth0token)
    }
  }

}
