package de.fellows.utils

import de.fellows.utils.Region.Germany
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import play.api.libs.json.Json

class RegionSpec extends AnyWordSpec with Matchers {

  "Region" should {
    "deserialize" in {

      Json.prettyPrint(Json.toJson(Region.Germany: Region)(Region.nameFormat)) should be("\"Germany\"")

      Json.parse("\"Germany\"").as[Region](Region.nameFormat) should be(Germany)

    }
  }

}
