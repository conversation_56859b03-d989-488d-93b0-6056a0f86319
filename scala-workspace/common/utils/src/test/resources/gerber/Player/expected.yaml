files:
  - filename: "coturn-B_Adhes.gbr"
    expectedFileType: "Unknown"
    expectedCategory: "gerber"
  - filename: "coturn-B_Cu.gbr"
    expectedFileType: "CopperBottom"
    expectedCategory: "gerber"
  - filename: "coturn-B_Fab.gbr"
    expectedFileType: "Mechanical"
    expectedCategory: "mechanical"
  - filename: "coturn-B_Mask.gbr"
    expectedFileType: "SoldermaskBottom"
    expectedCategory: "gerber"
  - filename: "coturn-bottom-pos.csv"
    expectedFileType: null
    expectedCategory: null
  - filename: "coturn-B_Paste.gbr"
    expectedFileType: "PasteBottom"
    expectedCategory: "gerber"
  - filename: "coturn-B_SilkS.gbr"
    expectedFileType: "SilkscreenBottom"
    expectedCategory: "gerber"
  - filename: "coturn.drl"
    expectedFileType: "Drill"
    expectedCategory: "mechanical"
  - filename: "coturn-Edge_Cuts.gbr"
    expectedFileType: "Outline"
    expectedCategory: "mechanical"
  - filename: "coturn-F_Adhes.gbr"
    expectedFileType: "Unknown"
    expectedCategory: "gerber"
  - filename: "coturn-F_Cu.gbr"
    expectedFileType: "CopperTop"
    expectedCategory: "gerber"
  - filename: "coturn-F_Fab.gbr"
    expectedFileType: "Mechanical"
    expectedCategory: "mechanical"
  - filename: "coturn-F_Mask.gbr"
    expectedFileType: "SoldermaskTop"
    expectedCategory: "gerber"
  - filename: "coturn-F_Paste.gbr"
    expectedFileType: "PasteTop"
    expectedCategory: "gerber"
  - filename: "coturn-F_SilkS.gbr"
    expectedFileType: "SilkscreenTop"
    expectedCategory: "gerber"
  - filename: "coturn-In1_Cu.gbr"
    expectedFileType: "CopperMid"
    expectedCategory: "gerber"
  - filename: "coturn-In2_Cu.gbr"
    expectedFileType: "CopperMid"
    expectedCategory: "gerber"
  - filename: "coturn.pdf"
    expectedFileType: null
    expectedCategory: null
  - filename: "coturn-sorted-by-value.csv"
    expectedFileType: null
    expectedCategory: null
  - filename: "coturn-sortet-by-ref.csv"
    expectedFileType: null
    expectedCategory: null
  - filename: "coturn-top-pos.csv"
    expectedFileType: null
    expectedCategory: null
  - filename: "ibom.html"
    expectedFileType: null
    expectedCategory: null
