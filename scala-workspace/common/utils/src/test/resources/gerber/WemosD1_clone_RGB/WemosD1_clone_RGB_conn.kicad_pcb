(kicad_pcb (version 20171130) (host pcbnew "(5.1.9)-1")

  (general
    (thickness 1.6)
    (drawings 42)
    (tracks 284)
    (zones 0)
    (modules 23)
    (nets 38)
  )

  (page A4)
  (title_block
    (title Esp12_WS2812B_Controller)
    (date 2021-02-23)
  )

  (layers
    (0 F.Cu signal)
    (31 B.Cu signal)
    (32 <PERSON>.<PERSON>hes user)
    (33 <PERSON>.<PERSON>hes user)
    (34 <PERSON><PERSON>e user)
    (35 <PERSON><PERSON>Paste user)
    (36 <PERSON><PERSON>SilkS user)
    (37 <PERSON><PERSON>SilkS user)
    (38 B.Mask user)
    (39 F.Mask user)
    (40 Dwgs.User user)
    (41 Cmts.User user)
    (42 Eco1.User user)
    (43 Eco2.User user)
    (44 Edge.Cuts user)
    (45 Margin user)
    (46 B.CrtYd user)
    (47 F.CrtYd user)
    (48 B.Fab user)
    (49 F.Fab user)
  )

  (setup
    (last_trace_width 0.25)
    (user_trace_width 0.5)
    (user_trace_width 1.5)
    (user_trace_width 2.5)
    (trace_clearance 0.2)
    (zone_clearance 0.508)
    (zone_45_only no)
    (trace_min 0.2)
    (via_size 0.8)
    (via_drill 0.4)
    (via_min_size 0.4)
    (via_min_drill 0.3)
    (uvia_size 0.3)
    (uvia_drill 0.1)
    (uvias_allowed no)
    (uvia_min_size 0.2)
    (uvia_min_drill 0.1)
    (edge_width 0.05)
    (segment_width 0.2)
    (pcb_text_width 0.3)
    (pcb_text_size 1.5 1.5)
    (mod_edge_width 0.12)
    (mod_text_size 1 1)
    (mod_text_width 0.15)
    (pad_size 1.524 1.524)
    (pad_drill 0.762)
    (pad_to_mask_clearance 0)
    (aux_axis_origin 0 0)
    (visible_elements 7FFFFFFF)
    (pcbplotparams
      (layerselection 0x010fc_ffffffff)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (excludeedgelayer false)
      (linewidth 0.100000)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin true)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (padsonsilk true)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "./gerber"))
  )

  (net 0 "")
  (net 1 GND)
  (net 2 +5V)
  (net 3 +3V3)
  (net 4 LED_DATA)
  (net 5 "Net-(J3-Pad3)")
  (net 6 "Net-(J3-Pad4)")
  (net 7 "Net-(J3-Pad2)")
  (net 8 "Net-(J4-Pad6)")
  (net 9 "Net-(J4-Pad5)")
  (net 10 "Net-(J4-Pad4)")
  (net 11 "Net-(J4-Pad2)")
  (net 12 /GPIO16)
  (net 13 /GPIO14)
  (net 14 /GPIO13)
  (net 15 /GPIO12)
  (net 16 /GPIO5)
  (net 17 "Net-(J5-Pad4)")
  (net 18 "Net-(J5-Pad3)")
  (net 19 "Net-(J5-Pad2)")
  (net 20 "Net-(R2-Pad1)")
  (net 21 "Net-(R3-Pad1)")
  (net 22 "Net-(R4-Pad1)")
  (net 23 "Net-(SW1-Pad2)")
  (net 24 "Net-(SW2-Pad1)")
  (net 25 "Net-(U3-Pad12)")
  (net 26 "Net-(U3-Pad11)")
  (net 27 "Net-(U3-Pad10)")
  (net 28 "Net-(U3-Pad9)")
  (net 29 "Net-(C4-Pad1)")
  (net 30 "Net-(C5-Pad1)")
  (net 31 "Net-(U4-Pad15)")
  (net 32 "Net-(U4-Pad14)")
  (net 33 "Net-(U4-Pad13)")
  (net 34 "Net-(U4-Pad12)")
  (net 35 "Net-(U4-Pad11)")
  (net 36 "Net-(U4-Pad10)")
  (net 37 "Net-(U4-Pad9)")

  (net_class Default "This is the default net class."
    (clearance 0.2)
    (trace_width 0.25)
    (via_dia 0.8)
    (via_drill 0.4)
    (uvia_dia 0.3)
    (uvia_drill 0.1)
    (add_net +3V3)
    (add_net +5V)
    (add_net /GPIO12)
    (add_net /GPIO13)
    (add_net /GPIO14)
    (add_net /GPIO16)
    (add_net /GPIO5)
    (add_net GND)
    (add_net LED_DATA)
    (add_net "Net-(C4-Pad1)")
    (add_net "Net-(C5-Pad1)")
    (add_net "Net-(J3-Pad2)")
    (add_net "Net-(J3-Pad3)")
    (add_net "Net-(J3-Pad4)")
    (add_net "Net-(J4-Pad2)")
    (add_net "Net-(J4-Pad4)")
    (add_net "Net-(J4-Pad5)")
    (add_net "Net-(J4-Pad6)")
    (add_net "Net-(J5-Pad2)")
    (add_net "Net-(J5-Pad3)")
    (add_net "Net-(J5-Pad4)")
    (add_net "Net-(R2-Pad1)")
    (add_net "Net-(R3-Pad1)")
    (add_net "Net-(R4-Pad1)")
    (add_net "Net-(SW1-Pad2)")
    (add_net "Net-(SW2-Pad1)")
    (add_net "Net-(U3-Pad10)")
    (add_net "Net-(U3-Pad11)")
    (add_net "Net-(U3-Pad12)")
    (add_net "Net-(U3-Pad9)")
    (add_net "Net-(U4-Pad10)")
    (add_net "Net-(U4-Pad11)")
    (add_net "Net-(U4-Pad12)")
    (add_net "Net-(U4-Pad13)")
    (add_net "Net-(U4-Pad14)")
    (add_net "Net-(U4-Pad15)")
    (add_net "Net-(U4-Pad9)")
  )

  (module Capacitor_SMD:C_0603_1608Metric (layer F.Cu) (tedit 5F68FEEE) (tstamp 60481F75)
    (at 132.08 42.405 90)
    (descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags capacitor)
    (path /6045F37C)
    (attr smd)
    (fp_text reference C5 (at 0.241 1.524 90) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value "22 pF" (at 0 1.43 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.48 0.73) (end -1.48 0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start -0.14058 0.51) (end 0.14058 0.51) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.14058 -0.51) (end 0.14058 -0.51) (layer F.SilkS) (width 0.12))
    (fp_line (start 0.8 0.4) (end -0.8 0.4) (layer F.Fab) (width 0.1))
    (fp_line (start 0.8 -0.4) (end 0.8 0.4) (layer F.Fab) (width 0.1))
    (fp_line (start -0.8 -0.4) (end 0.8 -0.4) (layer F.Fab) (width 0.1))
    (fp_line (start -0.8 0.4) (end -0.8 -0.4) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0 90) (layer F.Fab)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad 2 smd roundrect (at 0.775 0 90) (size 0.9 0.95) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 1 GND))
    (pad 1 smd roundrect (at -0.775 0 90) (size 0.9 0.95) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 30 "Net-(C5-Pad1)"))
    (model ${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Capacitor_SMD:C_0603_1608Metric (layer F.Cu) (tedit 5F68FEEE) (tstamp 60481371)
    (at 132.08 39.383 270)
    (descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags capacitor)
    (path /60470703)
    (attr smd)
    (fp_text reference C4 (at 0 -1.43 90) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value "22 pF" (at 0 1.43 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.48 0.73) (end -1.48 0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73) (layer F.CrtYd) (width 0.05))
    (fp_line (start -0.14058 0.51) (end 0.14058 0.51) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.14058 -0.51) (end 0.14058 -0.51) (layer F.SilkS) (width 0.12))
    (fp_line (start 0.8 0.4) (end -0.8 0.4) (layer F.Fab) (width 0.1))
    (fp_line (start 0.8 -0.4) (end 0.8 0.4) (layer F.Fab) (width 0.1))
    (fp_line (start -0.8 -0.4) (end 0.8 -0.4) (layer F.Fab) (width 0.1))
    (fp_line (start -0.8 0.4) (end -0.8 -0.4) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0 90) (layer F.Fab)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad 2 smd roundrect (at 0.775 0 270) (size 0.9 0.95) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 1 GND))
    (pad 1 smd roundrect (at -0.775 0 270) (size 0.9 0.95) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 29 "Net-(C4-Pad1)"))
    (model ${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Package_TO_SOT_SMD:SOT-223 (layer F.Cu) (tedit 5A02FF57) (tstamp 6047FDEE)
    (at 148.336 53.34 90)
    (descr "module CMS SOT223 4 pins")
    (tags "CMS SOT")
    (path /60284AEE)
    (attr smd)
    (fp_text reference U2 (at 5.08 0.508 180) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value AMS1117-3.3 (at 0 4.5 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.85 -3.35) (end 1.85 3.35) (layer F.Fab) (width 0.1))
    (fp_line (start -1.85 3.35) (end 1.85 3.35) (layer F.Fab) (width 0.1))
    (fp_line (start -4.1 -3.41) (end 1.91 -3.41) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.8 -3.35) (end 1.85 -3.35) (layer F.Fab) (width 0.1))
    (fp_line (start -1.85 3.41) (end 1.91 3.41) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.85 -2.3) (end -1.85 3.35) (layer F.Fab) (width 0.1))
    (fp_line (start -4.4 -3.6) (end -4.4 3.6) (layer F.CrtYd) (width 0.05))
    (fp_line (start -4.4 3.6) (end 4.4 3.6) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.4 3.6) (end 4.4 -3.6) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.4 -3.6) (end -4.4 -3.6) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.91 -3.41) (end 1.91 -2.15) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.91 3.41) (end 1.91 2.15) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.85 -2.3) (end -0.8 -3.35) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0) (layer F.Fab)
      (effects (font (size 0.8 0.8) (thickness 0.12)))
    )
    (pad 1 smd rect (at -3.15 -2.3 90) (size 2 1.5) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 3 smd rect (at -3.15 2.3 90) (size 2 1.5) (layers F.Cu F.Paste F.Mask)
      (net 2 +5V))
    (pad 2 smd rect (at -3.15 0 90) (size 2 1.5) (layers F.Cu F.Paste F.Mask)
      (net 3 +3V3))
    (pad 4 smd rect (at 3.15 0 90) (size 2 3.8) (layers F.Cu F.Paste F.Mask))
    (model ${KISYS3DMOD}/Package_TO_SOT_SMD.3dshapes/SOT-223.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Button_Switch_SMD:SW_SPST_TL3342 (layer F.Cu) (tedit 5A02FC95) (tstamp 6047FDD8)
    (at 137.668 59.436 180)
    (descr "Low-profile SMD Tactile Switch, https://www.e-switch.com/system/asset/product_line/data_sheet/165/TL3342.pdf")
    (tags "SPST Tactile Switch")
    (path /602B7FF6)
    (attr smd)
    (fp_text reference SW2 (at 0 3.556) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value SW_Push (at 0 3.75) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_circle (center 0 0) (end 1 0) (layer F.Fab) (width 0.1))
    (fp_line (start -4.25 3) (end -4.25 -3) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.25 3) (end -4.25 3) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.25 -3) (end 4.25 3) (layer F.CrtYd) (width 0.05))
    (fp_line (start -4.25 -3) (end 4.25 -3) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.2 -2.6) (end -2.6 -1.2) (layer F.Fab) (width 0.1))
    (fp_line (start 1.2 -2.6) (end -1.2 -2.6) (layer F.Fab) (width 0.1))
    (fp_line (start 2.6 -1.2) (end 1.2 -2.6) (layer F.Fab) (width 0.1))
    (fp_line (start 2.6 1.2) (end 2.6 -1.2) (layer F.Fab) (width 0.1))
    (fp_line (start 1.2 2.6) (end 2.6 1.2) (layer F.Fab) (width 0.1))
    (fp_line (start -1.2 2.6) (end 1.2 2.6) (layer F.Fab) (width 0.1))
    (fp_line (start -2.6 1.2) (end -1.2 2.6) (layer F.Fab) (width 0.1))
    (fp_line (start -2.6 -1.2) (end -2.6 1.2) (layer F.Fab) (width 0.1))
    (fp_line (start -1.25 -2.75) (end 1.25 -2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start -2.75 -1) (end -2.75 1) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.25 2.75) (end 1.25 2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 2.75 -1) (end 2.75 1) (layer F.SilkS) (width 0.12))
    (fp_line (start -2 1) (end -2 -1) (layer F.Fab) (width 0.1))
    (fp_line (start -1 2) (end -2 1) (layer F.Fab) (width 0.1))
    (fp_line (start 1 2) (end -1 2) (layer F.Fab) (width 0.1))
    (fp_line (start 2 1) (end 1 2) (layer F.Fab) (width 0.1))
    (fp_line (start 2 -1) (end 2 1) (layer F.Fab) (width 0.1))
    (fp_line (start 1 -2) (end 2 -1) (layer F.Fab) (width 0.1))
    (fp_line (start -1 -2) (end 1 -2) (layer F.Fab) (width 0.1))
    (fp_line (start -2 -1) (end -1 -2) (layer F.Fab) (width 0.1))
    (fp_line (start -1.7 -2.3) (end -1.25 -2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.7 -2.3) (end 1.25 -2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.7 2.3) (end 1.25 2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.7 2.3) (end -1.25 2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 3.2 1.6) (end 2.2 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 2.7 2.1) (end 2.7 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 1.7 2.1) (end 3.2 2.1) (layer F.Fab) (width 0.1))
    (fp_line (start -1.7 2.1) (end -3.2 2.1) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 1.6) (end -2.2 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -2.7 2.1) (end -2.7 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 -1.6) (end -2.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -1.7 -2.1) (end -3.2 -2.1) (layer F.Fab) (width 0.1))
    (fp_line (start -2.7 -2.1) (end -2.7 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 3.2 -1.6) (end 2.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 1.7 -2.1) (end 3.2 -2.1) (layer F.Fab) (width 0.1))
    (fp_line (start 2.7 -2.1) (end 2.7 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 -2.1) (end -3.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 2.1) (end -3.2 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 3.2 -2.1) (end 3.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 3.2 2.1) (end 3.2 1.6) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 -3.75) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 2 smd rect (at 3.15 1.9 180) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 2 smd rect (at -3.15 1.9 180) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 1 smd rect (at 3.15 -1.9 180) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 24 "Net-(SW2-Pad1)"))
    (pad 1 smd rect (at -3.15 -1.9 180) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 24 "Net-(SW2-Pad1)"))
    (model ${KISYS3DMOD}/Button_Switch_SMD.3dshapes/SW_SPST_TL3342.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Button_Switch_SMD:SW_SPST_TL3342 (layer F.Cu) (tedit 5A02FC95) (tstamp 6047FDA2)
    (at 103.124 59.436)
    (descr "Low-profile SMD Tactile Switch, https://www.e-switch.com/system/asset/product_line/data_sheet/165/TL3342.pdf")
    (tags "SPST Tactile Switch")
    (path /602BE984)
    (attr smd)
    (fp_text reference SW1 (at 0 -3.75) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value SW_Push (at 0 3.75) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_circle (center 0 0) (end 1 0) (layer F.Fab) (width 0.1))
    (fp_line (start -4.25 3) (end -4.25 -3) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.25 3) (end -4.25 3) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.25 -3) (end 4.25 3) (layer F.CrtYd) (width 0.05))
    (fp_line (start -4.25 -3) (end 4.25 -3) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.2 -2.6) (end -2.6 -1.2) (layer F.Fab) (width 0.1))
    (fp_line (start 1.2 -2.6) (end -1.2 -2.6) (layer F.Fab) (width 0.1))
    (fp_line (start 2.6 -1.2) (end 1.2 -2.6) (layer F.Fab) (width 0.1))
    (fp_line (start 2.6 1.2) (end 2.6 -1.2) (layer F.Fab) (width 0.1))
    (fp_line (start 1.2 2.6) (end 2.6 1.2) (layer F.Fab) (width 0.1))
    (fp_line (start -1.2 2.6) (end 1.2 2.6) (layer F.Fab) (width 0.1))
    (fp_line (start -2.6 1.2) (end -1.2 2.6) (layer F.Fab) (width 0.1))
    (fp_line (start -2.6 -1.2) (end -2.6 1.2) (layer F.Fab) (width 0.1))
    (fp_line (start -1.25 -2.75) (end 1.25 -2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start -2.75 -1) (end -2.75 1) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.25 2.75) (end 1.25 2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 2.75 -1) (end 2.75 1) (layer F.SilkS) (width 0.12))
    (fp_line (start -2 1) (end -2 -1) (layer F.Fab) (width 0.1))
    (fp_line (start -1 2) (end -2 1) (layer F.Fab) (width 0.1))
    (fp_line (start 1 2) (end -1 2) (layer F.Fab) (width 0.1))
    (fp_line (start 2 1) (end 1 2) (layer F.Fab) (width 0.1))
    (fp_line (start 2 -1) (end 2 1) (layer F.Fab) (width 0.1))
    (fp_line (start 1 -2) (end 2 -1) (layer F.Fab) (width 0.1))
    (fp_line (start -1 -2) (end 1 -2) (layer F.Fab) (width 0.1))
    (fp_line (start -2 -1) (end -1 -2) (layer F.Fab) (width 0.1))
    (fp_line (start -1.7 -2.3) (end -1.25 -2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.7 -2.3) (end 1.25 -2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.7 2.3) (end 1.25 2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.7 2.3) (end -1.25 2.75) (layer F.SilkS) (width 0.12))
    (fp_line (start 3.2 1.6) (end 2.2 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 2.7 2.1) (end 2.7 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 1.7 2.1) (end 3.2 2.1) (layer F.Fab) (width 0.1))
    (fp_line (start -1.7 2.1) (end -3.2 2.1) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 1.6) (end -2.2 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -2.7 2.1) (end -2.7 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 -1.6) (end -2.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -1.7 -2.1) (end -3.2 -2.1) (layer F.Fab) (width 0.1))
    (fp_line (start -2.7 -2.1) (end -2.7 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 3.2 -1.6) (end 2.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 1.7 -2.1) (end 3.2 -2.1) (layer F.Fab) (width 0.1))
    (fp_line (start 2.7 -2.1) (end 2.7 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 -2.1) (end -3.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start -3.2 2.1) (end -3.2 1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 3.2 -2.1) (end 3.2 -1.6) (layer F.Fab) (width 0.1))
    (fp_line (start 3.2 2.1) (end 3.2 1.6) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 -3.75) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 2 smd rect (at 3.15 1.9) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 23 "Net-(SW1-Pad2)"))
    (pad 2 smd rect (at -3.15 1.9) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 23 "Net-(SW1-Pad2)"))
    (pad 1 smd rect (at 3.15 -1.9) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 1 smd rect (at -3.15 -1.9) (size 1.7 1) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (model ${KISYS3DMOD}/Button_Switch_SMD.3dshapes/SW_SPST_TL3342.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Resistor_SMD:R_0805_2012Metric (layer F.Cu) (tedit 5F68FEEE) (tstamp 6047FD6C)
    (at 105.156 48.8715 90)
    (descr "Resistor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags resistor)
    (path /60294899)
    (attr smd)
    (fp_text reference R4 (at 2.6435 0 180) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value 3.3k (at 0 1.65 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.68 0.95) (end -1.68 0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.68 -0.95) (end 1.68 0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.68 -0.95) (end 1.68 -0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.68 0.95) (end -1.68 -0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -0.227064 0.735) (end 0.227064 0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.227064 -0.735) (end 0.227064 -0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start 1 0.625) (end -1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start 1 -0.625) (end 1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 -0.625) (end 1 -0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 0.625) (end -1 -0.625) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0 90) (layer F.Fab)
      (effects (font (size 0.5 0.5) (thickness 0.08)))
    )
    (pad 2 smd roundrect (at 0.9125 0 90) (size 1.025 1.4) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.2439014634146341)
      (net 3 +3V3))
    (pad 1 smd roundrect (at -0.9125 0 90) (size 1.025 1.4) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.2439014634146341)
      (net 22 "Net-(R4-Pad1)"))
    (model ${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0805_2012Metric.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Resistor_SMD:R_0805_2012Metric (layer F.Cu) (tedit 5F68FEEE) (tstamp 6047FD5B)
    (at 133.4125 45)
    (descr "Resistor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags resistor)
    (path /6028B0BD)
    (attr smd)
    (fp_text reference R3 (at 2.7315 0.212) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value 12k (at 0 1.65) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.68 0.95) (end -1.68 0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.68 -0.95) (end 1.68 0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.68 -0.95) (end 1.68 -0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.68 0.95) (end -1.68 -0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -0.227064 0.735) (end 0.227064 0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.227064 -0.735) (end 0.227064 -0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start 1 0.625) (end -1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start 1 -0.625) (end 1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 -0.625) (end 1 -0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 0.625) (end -1 -0.625) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0) (layer F.Fab)
      (effects (font (size 0.5 0.5) (thickness 0.08)))
    )
    (pad 2 smd roundrect (at 0.9125 0) (size 1.025 1.4) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.2439014634146341)
      (net 3 +3V3))
    (pad 1 smd roundrect (at -0.9125 0) (size 1.025 1.4) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.2439014634146341)
      (net 21 "Net-(R3-Pad1)"))
    (model ${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0805_2012Metric.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Resistor_SMD:R_0805_2012Metric (layer F.Cu) (tedit 5F68FEEE) (tstamp 6047FD4A)
    (at 109.22 48.8715 90)
    (descr "Resistor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags resistor)
    (path /6028803B)
    (attr smd)
    (fp_text reference R2 (at 2.6435 0 180) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value 3.3k (at 0 1.65 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.68 0.95) (end -1.68 0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.68 -0.95) (end 1.68 0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.68 -0.95) (end 1.68 -0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.68 0.95) (end -1.68 -0.95) (layer F.CrtYd) (width 0.05))
    (fp_line (start -0.227064 0.735) (end 0.227064 0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.227064 -0.735) (end 0.227064 -0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start 1 0.625) (end -1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start 1 -0.625) (end 1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 -0.625) (end 1 -0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 0.625) (end -1 -0.625) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0 90) (layer F.Fab)
      (effects (font (size 0.5 0.5) (thickness 0.08)))
    )
    (pad 2 smd roundrect (at 0.9125 0 90) (size 1.025 1.4) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.2439014634146341)
      (net 1 GND))
    (pad 1 smd roundrect (at -0.9125 0 90) (size 1.025 1.4) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.2439014634146341)
      (net 20 "Net-(R2-Pad1)"))
    (model ${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0805_2012Metric.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Capacitor_SMD:C_0805_2012Metric (layer F.Cu) (tedit 5F68FEEE) (tstamp 6047FBBB)
    (at 136.144 48.702 90)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags capacitor)
    (path /60294FD4)
    (attr smd)
    (fp_text reference C3 (at 0 -1.68 90) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value 100nF (at 0 1.68 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.7 0.98) (end -1.7 0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.7 -0.98) (end 1.7 0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.7 -0.98) (end 1.7 -0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.7 0.98) (end -1.7 -0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start 1 0.625) (end -1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start 1 -0.625) (end 1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 -0.625) (end 1 -0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 0.625) (end -1 -0.625) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0 90) (layer F.Fab)
      (effects (font (size 0.5 0.5) (thickness 0.08)))
    )
    (pad 2 smd roundrect (at 0.95 0 90) (size 1 1.45) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 3 +3V3))
    (pad 1 smd roundrect (at -0.95 0 90) (size 1 1.45) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 1 GND))
    (model ${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Capacitor_SMD:C_0805_2012Metric (layer F.Cu) (tedit 5F68FEEE) (tstamp 6047FBAA)
    (at 143.256 53.782 270)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags capacitor)
    (path /602B3D92)
    (attr smd)
    (fp_text reference C2 (at 0 1.524 90) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value 100nF (at 0 1.68 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 1.7 0.98) (end -1.7 0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.7 -0.98) (end 1.7 0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.7 -0.98) (end 1.7 -0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.7 0.98) (end -1.7 -0.98) (layer F.CrtYd) (width 0.05))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735) (layer F.SilkS) (width 0.12))
    (fp_line (start 1 0.625) (end -1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start 1 -0.625) (end 1 0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 -0.625) (end 1 -0.625) (layer F.Fab) (width 0.1))
    (fp_line (start -1 0.625) (end -1 -0.625) (layer F.Fab) (width 0.1))
    (fp_text user %R (at -0.955 -1.524 90) (layer F.Fab)
      (effects (font (size 0.5 0.5) (thickness 0.08)))
    )
    (pad 2 smd roundrect (at 0.95 0 270) (size 1 1.45) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 1 GND))
    (pad 1 smd roundrect (at -0.95 0 270) (size 1 1.45) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 3 +3V3))
    (model ${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Capacitor_Tantalum_SMD:CP_EIA-3216-18_Kemet-A (layer F.Cu) (tedit 5EBA9318) (tstamp 6047FB99)
    (at 149.526 59.436 180)
    (descr "Tantalum Capacitor SMD Kemet-A (3216-18 Metric), IPC_7351 nominal, (Body size from: http://www.kemet.com/Lists/ProductCatalog/Attachments/253/KEM_TC101_STD.pdf), generated with kicad-footprint-generator")
    (tags "capacitor tantalum")
    (path /602B56DB)
    (attr smd)
    (fp_text reference C1 (at 0 -1.75) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value 10uF (at 0 1.75) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 2.3 1.05) (end -2.3 1.05) (layer F.CrtYd) (width 0.05))
    (fp_line (start 2.3 -1.05) (end 2.3 1.05) (layer F.CrtYd) (width 0.05))
    (fp_line (start -2.3 -1.05) (end 2.3 -1.05) (layer F.CrtYd) (width 0.05))
    (fp_line (start -2.3 1.05) (end -2.3 -1.05) (layer F.CrtYd) (width 0.05))
    (fp_line (start -2.31 0.935) (end 1.6 0.935) (layer F.SilkS) (width 0.12))
    (fp_line (start -2.31 -0.935) (end -2.31 0.935) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.6 -0.935) (end -2.31 -0.935) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.6 0.8) (end 1.6 -0.8) (layer F.Fab) (width 0.1))
    (fp_line (start -1.6 0.8) (end 1.6 0.8) (layer F.Fab) (width 0.1))
    (fp_line (start -1.6 -0.4) (end -1.6 0.8) (layer F.Fab) (width 0.1))
    (fp_line (start -1.2 -0.8) (end -1.6 -0.4) (layer F.Fab) (width 0.1))
    (fp_line (start 1.6 -0.8) (end -1.2 -0.8) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0) (layer F.Fab)
      (effects (font (size 0.8 0.8) (thickness 0.12)))
    )
    (pad 2 smd roundrect (at 1.35 0 180) (size 1.4 1.35) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.1851844444444445)
      (net 1 GND))
    (pad 1 smd roundrect (at -1.35 0 180) (size 1.4 1.35) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.1851844444444445)
      (net 2 +5V))
    (model ${KISYS3DMOD}/Capacitor_Tantalum_SMD.3dshapes/CP_EIA-3216-18_Kemet-A.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Connector_USB:USB_Micro-B_Molex_47346-0001 (layer F.Cu) (tedit 5D8620A7) (tstamp 6047F181)
    (at 157.988 41.174 90)
    (descr "Micro USB B receptable with flange, bottom-mount, SMD, right-angle (http://www.molex.com/pdm_docs/sd/473460001_sd.pdf)")
    (tags "Micro B USB SMD")
    (path /6027AE74)
    (attr smd)
    (fp_text reference J3 (at 0 -3.3 270) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value USB_B_Micro (at 0 4.6 270) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start -3.25 2.65) (end 3.25 2.65) (layer F.Fab) (width 0.1))
    (fp_line (start -3.81 2.6) (end -3.81 2.34) (layer F.SilkS) (width 0.12))
    (fp_line (start -3.81 0.06) (end -3.81 -1.71) (layer F.SilkS) (width 0.12))
    (fp_line (start -3.81 -1.71) (end -3.43 -1.71) (layer F.SilkS) (width 0.12))
    (fp_line (start 3.81 -1.71) (end 3.81 0.06) (layer F.SilkS) (width 0.12))
    (fp_line (start 3.81 2.34) (end 3.81 2.6) (layer F.SilkS) (width 0.12))
    (fp_line (start -3.75 3.35) (end -3.75 -1.65) (layer F.Fab) (width 0.1))
    (fp_line (start -3.75 -1.65) (end 3.75 -1.65) (layer F.Fab) (width 0.1))
    (fp_line (start 3.75 -1.65) (end 3.75 3.35) (layer F.Fab) (width 0.1))
    (fp_line (start 3.75 3.35) (end -3.75 3.35) (layer F.Fab) (width 0.1))
    (fp_line (start -4.7 3.85) (end -4.7 -2.65) (layer F.CrtYd) (width 0.05))
    (fp_line (start -4.7 -2.65) (end 4.7 -2.65) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.7 -2.65) (end 4.7 3.85) (layer F.CrtYd) (width 0.05))
    (fp_line (start 4.7 3.85) (end -4.7 3.85) (layer F.CrtYd) (width 0.05))
    (fp_line (start 3.81 -1.71) (end 3.43 -1.71) (layer F.SilkS) (width 0.12))
    (fp_text user %R (at 0 1.2 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text user "PCB Edge" (at 0 2.67 270) (layer Dwgs.User)
      (effects (font (size 0.4 0.4) (thickness 0.04)))
    )
    (pad 6 smd rect (at 1.55 1.2 90) (size 1 1.9) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 6 smd rect (at -1.15 1.2 90) (size 1.8 1.9) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 6 smd rect (at 3.375 1.2 90) (size 1.65 1.3) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 6 smd rect (at -3.375 1.2 90) (size 1.65 1.3) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 6 smd rect (at 2.4875 -1.375 90) (size 1.425 1.55) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 6 smd rect (at -2.4875 -1.375 90) (size 1.425 1.55) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 5 smd rect (at 1.3 -1.46 90) (size 0.45 1.38) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 4 smd rect (at 0.65 -1.46 90) (size 0.45 1.38) (layers F.Cu F.Paste F.Mask)
      (net 6 "Net-(J3-Pad4)"))
    (pad 3 smd rect (at 0 -1.46 90) (size 0.45 1.38) (layers F.Cu F.Paste F.Mask)
      (net 5 "Net-(J3-Pad3)"))
    (pad 2 smd rect (at -0.65 -1.46 90) (size 0.45 1.38) (layers F.Cu F.Paste F.Mask)
      (net 7 "Net-(J3-Pad2)"))
    (pad 1 smd rect (at -1.3 -1.46 90) (size 0.45 1.38) (layers F.Cu F.Paste F.Mask)
      (net 2 +5V))
    (model ${KISYS3DMOD}/Connector_USB.3dshapes/USB_Micro-B_Molex_47346-0001.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Crystal:Crystal_SMD_3225-4Pin_3.2x2.5mm (layer F.Cu) (tedit 5A0FD1B2) (tstamp 60450DB5)
    (at 128.44 40.132 180)
    (descr "SMD Crystal SERIES SMD3225/4 http://www.txccrystal.com/images/pdf/7m-accuracy.pdf, 3.2x2.5mm^2 package")
    (tags "SMD SMT crystal")
    (path /6044EA3E)
    (attr smd)
    (fp_text reference Y1 (at 3.44 0.132) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value Crystal_GND24 (at 0 2.45) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 2.1 -1.7) (end -2.1 -1.7) (layer F.CrtYd) (width 0.05))
    (fp_line (start 2.1 1.7) (end 2.1 -1.7) (layer F.CrtYd) (width 0.05))
    (fp_line (start -2.1 1.7) (end 2.1 1.7) (layer F.CrtYd) (width 0.05))
    (fp_line (start -2.1 -1.7) (end -2.1 1.7) (layer F.CrtYd) (width 0.05))
    (fp_line (start -2 1.65) (end 2 1.65) (layer F.SilkS) (width 0.12))
    (fp_line (start -2 -1.65) (end -2 1.65) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.6 0.25) (end -0.6 1.25) (layer F.Fab) (width 0.1))
    (fp_line (start 1.6 -1.25) (end -1.6 -1.25) (layer F.Fab) (width 0.1))
    (fp_line (start 1.6 1.25) (end 1.6 -1.25) (layer F.Fab) (width 0.1))
    (fp_line (start -1.6 1.25) (end 1.6 1.25) (layer F.Fab) (width 0.1))
    (fp_line (start -1.6 -1.25) (end -1.6 1.25) (layer F.Fab) (width 0.1))
    (fp_text user %R (at 0 0) (layer F.Fab)
      (effects (font (size 0.7 0.7) (thickness 0.105)))
    )
    (pad 4 smd rect (at -1.1 -0.85 180) (size 1.4 1.2) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 3 smd rect (at 1.1 -0.85 180) (size 1.4 1.2) (layers F.Cu F.Paste F.Mask)
      (net 30 "Net-(C5-Pad1)"))
    (pad 2 smd rect (at 1.1 0.85 180) (size 1.4 1.2) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 1 smd rect (at -1.1 0.85 180) (size 1.4 1.2) (layers F.Cu F.Paste F.Mask)
      (net 29 "Net-(C4-Pad1)"))
    (model ${KISYS3DMOD}/Crystal.3dshapes/Crystal_SMD_3225-4Pin_3.2x2.5mm.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Package_SO:SOIC-16_3.9x9.9mm_P1.27mm (layer F.Cu) (tedit 5D9F72B1) (tstamp 60450DA1)
    (at 143.256 40.132)
    (descr "SOIC, 16 Pin (JEDEC MS-012AC, https://www.analog.com/media/en/package-pcb-resources/package/pkg_pdf/soic_narrow-r/r_16.pdf), generated with kicad-footprint-generator ipc_gullwing_generator.py")
    (tags "SOIC SO")
    (path /60442C05)
    (attr smd)
    (fp_text reference U4 (at -4.572 -3.556) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value CH340G (at 0 5.9) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 3.7 -5.2) (end -3.7 -5.2) (layer F.CrtYd) (width 0.05))
    (fp_line (start 3.7 5.2) (end 3.7 -5.2) (layer F.CrtYd) (width 0.05))
    (fp_line (start -3.7 5.2) (end 3.7 5.2) (layer F.CrtYd) (width 0.05))
    (fp_line (start -3.7 -5.2) (end -3.7 5.2) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.95 -3.975) (end -0.975 -4.95) (layer F.Fab) (width 0.1))
    (fp_line (start -1.95 4.95) (end -1.95 -3.975) (layer F.Fab) (width 0.1))
    (fp_line (start 1.95 4.95) (end -1.95 4.95) (layer F.Fab) (width 0.1))
    (fp_line (start 1.95 -4.95) (end 1.95 4.95) (layer F.Fab) (width 0.1))
    (fp_line (start -0.975 -4.95) (end 1.95 -4.95) (layer F.Fab) (width 0.1))
    (fp_line (start 0 -5.06) (end -3.45 -5.06) (layer F.SilkS) (width 0.12))
    (fp_line (start 0 -5.06) (end 1.95 -5.06) (layer F.SilkS) (width 0.12))
    (fp_line (start 0 5.06) (end -1.95 5.06) (layer F.SilkS) (width 0.12))
    (fp_line (start 0 5.06) (end 1.95 5.06) (layer F.SilkS) (width 0.12))
    (fp_text user %R (at 0 0) (layer F.Fab)
      (effects (font (size 0.98 0.98) (thickness 0.15)))
    )
    (pad 16 smd roundrect (at 2.475 -4.445) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 3 +3V3))
    (pad 15 smd roundrect (at 2.475 -3.175) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 31 "Net-(U4-Pad15)"))
    (pad 14 smd roundrect (at 2.475 -1.905) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 32 "Net-(U4-Pad14)"))
    (pad 13 smd roundrect (at 2.475 -0.635) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 33 "Net-(U4-Pad13)"))
    (pad 12 smd roundrect (at 2.475 0.635) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 34 "Net-(U4-Pad12)"))
    (pad 11 smd roundrect (at 2.475 1.905) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 35 "Net-(U4-Pad11)"))
    (pad 10 smd roundrect (at 2.475 3.175) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 36 "Net-(U4-Pad10)"))
    (pad 9 smd roundrect (at 2.475 4.445) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 37 "Net-(U4-Pad9)"))
    (pad 8 smd roundrect (at -2.475 4.445) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 30 "Net-(C5-Pad1)"))
    (pad 7 smd roundrect (at -2.475 3.175) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 29 "Net-(C4-Pad1)"))
    (pad 6 smd roundrect (at -2.475 1.905) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 7 "Net-(J3-Pad2)"))
    (pad 5 smd roundrect (at -2.475 0.635) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 5 "Net-(J3-Pad3)"))
    (pad 4 smd roundrect (at -2.475 -0.635) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 3 +3V3))
    (pad 3 smd roundrect (at -2.475 -1.905) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 9 "Net-(J4-Pad5)"))
    (pad 2 smd roundrect (at -2.475 -3.175) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 10 "Net-(J4-Pad4)"))
    (pad 1 smd roundrect (at -2.475 -4.445) (size 1.95 0.6) (layers F.Cu F.Paste F.Mask) (roundrect_rratio 0.25)
      (net 1 GND))
    (model ${KISYS3DMOD}/Package_SO.3dshapes/SOIC-16_3.9x9.9mm_P1.27mm.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module RF_Module:ESP-12E (layer F.Cu) (tedit 5A030172) (tstamp 60413365)
    (at 120.904 58.42 180)
    (descr "Wi-Fi Module, http://wiki.ai-thinker.com/_media/esp8266/docs/aithinker_esp_12f_datasheet_en.pdf")
    (tags "Wi-Fi Module")
    (path /602A0799)
    (attr smd)
    (fp_text reference U3 (at 0 14.224) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value ESP-12E (at -0.06 -12.78) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start 5.56 -4.8) (end 8.12 -7.36) (layer Dwgs.User) (width 0.12))
    (fp_line (start 2.56 -4.8) (end 8.12 -10.36) (layer Dwgs.User) (width 0.12))
    (fp_line (start -0.44 -4.8) (end 6.88 -12.12) (layer Dwgs.User) (width 0.12))
    (fp_line (start -3.44 -4.8) (end 3.88 -12.12) (layer Dwgs.User) (width 0.12))
    (fp_line (start -6.44 -4.8) (end 0.88 -12.12) (layer Dwgs.User) (width 0.12))
    (fp_line (start -8.12 -6.12) (end -2.12 -12.12) (layer Dwgs.User) (width 0.12))
    (fp_line (start -8.12 -9.12) (end -5.12 -12.12) (layer Dwgs.User) (width 0.12))
    (fp_line (start -8.12 -4.8) (end -8.12 -12.12) (layer Dwgs.User) (width 0.12))
    (fp_line (start 8.12 -4.8) (end -8.12 -4.8) (layer Dwgs.User) (width 0.12))
    (fp_line (start 8.12 -12.12) (end 8.12 -4.8) (layer Dwgs.User) (width 0.12))
    (fp_line (start -8.12 -12.12) (end 8.12 -12.12) (layer Dwgs.User) (width 0.12))
    (fp_line (start -8.12 -4.5) (end -8.73 -4.5) (layer F.SilkS) (width 0.12))
    (fp_line (start -8.12 -4.5) (end -8.12 -12.12) (layer F.SilkS) (width 0.12))
    (fp_line (start -8.12 12.12) (end -8.12 11.5) (layer F.SilkS) (width 0.12))
    (fp_line (start -6 12.12) (end -8.12 12.12) (layer F.SilkS) (width 0.12))
    (fp_line (start 8.12 12.12) (end 6 12.12) (layer F.SilkS) (width 0.12))
    (fp_line (start 8.12 11.5) (end 8.12 12.12) (layer F.SilkS) (width 0.12))
    (fp_line (start 8.12 -12.12) (end 8.12 -4.5) (layer F.SilkS) (width 0.12))
    (fp_line (start -8.12 -12.12) (end 8.12 -12.12) (layer F.SilkS) (width 0.12))
    (fp_line (start -9.05 13.1) (end -9.05 -12.2) (layer F.CrtYd) (width 0.05))
    (fp_line (start 9.05 13.1) (end -9.05 13.1) (layer F.CrtYd) (width 0.05))
    (fp_line (start 9.05 -12.2) (end 9.05 13.1) (layer F.CrtYd) (width 0.05))
    (fp_line (start -9.05 -12.2) (end 9.05 -12.2) (layer F.CrtYd) (width 0.05))
    (fp_line (start -8 -4) (end -8 -12) (layer F.Fab) (width 0.12))
    (fp_line (start -7.5 -3.5) (end -8 -4) (layer F.Fab) (width 0.12))
    (fp_line (start -8 -3) (end -7.5 -3.5) (layer F.Fab) (width 0.12))
    (fp_line (start -8 12) (end -8 -3) (layer F.Fab) (width 0.12))
    (fp_line (start 8 12) (end -8 12) (layer F.Fab) (width 0.12))
    (fp_line (start 8 -12) (end 8 12) (layer F.Fab) (width 0.12))
    (fp_line (start -8 -12) (end 8 -12) (layer F.Fab) (width 0.12))
    (fp_text user Antenna (at -0.06 -7 180) (layer Cmts.User)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text user "KEEP-OUT ZONE" (at 0.03 -9.55 180) (layer Cmts.User)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text user %R (at 0.49 -0.8) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 1 smd rect (at -7.6 -3.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 24 "Net-(SW2-Pad1)"))
    (pad 2 smd rect (at -7.6 -1.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 17 "Net-(J5-Pad4)"))
    (pad 3 smd rect (at -7.6 0.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 21 "Net-(R3-Pad1)"))
    (pad 4 smd rect (at -7.6 2.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 12 /GPIO16))
    (pad 5 smd rect (at -7.6 4.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 13 /GPIO14))
    (pad 6 smd rect (at -7.6 6.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 15 /GPIO12))
    (pad 7 smd rect (at -7.6 8.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 14 /GPIO13))
    (pad 8 smd rect (at -7.6 10.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 3 +3V3))
    (pad 9 smd rect (at -5 12 180) (size 1 1.8) (layers F.Cu F.Paste F.Mask)
      (net 28 "Net-(U3-Pad9)"))
    (pad 10 smd rect (at -3 12 180) (size 1 1.8) (layers F.Cu F.Paste F.Mask)
      (net 27 "Net-(U3-Pad10)"))
    (pad 11 smd rect (at -1 12 180) (size 1 1.8) (layers F.Cu F.Paste F.Mask)
      (net 26 "Net-(U3-Pad11)"))
    (pad 12 smd rect (at 1 12 180) (size 1 1.8) (layers F.Cu F.Paste F.Mask)
      (net 25 "Net-(U3-Pad12)"))
    (pad 13 smd rect (at 3 12 180) (size 1 1.8) (layers F.Cu F.Paste F.Mask)
      (net 19 "Net-(J5-Pad2)"))
    (pad 14 smd rect (at 5 12 180) (size 1 1.8) (layers F.Cu F.Paste F.Mask)
      (net 18 "Net-(J5-Pad3)"))
    (pad 15 smd rect (at 7.6 10.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 1 GND))
    (pad 16 smd rect (at 7.6 8.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 20 "Net-(R2-Pad1)"))
    (pad 17 smd rect (at 7.6 6.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 22 "Net-(R4-Pad1)"))
    (pad 18 smd rect (at 7.6 4.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 23 "Net-(SW1-Pad2)"))
    (pad 19 smd rect (at 7.6 2.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 4 LED_DATA))
    (pad 20 smd rect (at 7.6 0.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 16 /GPIO5))
    (pad 21 smd rect (at 7.6 -1.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 10 "Net-(J4-Pad4)"))
    (pad 22 smd rect (at 7.6 -3.5 180) (size 2.5 1) (layers F.Cu F.Paste F.Mask)
      (net 9 "Net-(J4-Pad5)"))
    (model ${KISYS3DMOD}/RF_Module.3dshapes/ESP-12E.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Connector_PinSocket_2.54mm:PinSocket_1x12_P2.54mm_Vertical (layer F.Cu) (tedit 5A19A41D) (tstamp 604356EA)
    (at 107.188 33.02 90)
    (descr "Through hole straight socket strip, 1x12, 2.54mm pitch, single row (from Kicad 4.0.7), script generated")
    (tags "Through hole socket strip THT 1x12 2.54mm single row")
    (path /602BD32B)
    (fp_text reference J5 (at 0 -2.54 180) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value Conn_01x12_Male (at -2.54 13.462 180) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start -1.27 -1.27) (end 0.635 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start 0.635 -1.27) (end 1.27 -0.635) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 -0.635) (end 1.27 29.21) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 29.21) (end -1.27 29.21) (layer F.Fab) (width 0.1))
    (fp_line (start -1.27 29.21) (end -1.27 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start -1.33 1.27) (end 1.33 1.27) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 1.27) (end -1.33 29.27) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 29.27) (end 1.33 29.27) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 1.27) (end 1.33 29.27) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 -1.33) (end 1.33 0) (layer F.SilkS) (width 0.12))
    (fp_line (start 0 -1.33) (end 1.33 -1.33) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.8 -1.8) (end 1.75 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 -1.8) (end 1.75 29.7) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 29.7) (end -1.8 29.7) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.8 29.7) (end -1.8 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0 13.97) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 12 thru_hole oval (at 0 27.94 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 3 +3V3))
    (pad 11 thru_hole oval (at 0 25.4 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 2 +5V))
    (pad 10 thru_hole oval (at 0 22.86 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 12 /GPIO16))
    (pad 9 thru_hole oval (at 0 20.32 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 13 /GPIO14))
    (pad 8 thru_hole oval (at 0 17.78 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 14 /GPIO13))
    (pad 7 thru_hole oval (at 0 15.24 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 15 /GPIO12))
    (pad 6 thru_hole oval (at 0 12.7 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 16 /GPIO5))
    (pad 5 thru_hole oval (at 0 10.16 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 4 LED_DATA))
    (pad 4 thru_hole oval (at 0 7.62 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 17 "Net-(J5-Pad4)"))
    (pad 3 thru_hole oval (at 0 5.08 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 18 "Net-(J5-Pad3)"))
    (pad 2 thru_hole oval (at 0 2.54 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 19 "Net-(J5-Pad2)"))
    (pad 1 thru_hole rect (at 0 0 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 1 GND))
    (model ${KISYS3DMOD}/Connector_PinSocket_2.54mm.3dshapes/PinSocket_1x12_P2.54mm_Vertical.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Connector_PinSocket_2.54mm:PinSocket_1x06_P2.54mm_Vertical (layer F.Cu) (tedit 5A19A430) (tstamp 6041326C)
    (at 144.78 33.02 90)
    (descr "Through hole straight socket strip, 1x06, 2.54mm pitch, single row (from Kicad 4.0.7), script generated")
    (tags "Through hole socket strip THT 1x06 2.54mm single row")
    (path /602C368A)
    (fp_text reference J4 (at 0 -2.54 180) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value Conn_01x06_Male (at -2.54 6.604 180) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start -1.27 -1.27) (end 0.635 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start 0.635 -1.27) (end 1.27 -0.635) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 -0.635) (end 1.27 13.97) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 13.97) (end -1.27 13.97) (layer F.Fab) (width 0.1))
    (fp_line (start -1.27 13.97) (end -1.27 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start -1.33 1.27) (end 1.33 1.27) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 1.27) (end -1.33 14.03) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 14.03) (end 1.33 14.03) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 1.27) (end 1.33 14.03) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 -1.33) (end 1.33 0) (layer F.SilkS) (width 0.12))
    (fp_line (start 0 -1.33) (end 1.33 -1.33) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.8 -1.8) (end 1.75 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 -1.8) (end 1.75 14.45) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 14.45) (end -1.8 14.45) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.8 14.45) (end -1.8 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0 6.35) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 6 thru_hole oval (at 0 12.7 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 8 "Net-(J4-Pad6)"))
    (pad 5 thru_hole oval (at 0 10.16 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 9 "Net-(J4-Pad5)"))
    (pad 4 thru_hole oval (at 0 7.62 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 10 "Net-(J4-Pad4)"))
    (pad 3 thru_hole oval (at 0 5.08 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 3 +3V3))
    (pad 2 thru_hole oval (at 0 2.54 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 11 "Net-(J4-Pad2)"))
    (pad 1 thru_hole rect (at 0 0 90) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 1 GND))
    (model ${KISYS3DMOD}/Connector_PinSocket_2.54mm.3dshapes/PinSocket_1x06_P2.54mm_Vertical.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Connector_PinSocket_2.54mm:PinSocket_1x03_P2.54mm_Vertical (layer F.Cu) (tedit 5A19A429) (tstamp 60413229)
    (at 157.988 47.752)
    (descr "Through hole straight socket strip, 1x03, 2.54mm pitch, single row (from Kicad 4.0.7), script generated")
    (tags "Through hole socket strip THT 1x03 2.54mm single row")
    (path /60278BC7)
    (fp_text reference J2 (at -0.508 -2.032) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value Conn_01x03_Male (at -1.988 4.248 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start -1.27 -1.27) (end 0.635 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start 0.635 -1.27) (end 1.27 -0.635) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 -0.635) (end 1.27 6.35) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 6.35) (end -1.27 6.35) (layer F.Fab) (width 0.1))
    (fp_line (start -1.27 6.35) (end -1.27 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start -1.33 1.27) (end 1.33 1.27) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 1.27) (end -1.33 6.41) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 6.41) (end 1.33 6.41) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 1.27) (end 1.33 6.41) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 -1.33) (end 1.33 0) (layer F.SilkS) (width 0.12))
    (fp_line (start 0 -1.33) (end 1.33 -1.33) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.8 -1.8) (end 1.75 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 -1.8) (end 1.75 6.85) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 6.85) (end -1.8 6.85) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.8 6.85) (end -1.8 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0 2.54 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 3 thru_hole oval (at 0 5.08) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 1 GND))
    (pad 2 thru_hole oval (at 0 2.54) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 4 LED_DATA))
    (pad 1 thru_hole rect (at 0 0) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 2 +5V))
    (model ${KISYS3DMOD}/Connector_PinSocket_2.54mm.3dshapes/PinSocket_1x03_P2.54mm_Vertical.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module Connector_PinSocket_2.54mm:PinSocket_1x03_P2.54mm_Vertical (layer F.Cu) (tedit 5A19A429) (tstamp 60413212)
    (at 100.584 53.848 180)
    (descr "Through hole straight socket strip, 1x03, 2.54mm pitch, single row (from Kicad 4.0.7), script generated")
    (tags "Through hole socket strip THT 1x03 2.54mm single row")
    (path /60275784)
    (fp_text reference J1 (at 0 7.112) (layer F.SilkS)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value Conn_01x03_Male (at 2.032 3.048 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_line (start -1.27 -1.27) (end 0.635 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start 0.635 -1.27) (end 1.27 -0.635) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 -0.635) (end 1.27 6.35) (layer F.Fab) (width 0.1))
    (fp_line (start 1.27 6.35) (end -1.27 6.35) (layer F.Fab) (width 0.1))
    (fp_line (start -1.27 6.35) (end -1.27 -1.27) (layer F.Fab) (width 0.1))
    (fp_line (start -1.33 1.27) (end 1.33 1.27) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 1.27) (end -1.33 6.41) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.33 6.41) (end 1.33 6.41) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 1.27) (end 1.33 6.41) (layer F.SilkS) (width 0.12))
    (fp_line (start 1.33 -1.33) (end 1.33 0) (layer F.SilkS) (width 0.12))
    (fp_line (start 0 -1.33) (end 1.33 -1.33) (layer F.SilkS) (width 0.12))
    (fp_line (start -1.8 -1.8) (end 1.75 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 -1.8) (end 1.75 6.85) (layer F.CrtYd) (width 0.05))
    (fp_line (start 1.75 6.85) (end -1.8 6.85) (layer F.CrtYd) (width 0.05))
    (fp_line (start -1.8 6.85) (end -1.8 -1.8) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0 2.54 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 3 thru_hole oval (at 0 5.08 180) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 2 +5V))
    (pad 2 thru_hole oval (at 0 2.54 180) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 16 /GPIO5))
    (pad 1 thru_hole rect (at 0 0 180) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 1 GND))
    (model ${KISYS3DMOD}/Connector_PinSocket_2.54mm.3dshapes/PinSocket_1x03_P2.54mm_Vertical.wrl
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module MountingHole:MountingHole_3.2mm_M3_DIN965 (layer F.Cu) (tedit 56D1B4CB) (tstamp 6041716B)
    (at 101 27)
    (descr "Mounting Hole 3.2mm, no annular, M3, DIN965")
    (tags "mounting hole 3.2mm no annular m3 din965")
    (path /60392540)
    (attr virtual)
    (fp_text reference H4 (at 3.14 -0.076) (layer F.SilkS) hide
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value MountingHole (at 8 -1) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_circle (center 0 0) (end 2.8 0) (layer Cmts.User) (width 0.15))
    (fp_circle (center 0 0) (end 3.05 0) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0.3 0) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 1 np_thru_hole circle (at 0 0) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask))
  )

  (module MountingHole:MountingHole_3.2mm_M3_DIN965 (layer F.Cu) (tedit 56D1B4CB) (tstamp 6041851E)
    (at 157 27)
    (descr "Mounting Hole 3.2mm, no annular, M3, DIN965")
    (tags "mounting hole 3.2mm no annular m3 din965")
    (path /60390CF3)
    (attr virtual)
    (fp_text reference H3 (at -3.6 -0.076) (layer F.SilkS) hide
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value MountingHole (at -8 -1) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_circle (center 0 0) (end 2.8 0) (layer Cmts.User) (width 0.15))
    (fp_circle (center 0 0) (end 3.05 0) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0.3 0) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 1 np_thru_hole circle (at 0 0) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask))
  )

  (module MountingHole:MountingHole_3.2mm_M3_DIN965 (layer F.Cu) (tedit 56D1B4CB) (tstamp 604131EB)
    (at 157 67)
    (descr "Mounting Hole 3.2mm, no annular, M3, DIN965")
    (tags "mounting hole 3.2mm no annular m3 din965")
    (path /6038F46C)
    (attr virtual)
    (fp_text reference H2 (at -0.044 3.088) (layer F.SilkS) hide
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value MountingHole (at -3 -1 90) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_circle (center 0 0) (end 2.8 0) (layer Cmts.User) (width 0.15))
    (fp_circle (center 0 0) (end 3.05 0) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0.3 0) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 1 np_thru_hole circle (at 0 0) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask))
  )

  (module MountingHole:MountingHole_3.2mm_M3_DIN965 (layer F.Cu) (tedit 56D1B4CB) (tstamp 604131E3)
    (at 101 67)
    (descr "Mounting Hole 3.2mm, no annular, M3, DIN965")
    (tags "mounting hole 3.2mm no annular m3 din965")
    (path /6038DF1E)
    (attr virtual)
    (fp_text reference H1 (at 0.092 3.088) (layer F.SilkS) hide
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value MountingHole (at 4 -1 270) (layer F.Fab)
      (effects (font (size 1 0.8) (thickness 0.15)))
    )
    (fp_circle (center 0 0) (end 2.8 0) (layer Cmts.User) (width 0.15))
    (fp_circle (center 0 0) (end 3.05 0) (layer F.CrtYd) (width 0.05))
    (fp_text user %R (at 0.3 0) (layer F.Fab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad 1 np_thru_hole circle (at 0 0) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask))
  )

  (dimension 48 (width 0.15) (layer Dwgs.User)
    (gr_text "48,000 mm" (at 165.3 47 270) (layer Dwgs.User)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (feature1 (pts (xy 161 71) (xy 164.586421 71)))
    (feature2 (pts (xy 161 23) (xy 164.586421 23)))
    (crossbar (pts (xy 164 23) (xy 164 71)))
    (arrow1a (pts (xy 164 71) (xy 163.413579 69.873496)))
    (arrow1b (pts (xy 164 71) (xy 164.586421 69.873496)))
    (arrow2a (pts (xy 164 23) (xy 163.413579 24.126504)))
    (arrow2b (pts (xy 164 23) (xy 164.586421 24.126504)))
  )
  (dimension 64 (width 0.15) (layer Dwgs.User)
    (gr_text "64,000 mm" (at 129 18.7) (layer Dwgs.User)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (feature1 (pts (xy 161 23) (xy 161 19.413579)))
    (feature2 (pts (xy 97 23) (xy 97 19.413579)))
    (crossbar (pts (xy 97 20) (xy 161 20)))
    (arrow1a (pts (xy 161 20) (xy 159.873496 20.586421)))
    (arrow1b (pts (xy 161 20) (xy 159.873496 19.413579)))
    (arrow2a (pts (xy 97 20) (xy 98.126504 20.586421)))
    (arrow2b (pts (xy 97 20) (xy 98.126504 19.413579)))
  )
  (gr_arc (start 137 70) (end 136 70) (angle -90) (layer Edge.Cuts) (width 0.05))
  (gr_arc (start 105 70) (end 105 71) (angle -90) (layer Edge.Cuts) (width 0.05))
  (gr_line (start 101 71) (end 105 71) (layer Edge.Cuts) (width 0.05) (tstamp 6041707D))
  (gr_arc (start 101 67) (end 97 67) (angle -90) (layer Edge.Cuts) (width 0.05))
  (gr_arc (start 157 67) (end 157 71) (angle -90) (layer Edge.Cuts) (width 0.05))
  (gr_arc (start 157 27) (end 161 27) (angle -90) (layer Edge.Cuts) (width 0.05))
  (gr_arc (start 101 27) (end 101 23) (angle -90) (layer Edge.Cuts) (width 0.05))
  (gr_text "Wifi RGB Controller\n[WS2812b]" (at 137.16 65.024) (layer F.SilkS)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text "Rev. 2" (at 139.7 69.088) (layer F.SilkS)
    (effects (font (size 1 1) (thickness 0.15)))
  )
  (gr_text PRG (at 104.14 62.992) (layer F.SilkS) (tstamp 60417D84)
    (effects (font (size 1 1) (thickness 0.15)) (justify right))
  )
  (gr_text RST (at 139.192 54.356) (layer F.SilkS)
    (effects (font (size 1 1) (thickness 0.15)) (justify right))
  )
  (gr_text GND (at 156.464 52.832) (layer F.SilkS) (tstamp 60417B6D)
    (effects (font (size 1 0.4) (thickness 0.075)) (justify right))
  )
  (gr_text 5V (at 156.464 47.752) (layer F.SilkS) (tstamp 60417B6D)
    (effects (font (size 1 0.4) (thickness 0.075)) (justify right))
  )
  (gr_text DO (at 156.464 50.292) (layer F.SilkS) (tstamp 60417B1E)
    (effects (font (size 1 0.4) (thickness 0.075)) (justify right))
  )
  (gr_text "GND\n" (at 103.632 53.848) (layer F.SilkS) (tstamp 60417A18)
    (effects (font (size 1 0.4) (thickness 0.075)) (justify right))
  )
  (gr_text DI (at 103.124 51.308) (layer F.SilkS) (tstamp 60417A18)
    (effects (font (size 1 0.4) (thickness 0.075)) (justify right))
  )
  (gr_text 5V (at 103.124 48.768) (layer F.SilkS) (tstamp 60417923)
    (effects (font (size 1 0.4) (thickness 0.075)) (justify right))
  )
  (gr_text RXD (at 153.924 30.48) (layer F.SilkS) (tstamp 60417923)
    (effects (font (size 1 0.8) (thickness 0.15)) (justify left))
  )
  (gr_text TXD (at 152.4 31.496 90) (layer F.SilkS) (tstamp 60417923)
    (effects (font (size 1 0.8) (thickness 0.15)) (justify left))
  )
  (gr_text 3.3V (at 149.86 31.496 90) (layer F.SilkS) (tstamp 60417923)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text GND (at 144.78 31.496 90) (layer F.SilkS) (tstamp 60417923)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text 3.3V (at 135.128 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text 5V (at 132.588 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text GPIO16 (at 130.048 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text GPIO14 (at 127.508 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text GPIO13 (at 124.968 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text GPIO12 (at 122.428 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text GPIO05 (at 119.888 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text DATA (at 117.348 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text ADC (at 114.808 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text SCLK (at 112.268 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text MOSI (at 109.728 31.496 90) (layer F.SilkS) (tstamp 60417835)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_text GND (at 107.188 31.496 90) (layer F.SilkS)
    (effects (font (size 1 1) (thickness 0.15)) (justify left))
  )
  (gr_line (start 137 71) (end 157 71) (layer Edge.Cuts) (width 0.05) (tstamp 6041707E))
  (gr_line (start 106 64) (end 106 70) (layer Edge.Cuts) (width 0.05))
  (gr_line (start 136 64) (end 106 64) (layer Edge.Cuts) (width 0.05))
  (gr_line (start 136 70) (end 136 64) (layer Edge.Cuts) (width 0.05))
  (gr_line (start 161 27) (end 161 67) (layer Edge.Cuts) (width 0.05))
  (gr_line (start 101 23) (end 157 23) (layer Edge.Cuts) (width 0.05))
  (gr_line (start 97 67) (end 97 27) (layer Edge.Cuts) (width 0.05))

  (segment (start 136.144 51.816) (end 136.144 51.856) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 106.516 53.848) (end 106.68 54.012) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 100.584 53.848) (end 106.516 53.848) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 109.966501 48.602001) (end 112.621999 48.602001) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 109.22 47.8555) (end 109.966501 48.602001) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 112.621999 48.602001) (end 113.304 47.92) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 103.151968 53.848) (end 106.999968 50) (width 0.5) (layer B.Cu) (net 1))
  (segment (start 100.584 53.848) (end 103.151968 53.848) (width 0.5) (layer B.Cu) (net 1))
  (via (at 106.999968 50) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 1))
  (segment (start 106.999968 49.375532) (end 106.999968 50) (width 0.5) (layer F.Cu) (net 1))
  (via (at 136.144 51.856) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 1))
  (segment (start 129.14 40.982) (end 129.54 40.982) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 127.44 39.282) (end 129.14 40.982) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 127.34 39.282) (end 127.44 39.282) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 144.78 35.593882) (end 144.78 33.02) (width 1.5) (layer B.Cu) (net 1))
  (segment (start 144.373882 36) (end 144.78 35.593882) (width 1.5) (layer B.Cu) (net 1))
  (segment (start 139 36) (end 144.373882 36) (width 1.5) (layer B.Cu) (net 1))
  (segment (start 136.144 38.856) (end 139 36) (width 1.5) (layer B.Cu) (net 1))
  (segment (start 136.144 51.856) (end 136.144 38.856) (width 1.5) (layer B.Cu) (net 1))
  (segment (start 134.518 57.536) (end 140.818 57.536) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 136.144 55.91) (end 134.518 57.536) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 136.144 51.856) (end 136.144 55.91) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 149.87601 61.13601) (end 148.176 59.436) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 157.988 56.760177) (end 153.612167 61.13601) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 153.612167 61.13601) (end 149.87601 61.13601) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 157.988 52.832) (end 157.988 56.760177) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 146.036 57.296) (end 146.036 56.49) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 148.176 59.436) (end 146.036 57.296) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 145.796 57.536) (end 146.036 57.296) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 140.818 57.536) (end 145.796 57.536) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 140.818 57.17) (end 143.256 54.732) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 140.818 57.536) (end 140.818 57.17) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 106.516 57.294) (end 106.274 57.536) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 106.516 53.848) (end 106.516 57.294) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 99.974 54.458) (end 100.584 53.848) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 99.974 57.536) (end 99.974 54.458) (width 1.5) (layer F.Cu) (net 1))
  (segment (start 107.26649 49.10901) (end 106.999968 49.375532) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 108.06999 49.10901) (end 107.26649 49.10901) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 109.22 47.959) (end 108.06999 49.10901) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 158.3005 38.6865) (end 159.188 37.799) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 156.613 38.6865) (end 158.3005 38.6865) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 159.188 39.624) (end 159.188 37.799) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 159.188 39.624) (end 159.188 42.324) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 159.188 42.324) (end 159.188 44.549) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 157.5005 44.549) (end 156.613 43.6615) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 159.188 44.549) (end 157.5005 44.549) (width 0.5) (layer F.Cu) (net 1))
  (segment (start 156.528 38.7715) (end 156.613 38.6865) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 156.528 39.874) (end 156.528 38.7715) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 129.54 40.982) (end 131.738 40.982) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 132.08 40.64) (end 131.738 40.982) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 132.08 40.158) (end 132.08 40.64) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 132.08 41.324) (end 131.738 40.982) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 132.08 41.63) (end 132.08 41.324) (width 0.25) (layer F.Cu) (net 1))
  (segment (start 157.988 47.752) (end 154.94 47.752) (width 1.5) (layer F.Cu) (net 2))
  (segment (start 154.620999 47.432999) (end 154.94 47.752) (width 0.25) (layer F.Cu) (net 2))
  (segment (start 154.620999 43.499001) (end 154.620999 47.432999) (width 0.25) (layer F.Cu) (net 2))
  (segment (start 155.646 42.474) (end 154.620999 43.499001) (width 0.25) (layer F.Cu) (net 2))
  (segment (start 156.528 42.474) (end 155.646 42.474) (width 0.25) (layer F.Cu) (net 2))
  (segment (start 150.876 56.73) (end 150.636 56.49) (width 1.5) (layer F.Cu) (net 2))
  (segment (start 150.876 59.436) (end 150.876 56.73) (width 1.5) (layer F.Cu) (net 2))
  (segment (start 150.876 59.436) (end 152.908 59.436) (width 1.5) (layer F.Cu) (net 2))
  (segment (start 152.908 59.436) (end 153.416 58.928) (width 1.5) (layer F.Cu) (net 2))
  (segment (start 153.416 49.276) (end 154.94 47.752) (width 1.5) (layer F.Cu) (net 2))
  (segment (start 153.416 58.928) (end 153.416 49.276) (width 1.5) (layer F.Cu) (net 2))
  (segment (start 146.153 47.801) (end 146.304 47.65) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 141.224 47.801) (end 146.153 47.801) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 128.672 47.752) (end 128.504 47.92) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 136.928001 31.219999) (end 135.128 33.02) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 149.26208 31.219999) (end 136.928001 31.219999) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 149.86 31.817919) (end 149.26208 31.219999) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 149.86 33.02) (end 149.86 31.817919) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 105.156 46.628564) (end 105.156 47.959) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 126.028002 43.434) (end 108.350564 43.434) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 128.504 45.909998) (end 126.028002 43.434) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 108.350564 43.434) (end 105.156 46.628564) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 128.504 47.92) (end 128.504 45.909998) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 147.193 35.687) (end 149.86 33.02) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 145.731 35.687) (end 147.193 35.687) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 146.304 47.65) (end 147.422 47.65) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 149.86 33.02) (end 150 46) (width 1.5) (layer B.Cu) (net 3))
  (segment (start 150 46) (end 149.86 46.14) (width 1.5) (layer B.Cu) (net 3) (tstamp 60453627))
  (via (at 150 46) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 3))
  (segment (start 148.35 47.65) (end 150 46) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 146.304 47.65) (end 148.35 47.65) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 150 42) (end 150 42) (width 0.25) (layer F.Cu) (net 3) (tstamp 604539FA))
  (via (at 150 42) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 3))
  (segment (start 143.431043 42.401042) (end 143.431043 44.879043) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 143.976977 45.424977) (end 147.575022 45.424977) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 142.24 41.209999) (end 143.431043 42.401042) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 150 43) (end 150 42) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 142.24 40.132) (end 142.24 41.209999) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 147.575022 45.424977) (end 150 43) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 143.431043 44.879043) (end 143.976977 45.424977) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 141.605 39.497) (end 142.24 40.132) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 140.781 39.497) (end 141.605 39.497) (width 0.25) (layer F.Cu) (net 3))
  (segment (start 135.976 47.92) (end 136.144 47.752) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 128.504 47.92) (end 135.976 47.92) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 141.175 47.752) (end 141.224 47.801) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 136.144 47.752) (end 141.175 47.752) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 147.178 52.832) (end 143.256 52.832) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 148.336 53.99) (end 147.178 52.832) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 148.336 56.49) (end 148.336 53.99) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 143.256 49.833) (end 141.175 47.752) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 143.256 52.832) (end 143.256 49.833) (width 1.5) (layer F.Cu) (net 3))
  (segment (start 134.325 45.933) (end 136.144 47.752) (width 0.5) (layer F.Cu) (net 3))
  (segment (start 134.325 45) (end 134.325 45.933) (width 0.5) (layer F.Cu) (net 3))
  (segment (start 115.316 52.832) (end 115.316 52.832) (width 0.25) (layer B.Cu) (net 4) (tstamp 6041677E))
  (via (at 115.316 52.832) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 4))
  (segment (start 114.804 55.92) (end 113.304 55.92) (width 0.25) (layer F.Cu) (net 4))
  (segment (start 115.316 55.408) (end 114.804 55.92) (width 0.25) (layer F.Cu) (net 4))
  (segment (start 115.316 52.832) (end 115.316 55.408) (width 0.25) (layer F.Cu) (net 4))
  (segment (start 116.251967 51.896033) (end 115.316 52.832) (width 0.25) (layer B.Cu) (net 4))
  (segment (start 116.251967 34.116033) (end 116.251967 51.896033) (width 0.25) (layer B.Cu) (net 4))
  (segment (start 117.348 33.02) (end 116.251967 34.116033) (width 0.25) (layer B.Cu) (net 4))
  (segment (start 157.988 50.292) (end 142.24 50.292) (width 0.5) (layer B.Cu) (net 4))
  (segment (start 142.24 50.292) (end 132.08 60.452) (width 0.5) (layer B.Cu) (net 4))
  (segment (start 132.08 60.452) (end 132.08 60.452) (width 0.25) (layer B.Cu) (net 4) (tstamp 604532D9))
  (via (at 132.08 60.452) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 4))
  (segment (start 131.661999 60.870001) (end 132.08 60.452) (width 0.5) (layer F.Cu) (net 4))
  (segment (start 121.119189 60.870001) (end 131.661999 60.870001) (width 0.5) (layer F.Cu) (net 4))
  (segment (start 116.169188 55.92) (end 121.119189 60.870001) (width 0.5) (layer F.Cu) (net 4))
  (segment (start 113.304 55.92) (end 116.169188 55.92) (width 0.5) (layer F.Cu) (net 4))
  (segment (start 141.977758 41.58699) (end 141.802758 41.41199) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 141.160588 40.767) (end 140.781 40.767) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 142.981032 42.587442) (end 141.160588 40.767) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 142.981032 45.065443) (end 142.981032 42.587442) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 143.790577 45.874988) (end 142.981032 45.065443) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 152.61239 41.174) (end 147.911402 45.874988) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 156.528 41.174) (end 152.61239 41.174) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 147.911402 45.874988) (end 143.790577 45.874988) (width 0.25) (layer F.Cu) (net 5))
  (segment (start 141.756 42.037) (end 140.781 42.037) (width 0.25) (layer F.Cu) (net 7))
  (segment (start 142.531021 42.812021) (end 141.756 42.037) (width 0.25) (layer F.Cu) (net 7))
  (segment (start 142.531021 45.251843) (end 142.531021 42.812021) (width 0.25) (layer F.Cu) (net 7))
  (segment (start 143.604177 46.324999) (end 142.531021 45.251843) (width 0.25) (layer F.Cu) (net 7))
  (segment (start 148.12498 46.324999) (end 143.604177 46.324999) (width 0.25) (layer F.Cu) (net 7))
  (segment (start 152.5988 41.824) (end 156.528 41.824) (width 0.25) (layer F.Cu) (net 7))
  (segment (start 148.12498 46.29782) (end 152.5988 41.824) (width 0.25) (layer F.Cu) (net 7))
  (segment (start 148.12498 46.324999) (end 148.12498 46.29782) (width 0.25) (layer F.Cu) (net 7))
  (via (at 115.823976 60.96) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 9))
  (segment (start 113.304 61.92) (end 114.863976 61.92) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 114.863976 61.92) (end 115.823976 60.96) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 153.575001 33.02) (end 154.94 33.02) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 152.964001 34.195001) (end 153.575001 33.584001) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 153.575001 33.584001) (end 153.575001 33.02) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 151.060411 34.195001) (end 152.964001 34.195001) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 150.6104 34.645012) (end 151.060411 34.195001) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 149.758988 34.645012) (end 150.6104 34.645012) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 146.812 37.592) (end 149.758988 34.645012) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 142.179232 37.592) (end 146.812 37.592) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 141.544232 38.227) (end 142.179232 37.592) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 140.781 38.227) (end 141.544232 38.227) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 140.150669 37.596669) (end 118.876651 37.596669) (width 0.25) (layer F.Cu) (net 9))
  (segment (start 140.781 38.227) (end 140.150669 37.596669) (width 0.25) (layer F.Cu) (net 9))
  (via (at 118.876651 37.596669) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 9))
  (segment (start 118.876651 37.030984) (end 118.876651 37.596669) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 118.421667 36.576) (end 118.876651 37.030984) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 116.223975 52.560436) (end 116.701978 52.082433) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 116.701978 37.222022) (end 117.348 36.576) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 116.223975 60.560001) (end 116.223975 52.560436) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 117.348 36.576) (end 118.421667 36.576) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 116.701978 52.082433) (end 116.701978 37.222022) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 115.823976 60.96) (end 116.223975 60.560001) (width 0.25) (layer B.Cu) (net 9))
  (segment (start 149.321409 34.195001) (end 150.424001 34.195001) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 147.18442 36.33199) (end 149.321409 34.195001) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 140.781 36.957) (end 141.40601 36.33199) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 141.40601 36.33199) (end 147.18442 36.33199) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 151.599002 33.02) (end 152.4 33.02) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 150.424001 34.195001) (end 151.599002 33.02) (width 0.25) (layer F.Cu) (net 10))
  (via (at 117.856 37.592) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 10))
  (segment (start 140.481 36.657) (end 118.791 36.657) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 118.791 36.657) (end 117.856 37.592) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 140.781 36.957) (end 140.481 36.657) (width 0.25) (layer F.Cu) (net 10))
  (via (at 117.048382 59.447956) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 10))
  (segment (start 116.576338 59.92) (end 117.048382 59.447956) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 117.856 37.592) (end 117.151989 38.296011) (width 0.25) (layer B.Cu) (net 10))
  (segment (start 117.151989 38.296011) (end 117.151989 59.344349) (width 0.25) (layer B.Cu) (net 10))
  (segment (start 113.304 59.92) (end 116.576338 59.92) (width 0.25) (layer F.Cu) (net 10))
  (segment (start 117.151989 59.344349) (end 117.048382 59.447956) (width 0.25) (layer B.Cu) (net 10))
  (segment (start 133.09134 49.722338) (end 133.09134 49.78866) (width 0.25) (layer B.Cu) (net 12))
  (segment (start 128.504 55.92) (end 130.004 55.92) (width 0.25) (layer F.Cu) (net 12))
  (via (at 133.09134 49.78866) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 12))
  (segment (start 130.004 55.92) (end 133.09134 52.83266) (width 0.25) (layer F.Cu) (net 12))
  (segment (start 133.09134 52.83266) (end 133.09134 49.78866) (width 0.25) (layer F.Cu) (net 12))
  (segment (start 130.048 33.02) (end 130.048 46.678998) (width 0.25) (layer B.Cu) (net 12))
  (segment (start 130.048 46.678998) (end 133.09134 49.722338) (width 0.25) (layer B.Cu) (net 12))
  (via (at 132.08 49.784) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 13))
  (segment (start 128.504 53.92) (end 130.992 53.92) (width 0.25) (layer F.Cu) (net 13))
  (segment (start 132.08 52.832) (end 132.08 49.784) (width 0.25) (layer F.Cu) (net 13))
  (segment (start 130.992 53.92) (end 132.08 52.832) (width 0.25) (layer F.Cu) (net 13))
  (segment (start 132.08 49.726996) (end 132.08 49.784) (width 0.25) (layer B.Cu) (net 13))
  (segment (start 127.508 33.02) (end 127.508 45.154996) (width 0.25) (layer B.Cu) (net 13))
  (segment (start 127.508 45.154996) (end 132.08 49.726996) (width 0.25) (layer B.Cu) (net 13))
  (via (at 131.064 49.784) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 14))
  (segment (start 124.968 33.02) (end 124.968 43.688) (width 0.25) (layer B.Cu) (net 14))
  (segment (start 124.968 43.688) (end 131.064 49.784) (width 0.25) (layer B.Cu) (net 14))
  (segment (start 128.504 49.92) (end 130.928 49.92) (width 0.25) (layer F.Cu) (net 14))
  (segment (start 130.928 49.92) (end 131.064 49.784) (width 0.25) (layer F.Cu) (net 14))
  (segment (start 128.504 51.92) (end 130.22207 51.92) (width 0.25) (layer F.Cu) (net 15))
  (segment (start 122.428 33.02) (end 122.428 43.080355) (width 0.25) (layer B.Cu) (net 15))
  (segment (start 130.22207 51.92) (end 131.0277 51.11437) (width 0.25) (layer F.Cu) (net 15))
  (segment (start 122.428 43.080355) (end 130.462015 51.11437) (width 0.25) (layer B.Cu) (net 15))
  (via (at 131.0277 51.11437) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 15))
  (segment (start 130.462015 51.11437) (end 131.0277 51.11437) (width 0.25) (layer B.Cu) (net 15))
  (segment (start 112.743998 57.92) (end 113.304 57.92) (width 0.5) (layer F.Cu) (net 16))
  (segment (start 107.992008 52.620008) (end 107.992008 55.91004) (width 0.5) (layer F.Cu) (net 16))
  (segment (start 107.992008 55.91004) (end 109.319998 57.23803) (width 0.5) (layer F.Cu) (net 16))
  (segment (start 112.62203 57.23803) (end 113.304 57.92) (width 0.5) (layer F.Cu) (net 16))
  (segment (start 101.896008 52.620008) (end 107.992008 52.620008) (width 0.5) (layer F.Cu) (net 16))
  (segment (start 109.319998 57.23803) (end 112.62203 57.23803) (width 0.5) (layer F.Cu) (net 16))
  (segment (start 100.584 51.308) (end 101.896008 52.620008) (width 0.5) (layer F.Cu) (net 16))
  (segment (start 117.856 59.436) (end 117.856 59.436) (width 0.25) (layer B.Cu) (net 16) (tstamp 604165D2))
  (segment (start 113.304 57.92) (end 117.107862 57.92) (width 0.25) (layer F.Cu) (net 16))
  (via (at 118.072583 59.450406) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 16))
  (segment (start 118.072583 58.884721) (end 118.072583 59.450406) (width 0.25) (layer F.Cu) (net 16))
  (segment (start 117.107862 57.92) (end 118.072583 58.884721) (width 0.25) (layer F.Cu) (net 16))
  (segment (start 117.602 39.944322) (end 117.602 58.979823) (width 0.25) (layer B.Cu) (net 16))
  (segment (start 119.888 37.658322) (end 117.602 39.944322) (width 0.25) (layer B.Cu) (net 16))
  (segment (start 117.602 58.979823) (end 118.072583 59.450406) (width 0.25) (layer B.Cu) (net 16))
  (segment (start 119.888 33.02) (end 119.888 37.658322) (width 0.25) (layer B.Cu) (net 16))
  (segment (start 115.57 50.8) (end 115.57 50.8) (width 0.25) (layer B.Cu) (net 17) (tstamp 60416CFF))
  (segment (start 115.57 50.8) (end 115.57 50.8) (width 0.25) (layer B.Cu) (net 17) (tstamp 60416D01))
  (segment (start 115.526957 50.8) (end 115.526955 50.8) (width 0.25) (layer F.Cu) (net 17))
  (segment (start 114.808 50.081043) (end 115.526957 50.8) (width 0.25) (layer B.Cu) (net 17))
  (segment (start 114.808 33.02) (end 114.808 50.081043) (width 0.25) (layer B.Cu) (net 17))
  (via (at 115.526957 50.8) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 17))
  (segment (start 116.041001 51.314044) (end 115.526957 50.8) (width 0.25) (layer F.Cu) (net 17))
  (segment (start 116.041001 54.573001) (end 116.041001 51.314044) (width 0.25) (layer F.Cu) (net 17))
  (segment (start 121.388 59.92) (end 116.041001 54.573001) (width 0.25) (layer F.Cu) (net 17))
  (segment (start 128.504 59.92) (end 121.388 59.92) (width 0.25) (layer F.Cu) (net 17))
  (segment (start 115.904 46.42) (end 111.859605 46.42) (width 0.25) (layer F.Cu) (net 18))
  (via (at 110.914157 47.365448) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 18))
  (segment (start 111.859605 46.42) (end 110.914157 47.365448) (width 0.25) (layer F.Cu) (net 18))
  (segment (start 112.268 46.011605) (end 110.914157 47.365448) (width 0.25) (layer B.Cu) (net 18))
  (segment (start 112.268 33.02) (end 112.268 46.011605) (width 0.25) (layer B.Cu) (net 18))
  (segment (start 117.592 44.958) (end 110.49 44.958) (width 0.25) (layer F.Cu) (net 19))
  (segment (start 117.904 45.27) (end 117.592 44.958) (width 0.25) (layer F.Cu) (net 19))
  (segment (start 117.904 46.42) (end 117.904 45.27) (width 0.25) (layer F.Cu) (net 19))
  (segment (start 109.728 44.196) (end 110.49 44.958) (width 0.25) (layer B.Cu) (net 19))
  (segment (start 109.728 33.02) (end 109.728 44.196) (width 0.25) (layer B.Cu) (net 19))
  (via (at 110.49 44.958) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 19))
  (segment (start 113.0645 49.6805) (end 113.304 49.92) (width 0.5) (layer F.Cu) (net 20))
  (segment (start 109.22 49.6805) (end 113.0645 49.6805) (width 0.5) (layer F.Cu) (net 20))
  (via (at 131.064 45.72) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 21))
  (via (at 134.112016 49.784) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 21))
  (segment (start 131.064 46.736) (end 134.112 49.784) (width 0.25) (layer B.Cu) (net 21))
  (segment (start 134.112 49.784) (end 134.112016 49.784) (width 0.25) (layer B.Cu) (net 21))
  (segment (start 131.064 45.72) (end 131.064 46.736) (width 0.25) (layer B.Cu) (net 21))
  (segment (start 134.112016 52.831984) (end 134.112016 49.784) (width 0.25) (layer F.Cu) (net 21))
  (segment (start 129.024 57.92) (end 134.112016 52.831984) (width 0.25) (layer F.Cu) (net 21))
  (segment (start 128.504 57.92) (end 129.024 57.92) (width 0.25) (layer F.Cu) (net 21))
  (segment (start 131.78 45.72) (end 132.5 45) (width 0.5) (layer F.Cu) (net 21))
  (segment (start 131.064 45.72) (end 131.78 45.72) (width 0.5) (layer F.Cu) (net 21))
  (segment (start 107.292 51.92) (end 113.304 51.92) (width 0.5) (layer F.Cu) (net 22))
  (segment (start 105.156 49.784) (end 107.292 51.92) (width 0.5) (layer F.Cu) (net 22))
  (segment (start 113.864002 53.92) (end 113.304 53.92) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 112.743998 53.92) (end 113.304 53.92) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 109.760208 56.420238) (end 109.728 56.38803) (width 0.5) (layer B.Cu) (net 23))
  (segment (start 109.760208 58.088036) (end 109.760208 56.420238) (width 0.5) (layer B.Cu) (net 23))
  (segment (start 111.554 53.92) (end 109.728 55.746) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 113.304 53.92) (end 111.554 53.92) (width 0.5) (layer F.Cu) (net 23))
  (via (at 109.728 56.38803) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 23))
  (via (at 109.760208 58.088036) (size 0.8) (drill 0.4) (layers F.Cu B.Cu) (net 23))
  (segment (start 109.728 55.746) (end 109.728 56.38803) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 108.844 61.336) (end 106.274 61.336) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 109.760208 60.419792) (end 108.844 61.336) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 109.760208 58.088036) (end 109.760208 60.419792) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 106.274 61.336) (end 99.974 61.336) (width 0.5) (layer F.Cu) (net 23))
  (segment (start 133.934 61.92) (end 134.518 61.336) (width 0.25) (layer F.Cu) (net 24))
  (segment (start 128.504 61.92) (end 133.934 61.92) (width 0.25) (layer F.Cu) (net 24))
  (segment (start 134.518 61.336) (end 140.818 61.336) (width 0.25) (layer F.Cu) (net 24))
  (segment (start 140.335 43.307) (end 140.781 43.307) (width 0.25) (layer F.Cu) (net 29))
  (segment (start 135.664 38.636) (end 140.335 43.307) (width 0.25) (layer F.Cu) (net 29))
  (segment (start 132.108 38.636) (end 132.08 38.608) (width 0.25) (layer F.Cu) (net 29))
  (segment (start 135.664 38.636) (end 132.108 38.636) (width 0.25) (layer F.Cu) (net 29))
  (segment (start 130.214 38.608) (end 129.54 39.282) (width 0.25) (layer F.Cu) (net 29))
  (segment (start 132.08 38.608) (end 130.214 38.608) (width 0.25) (layer F.Cu) (net 29))
  (segment (start 128.265001 41.907001) (end 127.34 40.982) (width 0.25) (layer F.Cu) (net 30))
  (segment (start 130.046 43.688) (end 128.265001 41.907001) (width 0.25) (layer F.Cu) (net 30))
  (segment (start 136.763312 44.577) (end 135.874312 43.688) (width 0.25) (layer F.Cu) (net 30))
  (segment (start 140.781 44.577) (end 136.763312 44.577) (width 0.25) (layer F.Cu) (net 30))
  (segment (start 132.08 43.18) (end 131.572 43.18) (width 0.25) (layer F.Cu) (net 30))
  (segment (start 131.572 43.18) (end 131.064 43.688) (width 0.25) (layer F.Cu) (net 30))
  (segment (start 131.064 43.688) (end 130.046 43.688) (width 0.25) (layer F.Cu) (net 30))
  (segment (start 135.874312 43.688) (end 131.064 43.688) (width 0.25) (layer F.Cu) (net 30))

  (zone (net 2) (net_name +5V) (layer B.Cu) (tstamp 6049174E) (hatch edge 0.508)
    (connect_pads (clearance 0.508))
    (min_thickness 0.254)
    (fill yes (arc_segments 32) (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 161 71) (xy 97 71) (xy 97 23) (xy 161 23)
      )
    )
    (filled_polygon
      (pts
        (xy 157.648126 23.726714) (xy 158.271572 23.914943) (xy 158.846579 24.220681) (xy 159.351247 24.632279) (xy 159.766362 25.134067)
        (xy 160.076105 25.706924) (xy 160.268682 26.329039) (xy 160.34 27.007584) (xy 160.340001 66.967711) (xy 160.273286 67.648126)
        (xy 160.085057 68.27157) (xy 159.779323 68.846573) (xy 159.367721 69.351248) (xy 158.865933 69.766362) (xy 158.293077 70.076104)
        (xy 157.670961 70.268682) (xy 156.992417 70.34) (xy 137.032279 70.34) (xy 136.934576 70.33042) (xy 136.871643 70.31142)
        (xy 136.813594 70.280554) (xy 136.762657 70.239011) (xy 136.720752 70.188356) (xy 136.689485 70.130529) (xy 136.670044 70.067728)
        (xy 136.66 69.972165) (xy 136.66 66.779872) (xy 154.765 66.779872) (xy 154.765 67.220128) (xy 154.85089 67.651925)
        (xy 155.019369 68.058669) (xy 155.263962 68.424729) (xy 155.575271 68.736038) (xy 155.941331 68.980631) (xy 156.348075 69.14911)
        (xy 156.779872 69.235) (xy 157.220128 69.235) (xy 157.651925 69.14911) (xy 158.058669 68.980631) (xy 158.424729 68.736038)
        (xy 158.736038 68.424729) (xy 158.980631 68.058669) (xy 159.14911 67.651925) (xy 159.235 67.220128) (xy 159.235 66.779872)
        (xy 159.14911 66.348075) (xy 158.980631 65.941331) (xy 158.736038 65.575271) (xy 158.424729 65.263962) (xy 158.058669 65.019369)
        (xy 157.651925 64.85089) (xy 157.220128 64.765) (xy 156.779872 64.765) (xy 156.348075 64.85089) (xy 155.941331 65.019369)
        (xy 155.575271 65.263962) (xy 155.263962 65.575271) (xy 155.019369 65.941331) (xy 154.85089 66.348075) (xy 154.765 66.779872)
        (xy 136.66 66.779872) (xy 136.66 64.032419) (xy 136.663193 64) (xy 136.65045 63.870617) (xy 136.61271 63.746207)
        (xy 136.551425 63.63155) (xy 136.468948 63.531052) (xy 136.36845 63.448575) (xy 136.253793 63.38729) (xy 136.129383 63.34955)
        (xy 136.032419 63.34) (xy 136 63.336807) (xy 135.967581 63.34) (xy 106.032419 63.34) (xy 106 63.336807)
        (xy 105.967581 63.34) (xy 105.870617 63.34955) (xy 105.746207 63.38729) (xy 105.63155 63.448575) (xy 105.531052 63.531052)
        (xy 105.448575 63.63155) (xy 105.38729 63.746207) (xy 105.34955 63.870617) (xy 105.336807 64) (xy 105.34 64.032419)
        (xy 105.340001 69.967711) (xy 105.33042 70.065424) (xy 105.31142 70.128357) (xy 105.280554 70.186406) (xy 105.239011 70.237343)
        (xy 105.188356 70.279248) (xy 105.130529 70.310515) (xy 105.067728 70.329956) (xy 104.972165 70.34) (xy 101.032279 70.34)
        (xy 100.351874 70.273286) (xy 99.72843 70.085057) (xy 99.153427 69.779323) (xy 98.648752 69.367721) (xy 98.233638 68.865933)
        (xy 97.923896 68.293077) (xy 97.731318 67.670961) (xy 97.66 66.992417) (xy 97.66 66.779872) (xy 98.765 66.779872)
        (xy 98.765 67.220128) (xy 98.85089 67.651925) (xy 99.019369 68.058669) (xy 99.263962 68.424729) (xy 99.575271 68.736038)
        (xy 99.941331 68.980631) (xy 100.348075 69.14911) (xy 100.779872 69.235) (xy 101.220128 69.235) (xy 101.651925 69.14911)
        (xy 102.058669 68.980631) (xy 102.424729 68.736038) (xy 102.736038 68.424729) (xy 102.980631 68.058669) (xy 103.14911 67.651925)
        (xy 103.235 67.220128) (xy 103.235 66.779872) (xy 103.14911 66.348075) (xy 102.980631 65.941331) (xy 102.736038 65.575271)
        (xy 102.424729 65.263962) (xy 102.058669 65.019369) (xy 101.651925 64.85089) (xy 101.220128 64.765) (xy 100.779872 64.765)
        (xy 100.348075 64.85089) (xy 99.941331 65.019369) (xy 99.575271 65.263962) (xy 99.263962 65.575271) (xy 99.019369 65.941331)
        (xy 98.85089 66.348075) (xy 98.765 66.779872) (xy 97.66 66.779872) (xy 97.66 56.286091) (xy 108.693 56.286091)
        (xy 108.693 56.489969) (xy 108.732774 56.689928) (xy 108.810795 56.878286) (xy 108.875209 56.974688) (xy 108.875208 57.549581)
        (xy 108.843003 57.59778) (xy 108.764982 57.786138) (xy 108.725208 57.986097) (xy 108.725208 58.189975) (xy 108.764982 58.389934)
        (xy 108.843003 58.578292) (xy 108.956271 58.74781) (xy 109.100434 58.891973) (xy 109.269952 59.005241) (xy 109.45831 59.083262)
        (xy 109.658269 59.123036) (xy 109.862147 59.123036) (xy 110.062106 59.083262) (xy 110.250464 59.005241) (xy 110.419982 58.891973)
        (xy 110.564145 58.74781) (xy 110.677413 58.578292) (xy 110.755434 58.389934) (xy 110.795208 58.189975) (xy 110.795208 57.986097)
        (xy 110.755434 57.786138) (xy 110.677413 57.59778) (xy 110.645208 57.549582) (xy 110.645208 56.878279) (xy 110.723226 56.689928)
        (xy 110.763 56.489969) (xy 110.763 56.286091) (xy 110.723226 56.086132) (xy 110.645205 55.897774) (xy 110.531937 55.728256)
        (xy 110.387774 55.584093) (xy 110.218256 55.470825) (xy 110.029898 55.392804) (xy 109.829939 55.35303) (xy 109.626061 55.35303)
        (xy 109.426102 55.392804) (xy 109.237744 55.470825) (xy 109.068226 55.584093) (xy 108.924063 55.728256) (xy 108.810795 55.897774)
        (xy 108.732774 56.086132) (xy 108.693 56.286091) (xy 97.66 56.286091) (xy 97.66 52.998) (xy 99.095928 52.998)
        (xy 99.095928 54.698) (xy 99.108188 54.822482) (xy 99.144498 54.94218) (xy 99.203463 55.052494) (xy 99.282815 55.149185)
        (xy 99.379506 55.228537) (xy 99.48982 55.287502) (xy 99.609518 55.323812) (xy 99.734 55.336072) (xy 101.434 55.336072)
        (xy 101.558482 55.323812) (xy 101.67818 55.287502) (xy 101.788494 55.228537) (xy 101.885185 55.149185) (xy 101.964537 55.052494)
        (xy 102.023502 54.94218) (xy 102.059812 54.822482) (xy 102.068625 54.733) (xy 103.108499 54.733) (xy 103.151968 54.737281)
        (xy 103.195437 54.733) (xy 103.195445 54.733) (xy 103.325458 54.720195) (xy 103.492281 54.669589) (xy 103.646027 54.587411)
        (xy 103.780785 54.476817) (xy 103.808502 54.443044) (xy 107.245012 51.006535) (xy 107.301866 50.995226) (xy 107.490224 50.917205)
        (xy 107.659742 50.803937) (xy 107.803905 50.659774) (xy 107.917173 50.490256) (xy 107.995194 50.301898) (xy 108.034968 50.101939)
        (xy 108.034968 49.898061) (xy 107.995194 49.698102) (xy 107.917173 49.509744) (xy 107.803905 49.340226) (xy 107.659742 49.196063)
        (xy 107.490224 49.082795) (xy 107.301866 49.004774) (xy 107.101907 48.965) (xy 106.898029 48.965) (xy 106.69807 49.004774)
        (xy 106.509712 49.082795) (xy 106.340194 49.196063) (xy 106.196031 49.340226) (xy 106.082763 49.509744) (xy 106.004742 49.698102)
        (xy 105.993433 49.754956) (xy 102.78539 52.963) (xy 102.068625 52.963) (xy 102.059812 52.873518) (xy 102.023502 52.75382)
        (xy 101.964537 52.643506) (xy 101.885185 52.546815) (xy 101.788494 52.467463) (xy 101.67818 52.408498) (xy 101.60562 52.386487)
        (xy 101.737475 52.254632) (xy 101.89999 52.011411) (xy 102.011932 51.741158) (xy 102.069 51.45426) (xy 102.069 51.16174)
        (xy 102.011932 50.874842) (xy 101.89999 50.604589) (xy 101.737475 50.361368) (xy 101.530632 50.154525) (xy 101.348466 50.032805)
        (xy 101.465355 49.963178) (xy 101.681588 49.768269) (xy 101.855641 49.53492) (xy 101.980825 49.272099) (xy 102.025476 49.12489)
        (xy 101.904155 48.895) (xy 100.711 48.895) (xy 100.711 48.915) (xy 100.457 48.915) (xy 100.457 48.895)
        (xy 99.263845 48.895) (xy 99.142524 49.12489) (xy 99.187175 49.272099) (xy 99.312359 49.53492) (xy 99.486412 49.768269)
        (xy 99.702645 49.963178) (xy 99.819534 50.032805) (xy 99.637368 50.154525) (xy 99.430525 50.361368) (xy 99.26801 50.604589)
        (xy 99.156068 50.874842) (xy 99.099 51.16174) (xy 99.099 51.45426) (xy 99.156068 51.741158) (xy 99.26801 52.011411)
        (xy 99.430525 52.254632) (xy 99.56238 52.386487) (xy 99.48982 52.408498) (xy 99.379506 52.467463) (xy 99.282815 52.546815)
        (xy 99.203463 52.643506) (xy 99.144498 52.75382) (xy 99.108188 52.873518) (xy 99.095928 52.998) (xy 97.66 52.998)
        (xy 97.66 48.41111) (xy 99.142524 48.41111) (xy 99.263845 48.641) (xy 100.457 48.641) (xy 100.457 47.447186)
        (xy 100.711 47.447186) (xy 100.711 48.641) (xy 101.904155 48.641) (xy 102.025476 48.41111) (xy 101.980825 48.263901)
        (xy 101.855641 48.00108) (xy 101.681588 47.767731) (xy 101.465355 47.572822) (xy 101.215252 47.423843) (xy 100.940891 47.326519)
        (xy 100.711 47.447186) (xy 100.457 47.447186) (xy 100.227109 47.326519) (xy 99.952748 47.423843) (xy 99.702645 47.572822)
        (xy 99.486412 47.767731) (xy 99.312359 48.00108) (xy 99.187175 48.263901) (xy 99.142524 48.41111) (xy 97.66 48.41111)
        (xy 97.66 32.17) (xy 105.699928 32.17) (xy 105.699928 33.87) (xy 105.712188 33.994482) (xy 105.748498 34.11418)
        (xy 105.807463 34.224494) (xy 105.886815 34.321185) (xy 105.983506 34.400537) (xy 106.09382 34.459502) (xy 106.213518 34.495812)
        (xy 106.338 34.508072) (xy 108.038 34.508072) (xy 108.162482 34.495812) (xy 108.28218 34.459502) (xy 108.392494 34.400537)
        (xy 108.489185 34.321185) (xy 108.568537 34.224494) (xy 108.627502 34.11418) (xy 108.649513 34.04162) (xy 108.781368 34.173475)
        (xy 108.968 34.298179) (xy 108.968001 44.158668) (xy 108.964324 44.196) (xy 108.968001 44.233333) (xy 108.974393 44.298226)
        (xy 108.978998 44.344985) (xy 109.022454 44.488246) (xy 109.093026 44.620276) (xy 109.164201 44.707002) (xy 109.188 44.736001)
        (xy 109.216998 44.759799) (xy 109.455 44.997801) (xy 109.455 45.059939) (xy 109.494774 45.259898) (xy 109.572795 45.448256)
        (xy 109.686063 45.617774) (xy 109.830226 45.761937) (xy 109.999744 45.875205) (xy 110.188102 45.953226) (xy 110.388061 45.993)
        (xy 110.591939 45.993) (xy 110.791898 45.953226) (xy 110.980256 45.875205) (xy 111.149774 45.761937) (xy 111.293937 45.617774)
        (xy 111.407205 45.448256) (xy 111.485226 45.259898) (xy 111.508001 45.1454) (xy 111.508001 45.696802) (xy 110.874356 46.330448)
        (xy 110.812218 46.330448) (xy 110.612259 46.370222) (xy 110.423901 46.448243) (xy 110.254383 46.561511) (xy 110.11022 46.705674)
        (xy 109.996952 46.875192) (xy 109.918931 47.06355) (xy 109.879157 47.263509) (xy 109.879157 47.467387) (xy 109.918931 47.667346)
        (xy 109.996952 47.855704) (xy 110.11022 48.025222) (xy 110.254383 48.169385) (xy 110.423901 48.282653) (xy 110.612259 48.360674)
        (xy 110.812218 48.400448) (xy 111.016096 48.400448) (xy 111.216055 48.360674) (xy 111.404413 48.282653) (xy 111.573931 48.169385)
        (xy 111.718094 48.025222) (xy 111.831362 47.855704) (xy 111.909383 47.667346) (xy 111.949157 47.467387) (xy 111.949157 47.405249)
        (xy 112.779003 46.575404) (xy 112.808001 46.551606) (xy 112.85835 46.490256) (xy 112.902974 46.435882) (xy 112.973546 46.303852)
        (xy 112.981938 46.276188) (xy 113.017003 46.160591) (xy 113.028 46.048938) (xy 113.028 46.048929) (xy 113.031676 46.011606)
        (xy 113.028 45.974283) (xy 113.028 34.298178) (xy 113.214632 34.173475) (xy 113.421475 33.966632) (xy 113.538 33.79224)
        (xy 113.654525 33.966632) (xy 113.861368 34.173475) (xy 114.048 34.298179) (xy 114.048001 50.04371) (xy 114.044324 50.081043)
        (xy 114.048001 50.118376) (xy 114.057315 50.212936) (xy 114.058998 50.230028) (xy 114.102454 50.373289) (xy 114.173026 50.505319)
        (xy 114.229695 50.574369) (xy 114.268 50.621044) (xy 114.296998 50.644842) (xy 114.491957 50.839801) (xy 114.491957 50.901939)
        (xy 114.531731 51.101898) (xy 114.609752 51.290256) (xy 114.72302 51.459774) (xy 114.867183 51.603937) (xy 115.036701 51.717205)
        (xy 115.225059 51.795226) (xy 115.233978 51.797) (xy 115.214061 51.797) (xy 115.014102 51.836774) (xy 114.825744 51.914795)
        (xy 114.656226 52.028063) (xy 114.512063 52.172226) (xy 114.398795 52.341744) (xy 114.320774 52.530102) (xy 114.281 52.730061)
        (xy 114.281 52.933939) (xy 114.320774 53.133898) (xy 114.398795 53.322256) (xy 114.512063 53.491774) (xy 114.656226 53.635937)
        (xy 114.825744 53.749205) (xy 115.014102 53.827226) (xy 115.214061 53.867) (xy 115.417939 53.867) (xy 115.463976 53.857843)
        (xy 115.463975 59.988841) (xy 115.33372 60.042795) (xy 115.164202 60.156063) (xy 115.020039 60.300226) (xy 114.906771 60.469744)
        (xy 114.82875 60.658102) (xy 114.788976 60.858061) (xy 114.788976 61.061939) (xy 114.82875 61.261898) (xy 114.906771 61.450256)
        (xy 115.020039 61.619774) (xy 115.164202 61.763937) (xy 115.33372 61.877205) (xy 115.522078 61.955226) (xy 115.722037 61.995)
        (xy 115.925915 61.995) (xy 116.125874 61.955226) (xy 116.314232 61.877205) (xy 116.48375 61.763937) (xy 116.627913 61.619774)
        (xy 116.741181 61.450256) (xy 116.819202 61.261898) (xy 116.858976 61.061939) (xy 116.858976 60.984226) (xy 116.929521 60.852248)
        (xy 116.972978 60.708987) (xy 116.983975 60.597334) (xy 116.983975 60.597324) (xy 116.987651 60.560002) (xy 116.983975 60.522679)
        (xy 116.983975 60.482956) (xy 117.150321 60.482956) (xy 117.35028 60.443182) (xy 117.538638 60.365161) (xy 117.558649 60.35179)
        (xy 117.582327 60.367611) (xy 117.770685 60.445632) (xy 117.970644 60.485406) (xy 118.174522 60.485406) (xy 118.374481 60.445632)
        (xy 118.562839 60.367611) (xy 118.589104 60.350061) (xy 131.045 60.350061) (xy 131.045 60.553939) (xy 131.084774 60.753898)
        (xy 131.162795 60.942256) (xy 131.276063 61.111774) (xy 131.420226 61.255937) (xy 131.589744 61.369205) (xy 131.778102 61.447226)
        (xy 131.978061 61.487) (xy 132.181939 61.487) (xy 132.381898 61.447226) (xy 132.570256 61.369205) (xy 132.739774 61.255937)
        (xy 132.883937 61.111774) (xy 132.997205 60.942256) (xy 133.075226 60.753898) (xy 133.086535 60.697043) (xy 142.606579 51.177)
        (xy 156.793344 51.177) (xy 156.834525 51.238632) (xy 157.041368 51.445475) (xy 157.21576 51.562) (xy 157.041368 51.678525)
        (xy 156.834525 51.885368) (xy 156.67201 52.128589) (xy 156.560068 52.398842) (xy 156.503 52.68574) (xy 156.503 52.97826)
        (xy 156.560068 53.265158) (xy 156.67201 53.535411) (xy 156.834525 53.778632) (xy 157.041368 53.985475) (xy 157.284589 54.14799)
        (xy 157.554842 54.259932) (xy 157.84174 54.317) (xy 158.13426 54.317) (xy 158.421158 54.259932) (xy 158.691411 54.14799)
        (xy 158.934632 53.985475) (xy 159.141475 53.778632) (xy 159.30399 53.535411) (xy 159.415932 53.265158) (xy 159.473 52.97826)
        (xy 159.473 52.68574) (xy 159.415932 52.398842) (xy 159.30399 52.128589) (xy 159.141475 51.885368) (xy 158.934632 51.678525)
        (xy 158.76024 51.562) (xy 158.934632 51.445475) (xy 159.141475 51.238632) (xy 159.30399 50.995411) (xy 159.415932 50.725158)
        (xy 159.473 50.43826) (xy 159.473 50.14574) (xy 159.415932 49.858842) (xy 159.30399 49.588589) (xy 159.141475 49.345368)
        (xy 159.00962 49.213513) (xy 159.08218 49.191502) (xy 159.192494 49.132537) (xy 159.289185 49.053185) (xy 159.368537 48.956494)
        (xy 159.427502 48.84618) (xy 159.463812 48.726482) (xy 159.476072 48.602) (xy 159.473 48.03775) (xy 159.31425 47.879)
        (xy 158.115 47.879) (xy 158.115 47.899) (xy 157.861 47.899) (xy 157.861 47.879) (xy 156.66175 47.879)
        (xy 156.503 48.03775) (xy 156.499928 48.602) (xy 156.512188 48.726482) (xy 156.548498 48.84618) (xy 156.607463 48.956494)
        (xy 156.686815 49.053185) (xy 156.783506 49.132537) (xy 156.89382 49.191502) (xy 156.96638 49.213513) (xy 156.834525 49.345368)
        (xy 156.793344 49.407) (xy 142.283469 49.407) (xy 142.24 49.402719) (xy 142.196531 49.407) (xy 142.196523 49.407)
        (xy 142.06651 49.419805) (xy 141.899687 49.470411) (xy 141.812068 49.517244) (xy 141.745941 49.552589) (xy 141.644953 49.635468)
        (xy 141.644951 49.63547) (xy 141.611183 49.663183) (xy 141.58347 49.696951) (xy 131.834957 59.445465) (xy 131.778102 59.456774)
        (xy 131.589744 59.534795) (xy 131.420226 59.648063) (xy 131.276063 59.792226) (xy 131.162795 59.961744) (xy 131.084774 60.150102)
        (xy 131.045 60.350061) (xy 118.589104 60.350061) (xy 118.732357 60.254343) (xy 118.87652 60.11018) (xy 118.989788 59.940662)
        (xy 119.067809 59.752304) (xy 119.107583 59.552345) (xy 119.107583 59.348467) (xy 119.067809 59.148508) (xy 118.989788 58.96015)
        (xy 118.87652 58.790632) (xy 118.732357 58.646469) (xy 118.562839 58.533201) (xy 118.374481 58.45518) (xy 118.362 58.452697)
        (xy 118.362 40.259123) (xy 120.399003 38.222121) (xy 120.428001 38.198323) (xy 120.522974 38.082598) (xy 120.593546 37.950569)
        (xy 120.637003 37.807308) (xy 120.648 37.695655) (xy 120.648 37.695646) (xy 120.651676 37.658323) (xy 120.648 37.621)
        (xy 120.648 34.298178) (xy 120.834632 34.173475) (xy 121.041475 33.966632) (xy 121.158 33.79224) (xy 121.274525 33.966632)
        (xy 121.481368 34.173475) (xy 121.668 34.298179) (xy 121.668001 43.043023) (xy 121.664324 43.080355) (xy 121.668001 43.117688)
        (xy 121.668643 43.124201) (xy 121.678998 43.22934) (xy 121.722454 43.372601) (xy 121.793026 43.504631) (xy 121.821245 43.539015)
        (xy 121.888 43.620356) (xy 121.916998 43.644154) (xy 129.898216 51.625373) (xy 129.922014 51.654371) (xy 129.951012 51.678169)
        (xy 130.037738 51.749344) (xy 130.153898 51.811433) (xy 130.169768 51.819916) (xy 130.312976 51.863357) (xy 130.367926 51.918307)
        (xy 130.537444 52.031575) (xy 130.725802 52.109596) (xy 130.925761 52.14937) (xy 131.129639 52.14937) (xy 131.329598 52.109596)
        (xy 131.517956 52.031575) (xy 131.687474 51.918307) (xy 131.831637 51.774144) (xy 131.944905 51.604626) (xy 132.022926 51.416268)
        (xy 132.0627 51.216309) (xy 132.0627 51.012431) (xy 132.024224 50.819) (xy 132.181939 50.819) (xy 132.381898 50.779226)
        (xy 132.570256 50.701205) (xy 132.582183 50.693236) (xy 132.601084 50.705865) (xy 132.789442 50.783886) (xy 132.989401 50.82366)
        (xy 133.193279 50.82366) (xy 133.393238 50.783886) (xy 133.581596 50.705865) (xy 133.605165 50.690117) (xy 133.62176 50.701205)
        (xy 133.810118 50.779226) (xy 134.010077 50.819) (xy 134.213955 50.819) (xy 134.413914 50.779226) (xy 134.602272 50.701205)
        (xy 134.759 50.596483) (xy 134.759 51.924036) (xy 134.77904 52.127506) (xy 134.858236 52.38858) (xy 134.986843 52.629187)
        (xy 135.159919 52.84008) (xy 135.370812 53.013157) (xy 135.611419 53.141764) (xy 135.872493 53.22096) (xy 136.144 53.247701)
        (xy 136.415506 53.22096) (xy 136.67658 53.141764) (xy 136.917187 53.013157) (xy 137.12808 52.840081) (xy 137.301157 52.629188)
        (xy 137.429764 52.388581) (xy 137.50896 52.127507) (xy 137.529 51.924037) (xy 137.529 39.429685) (xy 139.573686 37.385)
        (xy 144.305853 37.385) (xy 144.373882 37.3917) (xy 144.441911 37.385) (xy 144.441919 37.385) (xy 144.645389 37.36496)
        (xy 144.906463 37.285764) (xy 145.14707 37.157157) (xy 145.357963 36.984081) (xy 145.401336 36.931231) (xy 145.711235 36.621332)
        (xy 145.76408 36.577963) (xy 145.811665 36.519982) (xy 145.887961 36.427015) (xy 145.937157 36.36707) (xy 146.065764 36.126463)
        (xy 146.14496 35.865389) (xy 146.165 35.661919) (xy 146.165 35.661911) (xy 146.1717 35.593882) (xy 146.165 35.525853)
        (xy 146.165 34.216144) (xy 146.219502 34.11418) (xy 146.241513 34.04162) (xy 146.373368 34.173475) (xy 146.616589 34.33599)
        (xy 146.886842 34.447932) (xy 147.17374 34.505) (xy 147.46626 34.505) (xy 147.753158 34.447932) (xy 148.023411 34.33599)
        (xy 148.266632 34.173475) (xy 148.473475 33.966632) (xy 148.484945 33.949466) (xy 148.609965 45.540576) (xy 148.574236 45.60742)
        (xy 148.49504 45.868493) (xy 148.4683 46.14) (xy 148.49504 46.411507) (xy 148.574236 46.67258) (xy 148.702844 46.913188)
        (xy 148.875919 47.124081) (xy 149.086812 47.297156) (xy 149.32742 47.425764) (xy 149.588493 47.50496) (xy 149.86 47.5317)
        (xy 150.131507 47.50496) (xy 150.39258 47.425764) (xy 150.633188 47.297156) (xy 150.791233 47.167452) (xy 150.936527 47.022158)
        (xy 150.994637 46.97341) (xy 151.051963 46.902) (xy 156.499928 46.902) (xy 156.503 47.46625) (xy 156.66175 47.625)
        (xy 157.861 47.625) (xy 157.861 46.42575) (xy 158.115 46.42575) (xy 158.115 47.625) (xy 159.31425 47.625)
        (xy 159.473 47.46625) (xy 159.476072 46.902) (xy 159.463812 46.777518) (xy 159.427502 46.65782) (xy 159.368537 46.547506)
        (xy 159.289185 46.450815) (xy 159.192494 46.371463) (xy 159.08218 46.312498) (xy 158.962482 46.276188) (xy 158.838 46.263928)
        (xy 158.27375 46.267) (xy 158.115 46.42575) (xy 157.861 46.42575) (xy 157.70225 46.267) (xy 157.138 46.263928)
        (xy 157.013518 46.276188) (xy 156.89382 46.312498) (xy 156.783506 46.371463) (xy 156.686815 46.450815) (xy 156.607463 46.547506)
        (xy 156.548498 46.65782) (xy 156.512188 46.777518) (xy 156.499928 46.902) (xy 151.051963 46.902) (xy 151.075355 46.872862)
        (xy 151.157156 46.773187) (xy 151.160708 46.766541) (xy 151.165428 46.760662) (xy 151.224985 46.646289) (xy 151.285764 46.53258)
        (xy 151.287952 46.525367) (xy 151.291433 46.518682) (xy 151.327514 46.39495) (xy 151.36496 46.271507) (xy 151.365699 46.264)
        (xy 151.367808 46.256769) (xy 151.379062 46.128318) (xy 151.3917 46) (xy 151.384266 45.92452) (xy 151.255386 33.975493)
        (xy 151.453368 34.173475) (xy 151.696589 34.33599) (xy 151.966842 34.447932) (xy 152.25374 34.505) (xy 152.54626 34.505)
        (xy 152.833158 34.447932) (xy 153.103411 34.33599) (xy 153.346632 34.173475) (xy 153.553475 33.966632) (xy 153.67 33.79224)
        (xy 153.786525 33.966632) (xy 153.993368 34.173475) (xy 154.236589 34.33599) (xy 154.506842 34.447932) (xy 154.79374 34.505)
        (xy 155.08626 34.505) (xy 155.373158 34.447932) (xy 155.643411 34.33599) (xy 155.886632 34.173475) (xy 156.093475 33.966632)
        (xy 156.21 33.79224) (xy 156.326525 33.966632) (xy 156.533368 34.173475) (xy 156.776589 34.33599) (xy 157.046842 34.447932)
        (xy 157.33374 34.505) (xy 157.62626 34.505) (xy 157.913158 34.447932) (xy 158.183411 34.33599) (xy 158.426632 34.173475)
        (xy 158.633475 33.966632) (xy 158.79599 33.723411) (xy 158.907932 33.453158) (xy 158.965 33.16626) (xy 158.965 32.87374)
        (xy 158.907932 32.586842) (xy 158.79599 32.316589) (xy 158.633475 32.073368) (xy 158.426632 31.866525) (xy 158.183411 31.70401)
        (xy 157.913158 31.592068) (xy 157.62626 31.535) (xy 157.33374 31.535) (xy 157.046842 31.592068) (xy 156.776589 31.70401)
        (xy 156.533368 31.866525) (xy 156.326525 32.073368) (xy 156.21 32.24776) (xy 156.093475 32.073368) (xy 155.886632 31.866525)
        (xy 155.643411 31.70401) (xy 155.373158 31.592068) (xy 155.08626 31.535) (xy 154.79374 31.535) (xy 154.506842 31.592068)
        (xy 154.236589 31.70401) (xy 153.993368 31.866525) (xy 153.786525 32.073368) (xy 153.67 32.24776) (xy 153.553475 32.073368)
        (xy 153.346632 31.866525) (xy 153.103411 31.70401) (xy 152.833158 31.592068) (xy 152.54626 31.535) (xy 152.25374 31.535)
        (xy 151.966842 31.592068) (xy 151.696589 31.70401) (xy 151.453368 31.866525) (xy 151.246525 32.073368) (xy 151.13 32.24776)
        (xy 151.013475 32.073368) (xy 150.806632 31.866525) (xy 150.563411 31.70401) (xy 150.293158 31.592068) (xy 150.00626 31.535)
        (xy 149.71374 31.535) (xy 149.426842 31.592068) (xy 149.156589 31.70401) (xy 148.913368 31.866525) (xy 148.706525 32.073368)
        (xy 148.59 32.24776) (xy 148.473475 32.073368) (xy 148.266632 31.866525) (xy 148.023411 31.70401) (xy 147.753158 31.592068)
        (xy 147.46626 31.535) (xy 147.17374 31.535) (xy 146.886842 31.592068) (xy 146.616589 31.70401) (xy 146.373368 31.866525)
        (xy 146.241513 31.99838) (xy 146.219502 31.92582) (xy 146.160537 31.815506) (xy 146.081185 31.718815) (xy 145.984494 31.639463)
        (xy 145.87418 31.580498) (xy 145.754482 31.544188) (xy 145.63 31.531928) (xy 143.93 31.531928) (xy 143.805518 31.544188)
        (xy 143.68582 31.580498) (xy 143.575506 31.639463) (xy 143.478815 31.718815) (xy 143.399463 31.815506) (xy 143.340498 31.92582)
        (xy 143.304188 32.045518) (xy 143.291928 32.17) (xy 143.291928 33.87) (xy 143.304188 33.994482) (xy 143.340498 34.11418)
        (xy 143.395001 34.216145) (xy 143.395 34.615) (xy 139.068029 34.615) (xy 139 34.6083) (xy 138.931971 34.615)
        (xy 138.931963 34.615) (xy 138.751892 34.632735) (xy 138.728492 34.63504) (xy 138.467419 34.714236) (xy 138.226812 34.842843)
        (xy 138.117108 34.932875) (xy 138.068766 34.972548) (xy 138.068764 34.97255) (xy 138.015919 35.015919) (xy 137.97255 35.068764)
        (xy 135.212764 37.828551) (xy 135.15992 37.871919) (xy 135.116551 37.924764) (xy 135.116548 37.924767) (xy 134.986844 38.082812)
        (xy 134.858236 38.32342) (xy 134.77904 38.584493) (xy 134.7523 38.856) (xy 134.759001 38.924039) (xy 134.759 48.971517)
        (xy 134.602272 48.866795) (xy 134.413914 48.788774) (xy 134.213955 48.749) (xy 134.151802 48.749) (xy 131.825256 46.422455)
        (xy 131.867937 46.379774) (xy 131.981205 46.210256) (xy 132.059226 46.021898) (xy 132.099 45.821939) (xy 132.099 45.618061)
        (xy 132.059226 45.418102) (xy 131.981205 45.229744) (xy 131.867937 45.060226) (xy 131.723774 44.916063) (xy 131.554256 44.802795)
        (xy 131.365898 44.724774) (xy 131.165939 44.685) (xy 130.962061 44.685) (xy 130.808 44.715644) (xy 130.808 34.298178)
        (xy 130.994632 34.173475) (xy 131.201475 33.966632) (xy 131.323195 33.784466) (xy 131.392822 33.901355) (xy 131.587731 34.117588)
        (xy 131.82108 34.291641) (xy 132.083901 34.416825) (xy 132.23111 34.461476) (xy 132.461 34.340155) (xy 132.461 33.147)
        (xy 132.441 33.147) (xy 132.441 32.893) (xy 132.461 32.893) (xy 132.461 31.699845) (xy 132.715 31.699845)
        (xy 132.715 32.893) (xy 132.735 32.893) (xy 132.735 33.147) (xy 132.715 33.147) (xy 132.715 34.340155)
        (xy 132.94489 34.461476) (xy 133.092099 34.416825) (xy 133.35492 34.291641) (xy 133.588269 34.117588) (xy 133.783178 33.901355)
        (xy 133.852805 33.784466) (xy 133.974525 33.966632) (xy 134.181368 34.173475) (xy 134.424589 34.33599) (xy 134.694842 34.447932)
        (xy 134.98174 34.505) (xy 135.27426 34.505) (xy 135.561158 34.447932) (xy 135.831411 34.33599) (xy 136.074632 34.173475)
        (xy 136.281475 33.966632) (xy 136.44399 33.723411) (xy 136.555932 33.453158) (xy 136.613 33.16626) (xy 136.613 32.87374)
        (xy 136.555932 32.586842) (xy 136.44399 32.316589) (xy 136.281475 32.073368) (xy 136.074632 31.866525) (xy 135.831411 31.70401)
        (xy 135.561158 31.592068) (xy 135.27426 31.535) (xy 134.98174 31.535) (xy 134.694842 31.592068) (xy 134.424589 31.70401)
        (xy 134.181368 31.866525) (xy 133.974525 32.073368) (xy 133.852805 32.255534) (xy 133.783178 32.138645) (xy 133.588269 31.922412)
        (xy 133.35492 31.748359) (xy 133.092099 31.623175) (xy 132.94489 31.578524) (xy 132.715 31.699845) (xy 132.461 31.699845)
        (xy 132.23111 31.578524) (xy 132.083901 31.623175) (xy 131.82108 31.748359) (xy 131.587731 31.922412) (xy 131.392822 32.138645)
        (xy 131.323195 32.255534) (xy 131.201475 32.073368) (xy 130.994632 31.866525) (xy 130.751411 31.70401) (xy 130.481158 31.592068)
        (xy 130.19426 31.535) (xy 129.90174 31.535) (xy 129.614842 31.592068) (xy 129.344589 31.70401) (xy 129.101368 31.866525)
        (xy 128.894525 32.073368) (xy 128.778 32.24776) (xy 128.661475 32.073368) (xy 128.454632 31.866525) (xy 128.211411 31.70401)
        (xy 127.941158 31.592068) (xy 127.65426 31.535) (xy 127.36174 31.535) (xy 127.074842 31.592068) (xy 126.804589 31.70401)
        (xy 126.561368 31.866525) (xy 126.354525 32.073368) (xy 126.238 32.24776) (xy 126.121475 32.073368) (xy 125.914632 31.866525)
        (xy 125.671411 31.70401) (xy 125.401158 31.592068) (xy 125.11426 31.535) (xy 124.82174 31.535) (xy 124.534842 31.592068)
        (xy 124.264589 31.70401) (xy 124.021368 31.866525) (xy 123.814525 32.073368) (xy 123.698 32.24776) (xy 123.581475 32.073368)
        (xy 123.374632 31.866525) (xy 123.131411 31.70401) (xy 122.861158 31.592068) (xy 122.57426 31.535) (xy 122.28174 31.535)
        (xy 121.994842 31.592068) (xy 121.724589 31.70401) (xy 121.481368 31.866525) (xy 121.274525 32.073368) (xy 121.158 32.24776)
        (xy 121.041475 32.073368) (xy 120.834632 31.866525) (xy 120.591411 31.70401) (xy 120.321158 31.592068) (xy 120.03426 31.535)
        (xy 119.74174 31.535) (xy 119.454842 31.592068) (xy 119.184589 31.70401) (xy 118.941368 31.866525) (xy 118.734525 32.073368)
        (xy 118.618 32.24776) (xy 118.501475 32.073368) (xy 118.294632 31.866525) (xy 118.051411 31.70401) (xy 117.781158 31.592068)
        (xy 117.49426 31.535) (xy 117.20174 31.535) (xy 116.914842 31.592068) (xy 116.644589 31.70401) (xy 116.401368 31.866525)
        (xy 116.194525 32.073368) (xy 116.078 32.24776) (xy 115.961475 32.073368) (xy 115.754632 31.866525) (xy 115.511411 31.70401)
        (xy 115.241158 31.592068) (xy 114.95426 31.535) (xy 114.66174 31.535) (xy 114.374842 31.592068) (xy 114.104589 31.70401)
        (xy 113.861368 31.866525) (xy 113.654525 32.073368) (xy 113.538 32.24776) (xy 113.421475 32.073368) (xy 113.214632 31.866525)
        (xy 112.971411 31.70401) (xy 112.701158 31.592068) (xy 112.41426 31.535) (xy 112.12174 31.535) (xy 111.834842 31.592068)
        (xy 111.564589 31.70401) (xy 111.321368 31.866525) (xy 111.114525 32.073368) (xy 110.998 32.24776) (xy 110.881475 32.073368)
        (xy 110.674632 31.866525) (xy 110.431411 31.70401) (xy 110.161158 31.592068) (xy 109.87426 31.535) (xy 109.58174 31.535)
        (xy 109.294842 31.592068) (xy 109.024589 31.70401) (xy 108.781368 31.866525) (xy 108.649513 31.99838) (xy 108.627502 31.92582)
        (xy 108.568537 31.815506) (xy 108.489185 31.718815) (xy 108.392494 31.639463) (xy 108.28218 31.580498) (xy 108.162482 31.544188)
        (xy 108.038 31.531928) (xy 106.338 31.531928) (xy 106.213518 31.544188) (xy 106.09382 31.580498) (xy 105.983506 31.639463)
        (xy 105.886815 31.718815) (xy 105.807463 31.815506) (xy 105.748498 31.92582) (xy 105.712188 32.045518) (xy 105.699928 32.17)
        (xy 97.66 32.17) (xy 97.66 27.032278) (xy 97.684748 26.779872) (xy 98.765 26.779872) (xy 98.765 27.220128)
        (xy 98.85089 27.651925) (xy 99.019369 28.058669) (xy 99.263962 28.424729) (xy 99.575271 28.736038) (xy 99.941331 28.980631)
        (xy 100.348075 29.14911) (xy 100.779872 29.235) (xy 101.220128 29.235) (xy 101.651925 29.14911) (xy 102.058669 28.980631)
        (xy 102.424729 28.736038) (xy 102.736038 28.424729) (xy 102.980631 28.058669) (xy 103.14911 27.651925) (xy 103.235 27.220128)
        (xy 103.235 26.779872) (xy 154.765 26.779872) (xy 154.765 27.220128) (xy 154.85089 27.651925) (xy 155.019369 28.058669)
        (xy 155.263962 28.424729) (xy 155.575271 28.736038) (xy 155.941331 28.980631) (xy 156.348075 29.14911) (xy 156.779872 29.235)
        (xy 157.220128 29.235) (xy 157.651925 29.14911) (xy 158.058669 28.980631) (xy 158.424729 28.736038) (xy 158.736038 28.424729)
        (xy 158.980631 28.058669) (xy 159.14911 27.651925) (xy 159.235 27.220128) (xy 159.235 26.779872) (xy 159.14911 26.348075)
        (xy 158.980631 25.941331) (xy 158.736038 25.575271) (xy 158.424729 25.263962) (xy 158.058669 25.019369) (xy 157.651925 24.85089)
        (xy 157.220128 24.765) (xy 156.779872 24.765) (xy 156.348075 24.85089) (xy 155.941331 25.019369) (xy 155.575271 25.263962)
        (xy 155.263962 25.575271) (xy 155.019369 25.941331) (xy 154.85089 26.348075) (xy 154.765 26.779872) (xy 103.235 26.779872)
        (xy 103.14911 26.348075) (xy 102.980631 25.941331) (xy 102.736038 25.575271) (xy 102.424729 25.263962) (xy 102.058669 25.019369)
        (xy 101.651925 24.85089) (xy 101.220128 24.765) (xy 100.779872 24.765) (xy 100.348075 24.85089) (xy 99.941331 25.019369)
        (xy 99.575271 25.263962) (xy 99.263962 25.575271) (xy 99.019369 25.941331) (xy 98.85089 26.348075) (xy 98.765 26.779872)
        (xy 97.684748 26.779872) (xy 97.726714 26.351874) (xy 97.914943 25.728428) (xy 98.220681 25.153421) (xy 98.632279 24.648753)
        (xy 99.134067 24.233638) (xy 99.706924 23.923895) (xy 100.329039 23.731318) (xy 101.007584 23.66) (xy 156.967722 23.66)
      )
    )
  )
  (zone (net 1) (net_name GND) (layer F.Cu) (tstamp 6049174B) (hatch edge 0.508)
    (connect_pads (clearance 0.508))
    (min_thickness 0.254)
    (fill yes (arc_segments 32) (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 161 71) (xy 97 71) (xy 97 23) (xy 161 23)
      )
    )
    (filled_polygon
      (pts
        (xy 157.648126 23.726714) (xy 158.271572 23.914943) (xy 158.846579 24.220681) (xy 159.351247 24.632279) (xy 159.766362 25.134067)
        (xy 160.076105 25.706924) (xy 160.268682 26.329039) (xy 160.34 27.007584) (xy 160.34 36.584734) (xy 160.289185 36.522815)
        (xy 160.192494 36.443463) (xy 160.08218 36.384498) (xy 159.962482 36.348188) (xy 159.838 36.335928) (xy 159.47375 36.339)
        (xy 159.315 36.49775) (xy 159.315 37.672) (xy 159.335 37.672) (xy 159.335 37.926) (xy 159.315 37.926)
        (xy 159.315 39.497) (xy 159.335 39.497) (xy 159.335 39.751) (xy 159.315 39.751) (xy 159.315 40.60025)
        (xy 159.47375 40.759) (xy 160.138 40.762072) (xy 160.262482 40.749812) (xy 160.34 40.726297) (xy 160.34 40.821703)
        (xy 160.262482 40.798188) (xy 160.138 40.785928) (xy 159.47375 40.789) (xy 159.315 40.94775) (xy 159.315 42.197)
        (xy 159.335 42.197) (xy 159.335 42.451) (xy 159.315 42.451) (xy 159.315 44.422) (xy 159.335 44.422)
        (xy 159.335 44.676) (xy 159.315 44.676) (xy 159.315 45.85025) (xy 159.47375 46.009) (xy 159.838 46.012072)
        (xy 159.962482 45.999812) (xy 160.08218 45.963502) (xy 160.192494 45.904537) (xy 160.289185 45.825185) (xy 160.34 45.763266)
        (xy 160.340001 66.967711) (xy 160.273286 67.648126) (xy 160.085057 68.27157) (xy 159.779323 68.846573) (xy 159.367721 69.351248)
        (xy 158.865933 69.766362) (xy 158.293077 70.076104) (xy 157.670961 70.268682) (xy 156.992417 70.34) (xy 137.032279 70.34)
        (xy 136.934576 70.33042) (xy 136.871643 70.31142) (xy 136.813594 70.280554) (xy 136.762657 70.239011) (xy 136.720752 70.188356)
        (xy 136.689485 70.130529) (xy 136.670044 70.067728) (xy 136.66 69.972165) (xy 136.66 66.779872) (xy 154.765 66.779872)
        (xy 154.765 67.220128) (xy 154.85089 67.651925) (xy 155.019369 68.058669) (xy 155.263962 68.424729) (xy 155.575271 68.736038)
        (xy 155.941331 68.980631) (xy 156.348075 69.14911) (xy 156.779872 69.235) (xy 157.220128 69.235) (xy 157.651925 69.14911)
        (xy 158.058669 68.980631) (xy 158.424729 68.736038) (xy 158.736038 68.424729) (xy 158.980631 68.058669) (xy 159.14911 67.651925)
        (xy 159.235 67.220128) (xy 159.235 66.779872) (xy 159.14911 66.348075) (xy 158.980631 65.941331) (xy 158.736038 65.575271)
        (xy 158.424729 65.263962) (xy 158.058669 65.019369) (xy 157.651925 64.85089) (xy 157.220128 64.765) (xy 156.779872 64.765)
        (xy 156.348075 64.85089) (xy 155.941331 65.019369) (xy 155.575271 65.263962) (xy 155.263962 65.575271) (xy 155.019369 65.941331)
        (xy 154.85089 66.348075) (xy 154.765 66.779872) (xy 136.66 66.779872) (xy 136.66 64.032419) (xy 136.663193 64)
        (xy 136.65045 63.870617) (xy 136.61271 63.746207) (xy 136.551425 63.63155) (xy 136.468948 63.531052) (xy 136.36845 63.448575)
        (xy 136.253793 63.38729) (xy 136.129383 63.34955) (xy 136.032419 63.34) (xy 136 63.336807) (xy 135.967581 63.34)
        (xy 106.032419 63.34) (xy 106 63.336807) (xy 105.967581 63.34) (xy 105.870617 63.34955) (xy 105.746207 63.38729)
        (xy 105.63155 63.448575) (xy 105.531052 63.531052) (xy 105.448575 63.63155) (xy 105.38729 63.746207) (xy 105.34955 63.870617)
        (xy 105.336807 64) (xy 105.34 64.032419) (xy 105.340001 69.967711) (xy 105.33042 70.065424) (xy 105.31142 70.128357)
        (xy 105.280554 70.186406) (xy 105.239011 70.237343) (xy 105.188356 70.279248) (xy 105.130529 70.310515) (xy 105.067728 70.329956)
        (xy 104.972165 70.34) (xy 101.032279 70.34) (xy 100.351874 70.273286) (xy 99.72843 70.085057) (xy 99.153427 69.779323)
        (xy 98.648752 69.367721) (xy 98.233638 68.865933) (xy 97.923896 68.293077) (xy 97.731318 67.670961) (xy 97.66 66.992417)
        (xy 97.66 66.779872) (xy 98.765 66.779872) (xy 98.765 67.220128) (xy 98.85089 67.651925) (xy 99.019369 68.058669)
        (xy 99.263962 68.424729) (xy 99.575271 68.736038) (xy 99.941331 68.980631) (xy 100.348075 69.14911) (xy 100.779872 69.235)
        (xy 101.220128 69.235) (xy 101.651925 69.14911) (xy 102.058669 68.980631) (xy 102.424729 68.736038) (xy 102.736038 68.424729)
        (xy 102.980631 68.058669) (xy 103.14911 67.651925) (xy 103.235 67.220128) (xy 103.235 66.779872) (xy 103.14911 66.348075)
        (xy 102.980631 65.941331) (xy 102.736038 65.575271) (xy 102.424729 65.263962) (xy 102.058669 65.019369) (xy 101.651925 64.85089)
        (xy 101.220128 64.765) (xy 100.779872 64.765) (xy 100.348075 64.85089) (xy 99.941331 65.019369) (xy 99.575271 65.263962)
        (xy 99.263962 65.575271) (xy 99.019369 65.941331) (xy 98.85089 66.348075) (xy 98.765 66.779872) (xy 97.66 66.779872)
        (xy 97.66 60.836) (xy 98.485928 60.836) (xy 98.485928 61.836) (xy 98.498188 61.960482) (xy 98.534498 62.08018)
        (xy 98.593463 62.190494) (xy 98.672815 62.287185) (xy 98.769506 62.366537) (xy 98.87982 62.425502) (xy 98.999518 62.461812)
        (xy 99.124 62.474072) (xy 100.824 62.474072) (xy 100.948482 62.461812) (xy 101.06818 62.425502) (xy 101.178494 62.366537)
        (xy 101.275185 62.287185) (xy 101.329501 62.221) (xy 104.918499 62.221) (xy 104.972815 62.287185) (xy 105.069506 62.366537)
        (xy 105.17982 62.425502) (xy 105.299518 62.461812) (xy 105.424 62.474072) (xy 107.124 62.474072) (xy 107.248482 62.461812)
        (xy 107.36818 62.425502) (xy 107.478494 62.366537) (xy 107.575185 62.287185) (xy 107.629501 62.221) (xy 108.800531 62.221)
        (xy 108.844 62.225281) (xy 108.887469 62.221) (xy 108.887477 62.221) (xy 109.01749 62.208195) (xy 109.184313 62.157589)
        (xy 109.338059 62.075411) (xy 109.472817 61.964817) (xy 109.500534 61.931044) (xy 110.355257 61.076322) (xy 110.389025 61.048609)
        (xy 110.432458 60.995687) (xy 110.499619 60.913851) (xy 110.500756 60.911724) (xy 110.581797 60.760105) (xy 110.632403 60.593282)
        (xy 110.645208 60.463269) (xy 110.645208 60.463261) (xy 110.649489 60.419792) (xy 110.645208 60.376323) (xy 110.645208 58.62649)
        (xy 110.677413 58.578292) (xy 110.755434 58.389934) (xy 110.795208 58.189975) (xy 110.795208 58.12303) (xy 111.415928 58.12303)
        (xy 111.415928 58.42) (xy 111.428188 58.544482) (xy 111.464498 58.66418) (xy 111.523463 58.774494) (xy 111.602815 58.871185)
        (xy 111.662296 58.92) (xy 111.602815 58.968815) (xy 111.523463 59.065506) (xy 111.464498 59.17582) (xy 111.428188 59.295518)
        (xy 111.415928 59.42) (xy 111.415928 60.42) (xy 111.428188 60.544482) (xy 111.464498 60.66418) (xy 111.523463 60.774494)
        (xy 111.602815 60.871185) (xy 111.662296 60.92) (xy 111.602815 60.968815) (xy 111.523463 61.065506) (xy 111.464498 61.17582)
        (xy 111.428188 61.295518) (xy 111.415928 61.42) (xy 111.415928 62.42) (xy 111.428188 62.544482) (xy 111.464498 62.66418)
        (xy 111.523463 62.774494) (xy 111.602815 62.871185) (xy 111.699506 62.950537) (xy 111.80982 63.009502) (xy 111.929518 63.045812)
        (xy 112.054 63.058072) (xy 114.554 63.058072) (xy 114.678482 63.045812) (xy 114.79818 63.009502) (xy 114.908494 62.950537)
        (xy 115.005185 62.871185) (xy 115.084537 62.774494) (xy 115.143502 62.66418) (xy 115.15512 62.625881) (xy 115.156223 62.625546)
        (xy 115.288252 62.554974) (xy 115.403977 62.460001) (xy 115.42778 62.430998) (xy 115.863777 61.995) (xy 115.925915 61.995)
        (xy 116.125874 61.955226) (xy 116.314232 61.877205) (xy 116.48375 61.763937) (xy 116.627913 61.619774) (xy 116.741181 61.450256)
        (xy 116.819202 61.261898) (xy 116.858976 61.061939) (xy 116.858976 60.858061) (xy 116.819202 60.658102) (xy 116.812734 60.642488)
        (xy 116.868585 60.625546) (xy 117.000614 60.554974) (xy 117.088368 60.482956) (xy 117.150321 60.482956) (xy 117.35028 60.443182)
        (xy 117.538638 60.365161) (xy 117.558649 60.35179) (xy 117.582327 60.367611) (xy 117.770685 60.445632) (xy 117.970644 60.485406)
        (xy 118.174522 60.485406) (xy 118.374481 60.445632) (xy 118.562839 60.367611) (xy 118.732357 60.254343) (xy 118.87652 60.11018)
        (xy 118.969153 59.971544) (xy 120.462659 61.46505) (xy 120.490372 61.498818) (xy 120.52414 61.526531) (xy 120.524142 61.526533)
        (xy 120.62513 61.609412) (xy 120.778875 61.69159) (xy 120.945699 61.742196) (xy 121.075712 61.755001) (xy 121.07572 61.755001)
        (xy 121.119189 61.759282) (xy 121.162658 61.755001) (xy 126.615928 61.755001) (xy 126.615928 62.42) (xy 126.628188 62.544482)
        (xy 126.664498 62.66418) (xy 126.723463 62.774494) (xy 126.802815 62.871185) (xy 126.899506 62.950537) (xy 127.00982 63.009502)
        (xy 127.129518 63.045812) (xy 127.254 63.058072) (xy 129.754 63.058072) (xy 129.878482 63.045812) (xy 129.99818 63.009502)
        (xy 130.108494 62.950537) (xy 130.205185 62.871185) (xy 130.284537 62.774494) (xy 130.335046 62.68) (xy 133.896678 62.68)
        (xy 133.934 62.683676) (xy 133.971322 62.68) (xy 133.971333 62.68) (xy 134.082986 62.669003) (xy 134.226247 62.625546)
        (xy 134.358276 62.554974) (xy 134.456855 62.474072) (xy 135.368 62.474072) (xy 135.492482 62.461812) (xy 135.61218 62.425502)
        (xy 135.722494 62.366537) (xy 135.819185 62.287185) (xy 135.898537 62.190494) (xy 135.949046 62.096) (xy 139.386954 62.096)
        (xy 139.437463 62.190494) (xy 139.516815 62.287185) (xy 139.613506 62.366537) (xy 139.72382 62.425502) (xy 139.843518 62.461812)
        (xy 139.968 62.474072) (xy 141.668 62.474072) (xy 141.792482 62.461812) (xy 141.91218 62.425502) (xy 142.022494 62.366537)
        (xy 142.119185 62.287185) (xy 142.198537 62.190494) (xy 142.257502 62.08018) (xy 142.293812 61.960482) (xy 142.306072 61.836)
        (xy 142.306072 60.836) (xy 142.293812 60.711518) (xy 142.257502 60.59182) (xy 142.198537 60.481506) (xy 142.119185 60.384815)
        (xy 142.022494 60.305463) (xy 141.91218 60.246498) (xy 141.792482 60.210188) (xy 141.668 60.197928) (xy 139.968 60.197928)
        (xy 139.843518 60.210188) (xy 139.72382 60.246498) (xy 139.613506 60.305463) (xy 139.516815 60.384815) (xy 139.437463 60.481506)
        (xy 139.386954 60.576) (xy 135.949046 60.576) (xy 135.898537 60.481506) (xy 135.819185 60.384815) (xy 135.722494 60.305463)
        (xy 135.61218 60.246498) (xy 135.492482 60.210188) (xy 135.368 60.197928) (xy 133.668 60.197928) (xy 133.543518 60.210188)
        (xy 133.42382 60.246498) (xy 133.313506 60.305463) (xy 133.216815 60.384815) (xy 133.137463 60.481506) (xy 133.115 60.523531)
        (xy 133.115 60.350061) (xy 133.075226 60.150102) (xy 133.05903 60.111) (xy 146.837928 60.111) (xy 146.850188 60.235482)
        (xy 146.886498 60.35518) (xy 146.945463 60.465494) (xy 147.024815 60.562185) (xy 147.121506 60.641537) (xy 147.23182 60.700502)
        (xy 147.351518 60.736812) (xy 147.476 60.749072) (xy 147.89025 60.746) (xy 148.049 60.58725) (xy 148.049 59.563)
        (xy 146.99975 59.563) (xy 146.841 59.72175) (xy 146.837928 60.111) (xy 133.05903 60.111) (xy 132.997205 59.961744)
        (xy 132.883937 59.792226) (xy 132.739774 59.648063) (xy 132.570256 59.534795) (xy 132.381898 59.456774) (xy 132.181939 59.417)
        (xy 131.978061 59.417) (xy 131.778102 59.456774) (xy 131.589744 59.534795) (xy 131.420226 59.648063) (xy 131.276063 59.792226)
        (xy 131.162795 59.961744) (xy 131.153162 59.985001) (xy 130.392072 59.985001) (xy 130.392072 59.42) (xy 130.379812 59.295518)
        (xy 130.343502 59.17582) (xy 130.284537 59.065506) (xy 130.205185 58.968815) (xy 130.145704 58.92) (xy 130.205185 58.871185)
        (xy 130.284537 58.774494) (xy 130.343502 58.66418) (xy 130.379812 58.544482) (xy 130.392072 58.42) (xy 130.392072 58.036)
        (xy 133.029928 58.036) (xy 133.042188 58.160482) (xy 133.078498 58.28018) (xy 133.137463 58.390494) (xy 133.216815 58.487185)
        (xy 133.313506 58.566537) (xy 133.42382 58.625502) (xy 133.543518 58.661812) (xy 133.668 58.674072) (xy 134.23225 58.671)
        (xy 134.391 58.51225) (xy 134.391 57.663) (xy 134.645 57.663) (xy 134.645 58.51225) (xy 134.80375 58.671)
        (xy 135.368 58.674072) (xy 135.492482 58.661812) (xy 135.61218 58.625502) (xy 135.722494 58.566537) (xy 135.819185 58.487185)
        (xy 135.898537 58.390494) (xy 135.957502 58.28018) (xy 135.993812 58.160482) (xy 136.006072 58.036) (xy 139.329928 58.036)
        (xy 139.342188 58.160482) (xy 139.378498 58.28018) (xy 139.437463 58.390494) (xy 139.516815 58.487185) (xy 139.613506 58.566537)
        (xy 139.72382 58.625502) (xy 139.843518 58.661812) (xy 139.968 58.674072) (xy 140.53225 58.671) (xy 140.691 58.51225)
        (xy 140.691 57.663) (xy 140.945 57.663) (xy 140.945 58.51225) (xy 141.10375 58.671) (xy 141.668 58.674072)
        (xy 141.792482 58.661812) (xy 141.91218 58.625502) (xy 142.022494 58.566537) (xy 142.119185 58.487185) (xy 142.198537 58.390494)
        (xy 142.257502 58.28018) (xy 142.293812 58.160482) (xy 142.306072 58.036) (xy 142.303 57.82175) (xy 142.14425 57.663)
        (xy 140.945 57.663) (xy 140.691 57.663) (xy 139.49175 57.663) (xy 139.333 57.82175) (xy 139.329928 58.036)
        (xy 136.006072 58.036) (xy 136.003 57.82175) (xy 135.84425 57.663) (xy 134.645 57.663) (xy 134.391 57.663)
        (xy 133.19175 57.663) (xy 133.033 57.82175) (xy 133.029928 58.036) (xy 130.392072 58.036) (xy 130.392072 57.626729)
        (xy 130.528801 57.49) (xy 144.647928 57.49) (xy 144.660188 57.614482) (xy 144.696498 57.73418) (xy 144.755463 57.844494)
        (xy 144.834815 57.941185) (xy 144.931506 58.020537) (xy 145.04182 58.079502) (xy 145.161518 58.115812) (xy 145.286 58.128072)
        (xy 145.75025 58.125) (xy 145.909 57.96625) (xy 145.909 56.617) (xy 144.80975 56.617) (xy 144.651 56.77575)
        (xy 144.647928 57.49) (xy 130.528801 57.49) (xy 130.982801 57.036) (xy 133.029928 57.036) (xy 133.033 57.25025)
        (xy 133.19175 57.409) (xy 134.391 57.409) (xy 134.391 56.55975) (xy 134.645 56.55975) (xy 134.645 57.409)
        (xy 135.84425 57.409) (xy 136.003 57.25025) (xy 136.006072 57.036) (xy 139.329928 57.036) (xy 139.333 57.25025)
        (xy 139.49175 57.409) (xy 140.691 57.409) (xy 140.691 56.55975) (xy 140.945 56.55975) (xy 140.945 57.409)
        (xy 142.14425 57.409) (xy 142.303 57.25025) (xy 142.306072 57.036) (xy 142.293812 56.911518) (xy 142.257502 56.79182)
        (xy 142.198537 56.681506) (xy 142.119185 56.584815) (xy 142.022494 56.505463) (xy 141.91218 56.446498) (xy 141.792482 56.410188)
        (xy 141.668 56.397928) (xy 141.10375 56.401) (xy 140.945 56.55975) (xy 140.691 56.55975) (xy 140.53225 56.401)
        (xy 139.968 56.397928) (xy 139.843518 56.410188) (xy 139.72382 56.446498) (xy 139.613506 56.505463) (xy 139.516815 56.584815)
        (xy 139.437463 56.681506) (xy 139.378498 56.79182) (xy 139.342188 56.911518) (xy 139.329928 57.036) (xy 136.006072 57.036)
        (xy 135.993812 56.911518) (xy 135.957502 56.79182) (xy 135.898537 56.681506) (xy 135.819185 56.584815) (xy 135.722494 56.505463)
        (xy 135.61218 56.446498) (xy 135.492482 56.410188) (xy 135.368 56.397928) (xy 134.80375 56.401) (xy 134.645 56.55975)
        (xy 134.391 56.55975) (xy 134.23225 56.401) (xy 133.668 56.397928) (xy 133.543518 56.410188) (xy 133.42382 56.446498)
        (xy 133.313506 56.505463) (xy 133.216815 56.584815) (xy 133.137463 56.681506) (xy 133.078498 56.79182) (xy 133.042188 56.911518)
        (xy 133.029928 57.036) (xy 130.982801 57.036) (xy 132.786801 55.232) (xy 141.892928 55.232) (xy 141.905188 55.356482)
        (xy 141.941498 55.47618) (xy 142.000463 55.586494) (xy 142.079815 55.683185) (xy 142.176506 55.762537) (xy 142.28682 55.821502)
        (xy 142.406518 55.857812) (xy 142.531 55.870072) (xy 142.97025 55.867) (xy 143.129 55.70825) (xy 143.129 54.859)
        (xy 143.383 54.859) (xy 143.383 55.70825) (xy 143.54175 55.867) (xy 143.981 55.870072) (xy 144.105482 55.857812)
        (xy 144.22518 55.821502) (xy 144.335494 55.762537) (xy 144.432185 55.683185) (xy 144.511537 55.586494) (xy 144.563114 55.49)
        (xy 144.647928 55.49) (xy 144.651 56.20425) (xy 144.80975 56.363) (xy 145.909 56.363) (xy 145.909 55.01375)
        (xy 145.75025 54.855) (xy 145.286 54.851928) (xy 145.161518 54.864188) (xy 145.04182 54.900498) (xy 144.931506 54.959463)
        (xy 144.834815 55.038815) (xy 144.755463 55.135506) (xy 144.696498 55.24582) (xy 144.660188 55.365518) (xy 144.647928 55.49)
        (xy 144.563114 55.49) (xy 144.570502 55.47618) (xy 144.606812 55.356482) (xy 144.619072 55.232) (xy 144.616 55.01775)
        (xy 144.45725 54.859) (xy 143.383 54.859) (xy 143.129 54.859) (xy 142.05475 54.859) (xy 141.896 55.01775)
        (xy 141.892928 55.232) (xy 132.786801 55.232) (xy 134.62302 53.395782) (xy 134.652017 53.371985) (xy 134.74699 53.25626)
        (xy 134.817562 53.124231) (xy 134.861019 52.98097) (xy 134.872016 52.869317) (xy 134.872016 52.869308) (xy 134.875692 52.831985)
        (xy 134.872016 52.794662) (xy 134.872016 50.487711) (xy 134.876191 50.483536) (xy 134.888463 50.506494) (xy 134.967815 50.603185)
        (xy 135.064506 50.682537) (xy 135.17482 50.741502) (xy 135.294518 50.777812) (xy 135.419 50.790072) (xy 135.85825 50.787)
        (xy 136.017 50.62825) (xy 136.017 49.779) (xy 136.271 49.779) (xy 136.271 50.62825) (xy 136.42975 50.787)
        (xy 136.869 50.790072) (xy 136.993482 50.777812) (xy 137.11318 50.741502) (xy 137.223494 50.682537) (xy 137.320185 50.603185)
        (xy 137.399537 50.506494) (xy 137.458502 50.39618) (xy 137.494812 50.276482) (xy 137.507072 50.152) (xy 137.504 49.93775)
        (xy 137.34525 49.779) (xy 136.271 49.779) (xy 136.017 49.779) (xy 135.997 49.779) (xy 135.997 49.525)
        (xy 136.017 49.525) (xy 136.017 49.505) (xy 136.271 49.505) (xy 136.271 49.525) (xy 137.34525 49.525)
        (xy 137.504 49.36625) (xy 137.507072 49.152) (xy 137.505595 49.137) (xy 140.601315 49.137) (xy 141.871001 50.406687)
        (xy 141.871 52.763963) (xy 141.864299 52.832) (xy 141.89104 53.103507) (xy 141.896972 53.123063) (xy 141.909992 53.255254)
        (xy 141.960528 53.42185) (xy 142.042595 53.575386) (xy 142.153038 53.709962) (xy 142.159594 53.715342) (xy 142.079815 53.780815)
        (xy 142.000463 53.877506) (xy 141.941498 53.98782) (xy 141.905188 54.107518) (xy 141.892928 54.232) (xy 141.896 54.44625)
        (xy 142.05475 54.605) (xy 143.129 54.605) (xy 143.129 54.585) (xy 143.383 54.585) (xy 143.383 54.605)
        (xy 144.45725 54.605) (xy 144.616 54.44625) (xy 144.619072 54.232) (xy 144.617595 54.217) (xy 146.604315 54.217)
        (xy 146.951001 54.563686) (xy 146.951001 54.876479) (xy 146.910482 54.864188) (xy 146.786 54.851928) (xy 146.32175 54.855)
        (xy 146.163 55.01375) (xy 146.163 56.363) (xy 146.183 56.363) (xy 146.183 56.617) (xy 146.163 56.617)
        (xy 146.163 57.96625) (xy 146.32175 58.125) (xy 146.786 58.128072) (xy 146.910482 58.115812) (xy 147.03018 58.079502)
        (xy 147.140494 58.020537) (xy 147.186 57.983191) (xy 147.231506 58.020537) (xy 147.34182 58.079502) (xy 147.461518 58.115812)
        (xy 147.538474 58.123391) (xy 147.476 58.122928) (xy 147.351518 58.135188) (xy 147.23182 58.171498) (xy 147.121506 58.230463)
        (xy 147.024815 58.309815) (xy 146.945463 58.406506) (xy 146.886498 58.51682) (xy 146.850188 58.636518) (xy 146.837928 58.761)
        (xy 146.841 59.15025) (xy 146.99975 59.309) (xy 148.049 59.309) (xy 148.049 59.289) (xy 148.303 59.289)
        (xy 148.303 59.309) (xy 148.323 59.309) (xy 148.323 59.563) (xy 148.303 59.563) (xy 148.303 60.58725)
        (xy 148.46175 60.746) (xy 148.876 60.749072) (xy 149.000482 60.736812) (xy 149.12018 60.700502) (xy 149.230494 60.641537)
        (xy 149.327185 60.562185) (xy 149.406537 60.465494) (xy 149.465502 60.35518) (xy 149.501812 60.235482) (xy 149.514072 60.111)
        (xy 149.511 59.72175) (xy 149.352252 59.563002) (xy 149.496808 59.563002) (xy 149.51104 59.707507) (xy 149.537928 59.796145)
        (xy 149.537928 59.861001) (xy 149.554992 60.034255) (xy 149.605528 60.200851) (xy 149.687595 60.354387) (xy 149.798038 60.488962)
        (xy 149.932613 60.599405) (xy 150.086149 60.681472) (xy 150.252745 60.732008) (xy 150.425999 60.749072) (xy 150.433441 60.749072)
        (xy 150.604493 60.80096) (xy 150.706584 60.811015) (xy 150.876 60.827701) (xy 150.944036 60.821) (xy 152.839971 60.821)
        (xy 152.908 60.8277) (xy 152.976029 60.821) (xy 152.976037 60.821) (xy 153.179507 60.80096) (xy 153.440581 60.721764)
        (xy 153.681188 60.593157) (xy 153.892081 60.420081) (xy 153.935455 60.36723) (xy 154.347231 59.955453) (xy 154.40008 59.912081)
        (xy 154.573157 59.701188) (xy 154.701764 59.460581) (xy 154.78096 59.199507) (xy 154.801 58.996037) (xy 154.801 58.996028)
        (xy 154.8077 58.928001) (xy 154.801 58.859974) (xy 154.801 53.18889) (xy 156.546524 53.18889) (xy 156.591175 53.336099)
        (xy 156.716359 53.59892) (xy 156.890412 53.832269) (xy 157.106645 54.027178) (xy 157.356748 54.176157) (xy 157.631109 54.273481)
        (xy 157.861 54.152814) (xy 157.861 52.959) (xy 158.115 52.959) (xy 158.115 54.152814) (xy 158.344891 54.273481)
        (xy 158.619252 54.176157) (xy 158.869355 54.027178) (xy 159.085588 53.832269) (xy 159.259641 53.59892) (xy 159.384825 53.336099)
        (xy 159.429476 53.18889) (xy 159.308155 52.959) (xy 158.115 52.959) (xy 157.861 52.959) (xy 156.667845 52.959)
        (xy 156.546524 53.18889) (xy 154.801 53.18889) (xy 154.801 49.849685) (xy 155.513685 49.137) (xy 156.791856 49.137)
        (xy 156.89382 49.191502) (xy 156.96638 49.213513) (xy 156.834525 49.345368) (xy 156.67201 49.588589) (xy 156.560068 49.858842)
        (xy 156.503 50.14574) (xy 156.503 50.43826) (xy 156.560068 50.725158) (xy 156.67201 50.995411) (xy 156.834525 51.238632)
        (xy 157.041368 51.445475) (xy 157.223534 51.567195) (xy 157.106645 51.636822) (xy 156.890412 51.831731) (xy 156.716359 52.06508)
        (xy 156.591175 52.327901) (xy 156.546524 52.47511) (xy 156.667845 52.705) (xy 157.861 52.705) (xy 157.861 52.685)
        (xy 158.115 52.685) (xy 158.115 52.705) (xy 159.308155 52.705) (xy 159.429476 52.47511) (xy 159.384825 52.327901)
        (xy 159.259641 52.06508) (xy 159.085588 51.831731) (xy 158.869355 51.636822) (xy 158.752466 51.567195) (xy 158.934632 51.445475)
        (xy 159.141475 51.238632) (xy 159.30399 50.995411) (xy 159.415932 50.725158) (xy 159.473 50.43826) (xy 159.473 50.14574)
        (xy 159.415932 49.858842) (xy 159.30399 49.588589) (xy 159.141475 49.345368) (xy 159.00962 49.213513) (xy 159.08218 49.191502)
        (xy 159.192494 49.132537) (xy 159.289185 49.053185) (xy 159.368537 48.956494) (xy 159.427502 48.84618) (xy 159.463812 48.726482)
        (xy 159.476072 48.602) (xy 159.476072 46.902) (xy 159.463812 46.777518) (xy 159.427502 46.65782) (xy 159.368537 46.547506)
        (xy 159.289185 46.450815) (xy 159.192494 46.371463) (xy 159.08218 46.312498) (xy 158.962482 46.276188) (xy 158.838 46.263928)
        (xy 157.138 46.263928) (xy 157.013518 46.276188) (xy 156.89382 46.312498) (xy 156.791856 46.367) (xy 155.380999 46.367)
        (xy 155.380999 44.818098) (xy 155.386815 44.825185) (xy 155.483506 44.904537) (xy 155.59382 44.963502) (xy 155.713518 44.999812)
        (xy 155.838 45.012072) (xy 156.32725 45.009) (xy 156.486 44.85025) (xy 156.486 43.7885) (xy 156.466 43.7885)
        (xy 156.466 43.5345) (xy 156.486 43.5345) (xy 156.486 43.5145) (xy 156.74 43.5145) (xy 156.74 43.5345)
        (xy 156.76 43.5345) (xy 156.76 43.7885) (xy 156.74 43.7885) (xy 156.74 44.85025) (xy 156.89875 45.009)
        (xy 157.388 45.012072) (xy 157.512482 44.999812) (xy 157.63218 44.963502) (xy 157.742494 44.904537) (xy 157.839185 44.825185)
        (xy 157.918537 44.728494) (xy 157.946595 44.676002) (xy 158.061748 44.676002) (xy 157.903 44.83475) (xy 157.899928 45.374)
        (xy 157.912188 45.498482) (xy 157.948498 45.61818) (xy 158.007463 45.728494) (xy 158.086815 45.825185) (xy 158.183506 45.904537)
        (xy 158.29382 45.963502) (xy 158.413518 45.999812) (xy 158.538 46.012072) (xy 158.90225 46.009) (xy 159.061 45.85025)
        (xy 159.061 44.676) (xy 159.041 44.676) (xy 159.041 44.422) (xy 159.061 44.422) (xy 159.061 42.451)
        (xy 159.041 42.451) (xy 159.041 42.197) (xy 159.061 42.197) (xy 159.061 40.94775) (xy 158.90225 40.789)
        (xy 158.238 40.785928) (xy 158.113518 40.798188) (xy 157.99382 40.834498) (xy 157.883506 40.893463) (xy 157.853063 40.918447)
        (xy 157.846223 40.849) (xy 157.856072 40.749) (xy 157.856072 40.632023) (xy 157.883506 40.654537) (xy 157.99382 40.713502)
        (xy 158.113518 40.749812) (xy 158.238 40.762072) (xy 158.90225 40.759) (xy 159.061 40.60025) (xy 159.061 39.751)
        (xy 159.041 39.751) (xy 159.041 39.497) (xy 159.061 39.497) (xy 159.061 37.926) (xy 159.041 37.926)
        (xy 159.041 37.672) (xy 159.061 37.672) (xy 159.061 36.49775) (xy 158.90225 36.339) (xy 158.538 36.335928)
        (xy 158.413518 36.348188) (xy 158.29382 36.384498) (xy 158.183506 36.443463) (xy 158.086815 36.522815) (xy 158.007463 36.619506)
        (xy 157.948498 36.72982) (xy 157.912188 36.849518) (xy 157.899928 36.974) (xy 157.903 37.51325) (xy 158.061748 37.671998)
        (xy 157.946595 37.671998) (xy 157.918537 37.619506) (xy 157.839185 37.522815) (xy 157.742494 37.443463) (xy 157.63218 37.384498)
        (xy 157.512482 37.348188) (xy 157.388 37.335928) (xy 156.89875 37.339) (xy 156.74 37.49775) (xy 156.74 38.5595)
        (xy 156.76 38.5595) (xy 156.76 38.8135) (xy 156.74 38.8135) (xy 156.74 38.8335) (xy 156.486 38.8335)
        (xy 156.486 38.8135) (xy 155.36175 38.8135) (xy 155.203 38.97225) (xy 155.199928 39.399) (xy 155.212188 39.523482)
        (xy 155.212978 39.526086) (xy 155.203 39.61725) (xy 155.270968 39.685218) (xy 155.307463 39.753494) (xy 155.385842 39.849)
        (xy 155.307463 39.944506) (xy 155.248498 40.05482) (xy 155.235247 40.098503) (xy 155.203 40.13075) (xy 155.210105 40.195666)
        (xy 155.199928 40.299) (xy 155.199928 40.414) (xy 152.649712 40.414) (xy 152.61239 40.410324) (xy 152.575067 40.414)
        (xy 152.575057 40.414) (xy 152.463404 40.424997) (xy 152.337282 40.463255) (xy 152.320143 40.468454) (xy 152.188113 40.539026)
        (xy 152.125395 40.590498) (xy 152.072389 40.633999) (xy 152.048591 40.662997) (xy 150.998256 41.713333) (xy 150.995226 41.698102)
        (xy 150.917205 41.509744) (xy 150.803937 41.340226) (xy 150.659774 41.196063) (xy 150.490256 41.082795) (xy 150.301898 41.004774)
        (xy 150.101939 40.965) (xy 149.898061 40.965) (xy 149.698102 41.004774) (xy 149.509744 41.082795) (xy 149.340226 41.196063)
        (xy 149.196063 41.340226) (xy 149.082795 41.509744) (xy 149.004774 41.698102) (xy 148.965 41.898061) (xy 148.965 42.101939)
        (xy 149.004774 42.301898) (xy 149.082795 42.490256) (xy 149.196063 42.659774) (xy 149.230744 42.694455) (xy 147.344072 44.581126)
        (xy 147.344072 44.427) (xy 147.328929 44.273255) (xy 147.284084 44.125418) (xy 147.211258 43.989171) (xy 147.172546 43.942)
        (xy 147.211258 43.894829) (xy 147.284084 43.758582) (xy 147.328929 43.610745) (xy 147.344072 43.457) (xy 147.344072 43.157)
        (xy 147.328929 43.003255) (xy 147.284084 42.855418) (xy 147.211258 42.719171) (xy 147.172546 42.672) (xy 147.211258 42.624829)
        (xy 147.284084 42.488582) (xy 147.328929 42.340745) (xy 147.344072 42.187) (xy 147.344072 41.887) (xy 147.328929 41.733255)
        (xy 147.284084 41.585418) (xy 147.211258 41.449171) (xy 147.172546 41.402) (xy 147.211258 41.354829) (xy 147.284084 41.218582)
        (xy 147.328929 41.070745) (xy 147.344072 40.917) (xy 147.344072 40.617) (xy 147.328929 40.463255) (xy 147.284084 40.315418)
        (xy 147.211258 40.179171) (xy 147.172546 40.132) (xy 147.211258 40.084829) (xy 147.284084 39.948582) (xy 147.328929 39.800745)
        (xy 147.344072 39.647) (xy 147.344072 39.347) (xy 147.328929 39.193255) (xy 147.284084 39.045418) (xy 147.211258 38.909171)
        (xy 147.172546 38.862) (xy 147.211258 38.814829) (xy 147.284084 38.678582) (xy 147.328929 38.530745) (xy 147.344072 38.377)
        (xy 147.344072 38.138508) (xy 147.352001 38.132001) (xy 147.375804 38.102997) (xy 147.504801 37.974) (xy 155.199928 37.974)
        (xy 155.203 38.40075) (xy 155.36175 38.5595) (xy 156.486 38.5595) (xy 156.486 37.49775) (xy 156.32725 37.339)
        (xy 155.838 37.335928) (xy 155.713518 37.348188) (xy 155.59382 37.384498) (xy 155.483506 37.443463) (xy 155.386815 37.522815)
        (xy 155.307463 37.619506) (xy 155.248498 37.72982) (xy 155.212188 37.849518) (xy 155.199928 37.974) (xy 147.504801 37.974)
        (xy 150.073791 35.405012) (xy 150.573078 35.405012) (xy 150.6104 35.408688) (xy 150.647722 35.405012) (xy 150.647733 35.405012)
        (xy 150.759386 35.394015) (xy 150.902647 35.350558) (xy 151.034676 35.279986) (xy 151.150401 35.185013) (xy 151.174204 35.156009)
        (xy 151.375212 34.955001) (xy 152.926679 34.955001) (xy 152.964001 34.958677) (xy 153.001323 34.955001) (xy 153.001334 34.955001)
        (xy 153.112987 34.944004) (xy 153.256248 34.900547) (xy 153.388277 34.829975) (xy 153.504002 34.735002) (xy 153.527804 34.705999)
        (xy 154.033509 34.200296) (xy 154.236589 34.33599) (xy 154.506842 34.447932) (xy 154.79374 34.505) (xy 155.08626 34.505)
        (xy 155.373158 34.447932) (xy 155.643411 34.33599) (xy 155.886632 34.173475) (xy 156.093475 33.966632) (xy 156.21 33.79224)
        (xy 156.326525 33.966632) (xy 156.533368 34.173475) (xy 156.776589 34.33599) (xy 157.046842 34.447932) (xy 157.33374 34.505)
        (xy 157.62626 34.505) (xy 157.913158 34.447932) (xy 158.183411 34.33599) (xy 158.426632 34.173475) (xy 158.633475 33.966632)
        (xy 158.79599 33.723411) (xy 158.907932 33.453158) (xy 158.965 33.16626) (xy 158.965 32.87374) (xy 158.907932 32.586842)
        (xy 158.79599 32.316589) (xy 158.633475 32.073368) (xy 158.426632 31.866525) (xy 158.183411 31.70401) (xy 157.913158 31.592068)
        (xy 157.62626 31.535) (xy 157.33374 31.535) (xy 157.046842 31.592068) (xy 156.776589 31.70401) (xy 156.533368 31.866525)
        (xy 156.326525 32.073368) (xy 156.21 32.24776) (xy 156.093475 32.073368) (xy 155.886632 31.866525) (xy 155.643411 31.70401)
        (xy 155.373158 31.592068) (xy 155.08626 31.535) (xy 154.79374 31.535) (xy 154.506842 31.592068) (xy 154.236589 31.70401)
        (xy 153.993368 31.866525) (xy 153.786525 32.073368) (xy 153.67 32.24776) (xy 153.553475 32.073368) (xy 153.346632 31.866525)
        (xy 153.103411 31.70401) (xy 152.833158 31.592068) (xy 152.54626 31.535) (xy 152.25374 31.535) (xy 151.966842 31.592068)
        (xy 151.696589 31.70401) (xy 151.453368 31.866525) (xy 151.246525 32.073368) (xy 151.245 32.07565) (xy 151.245 31.885948)
        (xy 151.2517 31.817919) (xy 151.245 31.74989) (xy 151.245 31.749882) (xy 151.22496 31.546412) (xy 151.145764 31.285338)
        (xy 151.017157 31.044731) (xy 150.844081 30.833838) (xy 150.791231 30.790465) (xy 150.289534 30.288768) (xy 150.246161 30.235918)
        (xy 150.035268 30.062842) (xy 149.794661 29.934235) (xy 149.533587 29.855039) (xy 149.330117 29.834999) (xy 149.330109 29.834999)
        (xy 149.26208 29.828299) (xy 149.194051 29.834999) (xy 136.99603 29.834999) (xy 136.928001 29.828299) (xy 136.859972 29.834999)
        (xy 136.859964 29.834999) (xy 136.656494 29.855039) (xy 136.39542 29.934235) (xy 136.154813 30.062842) (xy 135.996767 30.192547)
        (xy 135.996765 30.192549) (xy 135.94392 30.235918) (xy 135.900551 30.288763) (xy 134.528237 31.661078) (xy 134.424589 31.70401)
        (xy 134.181368 31.866525) (xy 133.974525 32.073368) (xy 133.858 32.24776) (xy 133.741475 32.073368) (xy 133.534632 31.866525)
        (xy 133.291411 31.70401) (xy 133.021158 31.592068) (xy 132.73426 31.535) (xy 132.44174 31.535) (xy 132.154842 31.592068)
        (xy 131.884589 31.70401) (xy 131.641368 31.866525) (xy 131.434525 32.073368) (xy 131.318 32.24776) (xy 131.201475 32.073368)
        (xy 130.994632 31.866525) (xy 130.751411 31.70401) (xy 130.481158 31.592068) (xy 130.19426 31.535) (xy 129.90174 31.535)
        (xy 129.614842 31.592068) (xy 129.344589 31.70401) (xy 129.101368 31.866525) (xy 128.894525 32.073368) (xy 128.778 32.24776)
        (xy 128.661475 32.073368) (xy 128.454632 31.866525) (xy 128.211411 31.70401) (xy 127.941158 31.592068) (xy 127.65426 31.535)
        (xy 127.36174 31.535) (xy 127.074842 31.592068) (xy 126.804589 31.70401) (xy 126.561368 31.866525) (xy 126.354525 32.073368)
        (xy 126.238 32.24776) (xy 126.121475 32.073368) (xy 125.914632 31.866525) (xy 125.671411 31.70401) (xy 125.401158 31.592068)
        (xy 125.11426 31.535) (xy 124.82174 31.535) (xy 124.534842 31.592068) (xy 124.264589 31.70401) (xy 124.021368 31.866525)
        (xy 123.814525 32.073368) (xy 123.698 32.24776) (xy 123.581475 32.073368) (xy 123.374632 31.866525) (xy 123.131411 31.70401)
        (xy 122.861158 31.592068) (xy 122.57426 31.535) (xy 122.28174 31.535) (xy 121.994842 31.592068) (xy 121.724589 31.70401)
        (xy 121.481368 31.866525) (xy 121.274525 32.073368) (xy 121.158 32.24776) (xy 121.041475 32.073368) (xy 120.834632 31.866525)
        (xy 120.591411 31.70401) (xy 120.321158 31.592068) (xy 120.03426 31.535) (xy 119.74174 31.535) (xy 119.454842 31.592068)
        (xy 119.184589 31.70401) (xy 118.941368 31.866525) (xy 118.734525 32.073368) (xy 118.618 32.24776) (xy 118.501475 32.073368)
        (xy 118.294632 31.866525) (xy 118.051411 31.70401) (xy 117.781158 31.592068) (xy 117.49426 31.535) (xy 117.20174 31.535)
        (xy 116.914842 31.592068) (xy 116.644589 31.70401) (xy 116.401368 31.866525) (xy 116.194525 32.073368) (xy 116.078 32.24776)
        (xy 115.961475 32.073368) (xy 115.754632 31.866525) (xy 115.511411 31.70401) (xy 115.241158 31.592068) (xy 114.95426 31.535)
        (xy 114.66174 31.535) (xy 114.374842 31.592068) (xy 114.104589 31.70401) (xy 113.861368 31.866525) (xy 113.654525 32.073368)
        (xy 113.538 32.24776) (xy 113.421475 32.073368) (xy 113.214632 31.866525) (xy 112.971411 31.70401) (xy 112.701158 31.592068)
        (xy 112.41426 31.535) (xy 112.12174 31.535) (xy 111.834842 31.592068) (xy 111.564589 31.70401) (xy 111.321368 31.866525)
        (xy 111.114525 32.073368) (xy 110.998 32.24776) (xy 110.881475 32.073368) (xy 110.674632 31.866525) (xy 110.431411 31.70401)
        (xy 110.161158 31.592068) (xy 109.87426 31.535) (xy 109.58174 31.535) (xy 109.294842 31.592068) (xy 109.024589 31.70401)
        (xy 108.781368 31.866525) (xy 108.649513 31.99838) (xy 108.627502 31.92582) (xy 108.568537 31.815506) (xy 108.489185 31.718815)
        (xy 108.392494 31.639463) (xy 108.28218 31.580498) (xy 108.162482 31.544188) (xy 108.038 31.531928) (xy 107.47375 31.535)
        (xy 107.315 31.69375) (xy 107.315 32.893) (xy 107.335 32.893) (xy 107.335 33.147) (xy 107.315 33.147)
        (xy 107.315 34.34625) (xy 107.47375 34.505) (xy 108.038 34.508072) (xy 108.162482 34.495812) (xy 108.28218 34.459502)
        (xy 108.392494 34.400537) (xy 108.489185 34.321185) (xy 108.568537 34.224494) (xy 108.627502 34.11418) (xy 108.649513 34.04162)
        (xy 108.781368 34.173475) (xy 109.024589 34.33599) (xy 109.294842 34.447932) (xy 109.58174 34.505) (xy 109.87426 34.505)
        (xy 110.161158 34.447932) (xy 110.431411 34.33599) (xy 110.674632 34.173475) (xy 110.881475 33.966632) (xy 110.998 33.79224)
        (xy 111.114525 33.966632) (xy 111.321368 34.173475) (xy 111.564589 34.33599) (xy 111.834842 34.447932) (xy 112.12174 34.505)
        (xy 112.41426 34.505) (xy 112.701158 34.447932) (xy 112.971411 34.33599) (xy 113.214632 34.173475) (xy 113.421475 33.966632)
        (xy 113.538 33.79224) (xy 113.654525 33.966632) (xy 113.861368 34.173475) (xy 114.104589 34.33599) (xy 114.374842 34.447932)
        (xy 114.66174 34.505) (xy 114.95426 34.505) (xy 115.241158 34.447932) (xy 115.511411 34.33599) (xy 115.754632 34.173475)
        (xy 115.961475 33.966632) (xy 116.078 33.79224) (xy 116.194525 33.966632) (xy 116.401368 34.173475) (xy 116.644589 34.33599)
        (xy 116.914842 34.447932) (xy 117.20174 34.505) (xy 117.49426 34.505) (xy 117.781158 34.447932) (xy 118.051411 34.33599)
        (xy 118.294632 34.173475) (xy 118.501475 33.966632) (xy 118.618 33.79224) (xy 118.734525 33.966632) (xy 118.941368 34.173475)
        (xy 119.184589 34.33599) (xy 119.454842 34.447932) (xy 119.74174 34.505) (xy 120.03426 34.505) (xy 120.321158 34.447932)
        (xy 120.591411 34.33599) (xy 120.834632 34.173475) (xy 121.041475 33.966632) (xy 121.158 33.79224) (xy 121.274525 33.966632)
        (xy 121.481368 34.173475) (xy 121.724589 34.33599) (xy 121.994842 34.447932) (xy 122.28174 34.505) (xy 122.57426 34.505)
        (xy 122.861158 34.447932) (xy 123.131411 34.33599) (xy 123.374632 34.173475) (xy 123.581475 33.966632) (xy 123.698 33.79224)
        (xy 123.814525 33.966632) (xy 124.021368 34.173475) (xy 124.264589 34.33599) (xy 124.534842 34.447932) (xy 124.82174 34.505)
        (xy 125.11426 34.505) (xy 125.401158 34.447932) (xy 125.671411 34.33599) (xy 125.914632 34.173475) (xy 126.121475 33.966632)
        (xy 126.238 33.79224) (xy 126.354525 33.966632) (xy 126.561368 34.173475) (xy 126.804589 34.33599) (xy 127.074842 34.447932)
        (xy 127.36174 34.505) (xy 127.65426 34.505) (xy 127.941158 34.447932) (xy 128.211411 34.33599) (xy 128.454632 34.173475)
        (xy 128.661475 33.966632) (xy 128.778 33.79224) (xy 128.894525 33.966632) (xy 129.101368 34.173475) (xy 129.344589 34.33599)
        (xy 129.614842 34.447932) (xy 129.90174 34.505) (xy 130.19426 34.505) (xy 130.481158 34.447932) (xy 130.751411 34.33599)
        (xy 130.994632 34.173475) (xy 131.201475 33.966632) (xy 131.318 33.79224) (xy 131.434525 33.966632) (xy 131.641368 34.173475)
        (xy 131.884589 34.33599) (xy 132.154842 34.447932) (xy 132.44174 34.505) (xy 132.73426 34.505) (xy 133.021158 34.447932)
        (xy 133.291411 34.33599) (xy 133.534632 34.173475) (xy 133.741475 33.966632) (xy 133.858 33.79224) (xy 133.974525 33.966632)
        (xy 134.181368 34.173475) (xy 134.424589 34.33599) (xy 134.694842 34.447932) (xy 134.98174 34.505) (xy 135.27426 34.505)
        (xy 135.561158 34.447932) (xy 135.831411 34.33599) (xy 136.074632 34.173475) (xy 136.281475 33.966632) (xy 136.346042 33.87)
        (xy 143.291928 33.87) (xy 143.304188 33.994482) (xy 143.340498 34.11418) (xy 143.399463 34.224494) (xy 143.478815 34.321185)
        (xy 143.575506 34.400537) (xy 143.68582 34.459502) (xy 143.805518 34.495812) (xy 143.93 34.508072) (xy 144.49425 34.505)
        (xy 144.653 34.34625) (xy 144.653 33.147) (xy 143.45375 33.147) (xy 143.295 33.30575) (xy 143.291928 33.87)
        (xy 136.346042 33.87) (xy 136.44399 33.723411) (xy 136.486922 33.619763) (xy 137.501687 32.604999) (xy 143.294296 32.604999)
        (xy 143.295 32.73425) (xy 143.45375 32.893) (xy 144.653 32.893) (xy 144.653 32.873) (xy 144.907 32.873)
        (xy 144.907 32.893) (xy 144.927 32.893) (xy 144.927 33.147) (xy 144.907 33.147) (xy 144.907 34.34625)
        (xy 145.06575 34.505) (xy 145.63 34.508072) (xy 145.754482 34.495812) (xy 145.87418 34.459502) (xy 145.984494 34.400537)
        (xy 146.081185 34.321185) (xy 146.160537 34.224494) (xy 146.219502 34.11418) (xy 146.241513 34.04162) (xy 146.373368 34.173475)
        (xy 146.616589 34.33599) (xy 146.886842 34.447932) (xy 147.17374 34.505) (xy 147.300199 34.505) (xy 146.947969 34.857229)
        (xy 146.857582 34.808916) (xy 146.709745 34.764071) (xy 146.556 34.748928) (xy 144.906 34.748928) (xy 144.752255 34.764071)
        (xy 144.604418 34.808916) (xy 144.468171 34.881742) (xy 144.348749 34.979749) (xy 144.250742 35.099171) (xy 144.177916 35.235418)
        (xy 144.133071 35.383255) (xy 144.117928 35.537) (xy 144.117928 35.57199) (xy 142.391 35.57199) (xy 142.391 35.559998)
        (xy 142.232252 35.559998) (xy 142.391 35.40125) (xy 142.394072 35.387) (xy 142.381812 35.262518) (xy 142.345502 35.14282)
        (xy 142.286537 35.032506) (xy 142.207185 34.935815) (xy 142.110494 34.856463) (xy 142.00018 34.797498) (xy 141.880482 34.761188)
        (xy 141.756 34.748928) (xy 141.06675 34.752) (xy 140.908 34.91075) (xy 140.908 35.56) (xy 140.928 35.56)
        (xy 140.928 35.741114) (xy 140.866009 35.791989) (xy 140.84221 35.820988) (xy 140.829198 35.834) (xy 140.654 35.834)
        (xy 140.654 35.814) (xy 139.32975 35.814) (xy 139.24675 35.897) (xy 118.828325 35.897) (xy 118.791 35.893324)
        (xy 118.753675 35.897) (xy 118.753667 35.897) (xy 118.642014 35.907997) (xy 118.498753 35.951454) (xy 118.366724 36.022026)
        (xy 118.250999 36.116999) (xy 118.227201 36.145997) (xy 117.816198 36.557) (xy 117.754061 36.557) (xy 117.554102 36.596774)
        (xy 117.365744 36.674795) (xy 117.196226 36.788063) (xy 117.052063 36.932226) (xy 116.938795 37.101744) (xy 116.860774 37.290102)
        (xy 116.821 37.490061) (xy 116.821 37.693939) (xy 116.860774 37.893898) (xy 116.938795 38.082256) (xy 117.052063 38.251774)
        (xy 117.196226 38.395937) (xy 117.365744 38.509205) (xy 117.554102 38.587226) (xy 117.754061 38.627) (xy 117.957939 38.627)
        (xy 118.157898 38.587226) (xy 118.346256 38.509205) (xy 118.362832 38.49813) (xy 118.386395 38.513874) (xy 118.574753 38.591895)
        (xy 118.774712 38.631669) (xy 118.97859 38.631669) (xy 119.178549 38.591895) (xy 119.366907 38.513874) (xy 119.536425 38.400606)
        (xy 119.580362 38.356669) (xy 126.093875 38.356669) (xy 126.050498 38.43782) (xy 126.014188 38.557518) (xy 126.001928 38.682)
        (xy 126.005 38.99625) (xy 126.16375 39.155) (xy 127.213 39.155) (xy 127.213 39.135) (xy 127.467 39.135)
        (xy 127.467 39.155) (xy 127.487 39.155) (xy 127.487 39.409) (xy 127.467 39.409) (xy 127.467 39.429)
        (xy 127.213 39.429) (xy 127.213 39.409) (xy 126.16375 39.409) (xy 126.005 39.56775) (xy 126.001928 39.882)
        (xy 126.014188 40.006482) (xy 126.050498 40.12618) (xy 126.053609 40.132) (xy 126.050498 40.13782) (xy 126.014188 40.257518)
        (xy 126.001928 40.382) (xy 126.001928 41.582) (xy 126.014188 41.706482) (xy 126.050498 41.82618) (xy 126.109463 41.936494)
        (xy 126.188815 42.033185) (xy 126.223367 42.061541) (xy 126.096039 42.049) (xy 126.096031 42.049) (xy 126.028002 42.0423)
        (xy 125.959973 42.049) (xy 108.418593 42.049) (xy 108.350564 42.0423) (xy 108.282535 42.049) (xy 108.282527 42.049)
        (xy 108.079057 42.06904) (xy 107.817983 42.148236) (xy 107.577376 42.276843) (xy 107.41933 42.406548) (xy 107.419328 42.40655)
        (xy 107.366483 42.449919) (xy 107.323114 42.502764) (xy 104.224765 45.601114) (xy 104.171919 45.644484) (xy 104.12855 45.697329)
        (xy 104.128548 45.697331) (xy 104.078907 45.757819) (xy 103.998843 45.855377) (xy 103.934114 45.976477) (xy 103.870236 46.095984)
        (xy 103.79104 46.357057) (xy 103.790333 46.364236) (xy 103.771 46.560528) (xy 103.771 46.560535) (xy 103.7643 46.628564)
        (xy 103.771 46.696593) (xy 103.771 48.027037) (xy 103.79104 48.230507) (xy 103.832169 48.36609) (xy 103.834992 48.394755)
        (xy 103.885528 48.561351) (xy 103.967595 48.714887) (xy 104.078038 48.849462) (xy 104.104891 48.8715) (xy 104.078038 48.893538)
        (xy 103.967595 49.028113) (xy 103.885528 49.181649) (xy 103.834992 49.348245) (xy 103.817928 49.521499) (xy 103.817928 50.046501)
        (xy 103.834992 50.219755) (xy 103.885528 50.386351) (xy 103.967595 50.539887) (xy 104.078038 50.674462) (xy 104.212613 50.784905)
        (xy 104.366149 50.866972) (xy 104.532745 50.917508) (xy 104.705999 50.934572) (xy 105.054994 50.934572) (xy 105.855429 51.735008)
        (xy 102.262587 51.735008) (xy 102.054539 51.52696) (xy 102.069 51.45426) (xy 102.069 51.16174) (xy 102.011932 50.874842)
        (xy 101.89999 50.604589) (xy 101.737475 50.361368) (xy 101.530632 50.154525) (xy 101.35624 50.038) (xy 101.530632 49.921475)
        (xy 101.737475 49.714632) (xy 101.89999 49.471411) (xy 102.011932 49.201158) (xy 102.069 48.91426) (xy 102.069 48.62174)
        (xy 102.011932 48.334842) (xy 101.89999 48.064589) (xy 101.737475 47.821368) (xy 101.530632 47.614525) (xy 101.287411 47.45201)
        (xy 101.017158 47.340068) (xy 100.73026 47.283) (xy 100.43774 47.283) (xy 100.150842 47.340068) (xy 99.880589 47.45201)
        (xy 99.637368 47.614525) (xy 99.430525 47.821368) (xy 99.26801 48.064589) (xy 99.156068 48.334842) (xy 99.099 48.62174)
        (xy 99.099 48.91426) (xy 99.156068 49.201158) (xy 99.26801 49.471411) (xy 99.430525 49.714632) (xy 99.637368 49.921475)
        (xy 99.81176 50.038) (xy 99.637368 50.154525) (xy 99.430525 50.361368) (xy 99.26801 50.604589) (xy 99.156068 50.874842)
        (xy 99.099 51.16174) (xy 99.099 51.45426) (xy 99.156068 51.741158) (xy 99.26801 52.011411) (xy 99.430525 52.254632)
        (xy 99.56238 52.386487) (xy 99.48982 52.408498) (xy 99.379506 52.467463) (xy 99.282815 52.546815) (xy 99.203463 52.643506)
        (xy 99.144498 52.75382) (xy 99.108188 52.873518) (xy 99.095928 52.998) (xy 99.099 53.56225) (xy 99.25775 53.721)
        (xy 100.457 53.721) (xy 100.457 53.701) (xy 100.711 53.701) (xy 100.711 53.721) (xy 101.91025 53.721)
        (xy 102.069 53.56225) (xy 102.069312 53.505008) (xy 107.107008 53.505008) (xy 107.107009 55.866561) (xy 107.102727 55.91004)
        (xy 107.119813 56.08353) (xy 107.17042 56.250353) (xy 107.252598 56.404099) (xy 107.260616 56.413869) (xy 107.248482 56.410188)
        (xy 107.124 56.397928) (xy 106.55975 56.401) (xy 106.401 56.55975) (xy 106.401 57.409) (xy 107.60025 57.409)
        (xy 107.759 57.25025) (xy 107.762072 57.036) (xy 107.750676 56.920286) (xy 108.663468 57.833079) (xy 108.691181 57.866847)
        (xy 108.724949 57.89456) (xy 108.724951 57.894562) (xy 108.740824 57.907589) (xy 108.725208 57.986097) (xy 108.725208 58.189975)
        (xy 108.764982 58.389934) (xy 108.843003 58.578292) (xy 108.875208 58.626491) (xy 108.875209 60.053212) (xy 108.477422 60.451)
        (xy 107.629501 60.451) (xy 107.575185 60.384815) (xy 107.478494 60.305463) (xy 107.36818 60.246498) (xy 107.248482 60.210188)
        (xy 107.124 60.197928) (xy 105.424 60.197928) (xy 105.299518 60.210188) (xy 105.17982 60.246498) (xy 105.069506 60.305463)
        (xy 104.972815 60.384815) (xy 104.918499 60.451) (xy 101.329501 60.451) (xy 101.275185 60.384815) (xy 101.178494 60.305463)
        (xy 101.06818 60.246498) (xy 100.948482 60.210188) (xy 100.824 60.197928) (xy 99.124 60.197928) (xy 98.999518 60.210188)
        (xy 98.87982 60.246498) (xy 98.769506 60.305463) (xy 98.672815 60.384815) (xy 98.593463 60.481506) (xy 98.534498 60.59182)
        (xy 98.498188 60.711518) (xy 98.485928 60.836) (xy 97.66 60.836) (xy 97.66 58.036) (xy 98.485928 58.036)
        (xy 98.498188 58.160482) (xy 98.534498 58.28018) (xy 98.593463 58.390494) (xy 98.672815 58.487185) (xy 98.769506 58.566537)
        (xy 98.87982 58.625502) (xy 98.999518 58.661812) (xy 99.124 58.674072) (xy 99.68825 58.671) (xy 99.847 58.51225)
        (xy 99.847 57.663) (xy 100.101 57.663) (xy 100.101 58.51225) (xy 100.25975 58.671) (xy 100.824 58.674072)
        (xy 100.948482 58.661812) (xy 101.06818 58.625502) (xy 101.178494 58.566537) (xy 101.275185 58.487185) (xy 101.354537 58.390494)
        (xy 101.413502 58.28018) (xy 101.449812 58.160482) (xy 101.462072 58.036) (xy 104.785928 58.036) (xy 104.798188 58.160482)
        (xy 104.834498 58.28018) (xy 104.893463 58.390494) (xy 104.972815 58.487185) (xy 105.069506 58.566537) (xy 105.17982 58.625502)
        (xy 105.299518 58.661812) (xy 105.424 58.674072) (xy 105.98825 58.671) (xy 106.147 58.51225) (xy 106.147 57.663)
        (xy 106.401 57.663) (xy 106.401 58.51225) (xy 106.55975 58.671) (xy 107.124 58.674072) (xy 107.248482 58.661812)
        (xy 107.36818 58.625502) (xy 107.478494 58.566537) (xy 107.575185 58.487185) (xy 107.654537 58.390494) (xy 107.713502 58.28018)
        (xy 107.749812 58.160482) (xy 107.762072 58.036) (xy 107.759 57.82175) (xy 107.60025 57.663) (xy 106.401 57.663)
        (xy 106.147 57.663) (xy 104.94775 57.663) (xy 104.789 57.82175) (xy 104.785928 58.036) (xy 101.462072 58.036)
        (xy 101.459 57.82175) (xy 101.30025 57.663) (xy 100.101 57.663) (xy 99.847 57.663) (xy 98.64775 57.663)
        (xy 98.489 57.82175) (xy 98.485928 58.036) (xy 97.66 58.036) (xy 97.66 57.036) (xy 98.485928 57.036)
        (xy 98.489 57.25025) (xy 98.64775 57.409) (xy 99.847 57.409) (xy 99.847 56.55975) (xy 100.101 56.55975)
        (xy 100.101 57.409) (xy 101.30025 57.409) (xy 101.459 57.25025) (xy 101.462072 57.036) (xy 104.785928 57.036)
        (xy 104.789 57.25025) (xy 104.94775 57.409) (xy 106.147 57.409) (xy 106.147 56.55975) (xy 105.98825 56.401)
        (xy 105.424 56.397928) (xy 105.299518 56.410188) (xy 105.17982 56.446498) (xy 105.069506 56.505463) (xy 104.972815 56.584815)
        (xy 104.893463 56.681506) (xy 104.834498 56.79182) (xy 104.798188 56.911518) (xy 104.785928 57.036) (xy 101.462072 57.036)
        (xy 101.449812 56.911518) (xy 101.413502 56.79182) (xy 101.354537 56.681506) (xy 101.275185 56.584815) (xy 101.178494 56.505463)
        (xy 101.06818 56.446498) (xy 100.948482 56.410188) (xy 100.824 56.397928) (xy 100.25975 56.401) (xy 100.101 56.55975)
        (xy 99.847 56.55975) (xy 99.68825 56.401) (xy 99.124 56.397928) (xy 98.999518 56.410188) (xy 98.87982 56.446498)
        (xy 98.769506 56.505463) (xy 98.672815 56.584815) (xy 98.593463 56.681506) (xy 98.534498 56.79182) (xy 98.498188 56.911518)
        (xy 98.485928 57.036) (xy 97.66 57.036) (xy 97.66 54.698) (xy 99.095928 54.698) (xy 99.108188 54.822482)
        (xy 99.144498 54.94218) (xy 99.203463 55.052494) (xy 99.282815 55.149185) (xy 99.379506 55.228537) (xy 99.48982 55.287502)
        (xy 99.609518 55.323812) (xy 99.734 55.336072) (xy 100.29825 55.333) (xy 100.457 55.17425) (xy 100.457 53.975)
        (xy 100.711 53.975) (xy 100.711 55.17425) (xy 100.86975 55.333) (xy 101.434 55.336072) (xy 101.558482 55.323812)
        (xy 101.67818 55.287502) (xy 101.788494 55.228537) (xy 101.885185 55.149185) (xy 101.964537 55.052494) (xy 102.023502 54.94218)
        (xy 102.059812 54.822482) (xy 102.072072 54.698) (xy 102.069 54.13375) (xy 101.91025 53.975) (xy 100.711 53.975)
        (xy 100.457 53.975) (xy 99.25775 53.975) (xy 99.099 54.13375) (xy 99.095928 54.698) (xy 97.66 54.698)
        (xy 97.66 35.387) (xy 139.167928 35.387) (xy 139.171 35.40125) (xy 139.32975 35.56) (xy 140.654 35.56)
        (xy 140.654 34.91075) (xy 140.49525 34.752) (xy 139.806 34.748928) (xy 139.681518 34.761188) (xy 139.56182 34.797498)
        (xy 139.451506 34.856463) (xy 139.354815 34.935815) (xy 139.275463 35.032506) (xy 139.216498 35.14282) (xy 139.180188 35.262518)
        (xy 139.167928 35.387) (xy 97.66 35.387) (xy 97.66 33.87) (xy 105.699928 33.87) (xy 105.712188 33.994482)
        (xy 105.748498 34.11418) (xy 105.807463 34.224494) (xy 105.886815 34.321185) (xy 105.983506 34.400537) (xy 106.09382 34.459502)
        (xy 106.213518 34.495812) (xy 106.338 34.508072) (xy 106.90225 34.505) (xy 107.061 34.34625) (xy 107.061 33.147)
        (xy 105.86175 33.147) (xy 105.703 33.30575) (xy 105.699928 33.87) (xy 97.66 33.87) (xy 97.66 32.17)
        (xy 105.699928 32.17) (xy 105.703 32.73425) (xy 105.86175 32.893) (xy 107.061 32.893) (xy 107.061 31.69375)
        (xy 106.90225 31.535) (xy 106.338 31.531928) (xy 106.213518 31.544188) (xy 106.09382 31.580498) (xy 105.983506 31.639463)
        (xy 105.886815 31.718815) (xy 105.807463 31.815506) (xy 105.748498 31.92582) (xy 105.712188 32.045518) (xy 105.699928 32.17)
        (xy 97.66 32.17) (xy 97.66 27.032278) (xy 97.684748 26.779872) (xy 98.765 26.779872) (xy 98.765 27.220128)
        (xy 98.85089 27.651925) (xy 99.019369 28.058669) (xy 99.263962 28.424729) (xy 99.575271 28.736038) (xy 99.941331 28.980631)
        (xy 100.348075 29.14911) (xy 100.779872 29.235) (xy 101.220128 29.235) (xy 101.651925 29.14911) (xy 102.058669 28.980631)
        (xy 102.424729 28.736038) (xy 102.736038 28.424729) (xy 102.980631 28.058669) (xy 103.14911 27.651925) (xy 103.235 27.220128)
        (xy 103.235 26.779872) (xy 154.765 26.779872) (xy 154.765 27.220128) (xy 154.85089 27.651925) (xy 155.019369 28.058669)
        (xy 155.263962 28.424729) (xy 155.575271 28.736038) (xy 155.941331 28.980631) (xy 156.348075 29.14911) (xy 156.779872 29.235)
        (xy 157.220128 29.235) (xy 157.651925 29.14911) (xy 158.058669 28.980631) (xy 158.424729 28.736038) (xy 158.736038 28.424729)
        (xy 158.980631 28.058669) (xy 159.14911 27.651925) (xy 159.235 27.220128) (xy 159.235 26.779872) (xy 159.14911 26.348075)
        (xy 158.980631 25.941331) (xy 158.736038 25.575271) (xy 158.424729 25.263962) (xy 158.058669 25.019369) (xy 157.651925 24.85089)
        (xy 157.220128 24.765) (xy 156.779872 24.765) (xy 156.348075 24.85089) (xy 155.941331 25.019369) (xy 155.575271 25.263962)
        (xy 155.263962 25.575271) (xy 155.019369 25.941331) (xy 154.85089 26.348075) (xy 154.765 26.779872) (xy 103.235 26.779872)
        (xy 103.14911 26.348075) (xy 102.980631 25.941331) (xy 102.736038 25.575271) (xy 102.424729 25.263962) (xy 102.058669 25.019369)
        (xy 101.651925 24.85089) (xy 101.220128 24.765) (xy 100.779872 24.765) (xy 100.348075 24.85089) (xy 99.941331 25.019369)
        (xy 99.575271 25.263962) (xy 99.263962 25.575271) (xy 99.019369 25.941331) (xy 98.85089 26.348075) (xy 98.765 26.779872)
        (xy 97.684748 26.779872) (xy 97.726714 26.351874) (xy 97.914943 25.728428) (xy 98.220681 25.153421) (xy 98.632279 24.648753)
        (xy 99.134067 24.233638) (xy 99.706924 23.923895) (xy 100.329039 23.731318) (xy 101.007584 23.66) (xy 156.967722 23.66)
      )
    )
    (filled_polygon
      (pts
        (xy 124.952815 47.771185) (xy 125.049506 47.850537) (xy 125.15982 47.909502) (xy 125.279518 47.945812) (xy 125.404 47.958072)
        (xy 126.404 47.958072) (xy 126.528482 47.945812) (xy 126.615928 47.919286) (xy 126.615928 48.42) (xy 126.628188 48.544482)
        (xy 126.664498 48.66418) (xy 126.723463 48.774494) (xy 126.802815 48.871185) (xy 126.862296 48.92) (xy 126.802815 48.968815)
        (xy 126.723463 49.065506) (xy 126.664498 49.17582) (xy 126.628188 49.295518) (xy 126.615928 49.42) (xy 126.615928 50.42)
        (xy 126.628188 50.544482) (xy 126.664498 50.66418) (xy 126.723463 50.774494) (xy 126.802815 50.871185) (xy 126.862296 50.92)
        (xy 126.802815 50.968815) (xy 126.723463 51.065506) (xy 126.664498 51.17582) (xy 126.628188 51.295518) (xy 126.615928 51.42)
        (xy 126.615928 52.42) (xy 126.628188 52.544482) (xy 126.664498 52.66418) (xy 126.723463 52.774494) (xy 126.802815 52.871185)
        (xy 126.862296 52.92) (xy 126.802815 52.968815) (xy 126.723463 53.065506) (xy 126.664498 53.17582) (xy 126.628188 53.295518)
        (xy 126.615928 53.42) (xy 126.615928 54.42) (xy 126.628188 54.544482) (xy 126.664498 54.66418) (xy 126.723463 54.774494)
        (xy 126.802815 54.871185) (xy 126.862296 54.92) (xy 126.802815 54.968815) (xy 126.723463 55.065506) (xy 126.664498 55.17582)
        (xy 126.628188 55.295518) (xy 126.615928 55.42) (xy 126.615928 56.42) (xy 126.628188 56.544482) (xy 126.664498 56.66418)
        (xy 126.723463 56.774494) (xy 126.802815 56.871185) (xy 126.862296 56.92) (xy 126.802815 56.968815) (xy 126.723463 57.065506)
        (xy 126.664498 57.17582) (xy 126.628188 57.295518) (xy 126.615928 57.42) (xy 126.615928 58.42) (xy 126.628188 58.544482)
        (xy 126.664498 58.66418) (xy 126.723463 58.774494) (xy 126.802815 58.871185) (xy 126.862296 58.92) (xy 126.802815 58.968815)
        (xy 126.723463 59.065506) (xy 126.672954 59.16) (xy 121.702802 59.16) (xy 116.801001 54.2582) (xy 116.801001 51.351366)
        (xy 116.804677 51.314044) (xy 116.801001 51.276721) (xy 116.801001 51.276711) (xy 116.790004 51.165058) (xy 116.746547 51.021797)
        (xy 116.682481 50.901939) (xy 116.675975 50.889767) (xy 116.6048 50.803041) (xy 116.581002 50.774043) (xy 116.561957 50.758413)
        (xy 116.561957 50.698061) (xy 116.522183 50.498102) (xy 116.444162 50.309744) (xy 116.330894 50.140226) (xy 116.186731 49.996063)
        (xy 116.017213 49.882795) (xy 115.828855 49.804774) (xy 115.628896 49.765) (xy 115.425018 49.765) (xy 115.225059 49.804774)
        (xy 115.192072 49.818438) (xy 115.192072 49.42) (xy 115.179812 49.295518) (xy 115.143502 49.17582) (xy 115.084537 49.065506)
        (xy 115.005185 48.968815) (xy 114.945704 48.92) (xy 115.005185 48.871185) (xy 115.084537 48.774494) (xy 115.143502 48.66418)
        (xy 115.179812 48.544482) (xy 115.192072 48.42) (xy 115.189 48.20575) (xy 115.03025 48.047) (xy 113.431 48.047)
        (xy 113.431 48.067) (xy 113.177 48.067) (xy 113.177 48.047) (xy 113.157 48.047) (xy 113.157 47.793)
        (xy 113.177 47.793) (xy 113.177 47.773) (xy 113.431 47.773) (xy 113.431 47.793) (xy 114.979397 47.793)
        (xy 115.049506 47.850537) (xy 115.15982 47.909502) (xy 115.279518 47.945812) (xy 115.404 47.958072) (xy 116.404 47.958072)
        (xy 116.528482 47.945812) (xy 116.64818 47.909502) (xy 116.758494 47.850537) (xy 116.855185 47.771185) (xy 116.904 47.711704)
        (xy 116.952815 47.771185) (xy 117.049506 47.850537) (xy 117.15982 47.909502) (xy 117.279518 47.945812) (xy 117.404 47.958072)
        (xy 118.404 47.958072) (xy 118.528482 47.945812) (xy 118.64818 47.909502) (xy 118.758494 47.850537) (xy 118.855185 47.771185)
        (xy 118.904 47.711704) (xy 118.952815 47.771185) (xy 119.049506 47.850537) (xy 119.15982 47.909502) (xy 119.279518 47.945812)
        (xy 119.404 47.958072) (xy 120.404 47.958072) (xy 120.528482 47.945812) (xy 120.64818 47.909502) (xy 120.758494 47.850537)
        (xy 120.855185 47.771185) (xy 120.904 47.711704) (xy 120.952815 47.771185) (xy 121.049506 47.850537) (xy 121.15982 47.909502)
        (xy 121.279518 47.945812) (xy 121.404 47.958072) (xy 122.404 47.958072) (xy 122.528482 47.945812) (xy 122.64818 47.909502)
        (xy 122.758494 47.850537) (xy 122.855185 47.771185) (xy 122.904 47.711704) (xy 122.952815 47.771185) (xy 123.049506 47.850537)
        (xy 123.15982 47.909502) (xy 123.279518 47.945812) (xy 123.404 47.958072) (xy 124.404 47.958072) (xy 124.528482 47.945812)
        (xy 124.64818 47.909502) (xy 124.758494 47.850537) (xy 124.855185 47.771185) (xy 124.904 47.711704)
      )
    )
    (filled_polygon
      (pts
        (xy 109.455 44.856061) (xy 109.455 45.059939) (xy 109.494774 45.259898) (xy 109.572795 45.448256) (xy 109.686063 45.617774)
        (xy 109.830226 45.761937) (xy 109.999744 45.875205) (xy 110.188102 45.953226) (xy 110.388061 45.993) (xy 110.591939 45.993)
        (xy 110.791898 45.953226) (xy 110.980256 45.875205) (xy 111.149774 45.761937) (xy 111.193711 45.718) (xy 111.560724 45.718)
        (xy 111.435329 45.785026) (xy 111.42945 45.789851) (xy 111.348601 45.856201) (xy 111.348597 45.856205) (xy 111.319604 45.879999)
        (xy 111.29581 45.908992) (xy 110.874355 46.330448) (xy 110.812218 46.330448) (xy 110.612259 46.370222) (xy 110.423901 46.448243)
        (xy 110.254383 46.561511) (xy 110.11022 46.705674) (xy 110.034056 46.819661) (xy 109.92 46.808428) (xy 109.50575 46.8115)
        (xy 109.347 46.97025) (xy 109.347 47.832) (xy 109.367 47.832) (xy 109.367 48.086) (xy 109.347 48.086)
        (xy 109.347 48.106) (xy 109.093 48.106) (xy 109.093 48.086) (xy 108.04375 48.086) (xy 107.885 48.24475)
        (xy 107.881928 48.4715) (xy 107.894188 48.595982) (xy 107.930498 48.71568) (xy 107.989463 48.825994) (xy 108.068815 48.922685)
        (xy 108.098276 48.946863) (xy 108.031595 49.028113) (xy 107.949528 49.181649) (xy 107.898992 49.348245) (xy 107.881928 49.521499)
        (xy 107.881928 50.046501) (xy 107.898992 50.219755) (xy 107.949528 50.386351) (xy 108.031595 50.539887) (xy 108.142038 50.674462)
        (xy 108.276613 50.784905) (xy 108.430149 50.866972) (xy 108.596745 50.917508) (xy 108.769999 50.934572) (xy 109.670001 50.934572)
        (xy 109.843255 50.917508) (xy 110.009851 50.866972) (xy 110.163387 50.784905) (xy 110.297962 50.674462) (xy 110.387385 50.5655)
        (xy 111.434564 50.5655) (xy 111.464498 50.66418) (xy 111.523463 50.774494) (xy 111.602815 50.871185) (xy 111.662296 50.92)
        (xy 111.602815 50.968815) (xy 111.548499 51.035) (xy 107.658579 51.035) (xy 106.494072 49.870494) (xy 106.494072 49.521499)
        (xy 106.477008 49.348245) (xy 106.426472 49.181649) (xy 106.344405 49.028113) (xy 106.233962 48.893538) (xy 106.207109 48.8715)
        (xy 106.233962 48.849462) (xy 106.344405 48.714887) (xy 106.426472 48.561351) (xy 106.477008 48.394755) (xy 106.479831 48.36609)
        (xy 106.52096 48.230507) (xy 106.541 48.027037) (xy 106.541 47.4465) (xy 107.881928 47.4465) (xy 107.885 47.67325)
        (xy 108.04375 47.832) (xy 109.093 47.832) (xy 109.093 46.97025) (xy 108.93425 46.8115) (xy 108.52 46.808428)
        (xy 108.395518 46.820688) (xy 108.27582 46.856998) (xy 108.165506 46.915963) (xy 108.068815 46.995315) (xy 107.989463 47.092006)
        (xy 107.930498 47.20232) (xy 107.894188 47.322018) (xy 107.881928 47.4465) (xy 106.541 47.4465) (xy 106.541 47.202249)
        (xy 108.92425 44.819) (xy 109.462372 44.819)
      )
    )
    (filled_polygon
      (pts
        (xy 131.015498 39.46382) (xy 130.979188 39.583518) (xy 130.966928 39.708) (xy 130.97 39.87225) (xy 131.12875 40.031)
        (xy 131.953 40.031) (xy 131.953 40.011) (xy 132.207 40.011) (xy 132.207 40.031) (xy 133.03125 40.031)
        (xy 133.19 39.87225) (xy 133.193072 39.708) (xy 133.180812 39.583518) (xy 133.144502 39.46382) (xy 133.108251 39.396)
        (xy 135.349199 39.396) (xy 139.167928 43.21473) (xy 139.167928 43.457) (xy 139.183071 43.610745) (xy 139.227916 43.758582)
        (xy 139.259141 43.817) (xy 137.078114 43.817) (xy 136.438115 43.177002) (xy 136.414313 43.147999) (xy 136.298588 43.053026)
        (xy 136.166559 42.982454) (xy 136.023298 42.938997) (xy 135.911645 42.928) (xy 135.911634 42.928) (xy 135.874312 42.924324)
        (xy 135.83699 42.928) (xy 133.190413 42.928) (xy 133.176488 42.786623) (xy 133.127375 42.624717) (xy 133.0493 42.478649)
        (xy 133.085537 42.434494) (xy 133.144502 42.32418) (xy 133.180812 42.204482) (xy 133.193072 42.08) (xy 133.19 41.91575)
        (xy 133.03125 41.757) (xy 132.207 41.757) (xy 132.207 41.777) (xy 131.953 41.777) (xy 131.953 41.757)
        (xy 131.12875 41.757) (xy 130.97 41.91575) (xy 130.966928 42.08) (xy 130.979188 42.204482) (xy 131.015498 42.32418)
        (xy 131.074463 42.434494) (xy 131.1107 42.478649) (xy 131.032625 42.624717) (xy 131.025638 42.64775) (xy 131.0082 42.668998)
        (xy 130.749199 42.928) (xy 130.360802 42.928) (xy 129.649801 42.217) (xy 129.667002 42.217) (xy 129.667002 42.058252)
        (xy 129.82575 42.217) (xy 130.24 42.220072) (xy 130.364482 42.207812) (xy 130.48418 42.171502) (xy 130.594494 42.112537)
        (xy 130.691185 42.033185) (xy 130.770537 41.936494) (xy 130.829502 41.82618) (xy 130.865812 41.706482) (xy 130.878072 41.582)
        (xy 130.875 41.26775) (xy 130.71625 41.109) (xy 129.667 41.109) (xy 129.667 41.129) (xy 129.413 41.129)
        (xy 129.413 41.109) (xy 129.393 41.109) (xy 129.393 40.855) (xy 129.413 40.855) (xy 129.413 40.835)
        (xy 129.667 40.835) (xy 129.667 40.855) (xy 130.71625 40.855) (xy 130.875 40.69625) (xy 130.875862 40.608)
        (xy 130.966928 40.608) (xy 130.979188 40.732482) (xy 131.015498 40.85218) (xy 131.037852 40.894) (xy 131.015498 40.93582)
        (xy 130.979188 41.055518) (xy 130.966928 41.18) (xy 130.97 41.34425) (xy 131.12875 41.503) (xy 131.953 41.503)
        (xy 131.953 40.285) (xy 132.207 40.285) (xy 132.207 41.503) (xy 133.03125 41.503) (xy 133.19 41.34425)
        (xy 133.193072 41.18) (xy 133.180812 41.055518) (xy 133.144502 40.93582) (xy 133.122148 40.894) (xy 133.144502 40.85218)
        (xy 133.180812 40.732482) (xy 133.193072 40.608) (xy 133.19 40.44375) (xy 133.03125 40.285) (xy 132.207 40.285)
        (xy 131.953 40.285) (xy 131.12875 40.285) (xy 130.97 40.44375) (xy 130.966928 40.608) (xy 130.875862 40.608)
        (xy 130.878072 40.382) (xy 130.865812 40.257518) (xy 130.829502 40.13782) (xy 130.826391 40.132) (xy 130.829502 40.12618)
        (xy 130.865812 40.006482) (xy 130.878072 39.882) (xy 130.878072 39.368) (xy 131.066716 39.368)
      )
    )
  )
)
