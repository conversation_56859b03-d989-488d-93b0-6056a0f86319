package de.fellows.utils.model

import play.api.libs.json.Format
import play.api.libs.functional.syntax._
import java.util.UUID

case class PCBId(value: UUID) extends AnyVal {
  override def toString: String = value.toString
}
case class ShareId(value: UUID) extends AnyVal {
  override def toString: String = value.toString
}

object PCBId {
  implicit val format: Format[PCBId] = implicitly[Format[UUID]].inmap(PCBId(_), _.value)
  def random: PCBId                  = PCBId(UUID.randomUUID())
}

object ShareId {
  implicit val format: Format[ShareId] = implicitly[Format[UUID]].inmap(ShareId(_), _.value)
}
