package de.fellows.utils.common

import com.fasterxml.jackson.annotation.JacksonAnnotation
import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode

import scala.util.control.NoStackTrace

/**
  * @param msg
  * @param code
  */
case class EntityException(msg: String, code: Option[Int]) extends RuntimeException(msg) with NoStackTrace {

}

object EntityException {
  def apply(msg: String): EntityException = new EntityException(msg, None)

  def apply(msg: String, code: Int): EntityException = new EntityException(msg, Some(code))

  def apply(msg: String, code: Option[Int]): EntityException = new EntityException(msg, code)

  def apply(code: TransportErrorCode, msg: String): EntityException = new EntityException(msg, Some(code.http))
}
