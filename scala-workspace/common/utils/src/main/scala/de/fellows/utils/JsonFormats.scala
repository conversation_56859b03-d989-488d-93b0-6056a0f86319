package de.fellows.utils

import de.fellows.utils.logging.StackrateLogging
import play.api.libs.json._

import java.nio.file.Path
import scala.util.{Failure, Success, Try}

object JsonFormats {

  def singletonReads[O](singleton: O): Reads[O] =
    (__ \ "value").read[String].collect(
      JsonValidationError(
        s"Expected a JSON object with a single field with key 'value' and value '${singleton.getClass.getSimpleName}'"
      )
    ) {
      case s if s == singleton.getClass.getSimpleName => singleton
    }

  def singletonWrites[O]: Writes[O] = Writes { singleton =>
    Json.obj("value" -> singleton.getClass.getSimpleName)
  }

  def singletonFormat[O](singleton: O): Format[O] =
    Format(singletonReads(singleton), singletonWrites)

  implicit def optionFormat[T: Format]: Format[Option[T]] = new Format[Option[T]] {
    override def reads(json: JsValue): JsResult[Option[T]] = json.validateOpt[T]

    override def writes(o: Option[T]): JsValue = o match {
      case Some(t) => implicitly[Writes[T]].writes(t)
      case None    => JsNull
    }
  }

  implicit val pathFormat: Format[Path] = new Format[Path] {
    override def writes(o: Path): JsValue = JsString(o.toAbsolutePath.toString)

    override def reads(json: JsValue): JsResult[Path] = json match {
      case JsString(value) => Try(Path.of(value)) match {
          case Failure(exception) => JsError(exception.getMessage)
          case Success(value)     => JsSuccess(value)
        }
      case _ => JsError("Unknown Type")
    }
  }

  def JsonSealedTraitConfig =
    JsonConfiguration(
      discriminator = "_t",
      typeNaming = JsonFormats.JsonSealedTraitNaming
    )

  def JsonSealedTraitNaming =
    JsonNaming { fullName =>
      val names = fullName.split("\\.")
      names.last
    }

  trait EnumResolver[T] {
    def from(value: String): Option[T]

    def to(value: T): Option[String]

    def ++(other: EnumResolver[T]): EnumResolver[T]
  }

  class BasicResolver[T](vals: Map[T, Seq[String]], caseSensitive: Boolean = false) extends EnumResolver[T] {
    override def from(value: String): Option[T] = vals.find(v =>
      value match {
        case x if caseSensitive  => v._2.contains(x)
        case x if !caseSensitive => v._2.contains(x.toLowerCase)
        case _                             => false
      }
    ).map(_._1)

    override def to(value: T): Option[String] = {
      vals.getOrElse(value, Seq()).headOption
    }

    override def ++(other: EnumResolver[T]): EnumResolver[T] =
      new CombinedResolver[T](Seq(this, other))
  }


  class CombinedResolver[T](res: Seq[EnumResolver[T]]) extends EnumResolver[T] {
    override def from(value: String): Option[T] =
      res.flatMap(_.from(value)).headOption

    override def to(value: T): Option[String] =
      res.flatMap(_.to(value)).headOption

    override def ++(other: EnumResolver[T]): EnumResolver[T] =
      new CombinedResolver[T](res :+ other)
  }

  def resolver[T](values: Seq[T], name: T => String): EnumResolver[T] = {
    val map = values.map(t => (t -> Seq(name(t).toLowerCase))).toMap

    new BasicResolver[T](map)
  }

  def enumFormat[T](implicit res: EnumResolver[T]) =
    Format(
      Reads {
        case JsString(value) => res.from(value) match {
            case Some(value) => JsSuccess(value)
            case None        => JsError(s"failed to resolve $value")
          }
        case x => JsError(s"error! ${x}")
      },
      Writes {
        x: T =>

          val opt = res.to(x)
          JsString(opt.getOrElse(throw new IllegalStateException(s"failed to write ${x}")))
      }
    )
}
