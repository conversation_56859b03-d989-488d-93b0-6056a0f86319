package de.fellows.utils

import play.api.libs.json._

import scala.collection.mutable

object MapFormats extends Format[mutable.Map[String, String]]{
  override def writes(o: mutable.Map[String, String]): JsValue = {
    val ob = Json.obj()

    for ((key, value) <- o) {
      ob + (key -> Json.toJson(value))
    }

    ob
  }

  override def reads(json: JsValue): JsResult[mutable.Map[String, String]] = {
    val fields: mutable.Map[String, String] = mutable.Map()
    for ((key, value) <- json.as[JsObject].fields) {
      fields + (key -> value)
    }

    JsSuccess(fields)
  }
}
