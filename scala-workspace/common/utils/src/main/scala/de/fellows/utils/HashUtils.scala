package de.fellows.utils

import org.apache.commons.codec.digest.DigestUtils

import java.io.{File, FileInputStream}
import java.security.SecureRandom
import java.util
import java.util.{Base64, Objects}
import javax.crypto.spec.PBEKeySpec
import play.api.libs.json.{Format, Json}

import scala.util.Using

object HashUtils {
  def hashEquals(one: HashedPassword, two: HashedPassword): Boolean =
    if (one.hash.sameElements(two.hash)) {
      true
    } else {
      Thread.sleep(1000)
      false
    }

  private val ITERATIONS = 10000
  private val KEY_LENGTH = 256

  def rand: SecureRandom = new SecureRandom()

  def nextSalt(): Array[Byte] = {
    val salt: Array[Byte] = Array.fill(30) {
      0
    }
    rand.nextBytes(salt)
    salt
  }

  def hashPassword(password: Array[Char]): HashedPassword =
    hashPassword(password, nextSalt())

  def checkPassword(password: String, hash: HashedPassword): Bo<PERSON>an = {
    val hashed = hashPassword(password.toCharArray, hash.salt)
    hashed.hash.sameElements(hash.hash)
  }

  def hashPassword(password: Array[Char], salt: Array[Byte]): HashedPassword = {
    val spec = new PBEKeySpec(password, salt, ITERATIONS, KEY_LENGTH)
    util.Arrays.fill(password, Character.MIN_VALUE)

    import javax.crypto.SecretKeyFactory
    val f = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256")

    HashedPassword(f.generateSecret(spec).getEncoded, salt)
  }

  case class HashedPassword(hash: Array[Byte], salt: Array[Byte]) {
    override def toString: String = s"${util.Arrays.toString(hash)}, ${util.Arrays.toString(salt)}"

    override def equals(obj: Any): Boolean =
      obj match {
        case oh: HashedPassword =>
          util.Arrays.equals(oh.salt, salt) && util.Arrays.equals(oh.hash, hash)
        case _ =>
          false
      }

    override def hashCode(): Int = Objects.hash(hash, salt)

  }

  object HashedPassword {
    implicit val format: Format[HashedPassword] = Json.format
  }

  def generateEtag(file: File) = {
    val etag = s"${file.lastModified()}.${file.length()}"
    Base64.getEncoder.encodeToString(etag.getBytes)
  }

  def generateSha256Hash(file: File): String =
    Using.resource(new FileInputStream(file)) { fis =>
      DigestUtils.sha256Hex(fis)
    }

}
