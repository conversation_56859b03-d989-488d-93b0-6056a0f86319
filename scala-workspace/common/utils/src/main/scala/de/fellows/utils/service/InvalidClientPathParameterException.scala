package de.fellows.utils.service

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode

sealed abstract class InvalidClientPathParameterException(val msg: String, val errorCode: TransportErrorCode)
    extends Exception(msg)

class WrongTypeClientPathParameterException(msg: String)
    extends InvalidClientPathParameterException(msg, TransportErrorCode.ProtocolError)

object WrongTypeClientPathParameterException {
  def apply(parameter: String, expectedType: String): WrongTypeClientPathParameterException =
    new WrongTypeClientPathParameterException(
      s"Invalid parameter: $parameter is not of type $expectedType"
    )
}

class MissingClientPathParameterException(msg: String)
    extends InvalidClientPathParameterException(msg, TransportErrorCode.ProtocolError)

object MissingClientPathParameterException {
  def apply(expectedType: String): WrongTypeClientPathParameterException =
    new WrongTypeClientPathParameterException(
      s"Missing parameter: type $expectedType"
    )
}
