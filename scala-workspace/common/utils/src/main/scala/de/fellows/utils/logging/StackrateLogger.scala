package de.fellows.utils.logging

import org.slf4j
import org.slf4j.MDC
import play.api.{Logger, LoggerLike, MarkerContext}

import java.util.UUID
import scala.collection.mutable

class StackrateLogger(val underlying: Logger) extends LoggerLike with StackrateContext {
  override def logger: slf4j.Logger = underlying.logger

  private var team: Option[String] = None
  private var id: Option[String] = None
  private var version: Option[String] = None
  private var gid: Option[String] = None

  private val extra: mutable.Map[String, String] = mutable.Map()

  def withAssembly(team: String, id: UUID, version: Option[UUID], gid: Option[String]) = {
    this.team = Some(team)
    this.id = Some(id.toString)
    this.version = version.map(_.toString)
    this.gid = gid
  }

  def clear() = {
    this.team = None
    this.id = None
    this.version = None
    this.gid = None

  }

  def withID(team: String, id: UUID) = {
    this.team = Some(team)
    this.id = Some(id.toString)
    this.version = None
    this.gid = None
  }

  def mdc(k: String, v: String): StackrateLogger = {
    this.mdc(Map(k -> v))
  }

  def mdc(m: Map[String, String]): StackrateLogger = {
    this.extra ++= m
    this
  }


  private def hooked(cb: => Unit) = {
    try {
      this.team.foreach(MDC.put("team", _))
      this.id.foreach(MDC.put("id", _))
      this.version.foreach(MDC.put("version", _))
      this.gid.foreach(MDC.put("gid", _))

      this.extra.foreach((e) => MDC.put(e._1, e._2))
      cb
    } finally {
      this.team.foreach(_ => MDC.remove("team"))
      this.id.foreach(_ => MDC.remove("id"))
      this.version.foreach(_ => MDC.remove("version"))
      this.gid.foreach(_ => MDC.remove("gid"))

      this.extra.foreach((e) => MDC.remove(e._1))
    }
  }

  /**
    * Logs a message with the `TRACE` level.
    *
    * @param message the message to log
    * @param mc      the implicit marker context, if defined.
    */
  override def trace(message: => String)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isTraceEnabled) {
        mc.marker match {
          case None => logger.trace(message)
          case Some(marker) => logger.trace(marker, message)
        }
      }
    }
  }

  /**
    * Logs a message with the `TRACE` level.
    *
    * @param message the message to log
    * @param error   the associated exception
    * @param mc      the implicit marker context, if defined.
    */
  override def trace(message: => String, error: => Throwable)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isTraceEnabled) {
        mc.marker match {
          case None => logger.trace(message, error)
          case Some(marker) => logger.trace(marker, message, error)
        }
      }
    }
  }

  /**
    * Logs a message with the `DEBUG` level.
    *
    * @param message the message to log
    * @param mc      the implicit marker context, if defined.
    */
  override def debug(message: => String)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isDebugEnabled) {
        mc.marker match {
          case None => logger.debug(message)
          case Some(marker) => logger.debug(marker, message)
        }
      }
    }
  }

  /**
    * Logs a message with the `DEBUG` level.
    *
    * @param message the message to log
    * @param error   the associated exception
    * @param mc      the implicit marker context, if defined.
    */
  override def debug(message: => String, error: => Throwable)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isDebugEnabled) {
        mc.marker match {
          case None => logger.debug(message, error)
          case Some(marker) => logger.debug(marker, message, error)
        }
      }
    }
  }

  /**
    * Logs a message with the `INFO` level.
    *
    * @param message the message to log
    * @param mc      the implicit marker context, if defined.
    */
  override def info(message: => String)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isInfoEnabled) {
        mc.marker match {
          case None => logger.info(message)
          case Some(marker) => logger.info(marker, message)
        }
      }
    }
  }

  /**
    * Logs a message with the `INFO` level.
    *
    * @param message the message to log
    * @param error   the associated exception
    * @param mc      the implicit marker context, if defined.
    */
  override def info(message: => String, error: => Throwable)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isInfoEnabled) {
        mc.marker match {
          case None => logger.info(message, error)
          case Some(marker) => logger.info(marker, message, error)
        }
      }
    }
  }

  /**
    * Logs a message with the `WARN` level.
    *
    * @param message the message to log
    * @param mc      the implicit marker context, if defined.
    */
  override def warn(message: => String)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isWarnEnabled) {
        mc.marker match {
          case None => logger.warn(message)
          case Some(marker) => logger.warn(marker, message)
        }
      }
    }
  }

  /**
    * Logs a message with the `WARN` level.
    *
    * @param message the message to log
    * @param error   the associated exception
    * @param mc      the implicit marker context, if defined.
    */
  override def warn(message: => String, error: => Throwable)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isWarnEnabled) {
        mc.marker match {
          case None => logger.warn(message, error)
          case Some(marker) => logger.warn(marker, message, error)
        }
      }
    }
  }

  /**
    * Logs a message with the `ERROR` level.
    *
    * @param message the message to log
    * @param mc      the implicit marker context, if defined.
    */
  override def error(message: => String)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isErrorEnabled) {
        mc.marker match {
          case None => logger.error(message)
          case Some(marker) => logger.error(marker, message)
        }
      }
    }
  }

  /**
    * Logs a message with the `ERROR` level.
    *
    * @param message the message to log
    * @param error   the associated exception
    * @param mc      the implicit marker context, if defined.
    */
  override def error(message: => String, error: => Throwable)(implicit mc: MarkerContext): Unit = {
    hooked{
      if (isErrorEnabled) {
        mc.marker match {
          case None => logger.error(message, error)
          case Some(marker) => logger.error(marker, message, error)
        }
      }
    }
  }
}
