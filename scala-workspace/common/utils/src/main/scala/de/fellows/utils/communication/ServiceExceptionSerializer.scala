package de.fellows.utils.communication

import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.deser.{DefaultExceptionSerializer, RawExceptionMessage}
import com.lightbend.lagom.scaladsl.api.transport.{ExceptionMessage, MessageProtocol, TransportErrorCode}
import de.fellows.utils.Tracing
import de.fellows.utils.common.EntityException
import de.fellows.utils.communication.ServiceExceptionSerializer.{createJson, fromJson}
import play.api.{Environment, Mode}
import play.api.libs.json._

import scala.util.control.NonFatal

class ServiceExceptionSerializer() extends DefaultExceptionSerializer(Environment.simple(mode = Mode.Prod)) {

  override def serialize(exception: Throwable, accept: Seq[MessageProtocol]): RawExceptionMessage = {
    Tracing.error("", exception)

    exception match {
      case e: EntityException =>
        val error = e.code.getOrElse(TransportErrorCode.InternalServerError.http)
        val obj = Json.obj(
          "message" -> e.msg,
          "code"    -> error
        )
        val b = ByteString.fromString(Json.stringify(obj))

        RawExceptionMessage(
          TransportErrorCode.fromHttp(error),
          MessageProtocol(Some("application/json"), None, None),
          b
        )
      case e: ServiceException =>
        val messageBytes = ByteString.fromString(Json.stringify(createJson(e)))
        RawExceptionMessage(e.ecode.transportError, MessageProtocol(Some("application/json"), None, None), messageBytes)

      case _ => super.serialize(exception, accept)

    }
  }

  override def deserialize(message: RawExceptionMessage): Throwable = {
    val messageJson =
      try
        Json.parse(message.message.iterator.asInputStream)
      catch {
        case NonFatal(e) =>
          Json.obj()
      }

    val deserialized: Throwable = fromJson(message.message.utf8String, messageJson) match {
      case Some(value) => value
      case None        => super.deserialize(message)
    }

    deserialized
  }

}

object ServiceExceptionSerializer {
  def createJson(e: ServiceException): JsObject =
    Json.obj(
      "service" -> Json.obj(
        "name"     -> e.service.name,
        "code"     -> e.ecode.code,
        "message"  -> e.ecode.message,
        "httpCode" -> e.ecode.transportError.http,
        "type"     -> e.getClassName()
      ),
      "name"   -> e.emsg.name,
      "detail" -> e.emsg.detail
    )

  def fromJson(message: String, messageJson: JsValue): Option[ServiceException] =
    messageJson \ "service" match {
      case JsDefined(s) =>
        val excMsg = for {
          name   <- (messageJson \ "name").validate[String]
          detail <- (messageJson \ "detail").validate[String]
        } yield new ExceptionMessage(name, detail)

        val transportError = (s \ "httpCode").validateOpt[Int] match {
          case JsSuccess(value, _) =>
            value.map(TransportErrorCode.fromHttp).getOrElse(TransportErrorCode.InternalServerError)
          case JsError(_) =>
            TransportErrorCode.InternalServerError
        }

        val serviceMsg = for {
          code    <- (s \ "code").validate[Int]
          message <- (s \ "message").validate[String]
        } yield new ServiceError(code, message, transportError)

        val name = (s \ "name").validate[String]
        implicit val serviceDef = ServiceDefinition(name match {
          case JsSuccess(m, _) => m
          case JsError(_)      => "Unknown Service"
        })

        val serviceError = serviceMsg match {
          case JsSuccess(m, _) => m
          case JsError(_)      => new ServiceError(0, message)
        }
        val exceptionMessage = excMsg match {
          case JsSuccess(m, _) => m
          case JsError(_)      => new ExceptionMessage("UndeserializableException", message)
        }

        Some(new ServiceException(serviceError, exceptionMessage))

      case _: JsUndefined => None
    }
}
