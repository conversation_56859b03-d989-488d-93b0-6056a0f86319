package de.fellows.utils.apidoc

import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.deser.MessageSerializer.{NegotiatedDeserializer, NegotiatedSerializer}
import com.lightbend.lagom.scaladsl.api.deser.StrictMessageSerializer
import com.lightbend.lagom.scaladsl.api.transport.{MessageProtocol, NotAcceptable, UnsupportedMediaType}

import scala.collection.immutable

class ApidocSerializer extends StrictMessageSerializer[String] {
  //  val StringMessageSerializer: StrictMessageSerializer[String] = new StrictMessageSerializer[String] {


  private val defaultProtocol = MessageProtocol(Some("application/json"), Some("utf-8"), None)
  private val default2Protocol = MessageProtocol(Some("application/yaml"), Some("utf-8"), None)
  override val acceptResponseProtocols: immutable.Seq[MessageProtocol] = immutable.Seq(defaultProtocol, default2Protocol)

  private class StringSerializer(override val protocol: MessageProtocol)
    extends NegotiatedSerializer[String, ByteString] {
    override def serialize(s: String) = ByteString.fromString(s, protocol.charset.getOrElse("utf-8"))
  }

  private class StringDeserializer(charset: String) extends NegotiatedDeserializer[String, ByteString] {
    override def deserialize(wire: ByteString) = wire.decodeString(charset)
  }

  override val serializerForRequest: NegotiatedSerializer[String, ByteString] = new StringSerializer(defaultProtocol)

  override def deserializer(protocol: MessageProtocol): NegotiatedDeserializer[String, ByteString] = {
    val contentType = protocol.contentType
    if (contentType.contains("text/plain") || contentType.contains("application/json") || contentType.contains("application/yaml")) {
      new StringDeserializer(protocol.charset.getOrElse("utf-8"))
    } else {
      throw UnsupportedMediaType(protocol, defaultProtocol)
    }
  }

  override def serializerForResponse(
                                      acceptedMessageProtocols: immutable.Seq[MessageProtocol]
                                    ): NegotiatedSerializer[String, ByteString] = {
    if (acceptedMessageProtocols.isEmpty) {
      serializerForRequest
    } else {
      acceptedMessageProtocols.collectFirst{
        case wildcardOrNone if wildcardOrNone.contentType.forall(ct => ct == "*" || ct == "*/*") =>
          new StringSerializer(wildcardOrNone.withContentType("text/plain"))
        case textPlain if textPlain.contentType.contains("text/plain") =>
          new StringSerializer(textPlain)
        case json if json.contentType.contains("application/json") =>
          new StringSerializer(json)
        case yaml if yaml.contentType.contains("application/yaml") =>
          new StringSerializer(yaml)
      } match {
        case Some(serializer) => serializer
        case None => throw NotAcceptable(acceptedMessageProtocols, defaultProtocol)
      }
    }
  }
}
