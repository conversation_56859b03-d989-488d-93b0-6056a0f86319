package de.fellows.utils.apidoc;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;

@Target({METHOD, TYPE, FIELD, ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ClasspathDocumentation {
    String resource();

    String resourceType() default "markdown";

    boolean after() default true;
}
