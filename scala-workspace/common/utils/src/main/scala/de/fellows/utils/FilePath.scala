package de.fellows.utils

import com.typesafe.config.Config
import de.fellows.utils.FilePathUtils.pathed
import de.fellows.utils.communication.ServiceDefinition
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.security.{FileSecurityClaim, FileTokenContent, JwtTokenUtil}
import play.api.Logging
import play.api.libs.json
import play.api.libs.json.Json.WithDefaultValues
import play.api.libs.json.{Format, JsError, JsSuccess, Json, Reads, Writes}

import java.io.{FileInputStream, FileOutputStream}
import java.nio.file.{Files, Path, Paths, StandardCopyOption}
import java.util.regex.Pattern
import java.util.zip.{GZIPInputStream, GZIPOutputStream}
import java.util.{Base64, Random}
import scala.concurrent.blocking
import scala.util.{Failure, Success, Try, Using}

/** A Serialized Object with typing.
  *  Non-API use, as it uses system paths.
  *
  * Provides better type safety than using [[FilePath]]
  * @param path the path to serialize to
  * @tparam X the type
  */
case class SerializedCompressedFile[X](
    path: Path
) extends StackrateLogging {
  private val j: Json.WithOptions[WithDefaultValues] = Json.using[WithDefaultValues]

  def read()(implicit reads: Reads[X]): Try[X] =
    Try {
      blocking {
        Using.resource(new GZIPInputStream(new FileInputStream(path.toFile))) { in =>
          j.fromJson[X](j.parse(in)) match {
            case JsSuccess(value, _) => Success(value)
            case JsError(errors) =>
              val msg = errors.map { x =>
                s"${x._1.toJsonString} -> ${x._2.map(_.messages.mkString(",")).mkString(",")}"
              }.mkString(";")
              val exception = new IllegalStateException(msg)
              logger.error(exception.getMessage, exception)
              Failure(exception)
          }
        }
      }
    }.flatten

  def write(content: X)(implicit reads: Writes[X]): Try[SerializedCompressedFile[X]] =
    Try {
      blocking {
        val parentFolder = path.toFile.getParentFile
        parentFolder.mkdirs()

        val tempFile = Files.createTempFile(parentFolder.toPath, path.getFileName.toString, ".tmp")

        logger.warn(s"writing to ${tempFile}")

        Using.resource(new GZIPOutputStream(new FileOutputStream(tempFile.toFile))) { out =>
          out.write(Json.toBytes(j.toJson(content)))
          out.flush()
        }

        Files.move(tempFile, path, StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.ATOMIC_MOVE)
      }

      this
    }
}

object SerializedCompressedFile {
  implicit val path: Format[Path] = JsonFormats.pathFormat

  implicit def format[X]: json.Format[SerializedCompressedFile[X]] = Json.using[Json.WithDefaultValues].format
}

/** @param fsroot the root of the filesystem
  * @param team the team the file is part of
  * @param resource the resource the file is part of (usually an id)
  * @param base a category of files (e.g. "upload")
  * @param subPath the path of the file relative to the base
  *                (i.e. the original path of the file as it appeared in an uploaded archive)
  * @param filename the name of the file
  */
case class FilePath(
    fsroot: String,
    team: String,
    resource: String,
    base: String,
    subPath: Option[Seq[String]],
    filename: String
) extends Logging {
  def sub(child: String): FilePath =
    copy(subPath = Some(subPath.getOrElse(Seq.empty) :+ child))
  def sub(children: Seq[String]): FilePath =
    copy(subPath = Some(subPath.getOrElse(Seq.empty) ++ children))

  def basePath: Path =
    this.copy(filename = "").toJavaPath

  def copyTo(targetPath: FilePath): Path =
    Files.copy(this.toJavaPath, targetPath.toJavaPath, StandardCopyOption.REPLACE_EXISTING)

  def toJavaFile: java.io.File = toJavaPath.toFile

  def createParentDir(): Boolean =
    this.toJavaPath.getParent.toFile.mkdirs()

  def toPath(decode: Boolean): String =
    if (decode) {
      URLUtils.decodeParts(s"/${pathed(fsroot)}/${toRelativePath}")
    } else {
      s"/${pathed(fsroot)}/${toRelativePath}"
    }

  def toPath: String = toPath(false)

  def apiPath: String = pathed(s"${pathed(base)}/${FilePathUtils.pathed(subPath)}${pathed(filename)}")

  def toRelativePath: String =
    pathed(s"${pathed(team)}/${pathed(resource)}/$apiPath")

  def toJavaPath(decode: Boolean): Path = Paths.get(toPath(decode))

  def toJavaPath: Path = toJavaPath(false)

  def findExisting(): Option[Path] = {
    val encoded = toJavaPath(false)
    if (encoded.toFile.exists()) {
      Some(encoded)
    } else {
      val decoded = toJavaPath(true)
      if (decoded.toFile.exists()) {
        Some(decoded)
      } else {
        None
      }
    }

  }

  def suffix: (String, Option[String]) =
    filename.split('.') match {
      case x if x.length == 1 => (filename.mkString("."), None)
      case x                  => (x.dropRight(1).mkString("."), Some(x.last))
    }

  def stamped: FilePath =
    stamped(true)

  def delete: Boolean =
    this.toJavaFile.delete()

  def stamped(move: Boolean): FilePath = {

    val suff    = suffix
    val newName = s"${suff._1}-${UUIDUtils.createShort()}${suff._2.map(s => s".${s}").getOrElse("")}"

    val newPath = copy(filename = newName)

    logger.info(s"moving ${toPath} to ${newPath.toPath}")
    if (move) {
      toJavaFile.renameTo(newPath.toJavaFile)
    } else {
      Files.copy(toJavaPath, newPath.toJavaPath)
    }

    newPath
  }

  /** Create FilePath with the same root, team, resource and base as #this, but a filename that points to the new path.
    *
    * @param otherPath
    * the new path. has to be absolute, but able to relativize to this path
    * @return
    * the new FilePath. fails, if the new relative path exceeds the root given by this path (w/o filename)
    */
  def relativize(otherPath: Path): Option[FilePath] = {
    val relPath = this.copy(filename = "").toJavaPath.relativize(otherPath)
    val newName = relPath.toString
    if (!newName.startsWith("./")) {
      Some(this.copy(filename = newName))
    } else {
      None
    }
  }

  def toApi(implicit d: ServiceDefinition) = s"$apiPath?k=${createToken(d)}"

  private def createToken(d: ServiceDefinition): String =
    JwtTokenUtil.generateFileToken(FileTokenContent(team, Seq(FileSecurityClaim(d.name, resource, apiPath)))).authToken

  def toAccess(sd: ServiceDefinition)(c: FilePath => String): FileAccess =
    FileAccess(
      path = c(this),
      key = createToken(sd)
    )

  def matchesResource(res: String): Boolean =
    URLUtils.decodeParts(pathed(resource)) == URLUtils.decodeParts(pathed(res))

  def matchesApiPath(res: String): Boolean = {

    val decodedRes = URLUtils.decodeParts(pathed(res))
    val decodedApi = URLUtils.decodeParts(apiPath)
    decodedApi == decodedRes
  }

  def toBase64String(): String =
    Base64.getEncoder.encodeToString(Files.readAllBytes(toJavaPath))
}

object FilePath {
  def basePath(implicit conf: Config): String =
    conf.getString("fellows.storage.service")

  val chars   = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
  val r       = new Random()
  val charlen = chars.length

  /** creates a temporary folder on the persistent volume.
    * This is not guaranteed to be deleted at any time,
    * but may be subject to deletion by manual/automated cleanup tasks.
    *
    * Best practice: delete it yourself after you are done.
    *
    * @param str prefix string for the folder name.
    */
  def createTempDirectory(str: String)(implicit conf: Config): Path = {
    var p  = Paths.get(basePath)
    val id = UUIDUtils.randomString(30)
    p = p.resolve("tmp")
    p = p.resolve(s"$str-$id")
    p.toFile.mkdirs()
    p
  }

  implicit val format: Format[FilePath] = Json.format

  def apply(fsRoot: String): FilePath = {
    val p    = Paths.get(fsRoot)
    val root = p.getRoot.resolve(p.getName(0))
    FilePath(root.toString, root.relativize(p).toString)
  }

  def apply(fsRoot: String, relativePath: String): FilePath = {
    var cleaned = relativePath

    cleaned =
      if (cleaned startsWith "/") {
        cleaned.substring(1)
      } else {
        cleaned
      }

    val p = Paths.get(fsRoot, cleaned)

    val splitseq = cleaned.split("/").toSeq
    val parts = splitseq.flatMap {
      case x if x.trim == "" => None
      case x                 => Some(x)
    }

    val root = fsRoot
    val team = parts.head

    if (parts.length < 3) {
      throw new IllegalArgumentException("too few arguments")
    }
    val resource = parts.slice(1, parts.size - 2).mkString("/")

    val base     = parts(parts.size - 2)
    val filename = p.getFileName.toString

    new FilePath(root, team, resource, base, None, filename)
  }

  private val NON_PRINTABLE_CHARS = Pattern.compile("[^ -~]+")

  def clean(s: String): String = {

    // < (less than)
    // > (greater than)
    // : (colon - sometimes works, but is actually NTFS Alternate Data Streams)
    // " (double quote)
    /// (forward slash)
    // \ (backslash)
    // | (vertical bar or pipe)
    // ? (question mark)
    // * (asterisk)
    // '  ' (space - can mess up http paths)
    val illegalChars = Seq('<', '>', ':', '"', '/', '\\', '|', '?', '*', '[', ']', '(', ')', '#', ' ')
    var res          = s

    illegalChars.foreach(c => res = res.replace(c, '-'))

    res = NON_PRINTABLE_CHARS.matcher(res).replaceAll("")
    res
  }

  def splitSubPath(path: String): (Option[Seq[String]], String) = {
    val parts = path.split("/").toSeq
    val subPath = parts.dropRight(1) match {
      case x if x.isEmpty => None
      case x              => Some(x)
    }
    val filename = parts.last
    (subPath, filename)
  }

}

object FilePathUtils extends Logging {

  val tmp = "/tmp/stackrate/temporary-files/"

  def createTransientTemporaryFile(prefix: String, suffix: String): FilePath =
    FilePath(Paths.get(tmp).resolve(s"$prefix-${UUIDUtils.createShort()}-$suffix").toAbsolutePath.toString)

  def pathed(s: Option[Seq[String]]): String =
    s match {
      case Some(value) => value.map(pathed).mkString("/") + "/"
      case None        => ""
    }

  def pathed(s: String): String = {
    var pre = s

    while (pre endsWith "/")
      pre = pre.substring(0, pre.length - 1)
    while (pre startsWith "/")
      pre = pre.substring(1)

    pre
  }

}
