package de.fellows.utils.communication

import com.lightbend.lagom.scaladsl.api.transport.TransportErrorCode
import de.fellows.utils.JsonFormats.singletonFormat
import play.api.libs.json.{Format, Json, Reads, __}
import play.api.libs.json._
import play.api.libs.functional.syntax._

case class ServiceError(@deprecated code: Int, message: String, transportError: TransportErrorCode = TransportErrorCode.InternalServerError)

object ServiceError {

  //  private val treads = (
  //    (__ \ "http").read[Int] and
  //      (__ \ "websocket").read[Int] and
  //      (__ \ "description").read[String]
  //    ) ((a, b, c) => TransportErrorCode(a, b, c))
  //  private val twrites = (
  //    (__ \ "http").write[Int] and
  //      (__ \ "websocket").write[Int] and
  //      (__ \ "description").write[String]
  //    ) ((x: TransportErrorCode) => (x.http, x.webSocket, x.description))
  //
  //
  //  implicit val tformat: Format[TransportErrorCode] = Format[TransportErrorCode](
  //    treads
  //    ,
  //    twrites
  //  )
  //
  //  implicit val format: Format[ServiceError] = Json.format
}

object InternalServiceError extends ServiceError(5, "Internal Service Error") {
  @transient implicit val format: Format[InternalServiceError.type] = singletonFormat(InternalServiceError)
}

class DetailedInternalServiceError(f: String) extends ServiceError(1, s"Internal Service Error: $f", transportError = TransportErrorCode.InternalServerError) {
  @transient implicit val format: Format[InternalServiceError.type] = singletonFormat(InternalServiceError)
}
