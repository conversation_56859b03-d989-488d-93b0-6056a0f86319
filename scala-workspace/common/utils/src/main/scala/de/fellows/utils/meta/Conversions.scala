package de.fellows.utils.meta

import de.fellows.utils.JsonFormats.EnumResolver
import play.api.libs.json.{JsString, JsValue}

class NoOpResolver[X] extends EnumResolver[X] {
  override def from(value: String): Option[X] = None

  override def to(value: X): Option[String] = None

  override def ++(other: EnumResolver[X]): EnumResolver[X] = other
}

object Conversions {
  def conv[T](x: Option[Any])(implicit resolver: EnumResolver[T] = new NoOpResolver[T]): Option[T] =
    x match {
      case Some(value: String) => resolver.from(value)
      case Some(value: T)      => Some(value)
      case None                => None
    }
}
