package de.fellows.utils.security

import play.api.libs.json.Format.GenericFormat
import play.api.libs.json._

import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.{Date, UUID}

case class LicenseContent(
    title: String,
    valid: Boolean,
    valid_till: Date,
    modules: Seq[LicenseModule],
    adminLicense: Option[Boolean] = None
)

case class LicenseModule(id: String, title: String)

// resourceclass:team:owner:resource:attribute:action
case class SecurityClaim(permission: Option[String])

case class FileSecurityClaim(service: String, resource: String, path: String)

object SecurityClaim {
  implicit val format: Format[SecurityClaim] = Json.format
}

object FileSecurityClaim {
  implicit val format: Format[FileSecurityClaim] = Json.format
}

sealed trait GenericTokenContent {
  def getUserId: String
  def getTeam: String
  def getAnalyticsId: Option[String]
}

case class TokenContent(
    userId: UUID,
    team: String,
    username: String,
    email: Option[String],
    share: Option[String],
    isRefreshToken: Boolean = false,
    claims: Seq[SecurityClaim] = Seq(),
    isQueryParameter: Boolean = false
) extends GenericTokenContent {
  override def getUserId: String              = userId.toString
  override def getTeam: String                = team
  override def getAnalyticsId: Option[String] = Some(getUserId)
}

sealed trait Auth0Token extends GenericTokenContent {
  def permissions: Seq[String]
}

case class Auth0TokenContent(
    sub: String,
    iss: String,
    scope: String,
    azp: String,
    permissions: Seq[String],
    tenant: String,
    analyticsId: String
) extends Auth0Token {
  override def getUserId: String              = sub
  override def getTeam: String                = tenant
  override def getAnalyticsId: Option[String] = Some(analyticsId)
}

object LicenseModule {
  implicit val format: Format[LicenseModule] = Json.format

  val ALL = Seq(
    LicenseModule("web-dfm", "Web DFM"),
    LicenseModule("pricing-module", "Pricing Module"),
    LicenseModule("panel-designer", "Panel Designer"),
    LicenseModule("gerber-viewer", "Gerber Viewer"),
    LicenseModule("stackup-manager", "Stackup Manager"),
    LicenseModule("spec-manager", "Specification Manager")
  )
}

object TokenContent {
  implicit val format: Format[TokenContent] = Json.format

  def technicalUser(claims: Seq[SecurityClaim] = Seq(), team: String = "technical-team"): TokenContent =
    TokenContent(
      new UUID(0, 0),
      team,
      "technical-user",
      None,
      None,
      false,
      claims,
    )
}

object Auth0TokenContent {

  import play.api.libs.functional.syntax._
  import play.api.libs.json._

  final case class Auth0Permission(value: String)
  val ViewCustomersPermission: Auth0Permission = Auth0Permission("view:customer:all")

  implicit val userFormat2: Format[Auth0TokenContent] = (
    (__ \ implicitly[JsonConfiguration].naming("sub")).format[String] and
      (__ \ implicitly[JsonConfiguration].naming("iss")).format[String] and
      (__ \ implicitly[JsonConfiguration].naming("scope")).format[String] and
      (__ \ implicitly[JsonConfiguration].naming("azp")).format[String] and
      (__ \ implicitly[JsonConfiguration].naming("permissions")).format[Seq[String]] and
      (__ \ implicitly[JsonConfiguration].naming("tenant")).format[String] and
      (__ \ implicitly[JsonConfiguration].naming("analytics_id")).format[String]
  )(Auth0TokenContent.apply, unlift(Auth0TokenContent.unapply))
}

case class FileTokenContent(
    team: String,
    claims: Seq[FileSecurityClaim],
    isQueryParameter: Boolean = true
) extends GenericTokenContent {
  override def getTeam: String                = team
  override def getUserId: String              = ???
  override def getAnalyticsId: Option[String] = None
}

object FileTokenContent {
  implicit val format: Format[FileTokenContent] = Json.format
}

case class InvitationToken(team: String, inviter: UUID, mail: Option[String])

object InvitationToken {
  implicit val format: Format[InvitationToken] = Json.format
}

// Fake token for development, when a fake token is used in Pcb-server, should act like an auth0 token
case class FakeToken(tenant: String) extends Auth0Token {
  override def getUserId: String              = "fake"
  override def getTeam: String                = tenant
  override def getAnalyticsId: Option[String] = None
  override def permissions: Seq[String]       = Seq.empty
}
