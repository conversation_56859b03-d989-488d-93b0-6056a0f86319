package de.fellows.utils

import de.fellows.utils.JsonFormats.BasicResolver
import play.api.libs.json.Format

import java.util.Locale

sealed trait CurrencyCode {
  val code: String
  lazy val currency                = java.util.Currency.getInstance(code)
  def symbol: String               = currency.getSymbol
  def name: String                 = currency.getDisplayName
  def name(locale: Locale): String = currency.getDisplayName(locale)
}

object CurrencyCode {

  final case object EUR extends CurrencyCode {
    override val code: String = "EUR"
  }
  final case object USD extends CurrencyCode {
    override val code: String = "USD"
  }
  final case object AUD extends CurrencyCode {
    override val code: String = "AUD"
  }
  final case object BGN extends C<PERSON>rencyCode {
    override val code: String = "BGN"
  }
  final case object BRL extends CurrencyCode {
    override val code: String = "BRL"
  }
  final case object CAD extends CurrencyCode {
    override val code: String = "CAD"
  }
  final case object CHF extends CurrencyCode {
    override val code: String = "CHF"
  }
  final case object CNY extends C<PERSON>rencyCode {
    override val code: String = "CNY"
  }
  final case object CZK extends CurrencyCode {
    override val code: String = "CZK"
  }
  final case object DKK extends CurrencyCode {
    override val code: String = "DKK"
  }
  final case object GBP extends CurrencyCode {
    override val code: String = "GBP"
  }
  final case object HKD extends CurrencyCode {
    override val code: String = "HKD"
  }
  final case object HRK extends CurrencyCode {
    override val code: String = "HRK"
  }
  final case object HUF extends CurrencyCode {
    override val code: String = "HUF"
  }
  final case object IDR extends CurrencyCode {
    override val code: String = "IDR"
  }
  final case object ILS extends CurrencyCode {
    override val code: String = "ILS"
  }
  final case object INR extends CurrencyCode {
    override val code: String = "INR"
  }
  final case object ISK extends CurrencyCode {
    override val code: String = "ISK"
  }
  final case object JPY extends CurrencyCode {
    override val code: String = "JPY"
  }
  final case object KRW extends CurrencyCode {
    override val code: String = "KRW"
  }
  final case object MXN extends CurrencyCode {
    override val code: String = "MXN"
  }
  final case object MYR extends CurrencyCode {
    override val code: String = "MYR"
  }
  final case object NOK extends CurrencyCode {
    override val code: String = "NOK"
  }
  final case object NZD extends CurrencyCode {
    override val code: String = "NZD"
  }
  final case object PHP extends CurrencyCode {
    override val code: String = "PHP"
  }
  final case object PLN extends CurrencyCode {
    override val code: String = "PLN"
  }
  final case object RON extends CurrencyCode {
    override val code: String = "RON"
  }
  final case object RUB extends CurrencyCode {
    override val code: String = "RUB"
  }
  final case object SEK extends CurrencyCode {
    override val code: String = "SEK"
  }
  final case object SGD extends CurrencyCode {
    override val code: String = "SGD"
  }
  final case object THB extends CurrencyCode {
    override val code: String = "THB"
  }
  final case object TRY extends CurrencyCode {
    override val code: String = "TRY"
  }
  final case object ZAR extends CurrencyCode {
    override val code: String = "ZAR"
  }
  final case object TWD extends CurrencyCode {
    override val code: String = "TWD"
  }

  val ALL: Seq[CurrencyCode] = Seq(
    EUR,
    USD,
    AUD,
    BGN,
    BRL,
    CAD,
    CHF,
    CNY,
    CZK,
    DKK,
    GBP,
    HKD,
    HRK,
    HUF,
    IDR,
    ILS,
    INR,
    ISK,
    JPY,
    KRW,
    MXN,
    MYR,
    NOK,
    NZD,
    PHP,
    PLN,
    RON,
    RUB,
    SEK,
    SGD,
    THB,
    TRY,
    ZAR,
    TWD
  )

  lazy val currencyCodeMapName: Map[CurrencyCode, Seq[String]] = ALL.map(r => r -> Seq(r.code)).toMap

  implicit val resolver: BasicResolver[CurrencyCode] = new BasicResolver[CurrencyCode](currencyCodeMapName, caseSensitive = true)
  implicit val codeFormat: Format[CurrencyCode] = JsonFormats.enumFormat[CurrencyCode](resolver)

}
