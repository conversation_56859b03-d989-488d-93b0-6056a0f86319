package de.fellows.utils.communication

import com.lightbend.lagom.scaladsl.api.transport.TransportException
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.service.InvalidClientPathParameterException
import play.api.http.DefaultHttpErrorHandler
import play.api.mvc.Results._
import play.api.mvc._
import play.api.routing.Router
import play.api.{ Configuration, Environment }
import play.core.SourceMapper

import java.lang.reflect.InvocationTargetException
import scala.concurrent._

class StackrateErrorHandler(
    environment: Environment,
    configuration: Configuration,
    sourceMapper: Option[SourceMapper],
    router: => Option[Router]
) extends DefaultHttpErrorHandler(environment, configuration, sourceMapper, router) with StackrateLogging {

  override def onClientError(request: RequestHeader, statusCode: Int, message: String): Future[Result] =
    super.onClientError(request, statusCode, message)

  override def onServerError(request: RequestHeader, exception: Throwable): Future[Result] =
    exception match {
      case ServiceException(code, message, cause) =>
        Future.successful(Status(code.transportError.http)(s"${message.name}: ${message.detail}"))
      case x: TransportException =>
        Future.successful(Status(x.errorCode.http)(s"${x.exceptionMessage.name}: ${x.exceptionMessage.detail}"))
      case x: InvocationTargetException => onServerError(request, x.getTargetException)
      case x: InvalidClientPathParameterException =>
        Future.successful(Status(x.errorCode.http)(x.getMessage))
      case x =>
        logger.error("unhandled exception", x)
        super.onServerError(request, exception)
    }
}
