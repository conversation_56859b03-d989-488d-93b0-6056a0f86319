fellows.services.seedip = "********"
fellows.services.seed = "http://"${fellows.services.seedip}

lagom.services {
  security = [
    "http://********:"${fellows.services.security.http},
  ]
  user = ["http://********:"${fellows.services.user.http}]
  assembly = ["http://********:"${fellows.services.assembly.http}]
  camunda-bridge = ["http://********:"${fellows.services.camunda-bridge.http}]
  camunda = ${fellows.services.camunda-bridge.endpoint}":8080/rest"
  notification = ["http://********:"${fellows.services.notification.http}]
  profile = ["http://********:"${fellows.services.profile.http}]
  renderer = [
    //    "http://********:"${fellows.services.renderer.http},
    "http://********:"${fellows.services.renderer.http}
  ]
  pcb = ["http://********:"${fellows.services.pcb.http}]
  pcb-supplier = ["http://********:"${fellows.services.pcb-supplier.http}]
  quotation = ["http://********:"${fellows.services.quotation.http}]
  dfm = ["http://********:"${fellows.services.dfm.http}]
  customer = ["http://********:"${fellows.services.customer.http}]
  inbox = ["http://********:"${fellows.services.inbox.http}]
  layerstack = ["http://********:"${fellows.services.layerstack.http}]
  erpnext-bridge = ["http://********:"${fellows.services.erpnext-bridge.http}]
  panel = ["http://********:"${fellows.services.panel.http}]
  price = ["http://********:"${fellows.services.price.http}]
  analysis = ["http://********:"${fellows.services.analysis.http}]
  collab = ["http://********:"${fellows.services.collab.http}]

  converter = ["http://********:"${fellows.services.converter.http}]
}

akka.discovery.config.services {
  security = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.security.remoting}
    }]
  }
  user = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.user.remoting}
    }]
  }
  assembly = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.assembly.remoting}
    }]
  }
  camunda-bridge = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.camunda-bridge.remoting}
    }]
  }
  notification = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.notification.remoting}
    }]
  }
  profile = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.profile.remoting}
    }]
  }
  renderer = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.renderer.remoting}
    }, {
      host = "http://********"
      port = ${fellows.services.renderer.remoting}
    }]
  }
  pcb = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.pcb.remoting}
    }]
  }
  pcb-supplier = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.pcb-supplier.remoting}
    }]
  }
  quotation = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.quotation.remoting}
    }]
  }
  dfm = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.dfm.remoting}
    }]
  }
  customer = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.customer.remoting}
    }]
  }
  inbox = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.inbox.remoting}
    }]
  }
  layerstack = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.layerstack.remoting}
    }]
  }
  erpnext-bridge = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.erpnext-bridge.remoting}
    }]
  }
  panel = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.panel.remoting}
    }]
  }
  price = {
    endpoints = [{
      host = "http://********"
      port = ${fellows.services.price.remoting}
    }]
  }
}

fellows.services {
  security {
    seed = "********"
    remoting = 2552
    management = 8558
    http = 9000
  }
  user {
    seed = "********"
    remoting = 2553
    management = 8559
    http = 9001
  }
  assembly {
    seed = "********"
    remoting = 2554
    management = 8560
    http = 9002
  }
  camunda-bridge {
    seed = "********"
    token = "3aee1012-7e61-4c07-8978-1d526915e33b"
    teamtoken.online = "9d6c0927-8ea6-4234-91ba-d2460eec608f"
    teamtoken.demo = "3aee1012-7e61-4c07-8978-1d526915e33b"
    teamtoken.jmeter = "81d8858b-5421-4393-b3a8-1dbc11ddac0e"
    endpoint = "http://********"
    remoting = 2555
    management = 8561
    http = 9003
  }
  notification {
    seed = "********"
    remoting = 2556
    management = 8562
    http = 9004
  }
  profile {
    seed = "********"
    remoting = 2557
    management = 8563
    http = 9005
  }

  renderer {
    seed = "********"
    remoting = 2558
    management = 8564
    http = 9006
  }
  pcb {
    seed = "********"
    remoting = 2559
    management = 8565
    http = 9007
  }
  pcb-supplier {
    seed = "********"
    remoting = 2560
    management = 8566
    http = 9008
  }
  quotation {
    seed = "********"
    remoting = 2561
    management = 8567
    http = 9009
  }
  dfm {
    seed = "********"
    remoting = 2562
    management = 8568
    http = 9010
  }
  customer {
    seed = "********"
    remoting = 2563
    management = 8569
    http = 9011
  }
  inbox {
    seed = "********"
    remoting = 2564
    management = 8570
    http = 9012
  }
  layerstack {
    seed = "********"
    remoting = 2565
    management = 8571
    http = 9013
  }
  erpnext-bridge {
    endpoint = "http://********"
    secret = "0b2d550cf3335c0"
    token = "bd3baef58ba4094"
    remoting = 2566
    management = 8572
    http = 9014
  }
  panel {
    seed = "********"
    remoting = 2567
    management = 8573
    http = 9015
  }
  price {
    seed = "********"
    remoting = 2568
    management = 8574
    http = 9016
    endpoint {
      demo = "http://********:8080/rest"
      richter = "http://10.0.0.4:8081/engine-rest"
    }
  }

  analysis {
    seed = "********"
    remoting = 2569
    management = 8575
    http = 9017
  }

  converter {
    seed = "********"
    remoting = 2570
    management = 8576
    http = 9018
  }
  collab {
    seed = "********"
    remoting = 2571
    management = 8577
    http = 9019
  }
}


//akka {
//  remote {
//    netty.tcp {
//      port = 2553
//      bind-port = 2553
//    }
//  }
//  management.http.port = 8559
//}
//
//play.server.http.port = 9003
