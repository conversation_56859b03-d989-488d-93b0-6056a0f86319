package de.fellows.utils.mailgun

import akka.http.scaladsl.model.{ContentType, MediaType, MediaTypes}
import akka.stream.scaladsl.{FileIO, Source}
import akka.util.ByteString
import com.lightbend.lagom.scaladsl.api.ServiceLocator
import com.typesafe.config.ConfigFactory
import de.fellows.utils.internal.FileReader.withResource
import de.fellows.utils.{FilePath, FilePathUtils, PathUtils}
import play.api.libs.json.{JsValue, Json}
import play.api.libs.ws.{WSAuthScheme, WSClient, WSResponse}
import play.api.mvc.MultipartFormData.{DataPart, FilePart}

import java.io.ByteArrayOutputStream
import scala.concurrent.{ExecutionContext, Future}

case class GenericMailDescriptor(
    subject: String,
    text: Seq[String],
    strongtext: Seq[String],
    button: String,
    buttonlink: String,
    templatelanguage: String
)

object Mailgun {
  val conf     = ConfigFactory.load()
  val confPath = "lagom.services.mailgun"

  //            domain: String,
  //            from: String,
  //            to: String,
  //            cc: Option[String] = None,
  //            bcc: Option[String] = None,
  //            subject: Option[String] = None,
  //            text: Option[String] = None,
  //            html: Option[String] = None,
  //            attachment: Option[Seq[String]] = None,
  //            inline: Option[String] = None,
  // formData.add("", "{"test": "test"}");

  def sendGenericEmail(
      to: Seq[String],
      name: String,
      desc: GenericMailDescriptor,
      cc: Option[Seq[String]] = None,
      bcc: Option[Seq[String]] = None,
      attachments: Option[Seq[FilePath]] = None,
      binaryAttachments: Option[Seq[(String, ContentType, Array[Byte])]] = None
  )(implicit ws: WSClient, sl: ServiceLocator, ec: ExecutionContext): Future[WSResponse] =
    sendFormData(
      domain = Mailgun.domain,
      from = s"${Mailgun.fromName} <${Mailgun.fromUser}@${Mailgun.domain}>",
      to = to,
      cc = cc,
      bcc = bcc,
      template = Some(s"generic_stackrate_template_${desc.templatelanguage}"),
      mailgunVariables = Some(Json.toJson(Map(
        "name"       -> Json.toJson(name),
        "text"       -> Json.toJson(desc.text.map(s => Map("line" -> s))),
        "strongtext" -> Json.toJson(desc.strongtext.map(s => Map("line" -> s))),
        "buttonLink" -> Json.toJson(desc.buttonlink),
        "buttonText" -> Json.toJson(desc.button)
      ))),
      subject = Some(desc.subject),
      inline = Some(getInlineImages("/images/stackrate.png", "stackrate.png")),
      attachments = attachments,
      binaryAttachments = binaryAttachments
    )

  private def readResource(resourcePath: String): Array[Byte] =
    withResource(new ByteArrayOutputStream()) { bos =>
      withResource(getClass.getResourceAsStream(resourcePath)) { is =>
        Iterator
          .continually(is.read)
          .takeWhile(-1 !=)
          .foreach(bos.write)

        bos.toByteArray
      }
    }

  private def getInlineImages(path: String, name: String, ctype: MediaType = MediaTypes.`image/png`) = {
    val srImg = readResource(path)

    Seq(
      (name, ContentType.apply(MediaTypes.`image/png`), srImg)
    )
  }

  def sendFormData(
      domain: String,
      from: String,
      to: Seq[String],
      cc: Option[Seq[String]] = None,
      bcc: Option[Seq[String]] = None,
      subject: Option[String] = None,
      text: Option[String] = None,
      html: Option[String] = None,
      template: Option[String] = None,
      mailgunVariables: Option[JsValue] = None,
      attachments: Option[Seq[FilePath]] = None,
      inline: Option[Seq[(String, ContentType, Array[Byte])]] = None,
      binaryAttachments: Option[Seq[(String, ContentType, Array[Byte])]] = None
  )(implicit ws: WSClient, sl: ServiceLocator, ec: ExecutionContext): Future[WSResponse] = {
    val endpoint =
      if (conf.hasPath(confPath)) {
        Future.successful(conf.getString(confPath))
      } else {
        sl.locate("mailgun").map(_.map(_.toString)).map {
          case Some(o) => o
          case None    => throw new IllegalStateException("Endpoint not found")
        }
      }

    import de.fellows.utils.FilePathUtils._

    endpoint.flatMap { url =>
      val curl    = pathed(url)
      val cdomain = pathed(domain)
      val ep      = s"$curl/v3/$cdomain/messages"
      println(s"Send mail to $ep to $to")
      ws.url(ep)
        .withAuth("api", conf.getString("mailgun.key"), WSAuthScheme.BASIC)
        //        .addHttpHeaders(("Authorization", "Basic " + Base64.getEncoder.encodeToString(s"api:${conf.getString("mailgun.key")}".getBytes)))
        .addHttpHeaders(("Accept", "application/json"))
        .post(
          Source({
            List(
              Some(DataPart("from", from)),
              Some(DataPart("to", to.mkString(", "))),
              cc.map(ccc => DataPart("cc", ccc.mkString(", "))),
              bcc.map(bccc => DataPart("bcc", bccc.mkString(", "))),
              subject.map(s => DataPart("subject", s)),
              text.map(s => DataPart("text", s)),
              html.map(h => DataPart("html", h)),
              template.map(h => DataPart("template", h)),
              mailgunVariables.map(h => DataPart("h:X-Mailgun-Variables", h.toString()))
            ).flatten ++
              attachments.map { atts =>
                atts.map { att =>
                  val file = att.toJavaFile
                  FilePart(
                    "attachment",
                    file.getName,
                    PathUtils.probeContentType(file.toPath).toOption.flatten,
                    FileIO.fromPath(file.toPath)
                  )
                }
              }.getOrElse(Seq()) ++
              binaryAttachments.map { atts =>
                atts.map { att =>
                  FilePart(
                    "attachment",
                    att._1,
                    Option(att._2.mediaType.toString()),
                    Source.fromFuture(Future.successful(ByteString.fromArray(att._3)))
                  )
                }
              }.getOrElse(Seq()) ++
              inline.map { i =>
                i.map { inline =>
                  FilePart(
                    "inline",
                    inline._1,
                    Option(inline._2.mediaType.toString()),
                    Source.fromFuture(Future.successful(ByteString.fromArray(inline._3)))
                  )
                }
              }.getOrElse(Seq())
          }.map { x =>
            println(s"Field $x")
            x
          })
        )
    }

  }

  def domain =
    conf.getString("mailgun.domain")

  def fromName =
    conf.getString("mailgun.fromname")

  def fromUser =
    conf.getString("mailgun.fromuser")

}
