package de.fellows.utils.mailgun

import java.util.concurrent.Executors

import akka.actor.{ActorSystem, Cancellable}
import akka.stream.{ActorMaterializerSettings, Attributes, ClosedShape, Graph, Materializer}
import com.lightbend.lagom.scaladsl.api.ServiceLocator
import com.typesafe.config.ConfigFactory
import play.api.libs.ws.ahc.{AhcWSClient, StandaloneAhcWSClient}
import play.shaded.ahc.org.asynchttpclient.DefaultAsyncHttpClient

import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{Await, ExecutionContext, ExecutionContextExecutor}
import scala.concurrent.duration._

object MailgunTest extends App {
//  implicit val mat = NoMaterializer
//  implicit val ws = new AhcWSClient(new StandaloneAhcWSClient(new DefaultAsyncHttpClient()))
//  implicit val sl: ServiceLocator = null
//
//  val excs = Executors.newCachedThreadPool()
//  implicit val exc = ExecutionContext.fromExecutor(excs)
//
//  println(ConfigFactory.load())
//  Await.result(
//    Mailgun.sendFormData("mail.electronic-fellows.de", "Test Ing <<EMAIL>>", Seq("<EMAIL>"))
//    , 10 seconds)
}


//object NoMaterializer extends Materializer {
//  override def withNamePrefix(name: String): Materializer =
//    throw new UnsupportedOperationException("NoMaterializer cannot be named")
//
//  override def materialize[Mat](runnable: Graph[ClosedShape, Mat]): Mat =
//    throw new UnsupportedOperationException(s"NoMaterializer cannot materialize $runnable")
//
//  override def materialize[Mat](runnable: Graph[ClosedShape, Mat], initialAttributes: Attributes): Mat =
//    throw new UnsupportedOperationException("NoMaterializer cannot materialize")
//
//  override def executionContext: ExecutionContextExecutor =
//    throw new UnsupportedOperationException("NoMaterializer does not provide an ExecutionContext")
//
//  def scheduleOnce(delay: FiniteDuration, task: Runnable): Cancellable =
//    throw new UnsupportedOperationException("NoMaterializer cannot schedule a single event")
//
//  def schedulePeriodically(initialDelay: FiniteDuration, interval: FiniteDuration, task: Runnable): Cancellable =
//    throw new UnsupportedOperationException("NoMaterializer cannot schedule a repeated event")
//
//  override def scheduleWithFixedDelay(initialDelay: FiniteDuration, delay: FiniteDuration, task: Runnable): Cancellable = ???
//
//  override def scheduleAtFixedRate(initialDelay: FiniteDuration, interval: FiniteDuration, task: Runnable): Cancellable = ???
//
//  override def shutdown(): Unit = ???
//
//  override def isShutdown: Boolean = ???
//
//  override def system: ActorSystem = ???
//
//  override def settings: ActorMaterializerSettings = ???
//}
