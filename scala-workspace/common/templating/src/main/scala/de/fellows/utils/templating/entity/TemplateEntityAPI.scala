//package de.fellows.utils.templating.entity
//
//import akka.actor.typed.ActorRef
//import com.lightbend.lagom.scaladsl.persistence.{AggregateEvent, AggregateEventShards, AggregateEventTag}
//import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
//import play.api.libs.json.{Format, Json}
//
//object TemplateEntityAPI {
//
//  sealed trait TemplateEntityCommand
//
//  sealed trait TemplateEntityEvent extends AggregateEvent[TemplateEntityEvent] {
//    override def aggregateTag: AggregateEventShards[TemplateEntityEvent] = TemplateEntityEvent.Tag
//  }
//
//  object TemplateEntityEvent {
//    val NumShards = 4
//    val Tag = AggregateEventTag.sharded[TemplateEntityEvent](NumShards)
//  }
//
//
//  final case class GetTemplate(replyTo: ActorRef[Template]) extends TemplateEntityCommand
//
//  final case class SetTemplate(template: Template, replyTo: ActorRef[Template]) extends TemplateEntityCommand
//
//  //
//
//  final case class TemplateChanged(template: Template) extends TemplateEntityEvent
//
//
//  object GetTemplate {
//    implicit val f: Format[GetTemplate] = Json.format
//  }
//
//  object SetTemplate {
//    implicit val f: Format[SetTemplate] = Json.format
//  }
//
//  object TemplateChanged {
//    implicit val f: Format[TemplateChanged] = Json.format
//  }
//
//
//  object ShoppingCartSerializerRegistry extends JsonSerializerRegistry {
//    override def serializers: Seq[JsonSerializer[_]] = Seq(
//      JsonSerializer[GetTemplate],
//      JsonSerializer[SetTemplate],
//      JsonSerializer[TemplateChanged]
//    )
//  }
//
//}
