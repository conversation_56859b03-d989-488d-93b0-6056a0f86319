package de.fellows.utils.templating

import com.hubspot.jinjava.lib.filter.Filter
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder
import com.openhtmltopdf.util.XRLog
import org.jsoup.helper.W3CDom
import play.api.Logging
import play.api.libs.json.JsValue
import org.jsoup.Jsoup
import org.jsoup.nodes.Document

import java.io.{File, InputStream}
import java.nio.file.{Files, Path, Paths}
import java.util.UUID

class PDFGenerator extends TemplateGenerator[Array[Byte]] with Logging {
  val underlying                     = new HTMLGenerator()
  var resources: Seq[Path]           = Seq()
  var workingDirectory: Option[Path] = None
  var debug: Boolean                 = false
  var header: Option[String]         = None
  var footer: Option[String]         = None

  override def withLocalTemplate(resource: String): PDFGenerator = {
    underlying.withLocalTemplate(resource)
    this
  }

  override def withTemplate(resource: Path): PDFGenerator = {
    underlying.withTemplate(resource)
    this
  }

  override def withTemplate(resource: String): PDFGenerator = {
    underlying.withTemplate(resource)
    this
  }

  def withHeader(header: Option[String]): PDFGenerator = {
    this.header = header
    this
  }

  def withFooter(footer: Option[String]): PDFGenerator = {
    this.footer = footer
    this
  }

  override def withWorkingDirectory(base: Path): PDFGenerator = {
    this.workingDirectory = Some(base)
    this.underlying.withWorkingDirectory(base)
    this
  }

  override def withVariable(name: String, value: Any): PDFGenerator = {
    underlying.withVariable(name, value)
    this
  }

  override def withJsonVariable(name: String, value: JsValue): PDFGenerator = {
    underlying.withJsonVariable(name, value)
    this
  }

  override def withVariables(vars: Map[String, Any]): PDFGenerator = {
    underlying.withVariables(vars)
    if (debug) {
      logger.info(s"template variables ${vars}")
    }
    this
  }

  override def withResources(res: Seq[Path]): PDFGenerator = {
    import scala.sys.process._
    logger.info(s"with resource ${res.map(_.getFileName.toString).mkString(", ")}")

    val allres = res.flatMap { p =>
      if (p.getFileName.toString.endsWith(".webp")) {
        val webp    = p.toAbsolutePath.toString
        val png     = webp.substring(0, webp.length - 4) + "png"
        val pngPath = Paths.get(png)
        if (!Files.exists(pngPath)) {
          s"dwebp '${webp}' -o '${png}' " !
        }
        Seq(p, pngPath)
      } else {
        Seq(p)
      }
    }

    underlying.withResources(allres)
    this.resources = allres
    this
  }

  override def withImportableResource(name: String, content: String): PDFGenerator = {
    underlying.withImportableResource(name, content);
    this
  }

  override def withResource(name: String, res: InputStream): PDFGenerator = {

    val workingDir = workingDirectory.get
    val bytes      = LazyList.continually(res.read).takeWhile(_ != -1).map(_.toByte).toArray
    Files.write(workingDir.resolve(name), bytes)

    this
  }

  override def withFilter(filter: Filter): PDFGenerator = {
    this.underlying.withFilter(filter)
    this
  }

  override def withDebug(debug: Boolean): PDFGenerator = {
    this.debug = debug;
    this.underlying.withDebug(debug)
    this
  }

  def simpleBuild(stylesBaseUri: String): Option[File] =
    underlying.build().map { html =>
      val file = Files.createTempFile("doc", ".pdf")
      val os   = Files.newOutputStream(file)
      XRLog.setLoggingEnabled(false);
      val builder = new PdfRendererBuilder()
      builder.toStream(os)
      val document = Jsoup.parse(html, "UTF-8")
      document.outputSettings.syntax(Document.OutputSettings.Syntax.xml)

      builder.withW3cDocument(new W3CDom().fromJsoup(document), stylesBaseUri).run()
      os.close()
      file.toFile
    }

  override def build(): Option[Array[Byte]] = {
    import scala.jdk.CollectionConverters._
    val (wd, isTemp) =
      if (workingDirectory.isEmpty) {
        (Files.createTempDirectory("templating"), true)
      } else {
        (workingDirectory.get, false)
      }

    underlying.build().map { html =>
      val convId = UUID.randomUUID()
      val f      = wd.resolve(s"convert-$convId.html")

      try {
        Files.write(f, Seq(html).asJava)

        val headerP = this.header.map { h =>
          val path = wd.resolve(s"convert-${convId}-header.html")
          Files.write(path, Seq(h).asJava)
          path
        }
        val footerP = this.footer.map { h =>
          val path = wd.resolve(s"convert-${convId}-footer.html")
          Files.write(path, Seq(h).asJava)
          path
        }

        if (debug) {
          logger.info(s"with resources ${this.resources.map(_.getFileName).mkString(",")}")
        }
        HTMLtoPDF.convert(
          html = f,
          workingDir = workingDirectory.getOrElse(throw new IllegalArgumentException("No working directory set")),
          resources = this.resources,
          deleteWorkingDir = !debug,
          header = headerP,
          footer = footerP
        )
      } finally
        if (!debug) {
          f.toFile.delete()
          if (isTemp) {
            wd.toFile.listFiles().foreach { f =>
              if (f.exists()) {
                f.delete()
              }
            }
            if (wd.toFile.exists()) {
              wd.toFile.delete()
            }
          }
        }

    }

  }
}
