package de.fellows.utils.templating

import play.api.libs.json._

import scala.jdk.CollectionConverters._

object JsonConverter {
  def value(v: JsValue): Any = {
    v match {
      case JsArray(a) => a.map(x => value(x)).asJava
      case JsObject(o) => o.map(e => (e._1 -> value(e._2))).asJava
      case JsNumber(v) => v
      case JsString(s) => s
      case JsBoolean(b) => b
      case _ => null
    }
  }

}
