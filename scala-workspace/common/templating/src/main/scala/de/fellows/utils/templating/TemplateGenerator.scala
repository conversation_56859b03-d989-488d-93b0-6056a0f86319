package de.fellows.utils.templating

import com.hubspot.jinjava.lib.filter.Filter
import play.api.libs.json.JsValue

import java.io.InputStream
import java.nio.file.Path

trait TemplateGenerator[T] {
  def withLocalTemplate(resource: String): TemplateGenerator[T]

  def withTemplate(resource: Path): TemplateGenerator[T]

  def withTemplate(resource: String): TemplateGenerator[T]

  def withWorkingDirectory(resource: Path): TemplateGenerator[T]

  def withVariable(name: String, value: Any): TemplateGenerator[T]

  def withJsonVariable(name: String, value: JsValue): TemplateGenerator[T]

  def withVariables(vars: Map[String, Any]): TemplateGenerator[T]

  def withResources(res: Seq[Path]): TemplateGenerator[T]

  def withImportableResource(name: String, content: String): TemplateGenerator[T]

  def withResource(name: String, res: InputStream): TemplateGenerator[T]

  def withFilter(filter: Filter): TemplateGenerator[T]

  def withDebug(debug: Boolean): TemplateGenerator[T]

  def build(): Option[T]
}
