package de.fellows.utils.templating.filter

import java.time.{Instant, ZoneId}

import com.hubspot.jinjava.interpret.JinjavaInterpreter
import com.hubspot.jinjava.lib.filter.Filter

class InstantFilter extends Filter {

  override def filter(vo: Any, interpreter: <PERSON>javaInterpreter, args: String*): AnyRef = {
    Option(vo).map(vv => {
      Instant.parse(vv.toString).atZone(ZoneId.systemDefault())
    }).orNull
  }

  override def getName: String = "parseinstant"
}
