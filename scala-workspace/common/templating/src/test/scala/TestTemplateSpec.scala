import de.fellows.utils.templating.{HTMLGenerator, JsonConverter, PDFGenerator, TemplateGenerator}
import org.scalatest.BeforeAndAfterAll
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec
import play.api.libs.json.{JsObject, JsString}

import java.nio.file.{Files, Path, Paths, StandardCopyOption}
import scala.jdk.CollectionConverters._

class TestTemplateSpec extends AsyncWordSpec with BeforeAndAfterAll with Matchers {

  private val resourcesPath = Paths.get(getClass.getResource("/").toURI)

  override def beforeAll(): Unit = {
    // Create necessary directories if they don't exist
    val templateDir = resourcesPath.resolve("templates/default/test")
    Files.createDirectories(templateDir)
  }

  "templating" should {
    "flo" in {
      val gen        = new PDFGenerator()
      val sourcePath = resourcesPath.resolve("schweizer/schweizerProposal.html")
      val targetDir  = Files.createTempDirectory("template")

      // Only try to copy files if the source directory exists and has files
      val sourceDir = sourcePath.getParent
      if (Files.exists(sourceDir) && Files.isDirectory(sourceDir)) {
        Files.list(sourceDir).forEach { file =>
          Files.copy(file, targetDir.resolve(file.getFileName), StandardCopyOption.REPLACE_EXISTING)
        }
      }

      val file = gen
        .withTemplate(sourcePath)
        .withWorkingDirectory(targetDir)
        .simpleBuild("")

      file match {
        case Some(_) => assert(true)
        case None    => assert(false)
      }
    }

    "simple" in {
      val res = simpleGen(new HTMLGenerator()).build()
      res match {
        case Some(_) => assert(true)
        case None    => assert(false)
      }
    }

    "pdf" in {
      val res = simpleGen(new PDFGenerator()).asInstanceOf[PDFGenerator].simpleBuild("")
      res match {
        case Some(_) => assert(true)
        case None    => assert(false)
      }
    }

    "quotation" in {
      val res = quotationGen(new HTMLGenerator()).build()
      res match {
        case Some(_) => assert(true)
        case None    => assert(false)
      }
    }
  }

  private def quotationGen[T](gen: TemplateGenerator[T]) = {
    val quotation = JsObject.apply(Seq(
      ("team" -> JsString("demo")),
      ("billing" -> JsObject.apply(Seq(
        "alias"   -> JsString("Sam"),
        "street"  -> JsString("Asternweg"),
        "no"      -> JsString("3"),
        "city"    -> JsString("Wiesbaden"),
        "postal"  -> JsString("65201"),
        "name"    -> JsString("Samuel Leisering"),
        "country" -> JsString("Germany")
      )))
    ))

    val sourcePath = resourcesPath.resolve("templates/default/test/quotation.html")
    val targetDir  = Files.createTempDirectory("template")

    gen
      .withTemplate(sourcePath)
      .withWorkingDirectory(targetDir)
      .withVariable("name", "Flo")
      .withJsonVariable("quotation", quotation)
  }

  private def simpleGen[T](gen: TemplateGenerator[T]) = {
    val sourcePath = resourcesPath.resolve("templates/default/test/quotation.html")
    val targetDir  = Files.createTempDirectory("template")
    gen
      .withTemplate(sourcePath)
      .withWorkingDirectory(targetDir)
      .withVariable("name", "Flo")
      .withVariable(
        "moreNames",
        Seq(
          Map(
            ("first" -> "1"),
            ("last"  -> "2")
          ).asJava,
          Map(
            ("first" -> "A"),
            ("last"  -> "B")
          ).asJava
        ).asJava
      )
  }
}
