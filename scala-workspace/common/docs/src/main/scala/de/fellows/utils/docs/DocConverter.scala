package de.fellows.utils.docs

import java.io.{File, FileInputStream, FileOutputStream}
import java.nio.file.{Path, Paths}
import java.util.concurrent.Executors

import fr.opensagres.poi.xwpf.converter.pdf.{PdfConverter, PdfOptions}
import org.apache.poi.hwpf.converter.{AbstractWordUtils, WordToHtmlConverter}
import org.apache.poi.util.XMLHelper
import org.apache.poi.xwpf.usermodel.XWPFDocument
import org.apache.tika.extractor.EmbeddedDocumentExtractor
import org.apache.tika.metadata.Metadata
import org.apache.tika.parser.jpeg.JpegParser
import org.apache.tika.parser.{AutoDetectParser, ParseContext, Parser}
import org.apache.tika.sax.{BodyContentHandler, ToXMLContentHandler}

import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.jdk.CollectionConverters._

object DocConverter {

  def getDoc(docFile: File) = {
    val docBuild = XMLHelper.getDocumentBuilderFactory.newDocumentBuilder
    try {
      val wordDocument = AbstractWordUtils.loadDoc(docFile)
      try {
        val wordToHtmlConverter = new WordToHtmlConverter(docBuild.newDocument)
        wordToHtmlConverter.processDocument(wordDocument)
        wordToHtmlConverter.getDocument
      } finally if (wordDocument != null) wordDocument.close()
    }
  }

  def convertHWPF(p: Path, to: Path)(implicit ctx: ExecutionContext) = {
    Future{
      val handler = new ToXMLContentHandler
      val context = new ParseContext()
      val parser = new AutoDetectParser
      val metadata = new Metadata()

      val rw = new ImageRewritingContentHandler(handler)
      var stream = new FileInputStream(p.toFile)
      context.set(classOf[Parser], parser)
      context.set(classOf[EmbeddedDocumentExtractor], new FileEmbeddedDocumentEtractor)

      parser.parse(stream, rw, metadata, context)
      stream.close()
      val s = rw.toString
      println(metadata.names().toSeq)
      println(s)
      s
    }


  }

  def convertXWPF(p: Path, to: Path)(implicit ctx: ExecutionContext) = {
    Future{
      println("start")
      val in = new FileInputStream(p.toFile)
      val out = new FileOutputStream(to.toFile)
      try {
        val document = new XWPFDocument(in)
        val opts = PdfOptions.create()
        PdfConverter.getInstance.convert(document, out, opts)
      } finally {
        in.close()
        out.close()
      }
      println("end")
    }
  }
}


object Test extends App {
  private val ex = Executors.newCachedThreadPool()
  implicit val t = ExecutionContext.fromExecutor(ex)
  val f = DocConverter.convertXWPF(Paths.get("/home/<USER>/Desktop/Meilenst03_Ergebnisdarstellung.docx"), Paths.get("/tmp/jo.pdf"))
  val f2 = DocConverter.convertHWPF(Paths.get("/home/<USER>/Desktop/PCB-Specification-5413-0.doc"), Paths.get("/tmp/jo2.pdf"))

  Await.ready(Future.sequence(Seq(f, f2)).recover{
    case e: Throwable => e.printStackTrace()
  }, 10 seconds)

  ex.shutdown()
}
