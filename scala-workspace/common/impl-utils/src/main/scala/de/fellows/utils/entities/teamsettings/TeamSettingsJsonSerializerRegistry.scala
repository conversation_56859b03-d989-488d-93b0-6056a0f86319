package de.fellows.utils.entities.teamsettings

import com.lightbend.lagom.scaladsl.playjson.{JsonSerializer, JsonSerializerRegistry}
import play.api.libs.json._

object TeamSettingsJsonSerializerRegistry extends JsonSerializerRegistry {
  override def serializers =
    List(
      JsonSerializer[TeamSettings],
      JsonSerializer[SetSetting],
      JsonSerializer[GetSettings],
      JsonSerializer[SettingsChanged],


      JsonSerializer[JsValue],
      JsonSerializer[JsArray],
      JsonSerializer[JsObject],
      JsonSerializer[JsNumber],
      JsonSerializer[JsString],
      JsonSerializer[JsBoolean],
    )
}
