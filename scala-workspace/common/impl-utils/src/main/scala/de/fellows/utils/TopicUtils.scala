package de.fellows.utils

import akka.Done
import akka.stream.scaladsl.Flow
import com.lightbend.lagom.scaladsl.api.broker
import com.lightbend.lagom.scaladsl.api.broker.{Message, Subscriber, Topic}
import com.lightbend.lagom.scaladsl.broker.kafka.KafkaMetadataKeys
import com.typesafe.config.ConfigFactory
import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.streams.PayloadName
import de.fellows.utils.telemetry.KamonUtils
import kamon.Kamon

import scala.concurrent.{ExecutionContext, Future}

object TopicUtils extends StackrateLogging {
  implicit def defaultNaming[T]: PayloadName[T] = new PayloadName[T] {
    override def name(o: T): String = o.getClass.getSimpleName
  }

  private def maintenance(handler: => Future[Done])(implicit ec: ExecutionContext) = {
    val initialization: Boolean = isInit
    if (!initialization) {
      handler
    } else {
      // do not subscribe, so that the topics have a chance to initialize
      logger.error("Maintenance Mode, skip topic subscription")
      Future.successful(Done)
    }
  }

  /** subscribes to a topic
    * @return
    */
  def subscribe[T](
      topic: Topic[T],
      parallelism: Int = 1
  )(handler: PartialFunction[broker.Message[T], Future[Done]])(implicit
      ec: ExecutionContext,
      naming: PayloadName[T]
  ): Future[Done] =
    maintenance {
      val subscriber: Subscriber[T] = topic.subscribe
      logger.info(s"subscribe to topic: ${topic.topicId.name}")
      subscriber.withMetadata.atLeastOnce(
        Flow[Message[T]].mapAsync(parallelism) { msg =>
          try
            if (handler.isDefinedAt(msg)) {
              KamonUtils.runWithKakfaContext(topic, msg) {
                handler(msg)
              }
            } else {
              Future.successful(Done)
            }
          catch {
            case e: Exception =>
              logger.error(s"Error while processing message $msg", e)
              throw e
          }

        }
      )
    }

  /** Subscribe to a topic starting at a given offset. Will irrevocably discard unhandled messages before that offset.
    */
  def subscribeLatest[T](
      topic: Topic[T],
      started: Long,
      parallelism: Int = 1
  )(handler: PartialFunction[broker.Message[T], Future[Done]])(implicit
      ec: ExecutionContext,
      naming: PayloadName[T]
  ): Future[Done] =
    maintenance {
      val subscriber: Subscriber[T] = topic.subscribe
      logger.info(s"subscribe to topic: ${topic.topicId.name}")
      subscriber.withMetadata.atLeastOnce(
        Flow[Message[T]].mapAsync(parallelism) { msg =>
          try {
            val start   = msg.get(KafkaMetadataKeys.Timestamp)
            val expired = start.map(x => x < started)
            if (expired.contains(true)) {
              logger.info(s"skipped message $msg")
              Future.successful(Done)
            } else {
              logger.debug(s"handle message ${msg.payload}")
              if (handler.isDefinedAt(msg)) {
                KamonUtils.runWithKakfaContext(topic, msg) {
                  handler(msg)
                }
              } else {
                Future.successful(Done)
              }
            }
          } catch {
            case e: Exception =>
              logger.error(s"Error while processing message $msg", e)
              throw e
          }

        }
      )
    }

  private def isInit = {
    val conf = ConfigFactory.load()
    val initialization =
      if (conf.hasPath("fellows.topic-initialization")) {
        conf.getBoolean("fellows.topic-initialization")
      } else {
        false
      }
    initialization
  }
}
