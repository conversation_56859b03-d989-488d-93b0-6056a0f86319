package de.fellows.utils.telemetry

import com.lightbend.lagom.scaladsl.api.broker.{Message, MetadataKey, Topic}
import de.fellows.utils.streams.PayloadName
import kamon.Kamon
import kamon.instrumentation.kafka.client.SpanPropagation
import kamon.trace.Span
import kamon.trace.Span.Kind.Consumer
import kamon.trace.Span.TagKeys
import org.apache.kafka.common.header.internals.RecordHeaders
import play.api.Logging

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

object KamonUtils extends Logging {

  /** catches all errors and failed futures and reports to the current span
    *
    * Returns a guaranteed successful Future containing a [[ Try ]] with the original error if applicable
    * @param f
    * @param exc
    * @tparam T
    * @return
    */
  def safe[T](f: => Future[T])(implicit exc: ExecutionContext): Future[Try[T]] = {

    def handle(e: Throwable) =
      Kamon.currentSpan().fail(e.getMessage, e)

    Try {
      f
        .map(Success(_))
        .recover {
          case e: Throwable =>
            handle(e)
            Failure(e)
        }
    } match {
      case Failure(exception) =>
        handle(exception)
        Future.successful(Failure(exception))

      case Success(value) => value
    }
  }

  class KamonMacros(val c: scala.reflect.macros.blackbox.Context) {
    import c.universe._
    def spanImpl[A](operation: c.Expr[String])(f: c.Expr[A]): c.Expr[A] = {
      val span = q"""
        kamon.Kamon.span($operation, getClass.getSimpleName) {
          $f
        }
      """
      c.Expr[A](span)
    }

  }

  /** Create a span with the calling class as operator
    */
  final def span[A](operation: String)(f: => A): A = macro KamonMacros.spanImpl[A]

  /** Extend the name of the parent span with the given string
    * @param str
    * @return
    */
  def parentName(str: String) =
    Kamon.currentSpan().name(s"${Kamon.currentSpan().operationName()}: ${str}")

  def withSpanAsync[T](
      operationName: String,
      component: String
  )(code: => Future[T]): Future[T] = {
    val span = Kamon
      .spanBuilder(operationName)
      .tagMetrics("span.kind", "client")
      .tagMetrics("component", component)
      .start()

    Kamon.runWithSpan(span)(code)
  }

  def runWithKakfaContext[T, M, Y](
      topic: Topic[Y],
      msg: Message[M]
  )(f: => Future[T])(implicit naming: PayloadName[M], ctx: ExecutionContext): Future[T] = {

    val currentContext  = Kamon.currentContext()
    val spanPropagation = SpanPropagation.KCtxHeader()
    val headers         = msg.get[RecordHeaders](MetadataKey("kafkaHeaders")).getOrElse(new RecordHeaders)
    val context         = spanPropagation.read(headers, currentContext)

    val payloadName = naming.name(msg.payload)

    val preSpan = Kamon.runWithContext(context) {
      val span = Kamon.spanBuilder(s"receive $payloadName")
        .kind(Span.Kind.Internal)
        .tagMetrics(Span.TagKeys.Component, "kafka.consumer")
        .start()

      Kamon.runWithSpan(span, false) {
        span
      }
    }
    Kamon.span(s"receive $payloadName", "kafka.consumer") {
      preSpan.link(Kamon.currentSpan(), Span.Link.Kind.FollowsFrom)
      preSpan.finish()
      Kamon.currentSpan().link(preSpan, Span.Link.Kind.FollowsFrom)
      Kamon.currentSpan().tag(TagKeys.SpanKind, Consumer.toString())

      Kamon.currentSpan().tag("kafka.topic", topic.topicId.name)
      Kamon.currentSpan().tag("kafka.event", payloadName)
      try
        f
          .recover {
            case e: Throwable =>
              Kamon.currentSpan().tag("kafka.fatal", true)
              Kamon.currentSpan().fail(e.getMessage, e)
              throw e
          }
      catch {
        case e: Throwable =>
          Kamon.currentSpan().tag("kafka.fatal", true)
          Kamon.currentSpan().fail(e.getMessage, e)
          throw e
      }
    }
  }

  def withConsumerSpan[T](operationName: String, component: String)(code: => T): T = {
    val span = Kamon.consumerSpanBuilder(operationName, component).start()

    Kamon.runWithSpan(span)(code)
  }

  def withSpan[T](operationName: String, component: String)(code: => T): T = {
    val span = Kamon
      .spanBuilder(operationName)
      .tagMetrics("span.kind", "client")
      .tagMetrics("component", component)
      .start()

    Kamon.runWithSpan(span)(code)
  }

  def exceptionError(ex: Throwable): Unit =
    exceptionError(ex, "Found an error")

  def exceptionError(ex: Throwable, msg: String): Unit = {
    val span = Kamon.currentSpan()
    span.fail(msg, ex)
    logger.error(s"$msg: ${ex.getMessage}")
  }

  def error(msg: String): Unit = {
    val span = Kamon.currentSpan()
    span.fail(msg)
    logger.error(msg)
  }
}
