package de.fellows.utils.internal

import com.fasterxml.jackson.databind.ObjectMapper
import de.fellows.utils.FilePath
import play.api.libs.json.jackson.PlayJsonModule
import play.api.libs.json.{JsValue, Json, JsonParserSettings, Writes}

import java.io.OutputStream
import java.nio.file.{Files, Path, StandardCopyOption}
import java.util.Objects
import java.util.zip.GZIPOutputStream
import scala.util.Using

object FileWriter {
  def writeCompressedObject[T](path: Path, obj: T)(implicit writes: Writes[T]): Path =
    writeCompressed(path, Json.toJson(obj))

  def writeCompressed(path: Path, obj: JsValue): Path =
    writeCompressed(path, Json.toBytes(obj))

  def writeCompressed(path: Path, bytes: Array[Byte]): Path = {
    // ensure bytes is not null before opening file
    Objects.requireNonNull(bytes)

    val out = new GZIPOutputStream(Files.newOutputStream(path))

    writeAndCloseBytes(bytes, out)

    path
  }

  private def write(path: Path, bytes: Array[Byte]): Path = {
    // ensure bytes is not null before opening file
    Objects.requireNonNull(bytes)

    val out = Files.newOutputStream(path)

    writeAndCloseBytes(bytes, out)

    path
  }

  private def writeAndCloseBytes(bytes: Array[Byte], out: OutputStream): Unit =
    Using.resource(out) { out =>
      val len = bytes.length
      var rem = len
      while (rem > 0) {
        val n = Math.min(rem, 8192)
        out.write(bytes, len - rem, n)
        rem -= n
      }
    }

  def write(fn: FilePath, bytes: Array[Byte], compressed: Boolean): Unit = {
    fn.toJavaFile.getParentFile.mkdirs()

    if (compressed) {
      writeCompressed(fn.toJavaPath, bytes)
    } else {
      write(fn.toJavaPath, bytes)
    }
  }

  lazy val mapper: ObjectMapper = (new ObjectMapper).registerModule(new PlayJsonModule(JsonParserSettings.settings))

  private def writeJsonByPath[G](g: G, filepath: FilePath, compressed: Boolean = true)(implicit
      x: Writes[G]
  ): FilePath = {

    val fname = filepath.toJavaPath.getFileName.toString
    val fp    = filepath.toJavaPath

    val newFileName =
      if (compressed && !fname.toLowerCase.endsWith(".gzip")) {
        s"${fname}.gzip"
      } else if (!compressed && fname.toLowerCase.endsWith(".gzip")) {
        fname.substring(0, fname.length - 5)
      } else {
        fname
      }

    val basePath = filepath.basePath
    val newPath  = fp.getParent.resolve(newFileName)

    val newFilePart  = basePath.relativize(newPath)
    val newFilePath  = filepath.copy(filename = newFilePart.toString)
    val path         = newFilePath.toJavaPath
    val parentFolder = path.toFile.getParentFile

    parentFolder.mkdirs()

    val json = Json.toJson(g)

    val tempFile = Files.createTempFile(parentFolder.toPath, path.getFileName.toString, ".tmp")

    Using.resource(
      compressed match {
        case true  => new GZIPOutputStream(Files.newOutputStream(tempFile))
        case false => Files.newOutputStream(tempFile)
      }
    ) { out =>
      mapper.writeValue(out, json)
      out.flush()
    }

    Files.move(tempFile, path, StandardCopyOption.REPLACE_EXISTING, StandardCopyOption.ATOMIC_MOVE)

    newFilePath
  }

  def writeJson[G](g: G, fp: FilePath, compressed: Boolean = true)(implicit x: Writes[G]): FilePath =
    writeJsonByPath(g, fp, compressed)
}
