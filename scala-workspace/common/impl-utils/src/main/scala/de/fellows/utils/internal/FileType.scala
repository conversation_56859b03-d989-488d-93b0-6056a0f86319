package de.fellows.utils.internal

import play.api.libs.json.{Format, Json}

case class FileType(
    service: String,
    category: Option[String],
    fileType: String,
    productionFile: Boolean,
    mimeType: Option[String],
    index: Option[Int] = None,
    from: Option[Int] = None,
    to: Option[Int] = None
) {
  def isMissing: Boolean =
    isUnmatched || isUnknown
  def isUnmatched: Boolean =
    fileType == FileType.UNMATCHED.fileType || fileType == FileType.UNMATCHED_ODB.fileType
  def isUnknown: Boolean =
    fileType == FileType.UNKNOWN.fileType
}

object FileType {
  val CATEGORY_ODB    = "odb"
  val GENERAL_SERVICE = "general"

  val UNKNOWN       = FileType(GENERAL_SERVICE, Some("unknown"), "unknown", productionFile = false, None)
  val ERROR         = FileType(GENERAL_SERVICE, Some("error"), "error", productionFile = false, None)
  val UNMATCHED     = FileType(GENERAL_SERVICE, Some("unknown"), "unmatched", productionFile = false, None)
  val UNMATCHED_ODB = FileType(GENERAL_SERVICE, Some(CATEGORY_ODB), "unmatched", productionFile = false, None)
  val FOLDER        = FileType(GENERAL_SERVICE, Some("folder"), "unmatched", productionFile = true, None)

  implicit val format: Format[FileType] = Json.format[FileType]
}
