package de.fellows.utils.health

import akka.actor.ActorSystem
import akka.util.Timeout
import akka.actor.typed.ActorRef
import akka.actor.typed.Scheduler
import akka.actor.typed.scaladsl.AskPattern._
import akka.actor.typed.scaladsl.adapter._
import de.fellows.utils.logging.StackrateLogging

import scala.concurrent.Future
import scala.concurrent.duration._
import akka.pattern.AskTimeoutException
import scala.util.control.NonFatal
import akka.event.Logging
import scala.concurrent.ExecutionContext
import akka.persistence.query.PersistenceQuery
import akka.persistence.cassandra.query.scaladsl.CassandraReadJournal
import scala.concurrent.ExecutionContextExecutor

class HealthCheck(actorSystem: ActorSystem) extends (() => Future[Boolean]) {
  private val log                           = Logging(actorSystem, classOf[HealthCheck])
  private implicit val timeout: Timeout     = 1.seconds
  private implicit val scheduler: Scheduler = actorSystem.toTyped.scheduler
  import actorSystem.dispatcher

  override def apply(): Future[Boolean] = {
    val fWriteSide = checkWriteSideSession()
    val fReadSide  = checkReadSideSession()

    (for {
      writeSide <- fWriteSide
      readSide  <- fReadSide
    } yield writeSide && readSide).recoverWith {
      case NonFatal(e) =>
        log.warning(s"Failed to execute health check due to: ${e.getMessage}")
        Future.successful(false)
    }
  }

  def checkWriteSideSession(): Future[Boolean] = {
    val queries = PersistenceQuery(actorSystem).readJournalFor[CassandraReadJournal](CassandraReadJournal.Identifier)

    queries
      .session
      .selectOne("SELECT now() FROM system.local")
      .map {
        case Some(_) => true
        case None    => false
      }
      .recoverWith {
        case NonFatal(e) =>
          log.warning(s"Failed to execute health check due to: ${e.getMessage}")
          Future.successful(false)
      }
  }

  def checkReadSideSession(): Future[Boolean] =
    HealthCheckActor.getActor match {
      case Some(actor) =>
        actor
          .ask[HealthCheckResponse](ref => HealthCheckQuery(ref))
          .map {
            case HealthCheckOkResponse     => true
            case HealthCheckFailedResponse => false
          }
          .recoverWith {
            case _: AskTimeoutException =>
              log.warning("Failed to execute health check due to ask timeout")
              Future.successful(false)
            case NonFatal(e) =>
              log.warning(s"Failed to execute health check due to: ${e.getMessage}")
              Future.successful(false)
          }
      case None =>
        log.error(
          "Health check actor not initialized. Ensure HealthCheckComponents is mixed in and initialized correctly."
        )
        Future.successful(false)
    }
}
