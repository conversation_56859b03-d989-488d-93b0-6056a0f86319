package de.fellows.utils.codec

import akka.Done
import com.datastax.driver.core._
import com.datastax.driver.extras.codecs.jdk8.InstantCodec
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession

import scala.concurrent.ExecutionContext

object CodecHelper {

  def registerUDTCodec[T <: TypeCodec[_]](udtName: String, codec: TypeCodec[UDTValue] => T)(implicit c: Session) = {
    val tp                      = c.getCluster.getMetadata.getKeyspace(c.getLoggedKeyspace).getUserType(udtName)
    val tc: TypeCodec[UDTValue] = c.getCluster.getConfiguration.getCodecRegistry.codecFor(tp)

    c.getCluster.getConfiguration.getCodecRegistry.register(codec(tc))
  }

  def registerCodec[K](codec: TypeCodec[K])(implicit c: Session): CodecRegistry =
    c.getCluster.getConfiguration.getCodecRegistry.register(codec)

  def registerCodecs(c: Session): CodecRegistry = {
    implicit val ic = c

    registerUDTCodec("filepath", tc => new FilePathCodec(tc))
    registerUDTCodec("filetype", tc => new FileTypeCodec(tc))

    registerCodec(InstantCodec.instance)

  }

  def loadTypes(session: CassandraSession)(implicit ctx: ExecutionContext) =
    // language=SQL
    for {
      _ <- session.executeCreateTable(
        """
          |CREATE TYPE IF NOT EXISTS filetype (
          |            typeService text,
          |            typeCategory text,
          |            typeType text,
          |            typeProd boolean,
          |            typeMime text,
          |            typeIndex decimal,
          |            typeFrom decimal,
          |            typeTo decimal,
          |);
            """.stripMargin
      )

      _ <- session.executeCreateTable(
        """
          |CREATE TYPE IF NOT EXISTS filepath (
          |           team text,
          |           root text,
          |           resource text,
          |           base text,
          |           name text,
          |           subpath text,
          |
          |);
            """.stripMargin
      )

      s <- session.underlying()
    } yield {
      registerCodec(InstantCodec.instance)(s)
      Done
    }

  def getBigDecimal(value: UDTValue, name: String) = {
    val v: java.math.BigDecimal = value.getDecimal(name)

    v match {
      case null => None
      case bd   => Some(BigDecimal(bd))
    }
  }

  def getBigDecimal(value: Row, name: String) = {
    val v: java.math.BigDecimal = value.getDecimal(name)

    v match {
      case null => None
      case bd   => Some(BigDecimal(bd))
    }
  }
}
