package de.fellows.utils

import de.fellows.utils.logging.StackrateLogging
import de.fellows.utils.spi.RandomFileProcessor

import java.nio.charset.Charset
import java.nio.file.spi.FileTypeDetector
import java.nio.file.{Files, Path}
import java.util.ServiceLoader
import scala.util.Try

object PathUtils extends StackrateLogging {

  def tryEncodings(jp: Path): String =
    Seq(Charset.forName("UTF-8"), Charset.forName("ISO_8859_1"))
      .view // as a view so we dont read the file more than necessary
      .map { charset =>
        Try {
          Files.readString(jp, charset)
        }
      }.find(_.isSuccess) match {
      case Some(value) => value.get
      case None        => throw new IllegalStateException(s"no valid charset found")
    }

  /** This function probes the content type of the file
    * although our custom detectors are registered as SPI FileTypeDetectors, that doesnt always work. (for instance in a mirrord context)
    *
    * we also try to make it more efficient by only reading the file once for our detectors
    *
    * @param jp
    * @return
    */
  def probeContentType(jp: Path): Try[Option[String]] =
    Try {

      val b = Seq.newBuilder[RandomFileProcessor]
      ServiceLoader.load(classOf[FileTypeDetector]).forEach {
        case processor: RandomFileProcessor => b += processor
        case _                              =>
      }

      val rafDetectors =
        if (b.result().nonEmpty) {
          val content = tryEncodings(jp)

          b.result().flatMap {
            case d if d.applies(content) => Some(d.mime)
            case _                       => None
          }

        } else {
          Seq()
        }

      rafDetectors.headOption orElse
        Option(Files.probeContentType(jp))

    }
}
