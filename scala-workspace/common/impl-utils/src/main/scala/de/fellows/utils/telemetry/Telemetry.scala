package de.fellows.utils.telemetry

import com.segment.analytics.Analytics
import com.segment.analytics.messages.{Message, MessageBuilder, TrackMessage}
import com.typesafe.config.{Config, ConfigFactory}
import de.fellows.utils.security.{AuthenticationServiceComposition, GenericTokenContent, TokenContent}
import org.slf4j.LoggerFactory

import java.time.Instant
import java.util.Date
import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success, Try}

/** Provides Access to Telemetry to export analytics through segment
  */
object Telemetry {
  private val conf: Config = ConfigFactory.load

  private val segmentKey = Try {
    conf.getString("segment.write_key") match {
      case null                => None
      case x if x.trim.isEmpty => None
      case x                   => Some(x)
    }
  }.toOption.flatten

  private lazy val analyticsInstance: Option[Analytics] = segmentKey.map(k => Analytics.builder(k).build)

  lazy final val logger = LoggerFactory.getLogger(classOf[Telemetry.type])

  def analytics(b: MessageBuilder[_ <: Message, _ <: MessageBuilder[_ <: Message, _ <: AnyRef]]): Try[Option[Unit]] = {
    val i = Instant.now()
    Try {
      analyticsInstance.map { a =>
        b.timestamp(Date.from(i))
        a.enqueue(b)
      }
    } match {
      case x: Failure[_] =>
        logger.error("Failed Analytics", x.exception)
        x
      case x: Success[_] =>
        x
    }
  }

  private val PCB_ANONYMOUS = "pcb-service-anonymous"

  def trackAnonymous(event: String, properties: Map[String, _]) = {
    val m =
      TrackMessage.builder(event)
        .properties(properties.asJava)
        .userId(PCB_ANONYMOUS)

    analytics(m)
  }

  def track(event: String, properties: Map[String, _], token: String): Try[Option[Unit]] =
    AuthenticationServiceComposition.decodeAnyToken(token)
      .flatMap(track(event, properties, _))

  def track(
      event: String,
      properties: Map[String, _],
      token: GenericTokenContent
  ): Try[Option[Unit]] = {
    val allProperties = properties + ("stackrate_team" -> token.getTeam)
    val m             = TrackMessage.builder(event).properties(allProperties.asJava)

    token.getAnalyticsId.foreach(id => m.userId(id))

    m.context(Map("groupId" -> token.getTeam).asJava)

    analytics(m)
  }

}
