package de.fellows.utils.apidoc

import io.swagger.v3.core.util.ReflectionUtils
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Info
import org.taymyr.lagom.internal.openapi.ClassLevelInfo
import play.api.Logging

import scala.jdk.CollectionConverters._

class StackrateAPIServiceProcessor(s: StackrateServiceInfo, classInfo: ClassLevelInfo, spec: OpenAPI) extends Logging {

  def process(): Unit = {
    Option(ReflectionUtils.getRepeatableAnnotations(s.service, classOf[ClasspathDocumentation])).map(_.asScala.toList).toList.flatten.foreach(d => {
      val info = spec.getInfo
      if (info == null) {
        spec.setInfo(new Info())
      }

      ApiGenUtils.extendInfos(d, s.service, info)
    })
  }

}
