package de.fellows.utils.health

import akka.actor.ActorSystem
import akka.util.Timeout
import akka.actor.typed.ActorRef
import akka.actor.typed.Behavior
import akka.actor.typed.scaladsl.AskPattern._
import akka.actor.typed.scaladsl.Behaviors
import akka.actor.typed.scaladsl.adapter._
import com.lightbend.lagom.scaladsl.persistence.cassandra.CassandraSession
import de.fellows.utils.logging.StackrateLogging

import java.util.concurrent.atomic.AtomicReference

import scala.util.Failure
import scala.util.Success
import scala.concurrent.Future
import scala.concurrent.duration._
import akka.pattern.AskTimeoutException
import scala.util.control.NonFatal
import akka.event.Logging
import scala.concurrent.ExecutionContext

trait HealthCheckComponents {
  def actorSystem: ActorSystem
  def cassandraSession: CassandraSession

  actorSystem.spawn(HealthCheckActor(cassandraSession), "health-check-actor")
}

sealed trait HealthCheckCommand

final case class HealthCheckQuery(actor: Actor<PERSON><PERSON>[HealthCheckResponse]) extends HealthCheckCommand

final case class CassandraHealthCheckOkResponse(replyTo: ActorRef[HealthCheckResponse])     extends HealthCheckCommand
final case class CassandraHealthCheckFailedResponse(replyTo: ActorRef[HealthCheckResponse]) extends HealthCheckCommand

sealed trait HealthCheckResponse
final case object HealthCheckOkResponse     extends HealthCheckResponse
final case object HealthCheckFailedResponse extends HealthCheckResponse

object HealthCheckActor {
  private val state = new AtomicReference[Option[ActorRef[HealthCheckCommand]]](None)

  def setActor(actor: ActorRef[HealthCheckCommand]): Unit =
    state.set(Some(actor))

  def getActor: Option[ActorRef[HealthCheckCommand]] = state.get()

  def apply(cassandraSession: CassandraSession): Behavior[HealthCheckCommand] = Behaviors.setup { ctx =>
    HealthCheckActor.setActor(ctx.self)
    Behaviors.receiveMessage[HealthCheckCommand] {
      case HealthCheckQuery(replyTo) =>
        ctx.pipeToSelf(
          cassandraSession.selectOne("SELECT now() FROM system.local")
        ) {
          case Success(_) => CassandraHealthCheckOkResponse(replyTo)
          case Failure(e) => CassandraHealthCheckFailedResponse(replyTo)
        }

        Behaviors.same

      case CassandraHealthCheckOkResponse(replyTo) =>
        replyTo ! HealthCheckOkResponse
        Behaviors.same

      case CassandraHealthCheckFailedResponse(replyTo) =>
        replyTo ! HealthCheckFailedResponse
        Behaviors.same
    }
  }
}
