package de.fellows.utils.codec

import java.nio.ByteBuffer
import scala.reflect.runtime.universe._

import com.datastax.driver.core.{ProtocolVersion, TypeCodec, UDTValue}

abstract class AbstractCodec[X >: Null](cdc: TypeCodec[UDTValue], cl: Class[X]) extends TypeCodec[X](cdc.getCqlType, cl) {

  def toValue(value: X): UDTValue

  def fromValue(value: UDTValue): X

  override def serialize(value: X, protocolVersion: ProtocolVersion): ByteBuffer = cdc.serialize(toValue(value), protocolVersion)

  override def deserialize(bytes: ByteBuffer, protocolVersion: ProtocolVersion): X = fromValue(cdc.deserialize(bytes, protocolVersion))

  def getBigDecimal(value: UDTValue, name: String) = {
    AbstractCodec.getBigDecimal(value, name)
  }


  override def parse(value: String): X =
    if (value == null || value.isEmpty) null
    else fromValue(cdc.parse(value))

  override def format(value: X): String =
    if (value == null) null
    else cdc.format(toValue(value))

  def get[T](evidence: Class[T])(name: String)(implicit value: UDTValue): T = {
    value.get[T](name, evidence)
  }

  def geto[T](evidence: Class[T])(name: String)(implicit value: UDTValue): Option[T] = {
    Option(value.get[T](name, evidence))
  }

  def getl[T](evidence: Class[T])(name: String)(implicit value: UDTValue): Seq[T] = {
    import scala.jdk.CollectionConverters._
    value.getList[T](name, evidence).asScala.toSeq
  }
}

object AbstractCodec {
  def getBigDecimal(value: UDTValue, name: String) = {
    val v: java.math.BigDecimal = value.getDecimal(name)

    v match {
      case null => None
      case bd => Some(BigDecimal(bd))
    }
  }

}
