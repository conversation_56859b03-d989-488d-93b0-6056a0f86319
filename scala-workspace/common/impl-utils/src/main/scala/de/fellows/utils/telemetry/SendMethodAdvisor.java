package de.fellows.utils.telemetry;

import kamon.Kamon;
import kamon.context.Context;
import kamon.instrumentation.context.HasContext;
import kamon.instrumentation.kafka.client.KafkaInstrumentation;
import kamon.instrumentation.kafka.client.ProducerCallback;
import kamon.trace.Span;
import kanela.agent.libs.net.bytebuddy.asm.Advice;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.ProducerRecord;

/**
 * Producer Instrumentation
 * It works the same as the Advice in the Kafka Instrumentation except that it does not start a new Span
 * when the message is sent. It just propagates the context in the headers.
 */
public class SendMethodAdvisor {
    @Advice.OnMethodEnter(suppress = Throwable.class)
    public static void onEnter(@Advice.Argument(value = 0, readOnly = false) ProducerRecord record,
                               @Advice.Argument(value = 1, readOnly = false) Callback callback,
                               @Advice.FieldValue("clientId") String clientId) {
        Context recordContext = ((HasContext) record).context();

        if (!recordContext.get(Span.Key()).isEmpty() || KafkaInstrumentation.settings().startTraceOnProducer()) {
            Span span = Kamon.currentSpan();

            Context ctx = recordContext.withEntry(Span.Key(), span);
            KafkaInstrumentation.settings().propagator().write(ctx, record.headers());

            callback = new NonClosingProducerCallback(callback, span, ctx);
        }
    }
}
