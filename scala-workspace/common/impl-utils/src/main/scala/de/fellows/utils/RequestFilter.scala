package de.fellows.utils

case class RequestFilter(elements: Seq[(String, String, String)]) {

}

object RequestFilter {

  def parse(s: String): Option[RequestFilter] = {

    s.trim match {
      case "" => None
      case _ => {
        val split = s.split(",").flatMap(field => {

          val args = Seq(">=", "<=", ">", "<", "!=", "=")


          args.find(field.contains) match {
            case Some(s) => {
              val splits = field.split(s)
              if (splits.size != 2 && splits.size != 1) {
                throw new IllegalArgumentException(s"cannot parse filter ${splits.mkString(",")}")
              } else {
                Some((splits(0), s, splits.lift(1).getOrElse("")))
              }

            }
            case None => None
          }
        })

        Some(RequestFilter(split))
      }
    }
  }

  def apply(s: String): RequestFilter = {
    parse(s).getOrElse(throw new IllegalArgumentException(s"cannot parse $s"))
  }
}
