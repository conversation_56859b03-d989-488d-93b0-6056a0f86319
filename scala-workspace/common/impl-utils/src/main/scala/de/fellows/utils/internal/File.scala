package de.fellows.utils.internal

import de.fellows.utils.FilePath
import play.api.libs.json._

import java.time.Instant
import java.util.UUID
import scala.concurrent.duration.{Deadline, Duration, FiniteDuration, MILLISECONDS}

case class File(
    id: UUID,
    name: String,
    path: FilePath,
    fType: FileType,
    detectedTypes: Seq[FileType],
    created: Instant,
    preview: Option[FilePath],
    lifecycles: Option[Seq[FileLifecycleStage]] = None,
    lifecycleHistory: Option[Seq[FileLifecycleStage]] = None,
    hash: Option[String]
) {
  def isEqual(add: File): Boolean =
    this.name == add.name && this.path.subPath == add.path.subPath
}

case class DuplicateResults(
    exactMatches: Seq[(UUID, File)]
)

object DuplicateResults {
  implicit val format: Format[DuplicateResults] = Json.format[DuplicateResults]
}

sealed trait FileLifecycleStageName extends LifecycleStageName

object FileLifecycleStageName {
  case object Render extends FileLifecycleStageName {
    val value = "render"
  }
  case object Preview extends FileLifecycleStageName {
    val value = "preview"
  }
  case object FileAnalysis extends FileLifecycleStageName {
    val value = "fileanalysis"
  }

  private val names = Array(Render, Preview, FileAnalysis)

  def fromName(name: String): Option[FileLifecycleStageName] = names.find(_.value == name)

  implicit val format: Format[FileLifecycleStageName] = new Format[FileLifecycleStageName] {
    override def writes(o: FileLifecycleStageName): JsValue = JsString(o.value)
    override def reads(json: JsValue): JsResult[FileLifecycleStageName] = json match {
      case JsString(name) =>
        fromName(name) match {
          case Some(value) => JsSuccess(value)
          case None        => JsError(s"Unknown FileLifecycleStageName: $name")
        }
      case _ => JsError(s"Expected JsString, got $json")
    }
  }
}

case class FileLifecycleStage(
    name: FileLifecycleStageName,
    status: LifecycleStageStatus,
    history: Option[Seq[HistoricLifecycleState]]
) extends LifecycleStage

object FileLifecycleStage {
  implicit val format: Format[FileLifecycleStage] = Json.format[FileLifecycleStage]
}

case class ApiFileLifecycle(
    name: String,
    status: LifecycleStageStatus,
    durations: Map[String, Long],
    history: Option[Seq[HistoricLifecycleState]]
)

case class HistoricLifecycleState(name: StageStatusName, start: Long, end: Option[Long])

object HistoricLifecycleState {
  implicit val format: Format[HistoricLifecycleState] = Json.format
}

object File {
  implicit val format: Format[File] = Json.using[Json.WithDefaultValues].format
}

object ApiFileLifecycle {
  implicit val format: Format[ApiFileLifecycle] = Json.using[Json.WithDefaultValues].format
}

object LifecycleDeadline {
  def in(d: FiniteDuration): Deadline = Deadline(Duration(System.currentTimeMillis(), MILLISECONDS)) + d
}
