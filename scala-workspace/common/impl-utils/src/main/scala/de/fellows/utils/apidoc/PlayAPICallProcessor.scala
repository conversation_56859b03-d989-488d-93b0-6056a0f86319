package de.fellows.utils.apidoc

import io.swagger.v3.oas.annotations.tags.{Tag => ATag, Tags => ATags}
import io.swagger.v3.oas.annotations.{Operation => AOperation}
import io.swagger.v3.oas.models.{OpenAPI, Operation}
import play.api.mvc.Action

import java.lang.reflect.{Method, Type}
import scala.collection.mutable
import scala.jdk.CollectionConverters._

class PlayAPICallProcessor(
    components: mutable.Builder[Type, Set[Type]],
    spec: OpenAPI,
    method: Method,
    api: StackrateApi,
    aop: AOperation,
    op: Operation
) {

  def process(): Unit = {
    val tags = (Option(method.getAnnotationsByType(classOf[ATag])).toSeq.flatMap(_.toSeq) ++
      Option(method.getAnnotationsByType(classOf[ATags])).toSeq.flatMap(x =>
        x.toSeq.flatMap(y => y.value().toSeq)
      )).map(_.name())

    if (tags.nonEmpty) {
      op.setTags(tags.asJava)
    }

    val internal = Option(method.getAnnotation(classOf[Internal]))

    if (internal.isDefined) {
      op.addExtension("x-internal", "true")
    }

    StackrateAPICallProcessor.addOperationToPath(spec, api.path(), aop.method(), op)
  }

}
