package de.fellows.utils.apidoc

import com.lightbend.lagom.scaladsl.api.Service
import com.lightbend.lagom.scaladsl.api.transport.{ MessageProtocol, NotFound, ResponseHeader }
import com.lightbend.lagom.scaladsl.server.ServerServiceCall
import com.typesafe.config.ConfigFactory
import de.fellows.utils.service.StackrateServiceAPI
import io.swagger.v3.core.util.{ Json, ReflectionUtils, Yaml }
import io.swagger.v3.oas.annotations.OpenAPIDefinition

import scala.concurrent.Future

trait StackrateAPIImpl extends StackrateServiceAPI with Service {
  val JSON: MessageProtocol = MessageProtocol.fromContentTypeHeader(Some("application/json"))
  val HTML: MessageProtocol = MessageProtocol.fromContentTypeHeader(Some("text/html"))

  val additionalCalls = Seq.newBuilder[StackrateCallInfo]

  val config = ConfigFactory.load()

  private def generateSpecResource(): OpenAPISpec = {

    val cl = Class.forName(getClass.getName).getClassLoader

    val api = new StackrateSpecGenerator()(cl).generate(this, additionalRouters.result())
    OpenAPISpec(Json.pretty(api), Yaml.pretty(api))
  }

  private lazy val spec: Option[OpenAPISpec] = {
    try {
      val isAnnotated: Boolean = ReflectionUtils.getAnnotation(this.getClass, classOf[OpenAPIDefinition]) != null
      if (isAnnotated) {
        Some(generateSpecResource())
      } else {
        None
      }
    } catch {
      case e: Throwable =>
        e.printStackTrace()
        None
    }
  }

  def withAdditionalCall(info: StackrateCallInfo): Unit =
    this.additionalCalls += info

  override def openapi() = ServerServiceCall { (_, _) =>
    if (spec.isEmpty || spec.get.json == null || spec.get.yaml == null)
      throw NotFound("OpenAPI specification not found")
    Future.successful(
      (ResponseHeader.Ok.withProtocol(JSON), spec.get.json)
    )
  }

  override def rapidoc() = ServerServiceCall { (a, b) =>
    val url = a.uri.toString.replace("rapidoc", "openapi")
    Future.successful(
      (
        ResponseHeader.Ok.withProtocol(HTML),
        s"""
           |<html>
           |        <head>
           |                  <meta charset="utf-8"> <!-- Important: rapi-doc uses utf8 charecters -->
           |                    <script type="module" src="https://unpkg.com/rapidoc/dist/rapidoc-min.js"></script>
           |        </head>
           |        <body>
           |                  <rapi-doc
           |                    spec-url = "${url}"
           |                    render-style = "focused"
           |                    show-components = "false"
           |
           |                    nav-bg-color="#2D323E"
           |                    primary-color="#039be5"
           |                    bg-color="#CFD2CD77"
           |                    font-size: "largest"
           |
           |                    allow-server-selection="false"
           |                    allow-try = "false"
           |                    show-header = "false"
           |                    regular-font="Open Sans"
           |                    mono-font="Roboto Mono"
           |                    default-schema-tab="model"
           |                    schema-expand-level = "1">
           |
           |                    <img
           |                      slot="nav-logo"
           |                      src="/assets/images/logos/stackrate-white.png"
           |                      width="100%"
           |                    />
           |
           |                    <div style="display:flex; padding: 24px 80px 12px;"> <a href="${url}"> OpenAPI Specification</a> </div>
           |
           |                    </rapi-doc>
           |        </body>
           |</html>
           |
           |""".stripMargin
      )
    )
  }

  private case class OpenAPISpec(json: String, yaml: String)

}
