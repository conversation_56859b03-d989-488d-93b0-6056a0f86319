package de.luminovo.odb.odbpp.model.constants

import de.luminovo.odb.odbpp.model.validation.ODBParseError

trait TextVersion {
  val odbString: String
}

case object Version0 extends TextVersion {
  override val odbString: String = "0"
}
case object Version1 extends TextVersion {
  override val odbString: String = "1"
}

object TextVersion {
  def apply(s: String): TextVersion = s match {
    case "0" => Version0
    case "1" => Version1
    case _   => throw new ODBParseError(s"Invalid text version: $s")
  }
}
