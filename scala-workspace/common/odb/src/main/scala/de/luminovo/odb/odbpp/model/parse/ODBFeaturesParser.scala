package de.luminovo.odb.odbpp.model.parse

import de.luminovo.odb.odbpp.model.ODBFeatures.{ArcRecord, BarcodeRecord, Curve, CurveArc, CurveLine, LineRecord, ODBFeatureRecord, PadRecord, Polygon, SurfaceRecord, TextRecord}
import de.luminovo.odb.odbpp.model.ODBLineRecord.RawLineRecordCommand
import de.luminovo.odb.odbpp.model.constants.{BarcodeAscii, BarcodeBackground, BarcodeChecksum, BarcodeTextOption, BarcodeTextPosition, Direction, Orientation, Polarity, PolygonType, TextVersion}
import de.luminovo.odb.odbpp.model.features.symbols.{ODBSymbol, UnitConvertingSymbolConverter}
import de.luminovo.odb.odbpp.model.validation.{ODBParseError, ODBSyntaxError}
import de.luminovo.odb.odbpp.model.{MilUnit, ODBLineRecord, ODBUnit}

class ODBFeaturesParser(actualUnit: ODBUnit, desiredUnit: ODBUnit) {

  def convertWidthFactor(value: Double): Double = {
    val mils = value * 12 // width factor is given in steps of 12 mils

    MilUnit.convert(mils, desiredUnit) match {
      case Left(value)  => throw new IllegalStateException(value.message)
      case Right(value) => value
    }
  }

  def convertUnit(value: Double): Double =
    actualUnit.convert(value, desiredUnit) match {
      case Left(value)  => throw new IllegalStateException(value.message)
      case Right(value) => value
    }

  def parsePolygon(first: RawLineRecordCommand, i: Iterator[RawLineRecordCommand]): Polygon = {
    // first line is the begin, starting with "OB"

    val curves = Seq.newBuilder[Curve]
    i.takeWhile(_.op != "OE").foreach { curve =>
      curve.op match {
        case "OS" =>
          val direction = Direction(curve.params.lift(2).map(_.content))
          curves += CurveLine(
            convertUnit((curve.params(0).content).toDouble),
            convertUnit((curve.params(1).content).toDouble),
            direction
          ) // line
        case "OC" => curves += CurveArc(
            convertUnit((curve.params(0).content).toDouble),
            convertUnit((curve.params(1).content).toDouble),
            convertUnit((curve.params(2).content).toDouble),
            convertUnit((curve.params(3).content).toDouble),
            Direction(curve.params.lift(4).map(_.content))
          )
      }
    }

    Polygon(
      convertUnit((first.params(0).content).toDouble),
      convertUnit((first.params(1).content).toDouble),
      PolygonType(first.params(2).content),
      curves.result()
    )
  }

  def parseSurface(surfaceCommand: RawLineRecordCommand, i: Iterator[RawLineRecordCommand]): SurfaceRecord = {

    val polarity = Polarity(surfaceCommand.params(0).content)
    val dcode    = surfaceCommand.params(1).content
    val rest     = surfaceCommand.params.drop(2)

    val polys = i.takeWhile(_.op != "SE").map { polygon =>
      parsePolygon(polygon, i)
    }

    SurfaceRecord(surfaceCommand.line, polys.toSeq, polarity, dcode, surfaceCommand.assignments)
  }

  def parsePad(cmd: RawLineRecordCommand): PadRecord =
    PadRecord(
      line = cmd.line,
      x = convertUnit(cmd.params(0).content.toDouble),
      y = convertUnit(cmd.params(1).content.toDouble),
      symNum = cmd.params(2).content.toInt,
      polarity = Polarity(cmd.params(3).content),
      dcode = cmd.params(4).content,
      // according to the spec, the orientation is given without space, for instance "8150" for arbitrary rotation (8) with 150 degrees.
      // however, in practice there is sometimes a space present for pad definitions (i.e. "8 150"), so we optionally allow for that.
      // the orientation is the last given parameter so it should not mess up the parsing of the other parameters.
      orientation = Orientation(cmd.params(5).content, cmd.params.lift(6).map(_.content))
    )

  def parseLine(cmd: RawLineRecordCommand): LineRecord =
    LineRecord(
      line = cmd.line,
      xs = convertUnit((cmd.params(0).content.toDouble)),
      ys = convertUnit((cmd.params(1).content.toDouble)),
      xe = convertUnit((cmd.params(2).content.toDouble)),
      ye = convertUnit((cmd.params(3).content.toDouble)),
      symNum = cmd.params(4).content.toInt,
      polarity = Polarity(cmd.params(5).content),
      dcode = cmd.params(6).content
    )

  def parseArc(cmd: RawLineRecordCommand): ArcRecord =
    ArcRecord(
      line = cmd.line,
      xs = convertUnit(cmd.params(0).content.toDouble),
      ys = convertUnit(cmd.params(1).content.toDouble),
      xe = convertUnit(cmd.params(2).content.toDouble),
      ye = convertUnit(cmd.params(3).content.toDouble),
      xc = convertUnit(cmd.params(4).content.toDouble),
      yc = convertUnit(cmd.params(5).content.toDouble),
      symNum = (cmd.params(6).content.toInt),
      polarity = Polarity(cmd.params(7).content),
      dCode = cmd.params(8).content,
      direction = Direction(cmd.params(9).content)
    )

  def parseTextRecord(cmd: RawLineRecordCommand): TextRecord =
    TextRecord(
      line = cmd.line,
      x = convertUnit(cmd.params(0).content.toDouble),
      y = convertUnit(cmd.params(1).content.toDouble),
      font = cmd.params(2).content,
      polarity = Polarity(cmd.params(3).content),
      orientation = Orientation(cmd.params(4).content),
      xSize = convertUnit((cmd.params(5).content).toDouble),
      ySize = convertUnit((cmd.params(6).content).toDouble),
      width = convertWidthFactor(cmd.params(7).content.toDouble),
      text = cmd.params(8).content,
      version = TextVersion(cmd.params(9).content)
    )

  def parseBarcode(cmd: RawLineRecordCommand): BarcodeRecord =
    BarcodeRecord(
      line = cmd.line,
      x = convertUnit((cmd.params(0).content).toDouble),
      y = convertUnit((cmd.params(1).content).toDouble),
      barcode = cmd.params(2).content,
      font = cmd.params(3).content,
      polarity = Polarity(cmd.params(4).content),
      orientation = Orientation(cmd.params(5).content),
      e = cmd.params(6).content,
      w = convertUnit((cmd.params(7).content).toDouble),
      h = convertUnit((cmd.params(8).content).toDouble),
      fasc = BarcodeAscii(cmd.params(9).content),
      cs = BarcodeChecksum(cmd.params(10).content),
      bg = BarcodeBackground(cmd.params(11).content),
      astr = BarcodeTextOption(cmd.params(12).content),
      astrPos = BarcodeTextPosition(cmd.params(13).content),
      text = cmd.params(14).content
    )

  def parse(base: ODBLineRecord): Seq[ODBFeatureRecord] = {
    val i = base.commands.iterator

    i.flatMap(cmd =>
      try
        cmd.op match {
          case "F" => None
          case "L" => Some(parseLine(cmd))
          case "P" => Some(parsePad(cmd))
          case "A" => Some(parseArc(cmd))
          case "T" => Some(parseTextRecord(cmd))
          case "B" => Some(parseBarcode(cmd))
          case "S" => Some(parseSurface(cmd, i))

          case x =>
            None
        }
      catch {
        case e: ODBParseError => throw new ODBSyntaxError(cmd.line, e)
      }
    ).toSeq
  }

  def parseSymbols(base: ODBLineRecord): Seq[ODBSymbol] =
    base.symbols.map { baseSymbol =>
      try
        new UnitConvertingSymbolConverter(actualUnit, desiredUnit).read(baseSymbol)
      catch {
        case e: ODBParseError => throw new ODBSyntaxError(baseSymbol.line, e)
      }
    }
}
