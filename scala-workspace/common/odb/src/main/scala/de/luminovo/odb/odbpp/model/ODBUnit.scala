package de.luminovo.odb.odbpp.model

import de.luminovo.odb.odbpp.model.ODBLineRecord.LineRecordHeader
import de.luminovo.odb.odbpp.model.validation.{RequiredUnitMissing, SemanticError}

sealed trait ODBUnit {
  def convert(value: Double, desiredUnit: ODBUnit): Either[SemanticError, Double]

}

case object MMUnit extends ODBUnit {
  override def convert(value: Double, desiredUnit: ODBUnit): Either[SemanticError, Double] = desiredUnit match {
    case MMUnit      => Right(value)
    case InchUnit    => Right(value / 25.4)
    case MilUnit     => Right(value / 0.0254)
    case UnknownUnit => Left(RequiredUnitMissing)
  }

}
case object MilUnit extends ODBUnit {
  override def convert(value: Double, desiredUnit: ODBUnit): Either[SemanticError, Double] = desiredUnit match {
    case MMUnit      => Right(value * 0.0254)
    case InchUnit    => Right(value * 0.001)
    case MilUnit     => Right(value)
    case UnknownUnit => Left(RequiredUnitMissing)
  }

}
case object InchUnit extends ODBUnit {
  override def convert(value: Double, desiredUnit: ODBUnit): Either[SemanticError, Double] = desiredUnit match {
    case MMUnit      => Right(value * 25.4)
    case InchUnit    => Right(value)
    case MilUnit     => Right(value * 1000)
    case UnknownUnit => Left(RequiredUnitMissing)
  }
}
case object UnknownUnit extends ODBUnit {
  override def convert(value: Double, desiredUnit: ODBUnit): Either[SemanticError, Double] =
    InchUnit.convert(value, desiredUnit)
}

object ODBUnit {
  def apply(unit: String): ODBUnit = unit.toLowerCase match {
    case "mm"   => MMUnit
    case "inch" => InchUnit
  }
  def apply(unit: LineRecordHeader): ODBUnit = apply(unit.value)
}
