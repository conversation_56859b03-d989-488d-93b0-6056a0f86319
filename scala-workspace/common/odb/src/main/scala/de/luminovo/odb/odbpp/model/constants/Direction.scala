package de.luminovo.odb.odbpp.model.constants

import de.luminovo.odb.odbpp.model.validation.{ODBParseError, ODBSyntaxError}

sealed trait Direction {
  val odbString: String
}

case object Clockwise extends Direction {
  override val odbString: String = "Y"
}
case object CounterClockwise extends Direction {
  override val odbString: String = "N"
}

object Direction {
  def apply(s: Option[String]): Direction = s match {
    case Some(x) => apply(x)
    case None    => Clockwise
  }
  def apply(s: String): Direction = s match {
    case "Y" => Clockwise
    case "N" => CounterClockwise
    case _   => throw new ODBParseError(s"Invalid direction: $s")
  }
}
