package de.luminovo.odb.odbpp.model.constants

import de.luminovo.odb.odbpp.model.validation.ODBParseError

sealed trait Orientation {
  val mirrored: Boolean
  val odbString: String
  val deg: BigDecimal
  lazy val theta: Double = Math.toRadians(this.deg.doubleValue)
}

case class Orientation0(override val mirrored: Boolean) extends Orientation {
  override val odbString: String = if (mirrored) "4" else "0"
  override val deg: BigDecimal   = 0
}

case class Orientation90(override val mirrored: Boolean) extends Orientation {
  override val odbString: String = if (mirrored) "5" else "1"
  override val deg: BigDecimal   = 90
}

case class Orientation180(override val mirrored: Boolean) extends Orientation {
  override val odbString: String = if (mirrored) "6" else "2"
  override val deg: BigDecimal   = 180
}

case class Orientation270(override val mirrored: Boolean) extends Orientation {
  override val odbString: String = if (mirrored) "7" else "3"
  override val deg: BigDecimal   = 270
}

case class AnyOrientation(degrees: BigDecimal, override val mirrored: Boolean) extends Orientation {
  override val odbString: String = if (mirrored) s"9${degrees}" else s"8${degrees}"
  override val deg: BigDecimal   = degrees
}

object Orientation {
  def apply(s: String, optionalAngle: Option[String] = None): Orientation = s match {
    case "0" => Orientation0(false)
    case "1" => Orientation90(false)
    case "2" => Orientation180(false)
    case "3" => Orientation270(false)
    case "4" => Orientation0(true)
    case "5" => Orientation90(true)
    case "6" => Orientation180(true)
    case "7" => Orientation270(true)
    case s"8$angle" =>
      val angleToUse = optionalAngle.getOrElse(angle).trim
      if (angleToUse == null || angleToUse == "") {
        Orientation0(false)
      } else {
        AnyOrientation(BigDecimal(angleToUse), false)
      }
    case s"9$angle" =>
      val angleToUse = optionalAngle.getOrElse(angle).trim
      if (angleToUse == null || angleToUse == "") {
        Orientation0(true)
      } else {
        AnyOrientation(BigDecimal(angleToUse), true)
      }

    case _ => throw new ODBParseError(s"Invalid orientation: $s")
  }
}
