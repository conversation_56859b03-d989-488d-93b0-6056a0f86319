package de.luminovo.odb.odbpp.model

import de.luminovo.odb.odbpp.model.error.ODBError

import java.nio.file.Path

trait ODBLayer {
  def getAttrList: Either[ODBError, ODBAttrList]
  def getComponents: Either[ODBError, ODBComponents]
  def getDimensions: Either[ODBError, ODBDimensions]
  def getFeatures: Either[ODBError, ODBFeatures]
  def getProfile: Either[ODBError, ODBProfile]
  def getNotes: Either[ODBError, ODBNotes]
  def getTools: Either[ODBError, ODBTools]
}

object ODBLayer {
  /**
    * get the ODB root from a layer folder (not the features file)
    * @param fp
    * @return
    */
  def getRootFromLayer(fp: Path): Path =
    fp           // layerfolder
      .getParent // layers
      .getParent // step folder
      .getParent // steps
      .getParent // odb root


}
