package de.luminovo.odb.odbpp.model

import de.luminovo.odb.odbpp.model.ODBLineRecord._
import de.luminovo.odb.odbpp.model.validation.{ODBParseError, SemanticError, SyntaxError}

import scala.collection.mutable
import scala.io.Source

case class ODBLineRecord(
    header: Seq[LineRecordHeader],
    symbols: Seq[LineRecordSymbol],
    attributes: Seq[LineRecordAttribute],
    attributeTexts: Seq[LineRecordAttributeText],
    commands: Seq[RawLineRecordCommand]
) {}

object ODBLineRecord {

  sealed trait LineRecordElement {
    def line: Int
  }

  /** ODB Specification 8.1.3 page 176
    * @param line the line number this symbol was defined on
    * @param id the ID of the symbol
    * @param name the name of the symbol (either a standard symbol definition string or the name of an user defined symbol)
    * @param unit an optional unit for the symbol (either M or I)
    */
  case class LineRecordSymbol(override val line: Int, id: Int, name: String, unit: Option[ODBUnit])
      extends LineRecordElement

  case class LineRecordAttribute(override val line: Int, id: Int, name: String) extends LineRecordElement

  case class LineRecordAttributeText(override val line: Int, id: Int, name: String) extends LineRecordElement

  case class RawLineRecordCommand(
      override val line: Int,
      op: String,
      params: Seq[CmdParam],
      assignments: Seq[Seq[LineRecordAssignment]]
  ) extends LineRecordElement

  case class CmdParam(content: String)

  case class LineRecordAssignment(name: String, value: String)

  case class LineRecordHeader(override val line: Int, name: String, value: String) extends LineRecordElement

  object LineRecordHeader {
    def apply(line: Int, x: (String, String)): LineRecordHeader = new LineRecordHeader(line, x._1, x._2)
  }

  object LineRecordAssignment {
    def apply(x: (String, String)): LineRecordAssignment = new LineRecordAssignment(x._1, x._2)
  }

  case class State(
      header: mutable.Builder[LineRecordHeader, Seq[LineRecordHeader]] = Seq.newBuilder[LineRecordHeader],
      symbols: mutable.Builder[LineRecordSymbol, Seq[LineRecordSymbol]] = Seq.newBuilder,
      attributes: mutable.Builder[LineRecordAttribute, Seq[LineRecordAttribute]] = Seq.newBuilder,
      attributeText: mutable.Builder[LineRecordAttributeText, Seq[LineRecordAttributeText]] = Seq.newBuilder,
      cmd: mutable.Builder[RawLineRecordCommand, Seq[RawLineRecordCommand]] = Seq.newBuilder
  )

  private def parseParameters(params: String) = {
    def parseString(i: Iterator[Char]): String = {
      i.takeWhile(x => x != '"' && x != '\'').mkString("")
    }

    val chars = params.trim.iterator

    var builder: Option[mutable.StringBuilder] = None
    val paramList                              = Seq.newBuilder[CmdParam]
    chars.foreach {
        case ' ' => builder match {
            case Some(value) =>
              paramList += CmdParam(value.result())
              builder = None
            case None => {
              // skip spaces between parameters
            }
          }
        case '"' | '\'' =>
          paramList += CmdParam(parseString(chars))

        case x =>
          builder match {
            case Some(value) => value.append(x)
            case None =>
              builder = Some(new StringBuilder())
              builder.get.append(x)
          }
      }

    builder.foreach(b => paramList += CmdParam(b.result()))
    val plist = paramList.result()
    plist
  }

  private def parseAssignments(assignments: String) =
    assignments.split(",").flatMap { assignment =>
      if (assignment.strip().contains("=")) {
        val spl = assignment.split("=")
        Some(LineRecordAssignment(spl.head -> spl.tail.mkString("=")))
      } else {
        None
      }
    }.toSeq

  private def parseCmd(lineNumber: Int, cmd: String, params: String, more: Seq[String]): RawLineRecordCommand = {
    val plist       = parseParameters(params)
    val assignments = more.map(parseAssignments)

    RawLineRecordCommand(lineNumber, cmd, plist, assignments)
  }

  private def cleanComments(str: String) =
    str.split("#").headOption.getOrElse(str)

  def parse(source: Iterator[String]): Either[SemanticError, State] = {
    val x: mutable.Builder[LineRecordSymbol, Seq[LineRecordSymbol]] = Seq.newBuilder[LineRecordSymbol]
    val state                                                       = State()
    source.zipWithIndex.foreach { line =>
      val (next, lineNumber) = line
      val l                  = cleanComments(next.strip())

      l match {
        case s"$$$symbol $name" =>
          val splitName = name.split(" ")
          if (splitName.length > 2) {
            return Left(SyntaxError(s"feature name ${name} is illegal"))
          }

          val n = splitName.head
          val u = splitName.lift(1)

          val symbolUnit = u match {
            case Some("I") => Some(InchUnit)
            case Some("M") => Some(MMUnit)
            case Some(x)   => return Left(SyntaxError(s"unknown symbol unit ${x}"))
            case None      => None
          }

          state.symbols += LineRecordSymbol(lineNumber, symbol.toInt, n, symbolUnit)
        case s"@$symbol $name" => state.attributes += LineRecordAttribute(lineNumber, symbol.toInt, name)
        case s"U $unit"        => state.header += LineRecordHeader(lineNumber, "UNITS" -> unit)
        case s"&$symbol $name" => state.attributeText += LineRecordAttributeText(lineNumber, symbol.toInt, name)
        case s"#${comment}"    =>
        case s"$x=$y" if !x.contains(" ") => state.header += LineRecordHeader(lineNumber, x -> y)
        case s"$cmd $params" =>
          if (cmd.toLowerCase == "char") {
            val command = parseCmd(lineNumber, cmd, params, Seq())
            state.cmd += command
          } else {
            val parts   = params.split(";")
            val command = parseCmd(lineNumber, cmd, parts.head, parts.tail)
            state.cmd += command
          }
        case x if x.nonEmpty => state.cmd += RawLineRecordCommand(lineNumber, x, Seq(), Seq())

        case _ =>
      }
    }

    Right(state)
  }

  def apply(source: Source): Either[SemanticError, ODBLineRecord] =
    parse(source.getLines()).map { state =>
      new ODBLineRecord(
        state.header.result(),
        state.symbols.result(),
        state.attributes.result(),
        state.attributeText.result(),
        state.cmd.result()
      )
    }

}
