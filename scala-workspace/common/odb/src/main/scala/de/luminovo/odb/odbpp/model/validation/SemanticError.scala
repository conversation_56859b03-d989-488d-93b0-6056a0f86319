package de.luminovo.odb.odbpp.model.validation

sealed trait SemanticError {
  def message: String
}

case object RequiredUnitMissing extends SemanticError {
  override def message: String = "Required unit missing"
}

case class SyntaxError(e: String) extends SemanticError {
  override def message: String = "Syntax error"
}

case class UnsupportedODBFeature(e: String) extends SemanticError {
  override def message: String = s"Unsupported ODB feature ${e}"
}