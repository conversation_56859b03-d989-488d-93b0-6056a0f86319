package de.luminovo.odb.odbpp.model

case class ODBStructuredText(
     assignments: Seq[ODBAssignment]
) {
  def obj(key: String): Seq[ODBArray] =
    assignments.filter(_.name == key).map(_.value).filter(_.isInstanceOf[ODBArray]).map(
      _.asInstanceOf[ODBArray]
    )
}

object ODBStructuredText {

  case class ArrayState(name: String, values: Seq[ODBAssignment])

  case class State(assignments: Seq[ODBAssignment], array: Option[ArrayState])

  private def parseAssignments(source: Iterator[String]): Seq[ODBAssignment] = {
    source.takeWhile(_.strip() != "}").flatMap { next =>
      val l = next.strip()
      if (!l.isBlank && !l.startsWith("#")) {
        Some(
          l match {
            case s"$name=$value" =>
              ODBAssignment(name.strip(), ODBSingleValue(value.strip()))
            case s"$name {" =>
              ODBAssignment(name.strip(), ODBArray(parseAssignments(source)))
          }
        )

      } else {
        None
      }
    }.toSeq
  }

  private def clean(source: Iterator[String]): Iterator[String] = {
    source.flatMap(line => {
      if(line.count(c=> c == '}') == 1 && line.trim.length != 1){
        line.split('}').flatMap(x => Seq(x, "}"))
      }else{
        Seq(line)
      }
    })
  }

  def parse(source: Iterator[String]) = {
    new ODBStructuredText(parseAssignments(clean(source)))
  }
}
