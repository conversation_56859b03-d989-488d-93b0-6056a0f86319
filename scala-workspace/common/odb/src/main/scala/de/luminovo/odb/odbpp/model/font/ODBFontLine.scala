package de.luminovo.odb.odbpp.model.font

import de.luminovo.odb.odbpp.model.constants.{CharShape, Polarity}
import de.luminovo.odb.odbpp.model.validation.ODBParseError

case class ODBFontLine(xs: Double, ys: Double, xe: Double, ye: Double, pol: Polarity, shape: CharShape, width: Double) {
  def toODBString: String = s"LINE ${xs} ${ys} ${xe} ${ye} ${pol.odbString} ${shape.odbString} ${width}"
}

object ODBFontLine {
  def apply(line: String): ODBFontLine = {
    validateLine(line)

    val parts = line.split(" ")
    ODBFontLine(
      xs = BigDecimal(parts(0)).doubleValue,
      ys = BigDecimal(parts(1)).doubleValue,
      xe = BigDecimal(parts(2)).doubleValue,
      ye = BigDecimal(parts(3)).doubleValue,
      pol = Polarity(parts(4)),
      shape = CharShape(parts(5)),
      width = BigDecimal(parts(6)).doubleValue
    )
  }

  private val numr = "-?[0-9]+(\\.[0-9]+)?".r
  private val ws   = "\\s+".r

  // validation for the font line format. Do not validate polarity and shape, as they are validated in their respective classes
  private val validation = s"$numr$ws$numr$ws$numr$ws$numr$ws.$ws.$ws$numr".r

  private def validateLine(line: String): Unit =
    if (!validation.matches(line)) {
      throw new ODBParseError(s"Invalid font line: $line")
    }
}
