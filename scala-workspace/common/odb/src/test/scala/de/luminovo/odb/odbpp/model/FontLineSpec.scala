package de.luminovo.odb.odbpp.model

import de.luminovo.odb.odbpp.model.constants.{PositivePolarity, RoundedChar}
import de.luminovo.odb.odbpp.model.font.ODBFontLine
import de.luminovo.odb.odbpp.model.validation.ODBParseError
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers._

class FontLineSpec extends AnyFlatSpec {
  def tap[X](b: => X): X =
    try
      b
    catch {
      case x: Throwable =>
        throw x
    }

  "FontLine" should "parse" in {
    val lines = Seq(
      "-0.05 -0.1 -0.05 0.2 P R 0.012",
      "0.05 -0.1 0.05 0.2 P R 0.012",
      "-0.1 0 0.1 0 P R 0.012",
      "-0.1 0.1 0.1 0.1 P R 0.012"
    )

    val result = lines.map(ODBFontLine(_))

    result should have size 4
    result should contain theSameElementsAs Seq(
      new ODBFontLine(-0.05, -0.1, -0.05, 0.2, PositivePolarity, RoundedChar, 0.012),
      new ODBFontLine(0.05, -0.1, 0.05, 0.2, PositivePolarity, RoundedChar, 0.012),
      new ODBFontLine(-0.1, 0, 0.1, 0, PositivePolarity, RoundedChar, 0.012),
      new ODBFontLine(-0.1, 0.1, 0.1, 0.1, PositivePolarity, RoundedChar, 0.012)
    )
  }

  it should "catch invalid input" in {
    assertThrows[ODBParseError] {
      tap {
        ODBFontLine("0.05 -0.1 0.05 0.2 P R") // missing number
      }
    }
    assertThrows[ODBParseError] {
      tap {
        ODBFontLine("0a05 -0.1 0.05 0.2 P R 0.2") // Malformed number
      }
    }
    assertThrows[ODBParseError] {
      tap {
        ODBFontLine("0.05 -0.1 0.05 0.2 X R 0.2") // Invalid Polarity
      }
    }
    assertThrows[ODBParseError] {
      tap {
        ODBFontLine("0.05 -0.1 0.05 0.2 P X 0.2") // Invalid CharShape
      }
    }
    assertThrows[ODBParseError] {
      tap {
        ODBFontLine("this is wrong") // Completely broken
      }
    }
  }
}
